package ru.naumen.metainfoadmin.client.fastlink.settings.command;

import java.util.List;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSetting;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSettingWithTitles;
import ru.naumen.metainfoadmin.client.fastlink.settings.forms.EditFastLinkSettingForm;

/**
 * <AUTHOR>
 * @since 01.03.18
 */
public class EditFastLinkSettingCommand extends BaseCommandImpl<FastLinkSetting,
        List<DtoContainer<FastLinkSettingWithTitles>>>
{
    public static final String ID = "editFastLinkSettingCommand";

    @Inject
    Provider<EditFastLinkSettingForm> editFastLinkSettingFormProvider;

    @Inject
    public EditFastLinkSettingCommand(@Assisted FastLinkSettingsCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<FastLinkSetting, List<DtoContainer<FastLinkSettingWithTitles>>> param)
    {
        FastLinkSettingsCommandParam p = (FastLinkSettingsCommandParam)param;

        EditFastLinkSettingForm presenter = editFastLinkSettingFormProvider.get();
        presenter.setFastLinkSetting(p.getValue());
        presenter.setRefreshCallback(p.getCallback());
        presenter.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}
