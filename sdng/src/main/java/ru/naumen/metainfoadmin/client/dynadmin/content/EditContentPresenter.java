package ru.naumen.metainfoadmin.client.dynadmin.content;

import java.util.List;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.client.permission.AdminPermissionCheckServiceSync;
import ru.naumen.admin.client.permission.AdminPermissionHelper;
import ru.naumen.core.client.content.ContextUtils;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithFolders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactory;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.AbstractMessageWidget;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.SaveUiAndGetSavedContentResponse;
import ru.naumen.metainfo.shared.ui.ContentUtils;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.parenttree.ContentParentTreeFactoryContext;
import ru.naumen.metainfoadmin.client.dynadmin.parenttree.ParentTreeGinModule.ContentParentTree;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * Базовый презентер формы редактирования контентов
 *
 * <AUTHOR>
 * @since 01 окт. 2013 г.
 *
 */
public abstract class EditContentPresenter<T extends FlowContent> extends OkCancelPresenter<PropertyDialogDisplay>
        implements ContentPropertiesOwner
{
    protected UIContext context;
    protected T content;
    protected int posInParent;
    protected ContentParentTreeFactoryContext treeContext;

    @Inject
    protected Processor validation;
    @Inject
    protected ContentCreatorMessages contentMessages;
    @Inject
    protected MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    protected AdminPermissionCheckServiceSync adminPermissionCheckServiceSync;
    @Inject
    protected AdminPermissionHelper adminPermissionHelper;
    @Inject
    protected TextBoxProperty code;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;
    @Inject
    private DtoTreeFactory<DtObject, ContentParentTree, WithFolders, ContentParentTreeFactoryContext> treeFactory;

    @Inject
    protected BooleanCheckBoxProperty collapseByDefault;
    protected Property<SelectItem> settingsSet;
    protected Property<DtObject> parent;

    protected EditContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public <P> PropertyRegistration<P> addPropertyAfter(Property<P> property, Property<?> propertyBefore)
    {
        int index = getDisplay().indexOf(propertyBefore) + 1;
        return getDisplay().addProperty(property, index);
    }

    public abstract void bindProperties();

    protected void bindSettingsSet()
    {
        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getDisplay(), content.getSettingsSet());
    }

    @Nullable
    @Override
    public AbstractMessageWidget getAttention()
    {
        return getDisplay().getAttention();
    }

    @Override
    public ContentFormType getContentFormType()
    {
        return ContentFormType.EDIT_CONTENT;
    }

    @Nullable
    @Override
    public <P> PropertyRegistration<P> getPropertyRegistration(Property<P> property)
    {
        return getDisplay().getPropertyRegistration(property);
    }

    @Override
    public Processor getValidationProcessor()
    {
        return validation;
    }

    public void init(T content, UIContext context)
    {
        this.content = content;
        this.context = context;
    }

    @Override
    public void onApply()
    {
        if (!validateForm())
        {
            return;
        }

        // копирование исходного контента на случай rollback
        final T oldContent = (T)content.clone();

        // обновление свойств контента значениями с полей формы
        updateCurrentContent();

        if (isContentEquals(oldContent))
        {
            // Если контент не изменился - просто скрыть форму
            unbind();
        }
        else
        {
            metainfoModificationService.saveUIAndGetSavedContent(
                    getUiFqn(),
                    getUiContext().getRootContent(),
                    content,
                    false,
                    getUiContext().getCode(),
                    getPendingTags(),
                    getSaveUICallback(oldContent));
        }
    }

    protected boolean validateForm()
    {
        return validation.validate();
    }

    /**
     * Fqn формы или карточки, на которой находится редактируемый контент
     */
    protected ClassFqn getUiFqn()
    {
        return getUiContext().getMetainfo().getFqn();
    }

    /**
     * Контекст формы или карточки, на которой находится редактируемый контент
     */
    protected UIContext getUiContext()
    {
        return context;
    }

    protected AsyncCallback<SaveUiAndGetSavedContentResponse> getSaveUICallback(T oldContent)
    {
        return new SafeBasicCallback<SaveUiAndGetSavedContentResponse>(getDisplay())
        {
            @Override
            protected void handleSuccess(SaveUiAndGetSavedContentResponse response)
            {
                response.getPermissions().fillPermissionHolder(
                        ContextUtils.getRootPermissionContext(context).getPermissions());
                EditContentPresenter.this.handleSuccess((T)response.getContent(), oldContent);
                unbind();
            }

            @Override
            protected void handleFailure(Throwable t)
            {
                restoreContent(oldContent);
                super.handleFailure(t);
            }
        };
    }

    protected List<DtObject> getPendingTags()
    {
        return null;
    }

    /**
     * Метод для обновления свойств контента значениями с формы
     */
    protected void updateCurrentContent()
    {
        Layout oldParent = (Layout)content.getParent();
        Layout newParent = treeContext.getContentByDtObject(parent.getValue());
        if (!eq(oldParent, newParent))
        {
            posInParent = oldParent.getContent().indexOf(content);
            content.removeFromParent();
            content.setParent(newParent);
            newParent.getContent().add(content);
        }
        if (settingsSet != null)
        {
            content.setSettingsSet(SelectListPropertyValueExtractor.getValue(settingsSet));
        }
    }

    /**
     * Восстанавливает контент до первоначального состояния
     */
    protected void restoreContent(T oldContent)
    {
        if (isChangeParent(oldContent))
        {
            content.removeFromParent();
            Layout oldParent = (Layout)oldContent.getParent();
            content.setParent(oldContent.getParent());
            oldParent.getContent().add(posInParent, content);
        }
    }

    /**
     * Признак того, что контент не изменился.
     * Проверяются только те свойства, которые можно изменить на форме редактирования.
     */
    protected boolean isContentEquals(T oldContent)
    {
        return eq(oldContent.getParent(), content.getParent())
               && eq(oldContent.getProfiles(), content.getProfiles())
               && eq(oldContent.getVersProfiles(), content.getVersProfiles())
               && eq(oldContent.getVisibilityCondition().getElements(), content.getVisibilityCondition().getElements())
               && eq(oldContent.getTags(), content.getTags())
               && eq(oldContent.getSettingsSet(), content.getSettingsSet());
    }

    /**
     * Метод для использования в потомках чисто для короткого названия
     */
    protected static boolean eq(@Nullable Object obj1, @Nullable Object obj2)
    {
        return ContentUtils.isContentParamEquals(obj1, obj2);
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaptionText(commonMessages.editContentDialogCaption());

        bindProperties();
        super.onBind();

        getDisplay().setFixed(false);
        getDisplay().display();
    }

    protected void bindParent()
    {
        treeContext = new ContentParentTreeFactoryContext(getUiContext(), content);
        treeFactory.createTree(treeContext, new BasicCallback<HasValueOrThrow<DtObject>>()
        {
            @Override
            protected void handleSuccess(HasValueOrThrow<DtObject> value)
            {
                parent = ContentFormHelper.createTreeProperty(treeContext, value, content.getParent(),
                        commonMessages.placeCard());
                getDisplay().add(parent);
            }
        });
    }

    /**
     * Проверить изменился ли у контента родитель
     */
    protected boolean isChangeParent(T newContent)
    {
        return !eq(newContent.getParent(), content.getParent());
    }

    /**
     * Метод для обновления значений контента на клиенте, по данным пришедшим с сервера
     * по умолчанию ничего не делает, чтобы не переопределять в дочерних классах
     *
     * @param savedContent - контент, пришедший с сервера
     */
    protected void updateAfterSave(T savedContent)
    {
    }

    protected void handleSuccess(T savedContent, T oldContent)
    {
        updateAfterSave(savedContent);

        if (isChangeParent(oldContent))
        {
            // Обновить старого родителя (т.к. текущего контента в нем не стало)
            getUiContext().getEventBus().fireEvent(new RefreshContentEvent(oldContent.getParent()));
            // Обновить нового родителя с новым контентом
            getUiContext().getEventBus().fireEvent(new RefreshContentEvent(content.getParent()));
        }
        // Обновить контент
        getUiContext().getEventBus().fireEvent(new RefreshContentEvent(content));
    }

    protected void bindCode()
    {
        code.setCaption(commonMessages.code());
        code.setValue(content.getUuid());
        code.setDisable();
        getDisplay().add(code);
        DebugIdBuilder.ensureDebugId(code, "code");
    }
}