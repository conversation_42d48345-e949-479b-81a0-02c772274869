package ru.naumen.metainfoadmin.client.dynadmin;

import ru.naumen.metainfo.shared.ui.Layout;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие посылаемое при включении и выключении режима редактирования разметки
 *
 * <AUTHOR>
 * @since 25.01.17
 *
 */
public class ChangeLayoutModeEvent extends GwtEvent<ChangeLayoutModeEventHandler>
{
    private static final Type<ChangeLayoutModeEventHandler> TYPE = new Type<ChangeLayoutModeEventHandler>();

    public static Type<ChangeLayoutModeEventHandler> getType()
    {
        return TYPE;
    }

    /**
     * Лэйаут, который переводится в режим разметки
     */
    private final Layout layout;

    private final boolean isLayoutModeEnabled;

    public ChangeLayoutModeEvent(Layout layout, boolean isLayoutModeEnabled)
    {
        this.layout = layout;
        this.isLayoutModeEnabled = isLayoutModeEnabled;
    }

    @Override
    protected void dispatch(ChangeLayoutModeEventHandler handler)
    {
        handler.onChangeLayoutMode(this);
    }

    @Override
    public Type<ChangeLayoutModeEventHandler> getAssociatedType()
    {
        return TYPE;
    }

    public Layout getLayout()
    {
        return layout;
    }

    public boolean isLayoutModeEnabled()
    {
        return isLayoutModeEnabled;
    }
}
