package ru.naumen.metainfoadmin.client.customforms.commands;

import java.util.Collections;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;
import ru.naumen.metainfoadmin.client.customforms.CustomFormContext;
import ru.naumen.metainfoadmin.client.customforms.FormSettingsChangedEvent;
import ru.naumen.metainfoadmin.shared.customforms.MoveParameterAction;

/**
 * Абстрактная реализация команды перемещения параметра настраиваемой формы
 *
 * <AUTHOR>
 * @since 13 мая 2016 г.
 */
public abstract class AbstractMoveParamCommand extends BaseCommandImpl<Attribute, Void>
{
    @Inject
    private DispatchAsync dispatch;

    private final CustomFormContext context;
    private final int direction;

    public AbstractMoveParamCommand(AttributeCommandParam param, int direction)
    {
        super(param);
        context = (CustomFormContext)param.getContext();
        this.direction = direction;
    }

    @Override
    public void execute(final CommandParam<Attribute, Void> param)
    {
        dispatch.execute(new MoveParameterAction(param.getValue().getFqn(), direction), new BasicCallback<EmptyResult>()
        {
            @Override
            protected void handleSuccess(EmptyResult result)
            {
                int index = indexOf(param.getValue());
                Collections.swap(context.getForm().getAttributes(), index, index + direction);
                context.getEventBus().fireEvent(new FormSettingsChangedEvent(context.getCustomForm()));
            }
        });
    }

    @Override
    public boolean isPossible(Object param)
    {
        int index = indexOf((Attribute)param);
        index += direction;
        return index >= 0 && index < context.getForm().getAttributes().size();
    }

    private int indexOf(final Attribute param)
    {
        return context.getForm().getAttributes().indexOf(param);
    }
}
