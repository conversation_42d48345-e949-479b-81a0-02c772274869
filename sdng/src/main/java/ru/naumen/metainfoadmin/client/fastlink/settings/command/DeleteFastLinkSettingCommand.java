package ru.naumen.metainfoadmin.client.fastlink.settings.command;

import java.util.List;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.Dialogs.DialogResult;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSetting;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSettingWithTitles;
import ru.naumen.metainfo.shared.fastlink.settings.dispatch.DeleteFastLinkSettingsAction;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsMessages;

/**
 * <AUTHOR>
 * @since 01.03.18
 */
public class DeleteFastLinkSettingCommand extends BaseCommandImpl<FastLinkSetting,
        List<DtoContainer<FastLinkSettingWithTitles>>>
{
    public static final String ID = "deleteFastLinkSettingCommand";

    @Inject
    private Dialogs dialogs;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private FastLinkSettingsMessages messages;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private MetainfoUtils metainfoUtils;

    @Inject
    public DeleteFastLinkSettingCommand(@Assisted FastLinkSettingsCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(final CommandParam<FastLinkSetting, List<DtoContainer<FastLinkSettingWithTitles>>> param)
    {
        final FastLinkSettingsCommandParam p = (FastLinkSettingsCommandParam)prepareParam(param);
        dialogs.question(cmessages.confirmDelete(), question(p), new DialogCallback()
        {
            @Override
            public void handleSuccess(DialogResult result)
            {
                result.getWidget().hide();
                if (Dialogs.Buttons.YES == result.getButtons())
                {
                    yesDelete(p);
                }
                else
                {
                    cancelDelete(p);
                }
            }
        });

    }

    protected void cancelDelete(FastLinkSettingsCommandParam param)
    {
        param.getCallback().onSuccess(null);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    protected String question(final FastLinkSettingsCommandParam p)
    {
        return cmessages.confirmDeleteQuestion(messages.objectMention(),
                metainfoUtils.getLocalizedValue(p.getValue().getTitle()));
    }

    protected void yesDelete(final FastLinkSettingsCommandParam param)
    {
        FastLinkSetting item = param.getValue();

        if (param.getSettings() != null)
        {
            param.getSettings().remove(item);
        }

        DeleteFastLinkSettingsAction action = new DeleteFastLinkSettingsAction(item.getCode());
        dispatch.execute(action,
                new SimpleResultCallbackDecorator<>(param.getCallbackSafe()));

    }
}