package ru.naumen.metainfoadmin.client.escalation.actions;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.EVENT_ACTIONS;

import java.util.HashSet;

import com.google.common.collect.ImmutableList;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import ru.naumen.admin.client.permission.AccessMarkerViewPermissionControlled;
import ru.naumen.core.client.DisplayHolder;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.select.popup.PopupListSelectResources;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.client.eventaction.EventActionsPresenterSettings;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.ui.EventActionList;
import ru.naumen.metainfoadmin.client.eventaction.list.EscalationActionsAdvlistFactory;
import ru.naumen.metainfoadmin.client.eventaction.list.EventActionsAdvlistFactory;
import ru.naumen.objectlist.client.ListPresenter;

/**
 * Презентер списка действий по событию в разделе эскалации.
 *
 * <AUTHOR>
 * @since 23.07.2012
 */
public class EscalationActionsPresenter extends BasicPresenter<DisplayHolder>
        implements AccessMarkerViewPermissionControlled
{
    private final EventActionsAdvlistFactory advlistFactory;

    private final String addEventActionCommandCode;
    private ListPresenter<EventActionList> listPresenter;

    private final HashSet<EventType> permittedTypes;
    private final HashSet<EventType> prohibitedTypes;
    private final ImmutableList<AttributeFqn> attrs;

    private final EventActionsPresenterSettings settings;

    @Inject
    public EscalationActionsPresenter(DisplayHolder display, EventBus eventBus,
            EscalationActionsAdvlistFactory advlistFactory,
            @Named(EscalationActionsGinModule.ESCALATION_EVENT_ACTION_PERMITTED) HashSet<EventType> permittedTypes,
            @Named(EscalationActionsGinModule.ESCALATION_EVENT_ACTION_PROHIBITED) HashSet<EventType> prohibitedTypes,
            @Named(EscalationActionsGinModule.ESCALATION_EVENT_ACTION_ATTRS) ImmutableList<AttributeFqn> attrs,
            EventActionsPresenterSettings settings)
    {
        super(display, eventBus);
        this.permittedTypes = permittedTypes;
        this.prohibitedTypes = prohibitedTypes;
        this.attrs = attrs;
        this.advlistFactory = advlistFactory;
        this.settings = settings;
        this.addEventActionCommandCode = EscalationActionsGinModule.ADD_ESCALATION_EVENT_ACTION;
    }

    @Override
    public void refreshDisplay()
    {
        listPresenter.refreshDisplay();
    }

    @Inject
    protected void init(PopupListSelectResources resources)
    {
        resources.styles().ensureInjected();
    }

    @Override
    protected void onBind()
    {
        listPresenter = advlistFactory.create(permittedTypes, prohibitedTypes, addEventActionCommandCode, attrs,
                settings.isWithLinkInEscalation());

        getDisplay().add(listPresenter.getDisplay());
        listPresenter.bind();
        refreshDisplay();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return EVENT_ACTIONS;
    }
}