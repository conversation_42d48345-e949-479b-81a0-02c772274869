package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode.ICON;

import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.PropertyUtils;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.Constants.IconsForControlsCatalog;
import ru.naumen.core.shared.Constants.ImageFileExtension;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.dispatch2.catalog.GetCatalogIconsAction;

/**
 * Делегат биндинга свойства "Иконка" элемента левого меню
 *
 * Возможно, стоит заменить на IconBindDelegate
 *
 * <AUTHOR>
 * @since 27.06.2020
 */
public class MenuSettingsIconBindDelegateImpl extends PropertyDelegateBindImpl<SelectItem, DtObjectSelectProperty>
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private CommonMessages commonMessages;

    @Override
    public void bindProperty(PropertyContainerContext context, DtObjectSelectProperty property,
            AsyncCallback<Void> callback)
    {
        DtObject selectedIcon = context.getPropertyValues().<DtObject> getProperty(ICON);
        final SingleSelectCellList<DtObject> widget = property.getValueWidget();
        List<String> iconTypes = Lists.newArrayList(IconsForControlsCatalog.CODE);
        PropertyUtils.addDescriptionIconWithHint(commonMessages.iconsFormat(
                        StringUtilities.join(ImageFileExtension.VECTOR_IMAGE_EXTENSIONS, ", ").toUpperCase()),
                property, false);
        GetCatalogIconsAction getCatalogIconsAction = new GetCatalogIconsAction(iconTypes,
                ImageFileExtension.VECTOR_IMAGE_EXTENSIONS, false);
        dispatch.execute(getCatalogIconsAction, new BasicCallback<SimpleResult<List<DtObject>>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<List<DtObject>> response)
            {
                widget.addItems(response.get(), true);
                property.trySetObjValue(selectedIcon);
                context.getPropertyValues().setProperty(ICON, selectedIcon);
                callback.onSuccess(null);
            }
        });
    }
}
