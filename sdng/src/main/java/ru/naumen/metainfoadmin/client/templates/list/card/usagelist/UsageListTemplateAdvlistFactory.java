package ru.naumen.metainfoadmin.client.templates.list.card.usagelist;

import java.util.ArrayList;
import java.util.Collection;
import java.util.function.Predicate;

import com.google.inject.Singleton;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.ui.toolbar.SimpleActionToolFactory;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsageListTemplate;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.Tool.PresentationType;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMessages;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.extended.advlist.FeatureCodes;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;
import ru.naumen.objectlist.shared.AdvListMassOperationLightActionDesc;
import ru.naumen.objectlist.shared.AdvListMassOperationLightContext;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Фабрика списка мест использования "Шаблона списка"
 * <AUTHOR>
 * @since 06.08.2018
 */
@Singleton
public class UsageListTemplateAdvlistFactory extends AdminCustomAdvlistFactoryBase
{
    @Inject
    private ListTemplatesMessages listTemplatesMessages;

    private final Predicate<DtObject> enabledCondition = dto -> !UI.LEFT_MENU_ITEM.equals(
            dto.getProperty(UsageListTemplate.FORM_CODE));

    private final Predicate<Collection<DtObject>> enabledConditions = dtos -> dtos.stream().anyMatch(enabledCondition);

    @Override
    public ListPresenter<CustomList> create(@Nullable Context context)
    {
        ListPresenter<CustomList> list = advlistPresenterProvider.get();
        MetaClass metaClass = getMetaClassInt();
        CustomList objectList = createContent(context, metaClass);
        list.init(objectList, createUIContext(list, metaClass, context));
        return list;
    }

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        ArrayList<ExtendedListActionCellContext> actionColumns = new ArrayList<>();
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.DELETE,
                UsageListTemplateCommandCode.BREAK_LINK, cmessages.breakLink(), enabledCondition));
        return actionColumns;
    }

    @Override
    protected AdvListMassOperationLightContext createAdvListMassOperationLightContext()
    {
        return new AdvListMassOperationLightContext(
                new AdvListMassOperationLightActionDesc(UsageListTemplateCommandCode.APPLY, listTemplatesMessages
                        .apply(), enabledConditions::test),
                new AdvListMassOperationLightActionDesc(UsageListTemplateCommandCode.BREAK_LINK, cmessages
                        .breakLink().toLowerCase(), enabledConditions::test));
    }

    @Override
    protected CustomList createContent(@Nullable Context context, MetaClass metaclass)
    {
        final CustomList content = super.createContent(context, metaclass);
        content.setDefaultPageSize(20);
        content.setFeatures(FeatureCodes.PAGING, FeatureCodes.SELECTION, FeatureCodes.MASS_OPERATION_LIGHT);
        return content;
    }

    @Override
    protected ToolPanel createToolPanel(CustomList content)
    {
        ToolPanel panel = new ToolPanel(content);
        content.setToolPanel(panel);

        ToolBar addToolBar = new ToolBar(panel);
        panel.getToolBars().add(addToolBar);

        Tool addTool = tfInitializer.initFactory(new SimpleActionToolFactory(
                        UsageListTemplateCommandCode.APPLY_FOR_ALL, UsageListTemplateCommandCode.APPLY_FOR_ALL,
                        PresentationType.DEFAULT, listTemplatesMessages.applyForAllList()))
                .create()
                .setToolBar(addToolBar);
        addToolBar.addTool(addTool);

        return panel;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return UsageListTemplate.FQN;
    }
}
