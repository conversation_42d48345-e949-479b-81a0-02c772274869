package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import jakarta.annotation.Nullable;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.ReferenceHelper;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления для виджета "Атрибут связи"
 * {@link ru.naumen.metainfo.shared.Constants.ReferenceCode#ATTRIBUTE_CHAIN}
 *
 * <AUTHOR>
 * @since 20.01.2023
 */
public class HomePageAttrChainDelegateRefresh implements PropertyDelegateRefresh<RelationsAttrTreeObject,
        PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<?, RelationsAttrTreeObject, ?>>>
{
    @Override
    public void refreshProperty(PropertyContainerContext context,
            @Nullable PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<?, RelationsAttrTreeObject, ?>> property,
            AsyncCallback<Boolean> callback)
    {
        String objectClass = context.getPropertyValues().getProperty(HomePage.REFERENCE_CARD_TYPE, null);
        boolean isVisible = objectClass != null
                            && ReferenceHelper.AVAILABLE_MENU_ITEM_TYPES_OF_CARD.contains(objectClass);
        if (!isVisible)
        {
            if (property != null)
            {
                property.clearValue();
            }
            context.getPropertyControllers().get(ReferenceCode.ATTRIBUTE_CHAIN).unbindValidators();
        }
        callback.onSuccess(isVisible);
    }
}