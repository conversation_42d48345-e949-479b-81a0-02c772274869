package ru.naumen.metainfoadmin.client.attributes.commands;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;

/**
 *
 * <AUTHOR>
 */
public class CommandsGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        bind(AttributeCommandFactoryInitializer.class).asEagerSingleton();
        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, DelCommand.class)
            .build(new TypeLiteral<CommandProvider<DelCommand, AttributeCommandParam>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, EditCommand.class)
            .build(new TypeLiteral<CommandProvider<EditCommand, AttributeCommandParam>>() {}));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, ShowUsageAttrCommand.class)
                .build(new TypeLiteral<CommandProvider<ShowUsageAttrCommand, AttributeCommandParam>>() {}));
        //@formatter:on
    }
}
