package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import jakarta.inject.Inject;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.SimplePanel;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.AbstractContentPresenter;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextChangedEvent;
import ru.naumen.core.client.content.ContextChangedHandler;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfoadmin.client.CommonUtils;
import ru.naumen.metainfoadmin.client.MGinjector;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.common.content.commands.ContentCommandParam;
import ru.naumen.metainfoadmin.client.common.content.commands.FormContentCommandCode;
import ru.naumen.metainfoadmin.client.common.content.commands.TabContentCommandCode;
import ru.naumen.metainfoadmin.client.common.content.commands.TabContentCommandParam;
import ru.naumen.metainfoadmin.client.dynadmin.BasicUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeContextEditableEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeContextEditableHandler;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeLayoutModeEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeLayoutModeEventHandler;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.rename.StringRenamedEvent;
import ru.naumen.metainfoadmin.client.rename.StringRenamedHandler;

/**
 * {@link Presenter} настройки формы добавления.
 *
 * <AUTHOR>
 * @since 08.09.2010
 *
 */
public class FormContentPresenter extends AbstractContentPresenter<FormContentDisplay, Form, UIContext>
        implements StringRenamedHandler
{
    @Inject
    MGinjector injector;
    @Inject
    DisplayMessages messages;
    @Inject
    CommonMessages cmessages;
    @Inject
    AdminContentFactory factory;
    @Inject
    MetainfoUtils metainfoUtils;
    @Inject
    MetainfoServiceAsync metainfoService;
    @Inject
    ButtonFactory buttonFactory;
    @Inject
    CommonUtils commonUtils;
    @Inject
    MetainfoModificationServiceAsync metainfoModificationService;

    LayoutContentPresenter layoutPresenter;
    private final ToolBarDisplayMediator<Form> toolBar;

    @Inject
    public FormContentPresenter(FormContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "Form");
        toolBar = new ToolBarDisplayMediator<Form>(getDisplay().getToolBar());
    }

    @Override
    public void onHide()
    {
        layoutPresenter.hideDisplay();
    }

    @Override
    public void onReveal()
    {
        layoutPresenter.revealDisplay();
    }

    @Override
    public void onStringRenamed(final StringRenamedEvent event)
    {
        final String oldName = metainfoUtils.getLocalizedValue(getContext().getRootContent().getCaption());
        final String name = event.getValue();
        ru.naumen.core.shared.utils.CommonUtils.assertNotNull(name, "Content title");
        metainfoUtils.setCaption(getContext().getRootContent().getCaption(), name);
        ClassFqn fqn = getContext().getMetainfo().getFqn();
        metainfoModificationService.saveUI(fqn, getContext().getRootContent(), null, getContext().getCode(), null,
                true, new SafeBasicCallback<Void>(getDisplay())
                {
                    @Override
                    protected void handleFailure(Throwable t)
                    {
                        super.handleFailure(t);
                        // откатываем изменения
                        metainfoUtils.setCaption(getContext().getRootContent().getCaption(), oldName);
                    }

                    @Override
                    protected void handleSuccess(Void result)
                    {
                        event.getPresenter().unbind();
                        refreshDisplay();
                    }
                });
    }

    @Override
    public void refreshDisplay()
    {
        layoutPresenter.refreshDisplay();
        toolBar.refresh();
        display.setCaption(metainfoUtils.getLocalizedValue(getContent().getCaption()));
        getDisplay().setToolBarVisible(getContext().isEditable());
    }

    @Override
    protected void onBind()
    {
        layoutPresenter = factory.build(getContent().getLayout(), getContext());
        display.setContent(layoutPresenter.getDisplay());
        SimplePanel layoutEditPanel = new SimplePanel();
        layoutPresenter.getDisplay().setLayoutEditPanel(layoutEditPanel);
        display.getBodyPanel().add(layoutEditPanel);
        initToolBar();
        refreshDisplay();
        registerHandler(getContext().getEventBus().addHandler(ChangeContextEditableEvent.getType(),
                new ChangeContextEditableHandler()
                {
                    @Override
                    public void onChangeEditable(ChangeContextEditableEvent e)
                    {
                        refreshEditable();
                    }
                }));
        registerHandler(getContext().getEventBus().addHandler(ContextChangedEvent.getType(),
                new ContextChangedHandler<Context>()
                {
                    @Override
                    public void onContextChanged(ContextChangedEvent<Context> e)
                    {
                        refreshDisplay();
                    }
                }));
        registerHandler(getContext().getEventBus().addHandler(ChangeLayoutModeEvent.getType(),
                new ChangeLayoutModeEventHandler()
                {
                    @Override
                    public void onChangeLayoutMode(ChangeLayoutModeEvent e)
                    {
                        if (getContext() instanceof BasicUIContext)
                        {
                            ((BasicUIContext)getContext())
                                    .setContentInLayoutEditMode(e.isLayoutModeEnabled() ? e.getLayout() : null);
                            Scheduler.get().scheduleDeferred(new Scheduler.ScheduledCommand()
                            {
                                @Override
                                public void execute()
                                {
                                    toolBar.refresh(getContent());
                                }
                            });
                        }
                    }
                }));
        registerHandler(getContext().getEventBus().addHandler(StringRenamedEvent.getType(), this));
        refreshEditable();
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        toolBar.unbind();
        if (null != layoutPresenter)
        {
            layoutPresenter.unbind();
        }
        display.destroy();
    }

    protected void refreshEditable()
    {
        getDisplay().setEditable(getContext().isEditable() && metainfoUtils.isPossible(getContext().getMetainfo(), true)
                                 && commonUtils.uiNotInherit(getContext()));
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    private void initToolBar()
    {
        BasicCallback callback = new BasicCallback<Void>(getDisplay())
        {
            @Override
            protected void handleSuccess(Void value)
            {
                refreshDisplay();
            }
        };
        toolBar.add((ButtonPresenter<Form>)buttonFactory.create(ButtonCode.ADD, messages.addContent(),
                TabContentCommandCode.ADD_CONTENT,
                new TabContentCommandParam(null, callback, getContext(), layoutPresenter.getContent())));
        toolBar.add((ButtonPresenter<Form>)buttonFactory.create(ButtonCode.EDIT, messages.renameButton(),
                FormContentCommandCode.RENAME_FORM_CONTENT,
                new ContentCommandParam(null, callback, getContext(), layoutPresenter.getContent())));
        toolBar.add((ButtonPresenter<Form>)buttonFactory.create(ButtonCode.ENABLE_LAYOUT_MODE, cmessages.layoutMode(),
                TabContentCommandCode.ENABLE_LAYOUT_MODE,
                new TabContentCommandParam(null, callback, getContext(), layoutPresenter.getContent())));
        toolBar.add((ButtonPresenter<Form>)buttonFactory.create(ButtonCode.ENABLE_LAYOUT_MODE,
                cmessages.exitFromLayoutMode(), TabContentCommandCode.DISABLE_LAYOUT_MODE,
                new TabContentCommandParam(null, callback, getContext(), layoutPresenter.getContent())));
        toolBar.bind();
        toolBar.refresh(getContent());
    }
}
