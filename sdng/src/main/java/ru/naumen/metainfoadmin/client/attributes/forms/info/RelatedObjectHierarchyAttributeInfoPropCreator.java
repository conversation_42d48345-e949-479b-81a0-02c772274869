package ru.naumen.metainfoadmin.client.attributes.forms.info;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;

/**
 * Создает {@link Property} для отображения информации об уровне иерархии атрибута связанного объекта
 *
 * <AUTHOR>
 * @since 14.09.18
 */
public class RelatedObjectHierarchyAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    private AttributesMessages attributesMessages;

    @Override
    protected void createInt(String code)
    {
        int level = attribute.getType().getRelatedObjectHierarchyLevel();
        if (!attribute.getType().isAttributeOfRelatedObject()
            || level == 0)
        {
            return;
        }

        if (level == -1)
        {
            createProperty(code, attributesMessages.parentHierarchyTop());
        }
        else if (level == 1)
        {
            createProperty(code, attributesMessages.parentHierarchy1());
        }
        else if (level == 2)
        {
            createProperty(code, attributesMessages.parentHierarchy2());
        }
        else if (level == 3)
        {
            createProperty(code, attributesMessages.parentHierarchy3());
        }
        else
        {
            createProperty(code, attributesMessages.parentHierarchyN(Integer.toString(level)));
        }
    }
}
