/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.columns;

import static ru.naumen.metainfo.shared.Constants.Presentations.TEXT_VIEW;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.HasValue;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.attr.presentation.PresentationFactoryView;
import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.client.widgets.grouplist.AbstractBaseColumn;
import ru.naumen.core.shared.dispatch.FastSelectionChangesWithValue;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode;

/**
 * <AUTHOR>
 *
 */
public class DefaultValueColumn extends AbstractBaseColumn<Attribute>
{
    @Inject
    PresentationFactories presentationFactories;
    @Inject
    AttributesMessages messages;

    @Inject
    public DefaultValueColumn()
    {
        super(AttributeColumnCode.DEFAULT_VALUE);
    }

    @Override
    @SuppressWarnings("unchecked")
    public IsWidget createWidget(WidgetContext<Attribute> context)
    {
        PresentationContext presentationContext = (PresentationContext)context;
        presentationContext.setIsDefaultValue(true);
        Attribute attr = context.getContextObject();
        try
        {
            String prsCode = attr.isDefaultByScript() ? TEXT_VIEW : attr.getViewPresentation().getCode();

            presentationContext.setPresentationCode(prsCode);
            PresentationFactoryView<Object> pf = presentationFactories.getViewPresentationFactory(prsCode);

            IsWidget widget = pf.createWidget(presentationContext);
            if (null == widget)
            {
                throw new UnsupportedOperationException();
            }
            //Виджеты, которые возвращает PrsFactoryBase, - это CellWidget
            //Необходимо дополнительно инициализировать их значением по умолчанию, которое будет отображаться
            ensureDebugId(widget, attr.getCode());
            Object defaultValue = attr.isDefaultByScript() ? messages.script() : attr.getDefaultValue();
            if (defaultValue instanceof FastSelectionChangesWithValue)
            {
                defaultValue = ((FastSelectionChangesWithValue)defaultValue).getTreeValue();
            }
            if (attr.getType().getCode().equals(DateTimeIntervalAttributeType.CODE) && defaultValue == null)
            {
                defaultValue = new DateTimeInterval(Interval.SECOND);
            }
            try
            {
                ((HasValue<Object>)widget.asWidget()).setValue(defaultValue);
                return widget;
            }
            catch (Exception e)
            {
                return widget;
            }
        }
        catch (Exception e)
        {
            throw new FxException("Can't create widget for " + attr.getCode() + ", presentation="
                                  + attr.getViewPresentation().getCode(), e);
        }
    }
}
