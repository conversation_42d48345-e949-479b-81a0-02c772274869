package ru.naumen.metainfoadmin.client.escalation.schemes.commands;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.metainfoadmin.client.escalation.schemes.commands.EscalationSchemesCommandsGinModule.EscalationCommandCode;

/**
 * Регистрация команд в фабрике
 *
 * <AUTHOR>
 * @since 14.12.18
 */
public class EscalationSchemesCommandFactoryInitializer
{
    //@formatter:off
    @Inject
    public EscalationSchemesCommandFactoryInitializer(
            CommandFactory factory,
            CommandProvider<EscalationSchemeEditCommand, EscalationSchemesCommandParam> editCommandProvider,
            CommandProvider<EscalationSchemeDeleteCommand, EscalationSchemesCommandParam> deleteCommandProvider,
            CommandProvider<EscalationSchemeToggleCommand, EscalationSchemesCommandParam> toggleCommandProvider)
    {
        //@formatter:on
        factory.register(EscalationCommandCode.EDIT, editCommandProvider);
        factory.register(EscalationCommandCode.DELETE, deleteCommandProvider);
        factory.register(EscalationCommandCode.TOGGLE, toggleCommandProvider);
    }
}
