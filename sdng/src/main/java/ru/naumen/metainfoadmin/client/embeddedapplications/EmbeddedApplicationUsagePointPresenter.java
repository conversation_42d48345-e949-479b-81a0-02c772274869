package ru.naumen.metainfoadmin.client.embeddedapplications;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Provider;

import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.components.block.TitledBlockDisplay;
import ru.naumen.core.client.components.block.TitledBlockPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.objectlist.client.AddMetainfoObjectEvent;
import ru.naumen.objectlist.client.AddMetainfoObjectHandler;
import ru.naumen.metainfoadmin.client.dynadmin.BasicUIContext;
import ru.naumen.metainfoadmin.client.embeddedapplications.usagelist.UsagePointsListApplicationAdvlistFactory;
import ru.naumen.metainfoadmin.client.embeddedapplications.usagelist.UsagePointsListApplicationCommandCode;
import ru.naumen.metainfoadmin.client.embeddedapplications.usagelist.commands.UsagePointApplicationCommandParam;
import ru.naumen.metainfoadmin.client.embeddedapplications.usagelist.form.AddUsagePointApplicationFormPresenter;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionEvent;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionHandler;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Презентер блока "Места использования приложения на модальных формах" на карточке ВП
 * <AUTHOR>
 * @since 20.10.2021
 */
public class EmbeddedApplicationUsagePointPresenter extends TitledBlockPresenter implements ObjectListActionHandler,
        AddMetainfoObjectHandler
{
    private final EmbeddedApplicationMessages embeddedApplicationMessages;
    private final UsagePointsListApplicationAdvlistFactory listFactory;
    private final CommandFactory commandFactory;
    private final Provider<AddUsagePointApplicationFormPresenter> formProvider;

    private EmbeddedApplicationAdminSettingsDto embeddedApplication;
    private ListPresenter<CustomList> listPresenter;

    private final BasicCallback<Void> refreshCallback = new BasicCallback<Void>(getDisplay())
    {
        @Override
        protected void handleSuccess(Void applicationUsagePoint)
        {
            listPresenter.refreshDisplay();
            listPresenter.getListComponents().getSelectionModel().clear();
        }
    };

    @Inject
    public EmbeddedApplicationUsagePointPresenter(TitledBlockDisplay display, EventBus eventBus,
            EmbeddedApplicationMessages embeddedApplicationMessages,
            UsagePointsListApplicationAdvlistFactory listFactory,
            CommandFactory commandFactory,
            Provider<AddUsagePointApplicationFormPresenter> formProvider)
    {
        super(display, eventBus);
        this.embeddedApplicationMessages = embeddedApplicationMessages;
        this.listFactory = listFactory;
        this.commandFactory = commandFactory;
        this.formProvider = formProvider;
        display.asWidget().ensureDebugId("usagePlaces");
    }

    @Override
    public void init(ListComponents components)
    {
    }

    public void init(EmbeddedApplicationAdminSettingsDto embeddedApplication)
    {
        this.embeddedApplication = embeddedApplication;
    }

    @Override
    public void onAddMetainfoObject(AddMetainfoObjectEvent event)
    {
        AddUsagePointApplicationFormPresenter presenter = formProvider.get();
        presenter.init(embeddedApplication, null, refreshCallback);
        presenter.bind();
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Override
    public void onObjectListAction(ObjectListActionEvent event)
    {
        if (UsagePointsListApplicationCommandCode.COMMANDS_IN_LIST.contains(event.getAction()))
        {
            CommandParam commandParam = new UsagePointApplicationCommandParam(embeddedApplication, event.getValues(),
                    refreshCallback);
            BaseCommand<?, ?> command = commandFactory.create(event.getAction(), commandParam);
            command.execute(commandParam);
        }
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaption(embeddedApplicationMessages.usagePlaces());

        BasicUIContext context = new BasicUIContext(null, null);
        String code = embeddedApplication.getCode();
        context.setObject(new SimpleDtObject(code, code));
        listPresenter = listFactory.create(context);
        registerChildPresenter(listPresenter, true);
        Display listDisplay = listPresenter.getDisplay();
        listDisplay.asWidget().ensureDebugId("usagePointsListApplication");
        getDisplay().setControlledWidget(listDisplay);

        listPresenter.refreshDisplay();
        registerHandler(eventBus.addHandler(ObjectListActionEvent.getType(), this));
        registerHandler(listPresenter.getListComponents().getLocalEventBus()
                .addHandler(AddMetainfoObjectEvent.getType(), this));
    }
}
