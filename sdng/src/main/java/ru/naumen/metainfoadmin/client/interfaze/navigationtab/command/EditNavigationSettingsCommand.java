package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.EditNavigationSettingsMenuVisibilityAction;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormPresenter;

/**
 * <AUTHOR>
 * @since 20 сент. 2013 г.
 */
public class EditNavigationSettingsCommand extends
        PresenterCommandImpl<DtoContainer<NavigationSettings>, DtoContainer<NavigationSettings>,
                DtoContainer<NavigationSettings>>
{
    public static final String ID = "EditNavigationSettingsCommand";

    @Inject
    private DispatchAsync dispatch;
    @Inject
    private Provider<EditNavigationSettingsFormPresenter> editFormProvider;

    @Inject
    public EditNavigationSettingsCommand(
            @Assisted CommandParam<DtoContainer<NavigationSettings>, DtoContainer<NavigationSettings>> param)
    {
        super(param);
    }

    @Override
    public void onExecute(DtoContainer<NavigationSettings> dtoSettings,
            CallbackDecorator<DtoContainer<NavigationSettings>, DtoContainer<NavigationSettings>> callback)
    {
        NavigationSettings settings = dtoSettings.get();
        dispatch.execute(
                new EditNavigationSettingsMenuVisibilityAction(settings.isShowTopMenu(),
                        settings.getLeftMenu().isEnabled(),
                        settings.getQuickAccessPanelSettings().isShowAdminArea(),
                        settings.getQuickAccessPanelSettings().isShowUserArea(),
                        settings.getQuickAccessPanelSettings().isShowSystemArea(),
                        settings.isShowBreadCrumb(),
                        settings.isShowHomePage()),
                new SimpleResultCallbackDecorator<>(callback));
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected CallbackPresenter<DtoContainer<NavigationSettings>, DtoContainer<NavigationSettings>> getPresenter(
            DtoContainer<NavigationSettings> value)
    {
        return editFormProvider.get();
    }
}
