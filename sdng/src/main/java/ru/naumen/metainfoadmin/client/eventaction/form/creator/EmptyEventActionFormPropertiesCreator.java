package ru.naumen.metainfoadmin.client.eventaction.form.creator;

import java.text.ParseException;
import java.util.List;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.Label;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;

/**
 * Заглушка для создания свойсв на форме ДПС
 *
 * <AUTHOR>
 * @since 26.01.2012
 */
public class EmptyEventActionFormPropertiesCreator extends AbstractEventActionFormPropertiesCreator<Action>
{
    static class EmptyInfoProperty extends PropertyBase<String, EmptyInfoWidget>
    {
        EmptyInfoProperty()
        {
            super(new EmptyInfoWidget());
        }
    }

    static class EmptyInfoWidget extends FlowPanel implements HasValueOrThrow<String>, HasEnabled
    {
        Label emptyInfoLabel = new Label();

        EmptyInfoWidget()
        {
            add(emptyInfoLabel);
            addStyleName(WidgetResources.INSTANCE.form().bLightboxFormWideGridEmptyInfo());
        }

        @Override
        public HandlerRegistration addValueChangeHandler(ValueChangeHandler<String> handler)
        {
            return addHandler(handler, ValueChangeEvent.getType());
        }

        @Override
        public String getValue()
        {
            return emptyInfoLabel.getText();
        }

        @Override
        public String getValueOrThrow() throws ParseException
        {
            return getValue();
        }

        @Override
        public boolean isEnabled()
        {
            return true;
        }

        @Override
        public void setEnabled(boolean enabled)
        {
        }

        @Override
        public void setValue(String value)
        {
            emptyInfoLabel.setText(value);
        }

        @Override
        public void setValue(String value, boolean fireEvents)
        {
        }

        void setText(String text)
        {
            emptyInfoLabel.setText(text);
        }
    }

    @Override
    public void bindProperties(EventActionFormDisplay display, List<ClassFqn> fqns)
    {
        super.bindProperties(display, fqns);
        EmptyInfoProperty emptyInfoProperty = new EmptyInfoProperty();
        emptyInfoProperty.setValue(messages.emptyEventActionInfo());
        add(emptyInfoProperty);
    }

    @Override
    public EventActionWithScript getEventAction()
    {
        return eventAction;
    }

    @Override
    protected void setActionProperties(Action action)
    {
    }

    @Override
    protected Action newEventActionTypeInstance()
    {
        return null;
    }

    @Override
    public void init(EventActionWithScript eventAction, Property<SelectItem> event)
    {
        this.eventAction = eventAction;
        super.init(eventAction, eventProperty);
    }
}
