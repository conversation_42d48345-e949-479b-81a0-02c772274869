package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.defaultsettings;

import java.util.Collections;
import java.util.Comparator;
import java.util.ArrayList;
import java.util.List;
import java.util.Map.Entry;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.common.collect.Lists;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.shared.dynadmin.HierarchyItemSettingsContext;
import ru.naumen.objectlist.client.mode.active.ObjectListActive;

/**
 * Команда настройки вида по умолчанию для уровня иерархического списка.
 * <AUTHOR>
 * @since 11.01.2021
 */
public class EditHierarchyItemDefaultSettingsCommand
        extends BaseCommandImpl<HierarchyItemSettingsContext, HierarchyItemSettingsContext>
{
    private final Provider<HierarchyDefaultSettingsFormPresenter> formProvider;
    private final ILocaleInfo localeInfo;

    @Inject
    public EditHierarchyItemDefaultSettingsCommand(@Assisted HierarchyDefaultSettingsCommandParam param,
            Provider<HierarchyDefaultSettingsFormPresenter> formProvider,
            ILocaleInfo localeInfo)
    {
        super(param);
        this.formProvider = formProvider;
        this.localeInfo = localeInfo;
    }

    @Override
    public void execute(CommandParam<HierarchyItemSettingsContext, HierarchyItemSettingsContext> param)
    {
        HierarchyDefaultSettingsCommandParam commandParam = (HierarchyDefaultSettingsCommandParam)getParam();
        HierarchyItemSettingsContext hierarchyItem = param.getValue();

        ObjectListBase list = new ObjectListBase();
        list.setClazz(hierarchyItem.getClazz());
        list.setCases(Lists.newArrayList(hierarchyItem.getCases()));
        list.setCaption(Collections.singletonList(new LocalizedString(localeInfo.getCurrentLang(),
                hierarchyItem.getTitle())));
        ObjectListUIContext parentContext = new ObjectListUIContext(commandParam.getContext(),
                commandParam.getContext().getRootContentInfo(), false, list);
        parentContext.setMode(new ObjectListActive());
        List<String> attributeCodes = new ArrayList<>();
        hierarchyItem.getAttributes().entrySet()
                .stream()
                .sorted(Comparator.comparing(e -> e.getValue().getTitle()))
                .map(Entry::getKey)
                .forEach(attributeCodes::add);
        parentContext.getMode().setAttributeCodes(attributeCodes);
        parentContext.getMode().setAttributes(hierarchyItem.getAttributes());
        parentContext.getMode().setDisabledAttributes(hierarchyItem.getDisabledAttributes());

        HierarchyDefaultSettingsFormPresenter form = formProvider.get();
        form.init(parentContext, hierarchyItem, param.getCallback());
        form.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}
