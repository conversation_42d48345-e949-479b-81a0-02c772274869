package ru.naumen.metainfoadmin.client.customforms;

import ru.naumen.metainfoadmin.shared.customforms.ModificationContext;

/**
 * Фабрика презентера списка параметров настраиваемой формы
 *
 * <AUTHOR>
 * @since 22 апр. 2016 г.
 */
public interface CustomFormParametersPresenterFactory
{
    /**
     * Создает презентер списка параметров настраиваемой формы
     *
     * @param context - контекст настройки
     * @return
     */
    CustomFormParametersPresenter create(ModificationContext context);
}
