package ru.naumen.metainfoadmin.client.customforms;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.customforms.CustomFormFakeMetaClass;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfoadmin.client.DefaultContext;
import ru.naumen.metainfoadmin.shared.customforms.ModificationContext;

/**
 * Контекст интерфейса настройки настраиваемой формы 
 *
 * <AUTHOR>
 * @since 21 апр. 2016 г.
 */
public class CustomFormContext extends DefaultContext
{
    private final ModificationContext modificationContext;
    private DtoContainer<CustomForm> customForm;

    public CustomFormContext(ModificationContext modificationContext)
    {
        super(new CustomFormFakeMetaClass("form-template"));
        this.modificationContext = modificationContext;
    }

    public CustomForm getForm()
    {
        return customForm != null ? customForm.get() : null;
    }

    public DtoContainer<CustomForm> getCustomForm()
    {
        return customForm;
    }

    public String getFormCode()
    {
        return modificationContext.getFormCode();
    }

    public ModificationContext getModificationContext()
    {
        return modificationContext;
    }

    public void setCustomForm(@Nullable DtoContainer<CustomForm> customForm)
    {
        this.customForm = customForm;
        modificationContext.setFormCode(customForm != null ? customForm.getCode() : null);
        setMetainfo(new CustomFormFakeMetaClass(getFormCode()));
    }
}
