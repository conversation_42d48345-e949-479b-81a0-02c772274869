package ru.naumen.metainfoadmin.client.attributes;

import java.util.Objects;
import java.util.Set;

import jakarta.annotation.Nullable;

import com.google.gwt.event.shared.GwtEvent;

import ru.naumen.core.client.content.Context;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Событие изменения набора выключенных метками атрибутов.
 * <AUTHOR>
 * @since Feb 04, 2019
 */
public class DisabledAttributesChangedEvent extends GwtEvent<DisabledAttributesChangedHandler>
{
    public static final Type<DisabledAttributesChangedHandler> TYPE = new Type<>();

    private final ClassFqn classFqn;
    private final Set<String> disabledAttributes;

    public DisabledAttributesChangedEvent(ClassFqn classFqn, Set<String> disabledAttributes)
    {
        this.classFqn = classFqn;
        this.disabledAttributes = disabledAttributes;
    }

    @Override
    public Type<DisabledAttributesChangedHandler> getAssociatedType()
    {
        return TYPE;
    }

    @Override
    protected void dispatch(DisabledAttributesChangedHandler handler)
    {
        handler.onDisabledAttributesChanged(this);
    }

    public ClassFqn getClassFqn()
    {
        return classFqn;
    }

    public Set<String> getDisabledAttributes()
    {
        return disabledAttributes;
    }

    public boolean isSuitableForContext(@Nullable Context context)
    {
        return null != context && null != context.getMetainfo()
               && Objects.equals(context.getMetainfo().getFqn(), getClassFqn());
    }
}
