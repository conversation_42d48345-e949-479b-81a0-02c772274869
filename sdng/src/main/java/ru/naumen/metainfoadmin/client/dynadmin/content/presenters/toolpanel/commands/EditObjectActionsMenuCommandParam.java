package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.commands;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfo.client.HasContextCommandParam;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.ObjectActionsMenuHolder;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 *
 * <AUTHOR>
 * @since 01.03.17
 */
public class EditObjectActionsMenuCommandParam extends CommandParam<ActionTool, ObjectActionsMenuHolder>
        implements HasContextCommandParam
{
    private ActionTool actionTool;
    private final UIContext context;

    public EditObjectActionsMenuCommandParam(UIContext context, @Nullable ActionTool value,
            AsyncCallback<ObjectActionsMenuHolder> callback)
    {
        super(value, callback);
        this.context = context;
    }

    @Nullable
    public ActionTool getActionTool()
    {
        return actionTool;
    }

    @Override
    public UIContext getContext()
    {
        return context;
    }

    public void setActionTool(ActionTool actionTool)
    {
        this.actionTool = actionTool;
    }
}
