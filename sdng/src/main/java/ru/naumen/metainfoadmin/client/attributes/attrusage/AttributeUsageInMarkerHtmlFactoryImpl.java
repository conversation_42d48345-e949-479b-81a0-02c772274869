package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInMarker;

/**
 * Представление для отображения значения места использования "Маркер прав" на форме "Используется в настройках" в
 * таблице атрибутов
 * <AUTHOR>
 * @since 3 Jul 18
 */
public class AttributeUsageInMarkerHtmlFactoryImpl extends AttributeHtmlFactoryImpl<AttributeUsageInMarker>
{
    @Inject
    private Formatters formatters;
    @Inject
    private CommonMessages messages;
    @Inject
    private PlaceHistoryMapper historyMapper;

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInMarker usage)
    {
        return new SafeHtmlBuilder().append(formatters.formatHyperlinkAsHtml(createLinkToPermissionSettingsTab(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLinkToPermissionSettingsTab(AttributeUsageInMarker usage)
    {
        ClassFqn declaredMetaClass = usage.getTypes().isEmpty() ? usage.getClazz().getFqn() : usage.getTypes().get(0);

        MetaClassPlace mcPlace = new MetaClassPlace(declaredMetaClass, "Class.PermissionSettings");
        return new Hyperlink(
                usage.isView()
                        ? messages.permissionMarkerForViewAttr(usage.getTitle())
                        : messages.permissionMarkerForEditAttr(usage.getTitle()),
                StringUtilities.getHrefByToken(historyMapper.getToken(mcPlace)));
    }
}