package ru.naumen.metainfoadmin.client.customforms.form;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.ui.customform.ChangeCaseForm;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;
import ru.naumen.metainfo.shared.ui.customform.CustomFormType;
import ru.naumen.metainfo.shared.ui.customform.MassEditForm;
import ru.naumen.metainfo.shared.ui.customform.QuickForm;

/**
 * <AUTHOR>
 * @since 26.04.2016
 *
 */
public class EditCustomFormFormPresenter extends AbstractCustomFormFormPresenter
{
    @Inject
    public EditCustomFormFormPresenter(
            PropertyDialogDisplay display,
            EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void initPropertiesValues(final CustomForm customForm, final AsyncCallback<Void> callback)
    {
        isEditForm = true;

        CustomFormType formType = customForm.getFormType();
        formTypeProperty.trySetObjValue(formType.name());
        formTypeProperty.setDisable();

        if (customForm instanceof HasCode)
        {
            codeProperty.setValue(((HasCode)customForm).getCode());
        }
        codeProperty.setDisable();

        if (formType == CustomFormType.ChangeCaseForm)
        {
            transitionCasesProperty.setCaption(messages.forTransitionsInTypes());
        }
        else
        {
            transitionCasesProperty.setCaption(messages.forTypes());
        }

        onFormTypeChange(formType, true, true);

        setTransitionCases();

        List<MetaClassAttributeGroups> metaClassesGroups = new ArrayList<>();
        for (MetaClassLite metaClassLite : customForm.getTargetCases().getTargets())
        {
            if (attributeGroupsCache.containsKey(metaClassLite.getFqn()))
            {
                metaClassesGroups.add(attributeGroupsCache.get(metaClassLite.getFqn()));
            }
        }

        Collection<DtObject> groups = getAvailableAttributeGroups(metaClassesGroups);
        updateAttributeGroups(groups);
        if (StringUtilities.isEmpty(customForm.getAttrGroup()))
        {
            useStandardAttributesSet.setValue(true);
            validation.unvalidate(attributeGroup);
            attributeGroup.initValidation();
        }
        else
        {
            useStandardAttributesSet.setValue(false);
            attributeGroup.trySetObjValue(
                    new SimpleDtObject(customForm.getAttrGroup(), customForm.getAttrGroup()).getTitle());
        }

        if (customForm instanceof ChangeCaseForm)
        {
            commentOnFormProperty.setValue(((ChangeCaseForm)customForm).getCommentOnFormProperty());
            commentAttributeGroup.trySetObjValue(((ChangeCaseForm)customForm).getCommentOnFormAttrGroupCode());
            showAttributeDescription.setValue(((ChangeCaseForm)customForm).getShowAttrDescription());
        }

        if (customForm instanceof QuickForm)
        {
            titleProperty.setValue(metainfoUtils.getLocalizedValue(((QuickForm)customForm).getTitle()));
            showAttributeDescription.setValue(((QuickForm)customForm).getShowAttrDescription());
            immediateObjectSavingEnabled.setValue(((QuickForm)customForm).isImmediateObjectSavingEnabled());
        }

        if (customForm instanceof MassEditForm)
        {
            titleProperty.setValue(metainfoUtils.getLocalizedValue(((MassEditForm)customForm).getTitle()));
            commentOnFormProperty.setValue(((MassEditForm)customForm).getCommentOnFormProperty());
            commentAttributeGroup.trySetObjValue(((MassEditForm)customForm).getCommentOnFormAttrGroupCode());
            showAttributeDescription.setValue(((MassEditForm)customForm).getShowAttrDescription());
            useAsDefaultProperty.setValue(((MassEditForm)customForm).getUseAsDefault());
        }
        callback.onSuccess(null);
        eventBus.fireEvent(new UpdateTabOrderEvent(true));
    }

    @Override
    protected void bindSettingsSetProperty()
    {
        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getDisplay(), customForm.getSettingsSet());
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.editingForm());
    }

    @Override
    protected void setTransitionCases()
    {
        transitionCasesProperty.setValue(CollectionUtils.transform(customForm.getTargetCases().getTargets(),
                DtObject.CREATE_FROM_METACLASSLITE));
    }

    /**
     * На форме редактирования Тип формы уже выбран и его изменить нельзя.
     */
    @Override
    protected boolean checkSelectNextFormType()
    {
        return false;
    }
}
