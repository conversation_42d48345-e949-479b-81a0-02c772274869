package ru.naumen.metainfoadmin.client.scheduler;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 *
 * <AUTHOR>
 */
@DefaultLocale("ru")
public interface SchedulerTaskMessages extends Messages
{
    String addingSchedulerTask();

    String addingTrigger();

    String addTask();

    String bigDaily();

    String bigMonthly();

    String bigWeekly();

    String bigYearly();

    String calculationStrategy();

    String creationDate();

    String daily();

    String editingSchedulerTask();

    String editingTrigger();

    String executeAtConcreteDate(String date);

    String executeAtConcreteDateType();

    String executionDate();

    String fromLastExecution();

    String fromStart();

    String headTitle();

    String interval();

    String isOn();

    String lastExecutionDate();

    String monthly();

    String parameters();

    String period();

    String periodicExecution();

    String periodicRule();

    String planExecutionDate();

    String schedulerTask();

    String schedulerTaskCard();

    String schedulerTaskLog();

    String script();

    String startDate();

    String taskCard();

    String taskType();

    String theSchedulerTask();

    String toSchedulerTask();

    String schedulerTasksSelected();

    String trigger();

    String triggerCard();

    String triggers();

    String triggerType();

    String version();

    String weekly();

    String yearly();

    String confirmRunQuestion(String task);

    String confirmRunQuestion2(String task);

    String executeScriptTask();

    String receiveMailTask();

    String advImportSchedulerTask();

    String randomizeDelay();

    @Description("После запуска задачи планировщика письма загрузятся в систему... Вы действительно хотите выполнить "
                 + "задачу планировщика?")
    String confirmRunQuestionInboundMailServer();

    @Description("При обработке входящей почты письма загрузятся в систему и будут удалены с почтового "
                 + "сервера...")
    String confirmForInboundMailServer();

    @Description("Вы действительно хотите изменить параметры подключения?")
    String questionChangeInboundMailServer();

    @Description("Вы действительно хотите включить подключение?")
    String questionEnableInboundMailServer();

    @Description("Вы действительно хотите включить правило выполнения задачи планировщика?")
    String questionEnableRuleInboundMailServer();

    @Description("Вы действительно хотите изменить сервер входящей почты для задачи планировщика?")
    String questionChangeInboundMailServerInTask();
}
