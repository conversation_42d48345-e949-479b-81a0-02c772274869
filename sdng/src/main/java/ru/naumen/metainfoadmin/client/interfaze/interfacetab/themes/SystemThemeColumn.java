/**
 *
 */
package ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes;

import com.google.inject.Inject;

import ru.naumen.core.client.widgets.columns.FontIconCell;
import ru.naumen.core.client.widgets.columns.ToggleColumn;
import ru.naumen.core.shared.personalsettings.ThemeClient;

/**
 * Колонка с признаком, является ли тема системной (зашита в код), либо пользовательской
 *
 * <AUTHOR>
 * @since 18.07.16.
 *
 */
public class SystemThemeColumn extends ToggleColumn<ThemeClient>
{
    @Inject
    public SystemThemeColumn(FontIconCell<ThemeClient> cell)
    {
        super(cell);
    }

    @Override
    protected boolean isEnabled(ThemeClient value)
    {
        return value.isSystem();
    }
}