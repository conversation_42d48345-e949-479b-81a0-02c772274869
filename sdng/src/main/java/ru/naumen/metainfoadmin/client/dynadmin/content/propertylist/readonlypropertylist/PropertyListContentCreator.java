package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist;

import jakarta.inject.Inject;

import ru.naumen.core.client.content.toolbar.ToolBarUtils;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.ui.PropertyList;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.PropertyListBaseContentCreator;

/**
 * {@link Presenter} для отображения параметров добавляемого контента типа "Параметры объекта"
 *
 * <AUTHOR>
 * @since 11.08.2010
 *
 */
public class PropertyListContentCreator extends PropertyListBaseContentCreator<PropertyList>
{
    @Inject
    private ToolBarUtils toolBarUtils;

    @Override
    public void fillContent(PropertyList content)
    {
        super.fillContent(content);
        content.setToolPanel(toolBarUtils.createDefault(content));
    }
}
