package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb.BreadCrumbPlace;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInBreadCrumb;

/**
 * Представление для отображения значения места использования "Хлебные крошки" на форме "Используется в настройках" в
 * таблице атрибутов
 * <AUTHOR>
 * @since 3 Jul 18
 */
@Singleton
public class AttributeUsageInBreadCrumbHtmlFactoryImpl extends AttributeHtmlFactoryImpl<AttributeUsageInBreadCrumb>
{
    @Inject
    private Formatters formatters;
    @Inject
    private CommonMessages messages;
    @Inject
    private PlaceHistoryMapper historyMapper;

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInBreadCrumb usage)
    {
        return new SafeHtmlBuilder().append(formatters.formatHyperlinkAsHtml(createLinkToBreadCrumb(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLinkToBreadCrumb(AttributeUsageInBreadCrumb usage)
    {
        BreadCrumbPlace bcPlace = new BreadCrumbPlace(usage.getCode());
        //@formatter:off
        return new Hyperlink(
                    messages.breadCrumbElement(),
                    StringUtilities.getHrefByToken(historyMapper.getToken(bcPlace)));
        //@formatter:on
    }
}
