package ru.naumen.metainfoadmin.client.dynadmin.content;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.NoSuchElementException;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;

import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.HierarchyGridInfoPropertiesPresenter;

/**
 * Реестр представлений свойств отдельных контентов.
 * <AUTHOR>
 * @since Mar 27, 2021
 */
@Singleton
public class ContentInfoPropertiesPresenterRegistry
{
    private final Map<String, Provider<? extends ContentInfoPropertiesPresenter<?>>> registry = new LinkedHashMap<>();

    @Inject
    public ContentInfoPropertiesPresenterRegistry(
            Provider<HierarchyGridInfoPropertiesPresenter> hierarchyGridInfoPropertiesPresenterProvider)
    {
        registry.put(HierarchyGrid.class.getSimpleName(), hierarchyGridInfoPropertiesPresenterProvider);
    }

    public <C extends Content> ContentInfoPropertiesPresenter<C> getPropertiesPresenter(String contentType)
    {
        @SuppressWarnings("unchecked")
        Provider<ContentInfoPropertiesPresenter<C>> provider = (Provider<ContentInfoPropertiesPresenter<C>>)registry
                .get(contentType);
        if (null == provider)
        {
            throw new NoSuchElementException("Properties presenter for content '" + contentType
                                             + "' is not registered.");
        }
        return provider.get();
    }
}
