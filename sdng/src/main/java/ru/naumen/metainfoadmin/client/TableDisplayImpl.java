package ru.naumen.metainfoadmin.client;

import com.google.gwt.core.client.GWT;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.client.ui.Label;

import ru.naumen.core.client.components.table.TableWithToolPanelDisplayImpl;
import ru.naumen.core.client.content.scroll.ScrollableContentPanel;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.WidgetResources;

/**
 * Дисплей простого списка в интерфейсе администратора
 * <AUTHOR>
 *
 */
public class TableDisplayImpl<T> extends TableWithToolPanelDisplayImpl<T> implements TableDisplay<T>
{
    // TODO dzevako сделать нормальные ресурсы, а не CatalogCellTableResources
    protected final static CatalogCellTableResources cellTableResource = GWT.create(CatalogCellTableResources.class);

    protected Label infoMessage;

    public TableDisplayImpl()
    {
        this(cellTableResource);
    }

    public TableDisplayImpl(CellTable.Resources resources)
    {
        this(new DefaultCellTable<T>(resources));
    }

    public TableDisplayImpl(DataTable<T> dataTable)
    {
        super(dataTable);
    }

    @Override
    public Label getInfoMessage()
    {
        return infoMessage;
    }

    @Override
    public void initTable()
    {
        infoMessage = new Label();
        infoMessage.setVisible(false);
        infoMessage.addStyleName(WidgetResources.INSTANCE.all().tableDisplayInfo());
        infoMessage.ensureDebugId("infoMessage");
        addControlledWidget(infoMessage);

        super.initTable();
    }

    @Override
    public void setInfoMessage(String text)
    {
        infoMessage.setText(text);
        infoMessage.setVisible(Boolean.TRUE);
    }

    protected void wrapTableInScrollableContainer()
    {
        ScrollableContentPanel widgetContainer =
                new ScrollableContentPanel(ScrollableContentPanel.DEFAULT_SCALE_DIFFERENCE);
        tableContainer.removeFromParent();
        widgetContainer.add(tableContainer);
        widgetContainer.getScrollBarBottom().getElement().getStyle().setZIndex(10);
        widgetContainer.getScrollBarTop().getElement().getStyle().setZIndex(10);
        addControlledWidget(widgetContainer);
    }
}