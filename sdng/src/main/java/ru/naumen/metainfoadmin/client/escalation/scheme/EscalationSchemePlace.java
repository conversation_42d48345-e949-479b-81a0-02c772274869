package ru.naumen.metainfoadmin.client.escalation.scheme;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * <AUTHOR>
 * @since 20.08.2012
 *
 */
public class EscalationSchemePlace extends Place
{

    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<EscalationSchemePlace>
    {

        @Override
        public EscalationSchemePlace getPlace(String token)
        {
            return new EscalationSchemePlace(token);
        }

        @Override
        public String getToken(EscalationSchemePlace place)
        {
            return place == null ? "" : place.getCode();
        }
    }

    public static final String PLACE_PREFIX = "escalationScheme";

    private final String code;
    private final DtoContainer<EscalationScheme> scheme;

    @AssistedInject
    public EscalationSchemePlace(@Assisted DtoContainer<EscalationScheme> container)
    {
        this.code = container.get().getCode();
        this.scheme = container;
    }

    public EscalationSchemePlace(String code)
    {
        this.code = code;
        this.scheme = null;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null || getClass() != obj.getClass())
        {
            return false;
        }
        return ObjectUtils.equals(code, ((EscalationSchemePlace)obj).code);
    }

    public String getCode()
    {
        return code;
    }

    public DtoContainer<EscalationScheme> getScheme()
    {
        return scheme;
    }

    @Override
    public int hashCode()
    {
        return code == null ? 0 : code.hashCode();
    }

    @Override
    public String toString()
    {
        return "EscalationSchemePlace [" + code + "]";
    }
}
