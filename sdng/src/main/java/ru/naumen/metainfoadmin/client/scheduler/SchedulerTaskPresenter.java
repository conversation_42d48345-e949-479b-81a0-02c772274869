package ru.naumen.metainfoadmin.client.scheduler;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.SCHEDULER;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;

import jakarta.inject.Inject;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.activity.PrevLinkContainer;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.ResourceCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;

/**
 * {@link Presenter} карточки задачи планировщика
 * <AUTHOR>
 */
public class SchedulerTaskPresenter extends AdminTabPresenter<SchedulerTaskPlace>
{
    @Inject
    protected AdminMetainfoServiceAsync metainfoService;
    @Inject
    protected PlaceController placeController;
    @Inject
    protected SchedulerTaskMessages schedulerTaskMessages;
    @Inject
    protected CommonMessages commonMessages;
    @Inject
    protected SchedulerTaskTypeFactory typeFactory;
    @Inject
    protected TriggersPresenter triggersPresenter;
    @Inject
    private SchedulerPlace schedulerPlace;
    @Inject
    protected PrevLinkContainer prevLinkContainer;
    @Inject
    private Dialogs dialogs;
    @Inject
    protected I18nUtil i18nUtil;

    protected SchedulerTaskInfoPresenterBase<SchedulerTask> taskInfoPresenter;
    protected DtoContainer<SchedulerTask> task;
    @SuppressWarnings("rawtypes")
    protected OnStartCallback refreshCallback;

    @Inject
    public SchedulerTaskPresenter(AdminTabDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        getDisplay().setTabBarVisible(false);
    }

    @Override
    public void refreshDisplay()
    {
        if (null == task)
        {
            return;
        }
        getDisplay().setTitle(i18nUtil.getLocalizedTitle(task.get()));
        taskInfoPresenter.refreshDisplay();
        triggersPresenter.refreshDisplay();
    }

    @SuppressWarnings("rawtypes")
    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        refreshCallback = new SafeOnStartBasicCallback(getDisplay())
        {
            @Override
            protected void handleSuccess(Object value)
            {
                if (value instanceof SchedulerTask)
                {
                    task.get().setCode(((SchedulerTask)value).getCode());
                }
                refreshDisplay();
            }
        };
        if (null != getPlace().getSchedulerTask())
        {
            afterLoadTask(getPlace().getSchedulerTask());
        }
        else
        {
            metainfoService.getSchedulerTask(getPlace().getCode(),
                    new ResourceCallback<DtoContainer<SchedulerTask>>(commonMessages)
                    {
                        @Override
                        public void onSuccess(DtoContainer<SchedulerTask> value)
                        {
                            if (null == value)
                            {
                                dialogs.error(commonMessages.resourceNotFoundUserMessage());
                                return;
                            }
                            afterLoadTask(value);
                        }
                    });
        }
    }

    @Override
    protected void onUnbind()
    {
        if (taskInfoPresenter != null)
        {
            taskInfoPresenter.unbind();
        }
        if (triggersPresenter != null)
        {
            triggersPresenter.unbind();
        }
    }

    /**
     * Инициализация дочерних presenter после того, как с сервера придет информация об отображаемой задаче планировщика
     * @param task
     */
    private void afterLoadTask(DtoContainer<SchedulerTask> task)
    {
        this.task = task;
        taskInfoPresenter = typeFactory.getTaskInfoPresenter(task.get());
        taskInfoPresenter.init(this.task, refreshCallback);
        triggersPresenter.init(this.task, refreshCallback);

        addChildrenToDisplay();

        prevPageLinkPresenter.bind(commonMessages.back(), schedulerPlace);
        prevLinkContainer.pushPlace(schedulerTaskMessages.toSchedulerTask(), placeController.getWhere());
        refreshDisplay();
    }

    protected void addChildrenToDisplay()
    {
        addContent(taskInfoPresenter, "info");
        addContent(triggersPresenter, "triggers");
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return SCHEDULER;
    }
}
