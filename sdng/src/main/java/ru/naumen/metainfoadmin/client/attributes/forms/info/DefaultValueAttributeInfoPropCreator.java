package ru.naumen.metainfoadmin.client.attributes.forms.info;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.safehtml.shared.SafeHtml;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.SourceCode;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactories;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactory;
import ru.naumen.core.client.attr.presentation.factories.def.CellWidgetWithValueOrThrow;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.client.widgets.sourcecode.view.SourceCodeViewWidget;
import ru.naumen.core.shared.dispatch.FastSelectionChangesWithValue;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.HyperlinkAttributeType;
import ru.naumen.metainfo.shared.Constants.SourceCodeAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.columns.DefaultValueColumn;

/**
 * Создает {@link Property} для отображения информации о значении по умолчанию 
 * на модальной форме свойств атрибута
 *
 * <AUTHOR>
 * @since 31 июл. 2018 г.
 */
public class DefaultValueAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    private final static int LINK_CLASSES_LIMIT = 3;

    private PresentationContext presentationContext = new PresentationContext();

    private PropertyParametersDescriptor descriptor;

    @Inject
    private DefaultValueColumn defaultValueColumn;

    @Inject
    private AttributeHtmlFactories attributeHtmlFactories;

    @Override
    public void init(Map<Integer, Property<?>> createdProperties, IProperties propertyValues, Attribute attribute,
            ReadyState rs)
    {
        super.init(createdProperties, propertyValues, attribute, rs);
        presentationContext.setAttribute(attribute);
    }

    @Override
    protected void createInt(String code)
    {
        if (attribute.getHasDefaultValue())
        {
            descriptor = formPropParameter.create(code);
            createProperty(code);
        }
    }

    @SuppressWarnings("unchecked")
    private void createDefaultProperty()
    {
        CellWidgetWithValueOrThrow<Object> widget = (CellWidgetWithValueOrThrow<Object>)defaultValueColumn
                .createWidget(presentationContext);
        PropertyBase<Object, CellWidgetWithValueOrThrow<Object>> property = new PropertyBase<>(widget);
        property.setCaption(descriptor.getCaption());
        property.ensureDebugId(descriptor.getDebugId());
        createdProperties.put(descriptor.getDisplayPos(), property);
    }

    @SuppressWarnings("unchecked")
    private void createHyperLinkProperty()
    {
        CellWidgetWithValueOrThrow<Hyperlink> widget = (CellWidgetWithValueOrThrow<Hyperlink>)defaultValueColumn
                .createWidget(presentationContext);
        PropertyBase<Hyperlink, CellWidgetWithValueOrThrow<Hyperlink>> property = new PropertyBase<>(widget);
        property.setCaption(descriptor.getCaption());
        property.ensureDebugId(descriptor.getDebugId());
        if (!StringUtilities.isEmptyTrim(widget.getValue().getText()))
        {
            createdProperties.put(descriptor.getDisplayPos(), property);
        }
    }

    private void createProperty(String code)
    {
        String attrCode = attribute.getType().getCode();
        switch (attrCode)
        {
            case BOLinksAttributeType.CODE:
                createPropertyForBOLinks(code, attribute);
                break;
            case CatalogItemsAttributeType.CODE:
            case ru.naumen.metainfo.shared.Constants.CaseListAttributeType.CODE:
                createPropertyForLists(code);
                break;
            case SourceCodeAttributeType.CODE:
                createSourceCodeProperty();
                break;
            case HyperlinkAttributeType.CODE:
                createHyperLinkProperty();
                break;
            default:
                createDefaultProperty();
                break;
        }
    }

    private void createPropertyForBOLinks(String code, Attribute attribute)
    {
        if (attribute.getHasDefaultValue())
        {
            Object defaultValue = attribute.getDefaultValue();
            Collection<?> defaultValueCollection = Collections.emptyList();
            if (defaultValue instanceof FastSelectionChangesWithValue)
            {
                defaultValueCollection = ((FastSelectionChangesWithValue)defaultValue).getTreeValue();
            }
            else if (defaultValue instanceof Collection)
            {
                defaultValueCollection = attribute.getDefaultValue();
            }
            createPropertyForList(code, defaultValueCollection);
        }
    }

    private void createPropertyForList(String code, Collection<?> defaultValueList)
    {
        Object values = defaultValueList.stream().limit(LINK_CLASSES_LIMIT).collect(Collectors
                .toList());
        AttributeHtmlFactory<Object> factory = attributeHtmlFactories.getFactory(attribute.getViewPresentation()
                .getCode());
        SafeHtml html = factory.create(presentationContext, values);
        createProperty(code, defaultValueList.size() > LINK_CLASSES_LIMIT ? html.asString() + " "
                                                                            + cmessages.etc() : html.asString());
    }

    private void createPropertyForLists(String code)
    {
        List<?> defaultValueList = attribute.getDefaultValue();
        createPropertyForList(code, defaultValueList);
    }

    private void createSourceCodeProperty()
    {
        SourceCodeViewWidget widget = (SourceCodeViewWidget)defaultValueColumn.createWidget(presentationContext);
        PropertyBase<SourceCode, SourceCodeViewWidget> property = new PropertyBase<>(widget);
        property.setCaption(descriptor.getCaption());
        property.ensureDebugId(descriptor.getDebugId());
        if (!StringUtilities.isEmptyTrim(property.getValue().getText()))
        {
            createdProperties.put(descriptor.getDisplayPos(), property);
        }
    }
}
