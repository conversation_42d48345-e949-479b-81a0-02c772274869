package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject;

import java.util.ArrayList;
import java.util.Collections;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Обработчик изменения связанного объекта
 *
 * <AUTHOR>
 *
 * @param <F> тип формы
 */
public class AttrChainVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        RelationsAttrTreeObject attrTree = context.getPropertyValues()
                .getProperty(AttributeFormPropertyCode.ATTR_CHAIN);
        if (attrTree != null)
        {
            ArrayList<AttributeFqn> attrList = new ArrayList<>();
            while (attrTree != null)
            {
                Attribute attribute = attrTree.getAttribute();
                attrList.add(new AttributeFqn(attribute.getMetaClassLite().getFqn(), attribute.getCode()));
                attrTree = attrTree.getParent();
            }
            Collections.reverse(attrList);

            context.getPropertyValues().setProperty(AttributeFormPropertyCode.ATTR_CHAIN_AS_LIST, attrList);
        }

        context.getPropertyControllers().get(AttributeFormPropertyCode.RELATED_OBJECT_METACLASS).refresh();
        context.getPropertyControllers().get(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE).refresh();
        context.getPropertyControllers().get(AttributeFormPropertyCode.RELATED_ATTRS_TO_EXPORT).refresh();
        context.getPropertyControllers().get(AttributeFormPropertyCode.SHOW_PRS).refresh();
    }
}
