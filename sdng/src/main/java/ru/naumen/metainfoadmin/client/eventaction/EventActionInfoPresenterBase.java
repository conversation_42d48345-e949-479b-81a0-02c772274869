package ru.naumen.metainfoadmin.client.eventaction;

import static java.lang.String.CASE_INSENSITIVE_ORDER;
import static java.util.Comparator.nullsFirst;
import static ru.naumen.core.client.widgets.properties.PropertyUtils.addDescriptionIconWithHint;
import static ru.naumen.core.client.widgets.properties.PropertyUtils.editDescriptionIconHint;
import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.JMSQueue.TITLE;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.ATTRIBUTES;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.AttrTree.CLASS_TITLE;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.AttrTree.TYPE_TITLE;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.CAN_BE_ENABLED;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.CONTEXT_ATTRIBUTES;
import static ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormUtils.isEventActionWithAttributes;
import static ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormUtils.isEventActionWithContextAttributes;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Collections2;
import com.google.common.collect.Sets;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.HasEnabled;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.eventaction.EventActionConstants;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.client.events.EventActionUpdatedEvent;
import ru.naumen.metainfo.client.events.EventActionUpdatedHandler;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.Constants;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.escalation.EscalationGinModule.EscalationPlaceTabs;
import ru.naumen.metainfoadmin.client.escalation.EscalationPlace;
import ru.naumen.metainfoadmin.client.escalation.actions.EscalationActionsGinModule;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormUtils;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.client.tags.formatters.TagPropertyFormatter;
import ru.naumen.metainfoadmin.shared.Constants.EventActionCommandCode;

/**
 * Базовый презентер для карточки действия по событию
 *
 * <AUTHOR>
 * @since 06.12.2011
 */
public class EventActionInfoPresenterBase<T extends Action> extends BasicPresenter<InfoDisplay> implements
        EventActionUpdatedHandler
{
    private static final Comparator<DtObject> ATTR_DTO_COMPARATOR = Comparator
            .comparing((DtObject attr) -> (String)attr.get(CLASS_TITLE), String.CASE_INSENSITIVE_ORDER)
            .thenComparing(attr -> (String)attr.get(TYPE_TITLE), nullsFirst(String.CASE_INSENSITIVE_ORDER))
            .thenComparing(DtObject::getTitle, CASE_INSENSITIVE_ORDER);

    public final class EventActionParam extends CommandParam<EventActionWithScript, EventActionWithScript>
    {
        public EventActionParam(AsyncCallback<EventActionWithScript> callback)
        {
            super(new FactoryParam.ValueSource<EventActionWithScript>()
            {
                @Override
                public EventActionWithScript getValue()
                {
                    return eventAction;
                }
            }, callback);
        }
    }

    @Inject
    protected EventActionMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    protected TagsMessages tagsMessages;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> code;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> description;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> tags;
    private Property<String> settingsSet;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> event;
    private HasProperties.PropertyRegistration<String> eventPR;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> attributes;
    private HasProperties.PropertyRegistration<String> attributesPR;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> action;
    private HasProperties.PropertyRegistration<String> actionPR;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> contextAttributes;
    private HasProperties.PropertyRegistration<String> contextAttributesPR;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> jmsQueue;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> usagePlaces;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> on;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> fqn;
    @Inject
    protected Formatters formatters;
    @Inject
    private PlaceController placeController;
    @Inject
    protected AdminMetainfoServiceAsync metainfoService;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private EventActionConstants eventActionConstants;
    @Inject
    private EventActionsPlace eventActionsPlace;
    @Inject
    private I18nUtil i18nUtil;
    @Inject
    private EventActionUsageClientHtmlFactory usageHtmlFactory;
    @Inject
    private TagPropertyFormatter tagPropertyFormatter;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;
    @Inject
    private EventActionFormUtils eventActionFormUtils;

    @Inject
    protected SecurityHelper security;

    @Inject
    protected PossibleSkipIfUserHasActiveSessionPredicate skipIfUserHasActiveSessionPredicate;

    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    protected Property<Boolean> skipIfUserHasActiveSession;

    protected EventActionWithScript eventAction;
    private final ToolBarDisplayMediator<EventActionWithScript> toolBar;
    protected ButtonPresenter<EventActionWithScript> toggleButtonPresenter;

    @SuppressWarnings("rawtypes")
    private OnStartCallback refreshCallback;

    @Inject
    public EventActionInfoPresenterBase(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<EventActionWithScript>(getDisplay().getToolBar());
    }

    @SuppressWarnings("rawtypes")
    public void init(EventActionWithScript eventAction, OnStartCallback refreshCallback)
    {
        this.eventAction = eventAction;
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void onEventActionUpdated(EventActionUpdatedEvent e)
    {
        if (eventAction == null)
        {
            eventAction = new EventActionWithScript(e.getEventAction());
        }
        else
        {
            eventAction.setDtObject(e.getEventAction());
        }
        if (e.getScriptDto() != null)
        {
            eventAction.putScript(e.getScriptDto());
        }

        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        setPropertiesValues();
        toolBar.refresh(eventAction);
        boolean isEnabled = Boolean.TRUE.equals(eventAction.getDtObject().getProperty(Constants.EventAction.ON));
        boolean canBeEnabled = Boolean.TRUE.equals(eventAction.getDtObject().getProperty(CAN_BE_ENABLED));
        updateAttention();
        if (toggleButtonPresenter.getDisplay() instanceof HasEnabled)
        {
            ((HasEnabled)toggleButtonPresenter.getDisplay()).setEnabled(canBeEnabled);
        }
        toggleButtonPresenter.setTitle(isEnabled ? messages.toggleOff() : messages.toggleOn());
        refreshEventAttrsProperty();
    }

    private void refreshEventAttrsProperty()
    {
        EventAction object = eventAction.getObject();
        EventAction.TxType txType = object.getTxType();
        EventType eventType = object.getEvent().getEventType();

        String eventAttrCaption = eventActionFormUtils.getEvenAttrsCaption(eventType);
        String eventAttrsDescription = eventActionFormUtils.getEventAttrsDescription(eventType);
        attributes.setCaption(eventAttrCaption);
        editDescriptionIconHint(eventAttrsDescription, attributes);

        boolean withAttributes = isEventActionWithAttributes(eventType);
        if (withAttributes && attributesPR == null)
        {
            attributesPR = getDisplay().addPropertyAfter(attributes, eventPR);
        }
        if (!withAttributes && attributesPR != null)
        {
            attributesPR.unregister();
            attributesPR = null;
        }

        boolean withContextAttributes = isEventActionWithContextAttributes(eventType,
                txType == EventAction.TxType.CURRENT_TX);
        if (withContextAttributes && contextAttributesPR == null)
        {
            contextAttributesPR = getDisplay().addPropertyAfter(contextAttributes, actionPR);
        }
        if (!withContextAttributes && contextAttributesPR != null)
        {
            contextAttributesPR.unregister();
            contextAttributesPR = null;
        }
    }

    protected void addActionProperties()
    {
    }

    protected void addProperties()
    {
        EventType eventType = eventAction.getObject().getEvent().getEventType();

        addProperty(cmessages.title(), title);
        addProperty(cmessages.code(), code);
        addProperty(cmessages.description(), description);

        if (eventType != EventType.arriveMessageOnQueue)
        {
            addProperty(cmessages.objects(), fqn);
        }

        if (security.isAdmin())
        {
            addProperty(tagsMessages.tags(), tags);
        }
        settingsSet = settingsSetOnFormCreator.createFieldOnCard(getDisplay());

        eventPR = addProperty(messages.event(), event);

        attributes.setVisible(isEventActionWithAttributes(eventType));
        attributes.setCaption(eventActionFormUtils.getEvenAttrsCaption(eventType));
        addDescriptionIconWithHint(eventActionFormUtils.getEventAttrsDescription(eventType),
                attributes, false);
        if (isEventActionWithAttributes(eventType))
        {
            attributesPR = getDisplay().addPropertyAfter(attributes, eventPR);
        }

        if (eventType == EventType.userEvent && security.isAdmin())
        {
            addProperty(messages.usagePlaces(), usagePlaces);
        }

        actionPR = addProperty(messages.action(), action);

        contextAttributes.setCaption(messages.attributesTransferredToContext());
        addDescriptionIconWithHint(messages.attributesTransferredToContextDescription(),
                contextAttributes, false);
        if (isEventActionWithContextAttributes(eventType,
                eventAction.getObject().getTxType() == EventAction.TxType.CURRENT_TX))
        {
            contextAttributesPR = getDisplay().addPropertyAfter(contextAttributes, actionPR);
        }

        if (eventAction.getObject().getJmsQueue() != null)
        {
            addProperty(messages.jmsQueue(), jmsQueue);
        }

        addProperty(cmessages.on(), on);
        if (skipIfUserHasActiveSessionPredicate.test(eventAction.getObject().getEvent().getEventType().name()))
        {
            addProperty(messages.skipIfUserHasActiveSession(), skipIfUserHasActiveSession);
        }

        addActionProperties();
    }

    @SuppressWarnings("unchecked")
    protected ButtonPresenter<EventActionWithScript> addTool(String btn, String title, String cmd,
            CommandParam<EventActionWithScript, EventActionWithScript> param)
    {
        ButtonPresenter<EventActionWithScript> buttonPresenter = (ButtonPresenter<EventActionWithScript>)buttonFactory
                .create(btn, title, cmd, param);
        toolBar.add(buttonPresenter);
        return buttonPresenter;
    }

    @SuppressWarnings("unchecked")
    protected T getAction()
    {
        return (T)eventAction.getObject().getAction();
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected void initToolBar()
    {
        OnStartCallback delCallback = new OnStartBasicCallback<Void>(getDisplay())
        {
            @Override
            protected void handleSuccess(Void value)
            {
                if (EventType.escalation.equals(eventAction.getObject().getEvent().getEventType()))
                {
                    placeController.goTo(new EscalationPlace(EscalationPlaceTabs.ACTIONS));
                }
                else
                {
                    placeController.goTo(eventActionsPlace);
                }
            }
        };

        toggleButtonPresenter = addTool(ButtonCode.SWITCH, eventAction.getObject().isOn() ? messages.toggleOff()
                        : messages.toggleOn(), EventActionCommandCode.TOGGLE_EVENT_ACTION,
                new EventActionParam(refreshCallback));
        toggleButtonPresenter.addPossibleFilter(eventActionWithScript ->
                AdminPermissionUtils.hasEditPermission(eventActionWithScript.getDtObject()));

        if (security.isAdmin())
        {
            ButtonPresenter<EventActionWithScript> editButton = addTool(ButtonCode.EDIT, cmessages.edit(),
                    selectEditCommand(), new EventActionParam(refreshCallback));
            editButton.addPossibleFilter(eventActionWithScript ->
                    AdminPermissionUtils.hasEditPermission(eventActionWithScript.getDtObject()));

            ButtonPresenter<EventActionWithScript> delButton = addTool(ButtonCode.DELETE, cmessages.delete(),
                    EventActionCommandCode.DELETE_EVENT_ACTION, new EventActionParam(delCallback));
            delButton.addPossibleFilter(eventActionWithScript ->
                    AdminPermissionUtils.hasEditPermission(eventActionWithScript.getDtObject()));
        }

        toolBar.bind();
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(EventActionUpdatedEvent.getType(), this));
        getDisplay().setTitle(cmessages.properties());
        initToolBar();
        addProperties();
        refreshDisplay();
        ensureDebugIds();
    }

    /**
     * Добавить свойство с названием
     */
    protected <T> PropertyRegistration<T> addProperty(String caption, Property<T> property)
    {
        property.setCaption(caption);
        return getDisplay().add(property);
    }

    @Override
    protected void onUnbind()
    {
    }

    protected void setPropertiesValues()
    {
        final EventAction action = eventAction.getObject();
        EventType eventType = action.getEvent().getEventType();

        title.setValue(i18nUtil.getLocalizedTitle(eventAction));
        code.setValue(action.getId());
        description.setValue(formatters.normalize(
                        SafeHtmlUtils.fromTrustedString(formatters.formatText(i18nUtil.getLocalizedDescription(eventAction))))
                .asString());
        DtObject eventActionDTO = eventAction.getDtObject();
        tags.setValue(tagPropertyFormatter.formatToAnchors(eventActionDTO.getProperty(Tag.ELEMENT_TAGS)).asString());
        settingsSetOnFormCreator.setValueOnCardProperty(eventAction.getObject().getSettingsSet(), settingsSet);

        event.setValue(eventActionConstants.eventTypes().get(eventType.name()));
        if (eventType == EventType.userEvent)
        {
            setUsagePlacesValue();
        }
        this.action.setValue(eventActionConstants.actionTypes().get(
                action.getAction().getActionType().name()));
        if (action.getJmsQueue() != null)
        {
            jmsQueue.setValue(eventActionDTO.getProperty(TITLE));
        }
        on.setValue(Boolean.TRUE.equals(eventActionDTO.getProperty(Constants.EventAction.ON)));

        final Set<ClassFqn> fqns = Sets.newHashSet(action.getLinkedClasses());
        fqns.addAll(Collections2.transform(action.getLinkedClasses(), ClassFqn.CLASS_EXTRACTOR));
        metainfoService.getMetaClasses(fqns, new BasicCallback<List<MetaClassLite>>(getDisplay())
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> classes)
            {
                String value = CommonUtils.getGroupedMetaClassTitles(action.getLinkedClasses(),
                        classes);
                fqn.setValue(value);
            }
        });

        if (isEventActionWithAttributes(eventType))
        {
            setAttributesProperty(attributes, (Collection<DtObject>)eventActionDTO.get(ATTRIBUTES));
        }

        if (isEventActionWithContextAttributes(eventType, action.getTxType() == EventAction.TxType.CURRENT_TX))
        {
            setAttributesProperty(contextAttributes, (Collection<DtObject>)eventActionDTO.get(CONTEXT_ATTRIBUTES));
        }
        skipIfUserHasActiveSession.setValue(eventAction.getObject().getSkipIfUserHasActiveSession());
    }

    private void setAttributesProperty(Property<String> property, Collection<DtObject> attributes)
    {
        String str = attributes.stream()
                .sorted(ATTR_DTO_COMPARATOR)
                .map(eventActionFormUtils::getAttrTitleWithClass)
                .collect(Collectors.joining("<br>"));
        property.setValue(str);
    }

    void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(description, "description");
        DebugIdBuilder.ensureDebugId(event, "event");
        DebugIdBuilder.ensureDebugId(attributes, "attributes");
        DebugIdBuilder.ensureDebugId(action, "action");
        DebugIdBuilder.ensureDebugId(contextAttributes, "contextAttributes");
        DebugIdBuilder.ensureDebugId(on, "on");
        DebugIdBuilder.ensureDebugId(fqn, "fqn");
        DebugIdBuilder.ensureDebugId(usagePlaces, "usagePlaces");
        DebugIdBuilder.ensureDebugId(tags, "tags");
        DebugIdBuilder.ensureDebugId(skipIfUserHasActiveSession, "skipIfUserHasActiveSession");
    }

    private String selectEditCommand()
    {
        if (null != eventAction && EventType.escalation.equals(eventAction.getObject().getEvent().getEventType()))
        {
            return EscalationActionsGinModule.EDIT_ESCALATION_EVENT_ACTION;
        }
        return EventActionCommandCode.EDIT_EVENT_ACTION;
    }

    private void setUsagePlacesValue()
    {
        usagePlaces.setValue(usageHtmlFactory.create(eventAction.getObject().getUsagePlaces()));
    }

    private void updateAttention()
    {
        List<String> warnings = eventAction.getDtObject().getProperty(Constants.EventAction.EVENT_ACTION_WARNINGS);

        if (CollectionUtils.isEmpty(warnings))
        {
            getDisplay().getAttention().setVisible(false);
            getDisplay().getAttention().setTitle(StringUtilities.EMPTY);
            getDisplay().getAttention().setText(StringUtilities.EMPTY);
        }
        else
        {
            getDisplay().getAttention().setVisible(true);
            getDisplay().getAttention().setTitle(cmessages.attention());
            getDisplay().getAttention().setText(String.join("\n", warnings));
        }
    }
}
