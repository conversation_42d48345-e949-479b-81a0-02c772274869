package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import java.util.ArrayList;
import java.util.Collections;

import ru.naumen.core.client.layout.Band;
import ru.naumen.core.client.layout.Column;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.Position;
import ru.naumen.metainfoadmin.client.common.content.FlowPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Класс, который реализует логику перемещения презентера контента вверх или вниз на карточке метакласса
 * <AUTHOR>
 * @since 26 янв. 2016 г.
 *
 */
public class LayoutContentPresenterMover
{

    private final ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands;

    public LayoutContentPresenterMover(ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands)
    {
        this.bands = bands;
    }

    /**
     * Перемещает элемент вниз. Логика перемещения -
     * http://projects.naumen.ru:8090/ServiceDesk/Releases/4.0/Requirements/Req00169
     */
    public void moveDown(FlowPresenter<FlowContent, UIContext> presenter)
    {
        Column<FlowPresenter<FlowContent, UIContext>> column = getColumn(presenter);
        int indexInColumn = column.getPresenters().indexOf(presenter);
        if (indexInColumn != column.getPresenters().size() - 1)
        {
            Collections.swap(column.getPresenters(), indexInColumn, indexInColumn + 1);
        }
        else
        {
            moveToOtherBand(presenter, false);
        }
    }

    /**
     * Перемещает элемент из одной колонки в другую.
     *
     * @param colIndex
     *            - индекс колонки, в которую надо переместить
     */
    public void moveToColumn(FlowPresenter<FlowContent, UIContext> presenter, int colIndex)
    {
        Column<FlowPresenter<FlowContent, UIContext>> currentCol = getColumn(presenter);
        Band<FlowPresenter<FlowContent, UIContext>> band = getBand(presenter);

        // Если контент из расширенной позиции смещаем влево, то колонка остается та же.
        if (Position.FULL.equals(presenter.getContent().getPosition()) && colIndex == 0)
        {
            return;
        }

        //удаляем из текущей колонки
        currentCol.remove(presenter);

        //добавляем в конец заданной колонки
        band.getColumn(Math.min(band.getColCount() - 1, colIndex)).add(presenter);
    }

    /**
     * Перемещает элемент вверх. Логика перемещения -
     * http://projects.naumen.ru:8090/ServiceDesk/Releases/4.0/Requirements/Req00169
     */
    public void moveUp(FlowPresenter<FlowContent, UIContext> presenter)
    {
        Column<FlowPresenter<FlowContent, UIContext>> column = getColumn(presenter);
        ArrayList<FlowPresenter<FlowContent, UIContext>> columnPresentersList = column.getPresenters();

        int indexInColumn = columnPresentersList.indexOf(presenter);

        if (indexInColumn != 0)
        {
            Collections.swap(column.getPresenters(), indexInColumn, indexInColumn - 1);
        }
        else
        {
            moveToOtherBand(presenter, true);
        }
    }

    /**
     * Возвращает ленту, в которой находится данный презентер
     */
    private Band<FlowPresenter<FlowContent, UIContext>> getBand(FlowPresenter<FlowContent, UIContext> presenter)
    {
        for (Band<FlowPresenter<FlowContent, UIContext>> band : bands)
        {
            for (Column<FlowPresenter<FlowContent, UIContext>> column : band.getColumns())
            {
                if (column.getPresenters().contains(presenter))
                {
                    return band;
                }
            }
        }
        return null;
    }

    /**
     * Возвращает колонку, в которой находится данный презентер.
     */
    private Column<FlowPresenter<FlowContent, UIContext>> getColumn(FlowPresenter<FlowContent, UIContext> presenter)
    {
        for (Band<FlowPresenter<FlowContent, UIContext>> band : bands)
        {
            for (Column<FlowPresenter<FlowContent, UIContext>> column : band.getColumns())
            {
                if (column.getPresenters().contains(presenter))
                {
                    return column;
                }
            }
        }
        return null;
    }

    /**
     * Перемещает контент в другую ленту
     *
     * @param up
     *            задает направление перемещения, true - вверх, false - вниз
     */
    private void moveToOtherBand(FlowPresenter<FlowContent, UIContext> presenter, boolean up)
    {
        Position position = presenter.getContent().getPosition();
        int colCount = position == Position.FULL ? 1 : 2;
        int columnIndex = (position == Position.RIGHT) ? 1 : 0;

        Band<FlowPresenter<FlowContent, UIContext>> band = getBand(presenter);
        int bandIndex = bands.indexOf(band);

        int nextBandIndex = bandIndex + (up ? -1 : 1);

        //Если дальше идет лента с тем же кол-вом колонок - перемещаем контент в неё
        if (band.getColCount() == bands.get(nextBandIndex).getColCount())
        {
            if (up)
            {
                //Поднимаем вверх и вставляем предпоследним - перепрыгиваем через последний контент в этой ленте
                if (bands.get(nextBandIndex).getColumn(columnIndex).size() == 0) // NOPMD
                {
                    bands.get(nextBandIndex).getColumn(columnIndex).getPresenters().add(0, presenter);
                }
                else
                {
                    bands.get(nextBandIndex).getColumn(columnIndex).getPresenters()
                            .add(bands.get(nextBandIndex).getColumn(columnIndex).size() - 1, presenter);
                }
            }
            else
            {
                //Опускаем вниз и вставляем вторым - перепрыгиваем через первый контент в этой ленте
                bands.get(nextBandIndex).getColumn(columnIndex).getPresenters().add(1, presenter);
            }
            //Либо удаляем исходную ленту (если в ней не осталось презентеров), либо удаляем только презентер из ленты
            if (band.countPresenters() == 1)
            {
                bands.remove(band);
            }
            else
            {
                band.getColumn(columnIndex).remove(presenter);
            }
        }
        else
        {
            //Дальше идет лента с другим кол-вом колонок
            int indexToSwap1 = bandIndex;
            if (band.countPresenters() > 1)
            {
                //Если в текущей еще есть презентеры, то надо выделить перемещаемый контент в новую ленту
                Band<FlowPresenter<FlowContent, UIContext>> newBand = new Band<>(
                        band.getColCount());
                newBand.getColumn(columnIndex).add(presenter);
                band.getColumn(columnIndex).remove(presenter);
                if (up)
                {
                    //Если перемещаем вверх, то вставляем новую ленту перед текущей, при этом индексы лент, которые
                    // будем менять местами, не меняются
                    bands.add(bandIndex, newBand);
                }
                else
                {
                    //Если же вниз - то вставляем новую ленту после текущей, и индексы лент, которые будем менять
                    // местами, все увеличиваются на 1
                    bands.add(bandIndex + 1, newBand);
                    indexToSwap1++;
                    nextBandIndex++;
                }
            }
            int indexToSwap2 = nextBandIndex;
            //Если в текущей ленте 2 колонки, а в следующей - одна, и там несколько презентеров, то мы должны
            // перепрыгнуть только через последний (или первый)
            //Для этого выделяем его в отдельную ленту
            boolean canJumpOverNextPresenter1 = colCount == 2 && bands.get(nextBandIndex).getColCount() == 1
                                                && bands.get(nextBandIndex).countPresenters() > 1;
            //Аналогично, если в текущей ленте 1 колонка, в следующей - две, там несколько презентеров, но все они
            // либо слева, либо справа
            //Тогда мы можем точно вставить перемещаемый контент (по всей ширине) между двумя контентами (которые оба
            // слева или справа)
            boolean canJumpOverNextPresenter2 = colCount == 1
                                                && bands.get(nextBandIndex).getColCount() == 2
                                                && bands.get(nextBandIndex).countPresenters() > 1
                                                && (bands.get(nextBandIndex).getColumn(0).getPresenters().isEmpty()
                                                    || bands.get(nextBandIndex)
                                                            .getColumn(1).getPresenters().isEmpty());

            if (canJumpOverNextPresenter1 || canJumpOverNextPresenter2)
            {
                Band<FlowPresenter<FlowContent, UIContext>> newBand = new Band<>(
                        bands.get(nextBandIndex).getColCount());
                int columnWithPresenters = 0;
                if (bands.get(nextBandIndex).getColumn(0).getPresenters().isEmpty())
                {
                    columnWithPresenters = 1;
                }
                FlowPresenter<FlowContent, UIContext> lastPresenter = bands.get(nextBandIndex)
                        .getColumn(columnWithPresenters).getFirst();
                if (up)
                {
                    lastPresenter = bands.get(nextBandIndex).getColumn(columnWithPresenters).getLast();
                }
                newBand.getColumn(columnWithPresenters).add(lastPresenter);
                bands.get(nextBandIndex).getColumn(columnWithPresenters).remove(lastPresenter);
                if (up)
                {
                    bands.add(nextBandIndex + 1, newBand);
                    indexToSwap1++;
                    indexToSwap2++;
                }
                else
                {
                    bands.add(nextBandIndex, newBand);
                }
            }
            Collections.swap(bands, indexToSwap1, indexToSwap2);
            tryMergeBands(Math.max(indexToSwap1, indexToSwap2), false);
            tryMergeBands(Math.min(indexToSwap1, indexToSwap2), true);
        }
    }

    /**
     * Пытается слить вместе две ленты - ленту с индексом index и лентой, непосредственно выше или ниже её 
     */
    private void tryMergeBands(int index, boolean toUp)
    {
        int index1 = toUp ? index - 1 : index;
        int index2 = toUp ? index : index + 1;
        if (index1 < 0 || index2 < 0 || index1 >= bands.size() || index2 >= bands.size())
        {
            return;
        }
        if (bands.get(index1).getColCount() != bands.get(index2).getColCount())
        {
            return;
        }
        for (int colNum = 0; colNum < bands.get(index2).getColCount(); colNum++)
        {
            for (FlowPresenter<FlowContent, UIContext> presenter : bands.get(index2).getColumn(colNum).getPresenters())
            {
                bands.get(index1).getColumn(colNum).add(presenter);
            }
        }
        bands.remove(index2);
    }
}