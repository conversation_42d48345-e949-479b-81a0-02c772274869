package ru.naumen.metainfoadmin.client.attributes.forms.add;

import static ru.naumen.metainfo.shared.Constants.NOT_EDITABLE_ATTRIBUTE_TYPES;
import static ru.naumen.metainfo.shared.Constants.NOT_REQUIRED_ATTRIBUTE_TYPES;
import static ru.naumen.metainfo.shared.Constants.NO_DEFAULT_VALUE_ATTRIBUTE_TYPES;
import static ru.naumen.metainfo.shared.Constants.UNIQUE_ATTRIBUTE_TYPES;

import com.google.common.collect.Lists;

import java.util.HashSet;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormContextPropertiesSetterImpl;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Вычисление различных вспомогательных значений для формы добавления атрибута
 * <AUTHOR>
 * @since 31.05.2012
 */
public class AddAttributeFormContextPropertiesSetterImpl<F extends ObjectFormAdd>
        extends AttributeFormContextPropertiesSetterImpl<F>
{
    @Override
    public void setContextProperties(PropertyContainerContext context)
    {
        super.setContextProperties(context);
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        context.setEnabled(AttributeFormPropertyCode.REQUIRED, !NOT_REQUIRED_ATTRIBUTE_TYPES.contains(attrType));
        context.setEnabled(AttributeFormPropertyCode.REQUIRED_IN_INTERFACE,
                !NOT_REQUIRED_ATTRIBUTE_TYPES.contains(attrType));
        context.setEnabled(AttributeFormPropertyCode.UNIQUE, UNIQUE_ATTRIBUTE_TYPES.contains(attrType));
        context.setEnabled(AttributeFormPropertyCode.EDITABLE, !NOT_EDITABLE_ATTRIBUTE_TYPES.contains(attrType));
        context.setEnabled(AttributeFormPropertyCode.DEFAULT_VALUE, true);

        boolean hasDefaultValue = !NO_DEFAULT_VALUE_ATTRIBUTE_TYPES.contains(attrType);
        context.getContextValues().setProperty(AttributeFormContextValues.HAS_DEFAULT_VALUE, hasDefaultValue);

        Boolean editable = context.getContextValues().getProperty(AttributeFormPropertyCode.EDITABLE);
        context.setEnabled(AttributeFormPropertyCode.EDITABLE_IN_LISTS, editable);
        context.setProperty(AttributeFormPropertyCode.EDITABLE, editable);
        context.setEnabled(AttributeFormPropertyCode.EXPORT_NDAP, !NOT_EDITABLE_ATTRIBUTE_TYPES.contains(attrType));
        context.setEnabled(AttributeFormPropertyCode.RELATED_ATTRS_TO_EXPORT, !NOT_EDITABLE_ATTRIBUTE_TYPES.contains(
                attrType));
        if (editable)
        {
            context.setProperty(AttributeFormPropertyCode.REQUIRED, false);
            context.setProperty(AttributeFormPropertyCode.REQUIRED_IN_INTERFACE, false);
            context.setProperty(AttributeFormPropertyCode.UNIQUE, false);
        }
        else
        {
            context.setProperty(AttributeFormPropertyCode.EDITABLE_IN_LISTS, false);
        }

        context.setProperty(AttributeFormPropertyCode.PERMITTED_TYPES, new HashSet<>());

        if (attrType.equals(AggregateAttributeType.CODE))
        {
            context.setProperty(AttributeFormPropertyCode.PERMITTED_TYPES,
                    Lists.newArrayList(DtObject.CREATE_FROM_FQN.apply(Employee.FQN),
                            DtObject.CREATE_FROM_FQN.apply(Team.FQN), DtObject.CREATE_FROM_FQN.apply(OU.FQN)));
        }

        if (!hasDefaultValue)
        {
            context.getPropertyControllers().get(AttributeFormPropertyCode.DEFAULT_VALUE).removeProperty();
        }

        boolean isDateTime = Constants.DATE_TIME_TYPES.contains(attrType);
        context.setEnabled(AttributeFormPropertyCode.DATE_TIME_COMMON_RESTRICTIONS, isDateTime);
        context.setEnabled(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_SCRIPT, isDateTime);
        context.setEnabled(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_TYPE, isDateTime);
        context.setEnabled(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_ATTRIBUTE, isDateTime);
        context.setEnabled(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_CONDITION, isDateTime);
    }
}
