package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determinablebytemplate;

import static ru.naumen.metainfo.shared.Constants.COMPOSITE_ATTR_TYPES;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.GenerationRuleDelegateHelper;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 *
 * <AUTHOR>
 *
 * @param <F>
 */
public class CompositeRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{

    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        if (metaClass.getFqn().toString().endsWith("__Evt") || Constants.GeoHistoryRecord.FQN.equals(metaClass
                .getFqn()))
        {
            callback.onSuccess(false);
            return;
        }

        Boolean computable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        Boolean determinable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);
        Boolean useGenRule = context.getPropertyValues().getProperty(AttributeFormPropertyCode.USE_GEN_RULE);

        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        String attrCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE);

        callback.onSuccess(!computable && !determinable && !useGenRule && COMPOSITE_ATTR_TYPES.contains(attrType)
                           && GenerationRuleDelegateHelper.isSuitableAttribute(metaClass.getFqn(), attrCode));
    }
}
