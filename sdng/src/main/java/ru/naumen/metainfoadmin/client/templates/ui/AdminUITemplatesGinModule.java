package ru.naumen.metainfoadmin.client.templates.ui;

import java.util.function.Function;

import jakarta.inject.Singleton;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.shared.HasCode.HasCodeExtractor;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.TabListManagerDisplay;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.TabListManagerDisplayImpl;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.TabModel;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands.EditTabCommand;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands.MoveTabDownCommand;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands.MoveTabUpCommand;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands.TabModelCommandFactoryInitializer;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands.TabModelCommandParam;
import ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands.ToggleTabCommand;

/**
 * Конфигурация зависимостей для настройки шаблонов карточек в интерфейсе администратора.
 * <AUTHOR>
 * @since Jul 29, 2021
 */
public class AdminUITemplatesGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        bind(UITemplateSelectListDisplay.class).to(UITemplateSelectListDisplayImpl.class);
        bind(TabListManagerDisplay.class).to(TabListManagerDisplayImpl.class);
        bind(UITemplateUserSettingsStorage.class).to(UITemplateUserSettingsStorageImpl.class);

        bind(new TypeLiteral<Function<? super TabModel, String>>()
        {
        })
                .annotatedWith(Names.named("rowIdExtractor"))
                .to(new TypeLiteral<HasCodeExtractor<TabModel>>()
                {
                })
                .in(Singleton.class);

        bind(TabModelCommandFactoryInitializer.class).asEagerSingleton();
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditTabCommand.class)
                .build(new TypeLiteral<CommandProvider<EditTabCommand, TabModelCommandParam>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, ToggleTabCommand.class)
                .build(new TypeLiteral<CommandProvider<ToggleTabCommand, TabModelCommandParam>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, MoveTabUpCommand.class)
                .build(new TypeLiteral<CommandProvider<MoveTabUpCommand, TabModelCommandParam>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, MoveTabDownCommand.class)
                .build(new TypeLiteral<CommandProvider<MoveTabDownCommand, TabModelCommandParam>>()
                {
                }));
    }
}
