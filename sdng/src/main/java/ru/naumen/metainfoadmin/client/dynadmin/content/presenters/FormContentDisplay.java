package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolBarDisplay;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.metainfoadmin.client.common.content.HasEditable;

import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HasWidgets.ForIsWidget;
import com.google.gwt.user.client.ui.IsWidget;

/**
 * {@link Display} настройки формы добавления/редактирования. 
 * Располагается на соответствующей вкладке интерфейса технолога. 
 *
 * <AUTHOR>
 * @since 08.09.2010
 *
 */
public interface FormContentDisplay extends Display, HasEditable
{
    void addToolContent(IsWidget display);

    FlowPanel getBodyPanel();

    String getCaption();

    ForIsWidget getContentContainer();

    ButtonToolBarDisplay getToolBar();

    ForIsWidget getToolContainer();

    boolean isCaseVisible();

    void setCaption(String caption);

    /**
     * Устанавливает содержимое формы
     *
     * @param layout
     */
    void setContent(IsWidget content);

    void setToolBarVisible(boolean visible);
}
