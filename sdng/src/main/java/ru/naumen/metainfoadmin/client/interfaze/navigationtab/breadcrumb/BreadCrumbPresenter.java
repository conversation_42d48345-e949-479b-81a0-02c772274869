package ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.INTERFACE_AND_NAVIGATION;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import jakarta.annotation.Nullable;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.GetBreadCrumbAction;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsPlace;

/**
 * Презентер страницы хлебной карточки
 *
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public class BreadCrumbPresenter extends AdminTabPresenter<BreadCrumbPlace>
        implements BreadCrumbChangedHandler
{
    protected OnStartCallback<DtoContainer<NavigationSettings>> refreshCallback =
            new OnStartBasicCallback<DtoContainer<NavigationSettings>>(
                    getDisplay())
            {
                @Override
                protected void handleSuccess(@Nullable DtoContainer<NavigationSettings> value)
                {
                    if (value != null)
                    {
                        int index = value.get().getBreadCrumb().indexOf(breadCrumbContext.getCrumb());
                        Crumb item = value.get().getBreadCrumb().get(index);
                        breadCrumbContext.setCrumb(item);
                        breadCrumbContext.setSettings(value);
                    }
                    refreshDisplay();
                }
            };

    private final BreadCrumbMessages breadCrumbMessages;
    private final DispatchAsync dispatch;
    private final BreadCrumbPropertiesPresenter propertiesPresenter;

    private BreadCrumbContext breadCrumbContext;

    @Inject
    public BreadCrumbPresenter(AdminTabDisplay display,
            EventBus eventBus,
            BreadCrumbMessages breadCrumbMessages,
            DispatchAsync dispatch,
            BreadCrumbPropertiesPresenter propertiesPresenter)
    {
        super(display, eventBus);
        this.breadCrumbMessages = breadCrumbMessages;
        this.dispatch = dispatch;
        this.propertiesPresenter = propertiesPresenter;
    }

    @Override
    public void init(BreadCrumbPlace place)
    {
        super.init(place);
        if (place.getCrumb() != null)
        {
            breadCrumbContext = new BreadCrumbContext(place.getCrumb(), place.getSettings());
        }
    }

    @Override
    public void onBreadCrumbChanged(BreadCrumbChangedEvent event)
    {
        breadCrumbContext.setCrumb(event.getCrumb());
        breadCrumbContext.setSettings(event.getSettings());
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        if (breadCrumbContext != null)
        {
            getDisplay().setTitle(breadCrumbMessages.titleOfBreadCrumbCard(breadCrumbContext.getCrumb().getTitle()));
        }
        super.refreshDisplay();
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        registerHandler(eventBus.addHandler(BreadCrumbChangedEvent.getType(), this));

        super.onBindAfterCheckPermission();

        prevPageLinkPresenter.bind(breadCrumbMessages.back(), new InterfaceSettingsPlace("navigation"), true);

        if (breadCrumbContext == null)
        {
            dispatch.execute(new GetBreadCrumbAction(getPlace().getCode()),
                    new BasicCallback<SimpleResult<DtoContainer<NavigationSettings>>>(
                            getDisplay())
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<DtoContainer<NavigationSettings>> response)
                        {
                            super.handleSuccess(response);
                            breadCrumbContext = new BreadCrumbContext(
                                    response.get().get().findCrumb(getPlace().getCode()),
                                    response.get());
                            hasContext();
                            refreshDisplay();
                        }
                    });
        }
        else
        {
            hasContext();
        }
    }

    private void hasContext()
    {
        propertiesPresenter.init(breadCrumbContext, refreshCallback);
        addContent(propertiesPresenter, "props");
        propertiesPresenter.revealDisplay();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return INTERFACE_AND_NAVIGATION;
    }
}