package ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb;

import java.util.ArrayDeque;
import java.util.Comparator;
import java.util.Deque;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

import com.google.common.collect.HashMultimap;

import java.util.ArrayList;
import java.util.HashMap;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeUri;
import com.google.gwt.safehtml.shared.UriUtils;
import com.google.inject.Inject;

import ru.naumen.common.client.utils.HtmlSanitizeUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.CrumbRelationAttribute;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Утилитарные методы для работы с крошками
 *
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public class BreadCrumbHelper
{
    private static final int SHORT_TITLE_LIMIT = 20;

    private Function<DtObject, String> WITH_LINK_CONVERTER = new Function<DtObject, String>()
    {
        @Override
        public String apply(DtObject dt)
        {
            return formatters.linkToMetaClass(dt.getMetaClass(), dt.getTitle()).asString();
        }
    };

    private Function<DtObject, String> SIMPLE_CONVERTER = new Function<DtObject, String>()
    {

        @Override
        public String apply(DtObject dt)
        {
            return dt.getTitle();
        }
    };

    @Inject
    private Formatters formatters;
    @Inject
    private CommonHtmlTemplates htmlTemplates;
    @Inject
    private PlaceHistoryMapper placeHistoryMapper;
    @Inject
    private HtmlSanitizeUtils htmlSanitizeUtils;

    private Map<ClassFqn, String> titleByFqn;
    private HashMultimap<ClassFqn, ClassFqn> items;

    /**
     * Генерируем все возможные примеры для связи
     */
    public String generateAllExamples4Rel(Crumb to, CrumbRelationAttribute rel)
    {
        return StringUtilities.join(generateAllExamples(to, rel), "");
    }

    /**
     * Генерируем только один пример для связи
     */
    public String generateExamples4Crumb(Crumb to)
    {
        List<String> result = new ArrayList<>();
        for (CrumbRelationAttribute rel : to.getRelationAttributes())
        {
            result.add(generateAllExamples(to, rel).get(0));
        }
        return StringUtilities.join(result, "");
    }

    /**
     * Название крошки с лимитом на длину
     */
    public String getCrumbTitle(Crumb crumb)
    {
        String title = getCrumbTitle(crumb, SIMPLE_CONVERTER);

        int index = title.indexOf(':') + 1;
        int lengthWithoutClass = title.length() - index;
        if (index > 0 && lengthWithoutClass > SHORT_TITLE_LIMIT)
        {
            return title.substring(0, index + SHORT_TITLE_LIMIT).concat("...");
        }
        return title;
    }

    /**
     * Название крошки для всплывающей подсказки.
     * Если длина полного имени крошки не привышает лимит то метод вернет null
     * Иначе полное название крошки
     */
    public String getCrumbTitle4Popup(Crumb crumb)
    {
        int limit = crumb.getTitle().length() + SHORT_TITLE_LIMIT;
        String fullTitle = getFullCrumbTitle(crumb);
        if (limit > fullTitle.length())
        {
            return "";
        }
        return fullTitle;
    }

    /**
     * Полное название крошки
     */
    public String getFullCrumbTitle(Crumb crumb)
    {
        return getCrumbTitle(crumb, SIMPLE_CONVERTER);
    }

    /**
     * Полное название крошки c ссылками на метаклассы
     */
    public String getFullCrumbTitleWithLinks(Crumb crumb)
    {
        return getCrumbTitle(crumb, WITH_LINK_CONVERTER);
    }

    public String getLinkToCrumb(Crumb item)
    {
        String popup = getCrumbTitle4Popup(item);
        BreadCrumbPlace place = new BreadCrumbPlace(item.getCode());
        SafeUri uri = UriUtils.fromSafeConstant("#" + placeHistoryMapper.getToken(place));
        return wrap(
                htmlTemplates.anchorWithIdAndPopup(getCrumbTitle(item), uri, item.getCode(), popup, false).asString());
    }

    public String getPermittedTypesView(CrumbRelationAttribute input)
    {
        return wrap(htmlSanitizeUtils.sanitize(SIMPLE_CONVERTER.apply(input.getPermittedClass())));
    }

    public String getPermittedTypesViewWithLinks(CrumbRelationAttribute input)
    {
        return wrap(WITH_LINK_CONVERTER.apply(input.getPermittedClass()));
    }

    public String getRelationAttributeTitle(CrumbRelationAttribute input)
    {
        return wrap(htmlSanitizeUtils.sanitize(input.getTitle()));
    }

    /**
     * Инициализация хелпера
     * Готовим элементы цепи 
     * чтобы генерировать все возможные варианты хлебных крошек
     */
    public void init(List<Crumb> bread)
    {
        titleByFqn = new HashMap<>();
        items = HashMultimap.create();
        for (Crumb crumb : bread)
        {
            ClassFqn crumbClass = crumb.getMetaClass();
            titleByFqn.put(crumbClass, crumb.getTitle());
            for (CrumbRelationAttribute rel : crumb.getRelationAttributes())
            {
                items.put(crumbClass, rel.getPermittedClass().getMetaClass());
                titleByFqn.put(rel.getPermittedClass().getMetaClass(), rel.getPermittedClass().getTitle());
            }
        }
    }

    private String createPostfix(String postfix, ClassFqn fqn)
    {
        StringBuilder sb = new StringBuilder(postfix);
        if (sb.length() > 0)
        {
            sb.insert(0, "&nbsp;/&nbsp;");
        }
        sb.insert(0, titleByFqn.get(fqn));
        return sb.toString();
    }

    private void element(List<String> result, Deque<ClassFqn> stack, ClassFqn current, String postfix)
    {
        String newPostfix = createPostfix(postfix, current);
        Set<ClassFqn> els = items.get(current);
        if (els.isEmpty() || stack.contains(current))
        {
            result.add(wrap(htmlSanitizeUtils.sanitize(newPostfix)));
        }
        else
        {
            stack.addFirst(current);
            for (ClassFqn next : els)
            {
                element(result, stack, next, newPostfix);
            }
            stack.removeFirst();
        }
    }

    private List<String> generateAllExamples(Crumb to, CrumbRelationAttribute rel)
    {
        List<String> result = new ArrayList<>();
        ClassFqn el1 = to.getMetaClass();
        ClassFqn el2 = rel.getPermittedClass().getMetaClass();
        Deque<ClassFqn> stack = new ArrayDeque<>();
        stack.add(el1);
        element(result, stack, el2, createPostfix("", el1));
        result.sort(new Comparator<String>()
        {
            @Override
            public int compare(String o1, String o2)
            {
                int io1 = o1.split("/").length;
                int io2 = o2.split("/").length;
                int compare = Integer.compare(io2, io1);
                return compare == 0 ? StringUtilities.compareTo(o1, o2) : compare;
            }
        });
        return result;
    }

    private String getCrumbTitle(Crumb crumb, Function<DtObject, String> converter)
    {
        StringBuilder sb = new StringBuilder();
        for (DtObject caze : crumb.getCases())
        {
            ClassFqn fqn = caze.getMetaClass();
            if (fqn.isCase())
            {
                if (sb.length() > 0)
                {
                    sb.append(", ");
                }
                sb.append(converter.apply(caze));
            }
        }
        if (sb.length() > 0)
        {
            sb.insert(0, ": ");
        }
        sb.insert(0, converter.apply(new SimpleDtObject(null, crumb.getTitle(), crumb.getMetaClass())));
        return sb.toString();
    }

    private String wrap(String str)
    {
        return "<div>" + str + "</div>";
    }
}
