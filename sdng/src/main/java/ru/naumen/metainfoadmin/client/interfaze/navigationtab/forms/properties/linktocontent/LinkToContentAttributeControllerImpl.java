package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import static ru.naumen.metainfo.shared.Constants.LinkObjectType.CURRENT_USER;
import static ru.naumen.metainfo.shared.Constants.LinkObjectType.OBJECT_LINKED_TO_CURRENT_USER;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist.attrseltree.RelationAttrsTreeFactoryContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist.attrseltree.SelectAttrTreeGinModule;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Контроллер свойства "Атрибут" типа "дерево атрибутов"
 *
 * <AUTHOR>
 * @since 29.10.2020
 */
public class LinkToContentAttributeControllerImpl extends LinkToContentAttrTreePropertyControllerBase
{
    @Inject
    public LinkToContentAttributeControllerImpl(
            //@formatter:off
            @Assisted String code,
            @Assisted PropertyContainerContext context,
            @Assisted PropertyParametersDescriptor propertyParams,
            @Assisted PropertyDelegateDescriptor<RelationsAttrTreeObject,
                    PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<?, RelationsAttrTreeObject, ?>>> propertyDelegates)
    //@formatter:on
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    @Override
    protected void doBind(AsyncCallback<Void> callback)
    {
        callback.onSuccess(null);
    }

    @Override
    public void refresh()
    {
        final Boolean showHierarchy = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.SHOW_HIERARCHY);

        String linkObject = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT);

        boolean isVisible = LinkToContentMetaClassPropertiesProcessor.getRelObjectListFragmentVisibility(context)
                            && !Boolean.TRUE.equals(showHierarchy) && linkObject != null;

        if (!isVisible)
        {
            new DefaultRefreshCallback().onSuccess(false);
            context.getPropertyControllers()
                    .get(MenuDtoProperties.MenuItemLinkToContentCode.ATTR_CHAIN)
                    .unbindValidators();
            return;
        }
        ClassFqn metaclass;

        if (CURRENT_USER.equals(linkObject))
        {
            metaclass = Employee.FQN;
        }
        else if (OBJECT_LINKED_TO_CURRENT_USER.equals(linkObject))
        {
            RelationsAttrTreeObject linkObjectAttr = context.getPropertyValues()
                    .getProperty(MenuItemLinkToContentCode.LINK_OBJECT_ATTR);

            if (linkObjectAttr == null || linkObjectAttr.getAttribute() == null)
            {
                context.getPropertyControllers()
                        .get(MenuDtoProperties.MenuItemLinkToContentCode.ATTR_CHAIN)
                        .unbindValidators();
                new DefaultRefreshCallback().onSuccess(false);

                return;
            }

            metaclass = linkObjectAttr.getAttribute().getType().<ObjectAttributeType> cast().getRelatedMetaClass();
        }
        else
        {
            metaclass = ClassFqn.parse(linkObject);
        }

        RelationAttrsTreeFactoryContext treeContext = new RelationAttrsTreeFactoryContext(metaclass,
                SelectAttrTreeGinModule.SIMPLE_ATTR_SELECT_FILTER, null);

        setTreeProperty(treeContext);
    }
}
