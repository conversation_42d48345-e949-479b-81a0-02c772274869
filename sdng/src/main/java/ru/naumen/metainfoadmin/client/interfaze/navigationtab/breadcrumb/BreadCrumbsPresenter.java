package ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb;

import static ru.naumen.core.client.adminpermission.AdminPermissionUtils.createPermissionPredicate;

import java.util.Comparator;
import java.util.List;
import java.util.function.Function;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.Handler;
import com.google.gwt.user.cellview.client.ColumnSortList;

import jakarta.inject.Inject;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.columns.HTMLCell;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.CrumbRelationAttribute;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableResources;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableStyle;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsResources;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.ScrollableTableDisplayImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.AddCrumbCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.BreadCrumbCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DeleteCrumbCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EditCrumbCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.NavigationSettingsChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.NavigationSettingsChangedHandler;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Презентер списка хлебных крошек
 *
 * <AUTHOR>
 * @since 11 июня 2014 г.
 */
public class BreadCrumbsPresenter extends BasicPresenter<TableDisplay<Crumb>> implements BreadCrumbChangedHandler,
        NavigationSettingsChangedHandler
{
    private OnStartCallback<DtoContainer<NavigationSettings>> refreshCallback =
            new OnStartBasicCallback<DtoContainer<NavigationSettings>>(
                    getDisplay())
            {
                @Override
                protected void handleSuccess(DtoContainer<NavigationSettings> value)
                {
                    BreadCrumbsPresenter.this.settings = value;
                    param.setSettings(settings);
                    refreshDisplay();
                }
            };

    private final ColumnSortEvent.Handler sortHandler = new ColumnSortEvent.Handler()
    {
        @Override
        public void onColumnSort(ColumnSortEvent event)
        {
            columnSortList = event.getColumnSortList();
            fillTable(Lists.newArrayList(getDisplay().getTable().getVisibleItems()));
        }
    };

    @Inject
    private BreadCrumbHelper helper;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    private WithArrowsCellTableResources cellTableResources;
    @Inject
    private BreadCrumbMessages messages;
    @Inject
    private ObjectListColumnBuilder tableBuilder;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private NavigationSettingsResources resources;

    private ColumnSortList columnSortList;
    private DtoContainer<NavigationSettings> settings;
    protected PermissionHolder permissions;
    private BreadCrumbCommandParam param;
    private final ToolBarDisplayMediator<NavigationSettings> toolBar;

    @Inject
    public BreadCrumbsPresenter(ScrollableTableDisplayImpl<Crumb> display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    public void init(DtoContainer<NavigationSettings> container)
    {
        NavigationSettings newSettings = new NavigationSettings();
        newSettings.copyFrom(container.get());
        DtoContainer<NavigationSettings> newSettingsDto = new DtoContainer<>(newSettings);
        this.settings = newSettingsDto;
        helper.init(container.get().getBreadCrumb());
        param = new BreadCrumbCommandParam(container, refreshCallback);
        this.permissions = container.getProperty(SettingsSet.ADMIN_PERMISSIONS);
    }

    @Override
    public void onBreadCrumbChanged(BreadCrumbChangedEvent event)
    {
        NavigationSettings newSettings = new NavigationSettings();
        newSettings.copyFrom(event.getSettings().get());
        DtoContainer<NavigationSettings> newSettingsDto = new DtoContainer<>(newSettings);
        this.settings = newSettingsDto;
        helper.init(settings.get().getBreadCrumb());
    }

    @Override
    public void onNavigationSettingsChanged(NavigationSettingsChangedEvent event)
    {
        DtoContainer<NavigationSettings> newSettings = event.getSettings();
        if (settings.get().isShowBreadCrumb() == !newSettings.get().isShowBreadCrumb())
        {
            init(newSettings);
            refreshDisplay();
        }
    }

    @Override
    public void refreshDisplay()
    {
        getDisplay().refresh();
        helper.init(settings.get().getBreadCrumb());
        fillTable(settings.get().getBreadCrumb());
        if (settings.get().isShowBreadCrumb())
        {
            getDisplay().getCaptionWidget().removeStyleName(resources.css().archiveBlockTitle());
        }
        else
        {
            getDisplay().getCaptionWidget().setStyleName(resources.css().archiveBlockTitle(), true);
            getDisplay().getCaptionWidget().setTitle(messages.visibilityHidden());
        }
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(BreadCrumbChangedEvent.getType(), this));
        registerHandler(eventBus.addHandler(NavigationSettingsChangedEvent.getType(), this));
        addTool(ButtonCode.ADD, cmessages.addItem(), AddCrumbCommand.ID);
        getDisplay().setCaption(messages.breadCrumb());
        initTable();
        toolBar.bind();
    }

    private void addActionColumn(String... commands)
    {
        PermissionType permissionType = DeleteCrumbCommand.ID.equals(commands[0])
                ? PermissionType.DELETE
                : PermissionType.EDIT;
        tableBuilder.addActionColumn(display, param, createPermissionPredicate(permissionType, permissions), commands);
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private void addTool(String btn, String title, String cmd)
    {
        toolBar.add((ButtonPresenter)buttonFactory.create(btn, title, cmd, param));
    }

    private void fillTable(List<Crumb> list)
    {
        if (columnSortList.size() > 0)
        {
            final int sign = columnSortList.get(0).isAscending() ? -1 : +1;
            list.sort(new Comparator<Crumb>()
            {
                @Override
                public int compare(Crumb o1, Crumb o2)
                {
                    int compareTo = StringUtilities.compareTo(o1.getTitle(), o2.getTitle());
                    if (compareTo == 0)
                    {
                        // На случай если будут классы с одинаковыми именами
                        compareTo = StringUtilities.compareTo(helper.getFullCrumbTitle(o1),
                                helper.getFullCrumbTitle(o2));
                    }
                    return sign * compareTo;
                }
            });
        }
        getDisplay().getTable().setRowCount(list.size());
        getDisplay().getTable().setRowData(0, list);
    }

    private void initTable()
    {
        DataTable<Crumb> table = getDisplay().getTable();
        table.setVisibleRange(0, DefaultCellTable.DEFAULT_PAGESIZE);
        table.addColumnSortHandler(new Handler()
        {
            @Override
            public void onColumnSort(ColumnSortEvent event)
            {
                event.getSource();
            }
        });

        WithArrowsCellTableStyle tableStyle = cellTableResources.cellTableStyle();

        Column<Crumb, String> textColumn = new Column<Crumb, String>(new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
        {
            @Override
            public String getValue(Crumb item)
            {
                return helper.getLinkToCrumb(item);
            }
        };
        textColumn.setSortable(true);
        textColumn.setDefaultSortAscending(false);
        textColumn.setCellStyleNames(tableStyle.columnWithLineBreak());
        table.getColumnSortList().push(textColumn);
        table.addColumn(textColumn, messages.objects());

        textColumn = new Column<Crumb, String>(new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
        {
            @Override
            public String getValue(Crumb item)
            {
                Function<CrumbRelationAttribute, String> f = new Function<CrumbRelationAttribute, String>()
                {
                    @Override
                    public String apply(CrumbRelationAttribute input)
                    {
                        return helper.getRelationAttributeTitle(input);
                    }
                };
                String value = StringUtilities.join(item.getRelationAttributes(), "", f);
                return value;
            }
        };
        textColumn.setCellStyleNames(tableStyle.columnWithLineBreak());
        table.addColumn(textColumn, messages.relationAttributes());

        textColumn = new Column<Crumb, String>(new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
        {
            @Override
            public String getValue(Crumb item)
            {
                Function<CrumbRelationAttribute, String> f = new Function<CrumbRelationAttribute, String>()
                {
                    @Override
                    public String apply(CrumbRelationAttribute input)
                    {
                        return helper.getPermittedTypesView(input);
                    }
                };
                return StringUtilities.join(item.getRelationAttributes(), "", f);
            }
        };
        textColumn.setCellStyleNames(tableStyle.columnWithLineBreak());
        table.addColumn(textColumn, messages.permittedTypes());

        textColumn = new Column<Crumb, String>(new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
        {
            @Override
            public String getValue(final Crumb item)
            {
                return helper.generateExamples4Crumb(item);
            }
        };
        textColumn.setCellStyleNames(tableStyle.columnWithLineBreak());
        table.addColumn(textColumn, messages.presentationOfBreadCrumb());

        addActionColumn(EditCrumbCommand.ID);
        addActionColumn(DeleteCrumbCommand.ID);

        table.asWidget().getHeader(0).setHeaderStyleNames(tableStyle.header4Crumbs());
        table.asWidget().getHeader(1).setHeaderStyleNames(tableStyle.header4Crumbs());
        table.asWidget().getHeader(2).setHeaderStyleNames(tableStyle.header4Crumbs());
        table.asWidget().getHeader(3).setHeaderStyleNames(tableStyle.header4Crumbs());

        columnSortList = table.getColumnSortList();
        table.addColumnSortHandler(sortHandler);
        table.asWidget().ensureDebugId("bread-crumb-table");
    }
}
