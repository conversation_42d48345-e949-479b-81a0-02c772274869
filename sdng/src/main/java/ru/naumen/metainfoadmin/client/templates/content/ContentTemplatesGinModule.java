package ru.naumen.metainfoadmin.client.templates.content;

import java.util.Collection;

import jakarta.inject.Singleton;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.templates.content.columns.ContentTemplateSummaryService;
import ru.naumen.metainfoadmin.client.templates.content.columns.ContentTemplateSummaryServiceImpl;
import ru.naumen.metainfoadmin.client.templates.content.command.ContentTemplateCommandFactoryInitializer;
import ru.naumen.metainfoadmin.client.templates.content.command.DeleteContentTemplateCommand;
import ru.naumen.metainfoadmin.client.templates.content.command.DeleteContentTemplatesCommand;
import ru.naumen.metainfoadmin.client.templates.content.command.EditContentTemplateCommand;

/**
 * Конфигурация сервисов и представлений для шаблонов контентов.
 * <AUTHOR>
 * @since Mar 24, 2021
 */
public class ContentTemplatesGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        bind(ContentTemplateServiceAsync.class).to(ContentTemplateServiceAsyncImpl.class).in(Singleton.class);
        bind(ContentTemplateSummaryService.class).to(ContentTemplateSummaryServiceImpl.class).in(Singleton.class);
        bindCommands();
    }

    private void bindCommands()
    {
        bind(ContentTemplateCommandFactoryInitializer.class).asEagerSingleton();

        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditContentTemplateCommand.class)
                .build(new TypeLiteral<CommandProvider<EditContentTemplateCommand, CommandParam<DtObject, DtObject>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, DeleteContentTemplateCommand.class)
                .build(new TypeLiteral<CommandProvider<DeleteContentTemplateCommand, CommandParam<DtObject, Void>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, DeleteContentTemplatesCommand.class)
                .build(new TypeLiteral<CommandProvider<DeleteContentTemplatesCommand,
                        CommandParam<Collection<DtObject>, Void>>>()
                {
                }));
    }
}
