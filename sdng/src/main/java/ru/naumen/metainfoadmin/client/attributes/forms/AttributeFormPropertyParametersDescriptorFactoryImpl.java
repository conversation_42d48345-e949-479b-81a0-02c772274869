package ru.naumen.metainfoadmin.client.attributes.forms;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import jakarta.inject.Inject;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptorFactoryImpl;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;

/**
 * <AUTHOR>
 * @since 14.05.2012
 *
 * ВАЖНО! При регистрации нового параметра атрибута, необходимо так же изменить displayPos
 * для переопределенных параметров в наследниках данного класса
 */
public class AttributeFormPropertyParametersDescriptorFactoryImpl<F extends ObjectForm>
        extends PropertyParametersDescriptorFactoryImpl<Attribute, F>
{
    @Inject
    protected AttributesMessages messages;
    @Inject
    protected TagsMessages tagsMessages;
    @Inject
    protected AdminDialogMessages adminDialogMessages;

    @Override
    protected void build()
    {
        int i = 0;
        //код свойства,                       название свойства, есть ли красная звездочка в углу, debugId,
        // порядковый номер, размещается ли сразу на форме, вешается ли сразу валидатор
        //@formatter:off
        registerOrModifyProperty(INHERIT,                     "",                                 false, "inherit",               i++, false,  true); //0
        registerOrModifyProperty(TITLE,                       cmessages.title(),                  true,  "title",                 i++, true,   true); //1
        registerOrModifyProperty(HIDDEN_ATTR_CAPTION,    messages.hideCaptionAttribute(),    true,  "hiddenAttrCaption",i++,true, false); //2
        ++i;
        registerOrModifyProperty(ATTR_TYPE,                   cmessages.typeOfValue(),            true,  "attrType",              i++, true,   true); //3
        registerOrModifyProperty(ATTR_CHAIN,                  cmessages.attributeLink(),          true,  "attributeChain",        i++, true,   true); //4
        registerOrModifyProperty(ATTR_CHAIN_VIEW,             cmessages.attributeLink(),          true,  "attributeChain",        i++, true,   true); //5
        registerOrModifyProperty(RELATED_OBJECT_METACLASS,    cmessages.objectClass(),             false, "relatedObjectMetaclass",i++, true,   false); //6
        registerOrModifyProperty(RELATED_OBJECT_ATTRIBUTE,    messages.attibuteOfRelatedClass(),  true,  "relatedObjectAttribute",i++, true,   true); //7
        registerOrModifyProperty(RELATED_OBJECT_HIERARCHY_LEVEL,messages.displayValueWithHierarchy(),true,"relatedObjectHierarchyLevel",i++, true,   true); //8
        registerOrModifyProperty(EXPORT_NDAP,                 messages.exportNDAP(),              false, "exportNDAP",            i++, true,   false); //9
        registerOrModifyProperty(COMPUTABLE,                  cmessages.computable(),             false, "computable",            i++, true,   true); //10
        registerOrModifyProperty(COMPOSITE,                   messages.composite(),               false, "composite",             i++, false,  false); //11
        registerOrModifyProperty(TEMPLATE,                    messages.template(),                true,  "template",              i++, false,  false); //12
        registerOrModifyProperty(USE_GEN_RULE,                "",                                 true,  "useGenRule",            i++, false,  false); //13
        registerOrModifyProperty(GEN_RULE,                    "",                                 true,  "genRule",               i++, false,  false); //14
        registerOrModifyProperty(DETERMINABLE,                messages.determinableByValueMap(),  false, "determinable",          i++, true,   true); //15
        registerOrModifyProperty(DETERMINER,                  messages.ruleToDetermine(),         false, "determiner",            i++, false,  false); //16
        registerOrModifyProperty(EDITABLE,                    messages.editable(),                false, "editable",              i++, true,   true); //17
        registerOrModifyProperty(EDITABLE_IN_LISTS,           messages.editableInLists(),         false, "editableInLists",       i++, true,   true); //18
        registerOrModifyProperty(REQUIRED,                    messages.mandatory(),               false, "required",              i++, true,   true); //19
        registerOrModifyProperty(COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT, messages.computeAnyCatalogElementsScript(), true, "computeAnyCatalogElementsScript", i++, false,  true); //20
        registerOrModifyProperty(REQUIRED_IN_INTERFACE,       messages.mandatoryInInterface(),    false, "requiredInInterface",   i++, false,  true); //21
        registerOrModifyProperty(UNIQUE,                      messages.unique(),                  false, "unique",                i++, true,   true); //22
        registerOrModifyProperty(TARGET_CLASS,                messages.objectClass(),             true,  "targetClass",           i++, false,  false); //23
        registerOrModifyProperty(TARGET_CATALOG,              cmessages.catalog(),                true,  "targetCatalog",         i++, false,  false); //24
        registerOrModifyProperty(DIRECT_LINK_TARGET,          messages.directLink(),              true,  "directLinkTarget",      i++, false,  false); //25
        registerOrModifyProperty(TARGET_TIMER,                messages.timerDefinition(),         true,  "timerDefinition",       i++, false,  false); //26
        registerOrModifyProperty(AGGREGATE_CLASSES,           messages.aggregateClasses(),        true,  "aggregateClasses",      i++, false,  false); //27
        registerOrModifyProperty(AGGREGATE_ATTRIBUTES,        messages.aggregatingAttributes(),   false, "aggregateAttributes",   i++, false,  false); //28
        registerOrModifyProperty(SCRIPT,                      messages.script(),                  true,  "script",                i++, false,  false); //29
        registerOrModifyProperty(PERMITTED_TYPES,             cmessages.classTypes(),             true,  "permittedTypes",        i++, false,  false); //30
        registerOrModifyProperty(RELATED_ATTRS_TO_EXPORT,     messages.relatedAttrsToExport(),    true, "relatedAttrsToExport",   i++, true,   false); //31
        registerOrModifyProperty(FILTERED_BY_SCRIPT,          messages.filterWhileEditing(),      false, "filteredByScript",      i++, false,  false); //32
        registerOrModifyProperty(SCRIPT_FOR_FILTRATION,       messages.script(),                  true,  "scriptForFiltration",   i++, false,  false); //33
        registerOrModifyProperty(HIDE_ARCHIVED,               messages.hideArchived(),            false, "hideArchive",           i++, false,  false); //34
        registerOrModifyProperty(COMPUTABLE_ON_FORM,          messages.computableOnForm(),        false, "computeValueOnEdit",    i++, true,   false); //35
        registerOrModifyProperty(COMPUTABLE_ON_FORM_SCRIPT,   messages.computableOnFormScript(),  true, "valueOnEditScript",     i++, false,  false); //36
        registerOrModifyProperty(DATE_TIME_COMMON_RESTRICTIONS, messages.dateTimeCommonRestrictions(), true, "dateTimeCommonRestrictions", i++, true, true); //37
        registerOrModifyProperty(DATE_TIME_RESTRICTION_TYPE,  messages.dateTimeRestrictionType(), true, "dateTimeRestrictionType",i++, true,   true); //38
        registerOrModifyProperty(DATE_TIME_RESTRICTION_SCRIPT,messages.dateTimeRestrictionScript(),true, "dateTimeRestrictionScript",i++, false,  false); //39
        registerOrModifyProperty(DATE_TIME_RESTRICTION_CONDITION,  messages.dateTimeRestrictionCondition(), true, "dateTimeRestrictionCondition",i++, true, true); //40
        registerOrModifyProperty(DATE_TIME_RESTRICTION_ATTRIBUTE,  messages.dateTimeRestrictionAttribute(), true, "dateTimeRestrictionAttribute",i++, true,   true); //41
        registerOrModifyProperty(NEED_STORE_UNITS,            messages.needStoreUnits(),          false, "needStoreUnits",        i++, false,   false); //42
        registerOrModifyProperty(ADVLIST_SEMANTIC_FILTERING,  messages.advlistSemanticFiltering(),false, "advlistSemanticFiltering", i++, true, false);//43
        registerOrModifyProperty(SHOW_PRS,                    messages.showPresentation(),        true, "showPresentation",      i++, true,   true); //44
        registerOrModifyProperty(HIDDEN_WHEN_EMPTY,           messages.hideWhenEmpty(),           false, "hiddenWhenEmpty",       i++, true,   false); //45
        registerOrModifyProperty(EDIT_PRS,                    messages.editPresentation(),        true, "editPresentation",      i++, true,   true); //46
        registerOrModifyProperty(STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE, messages.structuredObjectsViewForBuildingTree(),true, "structuredObjectsViewForBuildingTree", i++,true,true); //47
        registerOrModifyProperty(HAS_GROUP_SEPARATORS,        messages.hasGroupSeparators(),      false, "hasGroupSeparators",    i++, true,   true); //48
        registerOrModifyProperty(DIGITS_COUNT_RESTRICTION,    messages.digitsCountRestrictions(), false, "digitsCountRestrictions", i++, true,   true); //49
        registerOrModifyProperty(HIDDEN_WHEN_NO_POSSIBLE_VALUES,messages.hideWhenNoPossibleValues(),true, "hiddenWhenNoPossibleValues",i++,true,false); //50
        registerOrModifyProperty(INTERVAL_AVAILABLE_UNITS,    messages.intervalAvailableUnits(),  true, "intervalAvailableUnits",i++, true,   false); //51
        registerOrModifyProperty(INPUTMASK_MODE,              messages.inputmaskMode(),           true, "inputmaskMode",         i++, false,  false); //52
        registerOrModifyProperty(INPUTMASK,                   messages.inputmask(),               true,  "inputmask", i++, false,  false); //53
        registerOrModifyProperty(SUGGEST_CATALOG,             cmessages.catalog(),                true, "suggestCatalog",        i++, false,  false); //54
        registerOrModifyProperty(DESCRIPTION,                 messages.description(),             false, "description",           i++, true,   false); //55
        registerOrModifyProperty(SELECT_SORTING,              messages.selectSorting(),           true, "selectSorting",         i++, false,  false); //56
        registerOrModifyProperty(DEFAULT_VALUE_LABEL,         cmessages.defaultValue(),           false, "defaultValueLabel",     i++, true,   true); //57
        registerOrModifyProperty(DEFAULT_BY_SCRIPT,           messages.defaultByScript(),         false, "defaultByScript",       i++, false,  false); //58
        registerOrModifyProperty(DEFAULT_VALUE,               cmessages.value(),                  false, "defaultValue",          i++, true,   true); //59
        registerOrModifyProperty(SCRIPT_FOR_DEFAULT,          messages.script(),                  true, "scriptForDefault",      i++, false,  false); //60
        registerOrModifyProperty(COMPLEX_RELATION,            messages.complexRelation(),         false, "complexRelation",       i++, true,   true); //61
        registerOrModifyProperty(COMPLEX_RELATION_ATTR_GROUP, messages.complexAttrGroup(),        true, "complexAttrGroup",      i++, true,   true); //62
        registerOrModifyProperty(COMPLEX_EMPLOYEE_ATTR_GROUP, messages.complexEmplAttrGroup(),    false, "complex-employee-attrGroup", i++, true,   true); //63
        registerOrModifyProperty(COMPLEX_TEAM_ATTR_GROUP,     messages.complexTeamAttrGroup(),    false, "complex-team-attrGroup",     i++, true,   true); //64
        registerOrModifyProperty(COMPLEX_OU_ATTR_GROUP,       messages.complexOuAttrGroup(),      false, "complex-ou-attrGroup",       i++, true,   true); //65
        registerOrModifyProperty(COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW, messages.structuredObjectsView(),true, "complexStructuredObjectsView", i++,true,true); //66
        registerOrModifyProperty(EDIT_ON_COMPLEX_FORM_ONLY,   messages.editOnComplexFormOnly(),   false, "editOnComplexFormOnly", i++, true,   false); //67
        registerOrModifyProperty(QUICK_ADD_FORM_CODE,         messages.quickAddForm(),            false, "quickAddForm",          i++, true,   false); //68
        registerOrModifyProperty(QUICK_EDIT_FORM_CODE,        messages.quickEditForm(),           false, "quickEditForm",         i++, true,   false); //69
        registerOrModifyProperty(TAGS,                        tagsMessages.tags(),                false, "tags",i++, true,   false); //70
        registerOrModifyProperty(SETTINGS_SET,                adminDialogMessages.settingsSet(),  false, "settingsSet",           i, true,   false); //71
        //@formatter:on
    }
}