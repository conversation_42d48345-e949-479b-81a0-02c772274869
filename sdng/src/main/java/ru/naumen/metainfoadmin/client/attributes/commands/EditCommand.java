package ru.naumen.metainfoadmin.client.attributes.commands;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributePresenterFactory;

/**
 * Команда редактирования атрибута
 *
 * <AUTHOR>
 */
public class EditCommand extends PresenterCommandImpl<Attribute, Void, MetaClass>
{
    @Inject
    AttributePresenterFactory<ObjectFormEdit> presenterFactory;

    private final Context context;

    @Inject
    public EditCommand(@Assisted AttributeCommandParam param)
    {
        super(param);
        context = param.getContext();
    }

    @Override
    public void onExecute(MetaClass result, CallbackDecorator<Attribute, Void> callback)
    {
        //Ничего не делаем, обработка через через event        
    }

    @Override
    public boolean isPossible(Object input)
    {
        return ((AttributeCommandParam)param).hasPermission(input, PermissionType.EDIT);
    }

    @Override
    protected AsyncCallback<MetaClass> getCallback(Presenter p, Attribute value, OnStartCallback<Void> incomeCallback)
    {
        //Callback пустой, т.к. результат презентера сейчас обрабатывается через event
        return new BasicCallback<>();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected CallbackPresenter<Attribute, MetaClass> getPresenter(Attribute value)
    {
        return presenterFactory.create(context);
    }
}
