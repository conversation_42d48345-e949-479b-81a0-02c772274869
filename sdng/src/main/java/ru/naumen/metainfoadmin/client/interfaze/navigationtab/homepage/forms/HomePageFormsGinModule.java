/**
 *
 */
package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.add.HomePageAddFormPropertyParametersDescriptorFactoryImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageAttrChainController;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageAttrChainControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.edit.HomePageEditFormPropertyParametersDescriptorFactoryImpl;

/**
 * Модуль формы добавления и редактирования элемента домашней страницы
 * Использует {@link PropertyContainerPresenter}
 * <AUTHOR>
 * @since 10.01.2023
 */
public class HomePageFormsGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        //@formatter:off   
        install(PropertyControllerGinModule.create(HomePageDtObject.class, ObjectFormAdd.class)
                .setPropertyControllerFactory(new TypeLiteral<HomePageFormPropertyControllerFactory<ObjectFormAdd>>(){})
                .setPropertyParametersDescriptorFactory(new TypeLiteral<HomePageAddFormPropertyParametersDescriptorFactoryImpl>(){}));
        install(PropertyControllerGinModule.create(HomePageDtObject.class, ObjectFormEdit.class)
                .setPropertyControllerFactory(new TypeLiteral<HomePageFormPropertyControllerFactory<ObjectFormEdit>>(){})
                .setPropertyParametersDescriptorFactory(new TypeLiteral<HomePageEditFormPropertyParametersDescriptorFactoryImpl>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, HomePageAttrChainController.class)
                .build(new TypeLiteral<HomePageAttrChainControllerFactory>(){}));
        //@formatter:on
    }
}