package ru.naumen.metainfoadmin.client.fastlink.settings.forms;

import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsGinModule.FastLinkSettingFormPropertyCode;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 01.03.18
 *
 */
public class CodeRefreshDelegateImpl implements PropertyDelegateRefresh<String, TextBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        property.setValue((String)context.getPropertyValues().getProperty(FastLinkSettingFormPropertyCode.CODE));
        callback.onSuccess(true);
    }
}
