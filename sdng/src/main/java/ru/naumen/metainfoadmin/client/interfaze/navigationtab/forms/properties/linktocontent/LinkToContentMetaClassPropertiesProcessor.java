package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType_SnapshotObject;
import ru.naumen.metainfo.shared.elements.Attribute_SnapshotObject;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Вспомогательный класс вычисления класса списков объекта в зависимости от выбранных типа контента и настроек иерархии
 *
 * <AUTHOR>
 * @since 25.11.2020
 */
public class LinkToContentMetaClassPropertiesProcessor
{
    public static String getMetaClassString(IProperties propertyValues)
    {
        String contentTypeStr = propertyValues.getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);
        String metaClassStr;
        if (RelObjectList.class.getSimpleName().equals(contentTypeStr))
        {
            final Boolean showHierarchy = propertyValues.getProperty(MenuItemLinkToContentCode.SHOW_HIERARCHY);

            RelationsAttrTreeObject selectedAttr = (showHierarchy == null || Boolean.FALSE.equals(showHierarchy)) ?
                    propertyValues.getProperty(MenuItemLinkToContentCode.ATTR_CHAIN) :
                    propertyValues.getProperty(MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR);
            Attribute attribute = null == selectedAttr ? null : selectedAttr.getAttribute();
            if (attribute == null)
            {
                return null;
            }
            metaClassStr = attribute.getType().<ObjectAttributeType> cast().getRelatedMetaClass().getId();

        }
        else
        {
            metaClassStr = propertyValues.getProperty(MenuItemLinkToContentCode.OBJECT_CLASS);
        }
        return metaClassStr;
    }

    public static Attribute_SnapshotObject buildCurrentObjectAttribute(String title, MetaClass metaClass)
    {
        AttributeType_SnapshotObject attrType = new AttributeType_SnapshotObject();
        attrType.__init__code(ru.naumen.metainfo.shared.Constants.ObjectAttributeType.CODE);
        attrType.setProperty(Constants.ObjectAttributeType.METACLASS_FQN, metaClass.getFqn().toString());
        Attribute_SnapshotObject attr = new Attribute_SnapshotObject();
        attr.__init__title(title);
        attr.__init__metaClassLite(metaClass);
        attr.__init__type(attrType);
        attr.__init__computable(false);
        attr.__init__code(Constants.CURRENT_OBJECT);
        attrType.__init__attribute(attr);
        return attr;
    }

    /**
     * Проверяет видимость блока настройки списка связанных с использованием иерархии
     *
     * @param context - контекст формы
     * @return - true, если блок видим, false в противном случае
     */
    public static boolean getRelObjectListFragmentVisibility(PropertyContainerContext context)
    {
        if (!isLinkToContentElementType(context))
        {
            return false;
        }
        String contentTypeStr = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);
        return RelObjectList.class.getSimpleName().equals(contentTypeStr);
    }

    /**
     * Проверяет видимость блока настройки элемента типа "Ссылка на контент"
     *
     * @param context - контекст формы
     * @return - true, если блок видим, false в противном случае
     */
    public static boolean isLinkToContentElementType(PropertyContainerContext context)
    {
        String typeStr = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE);
        return typeStr != null && IMenuItem.MenuItemType.linkToContent.equals(IMenuItem.MenuItemType.valueOf(typeStr));
    }

    public static boolean isLinkToListElementType(PropertyContainerContext context)
    {
        String contentType = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);
        boolean isList = ObjectList.class.getSimpleName().equals(contentType)
                         || ChildObjectList.class.getSimpleName().equals(contentType)
                         || RelObjectList.class.getSimpleName().equals(contentType);
        return isList && isLinkToContentElementType(context);
    }
}
