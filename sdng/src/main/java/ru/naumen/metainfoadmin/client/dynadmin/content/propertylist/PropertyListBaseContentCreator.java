package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist;

import java.util.ArrayList;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.collect.Lists;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.ui.PropertyListBase;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.SimpleContentCreatorBase;

/**
 * Базовая реализация {@link ContentCreator} для {@link PropertyListBase}
 *
 * <AUTHOR>
 *
 */
public class PropertyListBaseContentCreator<T extends PropertyListBase> extends SimpleContentCreatorBase<T>
{
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> attributeGroup;
    @Named(PropertiesGinModule.CHECK_BOX)
    @Inject
    private Property<Boolean> showAttrDescription;

    @Override
    protected void bindPropertiesInner()
    {
        super.bindPropertiesInner();

        ensureDebugId();
        addWithMarker(cmessages.attributeGroup(), attributeGroup);
        addWithMarker(cmessages.showAttrDescription(), showAttrDescription);

        ArrayList<AttributeGroup> groups = Lists.newArrayList(context.getMetainfo().getAttributeGroups());
        metainfoUtils.sort(groups);
        for (AttributeGroup grp : groups)
        {
            attributeGroup.<SingleSelectCellList<?>> getValueWidget().addItem(grp.getTitle(), grp.getCode());
        }
    }

    @Override
    protected T getContentInner()
    {
        T content = contentProvider.get();
        content.setAttributeGroup(
                context.getMetainfo().getAttributeGroup(SelectListPropertyValueExtractor.getValue(attributeGroup))
                        .getCode());
        content.setShowAttrDescription(showAttrDescription.getValue());
        return content;
    }

    private void ensureDebugId()
    {
        DebugIdBuilder.ensureDebugId(attributeGroup, "attributeGroup");
        DebugIdBuilder.ensureDebugId(showAttrDescription, "showAttrDescription");
    }
}
