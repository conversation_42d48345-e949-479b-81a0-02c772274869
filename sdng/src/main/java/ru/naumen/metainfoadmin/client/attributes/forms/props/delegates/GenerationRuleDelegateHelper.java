package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import static ru.naumen.core.shared.Constants.AbstractCasedBO.NUMBER;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.Event;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.Constants.GeoHistoryRecord;
import ru.naumen.core.shared.Constants.Mail;
import ru.naumen.core.shared.Constants.MailLogRecord;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 *
 *
 * <AUTHOR>
 * @since 14.02.2014
 */
public final class GenerationRuleDelegateHelper
{
    public static boolean hideDependenceField(PropertyContainerContext context)
    {
        Boolean useGenerationRule = context.getPropertyValues().getProperty(AttributeFormPropertyCode.USE_GEN_RULE);
        if (useGenerationRule != null)
        {
            return useGenerationRule;
        }
        return false;
    }

    public static boolean isAlwaysGenaratedAttribute(Attribute attr)
    {
        String attributeCode = attr.getCode();
        return ServiceCall.FQN.isSameClass(attr.getMetaClassLite().getFqn()) && NUMBER.equals(attributeCode);
    }

    public static boolean isSuitableAttribute(ClassFqn fqn, String attrCode)
    {
        if (AbstractBO.UUID.equals(attrCode))
        {
            return false;
        }

        if (ServiceCall.FQN.isSameClass(fqn) && (ServiceCall.WF_PROFILE_CODE.equals(attrCode)
                                                 || Constants.Association.CLIENT_LINK_NAME.equals(attrCode)))
        {
            return false;
        }

        if (Employee.FQN.isSameClass(fqn)
            && (Employee.PASSWORD.equals(attrCode) || Employee.PHONES_INDEX.equals(attrCode)))
        {
            return false;
        }

        if (Event.FQN.isSameClass(fqn))
        {
            return false;
        }
        if (File.FQN.equals(fqn))
        {
            return false;
        }
        if (Comment.FQN.equals(fqn))
        {
            return false;
        }
        if (Mail.FQN.isSameClass(fqn) && !Mail.NUMBER.equals(attrCode))
        {
            return false;
        }
        if (MailLogRecord.FQN.isSameClass(fqn) && !MailLogRecord.NUMBER.equals(attrCode)
            && !AbstractBO.TITLE.equals(attrCode))
        {
            return false;
        }

        if (SuperUser.FQN.isSameClass(fqn) && (SuperUser.LOGIN.equals(attrCode) || SuperUser.PASSWORD.equals(attrCode)))
        {
            return false;
        }

        return true;
    }

    /**
     * Нужно ли сделать нередактируемым свойство на форме редактирования атрибутов
     * На данный момент ограничения применяются к служебным классам, перечисленным ниже.
     * @param attr атрибут, форма редактирования для которого открыта
     * @return результат проверки: нужно ли сделать свойство нередактируемым
     */
    public static boolean shouldBeDisabled(Attribute attr)
    {
        return MailLogRecord.FQN.isSameClass(attr.getMetaClassLite().getFqn())
               || Mail.FQN.isSameClass(attr.getMetaClassLite().getFqn())
               || GeoHistoryRecord.FQN.equals(attr.getMetaClassLite().getFqn());
    }
}
