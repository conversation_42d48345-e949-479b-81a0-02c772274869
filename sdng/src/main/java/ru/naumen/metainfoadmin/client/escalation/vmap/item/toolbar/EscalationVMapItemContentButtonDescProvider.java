/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.toolbar;

import java.util.ArrayList;

import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonDescription;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.toolbar.VMapItemContentButtonDescProvider;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.EscalationVMapItemRowCommandCode;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.EscalationValueMapItemContext;

/**
 * <AUTHOR>
 * @since 02.11.2012
 *
 */
public class EscalationVMapItemContentButtonDescProvider extends
        VMapItemContentButtonDescProvider<EscalationValueMapItemContext>
{
    @Override
    public ArrayList<ButtonDescription> get()
    {
        ArrayList<ButtonDescription> result = new ArrayList<>();
        result.add(new ButtonDescription(ButtonCode.ADD, messages.addRow(),
                EscalationVMapItemRowCommandCode.ADD));
        return result;
    }
}