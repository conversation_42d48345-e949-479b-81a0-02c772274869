package ru.naumen.metainfoadmin.client.jmsqueue.card;

import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.JMSQueue.PUB_SUB_DOMAIN;

import java.util.Date;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.jmsqueue.JMSQueueMessages;
import ru.naumen.metainfoadmin.client.jmsqueue.comands.JMSQueueCommandCode;
import ru.naumen.metainfoadmin.client.jmsqueue.service.JMSQueueServiceAsync;

/**
 * Презентер для блока "Количество сообщений в очереди"
 * <AUTHOR>
 * @since 26.04.2021
 **/
public class JMSQueueCountMessagesPresenter extends BasicPresenter<InfoDisplay>
{
    private final ToolBarDisplayMediator<DtObject> toolBar;
    private final BasicCallback<SimpleResult<Integer>> refreshCallback;

    @Inject
    private JMSQueueServiceAsync jmsQueueServiceAsync;
    @Inject
    private JMSQueueMessages jmsQueueMessages;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private Formatters formatters;

    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> countMessages;
    @Inject
    private SharedSettingsClientService sharedSettingsService;

    private DtObject jmsQueue;

    @Inject
    public JMSQueueCountMessagesPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        refreshCallback = new BasicCallback<SimpleResult<Integer>>()
        {
            @Override
            public void handleSuccess(final SimpleResult<Integer> result)
            {
                countMessages.setValue(result.get() + " (" + formatters.formatDateTime(new Date()) + ")");
            }
        };
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    public void init(DtObject jmsQueue)
    {
        this.jmsQueue = jmsQueue;
        if (!sharedSettingsService.isPubSubDomainEnabled()
            || !Boolean.parseBoolean(jmsQueue.getProperty(PUB_SUB_DOMAIN)))
        {
            jmsQueueServiceAsync.getCountMessage(jmsQueue.getUUID(), refreshCallback);
        }
        toolBar.refresh(jmsQueue);
    }

    @Override
    protected void onBind()
    {
        DebugIdBuilder.ensureDebugId(countMessages, "currentCountMessages");
        getDisplay().setCaption(jmsQueueMessages.countMessagesBlockCaption());
        countMessages.setCaption(jmsQueueMessages.atThisMoment());
        getDisplay().add(countMessages);
        bindToolBar();
    }

    @SuppressWarnings("unchecked")
    private void bindToolBar()
    {
        toolBar.add((ButtonPresenter<DtObject>)buttonFactory.create(ButtonCode.REFRESH, jmsQueueMessages.count(),
                JMSQueueCommandCode.GET_COUNT_MESSAGES,
                new CommandParam<DtObject, SimpleResult<Integer>>(jmsQueue, refreshCallback)));
        toolBar.bind();
    }
}