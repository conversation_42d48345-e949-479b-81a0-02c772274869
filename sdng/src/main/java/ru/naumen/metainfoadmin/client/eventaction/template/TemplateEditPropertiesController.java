package ru.naumen.metainfoadmin.client.eventaction.template;

import java.util.List;

import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.HasUnbind;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.eventaction.EventActionWithTemplate;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;

/**
 * Контроллер свойств для выбора шаблона.
 * Добавляет возможность предварительного просмотра текста на форме редактирования действия по событию. 
 * <AUTHOR>
 * @since Feb 7, 2017
 */
public class TemplateEditPropertiesController implements HasUnbind
{
    @Inject
    private TogglePreviewPropertyDecorator previewProperty;
    @Inject
    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    private SelectListProperty<String, SelectItem> templateProperty;
    @Inject
    private PreviewTemplateLoader templateLoader;
    @Inject
    private EventActionMessages messages;
    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    private HandlerRegistration stateChangeHandlerRegistration = null;

    public void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(templateProperty, "template");
    }

    public Property<String> getMessageProperty()
    {
        return previewProperty;
    }

    public void getPropertiesValues(EventActionWithTemplate action)
    {
        action.setMessageTemplate(SelectListPropertyValueExtractor.getValue(templateProperty));
    }

    public Property<SelectItem> getTemplateProperty()
    {
        return templateProperty;
    }

    public void initProperties(final EventActionFormDisplay display)
    {
        templateProperty.<SingleSelectCellList<String>> getValueWidget().customizeEmptyOption(messages.noTemplate());
        display.addLayerWidget(previewProperty.getPreviewPanel());

        stateChangeHandlerRegistration = previewProperty.addStateChangeHanlder(
                event -> display.enableLeftPanel(!event.isEnabled()));
    }

    public void setPropetiesValues(@Nullable EventActionWithTemplate action, ReadyState readyState)
    {
        metainfoService.getStyleTemplates(new BasicCallback<List<DtObject>>(readyState)
        {
            @Override
            protected void handleSuccess(List<DtObject> value)
            {
                SingleSelectCellList<?> widget = templateProperty.getValueWidget();
                widget.clear();
                for (DtObject item : value)
                {
                    widget.addItem(item.getTitle(), item.getUUID());
                }
                if (null == action)
                {
                    templateProperty.setValue(null, true);
                }
                else
                {
                    templateProperty.trySetObjValue(action.getMessageTemplate(), true);
                }

                templateLoader.init(previewProperty, templateProperty);
            }
        });
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        if (stateChangeHandlerRegistration != null)
        {
            stateChangeHandlerRegistration.removeHandler();
        }
        templateProperty.unbind(callback);
        previewProperty.unbind(callback);
    }
}
