package ru.naumen.metainfoadmin.client.embeddedapplications.usagelist;

import java.util.ArrayList;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.inject.Singleton;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsagePointApplication;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;
import ru.naumen.objectlist.shared.AdvListMassOperationLightActionDesc;
import ru.naumen.objectlist.shared.AdvListMassOperationLightContext;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Фабрика списка мест использования встроенного приложения
 * <AUTHOR>
 * @since 20.10.2021
 */
@Singleton
public class UsagePointsListApplicationAdvlistFactory extends AdminCustomAdvlistFactoryBase
{
    private final EmbeddedApplicationMessages messages;

    @Inject
    public UsagePointsListApplicationAdvlistFactory(EmbeddedApplicationMessages messages)
    {
        this.messages = messages;
    }

    @Override
    public ListPresenter<CustomList> create(@Nullable Context context)
    {
        ListPresenter<CustomList> list = advlistPresenterProvider.get();
        MetaClass metaClass = getMetaClassInt();
        CustomList objectList = createContent(context, metaClass);
        list.init(objectList, createUIContext(list, metaClass, context));
        return list;
    }

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        ArrayList<ExtendedListActionCellContext> actionColumns = new ArrayList<>();
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.EDIT,
                UsagePointsListApplicationCommandCode.EDIT, cmessages.edit(), null));
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.DELETE,
                UsagePointsListApplicationCommandCode.DELETE, cmessages.delete(), null));
        return actionColumns;
    }

    @Override
    protected AdvListMassOperationLightContext createAdvListMassOperationLightContext()
    {
        return new AdvListMassOperationLightContext(
                new AdvListMassOperationLightActionDesc(UsagePointsListApplicationCommandCode.DELETE,
                        cmessages.delete().toLowerCase(), null));
    }

    @Override
    protected CustomList createContent(@Nullable Context context, MetaClass metaclass)
    {
        final CustomList content = super.createContent(context, metaclass);
        content.setDefaultPageSize(20);
        return content;
    }

    @Override
    protected ToolPanel createToolPanel(CustomList content)
    {
        final ToolPanel panel = super.createToolPanel(content);
        panel.addToolBar(createAddButtonToolBar(messages.addUsagePlace()));
        return panel;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return UsagePointApplication.FQN;
    }
}
