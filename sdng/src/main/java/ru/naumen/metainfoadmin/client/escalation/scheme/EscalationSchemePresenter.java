package ru.naumen.metainfoadmin.client.escalation.scheme;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.ESCALATIONS;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.name.Named;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.ResourceCallback;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.PresenterCommandEvent;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.shared.EscalationSchemeResult;
import ru.naumen.metainfo.shared.dispatch2.GetEscalationSchemeAction;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.escalation.EscalationGinModule.EscalationPlaceTabs;
import ru.naumen.metainfoadmin.client.escalation.EscalationPlace;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemeGinjector.EscalationSchemeSubPresenterFactory;

/**
 * Презентер карточки схемы эскалации
 * <AUTHOR>
 * @since 20.08.2012
 *
 */
public class EscalationSchemePresenter extends AdminTabPresenter<EscalationSchemePlace>
{
    private final AsyncCallback<EscalationSchemeContext> BIND_CALLBACK = new BasicCallback<EscalationSchemeContext>()
    {
        @Override
        protected void handleSuccess(EscalationSchemeContext context)
        {
            getDisplay().setTitle(i18nUtil.getLocalizedTitle(context.getEscalationScheme().get()));
            prevPageLinkPresenter
                    .bind(messages.toEscalationSchemes(), new EscalationPlace(EscalationPlaceTabs.SCHEMES));

            addContent(subPresenterFactory.createAttributesPresenter(context), "attrs");
            addContent(subPresenterFactory.createLevelsPresenter(context), "levels");
        }
    };

    @Inject
    EscalationSchemeSubPresenterFactory subPresenterFactory;
    @Inject
    DispatchAsync dispatch;
    @Inject
    EscalationSchemeMessages messages;
    @Inject
    CommonMessages commonMessages;
    @Inject
    I18nUtil i18nUtil;
    @Inject
    @Named("new")
    EventBus localEventBus;

    private EscalationSchemeContext context;

    @Inject
    public EscalationSchemePresenter(AdminTabDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(EscalationSchemePlace place)
    {
        super.init(place);
        context = new EscalationSchemeContext(place.getScheme(), localEventBus, getDisplay());
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        registerHandler(context.getLocalEventBus().addHandler(PresenterCommandEvent.getType(), this));
        if (getPlace().getScheme() == null)
        {
            dispatch.execute(new GetEscalationSchemeAction(getPlace().getCode()),
                    new ResourceCallback<EscalationSchemeResult>(
                            commonMessages)
                    {
                        @Override
                        protected void handleSuccess(EscalationSchemeResult value)
                        {
                            context = new EscalationSchemeContext(value.getResult(), localEventBus, getDisplay());
                            BIND_CALLBACK.onSuccess(context);
                        }
                    });
        }
        else
        {
            BIND_CALLBACK.onSuccess(new EscalationSchemeContext(getPlace().getScheme(), localEventBus, getDisplay()));
        }
    }

    @Override
    public void refreshDisplay()
    {
        dispatch.execute(new GetEscalationSchemeAction(context.getEscalationScheme().get().getCode()),
                new BasicCallback<EscalationSchemeResult>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(EscalationSchemeResult response)
                    {
                        context.setEscalationScheme(response.getResult());
                        EscalationSchemePresenter.super.refreshDisplay();
                        getDisplay().setTitle(i18nUtil.getLocalizedTitle(context.getEscalationScheme().get()));
                    }
                });
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return ESCALATIONS;
    }
}
