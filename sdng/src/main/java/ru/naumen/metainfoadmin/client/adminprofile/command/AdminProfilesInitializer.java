package ru.naumen.metainfoadmin.client.adminprofile.command;

import java.util.Collection;

import com.google.gwt.core.client.GWT;
import com.google.inject.Provider;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import ru.naumen.admin.client.AdminMessages;
import ru.naumen.admin.client.AdminPlaceHistoryMapper;
import ru.naumen.admin.client.activities.AdminProfilesActivityCheck;
import ru.naumen.admin.client.settings.SettingsItem;
import ru.naumen.admin.client.settings.SettingsRegistry;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.ModuleHolder;
import ru.naumen.core.client.activity.ActivityFactory;
import ru.naumen.core.client.activity.AsyncActivity;
import ru.naumen.core.client.activity.DefaultActivityCheck;
import ru.naumen.core.client.activity.PlaceHistoryMapperRegistry;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.ActionHandlerProvider;
import ru.naumen.core.client.content.toolbar.ActionHandlerRegistry;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.adminprofile.AdminProfileCardPresenter;
import ru.naumen.metainfoadmin.client.adminprofile.AdminProfilePlace;
import ru.naumen.metainfoadmin.client.adminprofile.AdminProfilesPlace;
import ru.naumen.metainfoadmin.client.adminprofile.AdminProfilesPresenter;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionHandler;

/**
 * Инициализация команд, плейсов, обработчиков действий и доступности настроек в админке для профилей администрирования
 * <AUTHOR>
 * @since 17.01.2024
 */
@Singleton
public class AdminProfilesInitializer
{
    private final SettingsRegistry settingsRegistry;

    private final AdminMessages messages;
    private final SharedSettingsClientService sharedSettingsClientService;

    @Inject
    public AdminProfilesInitializer(SettingsRegistry settingsRegistry,
            AdminMessages messages,
            SharedSettingsClientService sharedSettingsClientService)
    {
        this.settingsRegistry = settingsRegistry;
        this.messages = messages;
        this.sharedSettingsClientService = sharedSettingsClientService;
    }

    @Inject
    public void initCommands(CommandFactory factory,
            CommandProvider<AddAdminProfileCommand, CommandParam<DtObject, DtObject>> addProvider,
            CommandProvider<EditAdminProfileCommand, CommandParam<DtObject, DtObject>> editProvider,
            CommandProvider<DeleteAdminProfileCommand, CommandParam<DtObject, Void>> deleteProvider,
            CommandProvider<DeleteAdminProfilesCommand, CommandParam<Collection<DtObject>, Void>> massDeleteProvider)
    {
        if (sharedSettingsClientService.isCustomAdminProfilesEnabled())
        {
            factory.register(AdminProfileCommandCode.ADD, addProvider);
            factory.register(AdminProfileCommandCode.EDIT, editProvider);
            factory.register(AdminProfileCommandCode.DELETE, deleteProvider);
            factory.register(AdminProfileCommandCode.DELETE_MASS, massDeleteProvider);
        }
    }

    /**
     * Регистрация Place-ов профилей администрирования
     */
    @Inject
    public void initPlaces(ActivityFactory factory,
            Provider<AsyncActivity<AdminProfilesPresenter, DefaultActivityCheck>> adminProfilesActivityProvider,
            Provider<AsyncActivity<AdminProfileCardPresenter, AdminProfilesActivityCheck>>
                    adminProfileCardActivityProvider,
            PlaceHistoryMapperRegistry placeHistoryMapperRegistry,
            ModuleHolder moduleHolder)
    {
        if (sharedSettingsClientService.isCustomAdminProfilesEnabled())
        {
            factory.register(AdminProfilesPlace.class, adminProfilesActivityProvider);
            factory.register(AdminProfilePlace.class, adminProfileCardActivityProvider);
            placeHistoryMapperRegistry.register(GWT.create(AdminPlaceHistoryMapper.class));

            moduleHolder.setModule(ModuleHolder.Module.Admin);
        }
    }

    /**
     * Регистрация обработчиков действий профилей администрирования
     */
    @Inject
    public void initActionHandlers(ActionHandlerRegistry registry,
            ActionHandlerProvider<AdminActionHandler<AddAdminProfileToolBarAction>> addAdminProfileActionHandlerProvider)
    {
        if (sharedSettingsClientService.isCustomAdminProfilesEnabled())
        {
            registry.register(AdminProfileCommandCode.ADD, addAdminProfileActionHandlerProvider);
        }
    }

    /**
     * Регистрация пункта меню в Настройках системы
     */
    @Inject
    public void initSettings()
    {
        settingsRegistry.addSystemSettings(
                new SettingsItem(messages.adminProfiles(),
                        messages.adminProfilesDescription(), AdminProfilesPlace.INSTANCE));
        settingsRegistry.sortSystemSettings();
    }
}
