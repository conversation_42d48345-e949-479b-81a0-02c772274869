/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.ToolPanelContentPresenterEdit;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие о необходимости открытии формы редактирования тула
 * Отдельное событие, т.к. в форму надо передавать захардкоженный тулбар из {@link ToolPanelContentPresenterEdit}
 * <AUTHOR>
 * @since 03 июня 2015 г.
 *
 */
public class ShowToolFormEvent extends GwtEvent<ShowToolFormHandler>
{
    private static final Type<ShowToolFormHandler> TYPE = new Type<ShowToolFormHandler>();

    public static Type<ShowToolFormHandler> getType()
    {
        return TYPE;
    }

    private final Tool tool;

    public ShowToolFormEvent(Tool tool)
    {
        this.tool = tool;
    }

    @Override
    public GwtEvent.Type<ShowToolFormHandler> getAssociatedType()
    {
        return TYPE;
    }

    public Tool getTool()
    {
        return tool;
    }

    @Override
    protected void dispatch(ShowToolFormHandler handler)
    {
        handler.onShowToolForm(this);
    }
}