package ru.naumen.metainfoadmin.client.jmsqueue.forms;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfoadmin.client.jmsqueue.JMSQueueMessages;
import ru.naumen.metainfoadmin.client.jmsqueue.service.JMSQueueServiceAsync;

/**
 * Форма добавления связи между ДПС и очередью
 * <AUTHOR>
 * @since 28.04.2021
 **/
public class AddLinkJMSQueueFormPresenter extends OkCancelPresenter<PropertyDialogDisplay> implements CallbackPresenter<DtObject, Void>
{
    @Named(PropertiesGinModule.MULTI_SELECT_BOX)
    @Inject
    private SelectListProperty<Collection<String>, Collection<SelectItem>> eventActions;

    @Inject
    protected Processor validation;
    @Inject
    private JMSQueueMessages messages;
    @Inject
    private JMSQueueServiceAsync serviceAsync;
    @Inject
    private NotEmptyCollectionValidator<Collection<SelectItem>> notEmptyCollectionValidator;

    private DtObject jmsQueue;
    private AsyncCallback<Void> backCallback;

    @Inject
    public AddLinkJMSQueueFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(@Nullable DtObject jmsQueue, AsyncCallback<Void> callback)
    {
        this.jmsQueue = jmsQueue;
        backCallback = callback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }

        serviceAsync.addLink(eventActions.getValue().stream().map(SelectItem::getCode).collect(Collectors.toList()),
                jmsQueue.getUUID(),
                new BasicCallback<SimpleResult<Void>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<Void> value)
                    {
                        unbind();
                        backCallback.onSuccess(null);
                    }
                });
    }

    @Override
    protected void onBind()
    {
        bindProperties();
        super.onBind();
        initEventActions();
        getDisplay().setCaptionText(messages.addingLink());
        getDisplay().setFixed(false);
        getDisplay().display();
    }

    protected void bindProperties()
    {
        eventActions.setCaption(messages.eventActions());
        eventActions.setValidationMarker(true);
        validation.validate(eventActions, notEmptyCollectionValidator);
        getDisplay().add(eventActions);
        DebugIdBuilder.ensureDebugId(eventActions, "eventActions");
    }

    public SelectListProperty<Collection<String>, Collection<SelectItem>> getEventActions()
    {
        return eventActions;
    }

    /**
     * Проинициализируем поле ДПС
     */
    private void initEventActions()
    {
        serviceAsync.getAvailableEventActionsForJMSQueue(jmsQueue.getUUID(),
                new BasicCallback<SimpleResult<List<DtObject>>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<List<DtObject>> value)
                    {
                        value.get().forEach(event ->
                        {
                            getEventActions().<MultiSelectCellList<String>> getValueWidget().addItem(event.getTitle(),
                                    event.getUUID());
                        });
                    }
                });
    }
}