package ru.naumen.metainfoadmin.client.eventaction;

import java.util.Collection;
import java.util.function.Predicate;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.common.collect.Lists;

import ru.naumen.metainfo.shared.eventaction.EventType;

/**
 * Предикат, для проверки возможности настраивать синхронность выполнения действия по событию по типу события
 *
 * <AUTHOR>
 */
@Singleton
public class PossibleTxTypePredicate implements Predicate<String>
{
    // @formatter:off
    private static final Collection<String> NOT_SUPPORTED_EVENTS = Lists.newArrayList(
            EventType.onsetTimeOfAttr.name(),
            EventType.escalation.name(),
            EventType.mail.name(),
            EventType.alertActivated.name(),
            EventType.alertDeactivated.name(),
            EventType.alertChanged.name(),
            EventType.ndapMessage.name(),
            EventType.insertMention.name(),
            EventType.arriveMessageOnQueue.name());
    // @formatter:on

    @Inject
    public PossibleTxTypePredicate()
    {
    }

    @Override
    public boolean test(@Nullable String eventName)
    {
        return !NOT_SUPPORTED_EVENTS.contains(eventName);
    }
}
