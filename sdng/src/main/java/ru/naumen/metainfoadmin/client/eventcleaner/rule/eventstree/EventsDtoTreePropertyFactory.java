package ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree;

import java.util.Collection;

import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Фабрика свойств с проинициализированным виджетом дерева
 * <AUTHOR>
 * @since 06.08.2023
 */
public interface EventsDtoTreePropertyFactory
{
    /**
     * Создает свойство, представленное деревом множественного быстрого выбора из предопределенного множества
     * элементов.
     * @param context контекст построения дерева выбора
     * @return созданное свойство с проинициализированным виджетом дерева
     */
    PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
            EventsDtoTreeSelectionModel>> create(
            EventsDtoTreeFactoryContext context);
}