package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.filters.RelationFilters;
import ru.naumen.metainfo.shared.ui.ChildObjectList;

/**
 * Делегат обновления свойства "Показывать в списке объекты, вложенные во вложенные"
 *
 * <AUTHOR>
 * @since 10.11.2020
 */
public class LinkToContentShowNestedInNestedDelegateRefreshImpl implements PropertyDelegateRefresh<Boolean,
        BooleanCheckBoxProperty>
{
    @Inject
    protected MetainfoServiceAsync metainfoService;
    @Inject
    protected MetainfoUtils metainfoUtils;

    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean isLinkToContentSelected = LinkToContentMetaClassPropertiesProcessor.isLinkToContentElementType(context);

        String contentTypeStr = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);

        if (!isLinkToContentSelected || !ChildObjectList.class.getSimpleName().equals(contentTypeStr))
        {
            property.clearValue();
            callback.onSuccess(false);
            return;
        }

        String metaClassStr = LinkToContentMetaClassPropertiesProcessor.getMetaClassString(context.getPropertyValues());
        ClassFqn metaclass = StringUtilities.isNotEmpty(metaClassStr) ? ClassFqn.parse(metaClassStr) : null;

        if (metaclass == null)
        {
            property.clearValue();
            callback.onSuccess(false);
            return;
        }

        metainfoService.getMetaClass(metaclass, new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass value)
            {
                callback.onSuccess(value.getOutgoingRelation()
                                           .stream()
                                           .anyMatch(RelationFilters.isSelf().and(RelationFilters.isParent()))
                                   || Root.FQN.equals(value.getFqn()));

            }
        });
        callback.onSuccess(true);

    }
}
