package ru.naumen.metainfoadmin.client.eventaction.form;

import static java.util.Comparator.comparing;
import static java.util.function.Predicate.isEqual;
import static ru.naumen.core.client.widgets.properties.PropertyUtils.addDescriptionIconWithHint;
import static ru.naumen.core.client.widgets.properties.PropertyUtils.editDescriptionIconHint;
import static ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormUtils.isEventActionWithAttributes;
import static ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormUtils.isEventActionWithContextAttributes;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.view.client.MultiSelectionModel;
import com.google.inject.Provider;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.admin.client.permission.AdminPermissionCheckServiceSync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.EnumUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithoutFolders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactory;
import ru.naumen.core.client.validation.HasValidation;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.OnGetValueThrowValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants.NDAPConstants;
import ru.naumen.core.shared.Constants.ScriptsComponentTree;
import ru.naumen.core.shared.FqnExtractorFunction;
import ru.naumen.core.shared.HasReadyState;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.HasReadyState.SynchronizationCallback;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.client.eventaction.EventActionConstants;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.client.eventaction.EventActionsPresenterSettings;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.eventaction.EventAttributesTreeType;
import ru.naumen.metainfo.shared.dispatch2.eventaction.GetEventAttributesTreeAction;
import ru.naumen.metainfo.shared.dispatch2.eventaction.GetEventAttributesTreeResponse;
import ru.naumen.metainfo.shared.dispatch2.script.GetScriptDtoAction;
import ru.naumen.metainfo.shared.elements.HasFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.Event;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventAction.TxType;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.FakeAction;
import ru.naumen.metainfo.shared.eventaction.NotificationEventAction;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.eventaction.EventActionFactory;
import ru.naumen.metainfoadmin.client.eventaction.EventActionFactory.EventCreator;
import ru.naumen.metainfoadmin.client.eventaction.EventActionPlace;
import ru.naumen.metainfoadmin.client.eventaction.ModuleSpecificEventTypePredicate;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormGinModule.EventAttributesTree;
import ru.naumen.metainfoadmin.client.eventaction.form.attrtree.EventAttributesTreeFactoryContext;
import ru.naumen.metainfoadmin.client.eventaction.form.creator.EventActionFormPropertiesCreator;
import ru.naumen.metainfoadmin.client.eventaction.form.creator.event.EventFormPropertiesCreator;
import ru.naumen.metainfoadmin.client.jmsqueue.service.JMSQueueServiceAsync;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.metainfoadmin.client.tags.property.TagsPropertyFactory;

/**
 * <AUTHOR>
 * @since 02.12.2011
 */
public abstract class AbstractEventActionFormPresenter extends OkCancelPresenter<EventActionFormDisplay>
        implements CallbackPresenter<EventActionWithScript, EventActionWithScript>
{
    class EventValidator extends OnGetValueThrowValidator<Collection<DtObject>>
    {
        private EventType eventType;

        public EventType getEventType()
        {
            return eventType;
        }

        public void setEventType(EventType eventType)
        {
            this.eventType = eventType;
        }

        @Override
        public boolean validate(HasValueOrThrow<Collection<DtObject>> hasValue)
        {
            if (eventType != null && eventType.equals(EventType.onsetTimeOfAttr))
            {
                Collection<DtObject> values = hasValue.getValue();
                if (values == null)
                {
                    return true;
                }
                Collection<ClassFqn> fqns = Collections2.transform(values, FqnExtractorFunction.FQN_EXTRACTOR);
                HashSet<String> ids = Sets.newHashSet(Collections2.transform(fqns, ClassFqn.ID_EXTRACTOR));
                if (ids.size() > 1 && hasValue instanceof HasValidation)
                {
                    ((HasValidation)hasValue).addValidationMessage(getErrorMessage());
                    return false;
                }
            }
            return true;
        }

        @Override
        protected String getErrorMessage()
        {
            return messages.errorOnlyOneType();
        }
    }

    protected static boolean containsFileClass(@Nullable List<MetaClassLite> metaClasses)
    {
        return null != metaClasses && metaClasses.stream().map(HasFqn::getFqn).anyMatch(isEqual(
                ru.naumen.core.shared.Constants.File.FQN));
    }

    private static final int START_INDEX = 8;
    @Inject
    private ModuleSpecificEventTypePredicate eventTypeByModulePredicate;
    @Inject
    protected Processor validation;
    @Inject
    private MetainfoServiceAsync metainfoService;
    @Inject
    protected PlaceController placeController;
    @Inject
    private EventActionFactory factory;
    @Inject
    private EventActionMessages messages;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private TagsMessages tagsMessages;
    @Inject
    private EventActionsPresenterSettings settings;
    @Inject
    protected EventActionConstants eventActionConstants;
    @Inject
    private MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    private JMSQueueServiceAsync jmsQueueService;
    @Inject
    private I18nUtil i18nUtil;
    @Inject
    protected AvailableActionsProvider availableActionsProvider;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT_AREA)
    private Property<String> description;
    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    @Inject
    private SelectListProperty<String, SelectItem> event;
    private HasProperties.PropertyRegistration<SelectItem> eventPR;

    @Inject
    private TagsPropertyFactory tagsPropertyFactory;
    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    @Inject
    private SelectListProperty<String, SelectItem> action;
    private HasProperties.PropertyRegistration<SelectItem> actionPR;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> jmsQueue;
    @Inject
    private Provider<NotNullValidator<SelectItem>> notNullValidatorProvider;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    private Provider<NotEmptyCollectionValidator<Collection<DtObject>>> notEmptyCollectionValidatorProvider;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private EventActionFormUtils eventActionFormUtils;
    @Inject
    private AdminPermissionCheckServiceSync adminPermissionCheckServiceSync;

    private EventActionWithScript eventAction;
    private List<MetaClassLite> metaClasses;

    private final Predicate<EventType> escalationEventPredicate = (type) ->
    {
        if (this.eventAction == null)
        {
            return true;
        }
        if (EventType.escalation.equals(this.eventAction.getObject().getEvent()
                .getEventType()))
        {
            return EventType.escalation.equals(type);
        }
        else
        {
            return !EventType.escalation.equals(type);
        }
    };

    private Predicate<EventType> arrivedMessageToQueueEventPredicate(@Nullable List<MetaClassLite> metaClasses,
            @Nullable Set<ClassFqn> selectedFqns)
    {
        return type -> !type.equals(EventType.arriveMessageOnQueue)
                       || (possibleArrivedEvent
                           && CollectionUtils.isEmpty(metaClasses)
                           && CollectionUtils.isEmpty(selectedFqns))
                       || (eventAction != null && eventAction.getObject()
                .getEvent().getEventType().equals(EventType.arriveMessageOnQueue));
    }

    private static Predicate<EventType> ndapMessageEventPredicate(@Nullable Set<ClassFqn> selectedFqns)
    {
        return type -> type != EventType.ndapMessage
                       || CollectionUtils.isEmpty(selectedFqns)
                       || selectedFqns.contains(NDAPConstants.SERVER_CLASS_FQN);
    }

    private TagsProperty tags;
    private Property<SelectItem> settingsSet;
    private EventActionFormPropertiesCreator creator;

    private final SettingsSetOnFormCreator settingsSetOnFormCreator;
    private final SelectFqnsPropertyFactory treeFactory;
    private Property<Collection<DtObject>> fqn;
    private AsyncCallback<EventActionWithScript> saveCallback;
    private ReadyState rs;
    private boolean possibleArrivedEvent;

    private final DtoTreeFactory<Collection<DtObject>, EventAttributesTree, WithoutFolders,
            EventAttributesTreeFactoryContext> eventAttrTreeFactory;

    private EventAttributesTreeFactoryContext attrTreeContext;
    private Property<Collection<DtObject>> attrTree;
    private HasProperties.PropertyRegistration<Collection<DtObject>> attrTreePR;

    private EventAttributesTreeFactoryContext contextAttrTreeContext;
    private Property<Collection<DtObject>> contextAttrTree;
    private HasProperties.PropertyRegistration<Collection<DtObject>> contextAttrTreePR;

    @SuppressWarnings("rawtypes")
    @CheckForNull
    private EventFormPropertiesCreator eventFormCreator;

    private EventValidator fqnValidator;

    protected AbstractEventActionFormPresenter(SelectFqnsPropertyFactory treeProvider, EventActionFormDisplay display,
            EventBus eventBus,
            SettingsSetOnFormCreator settingsSetOnFormCreator,
            DtoTreeFactory<Collection<DtObject>, EventAttributesTree, WithoutFolders,
                    EventAttributesTreeFactoryContext> attrTreeFactory)
    {
        super(display, eventBus);
        this.treeFactory = treeProvider;
        this.settingsSetOnFormCreator = settingsSetOnFormCreator;
        this.eventAttrTreeFactory = attrTreeFactory;
    }

    @Override
    public void init(@Nullable EventActionWithScript eventAction, AsyncCallback<EventActionWithScript> saveCallback)
    {
        this.eventAction = eventAction;
        this.saveCallback = saveCallback;
        this.rs = new ReadyState(this);
        rs.registerSynchronization(new SynchronizationCallback(this)
        {
            @Override
            public void error()
            {
                display.stopProcessing();
            }

            @Override
            public void notReady()
            {
                display.startProcessing();
            }

            @Override
            public void ready()
            {
                display.stopProcessing();
            }
        });
    }

    /**
     * Метод выполняется в самом начале инициализации презентера
     * Узнаем, есть ли пользовательские очереди без ДПС с типом события "Поступление сообщения в очередь"
     */
    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        super.loadData(readyState);
        readyState.notReady();
        jmsQueueService.possibleArrivedMessageToQueueEvent(new BasicCallback<SimpleResult<Boolean>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<Boolean> value)
            {
                possibleArrivedEvent = Boolean.TRUE.equals(value.get());
                readyState.ready();
            }
        });
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        tags = tagsPropertyFactory.createProperty(new BasicCallback<TagsProperty>(getDisplay())
        {
            @Override
            protected void handleSuccess(TagsProperty value)
            {
                if (eventAction != null)
                {
                    tags.trySetObjValue(eventAction.getObject().getTags());
                }
            }
        });

        int leftSidePropertyCount = settingsSetOnFormCreator.isDisplayedOnForms()
                ? EventActionFormDisplay.LEFT_SIDE_PROPERTIES_COUNT + 1
                : EventActionFormDisplay.LEFT_SIDE_PROPERTIES_COUNT;
        getDisplay().setLeftSideProperiesCount(leftSidePropertyCount);
        getDisplay().asWidget().addStyleName(WidgetResources.INSTANCE.form().eventActionForm());

        setPropertiesCaptions();
        initValidation();
        initEvents(null);
        initJMSQueues(isNewEventAction()
                      || getEventAction().getObject().getTxType() == TxType.NEW_TX);
        bindPropertiesAndShow();
        registerHandlers();
        ensureDebugIds();
    }

    /**
     * Добавление заголовков к полям на форме
     */
    private void setPropertiesCaptions()
    {
        action.setCaption(messages.action());
        event.setCaption(messages.event());
        title.setCaption(cmessages.title());
        code.setCaption(cmessages.code());
        description.setCaption(cmessages.description());
        tags.setCaption(tagsMessages.tags());
        jmsQueue.setCaption(messages.jmsQueue());
    }

    protected void initValidation()
    {
        action.setValidationMarker(true);
        event.setValidationMarker(true);
        title.setValidationMarker(true);
        title.setMaxLength(Constants.MAX_METAINFO_TITLE_LENGTH);

        validation.validate(code, notEmptyValidator);
        validation.validate(title, notEmptyValidator);
        validation.validate(event, notNullValidatorProvider.get());
        validation.validate(action, notNullValidatorProvider.get());
    }

    /**
     * Размещение и инициализации полей на форме
     */
    protected void bindPropertiesAndShow()
    {
        addPropertyToDisplay(new PropertiesBlockProperty("1. " + messages.commonProperties()));
        addPropertyToDisplay(title);
        addPropertyToDisplay(code);
        addPropertyToDisplay(description);
        fqn = treeFactory.create(eventAction == null ? null : eventAction.getObject());
        addPropertyToDisplay(fqn);
        addPropertyToDisplay(tags);
        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getSettingsSetInitialValue());
        if (settingsSet != null)
        {
            addPropertyToDisplay(settingsSet);
        }
        addPropertyToDisplay(new PropertiesBlockProperty("2. " + messages.event()));
        eventPR = addPropertyToDisplay(event);

        attrTreeContext = new EventAttributesTreeFactoryContext();
        eventAttrTreeFactory.createTree(attrTreeContext, new BasicCallback<HasValueOrThrow<Collection<DtObject>>>()
        {
            @Override
            protected void handleSuccess(HasValueOrThrow<Collection<DtObject>> value)
            {
                EventType eventType = getEventType();
                String caption = eventActionFormUtils.getEvenAttrsCaption(eventType);
                String description = eventActionFormUtils.getEventAttrsDescription(eventType);
                attrTree = new PropertyBase(caption, value);
                addDescriptionIconWithHint(description, attrTree, false);
            }
        });

        addPropertyToDisplay(new PropertiesBlockProperty("3. " + messages.action()));
        actionPR = addPropertyToDisplay(action);

        contextAttrTreeContext = new EventAttributesTreeFactoryContext();
        eventAttrTreeFactory.createTree(contextAttrTreeContext,
                new BasicCallback<HasValueOrThrow<Collection<DtObject>>>()
                {
                    @Override
                    protected void handleSuccess(HasValueOrThrow<Collection<DtObject>> value)
                    {
                        contextAttrTree = new PropertyBase(messages.attributesTransferredToContext(), value);
                        addDescriptionIconWithHint(messages.attributesTransferredToContextDescription(),
                                contextAttrTree, false);
                    }
                });

        addPropertyToDisplay(jmsQueue);

        afterFqnInitialized();
        initPropertiesValues(eventAction, new BasicCallback<Void>(getDisplay())
        {
            @Override
            protected void handleSuccess(Void value)
            {
                initFormPropertiesCreator(
                        eventAction == null ? null : eventAction.getObject().getAction().getActionType());
                initFormPropertiesCreator(
                        eventAction == null ? null : eventAction.getObject().getEvent().getEventType());
                if (eventAction != null)
                {
                    fqnValidator.setEventType(eventAction.getObject().getEvent().getEventType());
                }
                getDisplay().setFixed(false);
                getDisplay().display();
            }
        });
    }

    protected String getSettingsSetInitialValue()
    {
        return null;
    }

    protected <T> HasProperties.PropertyRegistration<T> addPropertyToDisplay(
            @Nullable HasProperties.Property<T> property)
    {
        PropertyRegistration<T> propertyRegistration = getDisplay().add(property);
        registrationContainer.register(propertyRegistration);
        return propertyRegistration;
    }

    private void refreshAttributeProperties()
    {
        if (attrTree != null)
        {
            attrTreePR = refreshAttributePropertyVisibility(attrTreePR, attrTree, eventPR,
                    isEventActionWithAttributes(getEventType()));
            refreshAttributeProperty(attrTree, attrTreeContext, EventAttributesTreeType.OBJECT_ATTRIBUTES);

            EventType eventType = getEventType();
            String eventAttrsCaption = eventActionFormUtils.getEvenAttrsCaption(eventType);
            attrTree.setCaption(eventAttrsCaption);

            String eventAttrsDescription = eventActionFormUtils.getEventAttrsDescription(eventType);
            editDescriptionIconHint(eventAttrsDescription, attrTree);
        }

        if (contextAttrTree != null)
        {
            contextAttrTreePR = refreshAttributePropertyVisibility(contextAttrTreePR, contextAttrTree, actionPR,
                    isEventActionWithContextAttributes(getEventType(),
                            creator != null && !creator.isTxNewEventAction()));

            refreshAttributeProperty(contextAttrTree, contextAttrTreeContext,
                    EventAttributesTreeType.CONTEXT_ATTRIBUTES);
        }
    }

    @Nullable
    private EventType getEventType()
    {
        String eventValue = SelectItemValueExtractor.extract(event.getValue());
        return eventValue != null ? EventType.valueOf(eventValue) : null;
    }

    private HasProperties.PropertyRegistration<Collection<DtObject>> refreshAttributePropertyVisibility(
            HasProperties.PropertyRegistration<Collection<DtObject>> attrTreePR,
            Property<Collection<DtObject>> attrTree, HasProperties.PropertyRegistration<?> previousPR,
            boolean isVisible)
    {
        int leftSidePropertiesCount = display.getLeftSideProperiesCount();
        if (isVisible)
        {
            if (attrTreePR == null)
            {
                display.setLeftSideProperiesCount(leftSidePropertiesCount + 1);
                PropertyRegistration<Collection<DtObject>> propertyRegistration = getDisplay().addPropertyAfter(
                        attrTree, previousPR);
                registrationContainer.register(propertyRegistration);
                return propertyRegistration;
            }
            return attrTreePR;
        }

        if (attrTreePR != null)
        {
            display.setLeftSideProperiesCount(leftSidePropertiesCount - 1);
            attrTreePR.unregister();
        }
        return null;
    }

    private void refreshAttributeProperty(Property<Collection<DtObject>> property,
            EventAttributesTreeFactoryContext context, EventAttributesTreeType treeType)
    {
        EventType eventType = getEventType();
        List<ClassFqn> eventFqns = getFqns();

        GetEventAttributesTreeAction action = new GetEventAttributesTreeAction(eventFqns, eventType, treeType);
        dispatch.execute(action, new BasicCallback<GetEventAttributesTreeResponse>()
        {
            @Override
            protected void handleSuccess(GetEventAttributesTreeResponse response)
            {
                if (!CollectionUtils.isEqualCollections(response.getEventFqns(), getFqns())
                    || !Objects.equals(response.getEventType(), getEventType()))
                {
                    return;
                }

                Set<DtObject> oldValue = new HashSet<>(property.getValue());
                Map<DtObject, List<DtObject>> hierarchy = response.getHierarchy();
                Set<DtObject> newValue = hierarchy.values().stream()
                        .flatMap(Collection::stream)
                        .filter(oldValue::contains)
                        .collect(Collectors.toSet());

                PopupValueCellTree tree = property.getValueWidget();
                context.setHierarchy(hierarchy);
                tree.setRootOpen(false);
                tree.setRootOpen(true);

                property.setValue(newValue);
            }
        });
    }

    private void setPropertyVisible(Property<?> property, boolean isVisible)
    {
        property.getCaptionWidget().asWidget().setVisible(isVisible);
        property.asWidget().setVisible(isVisible);
    }

    /**
     * Регистрация обработчиков на изменение данных полей
     */
    protected void registerHandlers()
    {
        registerHandler(action.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                initFormPropertiesCreator(getActionType());
                initJMSQueues(true);
                AbstractEventActionFormPresenter.this.getDisplay().ready(new HasReadyState.ReadyCallback(this)
                {
                    @Override
                    public void onReady()
                    {
                        eventBus.fireEvent(new UpdateTabOrderEvent(false));
                        TabOrderHelper.setFocusDeffered(action.getValueWidget(), true);
                    }
                });

            }
        }));

        registerHandler(event.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                EventType eventValue;
                try
                {
                    eventValue = EventType.valueOf(SelectItemValueExtractor.extract(event.getValue()));
                    fqnValidator.setEventType(eventValue);
                }
                catch (Exception e)
                {
                    eventValue = null;
                }
                onChangedArrivedEvent(EventType.arriveMessageOnQueue.equals(eventValue));
                initJMSQueues(creator == null || creator.isTxNewEventAction());

                Set<ActionType> unavailableTypes = availableActionsProvider.getUnavailableActions(eventValue);
                SingleSelectCellList<?> list = getAction().getValueWidget();
                list.clear();
                initActionTypes(unavailableTypes);
                if (unavailableTypes.contains(getActionType()))
                {
                    action.trySetObjValue(null, true);
                }

                initFormPropertiesCreator(eventValue);
                creator.refreshProperties(getDisplay(), getFqns(), SelectItemValueExtractor.extract(event
                        .getValue()));
                AbstractEventActionFormPresenter.this.getDisplay().ready(new HasReadyState.ReadyCallback(this)
                {
                    @Override
                    public void onReady()
                    {
                        eventBus.fireEvent(new UpdateTabOrderEvent(false));
                        TabOrderHelper.setFocusDeffered(AbstractEventActionFormPresenter.this.event.getValueWidget(),
                                true);
                    }
                });

                refreshAttributeProperties();
            }
        }));

        registerHandler(fqn.addValueChangeHandler(valueChangeEvent -> refreshAttributeProperties()));
    }

    @SuppressWarnings("unchecked")
    protected void afterFqnInitialized()
    {
        fqn.setValidationMarker(true);
        validation.validate(fqn, notEmptyCollectionValidatorProvider.get());
        fqnValidator = new EventValidator();
        validation.validate(fqn, fqnValidator);

        registerHandler(((PopupValueCellTree<DtObject, Collection<DtObject>, MultiSelectionModel<DtObject>>)fqn
                .getValueWidget()).addValueChangeHandler(event ->
        {
            onMetaClassesChanged(event);
            if (null != creator)
            {
                creator.refreshProperties(getDisplay(), getFqns(), null);
            }
        }));

        registerHandler(getAction().addValueChangeHandler(event -> initEvents(
                fqn.getValue().stream().map(IHasMetaInfo::getMetaClass).collect(Collectors.toSet()))));
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            super.onApply();
            return;
        }
        eventAction = creator.getEventAction();
        if (eventAction == null)
        {
            super.onApply();
            return;
        }
        ScriptDto script = eventAction.getScript(eventAction.getObject().getAction().getScript());
        if (!isNotificationWithNotLoadedScript(script))
        {
            processSave();
            super.onApply();
            return;
        }
        dispatch.execute(new GetScriptDtoAction(script.getCode()), new BasicCallback<SimpleResult<ScriptDto>>(rs)
        {
            @Override
            public void handleSuccess(SimpleResult<ScriptDto> result)
            {
                eventAction.putScript(result.get());
                processSave();
            }
        });
        super.onApply();
    }

    private void processSave()
    {
        if (NotificationEventAction.class == eventAction.getObject().getAction().getClass()
            && isCurrentRecipientTogetherMethodCc(eventAction))
        {
            return;
        }

        if (eventFormCreator == null)
        {
            initFormPropertiesCreator(EventType.valueOf(SelectListPropertyValueExtractor.getValue(event)));
        }
        Event createdEvent = eventFormCreator != null ? eventFormCreator.getEvent() : new Event();
        if (createdEvent == null)
        {
            return;
        }
        EventType eventType = EventType.valueOf(SelectItemValueExtractor.extract(event.getValue()));
        createdEvent.setEventType(eventType);
        eventAction.getObject().setEvent(createdEvent);
        eventAction.getObject().getAction().setActionType(ActionType.valueOf(SelectListPropertyValueExtractor.getValue(
                action)));
        eventAction.getObject().setId(code.getValue());
        i18nUtil.updateI18nObjectTitle(eventAction, title.getValue());
        i18nUtil.updateI18nObjectDescription(eventAction, description.getValue());
        final List<ClassFqn> prevFqns = Lists.newArrayList(eventAction.getObject().getLinkedClasses());
        eventAction.getObject().getLinkedClasses().clear();
        eventAction.getObject().getLinkedClasses()
                .addAll(Collections2.transform(fqn.getValue(), DtObject.FQN_EXTRACTOR));
        final List<String> previousTags = new ArrayList<>(eventAction.getObject().getTags());
        eventAction.getObject().getTags().clear();
        eventAction.getObject().getTags().addAll(SelectListPropertyValueExtractor.getValue(tags));
        eventAction.getObject().setSettingsSet(SelectListPropertyValueExtractor.getValue(settingsSet));
        TxType txType = eventAction.getObject().getTxType();
        if (txType.equals(TxType.NEW_TX))
        {
            eventAction.getObject().setJmsQueue(SelectListPropertyValueExtractor.getValue(jmsQueue));
        }

        Set<AttributeFqn> attributes = isEventActionWithAttributes(eventType) ?
                getAttributes(attrTree) :
                Collections.emptySet();
        eventAction.getObject().setAttributes(attributes);

        Set<AttributeFqn> contextAttributes =
                isEventActionWithContextAttributes(eventType, txType.equals(TxType.CURRENT_TX)) ?
                        getAttributes(contextAttrTree) :
                        Collections.emptySet();
        eventAction.getObject().setContextAttributes(contextAttributes);

        metainfoModificationService.saveEventAction(eventAction, isNewEventAction(),
                tags.getPendingTags(),
                new CallbackDecorator<EventActionWithScript, EventActionWithScript>(rs, saveCallback)
                {
                    @Override
                    public void onFailure(Throwable caught)
                    {
                        eventAction.getObject().getLinkedClasses().clear();
                        eventAction.getObject().getLinkedClasses().addAll(prevFqns);
                        eventAction.getObject().getTags().clear();
                        eventAction.getObject().getTags().addAll(previousTags);
                        super.onFailure(caught);
                    }

                    @Override
                    public void onSuccess(EventActionWithScript result)
                    {
                        super.onSuccess(result);
                        if (isNewEventAction())
                        {
                            placeController.goTo(new EventActionPlace(result.getCode()));
                        }
                    }
                });
    }

    private Set<AttributeFqn> getAttributes(Property<Collection<DtObject>> property)
    {
        return property.getValue().stream()
                .map(dtObject -> AttributeFqn.parse(dtObject.getUUID()))
                .collect(Collectors.toSet());
    }

    /* --- Инициализация полей/данных --- */

    protected void initActionTypes()
    {
        initActionTypes(Collections.emptySet());
    }

    /**
     * Создание кастомизатора формы в зависимости от значения в поле "Действие"
     * @param action выбранное значение в поле "Действие"
     */
    protected void initFormPropertiesCreator(@Nullable ActionType action)
    {
        if (null != creator)
        {
            creator.removeProperties();
            creator = null;
        }
        creator = createFormCreatorByActionType(action);
        creator.setReadyState(rs);
    }

    @Override
    protected void onUnbind()
    {
        if (creator != null)
        {
            creator.unbind(new BasicCallback<>());
        }
        registrationContainer.unbindProperties();
        super.onUnbind();
    }

    /**
     * Создание кастомизатора формы в зависимости от значения в поле "События"
     * @param eventValue выбранное значение в поле "События"
     */
    protected void initFormPropertiesCreator(@Nullable EventType eventValue)
    {
        if (null != eventFormCreator)
        {
            int leftSidePropertiesCount = display.getLeftSideProperiesCount() - eventFormCreator.getPropertiesCount();
            getDisplay().setLeftSideProperiesCount(leftSidePropertiesCount);
            eventFormCreator.removeProperties();
        }
        final EventCreator<Event> eventCreator = factory.getEventCreator(eventValue);
        if (eventCreator != null)
        {
            eventFormCreator = createFormCreatorByEventType(eventCreator, eventValue);
        }
        else
        {
            eventFormCreator = null;
        }
    }

    /**
     * Инициализация поля "Событие" в зависимости от данных в поле "Объект"
     */
    private void initEvents(@Nullable Set<ClassFqn> selectedFqns)
    {
        String actionType = getAction().getValue() == null ? null : getAction().getValue().getCode();
        List<EventType> allowedEvents = getAllowedEvents(metaClasses, selectedFqns, actionType);
        allowedEvents.sort(comparing(o -> eventActionConstants.eventTypes().get(o.name())));

        String prevEvent = SelectItemValueExtractor.extract(event.getValue());
        SingleSelectCellList<?> list = event.getValueWidget();
        list.clear();
        String newEventValue = null;
        for (EventType eventValue : allowedEvents)
        {
            list.addItem(eventActionConstants.eventTypes().get(eventValue.name()), eventValue.name());
            if (prevEvent != null && prevEvent.equals(eventValue.name()))
            {
                newEventValue = prevEvent;
            }
        }

        event.trySetObjValue(newEventValue, !Objects.equals(prevEvent, newEventValue));
        if (eventAction != null && eventAction.getObject().getEvent().getEventType() != null)
        {
            event.trySetObjValue(eventAction.getObject().getEvent().getEventType().name(), true);
        }
    }

    /**
     * Инициализация значений для обязательных полей в случае, если инициализируется форма редактирования ДПС
     */
    protected void initPropertiesValues(@Nullable EventActionWithScript eventAction, AsyncCallback<Void> callback)
    {
        if (eventAction == null)
        {
            callback.onSuccess(null);
            return;
        }
        title.setValue(i18nUtil.getLocalizedTitle(eventAction));
        code.setValue(eventAction.getObject().getId());
        description.setValue(i18nUtil.getLocalizedDescription(eventAction));
        event.trySetObjValue(eventAction.getObject().getEvent().getEventType().name());
        tags.trySetObjValue(eventAction.getObject().getTags());
        if (EventType.escalation.equals(eventAction.getObject().getEvent().getEventType()))
        {
            event.setDisable();
        }

        DtObject dtObject = eventAction.getDtObject();

        attrTree.setValue((Collection<DtObject>)dtObject.get(
                ru.naumen.metainfo.shared.eventaction.Constants.EventAction.ATTRIBUTES));
        contextAttrTree.setValue((Collection<DtObject>)dtObject.get(
                ru.naumen.metainfo.shared.eventaction.Constants.EventAction.CONTEXT_ATTRIBUTES));

        callback.onSuccess(null);
    }

    /**
     * Инициализация поля "Очередь обработки действия"
     * @param visible видимость поля
     */
    private void initJMSQueues(boolean visible)
    {
        final String actionType = isNewEventAction() ? SelectListPropertyValueExtractor.getValue(getAction()) :
                getEventAction().getObject().getAction().getActionType().name();
        if (actionType == null || !visible)
        {
            initJMSQueues(Collections.emptyList());
        }
        else
        {
            jmsQueueService.getAvailableJMSQueuesForEventAction(getFakeEventAction(actionType),
                    new BasicCallback<SimpleResult<List<DtObject>>>()
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<List<DtObject>> value)
                        {
                            initJMSQueues(value.get());
                        }

                        @Override
                        protected void handleFailure(String msg, @Nullable String details)
                        {
                            initJMSQueues(Collections.emptyList());
                        }
                    });
        }
        refreshAttributeProperties();
    }

    private void initJMSQueues(List<DtObject> jmsQueues)
    {
        final boolean emptyJMSQueues = jmsQueues.isEmpty();
        setPropertyVisible(jmsQueue, !emptyJMSQueues);

        SingleSelectCellList<?> list = jmsQueue.getValueWidget();
        list.clear();
        jmsQueue.clearValue();
        for (DtObject jmsQueue : jmsQueues)
        {
            list.addItem(jmsQueue.getTitle(), jmsQueue.getUUID());
        }
        // в случае редактирования сперва пробуем выбрать уже сохраненную
        if (!isNewEventAction())
        {
            final String jmsQueueCode = getEventAction().getObject().getJmsQueue();
            final Optional<DtObject> queue =
                    jmsQueues.stream().filter(q -> q.getUUID().equals(jmsQueueCode)).findFirst();
            if (queue.isPresent())
            {
                jmsQueue.trySetObjValue(queue.get().getUUID(), true);
                return;
            }
        }
        if (!emptyJMSQueues)
        {
            // первая системная (или в случае с событием "Поступление сообщения в очередь" просто самая первая)
            jmsQueue.trySetObjValue(jmsQueues.get(0).getUUID(), true);
        }
    }

    /* --- Операции с кастомизаторами формы -- */

    /**
     * Создать дополнительный построитель формы в зависимости от выбранного значения в поле "Действие"
     * @param action тип действия на форме
     */
    private EventActionFormPropertiesCreator createFormCreatorByActionType(@Nullable ActionType action)
    {
        final EventActionFormPropertiesCreator formPropsCreator = factory.getFormPropertiesCreator(action)
                .setValidation(validation).addValueChangeHandler(event -> initJMSQueues(!event.getValue()));
        formPropsCreator.init(eventAction, event);
        formPropsCreator.setReadyState(rs);
        getDisplay().setRightSideStyle(WidgetResources.INSTANCE.form().formEventactionScript(), false);
        formPropsCreator.bindProperties(getDisplay(), getFqns());
        rs.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                formPropsCreator.addProperties(getDisplay());
                formPropsCreator.refreshProperties(getDisplay(), getFqns(),
                        SelectItemValueExtractor.extract(event.getValue()));
            }
        });
        return formPropsCreator;
    }

    /**
     * Создать дополнительный кастомизатор формы в зависимости от выбранного значения в поле "Событие"
     * @param eventCreator аггрегатор дополнительного кастомизатора формы,
     *  зависящего от выбора значения в поле "Событие"
     * @param eventValue тип события на форме
     */
    @SuppressWarnings("rawtypes")
    private EventFormPropertiesCreator createFormCreatorByEventType(EventCreator<Event> eventCreator,
            @Nullable EventType eventValue)
    {
        final EventFormPropertiesCreator<Event> eventFormPropCreator = eventCreator
                .getFormPropertiesCreator(eventValue);
        eventFormPropCreator.setStartIndex(START_INDEX);
        eventFormPropCreator.setReadyState(rs);
        eventFormPropCreator.init(eventAction != null ? eventAction.getObject().getEvent() : null, getFqns());
        eventFormPropCreator.bindProperties();
        eventFormPropCreator.addProperties(getDisplay());
        int leftSidePropertiesCount = display.getLeftSideProperiesCount() + eventFormPropCreator.getPropertiesCount();
        display.setLeftSideProperiesCount(leftSidePropertiesCount);
        PopupValueCellTree<DtObject, Collection<DtObject>, ?> valueWidget = fqn.getValueWidget();
        registerHandler(valueWidget.addValueChangeHandler(event ->
        {
            if (eventFormPropCreator != null)
            {
                eventFormPropCreator.refreshProperties(getFqns());
            }
        }));
        return eventFormPropCreator;
    }

    /* --- Операции с изменением данных и/или их видимости/доступности --- */

    /**
     * Реакция на выбор/отмену выбора типа события "Поступление соообщения в очередь"
     * @param selected true - выбран данный тип события
     */
    private void onChangedArrivedEvent(boolean selected)
    {
        visibleFqnField(!selected);
        if (selected)
        {
            action.trySetObjValue(ActionType.ScriptEventAction.name(), true);
        }
        action.setEnabled(!selected);
    }

    /**
     * Скрыть/показать + снять/навесить валидаторы с поля Объекты
     */
    void visibleFqnField(boolean visible)
    {
        if (visible)
        {
            // значит смена с события "Поступление сообщения в очередь" на другое
            // иначе ничего не делаем
            if (!fqn.asWidget().isVisible())
            {
                validation.validate(fqn, notEmptyCollectionValidatorProvider.get());
                validation.validate(fqn, fqnValidator);
            }
        }
        else
        {
            validation.unvalidate(fqn);
            fqn.initValidation();
        }
        setPropertyVisible(fqn, visible);
        fqn.setValidationMarker(visible);
    }

    // выполняется при изменении списка метаклассов для которых необходимо выполнить действие
    protected void onMetaClassesChanged(ValueChangeEvent<Collection<DtObject>> event)
    {
        // содержит список ClassFqn всех выбранных классов
        Set<ClassFqn> fqns = event.getValue().stream()
                .map(DtObject::getMetainfo)
                .map(ClassFqn::fqnOfClass)
                .collect(Collectors.toSet());

        Set<ClassFqn> selectedFqns = event.getValue().stream()
                .map(DtObject::getMetainfo)
                .collect(Collectors.toSet());

        metainfoService.getMetaClasses(fqns, new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> value)
            {
                metaClasses = value;
                initEvents(selectedFqns);
            }
        });
    }

    /* --- Операции с получением данных --- */

    private ActionType getActionType()
    {
        ActionType actionValue;
        try
        {
            actionValue = ActionType.valueOf(SelectListPropertyValueExtractor.getValue(action));
        }
        catch (Exception e)
        {
            actionValue = null;
        }
        return actionValue;
    }

    protected Set<ActionType> availableActionTypes(Set<ActionType> unavailableActionTypes)
    {
        final Set<ActionType> actionTypes = EnumSet.noneOf(ActionType.class);
        actionTypes.addAll(settings.getActionTypes());
        actionTypes.removeAll(unavailableActionTypes);
        return actionTypes;
    }

    protected List<EventType> getAllowedEvents(@Nullable List<MetaClassLite> metaClasses,
            @Nullable Set<ClassFqn> selectedFqns, @Nullable String actionType)
    {
        if (containsFileClass(metaClasses))
        {
            return Lists.newArrayList(EventType.userEvent);
        }
        List<EventType> result = EventType.valuesForClasses(metaClasses)
                .stream()
                .filter(escalationEventPredicate)
                .filter(eventTypeByModulePredicate)
                // событие Поступление сообщения в очередь доступно только, если не выбраны метаклассы
                .filter(arrivedMessageToQueueEventPredicate(metaClasses, selectedFqns))
                // событие "Сообщение NDAP" доступно только при выборе класса "Сервер мониторинга"
                .filter(ndapMessageEventPredicate(selectedFqns))
                .collect(Collectors.toList());
        if (eventAction != null && !EventType.escalation.equals(eventAction.getObject().getEvent().getEventType()))
        {
            result.remove(EventType.escalation);
        }
        if (null != actionType)
        {
            Set<EventType> unavailableEvents = availableActionsProvider.getUnavailableEvents(
                    EnumUtils.fromString(ActionType.class, actionType, ActionType.ScriptEventAction));
            result.removeIf(unavailableEvents::contains);
        }
        return result;
    }

    /**
     * Создадим DTO только с нужными данными для запроса доступных очередей
     * @param actionType тип действия
     */
    private EventAction getFakeEventAction(String actionType)
    {
        final EventAction eventAction = new EventAction();
        // в случае редактирования для выбора очереди нам нужен код ДПС
        if (!isNewEventAction())
        {
            eventAction.setCode(getEventAction().getCode());
        }
        String eventType;
        if (isSlowEventAction())
        {
            eventAction.setSlow(true);
        }
        else if ((eventType = SelectListPropertyValueExtractor.getValue(getEvent())) != null)
        {
            final Event event = new Event();
            event.setEventType(EventType.valueOf(eventType));
            eventAction.setEvent(event);
        }
        final FakeAction fakeAction = new FakeAction();
        fakeAction.setActionType(ActionType.valueOf(actionType));
        eventAction.setAction(fakeAction);
        return eventAction;
    }

    private List<ClassFqn> getFqns()
    {
        return CollectionUtils.asArrayList(CollectionUtils.transform(fqn.getValue(), DtObject.FQN_EXTRACTOR));
    }

    private boolean isCurrentRecipientTogetherMethodCc(EventActionWithScript eventAction)
    {
        NotificationEventAction ntfEventAction = (NotificationEventAction)eventAction.getObject().getAction();
        boolean isCurrentRecipientInMessage = metainfoUtils.getLocalizedValue(ntfEventAction.getSubject())
                                                      .contains(ru.naumen.core.shared.Constants.CURRENT_RECIPIENT_PARAM)
                                              || metainfoUtils.getLocalizedValue(
                ntfEventAction.getMessage()).contains(ru.naumen.core.shared.Constants.CURRENT_RECIPIENT_PARAM);
        ScriptDto script = eventAction.getScript(ntfEventAction.getScript());
        String scriptBody = "";
        if (script != null && script.getBody() != null && !ScriptsComponentTree.EMPTY.equals(
                script.getSelectStrategy()))
        {
            scriptBody = script.getBody();
        }
        boolean isCcMethod = scriptBody.contains("notification.cc") || scriptBody.contains("notification.ccEmployee");
        if (isCurrentRecipientInMessage && isCcMethod)
        {
            getDisplay().addErrorMessage(messages.currentRecipientTogetherMethodCc());
            return true;
        }
        return false;
    }

    private boolean isNotificationWithNotLoadedScript(@Nullable ScriptDto script)
    {
        return script != null && NotificationEventAction.class == eventAction.getObject().getAction().getClass()
               && !script.isLoaded() && !ScriptsComponentTree.EMPTY.equals(script.getSelectStrategy())
               && !ScriptsComponentTree.NEW_SCRIPT.equals(script.getSelectStrategy());
    }

    /**
     * Выставлен ли чекбокс взаимодействие с внешней системой
     * Если выбран creator под тип ДПС, то берем данные с чекбокса взаимодействие с внешней системой
     * Если это редактирование и метод вызывается в ходе биндинга формы, то смотрим на данные в редактируемом объекте
     */
    private boolean isSlowEventAction()
    {
        if (creator != null)
        {
            return creator.isSlowEventAction();
        }
        return !isNewEventAction() &&
               getEventAction().getObject().isSlow();
    }

    /* --- Геттеры для полей --- */

    protected Property<SelectItem> getAction()
    {
        return action;
    }

    protected Property<String> getCode()
    {
        return code;
    }

    protected Property<SelectItem> getEvent()
    {
        return event;
    }

    protected EventActionWithScript getEventAction()
    {
        return eventAction;
    }

    protected EventActionFactory getEventActionFactory()
    {
        return factory;
    }

    protected EventActionMessages getEventActionMessages()
    {
        return messages;
    }

    protected Property<Collection<DtObject>> getFqn()
    {
        return fqn;
    }

    protected MetainfoServiceAsync getMetainfoService()
    {
        return metainfoService;
    }

    protected Property<String> getTitle()
    {
        return title;
    }

    /* --- Неиспользуемые/дефолтные/абстрактные данные --- */

    protected abstract boolean isNewEventAction();

    @Override
    public void refreshDisplay()
    {

    }

    protected void initActionTypes(Set<ActionType> unavailableActionTypes)
    {
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(description, "description");
        DebugIdBuilder.ensureDebugId(event, "event");
        DebugIdBuilder.ensureDebugId(action, "action");
        DebugIdBuilder.ensureDebugId(fqn, "fqn");
        DebugIdBuilder.ensureDebugId(tags, "tags");
        DebugIdBuilder.ensureDebugId(jmsQueue, "jmsQueue");
        DebugIdBuilder.ensureDebugId(attrTree, "attrTree");
        DebugIdBuilder.ensureDebugId(contextAttrTree, "contextAttrTree");
    }
}