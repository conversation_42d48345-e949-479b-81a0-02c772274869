package ru.naumen.metainfoadmin.client.attributes;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.METAINFO;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DIGITS_COUNT_RESTRICTION;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.HAS_GROUP_SEPARATORS;

import java.util.Map;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.common.client.utils.HtmlSanitizeUtils;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextChangedEvent;
import ru.naumen.core.client.content.ContextChangedHandler;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.properties.HtmlProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.CommonRestriction;
import ru.naumen.metainfo.shared.elements.HasDateTimeRestriction.RestrictionType;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandCode;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormPropertyValuesInitializer;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeInfoPropertyCreator;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * {@link Presenter} контента модальной формы просмотра свойств атрибута
 *
 * <AUTHOR>
 * @since 1 авг. 2018 г.
 */
public class AttributeInfoContentPresenter extends BasicPresenter<InfoDisplay> implements ContextChangedHandler<Context>
{
    @Inject
    private AttributeFormPropertyValuesInitializer<ObjectFormEdit> propertyValuesInitializer;
    @Inject
    private AttributeInfoPropertyCreator propertyBinder;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private CommandFactory commandFactory;
    @Inject
    private CommonMessages messages;
    @Inject
    private HtmlSanitizeUtils htmlSanitizeUtils;

    private final IProperties propertyValues = new MapProperties();

    private final IProperties contextProps = new MapProperties();

    private Attribute attribute;

    private Context context;

    private final ToolBarDisplayMediator<Attribute> toolBar;

    private ReadyState rs;

    @Inject
    public AttributeInfoContentPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(display.getToolBar());
        setInitialProperties();
    }

    public void buildContent()
    {
        display.hideTitle();
        registerHandler(context.getEventBus().addHandler(ContextChangedEvent.getType(), this));
        refreshDisplay();
    }

    public Attribute getAttribute()
    {
        return attribute;
    }

    public Context getContext()
    {
        return context;
    }

    @Override
    public void onContextChanged(ContextChangedEvent<Context> e)
    {
        if (isRevealed)
        {
            context = e.getContext();
            attribute = context.getMetainfo().getAttribute(attribute.getCode());
            refreshDisplay();
        }
    }

    @Override
    public void refreshDisplay()
    {
        fillDisplay();
    }

    public void setAttribute(Attribute attribute)
    {
        this.attribute = attribute;
    }

    public void setContext(Context context)
    {
        this.context = context;
    }

    public void setReadyState(ReadyState rs)
    {
        this.rs = rs;
    }

    @Override
    protected void onBind()
    {
        display.hideTitle();
        registerHandler(context.getEventBus().addHandler(ContextChangedEvent.getType(), this));
        refreshDisplay();
    }

    @SuppressWarnings("unchecked")
    private void addEditButton()
    {
        AttributeCommandParam param = new AttributeCommandParam(attribute, null, context);
        if (param.hasPermission(attribute, PermissionType.EDIT))
        {
            ButtonPresenter<Attribute> editButton = (ButtonPresenter<Attribute>)buttonFactory.create(
                    ButtonCode.EDIT, messages.edit(), commandFactory.create(AttributeCommandCode.EDIT, param));
            toolBar.add(editButton);
        }
        toolBar.bind();
    }

    private void addPropertiesAfterInit()
    {
        if (attribute.getType().getCode().equals(DateTimeIntervalAttributeType.CODE) && attribute.isHardcoded())
        {
            propertyValues.setProperty(AttributeFormPropertyCode.NEED_STORE_UNITS, true);
        }
        if (Constants.IntegerAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            ru.naumen.metainfo.shared.elements.IntegerAttributeType objectType = attribute.getType().cast();
            propertyValues.setProperty(HAS_GROUP_SEPARATORS, objectType.isHasGroupSeparator());
        }
        if (Constants.DoubleAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            ru.naumen.metainfo.shared.elements.DoubleAttributeType objectType = attribute.getType().cast();
            propertyValues.setProperty(HAS_GROUP_SEPARATORS, objectType.isHasGroupSeparator());
            if (objectType.getDecimalsCountRestriction() != null)
            {
                propertyValues.setProperty(DIGITS_COUNT_RESTRICTION, objectType.getDecimalsCountRestriction());
            }
        }
    }

    private void addPropertiesToDisplay()
    {
        Map<Integer, Property<?>> toBind = propertyBinder.createProperties(rs);
        rs.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                cleanDisplay();
                toBind.keySet().forEach((code) ->
                {
                    Property<?> property = toBind.get(code);
                    if (attribute.isHiddenAttrCaption() && AttributeFormPropertyCode.TITLE.equals(
                            property.getCode()))
                    {
                        property.getValueWidget().asWidget()
                                .setStyleName(WidgetResources.INSTANCE.form().hiddenAttrCaption());
                    }
                    if (AttributeFormPropertyCode.TITLE.equals(property.getCode()))
                    {
                        HtmlProperty castProperty = (HtmlProperty)property;
                        castProperty.setValue(htmlSanitizeUtils.sanitize(castProperty.getValue()));
                    }
                    display.add(property);
                });
                addEditButton();
            }
        });
    }

    private void cleanDisplay()
    {
        display.clearProperties();
        display.getToolBar().clean();
    }

    private void fillDisplay()
    {
        contextProps.setProperty(METAINFO, context.getMetainfo());
        ReadyState rs = new ReadyState(this);
        propertyValuesInitializer.init(contextProps, propertyValues, attribute, rs);
        addPropertiesAfterInit();
        propertyValuesInitializer.loadRelatedData(contextProps, propertyValues, new BasicCallback<Void>(display)
        {
            @Override
            protected void handleSuccess(Void value)
            {
                rs.ready(new ReadyCallback(this)
                {
                    @Override
                    public void onReady()
                    {
                        initPropertyBinder();
                        addPropertiesToDisplay();
                    }
                });

            }
        });
    }

    private void initPropertyBinder()
    {
        propertyBinder.setPropertyValues(propertyValues);
        propertyBinder.setAttribute(attribute);
    }

    private void setInitialProperties()
    {
        propertyValues.setProperty(AttributeFormPropertyCode.COMPUTABLE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.COMPOSITE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.DETERMINABLE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.EDITABLE, true);
        propertyValues.setProperty(AttributeFormPropertyCode.COMPLEX_RELATION, Boolean.FALSE.toString());
        propertyValues.setProperty(AttributeFormPropertyCode.EDITABLE_IN_LISTS, false);
        propertyValues.setProperty(AttributeFormPropertyCode.REQUIRED, false);
        propertyValues.setProperty(AttributeFormPropertyCode.REQUIRED_IN_INTERFACE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.UNIQUE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.FILTERED_BY_SCRIPT, false);
        propertyValues.setProperty(AttributeFormPropertyCode.DEFAULT_BY_SCRIPT, false);
        propertyValues.setProperty(AttributeFormPropertyCode.USE_GEN_RULE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.EXPORT_NDAP, false);
        propertyValues.setProperty(AttributeFormPropertyCode.HIDDEN_WHEN_EMPTY, false);
        propertyValues.setProperty(AttributeFormPropertyCode.HIDDEN_WHEN_NO_POSSIBLE_VALUES, false);
        propertyValues.setProperty(AttributeFormPropertyCode.EDIT_ON_COMPLEX_FORM_ONLY, false);
        propertyValues.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_TYPE,
                RestrictionType.NO_RESTRICTION.name());
        propertyValues.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_SCRIPT, null);
        propertyValues.setProperty(AttributeFormPropertyCode.DATE_TIME_COMMON_RESTRICTIONS,
                Lists.newArrayList(CommonRestriction.values()));
        propertyValues.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_ATTRIBUTE, null);
        propertyValues.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_CONDITION, null);
        propertyValues.setProperty(AttributeFormPropertyCode.HIDE_ARCHIVED, false);
    }
}
