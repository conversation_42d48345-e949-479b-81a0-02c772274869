/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content;

import java.lang.reflect.Type;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.multibindings.GinMapBinder;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.factory.ContentFactoryInner;
import ru.naumen.core.client.content.factory.DefaultContentFactory;
import ru.naumen.core.client.inject.Gin;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * <AUTHOR>
 * @since 26.02.2013
 *
 */
public class DynadminContentGinModule<T extends Content> extends AbstractGinModule
{
    private static final Type CONTEXT = TypeLiteral.get(UIContext.class).getType();

    public static <T extends Content> DynadminContentGinModule<T> create(Class<T> clazz)
    {
        return new DynadminContentGinModule<T>(clazz);
    }

    protected final Class<T> clazzRaw;
    protected final Type clazz;

    private TypeLiteral<? extends ContentFactoryInner<T, UIContext>> factory;
    private TypeLiteral<? extends ContentPresenter<T, UIContext>> presenter;

    public DynadminContentGinModule(Class<T> clazz)
    {
        this.clazzRaw = clazz;
        this.clazz = TypeLiteral.get(clazz).getType();
        this.factory = Gin.typeLiteral(DefaultContentFactory.class, this.clazz, CONTEXT);
    }

    public DynadminContentGinModule<T> setFactory(Class<? extends ContentFactoryInner<T, UIContext>> factory)
    {
        this.factory = TypeLiteral.get(factory);
        return this;
    }

    public DynadminContentGinModule<T> setFactory(TypeLiteral<? extends ContentFactoryInner<T, UIContext>> factory)
    {
        this.factory = factory;
        return this;
    }

    public DynadminContentGinModule<T> setPresenter(Class<? extends ContentPresenter<T, UIContext>> presenter)
    {
        this.presenter = TypeLiteral.get(presenter);
        return this;
    }

    public DynadminContentGinModule<T> setPresenter(TypeLiteral<? extends ContentPresenter<T, UIContext>> presenter)
    {
        this.presenter = presenter;
        return this;
    }

    @Override
    @SuppressWarnings("rawtypes")
    protected void configure()
    {
        //@formatter:off
        if(presenter!=null)
        {
            bind(Gin.typeLiteral(ContentPresenter.class, clazz, CONTEXT)).to(presenter);
        }
        bind(Gin.typeLiteral(ContentFactoryInner.class, clazz, CONTEXT)).to(factory).in(Singleton.class);
        
        // регистрируем в ContentFactory
        GinMapBinder<Class, ContentFactoryInner> mapBinder = GinMapBinder.newMapBinder(binder(), Class.class, ContentFactoryInner.class);
        mapBinder.addBinding(clazzRaw).to(Gin.<ContentFactoryInner>typeLiteral(ContentFactoryInner.class, clazz, CONTEXT));
        //@formatter:on
    }
}