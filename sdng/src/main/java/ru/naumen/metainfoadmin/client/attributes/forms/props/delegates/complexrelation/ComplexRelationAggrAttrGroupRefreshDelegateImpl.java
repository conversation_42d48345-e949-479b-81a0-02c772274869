package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPLEX_RELATION;

import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType;
import ru.naumen.metainfo.shared.elements.ComplexRelationType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.aggrClasses.AggregateClassesUtils;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Реализация {@link AbstractComplexRelationAttrGroupRefreshDelegateImpl} для агрегирующих
 * атрибутов
 *
 * <AUTHOR>
 * @since 03 февр. 2016 г.
 */
public class ComplexRelationAggrAttrGroupRefreshDelegateImpl<F extends ObjectForm> extends
        AbstractComplexRelationAttrGroupRefreshDelegateImpl<F, ListBoxWithEmptyOptProperty>
{
    private final ClassFqn classFqn;

    @Inject
    public ComplexRelationAggrAttrGroupRefreshDelegateImpl(@Assisted ClassFqn classFqn, @Assisted String propertyCode)
    {
        super(propertyCode);
        this.classFqn = classFqn;
    }

    @Override
    protected List<ClassFqn> getFqns(PropertyContainerContext context)
    {
        return Lists.newArrayList(classFqn);
    }

    @Override
    protected boolean show(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        if (!AggregateAttributeType.CODE.equals(attrType) && !ResponsibleAttributeType.CODE.equals(attrType))
        {
            return false;
        }
        if (!ComplexRelationType.FLAT.getCode().equals(context.getPropertyValues().getProperty(COMPLEX_RELATION)))
        {
            return false;
        }

        return AggregateClassesUtils.getAggregateClasses(context).contains(classFqn);
    }
}
