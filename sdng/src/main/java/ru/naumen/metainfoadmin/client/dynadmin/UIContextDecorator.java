package ru.naumen.metainfoadmin.client.dynadmin;

import com.google.gwt.event.shared.EventBus;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ErrorAndAttentionMessageHandler;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.Content;

/**
 *
 * <AUTHOR>
 *
 */
public class UIContextDecorator implements UIContext
{
    private final UIContext adaptee;
    private final ReadyState readyState;

    @CheckForNull
    private final ErrorAndAttentionMessageHandler errorAndAttentionMsgHandler;

    public UIContextDecorator(UIContext adaptee, ErrorAndAttentionMessageHandler errorAndAttentionMsgHandler)
    {
        this(adaptee, adaptee.getReadyState(), errorAndAttentionMsgHandler);
    }

    public UIContextDecorator(UIContext adaptee, ReadyState readyState)
    {
        this(adaptee, readyState, adaptee.getErrorAndAttentionMsgHandler());
    }

    public UIContextDecorator(UIContext adaptee, ReadyState readyState,
            @Nullable ErrorAndAttentionMessageHandler errorAndAttentionMsgHandler)
    {
        this.adaptee = adaptee;
        this.readyState = readyState;
        this.errorAndAttentionMsgHandler = errorAndAttentionMsgHandler;
    }

    @Override
    public void destroy()
    {
        adaptee.destroy();
    }

    public UIContext getAdaptee()
    {
        return adaptee;
    }

    @Override
    public String getCode()
    {
        return adaptee.getCode();
    }

    @Override
    public Content getContentInLayoutEditMode()
    {
        return adaptee.getContentInLayoutEditMode();
    }

    @Override
    public MapProperties getContextProperties()
    {
        return adaptee.getContextProperties();
    }

    @Override
    public <T> T getContextProperty(String name)
    {
        return adaptee.getContextProperty(name);
    }

    @Override
    @CheckForNull
    public ErrorAndAttentionMessageHandler getErrorAndAttentionMsgHandler()
    {
        return errorAndAttentionMsgHandler;
    }

    @Override
    public EventBus getEventBus()
    {
        return adaptee.getEventBus();
    }

    @Override
    public MetaClass getMetainfo()
    {
        return adaptee.getMetainfo();
    }

    @Override
    public DtObject getObject()
    {
        return adaptee.getObject();
    }

    @Override
    public <T extends Context> T getParentContext()
    {
        return adaptee.getParentContext();
    }

    @Override
    public ReadyState getReadyState()
    {
        return readyState;
    }

    @Override
    public Content getRootContent()
    {
        return adaptee.getRootContent();
    }

    @Override
    public ContentInfo getRootContentInfo()
    {
        return adaptee.getRootContentInfo();
    }

    @Override
    public String getTemplateCode()
    {
        return adaptee.getTemplateCode();
    }

    @Nullable
    @Override
    public UITemplateContext getUITemplateContext()
    {
        return adaptee.getUITemplateContext();
    }

    @Override
    public boolean isEditable()
    {
        return adaptee.isEditable();
    }

    @Override
    public void ready(IReadyCallback callback)
    {
        adaptee.ready(callback);
    }

    @Override
    public void registerChildContext(Context child)
    {
        adaptee.registerChildContext(child);
    }

    @Override
    public SynchronizationCallbackRegistration registerSynchronization(SynchronizationCallback callback)
    {
        return adaptee.registerSynchronization(callback);
    }

    @Override
    public void setContextProperties(MapProperties contextProperties)
    {
        adaptee.setContextProperties(contextProperties);
    }

    @Override
    public void setContextProperty(String name, @Nullable Object value)
    {
        adaptee.setContextProperty(name, value);
    }

    @Override
    public void setObject(DtObject object)
    {
        adaptee.setObject(object);
    }

    @Override
    public void setUITemplateContext(@Nullable UITemplateContext context)
    {
        adaptee.setUITemplateContext(context);
    }

    @Override
    public void unregisterChildContext(Context child)
    {
        adaptee.unregisterChildContext(child);
    }

    @Override
    public PermissionHolder getPermissions()
    {
        return adaptee.getPermissions();
    }

    @Override
    public boolean hasPermission(String subjectUuid, String permissionKey)
    {
        return adaptee.hasPermission(subjectUuid, permissionKey);
    }

    @Override
    public boolean hasContentPermission(Content content, String permissionKey)
    {
        return adaptee.hasContentPermission(content, permissionKey);
    }

    @Override
    public boolean hasContentPermission(String contentUuid, String permissionKey)
    {
        return adaptee.hasContentPermission(contentUuid, permissionKey);
    }

    @Override
    public boolean hasContentPermission(String contentUuid, String permissionKey, boolean defaultValue)
    {
        return adaptee.hasContentPermission(contentUuid, permissionKey, defaultValue);
    }

    @Override
    public boolean isContentPermissionDefined(String contentUuid, String permissionKey)
    {
        return adaptee.isContentPermissionDefined(contentUuid, permissionKey);
    }
}
