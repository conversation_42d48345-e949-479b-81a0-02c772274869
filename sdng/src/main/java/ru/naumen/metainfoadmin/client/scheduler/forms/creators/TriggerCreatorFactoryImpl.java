package ru.naumen.metainfoadmin.client.scheduler.forms.creators;

import java.util.HashMap;
import java.util.Map;

import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.scheduler.Trigger.CalculateStrategies;
import ru.naumen.metainfo.shared.scheduler.Trigger.Periods;
import ru.naumen.metainfo.shared.scheduler.TriggerType;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerGinjector;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskMessages;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

/**
 * Реализация {@link TriggerCreatorFactory}
 *
 * <AUTHOR>
 *
 */
@Singleton
public class TriggerCreatorFactoryImpl implements TriggerCreatorFactory
{
    @Inject
    public MetainfoServiceAsync metainfoService;

    @Inject
    MetainfoUtils metainfoUtils;

    HashMap<String, TriggerCreatorFactory.Factory> types = new HashMap<String, TriggerCreatorFactory.Factory>();
    HashMap<String, String> periods = new HashMap<String, String>(4);
    HashMap<String, String> strategies = new HashMap<String, String>(2);

    @Inject
    public TriggerCreatorFactoryImpl(final SchedulerGinjector injector, final SchedulerTaskMessages messages)
    {
        types.put(TriggerType.CONCRETE_DATE.toString(), new TriggerCreatorFactory.Factory()
        {
            @Override
            public TriggerCreator create()
            {
                return injector.concreteDateTriggerCreator();
            }

            @Override
            public String getTitle()
            {
                return messages.executeAtConcreteDateType();
            }

        });
        types.put(TriggerType.PERIODIC.toString(), new TriggerCreatorFactory.Factory()
        {
            @Override
            public TriggerCreator create()
            {
                return injector.periodicTriggerCreator();
            }

            @Override
            public String getTitle()
            {
                return messages.periodicExecution();
            }

        });
        periods.put(Periods.DAILY.toString(), messages.daily());
        periods.put(Periods.WEEKLY.toString(), messages.weekly());
        periods.put(Periods.MONTHLY.toString(), messages.monthly());
        periods.put(Periods.YEARLY.toString(), messages.yearly());
        strategies.put(CalculateStrategies.FROM_START.toString(), messages.fromStart());
        strategies.put(CalculateStrategies.FROM_LAST_EXECUTION.toString(), messages.fromLastExecution());
    }

    @Override
    public Map<String, String> getCalculateStrategies()
    {

        return strategies;
    }

    @Override
    public TriggerCreator getCreator(String name)
    {
        return types.get(name).create();
    }

    @Override
    public Map<String, Factory> getCreators()
    {
        return types;
    }

    @Override
    public Map<String, String> getPeriods()
    {
        return periods;
    }

    @Override
    public String getTypeTitle(String code)
    {
        return types.get(code).getTitle();
    }
}
