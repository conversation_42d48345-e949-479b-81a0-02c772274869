package ru.naumen.metainfoadmin.client.fastlink.settings;

import java.util.Collection;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.Key;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.tree.selection.HierarchicalMetaClassMultiSelectionModel;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumn;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumnFactory;
import ru.naumen.core.client.widgets.columns.LinkToPlaceWithIndentColumn;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSetting;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.AddFastLinkSettingCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.DeleteFastLinkSettingCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.EditFastLinkRightsEnabledCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.EditFastLinkRightsEnabledCommandParam;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.EditFastLinkSettingCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.FastLinkSettingsCommandFactoryInitializer;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.FastLinkSettingsCommandParam;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.MoveFastLinkSettingDownCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.MoveFastLinkSettingUpCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.forms.FastLinkSettingFormPropertyControllerFactoryImpl;
import ru.naumen.metainfoadmin.client.fastlink.settings.forms.FastLinkSettingFormPropertyParametersDescriptorFactoryImpl;

/**
 * <AUTHOR>
 * @since 05.03.18
 */
public class FastLinkSettingsGinModule extends AbstractGinModule
{
    public static class FastLinkSettingFormPropertyCode
    {
        private FastLinkSettingFormPropertyCode()
        {
        }

        public static final String CODE = "code";
        public static final String TITLE = "title";
        public static final String ALIAS = "alias";
        public static final String MENTION_TYPES = "mentionTypes";
        public static final String CONTEXT_TYPES = "contextTypes";
        public static final String MENTION_ATTRIBUTE = "mentionAttribute";
        public static final String ATTRIBUTE_GROUP = "attributeGroup";
        public static final String PROFILES = "profiles";
        public static final String SETTINGS_SET = "settingsSet";
    }

    /**
     * Максимальная длина названий
     */
    public static final int MAX_TITLE_LENGTH = 255;

    /**
     * Максимальная длина кода
     */
    public static final int MAX_CODE_LENGTH = 255;

    /**
     * Максимальная длина алиаса
     */
    public static final int MAX_ALIAS_LENGTH = 255;

    //@formatter:off
    @Override
    protected void configure()
    {        
        bind(FastLinkSettingsCommandFactoryInitializer.class).asEagerSingleton();
        
        bind(new TypeLiteral<TableDisplay<FastLinkSetting>>() {}).to(new TypeLiteral<TableWithArrowsDisplay<FastLinkSetting>>() {});
        
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<LinkToPlaceColumn<FastLinkSetting>>(){}, new TypeLiteral<LinkToPlaceWithIndentColumn<FastLinkSetting>>(){})
            .build(new TypeLiteral<LinkToPlaceColumnFactory<FastLinkSetting>>(){})); 
        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, MentionContextTypesPropertyControllerImpl.class)
                .build(Key.get(new TypeLiteral<PropertyControllerSyncFactoryInj<
                        Collection<DtObject>, PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>, 
                        HierarchicalMetaClassMultiSelectionModel<DtObject>>>>>(){}, 
                Names.named(FastLinkSettingFormPropertyCode.CONTEXT_TYPES))));        
        install(Gin.install(
                new TypeLiteral<CommandProvider<MoveFastLinkSettingUpCommand, FastLinkSettingsCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<FastLinkSetting>>(){}, 
                new TypeLiteral<MoveFastLinkSettingUpCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<MoveFastLinkSettingDownCommand, FastLinkSettingsCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<FastLinkSetting>>(){}, 
                new TypeLiteral<MoveFastLinkSettingDownCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<AddFastLinkSettingCommand, FastLinkSettingsCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<FastLinkSetting>>(){}, 
                new TypeLiteral<AddFastLinkSettingCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditFastLinkSettingCommand, FastLinkSettingsCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<FastLinkSetting>>(){}, 
                new TypeLiteral<EditFastLinkSettingCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<DeleteFastLinkSettingCommand, FastLinkSettingsCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<FastLinkSetting>>(){}, 
                new TypeLiteral<DeleteFastLinkSettingCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditFastLinkRightsEnabledCommand, EditFastLinkRightsEnabledCommandParam>>(){},
                new TypeLiteral<ClosureCommand<DtObject>>(){},
                new TypeLiteral<EditFastLinkRightsEnabledCommand>(){}));

        install(PropertyControllerGinModule.create(FastLinkSetting.class, ObjectForm.class)
                .setPropertyControllerFactory(new TypeLiteral<FastLinkSettingFormPropertyControllerFactoryImpl>(){})
                .setPropertyParametersDescriptorFactory(new TypeLiteral<FastLinkSettingFormPropertyParametersDescriptorFactoryImpl>(){}));
    }    
    //@formatter:on
}
