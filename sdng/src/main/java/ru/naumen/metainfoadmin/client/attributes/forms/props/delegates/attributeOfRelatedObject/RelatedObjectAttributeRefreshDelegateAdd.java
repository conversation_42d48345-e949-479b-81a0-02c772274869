package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.NDAPConstants;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления свойства "Атрибут связанного класса".
 * <AUTHOR>
 * @since 08.08.18
 */
public class RelatedObjectAttributeRefreshDelegateAdd<F extends ObjectForm>
        implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    private static List<AttributeFqn> attributesToSkip = Lists.newArrayList(
            new AttributeFqn(Employee.FQN, Employee.SECURITY_GROUPS),
            new AttributeFqn(Employee.FQN, Employee.ALL_GROUP),
            new AttributeFqn(Employee.FQN, Employee.DIRECT_HEAD),
            new AttributeFqn(Employee.FQN, Employee.IS_ONLINE), new AttributeFqn(Employee.FQN, Employee.LICENSE),
            new AttributeFqn(OU.FQN, OU.SECURITY_GROUPS), new AttributeFqn(Team.FQN, Team.SECURITY_GROUPS),
            new AttributeFqn(ClassFqn.parse(NDAPConstants.METRIC_CLASS_ID), NDAPConstants.NDAPMetric.SYSTEM_ERROR),
            new AttributeFqn(ClassFqn.parse(NDAPConstants.METRIC_CLASS_ID), NDAPConstants.NDAPMetric.SYSTEM_TIME),
            new AttributeFqn(ClassFqn.parse(NDAPConstants.METRIC_CLASS_ID), NDAPConstants.NDAPMetric.SYSTEM_VALUE),
            new AttributeFqn(ClassFqn.parse(NDAPConstants.METRIC_CLASS_ID), NDAPConstants.NDAPMetric.SYSTEM_SEVERITY),
            new AttributeFqn(ClassFqn.parse(NDAPConstants.METRIC_CLASS_ID),
                    NDAPConstants.NDAPMetric.SYSTEM_RECORD_TIME),
            new AttributeFqn(ClassFqn.parse(NDAPConstants.TRIGGER_CLASS_ID), NDAPConstants.NDAPTrigger.STATUS));

    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context,
            ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        IProperties propertyValues = context.getPropertyValues();
        String typeCode = propertyValues.getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        String savedValue = context.getPropertyValues().getProperty(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE);

        if (!typeCode.equals(AttributeOfRelatedObjectSettings.CODE))
        {
            callback.onSuccess(false);
            return;
        }
        RelationsAttrTreeObject selectedAttr = context.getPropertyValues()
                .getProperty(AttributeFormPropertyCode.ATTR_CHAIN);
        ClassFqn relatedObjectMetaClass = selectedAttr == null
                ? null
                : selectedAttr.getAttribute().getType().<ObjectAttributeType> cast().getRelatedMetaClass();
        if (relatedObjectMetaClass != null)
        {
            List<ClassFqn> permittedTypes = Lists.newArrayList(relatedObjectMetaClass);
            ObjectAttributeType attrType = selectedAttr.getAttribute().getType().cast();
            if (attrType != null)
            {
                permittedTypes.addAll(attrType.getPermittedTypes());
            }

            metainfoService.getFullMetaInfo(permittedTypes, new BasicCallback<List<MetaClass>>()
            {
                @Override
                protected void handleSuccess(List<MetaClass> value)
                {
                    context.getPropertyValues().setProperty(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE, null);
                    property.getValueWidget().clear();

                    Optional<MetaClass> rootMc = value.stream()
                            .filter(metaClass -> relatedObjectMetaClass.equals(metaClass.getFqn())).findFirst();

                    if (!rootMc.isPresent())
                    {
                        // Такого быть не должно. Сделано на всякий случай для избежания исключений

                        callback.onSuccess(true);
                        return;
                    }

                    Map<AttributeFqn, Attribute> attrsOfRoot = rootMc.get().getAttributes().stream()
                            .filter(this::attributeFilter)
                            .collect(Collectors.toMap(Attribute::getFqn, attr -> attr));
                    value.stream()
                            .filter(metaClass -> !relatedObjectMetaClass.equals(metaClass.getFqn()))
                            .flatMap(mc -> mc.getAttributes().stream())
                            .filter(this::attributeFilter)
                            .filter(attr -> attrsOfRoot.keySet().stream().noneMatch(at -> at.equals(attr.getFqn())))
                            .forEach(attr -> attrsOfRoot.put(attr.getFqn(), attr));
                    List<Attribute> attrsList = Lists.newArrayList(attrsOfRoot.values());
                    attrsList.sort(ITitled.COMPARATOR);

                    attrsList.forEach(attr ->
                    {
                        String attrFqnAsString = AttributeFqn.toString(attr.getDeclaredMetaClass(), attr.getCode());

                        if (context.getPropertyValues()
                                    .<String> getProperty(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE) == null)
                        {
                            context.getPropertyValues()
                                    .setProperty(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE, attrFqnAsString);
                            context.getPropertyControllers().get(AttributeFormPropertyCode.SHOW_PRS).refresh();
                        }

                        String attrTitle = attr.getTitle() + " (" + attr.getCode() + ')';
                        if (!attr.getDeclaredMetaClass().equals(relatedObjectMetaClass)
                            && value.stream().anyMatch(mc -> attr.getDeclaredMetaClass().equals(mc.getFqn())))
                        {
                            attrTitle = attr.getTitle() + " (" + attr.getDeclaredMetaClass().getCode() + '@'
                                        + attr.getCode() + ')';
                        }

                        property.getValueWidget().addItem(attrTitle, attrFqnAsString);
                        if (attrFqnAsString.equals(savedValue))
                        {
                            context.getPropertyValues().setProperty(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE,
                                    savedValue);
                            property.getValueWidget().setObjValue(savedValue);
                        }

                    });

                    callback.onSuccess(true);
                }

                private boolean attributeFilter(Attribute attr)
                {
                    return !attr.isComputable()
                           && !attr.getType().isAttributeOfRelatedObject() && !attributesToSkip.contains(attr.getFqn())
                           && !attr.getCode().equals(AbstractBO.METACLASS);
                }
            });
        }

        callback.onSuccess(true);
    }
}
