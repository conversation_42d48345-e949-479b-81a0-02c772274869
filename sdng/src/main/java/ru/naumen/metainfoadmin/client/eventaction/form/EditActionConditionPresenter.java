package ru.naumen.metainfoadmin.client.eventaction.form;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.eventaction.EventActionConstants;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.elements.wf.Condition;
import ru.naumen.metainfo.shared.elements.wf.Condition.ConditionType;
import ru.naumen.metainfo.shared.eventaction.ActionConditionType;
import ru.naumen.metainfo.shared.eventaction.ActionConditionWithScript;
import ru.naumen.metainfoadmin.client.eventaction.ActionConditionCreator;
import ru.naumen.metainfoadmin.client.eventaction.ActionConditionsCreatorFactory;

/**
 * {@link Presenter} диалога редактирования {@link Condition}
 *
 * <AUTHOR>
 *
 */
public class EditActionConditionPresenter extends OkCancelPresenter<PropertyDialogDisplay> implements
        CallbackPresenter<ActionConditionWithScript, ActionConditionWithScript>
{
    AsyncCallback<ActionConditionWithScript> callback;
    @Inject
    EventActionMessages messages;
    @Inject
    EventActionConstants eventActionConstants;

    @Inject
    Processor validation;

    @Inject
    ActionConditionsCreatorFactory factory;

    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    @Inject
    SelectListProperty<String, SelectItem> type;

    ActionConditionCreator creator;
    private ActionConditionWithScript condition;

    @Inject
    public EditActionConditionPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(ActionConditionWithScript value, AsyncCallback<ActionConditionWithScript> callback)
    {
        this.condition = value;
        this.callback = callback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        IProperties properties = creator.getConditionProperties();
        if (null == properties)
        {
            return;
        }
        callback.onSuccess(creator.getCondition());
    }

    protected void initConditionCreator(String code)
    {
        creator = factory.create(ActionConditionType.valueOf(code), condition);
        creator.bindProperties();
        creator.addProperties(getDisplay());
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.editingCondition());
        initTypeProperty(Lists.newArrayList(ConditionType.SCRIPT.name()));
        initConditionCreator(condition.getObject().getType().name());
        getDisplay().display();
    }

    private void initTypeProperty(List<String> types)
    {
        type.setCaption(messages.conditionType());
        type.setValidationMarker(true);
        SingleSelectCellList<String> typeWidget = type.getValueWidget();
        for (String conditionType : types)
        {
            typeWidget.addItem(eventActionConstants.actionConditionsTypes().get(conditionType), conditionType);
        }
        type.setDisable();
        type.trySetObjValue(condition.getObject().getType().name());
        if (types.size() != 1)
        {
            getDisplay().add(type);
        }
    }
}
