package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.USER_INTERFACE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;
import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.logging.Logger;

import com.allen_sauer.gwt.dnd.client.DragContext;
import com.allen_sauer.gwt.dnd.client.DragEndEvent;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.Command;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Widget;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.admin.client.permission.AdminAccessDeniedHandler;
import ru.naumen.admin.client.permission.AdminPermissionHelper;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.client.content.AbstractLayoutContentPresenter;
import ru.naumen.core.client.layout.Band;
import ru.naumen.core.client.layout.Column;
import ru.naumen.core.client.layout.DragAndDropLogic;
import ru.naumen.core.client.layout.model.ContentPos;
import ru.naumen.core.client.layout.model.Rect;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.ClientInfo;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.Position;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.reference.TabReference;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.common.content.FlowPresenter;
import ru.naumen.metainfoadmin.client.common.content.FlowPresenter.Direction;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeLayoutModeEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeUITemplateEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ColumnWidthChangedEvent;
import ru.naumen.metainfoadmin.client.dynadmin.PreColumnWidthChangingEvent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UITemplateContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.AddContentPresenter;

/**
 * {@link Presenter} для контента {@link Layout}.
 *
 * <AUTHOR>
 *
 */
public class LayoutContentPresenter extends AbstractLayoutContentPresenter<LayoutContentDisplay, UIContext>
{
    public interface MoveDragCommand
    {
        void execute(DragEndEvent event);
    }

    public class ResizeColumnCommand implements Command
    {
        private int leftColumnWidth;

        public ResizeColumnCommand()
        {
        }

        @Override
        public void execute()
        {
            onResizeColumn(leftColumnWidth);
        }

        public void preColumnWidthChanging(int preLeftColumnWidth, int preRightColumnWidth)
        {
            getContext().getEventBus()
                    .fireEvent(new PreColumnWidthChangingEvent(preLeftColumnWidth, preRightColumnWidth));
        }

        public void setLeftColumnWidth(int leftColumnWidth)
        {
            this.leftColumnWidth = leftColumnWidth;
        }
    }

    /**
     * <pre>
     *   ________________________________________
     *  | c        1      |  c      3      |     |
     *  |_o_______________|__o_____________|     |
     *  | l        2      |  l             |     |
     *  |_u_______________|  u      4      |     |   horizontalBand1
     *  | m               |  m             |     |
     *  | n        5      |  n             |     |
     *  |_1_______________|__2_____________|  ___|
     *  |                                  |     |
     *  | c               6                |     |        
     *  |_o________________________________|     |   horizontalBand2
     *  | l                                |     |
     *  | 1               7                |     |        
     *  |__________________________________|  ___|
     *  |                 |                |     |                                                   
     *  | c        8      |  c      10     |     |
     *  | o               |__o_____________|     |
     *  | l               |  l             |     |   horizontalBand3
     *  |_1_______________|  2      11     |     |
     *  |          9      |                |     |
     *  |_________________|________________|  ___|
     *  | c                                |     |
     *  | o                                |     |   horizontalBand4
     *  | l               12               |     |    
     *  |_1________________________________|     .
     * </pre>
     *
     * Логика добавления контента:
     *
     * При добавлении виджета, например, слева, создается горизонтальная панель, на неё кладутся левая и правая
     * вертикальные панели. На левую - кладется новый виджет. При добавлении еще одного виджета слева, он кладется на
     * уже имеющуюся левую панель. При добавлении виджета справа, он кладется на правую панель. И так далее.
     *
     * Если мы добавляем виджет на всю ширину, то он кладется напрямую на главную вертикальную панель. Вертикальные
     * панели, созданные до него мы считаем закрытыми: на них уже нельзя добавить другие виджеты. Поэтому, если мы
     * добавляем еще один виджет, то создается новая горизонтальная панель, на неё кладутся левая и правая вертикальные
     * панели; виджет кладется на соответствующую. И так далее.
     */

    class MoveCommand implements Command
    {
        FlowPresenter<FlowContent, UIContext> presenter;
        Direction direction;

        public MoveCommand(FlowPresenter<FlowContent, UIContext> presenter, Direction direction)
        {
            this.presenter = presenter;
            this.direction = direction;
        }

        @Override
        public void execute()
        {
            onMoveContent(presenter, direction);
        }
    }

    class MoveDraggableCommand implements Command, MoveDragCommand
    {
        FlowPresenter<FlowContent, UIContext> presenter;

        public MoveDraggableCommand(FlowPresenter<FlowContent, UIContext> presenter)
        {
            this.presenter = presenter;
        }

        @Override
        public void execute()
        {
            throw new RuntimeException("Never call!");
        }

        @Override
        public void execute(DragEndEvent event)
        {
            onDragAndDrop(presenter, event);
        }
    }

    private static Logger LOG = Logger.getLogger("LayoutContentPresenter");

    //Горизонтальные полосы, располагаемые вертикально
    private final ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands = new ArrayList<>();
    private final LayoutContentPresenterMover presenterMover = new LayoutContentPresenterMover(bands);

    private final Map<FlowContent, FlowPresenter<FlowContent, UIContext>> presenters = new HashMap<>();

    protected final AdminContentFactory factory;
    private final MetainfoModificationServiceAsync metainfoModificationService;
    private final AdminPermissionHelper adminPermissionHelper;
    private final Provider<AddContentPresenter> addContentPresenterProvider;

    @Inject
    public LayoutContentPresenter(LayoutContentDisplay display,
            EventBus eventBus,
            AdminContentFactory factory,
            MetainfoModificationServiceAsync metainfoModificationService,
            AdminPermissionHelper adminPermissionHelper,
            Provider<AddContentPresenter> addContentPresenterProvider)
    {
        super(display, eventBus, "LayoutContent");
        this.factory = factory;
        this.metainfoModificationService = metainfoModificationService;
        this.adminPermissionHelper = adminPermissionHelper;
        this.addContentPresenterProvider = addContentPresenterProvider;
        display.initResizeColumnCommand(new ResizeColumnCommand());
    }

    /**
     * C помощью {@link AddContentPresenter} запрашивает у пользователя всю необходимую информацию о новом контенте и
     * добавляет его.
     */
    public void addContent()
    {
        final AddContentPresenter addPresenter = addContentPresenterProvider.get();
        addPresenter.init(getContext(), getContent());
        addPresenter.bind();
        addPresenter.getDisplay().display();
        addPresenter.focusOnTitleField();
    }

    @SuppressWarnings("unchecked")
    public void onDragAndDrop(FlowPresenter<FlowContent, UIContext> presenter, DragEndEvent event)
    {
        DragContext context = event.getContext();
        DragAndDropLogic<Presenter> dnd = new DragAndDropLogic<>();

        // выходим из режима разметки
        leaveLayoutMode(presenter.getContent());

        // заполняем новую модель
        for (Band<FlowPresenter<FlowContent, UIContext>> band : bands)
        {
            int bandIndex = bands.indexOf(band);
            dnd.setBandColumns(bandIndex, band.getColCount());
            for (Column<FlowPresenter<FlowContent, UIContext>> column : band.getColumns())
            {
                int columnIndex = band.getColumns().indexOf(column);
                for (FlowPresenter<FlowContent, UIContext> p : column.getPresenters())
                {
                    int contentIndex = column.getPresenters().indexOf(p);
                    Widget w = p.getDisplay().asWidget();
                    Rect rect = new Rect(w.getAbsoluteLeft(), w.getAbsoluteTop(), w.getOffsetWidth(),
                            w.getOffsetHeight());
                    dnd.addContentPos(new ContentPos<Presenter>(p, rect, bandIndex, columnIndex, contentIndex),
                            p.getContent().getPosition());
                }
            }
        }

        // выполняем алгоритм перемещения
        dnd.moveContent(presenter, context.mouseX, context.mouseY);

        // переносим новое расположение в список лент
        bands.clear();
        int maxBand = dnd.getMaxBand();
        for (int iBand = 0; iBand <= maxBand; ++iBand)
        {
            Band<FlowPresenter<FlowContent, UIContext>> newBand = new Band<>(
                    dnd.getBandColumns(iBand));
            for (int iCol = 0; iCol < dnd.getBandColumns(iBand); ++iCol)
            {
                int maxContent = dnd.getMaxContent(iBand, iCol);
                for (int iContent = 0; iContent <= maxContent; ++iContent)
                {
                    ContentPos<Presenter> contentPos = dnd.getContentPos(iBand, iCol, iContent);
                    FlowPresenter<FlowContent, UIContext> p = (FlowPresenter<FlowContent, UIContext>)contentPos
                            .getDraggable();
                    p.getContent().setPosition(dnd.getColumnPosition(p));
                    newBand.getColumn(iCol).add(p);
                    p.clearStyles();
                }
            }
            bands.add(newBand);
        }
        updateFlowContentList();
        refreshDisplay();

        ClassFqn fqn = getContext().getMetainfo().getFqn();
        Content rootContent = getContext().getRootContent();
        metainfoModificationService.saveUI(fqn, rootContent, null, getContext().getCode(), null,
                false, new BasicCallback<Void>());
    }

    @Override
    public void onHide()
    {
        LOG.finest("Hide");
        for (Presenter p : presenters.values())
        {
            p.hideDisplay();
        }
    }

    /**
     * При перемещении не осуществляется проверка на неверные входные данные (например, при попытке переместить вверх
     * единственный элемент). Невозможность появления таких входных данных гарантируется методом
     * {@link LayoutContentPresenter#layoutContents()}, который отвечает (в том числе) за добавление кнопок перемещения.
     */
    public void onMoveContent(FlowPresenter<FlowContent, UIContext> presenter, Direction direction)
    {
        adminPermissionHelper.runWithCheckPermissionByAccessMarker(USER_INTERFACE, EDIT, adminAccessDeniedHandler, () ->
        {
            FlowContent content = presenter.getContent();
            presenter.clearStyles();
            switch (direction)
            {
                case TO_LEFT:
                    presenterMover.moveToColumn(presenter, 0);
                    content.setPosition(Position.LEFT);
                    break;
                case TO_RIGHT:
                    presenterMover.moveToColumn(presenter, 1);
                    content.setPosition(Position.RIGHT);
                    break;
                case UP:
                    presenterMover.moveUp(presenter);
                    break;
                case DOWN:
                    presenterMover.moveDown(presenter);
                    break;
                case WIDE:
                    content.setPosition(Position.FULL);
                    break;
                case LEFT:
                    content.setPosition(Position.LEFT);
                    break;
                case RIGHT:
                    content.setPosition(Position.RIGHT);
                    break;
                case DRAG_AND_DROP:
                    break;
                default:
                    throw new FxException("Unsupported enum Direction value:" + direction);
            }

            // выходим из режима разметки
            leaveLayoutMode(content);

            updateFlowContentList();
            refreshDisplay();
            presenter.refreshDisplay();

            ClassFqn fqn = getContext().getMetainfo().getFqn();
            Content rootContent = getContext().getRootContent();
            metainfoModificationService.saveUI(fqn, rootContent, null, getContext().getCode(), null,
                    false, new BasicCallback<Void>());
        });
    }

    private final AdminAccessDeniedHandler adminAccessDeniedHandler = new AdminAccessDeniedHandler()
    {
        @Override
        public void onAccessDenied(PermissionType permissionType)
        {
            if (EDIT.equals(permissionType))
            {
                adminPermissionHelper.showAccessMarkerDeniedError();
            }
        }
    };

    /**
     * Сохраняет ширину левого столба в процентах
     */
    public void onResizeColumn(int leftColumnWidth)
    {
        getContent().setLeftColumnWidth(leftColumnWidth);
        ClassFqn fqn = getContext().getMetainfo().getFqn();
        if (null == getContext().getUITemplateContext())
        {
            Content rootContent = getContext().getRootContent();
            metainfoModificationService.saveUI(fqn, rootContent, null, getContext().getCode(),
                    getContent().getParent(), false, new BasicCallback<>());
        }
        else if (getContent().getParent() instanceof Tab)
        {
            UITemplateContext templateContext = getContext().getUITemplateContext();
            TabReference tabReference = templateContext.getTemplateReference(getContent().getParent());
            if (null == tabReference.getLayout())
            {
                Layout layoutReference = new Layout();
                layoutReference.setUuid(getContent().getUuid());
                layoutReference.setParent(tabReference);
                tabReference.setLayout(layoutReference);
            }
            tabReference.getLayout().setLeftColumnWidth(getContent().getLeftColumnWidth());
            getContext().getEventBus().fireEvent(new ChangeUITemplateEvent(true));
        }
        getContext().getEventBus().fireEvent(new ColumnWidthChangedEvent());
    }

    @Override
    public void onReveal()
    {
        LOG.finest("Reveal");
        for (Presenter p : presenters.values())
        {
            p.revealDisplay();
        }
    }

    @Override
    public void refreshDisplay()
    {
        layoutContents();
        display.setSplitterEnabled(hasPermission(context, PermissionType.EDIT, content.getParent()));
        display.drawContent(bands, content.getLeftColumnWidth(), getContent().isWindowLayout());
        if (null != bands && !bands.isEmpty())
        {
            applyStyles(bands.get(0));
        }
    }

    public void setLayoutModeOffset()
    {
        display.setLayoutModeOffset();
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        registerHandler(Window.addResizeHandler(event -> Scheduler.get().scheduleDeferred(
                () -> getDisplay().moveSplitterBetweenColumns(false))));
        registerHandler(getContext().getEventBus().addHandler(ChangeLayoutModeEvent.getType(),
                e -> getDisplay().enableLayoutMode(e.isLayoutModeEnabled() && getContent().equals(e.getLayout()))));

        refreshDisplay();
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        for (Presenter p : presenters.values())
        {
            p.unbind();
        }
    }

    /**
     * Превращает список лент в список контентов
     * При переборе контенты также очищаются от стилей(чтоб не появлялось 2 голубых контента)
     */
    private List<FlowContent> convertToList(ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands)
    {
        List<FlowContent> list = new ArrayList<>();

        for (Band<FlowPresenter<FlowContent, UIContext>> band : bands)
        {
            for (Column<FlowPresenter<FlowContent, UIContext>> column : band.getColumns())
            {
                for (FlowPresenter<FlowContent, UIContext> presenter : column.getPresenters())
                {
                    presenter.clearStyles();
                    list.add(presenter.getContent());
                }
            }
        }
        return list;
    }

    private FlowPresenter<FlowContent, UIContext> createContentPresenter(FlowContent content)
    {
        final FlowPresenter<FlowContent, UIContext> presenter = factory.build(content, getContext());
        presenters.put(content, presenter);
        if (isRevealed)
        {
            presenter.revealDisplay();
        }
        return presenter;
    }

    /**
     * Возвращает последнюю ленту, если она подходит по размеру (по количеству колонок), или создает новую.
     *
     * @return В любом случае возвращет ленту
     */
    private Band<FlowPresenter<FlowContent, UIContext>> getLastProperBand(int size)
    {
        if (bands.isEmpty())
        {
            Band<FlowPresenter<FlowContent, UIContext>> band = new Band<>(size);
            bands.add(band);
            return band;
        }
        Band<FlowPresenter<FlowContent, UIContext>> band = bands.get(bands.size() - 1);
        if (band.getColCount() == size)
        {
            return band;
        }
        band = new Band<>(size);
        bands.add(band);
        return band;
    }

    /**
     * Располагает одиночный элемент. См. {@link LayoutContentPresenter#layoutContents()}.
     */
    private void layoutContent(final FlowPresenter<FlowContent, UIContext> presenter, FlowContent content)
    {
        presenter.removeAllDirections();
        Position position = content.getPosition();
        if (null == position)
        {
            position = Position.FULL;
        }
        Band<FlowPresenter<FlowContent, UIContext>> band;

        presenter.addDirection(Direction.UP, new MoveCommand(presenter, Direction.UP));
        presenter.addDirection(Direction.DOWN, new MoveCommand(presenter, Direction.DOWN));

        switch (position)
        {
            case LEFT:
                presenter.addDirection(Direction.TO_RIGHT, new MoveCommand(presenter, Direction.TO_RIGHT));
                presenter.addDirection(Direction.WIDE, new MoveCommand(presenter, Direction.WIDE));
                band = getLastProperBand(2);
                band.getColumn(0).add(presenter);
                break;
            case RIGHT:
                presenter.addDirection(Direction.TO_LEFT, new MoveCommand(presenter, Direction.TO_LEFT));
                presenter.addDirection(Direction.WIDE, new MoveCommand(presenter, Direction.WIDE));
                band = getLastProperBand(2);
                band.getColumn(Math.min(band.getColCount() - 1, 1)).add(presenter);
                break;
            case FULL:
                presenter.addDirection(Direction.TO_LEFT, new MoveCommand(presenter, Direction.TO_LEFT));
                presenter.addDirection(Direction.RIGHT, new MoveCommand(presenter, Direction.RIGHT));
                band = getLastProperBand(1);
                band.getColumn(0).add(presenter);
                break;
            default:
                throw new RuntimeException("Handling of enum Position." + position + "is not implemented");
        }

        // добавляем команду для Drag & Drop
        presenter.addDirection(Direction.DRAG_AND_DROP, new MoveDraggableCommand(presenter));
    }

    /**
     * Располагает контенты по полосам и колонкам, получая из списка FlowContent'ов список HorizontalBand. В процессе
     * расположения у соответствующим {@link ru.naumen.metainfoadmin.client.common.content.FlowContentDisplay дисплеям}
     * добавляются необходимые кнопки перемещения.
     */
    private void layoutContents()
    {
        bands.clear();

        List<FlowContent> contents = getContent().getContent();
        for (FlowContent content : contents)
        {
            FlowPresenter<FlowContent, UIContext> presenter = presenters.get(content);
            if (presenter == null)
            {
                presenter = createContentPresenter(content);
            }
            else
            {
                // ибо у RelObjPropertyList и ClientInfo свой собственный контекст, не связанный с текущим
                if (!(presenter.getContent() instanceof RelObjPropertyList
                      || presenter.getContent() instanceof ClientInfo))
                {
                    /* TODO Переделать на ContextChangedEvent, пусть изменение контекста
                        обрабатывают только те контенты, кому надо, мы не можем формировать
                        список исключений бесконечно
                        P.S. Вообще не понятно зачем инициализация нужна в методе refreshDisplay
                        (из которого вызывается этот метод) */
                    presenter.init(content, context);
                }
                presenter.refreshDisplay();
            }
            layoutContent(presenter, content);
        }
        //убираем кнопки(пункты меню) перемещения у самых верхних и самых нижних элементов.
        //для этого берем верхнюю ленту и у первого элемента каждой колонки убираем кнопку "вверх"
        if (!bands.isEmpty())
        {
            Band<FlowPresenter<FlowContent, UIContext>> band = bands.get(0);
            for (Column<FlowPresenter<FlowContent, UIContext>> col : band.getColumns())
            {
                if (col.getFirst() != null)
                {
                    col.getFirst().removeDirection(Direction.UP);
                }
            }
            //аналогично для нижней полосы
            band = bands.get(bands.size() - 1);
            for (Column<FlowPresenter<FlowContent, UIContext>> col : band.getColumns())
            {
                if (col.getLast() != null)
                {
                    col.getLast().removeDirection(Direction.DOWN);
                }
            }
        }

        // удаляем presenter-ы контентов более не присутствующих в списке контентов
        List<FlowContent> forRemove = new ArrayList<>();
        for (Entry<FlowContent, FlowPresenter<FlowContent, UIContext>> e : presenters.entrySet())
        {
            if (contents.contains(e.getKey()))
            {
                continue;
            }
            e.getValue().unbind();
            forRemove.add(e.getKey());
        }
        for (FlowContent cnt : forRemove)
        {
            presenters.remove(cnt);
        }
    }

    private void leaveLayoutMode(FlowContent content)
    {
        if (getContext().getContentInLayoutEditMode() != null && content instanceof TabBar
            && ((TabBar)content).getTab().contains(getContext().getContentInLayoutEditMode().getParent()))
        {
            getContext().getEventBus().fireEvent(new ChangeLayoutModeEvent(getContent(), false));

        }
    }

    /**
     * Обновляет состояние списка контентов. После операций перемещения этот список может перестать соответствовать
     * списку лент. Поэтому после этих операций необходимо вызывать этот метод.
     */
    private void updateFlowContentList()
    {
        List<FlowContent> content = getContent().getContent();
        content.clear();
        content.addAll(convertToList(bands));
    }
}
