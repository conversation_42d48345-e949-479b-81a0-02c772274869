package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import java.util.ArrayList;

import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.tags.dispatch.GetTagsAction;
import ru.naumen.metainfo.shared.tags.dispatch.GetTagsResult;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;

/**
 * Делегат биндинга свойства "Метки" элемента левого меню
 * <AUTHOR>
 * @since 14.04.2021
 */
public class MenuSettingsTagsBindDelegateImpl extends PropertyDelegateBindImpl<Collection<SelectItem>, TagsProperty>
{
    private final DispatchAsync dispatch;
    private final MetainfoUtils metainfoUtils;

    @Inject
    public MenuSettingsTagsBindDelegateImpl(DispatchAsync dispatch,
            MetainfoUtils metainfoUtils)
    {
        this.dispatch = dispatch;
        this.metainfoUtils = metainfoUtils;
    }

    @Override
    public void bindProperty(PropertyContainerContext context, TagsProperty property,
            AsyncCallback<Void> callback)
    {
        dispatch.execute(new GetTagsAction(), new BasicCallback<GetTagsResult>()
        {
            @Override
            protected void handleSuccess(GetTagsResult result)
            {
                property.getValueWidget().clear();
                List<DtObject> tags = result.getTags();
                metainfoUtils.sort(tags);
                property.fillTagsProperty(tags);

                List<String> value = context.getPropertyValues().getProperty(MenuSettingsPropertyCode.TAGS,
                        new ArrayList<>());
                property.trySetObjValue(value);
                callback.onSuccess(null);
            }
        });
    }
}