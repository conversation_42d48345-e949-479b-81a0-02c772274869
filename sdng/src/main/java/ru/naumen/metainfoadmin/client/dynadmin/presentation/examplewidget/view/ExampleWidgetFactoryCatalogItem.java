package ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.view;

import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.inject.Singleton;

import ru.naumen.core.client.attr.presentation.PresentationContext;

/**
 * <AUTHOR>
 * @since 31 янв. 2014 г.
 *
 */
@Singleton
public class ExampleWidgetFactoryCatalogItem<T> extends ExampleWidgetFactoryView<T>
{
    @Override
    public IsWidget createExampleWidget(PresentationContext context)
    {
        String presentation = context.getPresentationCode();
        T objs = exampleValueFactories.<T> getFactory(presentation).create(context.getAttribute());
        return new HTML( // NOPMD NSDPRD-28509 unsafe html
                htmlFactories.<T> getFactory(presentation).create(context, objs)); // NOPMD NSDPRD-28509 unsafe html
    }
}