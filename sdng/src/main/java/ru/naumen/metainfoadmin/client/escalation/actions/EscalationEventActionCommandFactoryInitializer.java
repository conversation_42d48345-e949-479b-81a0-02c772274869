package ru.naumen.metainfoadmin.client.escalation.actions;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;

import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 01.08.2012
 *
 */
@Singleton
public class EscalationEventActionCommandFactoryInitializer
{
    @Inject
    public EscalationEventActionCommandFactoryInitializer(
            CommandFactory factory,
            CommandProvider<AddEscalationEventActionCommand, CommandParam<EventActionWithScript,
                    EventActionWithScript>> addProvider,
            CommandProvider<EditEscalationEventActionCommand, CommandParam<EventActionWithScript,
                    EventActionWithScript>> editProvider)
    {
        // @formatter:off
        factory.register(EscalationActionsGinModule.ADD_ESCALATION_EVENT_ACTION, addProvider);
        factory.register(EscalationActionsGinModule.EDIT_ESCALATION_EVENT_ACTION, editProvider);
        // @formatter:on
    }
}
