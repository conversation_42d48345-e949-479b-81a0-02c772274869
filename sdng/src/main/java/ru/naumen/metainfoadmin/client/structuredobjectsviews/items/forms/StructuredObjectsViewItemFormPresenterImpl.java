package ru.naumen.metainfoadmin.client.structuredobjectsviews.items.forms;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassSameClassOnlyTreeContext;
import ru.naumen.core.client.tree.metainfo.MetaClassMultiSelectionModelSameClassOnly;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.tree.view.ITreeViewModel;
import ru.naumen.core.client.validation.MetainfoKeyCodeStructuredItemValidator;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.properties.condition.ConditionProperty;
import ru.naumen.core.client.widgets.properties.sort.SortProperty;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.HasReadyState.SynchronizationCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.ui.element.PermittedCasesContainer;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.StructuredObjectsView;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosAction;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosResponse;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.StructuredObjectsViewItemClient;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListSort;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseContentCreator;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewHelper;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsMessages;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;

/**
 * Базовая форма элемента структуры
 * <AUTHOR>
 * @since 24.10.2019
 */
public abstract class StructuredObjectsViewItemFormPresenterImpl extends OkCancelPresenter<PropertyDialogDisplay>
{
    private final static String CLASS_FQNS = "classFqns";

    private static void getVisibleChildItems(List<StructuredObjectsViewItemClient> items,
            @Nullable StructuredObjectsViewItemClient item, List<StructuredObjectsViewItemClient> result)
    {
        items.stream().filter(i -> null == item || !item.getCode().equals(i.getCode())).forEach(i ->
        {
            result.add(i);
            getVisibleChildItems(i.getChildren(), item, result);
        });
    }

    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> code;
    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    protected SelectListProperty<String, SelectItem> parent;
    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    protected SelectListProperty<String, SelectItem> relAttr;
    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    protected SelectListProperty<String, SelectItem> attrGroup;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> showNested;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> showName;
    @Inject
    protected Processor validation;
    @Inject
    protected DispatchAsync service;
    @Inject
    protected StructuredObjectsViewsMessages structuredObjectsViewsMessages;
    @Inject
    private PopupValueCellTreeFactory<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly> treeFactory;
    @Inject
    private DtoMetaClassesTreeFactory<MetaClassMultiSelectionModelSameClassOnly,
            DtoMetaClassSameClassOnlyTreeContext> treeModelHelper;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    private NotEmptyCollectionValidator<Collection<DtObject>> notEmptyCollectionValidator;
    @Inject
    private MetainfoKeyCodeStructuredItemValidator metainfoKeyCodeItemValidator;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private StructuredObjectsViewHelper structuredObjectsViewHelper;
    @Inject
    private AdminMetainfoServiceAsync adminMetainfoServiceAsync;
    @Inject
    private ObjectFilterPropertyFactory objectFilterPropertyFactory;
    @Inject
    private DefaultSortPropertyFactory defaultSortPropertyFactory;
    @Inject
    private TagsMessages tagsMessages;

    private Property<Collection<DtObject>> classFqn;
    private ConditionProperty objectFilter;
    private SortProperty defaultSort;
    private ClassFqn currentFqnOfClass;
    private final ItemViewSettingsContext itemViewContext = new ItemViewSettingsContext();
    private final ReadyState fieldReadyState = new ReadyState(this);
    protected AsyncCallback<DtObject> refreshCallback;
    protected DtObject structuredObjectsView;
    protected StructuredObjectsViewItemClient structuredObjectsViewItem;

    public StructuredObjectsViewItemFormPresenterImpl(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(DtObject structuredObjectsView,
            @Nullable StructuredObjectsViewItemClient structuredObjectsViewItem,
            AsyncCallback<DtObject> refreshCallback)
    {
        this.structuredObjectsView = structuredObjectsView;
        itemViewContext.setDisabledAttributes(structuredObjectsViewItem == null ? Collections.emptyList() :
                structuredObjectsViewItem.getDisabledAttributes());
        this.structuredObjectsViewItem = structuredObjectsViewItem;
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void onApply()
    {
        fillValues();
    }

    @Override
    public void refreshDisplay()
    {
    }

    protected void bindProperties()
    {
        title.setCaption(cmessages.title());
        title.setValidationMarker(true);
        title.setMaxLength(Constants.MAX_METAINFO_TITLE_LENGTH);
        validation.validate(title, notEmptyValidator);
        DebugIdBuilder.ensureDebugId(title, "title");
        getDisplay().add(title);

        code.setCaption(cmessages.code());
        code.setValidationMarker(true);
        code.setMaxLength(Constants.MAX_METAINFO_KEY_LENGTH);
        validation.validate(code, notEmptyValidator);
        validation.validate(code, metainfoKeyCodeItemValidator);
        DebugIdBuilder.ensureDebugId(code, "code");
        getDisplay().add(code);

        parent.setCaption(structuredObjectsViewsMessages.parentItem());
        parent.addValueChangeHandler(event ->
        {
            updateRelAttrProperty(null);
            if (structuredObjectsViewItem != null && !structuredObjectsViewItem.getChildren().isEmpty())
            {
                display.addAttentionMessage(structuredObjectsViewsMessages.attentionAllNestedItemsWillBeMoved());
            }
        });
        DebugIdBuilder.ensureDebugId(parent, "parent");
        getDisplay().add(parent);
        updateParentProperty();

        ITreeViewModel<DtObject, MetaClassMultiSelectionModelSameClassOnly> treeViewModel = treeModelHelper
                .createMetaClassTreeViewModel(Container.create(new DtoMetaClassSameClassOnlyTreeContext(AbstractBO.FQN,
                        MetaClassFilters.and(MetaClassFilters.isNotSystem(), MetaClassFilters.not(MetaClassFilters
                                .equal(Root.FQN))))));
        PopupValueCellTree<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly> cellTree =
                treeFactory
                        .create(treeViewModel);
        classFqn = new PropertyBase<>(cmessages.objects(), cellTree);
        classFqn.setValidationMarker(true);
        validation.validate(classFqn, notEmptyCollectionValidator);
        classFqn.addValueChangeHandler(event ->
        {
            ClassFqn newFqnOfClass = getFqnOfClass();
            boolean classChanged = !ObjectUtils.equals(newFqnOfClass, currentFqnOfClass);
            currentFqnOfClass = newFqnOfClass;
            updateAttrGroupProperty(null);
            updateRelAttrProperty(null);
            updateItemView(classChanged ? new ListFilter() : null, classChanged ? new ListSort() : null);
        });
        DebugIdBuilder.ensureDebugId(classFqn, "classFqn");
        getDisplay().add(classFqn);

        relAttr.setCaption(cmessages.attributeLink());
        DebugIdBuilder.ensureDebugId(relAttr, "relAttr");
        getDisplay().add(relAttr);

        attrGroup.setCaption(cmessages.attributeGroup());
        DebugIdBuilder.ensureDebugId(attrGroup, "attrGroup");
        getDisplay().add(attrGroup);

        showNested.setCaption(structuredObjectsViewsMessages.showNested());
        DebugIdBuilder.ensureDebugId(showNested, "showNested");
        getDisplay().add(showNested);

        objectFilter = objectFilterPropertyFactory.create(itemViewContext);
        objectFilter.getValueWidget().setAttentionMessage(tagsMessages.disabledInRestrictionContentStructureElement());
        objectFilter.setCaption(structuredObjectsViewsMessages.objectFilter());
        DebugIdBuilder.ensureDebugId(objectFilter, "objectFilter");
        getDisplay().add(objectFilter);

        defaultSort = defaultSortPropertyFactory.create(itemViewContext);
        defaultSort.setCaption(structuredObjectsViewsMessages.defaultSort());
        DebugIdBuilder.ensureDebugId(defaultSort, "defaultSort");
        getDisplay().add(defaultSort);

        showName.setCaption(structuredObjectsViewsMessages.showName());
        DebugIdBuilder.ensureDebugId(showName, "showName");
        getDisplay().add(showName);
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        fieldReadyState.registerSynchronization(new SynchronizationCallback(this)
        {
            @Override
            public void error()
            {
                getDisplay().stopProcessing();
            }

            @Override
            public void notReady()
            {
                getDisplay().startProcessing();
            }

            @Override
            public void ready()
            {
                getDisplay().stopProcessing();
            }
        });

        bindProperties();

        if (structuredObjectsViewItem != null)
        {
            code.setValue(structuredObjectsViewItem.getCode());
            title.setValue(structuredObjectsViewItem.getTitle());
            if (null != structuredObjectsViewItem.getParent())
            {
                parent.trySetObjValue(structuredObjectsViewItem.getParent().getCode(), false);
            }
            Collection<ClassFqn> fqns = structuredObjectsViewItem.getClassFqns();
            classFqn.setValue(CollectionUtils.transform(fqns, DtObject.CREATE_FROM_FQN), false);
            currentFqnOfClass = getFqnOfClass();
            showNested.setValue(structuredObjectsViewItem.isShowNested());
            showName.setValue(structuredObjectsViewItem.isShowName());

            updateAttrGroupProperty(structuredObjectsViewItem.getAttrGroupCode());
            if (null != structuredObjectsViewItem.getRelAttrFqn())
            {
                updateRelAttrProperty(structuredObjectsViewItem.getRelAttrFqn().toString());
            }
            updateItemView(structuredObjectsViewItem.getObjectFilter(), structuredObjectsViewItem.getDefaultSort());
        }
        else
        {
            updateItemView(null, null);
        }

        registerHandler(structuredObjectsViewHelper.addTitleToCodeHandler(title, code));
    }

    private void fillValues()
    {
        String relAttrValue = SelectListPropertyValueExtractor.getValue(relAttr);
        structuredObjectsViewItem = new StructuredObjectsViewItemClient(title.getValue(), code.getValue(),
                SelectListPropertyValueExtractor.getValue(parent),
                getFqns(), null != relAttrValue ? AttributeFqn.parse(relAttrValue) : null,
                SelectListPropertyValueExtractor.getValue(attrGroup), showNested.getValue(),
                null != structuredObjectsViewItem ? structuredObjectsViewItem.getChildren() : null,
                showName.getValue());
        structuredObjectsViewItem.setObjectFilter(objectFilter.getValue());
        structuredObjectsViewItem.setDefaultSort(defaultSort.getValue());
    }

    @Nullable
    private ClassFqn getFqnOfClass()
    {
        List<ClassFqn> fqns = getFqns();
        return fqns.isEmpty() ? null : fqns.iterator().next().fqnOfClass();
    }

    private List<ClassFqn> getFqns()
    {
        return null == classFqn.getValue() ? new ArrayList<>()
                : classFqn.getValue().stream().map(DtObject::getMetainfo).collect(Collectors.toList());
    }

    private ListSort processDefaultSort(@Nullable ListSort sortValue, Map<String, Attribute> attributes)
    {
        if (null == sortValue)
        {
            return new ListSort();
        }
        ListSort sort = ObjectUtils.clone(sortValue);
        sort.getElements().removeIf(sortElement -> !attributes.containsKey(sortElement.getAttrCode()));
        return sort;
    }

    private ListFilter processObjectFilter(@Nullable ListFilter filterValue, Map<String, Attribute> attributes)
    {
        if (null == filterValue)
        {
            return new ListFilter();
        }
        ListFilter filter = ObjectUtils.clone(filterValue);
        filter.getElements().forEach(andElement ->
                andElement.getElements().removeIf(orElement -> !attributes.containsKey(orElement.getAttributeFqn())));
        filter.getElements().removeIf(andElement -> andElement.getElements().isEmpty());
        return filter;
    }

    private void updateAttrGroupProperty(@Nullable String attrGroupCode)
    {
        if (getFqns().isEmpty())
        {
            attrGroup.<SingleSelectCellList<SelectItem>> getValueWidget().clear();
            attrGroup.clearValue();
            attrGroup.<SingleSelectCellList<SelectItem>> getValueWidget().refreshPopupCellList();
            return;
        }
        dispatch.execute(new GetAttributeGroupInfosAction(getFqns()),
                new BasicCallback<GetAttributeGroupInfosResponse>(fieldReadyState)
                {
                    @Override
                    protected void handleSuccess(GetAttributeGroupInfosResponse response)
                    {
                        String value = null != attrGroupCode
                                ? attrGroupCode
                                : SelectListPropertyValueExtractor.getValue(attrGroup);
                        attrGroup.<SingleSelectCellList<SelectItem>> getValueWidget().clear();
                        attrGroup.clearValue();
                        if (response.getGroupInfos().isEmpty())
                        {
                            return;
                        }
                        Map<String, String> visibleGroups = ObjectListBaseContentCreator.getVisibleAttrGroups(response
                                .getGroupInfos(), getFqns(), true);
                        List<String> codes = visibleGroups.entrySet()
                                .stream()
                                .sorted(Map.Entry.comparingByValue())
                                .map(Entry::getKey)
                                .collect(Collectors.toList());
                        attrGroup.<SingleSelectCellList<SelectItem>> getValueWidget().setHasEmptyOption(true);
                        codes.forEach(code -> attrGroup.<SingleSelectCellList<SelectItem>> getValueWidget()
                                .addItem(visibleGroups.get(code), code));
                        attrGroup.trySetObjValue(value);
                        attrGroup.<SingleSelectCellList<SelectItem>> getValueWidget().refreshPopupCellList();
                    }
                });
    }

    private void updateItemView(@Nullable ListFilter filterValue, @Nullable ListSort sortValue)
    {
        List<ClassFqn> fqns = getFqns();
        PermittedCasesContainer container = fqns.isEmpty() ? null : new PermittedCasesContainer(fqns);
        if (null == container)
        {
            itemViewContext.update(null, new ArrayList<>());

            objectFilter.getValueWidget().updateAttributes(new HashMap<>());
            objectFilter.setValue(new ListFilter());
            objectFilter.setEnabled(false);

            defaultSort.setValue(new ListSort());
            defaultSort.setEnabled(false);
        }
        else
        {
            adminMetainfoServiceAsync.getAttributesForCases(container,
                    new BasicCallback<List<Attribute>>(fieldReadyState)
                    {
                        @Override
                        protected void handleSuccess(List<Attribute> value)
                        {
                            itemViewContext.update(container, value);
                            Map<String, Attribute> attributeMap = new HashMap<>();
                            value.forEach(attribute -> attributeMap.put(attribute.getFqn().toString(), attribute));

                            objectFilter.getValueWidget()
                                    .getTextFactory()
                                    .setDisabledByTags(itemViewContext.getDisabledAttributes());
                            objectFilter.getValueWidget().updateAttributes(attributeMap);
                            ListFilter currentFilter = null == filterValue ? objectFilter.getValue() : filterValue;
                            ListFilter listFilter = processObjectFilter(currentFilter, attributeMap);
                            objectFilter.setValue(listFilter);

                            ListSort currentSort = null == sortValue ? defaultSort.getValue() : sortValue;
                            ListSort listSort = processDefaultSort(currentSort, attributeMap);
                            defaultSort.setValue(listSort);
                        }
                    });
            fieldReadyState.ready(new ReadyCallback(this)
            {
                @Override
                public void onReady()
                {
                    Scheduler.get().scheduleDeferred(() ->
                    {
                        objectFilter.setEnabled(true);
                        defaultSort.setEnabled(true);
                    });
                }
            });
        }
    }

    private void updateParentProperty()
    {
        List<StructuredObjectsViewItemClient> items = new ArrayList<>();
        List<StructuredObjectsViewItemClient> itemsFromStructure = structuredObjectsView.getProperty(
                StructuredObjectsView.ITEMS);
        if (!(itemsFromStructure.isEmpty()))
        {
            getVisibleChildItems(itemsFromStructure, structuredObjectsViewItem, items);
        }

        parent.<SingleSelectCellList<SelectItem>> getValueWidget().setHasEmptyOption(true);
        items.forEach(item -> parent.<SingleSelectCellList<SelectItem>> getValueWidget().addItem(item.getTitle(), item
                .getCode(), true, true, 0, null).setProperty(CLASS_FQNS, item.getClassFqns()));
    }

    private void updateRelAttrProperty(@Nullable String relAttrFqn)
    {
        if (getFqns().isEmpty())
        {
            relAttr.<SingleSelectCellList<SelectItem>> getValueWidget().clear();
            relAttr.clearValue();
            relAttr.<SingleSelectCellList<SelectItem>> getValueWidget().refreshPopupCellList();
            return;
        }
        adminMetainfoServiceAsync.getCommonParent(getFqns(), new BasicCallback<MetaClass>(fieldReadyState)
        {
            @Override
            protected void handleSuccess(MetaClass commonClass)
            {
                String value = null != relAttrFqn ? relAttrFqn : SelectListPropertyValueExtractor.getValue(relAttr);
                relAttr.<SingleSelectCellList<SelectItem>> getValueWidget().clear();
                relAttr.clearValue();
                Map<AttributeFqn, Attribute> attrs = new HashMap<>();
                commonClass.getAttributes().stream().filter(attr ->
                {
                    boolean result = Constants.LINK_ATTRIBUTE_TYPES.contains(attr.getType().getCode())
                                     && !attr.isComputable() && !attr.getType().isAttributeOfRelatedObject()
                                     && !Employee.DIRECT_HEAD.equals(attr.getCode());
                    if (null != parent.getValue() && result)
                    {
                        ClassFqn relationFqn = attr.getType().<ObjectAttributeType> cast().getRelatedMetaClass();
                        Collection<ClassFqn> fqns = parent.getValue().getProperty(CLASS_FQNS);
                        return fqns.stream().anyMatch(fqn -> fqn.getId().equals(relationFqn.getId()));
                    }
                    return result;
                }).forEach(attr -> attrs.put(attr.getFqn(), attr));
                relAttr.<SingleSelectCellList<SelectItem>> getValueWidget().setHasEmptyOption(true);
                attrs.values().forEach(attr -> relAttr.<SingleSelectCellList<SelectItem>> getValueWidget()
                        .addItem(attr.getTitle(), attr.getFqn().toString()));
                relAttr.trySetObjValue(value);
                relAttr.<SingleSelectCellList<SelectItem>> getValueWidget().refreshPopupCellList();
            }
        });
    }
}
