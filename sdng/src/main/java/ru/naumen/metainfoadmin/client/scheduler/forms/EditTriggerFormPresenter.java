package ru.naumen.metainfoadmin.client.scheduler.forms;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.scheduler.TriggerType;

/**
 * <AUTHOR>
 *
 */
public class EditTriggerFormPresenter extends TriggerFormPresenter
{
    @Inject
    public EditTriggerFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.editingTrigger());
        SingleSelectCellList<?> selList = type.getValueWidget();
        selList.addItem(factory.getTypeTitle(trigger.get().getType().toString()), trigger.get().getType().toString());
        type.setDisable();
        ((ListBoxWithEmptyOptProperty)type).trySetObjValue(trigger.get().getType().toString());
        if (ObjectUtils.equals(trigger.get().getType().toString(), TriggerType.PERIODIC.toString()))
        {
            getDisplay().setFixed(false);
        }
        getDisplay().display();
    }
}
