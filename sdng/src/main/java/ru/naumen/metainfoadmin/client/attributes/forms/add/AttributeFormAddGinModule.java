/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.forms.add;

import ru.naumen.core.client.attr.AddFormAvailibleTypesProvider;
import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.metainfo.shared.dispatch2.AddAttributeAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormGinModule;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormPropertyControllerFactorySelectorImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeOfRelatedObjectPropertyControllerImpl;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.Key;
import com.google.inject.TypeLiteral;

/**
 * У классов стоит FQN, т.к. import xxx.* вызывает положительное срабатывание PMD, если текущий пакет - xxx.aaa
 * http://sourceforge.net/p/pmd/bugs/1078/
 * <AUTHOR>
 * @since 28.06.2013
 *
 */
public class AttributeFormAddGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        //@formatter:off
        install(PropertyControllerGinModule.create(Attribute.class, ObjectFormAdd.class)
                .setPropertyControllerFactory(new TypeLiteral<AttributeFormPropertyControllerFactorySelectorImpl<ObjectFormAdd>>(){})
                .setPropertyParametersDescriptorFactory(
                        new TypeLiteral<AddAttributeFormPropertyParametersDescriptorFactoryImpl<ObjectFormAdd>>(){}));
        
        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, AttributeOfRelatedObjectPropertyControllerImpl.class)
                .build(Key.get(new TypeLiteral<PropertyControllerSyncFactoryInj<RelationsAttrTreeObject,
                        PropertyBase<RelationsAttrTreeObject,
                        PopupValueCellTree<RelationsAttrTreeObject,
                               RelationsAttrTreeObject,
                               FilteredSingleSelectionModel<RelationsAttrTreeObject>>>>>(){})));
        
        install(AttributeFormGinModule.create(ObjectFormAdd.class)
                .setPropertyControllerFactorySync(new TypeLiteral<AddAttributeFormPropertyControllerFactorySyncImpl>(){})
                .setAfterBindHandler(new TypeLiteral<AddAttributeFormAfterBindHandlerImpl<ObjectFormAdd>>(){})
                .setContextPropertiesSetter(new TypeLiteral<AddAttributeFormContextPropertiesSetterImpl<ObjectFormAdd>>(){})
                .setApplyFormHandler(AttributeFormApplyHandlerAddImpl.class)
                .setMessages(AddAttributeFormMessages.class)
                .setConstants(AddAttributeFormConstants.class)
                .setAction(AddAttributeAction.class)
                .setAttrService(AddFormAvailibleTypesProvider.class));
        //@formatter:on
    }
}