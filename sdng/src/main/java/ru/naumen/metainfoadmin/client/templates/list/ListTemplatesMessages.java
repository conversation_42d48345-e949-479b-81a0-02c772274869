package ru.naumen.metainfoadmin.client.templates.list;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 *
 * <AUTHOR>
 */
@DefaultLocale("ru")
public interface ListTemplatesMessages extends Messages
{
    String addingTemplate();

    String addTemplate();

    @Description("применить изменения")
    String apply();

    @Description("Применить изменения ко всем спискам")
    String applyForAllList();

    @Description("Настройки шаблона были успешно скопированы.")
    String applySuccessfully();

    String backToTemplates();

    String copyToTemplate();

    String creationDate();

    String editingParametersTemplate();

    String editingTemplate();

    @Description("Используется в списках")
    String isUsedInList();

    String lastModifiedDate();

    @Description("Вы действительно хотите удалить шаблон списков {}?")
    String listTemplateConfirmDelete(String title);

    @Description("Вы действительно хотите удалить выбранные шаблоны списков?")
    String listTemplateConfirmMassDelete();

    String parametersList();

    String settingList();

    String template();

    @Description("Вы действительно хотите удалить связь настроек шаблона со списком {}?")
    String usageListTemplateConfirmBreakLink(String title);

    @Description("Вы действительно хотите удалить все выбранные связи настроек шаблона со списками?")
    String usageListTemplateConfirmMassBreakLink();
}
