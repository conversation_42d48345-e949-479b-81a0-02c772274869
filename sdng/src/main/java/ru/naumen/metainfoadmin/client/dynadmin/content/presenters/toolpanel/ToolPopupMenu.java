package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import jakarta.inject.Inject;

import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.ui.MenuItem;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.metainfo.shared.ui.AdvlistPrsSelectTool;
import ru.naumen.metainfo.shared.ui.AttributeToolPanel;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.SearchBoxTool;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.ToolBar;

/**
 * Контекстное меню для тулов
 * <AUTHOR>
 * @since 29 мая 2015 г.
 *
 */
public class ToolPopupMenu extends AttributeToolPopupMenuBase<Tool>
{
    /**
     *  Пункт контекстного меню "Редактировать"
     */
    private MenuItem editItem;
    /**
     *  Пункт контекстного меню "Переместить влево"
     */
    private MenuItem moveLeftItem;
    /**
     *  Пункт контекстного меню "Переместить вправо"
     */
    private MenuItem moveRightItem;
    /**
     *  Пункт контекстного меню "Удалить"
     */
    private MenuItem deleteItem;
    /**
     *  Пункт контекстного меню "Убрать с панели действий"
     */
    private MenuItem deleteFromPanelItem;
    /**
     *  Пункт контекстного меню "Добавить на панель действий"
     */
    private MenuItem addItem;

    public MenuItem getAddItem()
    {
        return addItem;
    }

    public MenuItem getDeleteFromPanelItem()
    {
        return deleteFromPanelItem;
    }

    public MenuItem getDeleteItem()
    {
        return deleteItem;
    }

    public MenuItem getEditItem()
    {
        return editItem;
    }

    public MenuItem getMoveLeftItem()
    {
        return moveLeftItem;
    }

    public MenuItem getMoveRightItem()
    {
        return moveRightItem;
    }

    @Inject
    public void init(CommonMessages cmessages, EditableToolPanelMessages messages, ToolMenuResources resources)
    {
        super.init(resources);
        editItem = new MenuItem(SafeHtmlUtils.fromTrustedString(cmessages.edit()));
        moveLeftItem = new MenuItem(SafeHtmlUtils.fromTrustedString(cmessages.moveLeft()));
        moveRightItem = new MenuItem(SafeHtmlUtils.fromTrustedString(cmessages.moveRight()));
        deleteItem = new MenuItem(SafeHtmlUtils.fromTrustedString(cmessages.delete()));
        deleteFromPanelItem = new MenuItem(SafeHtmlUtils.fromTrustedString(messages.deleteFromToolBarContent()));
        addItem = new MenuItem(SafeHtmlUtils.fromTrustedString(messages.addToToolBarContent()));

        addItem.ensureDebugId("add");
        editItem.ensureDebugId("edit");
        moveLeftItem.ensureDebugId("moveLeft");
        moveRightItem.ensureDebugId("moveRight");
        deleteItem.ensureDebugId("deleteItem");
        deleteFromPanelItem.ensureDebugId("delete");

        moveLeftItem.addStyleName(resources.css().removeFromContent());
        moveRightItem.addStyleName(resources.css().removeFromContent());
        deleteItem.addStyleName(resources.css().removeFromContent());
        deleteFromPanelItem.addStyleName(resources.css().removeFromContent());
    }

    protected void fillPopup(Tool content)
    {
        if (isNotAddedTool(content))
        {
            menuBar.addItem(addItem);
        }
        else
        {
            if (canEditTool(content))
            {
                menuBar.addItem(editItem);
            }

            ToolBar toolBar = content.getParent();
            if (toolBar != null && toolBar.getTools().size() > 1)
            {
                if (toolBar.getTools().indexOf(content) > 0)
                {
                    menuBar.addItem(moveLeftItem);
                }

                if (toolBar.getTools().indexOf(content) < (toolBar.getTools().size() - 1))
                {
                    menuBar.addItem(moveRightItem);
                }
            }

            if (isAttributePanelTool(content))
            {
                menuBar.addItem(deleteItem);
            }
            else
            {
                menuBar.addItem(deleteFromPanelItem);
            }
        }
    }

    /**
     * Для тула Разделитель границ и для тула выбора представления форму редактирования не показываем
     */
    private static boolean canEditTool(Tool content)
    {
        return !EditableToolPanelGinModule.SEPARATOR.equals(content.getUuid())
               && !EditableToolPanelGinModule.FAST_CHANGE_STATE_SEPARATOR.equals(content.getUuid())
               && !Constants.WINDOW_ACTIONS_META_TOOL.equals(content.getUuid())
               && !(content instanceof AdvlistPrsSelectTool)
               && !(content instanceof SearchBoxTool);
    }

    /**
     * Если тул расположен на панели атрибута контента "Параметры объекта", для него необходимо показывать своё меню
     */
    private static boolean isAttributePanelTool(Tool content)
    {
        return (content.getAssociatedContent() instanceof AttributeToolPanel);
    }

    /**
     * Проверка находится ли кнопка действия на панели доступных действий
     * @param content кнопка действия
     * @return результат проверки
     */
    private static boolean isNotAddedTool(Tool content)
    {
        return content.getParent().getParent().getUuid().startsWith(EditableToolPanelGinModule.TOOLS_PREFIX);
    }
}
