package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.editable;

import static java.lang.Boolean.TRUE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.METAINFO;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPOSITE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DETERMINABLE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.USE_GEN_RULE;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления свойства "Редактируемый в списках"
 * <AUTHOR>
 * @since 1.07.2014
 */
public class EditableInListsRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        boolean editable = !Constants.NOT_EDITABLE_ATTRIBUTE_TYPES.contains(attrType);
        boolean editableInLists = Constants.EDITABLE_IN_LISTS_ATTRIBUTE_TYPES.contains(attrType);

        boolean computable = TRUE.equals(context.getPropertyValues().getProperty(COMPUTABLE));
        boolean determinable = TRUE.equals(context.getPropertyValues().getProperty(DETERMINABLE));
        boolean composite = TRUE.equals(context.getPropertyValues().getProperty(COMPOSITE));
        boolean useGenRule = TRUE.equals(context.getPropertyValues().getProperty(USE_GEN_RULE));
        ClassFqn fqn = context.getContextValues().<MetaClass> getProperty(METAINFO).getFqn();
        boolean suitableClass = !Comment.FQN.isSameClass(fqn) && !SuperUser.FQN.isSameClass(fqn);

        callback.onSuccess(editable && editableInLists && !computable && !determinable && !useGenRule && !composite
                           && suitableClass);
    }
}
