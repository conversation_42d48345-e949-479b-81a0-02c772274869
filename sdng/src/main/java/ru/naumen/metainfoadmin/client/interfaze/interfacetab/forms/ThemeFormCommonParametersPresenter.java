package ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms;

import static ru.naumen.core.shared.Constants.ImageFileExtension.IMAGE_FILE_EXTENSIONS;

import java.util.Collection;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.events.UploadCompleteEvent;
import ru.naumen.core.client.events.UploadCompleteHandler;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.personalsettings.PersonalSettingsMessages;
import ru.naumen.core.client.validation.HasValidation;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.ValidateEvent;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.FileUploadProperty;
import ru.naumen.core.client.widgets.properties.LabelProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.personalsettings.ThemeClient;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.ThemeMessages;

/**
 * <AUTHOR>
 * @since 14 сент. 2016 г.
 *
 */
public class ThemeFormCommonParametersPresenter extends BasicPresenter<PropertyFormDisplay>
{
    private class LogoUploadCompleteHandler implements UploadCompleteHandler
    {
        @Override
        public void onUploadComplete(UploadCompleteEvent event)
        {
            String msg = event.getUploadErrorMessage();
            String title = cmessages.attention();

            if (StringUtilities.isNotEmpty(msg))
            {
                getDisplay().addAttentionMessage(msg);
                getDisplay().getAttention().setTitle(title);
            }
        }
    }

    private class NotDefaultThemeValidator implements Validator<Boolean>
    {
        @Override
        public boolean validate(HasValueOrThrow<Boolean> hasValue)
        {
            if (!hasValue.getValue() && theme.isOperatorTheme() && hasValue instanceof HasValidation)
            {
                ((HasValidation)hasValue).addValidationMessage(messages.cantBeSwitched(title.getValue()));
                return false;
            }
            return true;
        }

        @Override
        public void validateAsync(HasValueOrThrow<Boolean> hasValue, ValidateEvent event)
        {
            validate(hasValue);
        }
    }

    @Inject
    private WidgetResources resources;
    @Inject
    private ThemeMessages messages;
    @Inject
    private CommonMessages cmessages;

    @Inject
    private PersonalSettingsMessages settingsMessages;
    @Inject
    private LabelProperty blockCaption;
    @Inject
    private TextBoxProperty title;
    @Inject
    private TextBoxProperty themeCode;
    @Inject
    private ListBoxProperty logoFileType;
    private PropertyRegistration<SelectItem> logoFileTypePR;
    @Inject
    private FileUploadProperty logoFileUpload;
    private PropertyRegistration<Collection<DtObject>> logoFileUploadPR;
    @Inject
    private ListBoxProperty logoLoginWindowFileType;
    private PropertyRegistration<SelectItem> logoLoginWindowFileTypePR;
    @Inject
    private FileUploadProperty logoLoginWindowFileUpload;
    private PropertyRegistration<Collection<DtObject>> logoLoginWindowFileUploadPR;

    @Inject
    private BooleanCheckBoxProperty enabledForUsers;
    @Inject
    private Provider<NotEmptyFileValidator> fileValidationProvider;
    @Inject
    private NotEmptyValidator notEmptyValidator;

    @Inject
    private ThemeCodeValidator themeCodeValidator;
    private final Processor validation;

    private final ThemeClient theme;

    @Inject
    public ThemeFormCommonParametersPresenter(@Assisted PropertyFormDisplay display, @Assisted Processor validation,
            @Assisted ThemeClient theme, EventBus eventBus)
    {
        super(display, eventBus);
        this.validation = validation;
        this.theme = theme;
    }

    @Override
    protected void onBind()
    {
        bindProperties();
        addProperties();
        fillProperties();
    }

    TextBoxProperty getCode()
    {
        return themeCode;
    }

    BooleanCheckBoxProperty getEnabledForUsers()
    {
        return enabledForUsers;
    }

    ListBoxProperty getLogoFileType()
    {
        return logoFileType;
    }

    FileUploadProperty getLogoFileUpload()
    {
        return logoFileUpload;
    }

    ListBoxProperty getLogoLoginWindowFileType()
    {
        return logoLoginWindowFileType;
    }

    FileUploadProperty getLogoLoginWindowFileUpload()
    {
        return logoLoginWindowFileUpload;
    }

    TextBoxProperty getTitle()
    {
        return title;
    }

    private void addProperties()
    {
        int propertyPos = 0;
        getDisplay().addProperty(blockCaption, propertyPos++);
        getDisplay().addProperty(title, propertyPos++);
        getDisplay().addProperty(themeCode, propertyPos++);
        if (theme.isSystem())
        {
            logoFileTypePR = getDisplay().addProperty(logoFileType, propertyPos++);
            propertyPos++;
        }
        else
        {
            propertyPos++;
            logoFileUploadPR = getDisplay().addProperty(logoFileUpload, propertyPos++);
            validation.validate(logoFileUpload,
                    fileValidationProvider.get().setErrorMessage(messages.logoFileNotSelected()));
        }
        logoLoginWindowFileTypePR = getDisplay().addProperty(logoLoginWindowFileType, propertyPos++);

        getDisplay().addProperty(enabledForUsers, propertyPos++);
        validation.validate(title, notEmptyValidator);
        validation.validate(enabledForUsers, new NotDefaultThemeValidator());
        if (StringUtilities.isEmpty(theme.getCode()))
        {
            validation.validate(themeCode, themeCodeValidator);
        }
    }

    private void bindCode()
    {
        themeCode.setCaption(cmessages.code());
        themeCode.setValidationMarker(true);
        themeCode.setMaxLength(Constants.MAX_METAINFO_KEY_LENGTH);
        DebugIdBuilder.ensureDebugId(themeCode, "themeCode");
    }

    private void bindEnabledForUsers()
    {
        enabledForUsers.setCaption(messages.isEnabledForUsers());
        enabledForUsers.asWidget().ensureDebugId("enabledForUsers");
    }

    private void bindLogoFileType()
    {
        logoFileType.asWidget().ensureDebugId("logoFileType");
        logoFileType.setCaption(messages.logo());
        logoFileType.getValueWidget().addItem(messages.standart(), ThemeFormPresenterBase.STANDART_CHOICE);
        logoFileType.getValueWidget().addItem(messages.fromFile(), ThemeFormPresenterBase.FROM_FILE_CHOICE);
        registerHandler(logoFileType.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {

            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                logoFileUploadPR = fileOptionValueChanged(logoFileType, logoFileTypePR, logoFileUpload,
                        logoFileUploadPR, settingsMessages.incorrectThemesLogoState());
            }
        }));
    }

    private void bindLogoFileUpload()
    {
        logoFileUpload.setValueFormatter(ITitled.TITLE_JOINER);
        logoFileUpload.setCaption(messages.logoFile());
        logoFileUpload.setValidationMarker(true);
        logoFileUpload.getValueWidget().setValidFileExtension(IMAGE_FILE_EXTENSIONS);
        DebugIdBuilder.ensureDebugId(logoFileUpload, "logoFileUpload");
        registerHandler(logoFileUpload.getValueWidget().addUploadCompleteHandler(new LogoUploadCompleteHandler()));
    }

    private void bindLogoLoginWindowFileType()
    {
        logoLoginWindowFileType.asWidget().ensureDebugId("logoLoginWindowFileType");
        logoLoginWindowFileType.setCaption(messages.loginWindowLogo());
        logoLoginWindowFileType.getValueWidget()
                .addItem(messages.systemLogotype(), ThemeFormPresenterBase.STANDART_CHOICE);
        logoLoginWindowFileType.getValueWidget().addItem(messages.fromFile(), ThemeFormPresenterBase.FROM_FILE_CHOICE);
        registerHandler(logoLoginWindowFileType.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {

            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                logoLoginWindowFileUploadPR = fileOptionValueChanged(logoLoginWindowFileType, logoLoginWindowFileTypePR,
                        logoLoginWindowFileUpload, logoLoginWindowFileUploadPR,
                        settingsMessages.incorrectLoginLogoState());
            }
        }));
    }

    private void bindLogoLoginWindowFileUpload()
    {
        logoLoginWindowFileUpload.setValueFormatter(ITitled.TITLE_JOINER);
        logoLoginWindowFileUpload.setCaption(messages.loginLogoFile());
        logoLoginWindowFileUpload.setValidationMarker(true);
        logoLoginWindowFileUpload.getValueWidget().setValidFileExtension(IMAGE_FILE_EXTENSIONS);
        registerHandler(
                logoLoginWindowFileUpload.getValueWidget().addUploadCompleteHandler(new LogoUploadCompleteHandler()));
        DebugIdBuilder.ensureDebugId(logoLoginWindowFileUpload, "logoLoginWindowFileUpload");
    }

    private void bindProperties()
    {
        blockCaption.setCaption(messages.commonParameters());
        blockCaption.ensureDebugId("commonParameters");
        bindTitle();
        bindCode();
        bindLogoFileType();
        bindLogoFileUpload();
        bindLogoLoginWindowFileType();
        bindLogoLoginWindowFileUpload();
        bindEnabledForUsers();
    }

    private void bindTitle()
    {
        title.setCaption(cmessages.title());
        title.setValidationMarker(true);
        title.setMaxLength(StringAttributeType.MAX_LENGTH_DEFAULT);
        DebugIdBuilder.ensureDebugId(title, "title");

    }

    private PropertyRegistration<Collection<DtObject>> fileOptionValueChanged(Property<SelectItem> option,
            PropertyRegistration<SelectItem> optionPR, Property<Collection<DtObject>> fileUpload,
            PropertyRegistration<Collection<DtObject>> fileUploadPR, String fileUploadValidationMessage)
    {
        if (ThemeFormPresenterBase.STANDART_CHOICE.equals(SelectListPropertyValueExtractor.getValue(option))
            && fileUploadPR != null)
        {
            validation.unvalidate(fileUpload);
            fileUploadPR.unregister();
            fileUploadPR = null;
        }
        if (ThemeFormPresenterBase.FROM_FILE_CHOICE.equals(SelectListPropertyValueExtractor.getValue(option))
            && fileUploadPR == null)
        {
            fileUploadPR = getDisplay().addPropertyAfter(fileUpload, optionPR);
            validation.validate(fileUpload, fileValidationProvider.get().setErrorMessage(fileUploadValidationMessage));
        }
        return fileUploadPR;
    }

    private void fillProperties()
    {
        if (theme == null)
        {
            return;
        }
        blockCaption.setDisable();
        blockCaption.getCaptionWidget().asWidget().addStyleName(resources.all().gh3());
        title.setValue(theme.getTitle());
        if (theme.getLogoFile() == null)
        {
            logoFileType.trySetObjValue(ThemeFormPresenterBase.STANDART_CHOICE);
        }
        else
        {
            logoFileType.trySetObjValue(ThemeFormPresenterBase.FROM_FILE_CHOICE, true);
            logoFileUpload.setValue(Lists.<DtObject> newArrayList(theme.getLogoFile()));
        }

        if (theme.getLogoLoginFile() == null)
        {
            logoLoginWindowFileType.trySetObjValue(ThemeFormPresenterBase.STANDART_CHOICE);
        }
        else
        {
            logoLoginWindowFileType.trySetObjValue(ThemeFormPresenterBase.FROM_FILE_CHOICE, true);
            logoLoginWindowFileUpload.setValue(Lists.<DtObject> newArrayList(theme.getLogoLoginFile()));
        }

        themeCode.setValue(theme.getCode());
        enabledForUsers.setValue(theme.isEnabled());
    }

}