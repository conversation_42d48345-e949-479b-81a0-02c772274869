package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import java.util.List;
import java.util.stream.Collectors;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.HasFolders;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfo.shared.filters.CatalogFilters;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 */
public class SuggestCatalogRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    private class GetCatalogsCallback extends BasicCallback<List<DtoContainer<Catalog>>>
    {
        private final PropertyContainerContext context;
        private final ListBoxProperty property;
        private final AsyncCallback<Boolean> callback;

        public GetCatalogsCallback(PropertyContainerContext context, ListBoxProperty property,
                AsyncCallback<Boolean> callback)
        {
            this.context = context;
            this.property = property;
            this.callback = callback;
        }

        @Override
        protected void handleSuccess(List<DtoContainer<Catalog>> catalogs)
        {
            List<Catalog> catalogList = catalogs.stream().map(DtoContainer::get).collect(Collectors.toList());
            metainfoUtils.sort(catalogList);

            SingleSelectCellList<String> selectList = property.getValueWidget();
            selectList.clear();
            for (Catalog cat : catalogList)
            {
                selectList.addItem(cat.getTitle(), cat.getItemMetaClass().getFqn().toString());
            }
            String value = context.getPropertyValues().getProperty(AttributeFormPropertyCode.SUGGEST_CATALOG);
            property.trySetObjValue(value);
            context.setProperty(AttributeFormPropertyCode.SUGGEST_CATALOG,
                    SelectListPropertyValueExtractor.getValue(property));
            callback.onSuccess(true);
        }
    }

    @Inject
    AdminMetainfoServiceAsync metainfoService;
    @Inject
    MetainfoUtils metainfoUtils;

    @Override
    public void refreshProperty(final PropertyContainerContext context, final ListBoxProperty property,
            final AsyncCallback<Boolean> callback)
    {
        Boolean computable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        Boolean determinable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);
        if (computable
            || determinable
            || !isStringCatalogSuggestionPresentation(context)
            || ObjectUtils.equals(HasFolders.FOLDERS,
                context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE)))
        {
            callback.onSuccess(false);
            return;
        }
        metainfoService.getCatalogs(CatalogFilters.notHidden(), new GetCatalogsCallback(context, property, callback));
    }

    private boolean isStringCatalogSuggestionPresentation(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        String editPrs = context.getPropertyValues().getProperty(AttributeFormPropertyCode.EDIT_PRS);
        return StringAttributeType.CODE.equals(attrType)
               && Presentations.STRING_EDIT_WITH_CATALOG_SUGGESTION.equals(editPrs);
    }
}
