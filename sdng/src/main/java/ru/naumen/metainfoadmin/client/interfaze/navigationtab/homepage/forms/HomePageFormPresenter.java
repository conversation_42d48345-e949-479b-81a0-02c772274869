package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.ObjectFormMessages;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector.PropertyContainerPresenterFactory;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerAfterBindHandlerImpl;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncImpl;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.client.TagActionExecutor;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfo.shared.homepage.HomePageType;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.ContentTemplatePropertiesTranslator;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemContextValueCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.ReferenceHelper;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.metainfoadmin.shared.homepage.HomePageActionBase;

/**
 * Базовая форма для элемента домашней страницы
 *
 * <AUTHOR>
 * @since 10.01.2023
 */
public abstract class HomePageFormPresenter<T extends ObjectForm> extends OkCancelPresenter<PropertyFormDisplay>
{
    private final Processor validation;
    private final NavigationSettingsMessages messages;
    private final PropertyContainerPresenterFactory containerFactory;
    private final PropertyControllerFactory<HomePageDtObject, T> propertyControllerFactory;
    private final ObjectFormMessages<T> formMessages;
    private final TagActionExecutor tagActionExecutor;
    private final ReferenceHelper referenceHelper;

    protected HomePageDtObject homePageItem;
    protected IProperties contextProps = new MapProperties();
    protected IProperties propertyValues = new MapProperties();
    protected PropertyContainerPresenter propertyContainer;
    private final PropertyContainerAfterBindHandlerImpl afterBindHandler;
    private AsyncCallback<DtoContainer<NavigationSettings>> refreshCallback;

    private NavigationSettings setting;

    @Inject
    public HomePageFormPresenter(
            PropertyFormDisplay display,
            EventBus eventBus,
            Processor validation,
            NavigationSettingsMessages messages,
            PropertyContainerPresenterFactory containerFactory,
            PropertyControllerFactory<HomePageDtObject, T> propertyControllerFactory,
            ObjectFormMessages<T> formMessages,
            TagActionExecutor tagActionExecutor,
            ReferenceHelper referenceHelper,
            PropertyContainerAfterBindHandlerImpl afterBindHandler)
    {
        super(display, eventBus);
        this.validation = validation;
        this.messages = messages;
        this.containerFactory = containerFactory;
        this.propertyControllerFactory = propertyControllerFactory;
        this.formMessages = formMessages;
        this.tagActionExecutor = tagActionExecutor;
        this.referenceHelper = referenceHelper;
        this.afterBindHandler = afterBindHandler;
    }

    public void init(NavigationSettings settings, HomePageDtObject homePageDtObject,
            AsyncCallback<DtoContainer<NavigationSettings>> refreshCallback)
    {
        this.homePageItem = homePageDtObject;
        this.setting = settings;
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        transformPropertiesToHomePage(propertyValues, homePageItem);
        setApplyButtonEnable(false);

        PropertyControllerSyncImpl controller = (PropertyControllerSyncImpl)propertyContainer.getContext()
                .getPropertyControllers().get(HomePage.TAGS);
        HomePageActionBase saveHomePageAction = getHomePageAction(homePageItem);
        tagActionExecutor.execute(saveHomePageAction, ((TagsProperty)controller.property).getPendingTags(),
                new BasicCallback<SimpleResult<DtoContainer<NavigationSettings>>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtoContainer<NavigationSettings>> navigationSettings)
                    {
                        refreshCallback.onSuccess(navigationSettings.get());
                        unbind();
                    }

                    @Override
                    protected void handleFailure(String msg)
                    {
                        super.handleFailure(msg);
                        setApplyButtonEnable(true);
                    }
                });
    }

    private static List<String> getProperties()
    {
        return Collections.unmodifiableList(HomePageFormConstants.PROPERTIES);
    }

    protected abstract HomePageActionBase getHomePageAction(HomePageDtObject homePageDtObject);

    @Override
    protected void onBind()
    {
        setCaption(formMessages.formCaption(messages.homePageElementBy()));
        propertyContainer = containerFactory.createSimple(getProperties(), getDisplay(),
                propertyControllerFactory, contextProps, propertyValues, afterBindHandler, validation);
        propertyContainer.bind();
        super.onBind();
    }

    @Override
    protected void onUnbind()
    {
        if (propertyContainer != null)
        {
            propertyContainer.unbind();
        }
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        contextProps.setProperty(MenuItemContextValueCode.SETTINGS, setting);
        if (homePageItem == null)
        {
            homePageItem = new HomePageDtObject();
            homePageItem.setType(HomePageType.REFERENCE.name());
            propertyValues.setProperty(HomePage.TYPE, homePageItem.getType());
            return;
        }
        transformHomePageToProperties(homePageItem, propertyValues);
        referenceHelper.transformAttrReferenceToAttrTreeObject(ReferenceCode.ATTRIBUTE_CHAIN, propertyValues,
                readyState,
                homePageItem.getAttrChain(), null);
    }

    private static void transformHomePageToProperties(HomePageDtObject from, IProperties to)
    {
        to.setProperty(HomePage.TAGS, from.getTags());
        to.setProperty(HomePage.SETTINGS_SET, from.getSettingsSet());
        to.setProperty(HomePage.PROFILES, from.getProfileCodes());
        to.setProperty(HomePage.TITLE, from.getTitle());
        to.setProperty(HomePage.TYPE, from.getType());
        to.setProperty(HomePage.CONTENT, from.getContentValue());
        if (from.getUUID() != null)
        {
            to.setProperty(AbstractBO.UUID, from.getUUID());
        }
        if (HomePageType.REFERENCE.name().equals(from.getType()))
        {
            Reference refValue = from.getReference();
            ArrayList<String> refMetaClassTabs = new ArrayList<>();
            ArrayList<String> refContentTabs = new ArrayList<>();
            ArrayList<String> tabUUIDs = refValue == null
                    ? new ArrayList<String>()
                    : refValue.getTabUUIDs();
            if (refValue == null)
            {
                to.setProperty(ReferenceCode.REFERENCE_VALUE, null);
                to.setProperty(HomePage.REFERENCE_TAB_VALUE, null);
                to.setProperty(HomePage.REFERENCE_UI_TEMPLATE, null);
            }
            else
            {
                if (CollectionUtils.isNotEmpty(tabUUIDs))
                {
                    refMetaClassTabs.add(tabUUIDs.get(0));
                    if (tabUUIDs.size() > 1)
                    {
                        refContentTabs.addAll(tabUUIDs.subList(1, tabUUIDs.size()));
                    }
                }
                Reference refMetaClass = new Reference(refValue.getClassFqn(), refMetaClassTabs);
                refMetaClass.setTitle(refValue.getTitle());

                SimpleDtObject refDTO = new SimpleDtObject(refMetaClass.getCode(), refMetaClass.getTitle());
                refDTO.setProperty(ReferenceCode.TAB_UUIDS, refMetaClassTabs);
                refDTO.setProperty(ReferenceCode.CLASS_FQN, refValue.getClassFqn());
                refDTO.setProperty(ReferenceCode.UI_TEMPLATE, refValue.getTemplateCode());

                Reference refContent = new Reference(refValue.getClassFqn(), refContentTabs);
                refContent.setTemplateCode(refValue.getTemplateCode());

                to.setProperty(ReferenceCode.REFERENCE_VALUE, refDTO);
                to.setProperty(HomePage.REFERENCE_TAB_VALUE, refContent);
                to.setProperty(HomePage.REFERENCE_UI_TEMPLATE, refValue.getTemplateCode());
            }

            to.setProperty(HomePage.REFERENCE_CARD_TYPE, from.getReferenceCardType());
            to.setProperty(ReferenceCode.OBJECT_CASES, from.getObjectCases());
            return;
        }
        if (HomePageType.CUSTOM_LINK.name().equals(from.getType()))
        {
            to.setProperty(HomePage.CUSTOM_LINK_VALUE, from.getCustomLink());
        }
    }

    private static void transformPropertiesToHomePage(IProperties from, HomePageDtObject to)
    {
        to.setType(from.getProperty(HomePage.TYPE, HomePageType.REFERENCE.name()));
        to.setTitle(from.getProperty(HomePage.TITLE, ""));
        String uuid = from.getProperty(AbstractBO.UUID, null);
        if (uuid != null)
        {
            to.setUUID(uuid);
        }
        Collection<String> profiles = from.getProperty(HomePage.PROFILES, new ArrayList<>());
        if (CollectionUtils.isNotEmpty(profiles))
        {
            to.setProfiles(profiles.stream()
                    .map(code -> new SimpleDtObject(code, ""))
                    .collect(Collectors.toList()));
        }
        to.setTags(from.getProperty(HomePage.TAGS));
        to.setSettingsSet(from.getProperty(HomePage.SETTINGS_SET));

        if (HomePageType.REFERENCE.name().equals(to.getType()))
        {
            DtObject refDTO = from.getProperty(ReferenceCode.REFERENCE_VALUE);
            Reference refContent = from.getProperty(HomePage.REFERENCE_TAB_VALUE);
            ArrayList<String> tabUUIDs = refDTO.getProperty(ReferenceCode.TAB_UUIDS, new ArrayList<>());
            if (refContent != null && refContent.getTabUUIDs() != null)
            {
                tabUUIDs.addAll(refContent.getTabUUIDs());
            }

            to.setAttrChain(ContentTemplatePropertiesTranslator.transformAttrTreeDtoToAttrChain(
                    from.getProperty(ReferenceCode.ATTRIBUTE_CHAIN)));
            Reference refMetaClass = new Reference(refDTO.getProperty(ReferenceCode.CLASS_FQN), tabUUIDs);
            refMetaClass.setTemplateCode(from.getProperty(HomePage.REFERENCE_UI_TEMPLATE));

            List<AttrReference> chain = to.getAttrChain();
            if (chain != null)
            {
                refMetaClass.setAttrChain(chain);
            }
            to.setReference(refMetaClass);

            to.setReferenceCardType(from.getProperty(HomePage.REFERENCE_CARD_TYPE, ""));
            to.setObjectCases(from.getProperty(ReferenceCode.OBJECT_CASES, new HashSet<String>()));
            to.setContentValue(refDTO.getTitle());
            return;
        }
        if (HomePageType.CUSTOM_LINK.name().equals(to.getType()))
        {
            to.setCustomLink(from.getProperty(HomePage.CUSTOM_LINK_VALUE));
            to.setContentValue(to.getCustomLink());
        }
    }
}
