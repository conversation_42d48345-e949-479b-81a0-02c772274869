package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.INTERFACE_AND_NAVIGATION;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.activity.PrevLinkContainer;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.GetNavigationSettingsAction;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsPlace;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.MenuItemChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.MenuItemChangedHandler;

/**
 * Презентер карточки домашней странице в разделе "Интерфейс и навигация"
 *
 * @since 24.01.2023
 * <AUTHOR>
 */
public class HomePageCardPresenter extends AdminTabPresenter<NavigationHomePagePlace>
        implements MenuItemChangedHandler
{
    private final PrevLinkContainer prevLinkContainer;
    private final CommonMessages commonMessages;
    private final NavigationSettingsMessages navigationSettingsMessages;
    private final DispatchAsync dispatch;
    private final NavigationHomePageAttributesPresenter attributesPresenter;

    private HomePageDtObject homePageDtObject;
    private DtoContainer<NavigationSettings> settings;

    @Inject
    public HomePageCardPresenter(
            AdminTabDisplay display,
            EventBus eventBus,
            PrevLinkContainer prevLinkContainer,
            CommonMessages commonMessages,
            NavigationSettingsMessages navigationSettingsMessages,
            DispatchAsync dispatch,
            NavigationHomePageAttributesPresenter attributesPresenter)
    {
        super(display, eventBus);
        this.prevLinkContainer = prevLinkContainer;
        this.commonMessages = commonMessages;
        this.navigationSettingsMessages = navigationSettingsMessages;
        this.dispatch = dispatch;
        this.attributesPresenter = attributesPresenter;
    }

    @Override
    public void init(NavigationHomePagePlace place)
    {
        super.init(place);
        if (place.getHomePageItem() != null)
        {
            homePageDtObject = place.getHomePageItem();
            settings = place.getSettings();
        }
    }

    @Override
    public void onMenuItemChanged(MenuItemChangedEvent event)
    {
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        if (homePageDtObject != null)
        {
            getDisplay().setTitle(homePageDtObject.getTitle());
        }
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();
        Place previousPlace = prevLinkContainer.getPreviousPlace();
        String backLinkCaption = null == previousPlace || previousPlace instanceof InterfaceSettingsPlace
                ? navigationSettingsMessages.toHomePageElements()
                : commonMessages.back();
        prevPageLinkPresenter.bind(backLinkCaption, new InterfaceSettingsPlace("navigation"));
        dispatch.execute(new GetNavigationSettingsAction(),
                new BasicCallback<SimpleResult<DtoContainer<NavigationSettings>>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtoContainer<NavigationSettings>> response)
                    {
                        super.handleSuccess(response);
                        settings = response.get();
                        homePageDtObject = settings.get().findHomePage(getPlace().getUuid());
                        initAttributes();
                        refreshDisplay();
                    }
                });
    }

    private void initAttributes()
    {
        attributesPresenter.init(homePageDtObject, settings);
        addContent(attributesPresenter, "attrs");
        attributesPresenter.revealDisplay();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return INTERFACE_AND_NAVIGATION;
    }
}