package ru.naumen.metainfoadmin.client.escalation;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.ESCALATIONS;

import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;

import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminMultiTabPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.catalog.CatalogMessages;
import ru.naumen.metainfoadmin.client.escalation.EscalationGinModule.EscalationPlaceTabs;
import ru.naumen.metainfoadmin.client.escalation.actions.EscalationActionsPresenter;
import ru.naumen.metainfoadmin.client.escalation.schemes.EscalationSchemesPresenter;
import ru.naumen.metainfoadmin.client.escalation.vmap.EscalationVMapPresenter;
import ru.naumen.metainfoadmin.client.sec.AdminSecMessages;

/**
 * Презентер страницы "Эскалации"
 * <AUTHOR>
 * @since 20.07.2012
 *
 */
public class EscalationPresenter extends AdminMultiTabPresenterBase<EscalationPlace>
{
    private final EscalationMessages escalationMessages;
    private final AdminSecMessages secMessages;
    private final CatalogMessages catalogMessages;
    private final EscalationSchemesPresenter schemesPresenter;
    private final EscalationActionsPresenter actionsPresenter;
    private final EscalationVMapPresenter vmapPresenter;
    private final EscalationPresenterSettings config;

    @Inject
    public EscalationPresenter(AdminTabDisplay display, // NOSONAR старый код, кол-во зависимостей > 7
            EventBus eventBus,
            EscalationMessages escalationMessages,
            AdminSecMessages secMessages,
            CatalogMessages catalogMessages,
            EscalationSchemesPresenter schemesPresenter,
            EscalationActionsPresenter actionsPresenter,
            EscalationVMapPresenter vmapPresenter,
            EscalationPresenterSettings config)
    {
        super(display, eventBus);
        this.escalationMessages = escalationMessages;
        this.secMessages = secMessages;
        this.catalogMessages = catalogMessages;
        this.schemesPresenter = schemesPresenter;
        this.actionsPresenter = actionsPresenter;
        this.vmapPresenter = vmapPresenter;
        this.config = config;
    }

    @Override
    protected void initTabs(AsyncCallback<Void> callback)
    {
        if (config.isWithScheme())
        {
            addTabWithContent(escalationMessages.escalationSchemes(), EscalationPlaceTabs.SCHEMES, schemesPresenter);
        }

        addTabWithContent(secMessages.actions(), EscalationPlaceTabs.ACTIONS, actionsPresenter);

        if (config.isWithTables())
        {
            addTabWithContent(catalogMessages.valueMapTables(), EscalationPlaceTabs.VMAPS, vmapPresenter);
        }

        getDisplay().setOneTabShowed(true);

        callback.onSuccess(null);
    }

    @Override
    protected String getTitle()
    {
        return escalationMessages.escalation();
    }

    @Override
    protected EscalationPlace getTabbedPlace(SelectionEvent<Integer> event, String tab)
    {
        return new EscalationPlace(tab);
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return ESCALATIONS;
    }
}
