package ru.naumen.metainfoadmin.client.escalation.vmap;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.SYSTEM_CATALOGS;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import ru.naumen.admin.client.permission.AccessMarkerViewPermissionControlled;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants.ValueMapCatalog;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.catalog.CatalogContentPresenter;

/**
 * Презентер контента Таблиц соответствий для Эскалаций
 * <AUTHOR>
 * @since 23.07.2012
 *
 */
public class EscalationVMapPresenter extends CatalogContentPresenter<EscalationValueMapContext>
        implements AccessMarkerViewPermissionControlled
{
    private final AdminMetainfoServiceAsync metainfoService;

    @Inject
    public EscalationVMapPresenter(TableDisplay<DtObject> display,
            EventBus eventBus,
            AdminMetainfoServiceAsync metainfoService)
    {
        super(display, eventBus, new EscalationValueMapContext(null, null, null));
        this.metainfoService = metainfoService;
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaptionVisible(false);
        metainfoService.getCatalog(ValueMapCatalog.CODE, new BasicCallback<DtoContainer<Catalog>>()
        {
            @Override
            protected void handleSuccess(DtoContainer<Catalog> catalog)
            {
                context.setCatalog(catalog);

                ClassFqn itemFqn = catalog.get().getItemMetaClass().getFqn();
                metainfoService.getMetaClass(itemFqn, new BasicCallback<MetaClass>()
                {
                    @Override
                    protected void handleSuccess(MetaClass metaClass)
                    {
                        context.setMetaClass(metaClass);
                        EscalationVMapPresenter.super.onBind();
                    }
                });
            }
        });
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return SYSTEM_CATALOGS;
    }
}
