package ru.naumen.metainfoadmin.client.escalation.scheme.attributes;

import java.util.ArrayList;
import java.util.HashMap;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.name.Named;

import jakarta.inject.Inject;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.FactoryParam.ValueSource;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.properties.container.AttributePropertyDescription;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.escalation.EscalationGinModule.EscalationPlaceTabs;
import ru.naumen.metainfoadmin.client.escalation.EscalationPlace;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemeContext;
import ru.naumen.metainfoadmin.client.escalation.schemes.commands.EscalationSchemeCommandContext;
import ru.naumen.metainfoadmin.client.escalation.schemes.commands.EscalationSchemesCommandParam;
import ru.naumen.metainfoadmin.client.escalation.schemes.commands.EscalationSchemesCommandsGinModule.EscalationCommandCode;

/**
 * <AUTHOR>
 * @since 20.08.2012
 *
 */
public class EscalationSchemeAttributesPresenter extends BasicPresenter<InfoDisplay>
{
    @Inject
    @Named(EscalationSchemeAttributesGinModule.ESCALATION_SCHEME_ATTRIBUTES)
    private ArrayList<AttributePropertyDescription<?, EscalationScheme>> properties;
    @Inject
    @Named(EscalationSchemeAttributesGinModule.ESCALATION_SCHEME_STATE_CAPTION)
    private HashMap<String, String> stateCaptions;
    @Inject
    private EscalationSchemeAttributesMessages messages;
    @Inject
    private CommonMessages commonMessages;
    @Inject
    private ButtonFactory buttonFactory;

    private final EscalationSchemeContext context;
    @SuppressWarnings("rawtypes")
    private ToolBarDisplayMediator<DtoContainer<EscalationScheme>> toolBar;
    private ButtonPresenter<DtoContainer<EscalationScheme>> switchButton;
    @Inject
    private PlaceController placeController;

    private ValueSource<DtoContainer<EscalationScheme>> escalationSchemeValueSource =
            new ValueSource<DtoContainer<EscalationScheme>>()
            {
                @Override
                public DtoContainer<EscalationScheme> getValue()
                {
                    return context.getEscalationScheme();
                }

                ;
            };

    private OnStartCallback<Void> removeCallback = new SafeOnStartBasicCallback<Void>(
            getDisplay())
    {
        @Override
        protected void handleSuccess(Void value)
        {
            placeController.goTo(new EscalationPlace(EscalationPlaceTabs.SCHEMES));
        }

        ;
    };

    @SuppressWarnings("rawtypes")
    @Inject
    public EscalationSchemeAttributesPresenter(@Assisted EscalationSchemeContext context, InfoDisplay display,
            EventBus eventBus)
    {
        super(display, eventBus);
        this.context = context;
        toolBar = new ToolBarDisplayMediator(getDisplay().getToolBar());
    }

    @Override
    public void refreshDisplay()
    {
        revealDisplay();
    }

    @Override
    public void revealDisplay()
    {
        for (AttributePropertyDescription<?, EscalationScheme> propDesc : properties)
        {
            propDesc.setValue(context.getEscalationScheme().get());
        }
        switchButton.setTitle(stateCaptions.get(context.getEscalationScheme().get().getState()));
        toolBar.refresh(context.getEscalationScheme());
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(messages.escalationSchemeAttributes());
        for (AttributePropertyDescription<?, EscalationScheme> propDesc : properties)
        {
            getDisplay().add(propDesc.getProperty(), propDesc.getDebugId());
        }
        bindToolBar();
    }

    @SuppressWarnings("unchecked")
    private void bindToolBar()
    {
        EscalationSchemesCommandParam param = new EscalationSchemesCommandParam(escalationSchemeValueSource,
                null, new EscalationSchemeCommandContext(context.getLocalEventBus(), getDisplay()));
        switchButton = (ButtonPresenter<DtoContainer<EscalationScheme>>)buttonFactory.create(ButtonCode.SWITCH,
                stateCaptions.get(context.getEscalationScheme().get().getState()), EscalationCommandCode.TOGGLE, param);
        switchButton.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        toolBar.add(switchButton);

        ButtonPresenter<DtoContainer<EscalationScheme>> editBtn =
                (ButtonPresenter<DtoContainer<EscalationScheme>>)buttonFactory.create(
                        ButtonCode.EDIT,
                        commonMessages.edit(), EscalationCommandCode.EDIT, param);
        editBtn.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        toolBar.add(editBtn);

        ButtonPresenter<DtoContainer<EscalationScheme>> delBtn =
                (ButtonPresenter<DtoContainer<EscalationScheme>>)buttonFactory.create(ButtonCode.DELETE,
                        commonMessages.delete(),
                        EscalationCommandCode.DELETE, new EscalationSchemesCommandParam(escalationSchemeValueSource,
                                removeCallback,
                                new EscalationSchemeCommandContext(context.getLocalEventBus(), getDisplay())));
        delBtn.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE));
        toolBar.add(delBtn);

        toolBar.bind();
    }
}