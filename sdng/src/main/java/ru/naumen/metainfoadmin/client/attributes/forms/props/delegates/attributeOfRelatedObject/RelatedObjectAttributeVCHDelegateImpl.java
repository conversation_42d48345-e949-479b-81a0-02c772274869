package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject;

import java.util.Arrays;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

public class RelatedObjectAttributeVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getRefreshProcess().startCustomProcess(Arrays.asList(AttributeFormPropertyCode.SHOW_PRS,
                AttributeFormPropertyCode.DIGITS_COUNT_RESTRICTION,
                AttributeFormPropertyCode.HAS_GROUP_SEPARATORS,
                AttributeFormPropertyCode.HIDE_ARCHIVED));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();

        String relatedObjectAttribute = context.getPropertyValues()
                .getProperty(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE);
        if (relatedObjectAttribute != null && AttributeFqn.parse(relatedObjectAttribute)
                .getCode()
                .equals(Constants.PARENT_ATTR) && ("0").equals(
                context.getPropertyValues().getProperty(AttributeFormPropertyCode.RELATED_OBJECT_HIERARCHY_LEVEL)))
        {
            context.getPropertyValues().setProperty(AttributeFormPropertyCode.RELATED_OBJECT_HIERARCHY_LEVEL, "1");
        }

        context.getPropertyControllers().get(AttributeFormPropertyCode.RELATED_OBJECT_HIERARCHY_LEVEL).refresh();
    }
}
