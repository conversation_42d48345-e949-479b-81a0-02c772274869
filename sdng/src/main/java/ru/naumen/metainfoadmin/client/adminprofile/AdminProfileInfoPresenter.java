package ru.naumen.metainfoadmin.client.adminprofile;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.ADMINISTRATION_PROFILES;
import static ru.naumen.core.shared.permission.PermissionType.DELETE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.admin.client.permission.AdminPermissionHelper;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.FactoryParam.ValueSource;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.Constants.AdminProfile;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.adminprofile.command.AdminProfileCommandCode;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;

/**
 * Представление информации о комплекте на карточке профиля администрирования
 * <AUTHOR>
 * @since 24.01.2024
 */
public class AdminProfileInfoPresenter extends BasicPresenter<InfoDisplay>
{
    private class AdminProfileCommandParam extends CommandParam<DtObject, DtObject>
    {
        public AdminProfileCommandParam(AsyncCallback<DtObject> callback)
        {
            super(adminProfileValueSource, callback);
        }
    }

    private final Property<String> title;
    private final Property<String> code;
    private final Property<String> description;
    private final CommonMessages commonMessages;
    private final ButtonFactory buttonFactory;
    private final PlaceController placeController;

    private DtObject adminProfile;
    private OnStartCallback<DtObject> refreshCallback;
    private final ToolBarDisplayMediator<DtObject> toolBar;
    private final AdminPermissionHelper adminPermissionHelper;

    private final OnStartCallback<DtObject> removeCallback = new SafeOnStartBasicCallback<>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            placeController.goTo(AdminProfilesPlace.INSTANCE);
        }
    };

    private final ValueSource<DtObject> adminProfileValueSource = this::getAdminProfile;

    @Inject
    public AdminProfileInfoPresenter(InfoDisplay display,
            EventBus eventBus,
            @Named(PropertiesGinModule.TEXT) Property<String> title,
            @Named(PropertiesGinModule.TEXT) Property<String> code,
            @Named(PropertiesGinModule.HTML_TEXT) Property<String> description,
            CommonMessages commonMessages,
            ButtonFactory buttonFactory,
            PlaceController placeController,
            AdminPermissionHelper adminPermissionHelper)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(display.getToolBar());
        this.title = title;
        this.code = code;
        this.description = description;
        this.commonMessages = commonMessages;
        this.buttonFactory = buttonFactory;
        this.placeController = placeController;
        this.adminPermissionHelper = adminPermissionHelper;
    }

    public DtObject getAdminProfile()
    {
        return adminProfile;
    }

    public void init(OnStartCallback<DtObject> refreshCallback)
    {
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        if (adminProfile != null)
        {
            title.setValue(adminProfile.getTitle());
            code.setValue(adminProfile.getUUID());
            String rawDescription = adminProfile.getProperty(AdminProfile.DESCRIPTION);
            SafeHtml descriptionHtml = new SafeHtmlBuilder().appendEscapedLines(rawDescription).toSafeHtml();
            description.setValue(descriptionHtml.asString());
            toolBar.refresh(adminProfile);
        }
        else
        {
            title.setValue(StringUtilities.EMPTY);
            code.setValue(StringUtilities.EMPTY);
            description.setValue(StringUtilities.EMPTY);
        }
    }

    public void setAdminProfile(DtObject adminProfile)
    {
        this.adminProfile = adminProfile;
        refreshDisplay();
    }

    @SuppressWarnings("unchecked")
    protected ButtonPresenter<DtObject> addTool(String button, String title, String command,
            CommandParam<DtObject, DtObject> param)
    {
        ButtonPresenter<DtObject> buttonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(button, title,
                command, param);
        toolBar.add(buttonPresenter);
        return buttonPresenter;
    }

    protected void bindProperties()
    {
        title.setCaption(commonMessages.title());
        getDisplay().add(title);
        DebugIdBuilder.ensureDebugId(title, "title");
        code.setCaption(commonMessages.code());
        getDisplay().add(code);
        DebugIdBuilder.ensureDebugId(code, "code");
        description.setCaption(commonMessages.description());
        getDisplay().add(description);
        DebugIdBuilder.ensureDebugId(description, "description");
    }

    protected void initToolBar()
    {
        adminPermissionHelper.runIfPermissionByAccessMarkerGranted(ADMINISTRATION_PROFILES, EDIT,
                () -> addTool(ButtonCode.EDIT, commonMessages.edit(), AdminProfileCommandCode.EDIT,
                        new AdminProfileCommandParam(refreshCallback)));
        adminPermissionHelper.runIfPermissionByAccessMarkerGranted(ADMINISTRATION_PROFILES, DELETE,
                () -> addTool(ButtonCode.DELETE, commonMessages.delete(), AdminProfileCommandCode.DELETE,
                        new AdminProfileCommandParam(removeCallback)));
        toolBar.bind();
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(commonMessages.properties());
        initToolBar();
        bindProperties();
    }
}
