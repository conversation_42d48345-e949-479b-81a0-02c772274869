package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import static ru.naumen.core.shared.Constants.PARENT_ATTR;
import static ru.naumen.metainfo.shared.Constants.LINK_ATTRIBUTE_TYPES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.CODE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPLEX_RELATION;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.EDITABLE;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.elements.ComplexRelationType;

/**
 * Делегат обновления поля "Структуры" для настройки сложной связи.
 * <AUTHOR>
 * @since 25.12.2019
 */
public class ComplexRelationStructuredObjectsViewRefreshDelegateImpl<F extends ObjectForm>
        extends StructureBaseRefreshDelegate<F>
{
    @Inject
    protected ComplexRelationStructuredObjectsViewRefreshDelegateImpl(DispatchAsync dispatch)
    {
        super(dispatch);
    }

    @Override
    protected boolean isShowStructuredObjectsViewField(PropertyContainerContext context)
    {
        IProperties propertyValues = context.getPropertyValues();

        String attrType = propertyValues.getProperty(ATTR_TYPE);
        if (Boolean.FALSE.equals(LINK_ATTRIBUTE_TYPES.contains(attrType)))
        {
            return false;
        }

        // Если атрибут не доступен для редактирования, то настройку сложной формы не отображаем
        if (Boolean.FALSE.equals(propertyValues.getProperty(EDITABLE, false))
            && Boolean.FALSE.equals(PARENT_ATTR.equals(propertyValues.getProperty(CODE))))
        {
            return false;
        }

        return ComplexRelationType.HIERARCHY.getCode().equals(propertyValues.getProperty(COMPLEX_RELATION));
    }
}
