package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.dispatch.DeleteLeftMenuItemAction;
import ru.naumen.core.shared.navigationsettings.dispatch.DeleteNavigationMenuItemAction;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;

/**
 * Команда удаления элемента левого меню
 *
 * <AUTHOR>
 * @since 19.06.2020
 */
public class DeleteLeftMenuItemCommand extends DeleteMenuItemCommand<LeftMenuItemSettingsDTO>
{
    public static final String ID = "deleteLeftMenuItemCommand";

    private final NavigationSettingsMessages messages;

    @Inject
    public DeleteLeftMenuItemCommand(@Assisted NavigationSettingsLMCommandParam param,
            NavigationSettingsMessages messages)
    {
        super(param);
        this.messages = messages;
    }

    @Override
    protected DeleteNavigationMenuItemAction getAction()
    {
        return new DeleteLeftMenuItemAction();
    }

    @Override
    protected String additionalInfo(NavigationSettingsMenuItemAbstractCommandParam<LeftMenuItemSettingsDTO> param)
    {
        String addInfo = super.additionalInfo(param);

        StringBuilder sb = new StringBuilder(StringUtilities.toNonNullString(addInfo));

        LeftMenuItemSettingsDTO item = param.getValue();

        if (MenuItemType.linkToContent.equals(item.getType()))
        {
            SimpleDtObject listTemplate = item.getProperty(MenuItemLinkToContentCode.LIST_TEMPLATE);

            if (listTemplate != null)
            {
                sb.append("<br>");
                sb.append(messages.additionalDeleteWithListTemplate(listTemplate.getTitle()));
            }
        }

        return sb.toString();
    }
}