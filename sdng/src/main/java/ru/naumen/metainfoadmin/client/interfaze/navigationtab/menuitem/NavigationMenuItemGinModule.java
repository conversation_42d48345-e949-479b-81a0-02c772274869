package ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.safehtml.shared.UriUtils;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Named;
import com.google.inject.name.Names;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.client.permission.AdminPermissionCheckServiceSync;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.container.AttributePropertyDescription;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.navigationsettings.AddButtonValue;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO.MenuItemFormatting;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.LeftMenuItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelAreaSettingsDTO;
import ru.naumen.metainfo.client.CachedMetainfoServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentTitles;
import ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.HierarchyGridMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.template.CopyFromTemplateMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationLeftMenuItemPlace;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationMenuItemPlace;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationTabSettingsGinModule;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationTopMenuItemPlace;
import ru.naumen.metainfoadmin.client.sets.formatters.SettingsSetPropertyFormatter;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.card.StructuredObjectsViewPlace;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.client.tags.formatters.TagPropertyFormatter;
import ru.naumen.metainfoadmin.client.templates.content.card.ContentTemplatePlace;
import ru.naumen.metainfoadmin.client.templates.list.card.ListTemplatePlace;

/**
 * <AUTHOR>
 * @since 04 дек. 2013 г.
 *
 */
public class NavigationMenuItemGinModule extends AbstractGinModule
{
    public static class NavigationMenuItemAttributesPropertyProvider<M extends IMenuItem,
            C extends NavigationMenuItemContext<M>>
            implements Provider<ArrayList<AttributePropertyDescription<?, C>>>
    {
        @Inject
        private CommonMessages commonMessages;
        @Inject
        private NavigationSettingsMessages navMessages;
        @Inject
        private AdminDialogMessages adminDialogMessages;
        @Inject
        SettingsSetOnFormCreator settingsSetOnFormCreator;
        @Inject
        CachedMetainfoServiceAsync cachedMetainfoServiceAsync;
        @Inject
        SettingsSetPropertyFormatter settingsSetPropertyFormatter;
        @Inject
        private I18nUtil i18nUtil;
        @Inject
        protected PlaceHistoryMapper placeHistoryMapper;
        @Inject
        @Named(NavigationTabSettingsGinModule.MENU_TYPE_TITLES)
        private Map<MenuItemType, String> menuTypeTitles;
        @Inject
        protected CommonHtmlTemplates templates;
        @Inject
        private MetainfoUtils metainfoUtils;

        @Inject
        @Named(PropertiesGinModule.TEXT)
        private Property<String> title;
        @Inject
        @Named(PropertiesGinModule.HTML_TEXT)
        private Property<String> parent;
        @Inject
        @Named(PropertiesGinModule.TEXT)
        private Property<String> type;
        @Inject
        @Named(PropertiesGinModule.HTML_TEXT)
        private Property<String> content;
        @Inject
        @Named(PropertiesGinModule.BOOLEAN_IMAGE)
        private Property<Boolean> enabled;
        @Inject
        private AdminPermissionCheckServiceSync adminPermissionCheckServiceSync;

        private final Function<C, String> contentFunction = input ->
        {
            if (input == null)
            {
                return StringUtilities.EMPTY;
            }
            return input.getItem() instanceof MenuItem ? processMenuItem(input) : processLeftMenuItem(input);
        };

        @Override
        public ArrayList<AttributePropertyDescription<?, C>> get()
        {
            ArrayList<AttributePropertyDescription<?, C>> result = new ArrayList<>();
            result.add(new AttributePropertyDescription<String, C>(commonMessages.title(),
                    title, "title",
                    input -> input == null ? StringUtilities.EMPTY : i18nUtil.getLocalizedTitle(input.getItem())));
            result.add(new AttributePropertyDescription<String, C>(navMessages.insertedInChapter(), parent, "parent",
                    getParentFunction()));
            result.add(new AttributePropertyDescription<>(navMessages.itemType(), type,
                    "type",
                    input -> input == null ? StringUtilities.EMPTY : menuTypeTitles.get(input.getItem().getType())));
            result.add(new AttributePropertyDescription<>(commonMessages.content(),
                    content, "content", contentFunction));
            result.add(new AttributePropertyDescription<>(commonMessages.on(), enabled, "enabled",
                    input -> input == null ? Boolean.FALSE : input.getItem().isEnabled()));
            addSpecialProperties(result);
            if (settingsSetOnFormCreator.isDisplayedOnCards())
            {
                result.add(new AttributePropertyDescription<>(adminDialogMessages.settingsSet(),
                        settingsSetOnFormCreator.createFieldOnCard(), "Info.settingsSet",
                        getSettingsSetFunction()));
            }
            return result;
        }

        protected void addSpecialProperties(ArrayList<AttributePropertyDescription<?, C>> result)
        {
            //NOSONAR не абстрактный класс, нужно тело метода
        }

        private Function<C, String> getParentFunction()
        {
            return input ->
            {
                if (input == null)
                {
                    return StringUtilities.EMPTY;
                }
                IMenuItem chapter = getChapterParent(input.getItem());
                if (chapter == null)
                {
                    return StringUtilities.EMPTY;
                }
                NavigationMenuItemPlace<?> place = (chapter instanceof MenuItem) //NOSONAR
                        ? new NavigationTopMenuItemPlace((MenuItem)chapter, input.getSettings())
                        : new NavigationLeftMenuItemPlace((LeftMenuItemSettingsDTO)chapter, input.getSettings());
                String html = StringUtilities.getHrefByToken(placeHistoryMapper
                        .getToken(place));
                return templates
                        .anchorWithId(i18nUtil.getLocalizedTitle(chapter), UriUtils.fromTrustedString(html),
                                "parent", false, SafeHtmlUtils.EMPTY_SAFE_HTML)
                        .asString();
            };
        }

        private static IMenuItem getChapterParent(IMenuItem item)
        {
            do
            {
                item = item.getParent();
            }
            while (item != null && MenuItemType.chapter != item.getType());
            return item;
        }

        private Function<C, String> getSettingsSetFunction()
        {
            return input ->
            {
                if (input == null)
                {
                    return "";
                }
                DtObject settingsSet = cachedMetainfoServiceAsync.getSettingsSet(input.getItem().getSettingsSet());
                if (settingsSet == null)
                {
                    return "";
                }
                return settingsSetPropertyFormatter.format(settingsSet.getUUID(), settingsSet.getTitle()).asString();
            };
        }

        private String processLeftMenuItem(C input)
        {
            LeftMenuItemSettingsDTO lmi = (LeftMenuItemSettingsDTO)input.getItem();
            if (lmi.getType() == MenuItemType.customLink)
            {
                return lmi.getReference() == null ? "" : templates.anchor(lmi.getReference().getTitle(),
                        UriUtils.fromString(lmi.getReference().getTitle()), lmi.isNewTab(),
                        SafeHtmlUtils.EMPTY_SAFE_HTML).asString();
            }
            if (MenuItemType.customButton.equals(lmi.getType()))
            {
                return metainfoUtils.getLocalizedValue(lmi.getUserEventTool().getTitle());
            }
            return lmi.getReference() == null ? "" : lmi.getReference().getTitle();
        }

        private String processMenuItem(C input)
        {
            MenuItem mi = (MenuItem)input.getItem();
            switch (mi.getType())
            {
                case reference:
                    return ((Reference)mi.getValue()).getTitle();
                case customLink:
                    return templates.anchor((String)mi.getValue(), UriUtils.fromString((String)mi.getValue()),
                            mi.isNewTab(), SafeHtmlUtils.EMPTY_SAFE_HTML).asString();
                case addButton:
                    return ((AddButtonValue)mi.getValue()).getValueAsString();
                case customButton:
                    return metainfoUtils.getLocalizedValue(((UserEventTool)mi.getValue()).getTitle());
                default:
                    return StringUtilities.EMPTY;
            }
        }
    }

    public static class NavigationLeftMenuItemAttributesPropertyProvider
            extends NavigationMenuItemAttributesPropertyProvider<LeftMenuItemSettingsDTO, NavigationLeftMenuItemContext>
    {
        @Inject
        private NavigationSettingsMessages navMessages;
        @Inject
        @Named(NavigationTabSettingsGinModule.MENU_FORMATTING_TITLES)
        private Map<MenuItemFormatting, String> menuFormatTitles;
        @Inject
        @Named(PropertiesGinModule.TEXT)
        private Property<String> format;
        @Inject
        @Named(PropertiesGinModule.TEXT)
        private Property<String> contentType;
        @Inject
        @Named(PropertiesGinModule.HTML_TEXT)
        private Property<String> template;
        @Inject
        @Named(PropertiesGinModule.HTML_TEXT)
        private Property<String> structure;
        @Inject
        @Named(PropertiesGinModule.HTML_TEXT)
        private Property<String> tags;

        @Inject
        private ContentTitles contentTitles;
        @Inject
        private CopyFromTemplateMessages copyFromTemplateMessages;
        @Inject
        private HierarchyGridMessages hierarchyGridMessages;
        @Inject
        private TagsMessages tagsMessages;
        @Inject
        private TagPropertyFormatter tagPropertyFormatter;

        @Override
        protected void addSpecialProperties(
                ArrayList<AttributePropertyDescription<?, NavigationLeftMenuItemContext>> result)
        {
            result.add(new AttributePropertyDescription<String, NavigationLeftMenuItemContext>(navMessages.formatting(),
                    format, "formatting", this::getFormatting));

            result.add(
                    new AttributePropertyDescription<String, NavigationLeftMenuItemContext>(navMessages.contentType(),
                            contentType, "contentType", this::getContentString));

            result.add(new AttributePropertyDescription<String, NavigationLeftMenuItemContext>(
                    copyFromTemplateMessages.template(), template, "template", this::getTemplate));

            result.add(new AttributePropertyDescription<String, NavigationLeftMenuItemContext>(
                    hierarchyGridMessages.structuredObjectsView(), structure, "structure",
                    this::getStructure, context -> HierarchyGrid.class.getSimpleName().equals(
                    context.getItem().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE))));

            result.add(
                    new AttributePropertyDescription<String, NavigationLeftMenuItemContext>(tagsMessages.tags(), tags,
                            "tags", this::getTags));
        }

        private String getTags(@Nullable NavigationLeftMenuItemContext input)
        {
            if (null == input)
            {
                return StringUtilities.EMPTY;
            }
            List<DtObject> elementTags = input.getItem().getProperty(Tag.ELEMENT_TAGS);
            if (CollectionUtils.isEmpty(elementTags))
            {
                return StringUtilities.EMPTY;
            }
            return tagPropertyFormatter.formatToAnchors(elementTags).asString();
        }

        private String getStructure(@Nullable NavigationLeftMenuItemContext input)
        {
            if (input == null || MenuItemType.linkToContent != input.getItem().getType())
            {
                return navMessages.notDefined();
            }

            String structureCode = input.getItem()
                    .getProperty(MenuItemLinkToContentCode.HIERARCHY_STRUCTURE);
            if (null == structureCode)
            {
                return StringUtilities.EMPTY;
            }
            String title = input.getItem().getProperty(MenuItemLinkToContentCode.HIERARCHY_STRUCTURE_TITLE);
            if (null == title)
            {
                title = structureCode;
            }
            StructuredObjectsViewPlace place = new StructuredObjectsViewPlace(structureCode);
            String uri = Objects.requireNonNull(StringUtilities.getHrefByToken(
                    placeHistoryMapper.getToken(place)));
            return templates
                    .anchorWithId(title, UriUtils.fromTrustedString(uri), "structure", false,
                            SafeHtmlUtils.EMPTY_SAFE_HTML)
                    .asString();
        }

        private String getTemplate(@Nullable NavigationLeftMenuItemContext input)
        {
            if (input == null || MenuItemType.linkToContent != input.getItem().getType()
                                 && MenuItemType.reference != input.getItem().getType())
            {
                return navMessages.notDefined();
            }

            if (MenuItemType.reference == input.getItem().getType())
            {
                String title = input.getItem().getProperty(LeftMenuItem.UI_TEMPLATE_TITLE);
                return null == title ? StringUtilities.EMPTY : title;
            }
            String contentType = input.getItem().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);
            if (HierarchyGrid.class.getSimpleName().equals(contentType))
            {
                String templateCode = input.getItem().getProperty(MenuItemLinkToContentCode.CONTENT_TEMPLATE);
                if (null == templateCode)
                {
                    return StringUtilities.EMPTY;
                }
                String title = input.getItem().getProperty(MenuItemLinkToContentCode.CONTENT_TEMPLATE_TITLE);
                if (null == title)
                {
                    title = templateCode;
                }
                ContentTemplatePlace place = new ContentTemplatePlace(templateCode);
                String uri = Objects.requireNonNull(StringUtilities.getHrefByToken(
                        placeHistoryMapper.getToken(place)));
                return templates
                        .anchorWithId(title, UriUtils.fromTrustedString(uri), "contentTemplate", false,
                                SafeHtmlUtils.EMPTY_SAFE_HTML)
                        .asString();
            }
            else
            {
                SimpleDtObject listTemplate = input.getItem().getProperty(
                        MenuItemLinkToContentCode.LIST_TEMPLATE);

                if (listTemplate == null)
                {
                    return StringUtilities.EMPTY;
                }
                ListTemplatePlace place = new ListTemplatePlace(listTemplate.getUUID());
                String html = StringUtilities.getHrefByToken(placeHistoryMapper.getToken(place));
                return templates
                        .anchorWithId(listTemplate.getTitle(), UriUtils.fromTrustedString(html),
                                "listTemplate", false, SafeHtmlUtils.EMPTY_SAFE_HTML)
                        .asString();
            }
        }

        private String getContentString(@Nullable NavigationLeftMenuItemContext input)
        {
            if (input == null || MenuItemType.linkToContent != input.getItem().getType())
            {
                return navMessages.notDefined();
            }
            if (StringUtilities.isEmpty(
                    input.getItem().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE)))
            {
                return StringUtilities.EMPTY;
            }
            return contentTitles.content(
                    input.getItem().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE));
        }

        private String getFormatting(@Nullable NavigationLeftMenuItemContext input)
        {
            if (input == null)
            {
                return StringUtilities.EMPTY;
            }
            if (StringUtilities.isEmpty(input.getItem().getUiFormatting()))
            {
                return StringUtilities.EMPTY;
            }
            return menuFormatTitles.get(MenuItemFormatting.valueOf(input.getItem().getUiFormatting()));
        }
    }

    public static class NavigationQuickAreaAttributesPropertyProvider
            implements Provider<ArrayList<AttributePropertyDescription<?, QuickAccessPanelAreaSettingsDTO>>>
    {
        @Inject
        private CommonMessages commonMessages;

        @Inject
        @Named(PropertiesGinModule.TEXT)
        private Property<String> description;
        @Inject
        @Named(PropertiesGinModule.BOOLEAN_IMAGE)
        private Property<Boolean> enabled;
        @Inject
        @Named(NavigationTabSettingsGinModule.QUICK_AREA_DESCRIPTIONS)
        private Map<String, String> quickAreaDescriptions;

        @Override
        public ArrayList<AttributePropertyDescription<?, QuickAccessPanelAreaSettingsDTO>> get()
        {
            ArrayList<AttributePropertyDescription<?, QuickAccessPanelAreaSettingsDTO>> result = new ArrayList<>();

            result.add(new AttributePropertyDescription<String, QuickAccessPanelAreaSettingsDTO>(
                    commonMessages.description(), description, "description",
                    input ->
                    {
                        if (input == null)
                        {
                            return StringUtilities.EMPTY;
                        }
                        return quickAreaDescriptions.get(input.getCode());
                    }));

            result.add(new AttributePropertyDescription<Boolean, QuickAccessPanelAreaSettingsDTO>(commonMessages.on(),
                    enabled, "enabled",
                    input ->
                    {
                        if (input == null)
                        {
                            return Boolean.FALSE;
                        }
                        return input.isEnabled();
                    }));

            return result;
        }
    }

    public static final String NAVIGATION_MENU_ITEM_ATTRIBUTES = "navigationMenuItemAttributes";
    public static final String NAVIGATION_LEFT_MENU_ITEM_ATTRIBUTES = "navigationLeftMenuItemAttributes";
    public static final String QUICK_ACCESS_PANEL_AREA_ITEM_ATTRIBUTES = "quickAccessPanelAreaAttributes";

    @Override
    protected void configure()
    {
        //@formatter:off
        bind(new TypeLiteral<ArrayList<AttributePropertyDescription<?, NavigationMenuItemContext<MenuItem>>>>(){})
            .annotatedWith(Names.named(NAVIGATION_MENU_ITEM_ATTRIBUTES))
            .toProvider(NavigationMenuItemAttributesPropertyProvider.class);

        bind(new TypeLiteral<ArrayList<AttributePropertyDescription<?, NavigationLeftMenuItemContext>>>(){})
                .annotatedWith(Names.named(NAVIGATION_LEFT_MENU_ITEM_ATTRIBUTES))
                .toProvider(NavigationLeftMenuItemAttributesPropertyProvider.class);

        bind(new TypeLiteral<ArrayList<AttributePropertyDescription<?, QuickAccessPanelAreaSettingsDTO>>>(){})
                .annotatedWith(Names.named(QUICK_ACCESS_PANEL_AREA_ITEM_ATTRIBUTES))
                .toProvider(NavigationQuickAreaAttributesPropertyProvider.class);
        //@formatter:on
    }
}