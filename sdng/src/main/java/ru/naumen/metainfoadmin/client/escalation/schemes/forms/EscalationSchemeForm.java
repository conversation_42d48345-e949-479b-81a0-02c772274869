package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector.PropertyContainerPresenterFactory;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.core.shared.escalation.EscalationSchemeClient;
import ru.naumen.metainfo.shared.dispatch2.SaveEscalationSchemeAction;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.EscalationSchemeFormPropertyCode;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinjector.EscalationSchemeFormCallbackFactory;

/**
 * <AUTHOR>
 * @since 24.07.2012
 *
 */
public class EscalationSchemeForm<F extends ObjectForm> extends OkCancelPresenter<DefaultPropertyFormDisplayImpl>
{
    private static final List<String> PROPERTIES = Arrays.asList(EscalationSchemeFormPropertyCode.TITLE,
            EscalationSchemeFormPropertyCode.CODE, EscalationSchemeFormPropertyCode.DESCRIPTION,
            EscalationSchemeFormPropertyCode.TARGET_OBJECTS, EscalationSchemeFormPropertyCode.TIMER,
            EscalationSchemeFormPropertyCode.SETTINGS_SET);
    @Inject
    PropertyContainerPresenterFactory containerFactory;
    @Inject
    PropertyControllerFactory<EscalationScheme, F> propertyFactory;
    @Inject
    Processor validation;
    @Inject
    I18nUtil i18nUtil;
    @Inject
    protected DispatchAsync dispatch;
    @Inject
    EscalationSchemeFormMessages<F> messages;

    @Inject
    EscalationSchemeFormCallbackFactory<F> callbackFactory;

    @Inject
    EscalationSchemePropertyContainerAfterBindHandler<F> afterBindHandler;
    protected IProperties contextProps = new MapProperties();
    protected IProperties propertyValues = new MapProperties();
    protected PropertyContainerPresenter propertyContainer;
    private final EventBus localEventBus;

    @Inject
    public EscalationSchemeForm(DefaultPropertyFormDisplayImpl display, EventBus eventBus,
            @Assisted EventBus localEventBus)
    {
        super(display, eventBus);
        this.localEventBus = localEventBus;
    }

    public EventBus getLocalEventBus()
    {
        return localEventBus;
    }

    public IProperties getPropertyValues()
    {
        return propertyValues;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        EscalationSchemeClient scheme = new EscalationSchemeClient();
        scheme.setCode(propertyValues.<String> getProperty(EscalationSchemeFormPropertyCode.CODE));
        i18nUtil.updateI18nObjectTitle(scheme,
                propertyValues.<String> getProperty(EscalationSchemeFormPropertyCode.TITLE));
        i18nUtil.updateI18nObjectDescription(scheme,
                propertyValues.<String> getProperty(EscalationSchemeFormPropertyCode.DESCRIPTION));
        scheme.setTargetTypes(Lists.newArrayList(Collections2.transform(
                propertyValues.<Collection<DtObject>> getProperty(EscalationSchemeFormPropertyCode.TARGET_OBJECTS),
                DtObject.FQN_EXTRACTOR)));
        scheme.setTimerCode(propertyValues.<String> getProperty(EscalationSchemeFormPropertyCode.TIMER));
        scheme.setState(propertyValues.<String> getProperty(EscalationSchemeFormPropertyCode.STATE));
        scheme.setSettingsSet(propertyValues.<String> getProperty(EscalationSchemeFormPropertyCode.SETTINGS_SET));

        dispatch.execute(new SaveEscalationSchemeAction(scheme, isNewEscalationScheme()), callbackFactory.create(this));
    }

    protected boolean isNewEscalationScheme()
    {
        return false;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.title());
        setProperties();
        propertyContainer = containerFactory.createSimple(PROPERTIES, getDisplay(), propertyFactory, contextProps,
                propertyValues, afterBindHandler, validation);
        registerHandler(propertyContainer.addUpdateTabOrderHandler(this));
        propertyContainer.bind();
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        if (null != propertyContainer)
        {
            propertyContainer.unbind();
        }
    }

    protected void setProperties()
    {
    }
}
