/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.command;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogItemCommandParam;
import ru.naumen.metainfoadmin.client.catalog.command.EditCatalogItemCommand;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormFactory;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.forms.EscalationValueMapItemFormContext;

import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EditEscalationVMapItemCommand extends EditCatalogItemCommand
{
    @Inject
    CatalogItemFormFactory<EscalationValueMapItemFormContext> formFactory;

    @Inject
    public EditEscalationVMapItemCommand(@Assisted CatalogItemCommandParam<DtObject> param)
    {
        super(param);
    }

    @Override
    protected CallbackPresenter<DtObject, IProperties> getPresenter(DtObject value)
    {
        EscalationValueMapItemFormContext context = formFactory.createContext(getCatalog(), isFolder(value), value);
        return formFactory.createEditForm(context);
    }
}