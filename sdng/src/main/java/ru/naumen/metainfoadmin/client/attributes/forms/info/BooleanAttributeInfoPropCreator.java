package ru.naumen.metainfoadmin.client.attributes.forms.info;

import static ru.naumen.metainfo.shared.Constants.SEMANTIC_FILTRATION_NOT_ALLOWED_ATTRS;
import static ru.naumen.metainfo.shared.Constants.TYPES_FOR_ADVLIST_SEMANTIC_FILTERING;

import jakarta.inject.Inject;

import com.google.common.collect.ImmutableList;

import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Создает {@link Property} для отображения информации о
 * параметрах, имеющих логический тип на модальной форме свойств атрибута 
 *
 * <AUTHOR>
 * @since 3 авг. 2018 г.
 */
public class BooleanAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    private static final ImmutableList<String> SHOW_ALL_TIME = ImmutableList.of(
            AttributeFormPropertyCode.EDITABLE, AttributeFormPropertyCode.EDITABLE_IN_LISTS,
            AttributeFormPropertyCode.REQUIRED, AttributeFormPropertyCode.REQUIRED_IN_INTERFACE,
            AttributeFormPropertyCode.UNIQUE);

    private final SharedSettingsClientService settingsClientService;

    @Inject
    public BooleanAttributeInfoPropCreator(SharedSettingsClientService settingsClientService)
    {
        this.settingsClientService = settingsClientService;
    }

    @Override
    protected void createInt(String code)
    {
        boolean value = propertyValues.getProperty(code);
        if (SHOW_ALL_TIME.contains(code))
        {
            createProperty(code, value ? messages.yes() : messages.no());
        }
        else if (value)
        {
            createPropertyForNonShowAllTime(code);
        }
    }

    private void createPropertyForInherit()
    {
        createProperty(AttributeFormPropertyCode.INHERIT, messages.yes(), getTitleForInherit());
    }

    private void createPropertyForNonShowAllTime(String code)
    {
        if (code.equals(AttributeFormPropertyCode.INHERIT))
        {
            createPropertyForInherit();
        }
        else if (notNeedHideAdvlistSemanticFiltering(code))
        {
            createProperty(code, messages.yes());
        }
    }

    private String getTitleForInherit()
    {
        return attribute.getMetaClassLite().getFqn().isClass() ? messages.useSystemParams() : messages.inheritParams();
    }

    private boolean notNeedHideAdvlistSemanticFiltering(String code)
    {
        return !(AttributeFormPropertyCode.ADVLIST_SEMANTIC_FILTERING.equals(code)
                 && (!settingsClientService.isDatabaseFTSEnabled()
                     || !TYPES_FOR_ADVLIST_SEMANTIC_FILTERING.contains(attribute.getType().getCode())
                     || SEMANTIC_FILTRATION_NOT_ALLOWED_ATTRS.contains(attribute.getFqn())));
    }
}
