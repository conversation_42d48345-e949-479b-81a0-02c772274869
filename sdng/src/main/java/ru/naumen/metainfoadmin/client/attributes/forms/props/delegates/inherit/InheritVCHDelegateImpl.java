package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inherit;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * Обработчик изменения значения свойства "Наследовать параметры". Включает или выключает много других свойств.
 * <AUTHOR>
 * @since 17.05.2012
 */
public class InheritVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    private final static String[] propsToEnable = new String[] { TITLE, DETERMINABLE, EDITABLE, COMPLEX_RELATION,
            COMPLEX_RELATION_ATTR_GROUP, COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW, EDITABLE_IN_LISTS, REQUIRED,
            REQUIRED_IN_INTERFACE, UNIQUE, SCRIPT, DETERMINER, SHOW_PRS, EDIT_PRS, HAS_GROUP_SEPARATORS,
            DIGITS_COUNT_RESTRICTION, FILTERED_BY_SCRIPT, SCRIPT_FOR_FILTRATION, COMPUTABLE_ON_FORM,
            COMPUTABLE_ON_FORM_SCRIPT, DEFAULT_BY_SCRIPT, SCRIPT_FOR_DEFAULT, SUGGEST_CATALOG, SELECT_SORTING,
            USE_GEN_RULE, GEN_RULE, COMPOSITE, TEMPLATE, DESCRIPTION, INPUTMASK, INPUTMASK_MODE,
            COMPLEX_EMPLOYEE_ATTR_GROUP, COMPLEX_OU_ATTR_GROUP, COMPLEX_TEAM_ATTR_GROUP, EXPORT_NDAP, NEED_STORE_UNITS,
            INTERVAL_AVAILABLE_UNITS, HIDDEN_WHEN_EMPTY, HIDDEN_WHEN_NO_POSSIBLE_VALUES, QUICK_ADD_FORM_CODE,
            QUICK_EDIT_FORM_CODE, ATTR_CHAIN, RELATED_OBJECT_METACLASS, RELATED_OBJECT_ATTRIBUTE, ATTR_CHAIN_VIEW,
            RELATED_OBJECT_HIERARCHY_LEVEL, DATE_TIME_COMMON_RESTRICTIONS, DATE_TIME_RESTRICTION_TYPE,
            DATE_TIME_RESTRICTION_SCRIPT, DATE_TIME_RESTRICTION_ATTRIBUTE, DATE_TIME_RESTRICTION_CONDITION, TAGS,
            SETTINGS_SET, ADVLIST_SEMANTIC_FILTERING, RELATED_ATTRS_TO_EXPORT, HIDDEN_ATTR_CAPTION,
            EDIT_ON_COMPLEX_FORM_ONLY, HIDE_ARCHIVED, STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE };

    private final static String[] propsToInherit = new String[] { SCRIPT, SCRIPT_FOR_FILTRATION,
            COMPUTABLE_ON_FORM_SCRIPT, SCRIPT_FOR_DEFAULT, DATE_TIME_RESTRICTION_SCRIPT, HIDE_ARCHIVED };

    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        Boolean inherited = context.getPropertyValues().getProperty(INHERIT);
        context.setEnabled(DEFAULT_VALUE, !inherited);
        context.setEnabled(TARGET, !inherited);
        for (String property : propsToEnable)
        {
            context.setPropertyEnabled(property, !inherited);
        }

        for (String property : propsToInherit)
        {
            context.getPropertyControllers().get(property).setInherited(inherited);
        }

        context.getPropertyControllers().get(COMPOSITE).refreshSeparatly();
        context.getPropertyControllers().get(REQUIRED_IN_INTERFACE).refreshSeparatly();
        context.getPropertyControllers().get(TAGS).refreshSeparatly();
        context.getPropertyControllers().get(SETTINGS_SET).refreshSeparatly();
        context.getPropertyControllers().get(DATE_TIME_COMMON_RESTRICTIONS).refreshSeparatly();
        context.getPropertyControllers().get(ADVLIST_SEMANTIC_FILTERING).refreshSeparatly();
        context.getPropertyControllers().get(INHERIT).fireUpdateTabOrderEvent();
    }
}
