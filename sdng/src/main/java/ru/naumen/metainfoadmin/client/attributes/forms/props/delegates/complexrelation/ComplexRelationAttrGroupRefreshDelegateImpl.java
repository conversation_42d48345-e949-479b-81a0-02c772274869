package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation;

import static ru.naumen.core.shared.Constants.PARENT_ATTR;
import static ru.naumen.metainfo.shared.Constants.LINK_ATTRIBUTE_TYPES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.ComplexRelationType;
import ru.naumen.metainfoadmin.client.attributes.forms.DirectLinkValueFormatter;

/**
 * Делегат обновления поля "Группа атрибутов в списке"
 * <AUTHOR>
 * @since 22.09.2015
 */
public class ComplexRelationAttrGroupRefreshDelegateImpl<F extends ObjectForm> extends
        AbstractComplexRelationAttrGroupRefreshDelegateImpl<F, ListBoxProperty>
{
    @Inject
    private DirectLinkValueFormatter valueFormatter;

    public ComplexRelationAttrGroupRefreshDelegateImpl()
    {
        super(COMPLEX_RELATION_ATTR_GROUP);
    }

    @Override
    protected List<ClassFqn> getFqns(PropertyContainerContext context)
    {
        ClassFqn classFqn = getClassFqn(context);
        Collection<DtObject> types = context.getPropertyValues().getProperty(PERMITTED_TYPES);

        if (CollectionUtils.isEmpty(types))
        {
            return Lists.newArrayList(classFqn);
        }

        List<ClassFqn> fqns = new ArrayList<>();
        for (DtObject type : types)
        {
            if (type.getMetainfo().equals(Constants.NOONE))
            {
                fqns.add(classFqn);
            }
            else
            {
                fqns.add(type.getMetainfo());
            }
        }
        return fqns;
    }

    @Override
    protected boolean show(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(ATTR_TYPE);
        if (!LINK_ATTRIBUTE_TYPES.contains(attrType))
        {
            return false;
        }

        Boolean editable = context.getPropertyValues().getProperty(EDITABLE, false);
        if (!editable && !PARENT_ATTR.equals(context.getPropertyValues().getProperty(CODE)))
        {
            //Если атрибут не доступен для редактирования, то настройка сложной формы не отображается
            return false;
        }
        if (!ComplexRelationType.FLAT.getCode().equals(context.getPropertyValues().getProperty(COMPLEX_RELATION))
            && !ComplexRelationType.FLAT_WITH_FULL_TEXT_SEARCH.getCode()
                .equals(context.getPropertyValues().getProperty(COMPLEX_RELATION)))
        {
            return false;
        }
        ClassFqn classFqn = getClassFqn(context);
        if (classFqn == null)
        {
            return true;
        }
        return true;
    }

    private ClassFqn getClassFqn(PropertyContainerContext context)
    {
        String clazz;
        String directLinkTarget = context.getPropertyValues().getProperty(DIRECT_LINK_TARGET);
        String targetClass = context.getPropertyValues().getProperty(TARGET_CLASS);
        clazz = targetClass != null ? targetClass : directLinkTarget;
        if (clazz == null)
        {
            return null;
        }
        return valueFormatter.fqnFrom(clazz);
    }
}
