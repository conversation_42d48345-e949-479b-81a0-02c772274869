package ru.naumen.metainfoadmin.client.escalation.schemes.commands;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;

/**
 * <AUTHOR>
 * @since 24.07.2012
 *
 */
public class EscalationSchemesCommandsGinModule extends AbstractGinModule
{
    public interface EscalationCommandCode
    {
        String TOGGLE = "toggleEscalationSchemes";
        String EDIT = "editEscalationSchemes";
        String DELETE = "deleteEscalationSchemes";
    }

    @Override
    protected void configure()
    {
        bind(EscalationSchemesCommandFactoryInitializer.class).asEagerSingleton();

        //@formatter:off     
        install(Gin.install(
                new TypeLiteral<CommandProvider<EscalationSchemeEditCommand, EscalationSchemesCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<DtoContainer<EscalationScheme>>>(){},
                new TypeLiteral<EscalationSchemeEditCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EscalationSchemeDeleteCommand, EscalationSchemesCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<DtoContainer<EscalationScheme>>>(){},
                new TypeLiteral<EscalationSchemeDeleteCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EscalationSchemeToggleCommand, EscalationSchemesCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<DtoContainer<EscalationScheme>>>(){},
                new TypeLiteral<EscalationSchemeToggleCommand>(){}));    
        //@formatter:on
    }
}