package ru.naumen.metainfoadmin.client.attributes.columns;

import jakarta.inject.Inject;

import com.google.common.base.Predicates;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.dom.client.MouseOverEvent;
import com.google.gwt.event.dom.client.MouseOverHandler;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.Label;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.EditFormAvailibleTypesProvider;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.AttributeListCss;
import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.client.widgets.grouplist.AbstractBaseColumn;
import ru.naumen.core.client.widgets.grouplist.ButtonColumnFactory;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandCode;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;

/**
 * Отображается три параметра: "Название атрибута", "Код атрибута" и "Тип атрибута".
 * Для типа атрибута используется его название на русском (аналогичное на форме добавления и редактирования).
 * Рядом с названием отображается иконка, по нажатию на которой вызывается модальная форма "Места использования
 * атрибута".
 *
 * <AUTHOR>
 * @since 26 июл. 2018 г.
 *
 */
public class AttrTitleCodeTypeColumn extends AbstractBaseColumn<Attribute>
{
    /**
     * Добавляет hint, если название не влазит в отведенную ширину
     *
     * <AUTHOR>
     * @since 19 сент. 2018 г.
     *
     */
    private class AddHintIfNeededMouseOverHandler implements MouseOverHandler
    {
        private Label label;

        public AddHintIfNeededMouseOverHandler(Label label)
        {
            this.label = label;
        }

        @Override
        public void onMouseOver(MouseOverEvent event)
        {
            if (label.getElement().getOffsetWidth() < label.getElement().getScrollWidth()
                && StringUtilities.isEmpty(label.getTitle()))
            {
                label.setTitle(label.getText());
            }
        }
    }

    private ButtonColumnFactory<Attribute> buttonColumnFactory;
    private CommandFactory commandFactory;
    private final String hint;

    /**
     * Нужен для получения названий типов
     */
    private EditFormAvailibleTypesProvider availibleTypesProvider;

    private final RegistrationContainer registrationContainer;

    @Inject
    public AttrTitleCodeTypeColumn(ButtonColumnFactory<Attribute> buttonColumnFactory,
            CommandFactory commandFactory, EditFormAvailibleTypesProvider availibleTypesProvider,
            CommonMessages messages, @Assisted String debugId, @Assisted RegistrationContainer registrationContainer)
    {
        super(debugId);
        this.buttonColumnFactory = buttonColumnFactory;
        this.commandFactory = commandFactory;
        this.availibleTypesProvider = availibleTypesProvider;
        this.hint = messages.useInSettings();
        this.registrationContainer = registrationContainer;
    }

    @Override
    public IsWidget createWidget(WidgetContext<Attribute> context)
    {
        final FlowPanel result = new FlowPanel();
        AttributeListCss style = AdminWidgetResources.INSTANCE.attributeList();

        FlowPanel firstLine = new FlowPanel();
        firstLine.addStyleName(style.titleAndInfoLine());
        Attribute attr = context.getContextObject();

        if (context instanceof PresentationContext)
        {
            @SuppressWarnings("unchecked")
            IsWidget showUsageButton = buttonColumnFactory.create(AttributeColumnCode.SHOW_USAGE_BUTTON,
                    (ClosureCommand<Attribute>)commandFactory.create(AttributeCommandCode.SHOW_USAGE,
                            new AttributeCommandParam(null, null, ((PresentationContext)context).getParentContext())),
                    IconCodes.HINT, Predicates.alwaysTrue()).createWidget(context);
            DebugIdBuilder.ensureDebugId(showUsageButton.asWidget(), attr.getCode(),
                    AttributeColumnCode.SHOW_USAGE_BUTTON);
            // Хак. Установку кастомных хинтов на иконки надо сделать какой-то системный механизм.
            ((Element)showUsageButton.asWidget().getElement().getChild(0)).setTitle(hint);
            firstLine.add(showUsageButton);
        }

        FlowPanel attrTitleContainer = new FlowPanel();
        InlineLabel attrTitle = new InlineLabel(attr.getTitle());
        attrTitle.setStyleName(style.titleBadge());
        if (attr.isHardcoded())
        {
            attrTitleContainer.addStyleName(style.hardcodedAttribute());
        }
        registrationContainer.registerHandler(
                attrTitle.addMouseOverHandler(new AddHintIfNeededMouseOverHandler(attrTitle)));
        attrTitleContainer.add(attrTitle);
        firstLine.add(attrTitleContainer);
        FlowPanel secondLine = new FlowPanel();
        secondLine.addStyleName(style.typeAndCodeLine());
        FlowPanel codeBadgeContainer = new FlowPanel();
        codeBadgeContainer.setStyleName(style.codeBadgeContainer());
        InlineLabel attrCode = new InlineLabel(attr.getCode());
        attrCode.setStyleName(style.codeBadge());
        codeBadgeContainer.add(attrCode);
        secondLine.add(codeBadgeContainer);
        FlowPanel typeBadgeContainer = new FlowPanel();
        typeBadgeContainer.setStyleName(style.typeBadgeContainer());
        InlineLabel attrType = new InlineLabel(getTypeTitle(attr.getType().isAttributeOfRelatedObject()
                ? AttributeOfRelatedObjectSettings.CODE
                : attr.getType().getCode()));
        attrType.setStyleName(style.typeBadge());
        registrationContainer.registerHandler(
                attrType.addMouseOverHandler(new AddHintIfNeededMouseOverHandler(attrType)));
        typeBadgeContainer.add(attrType);
        secondLine.add(typeBadgeContainer);
        result.add(firstLine);
        result.add(secondLine);
        return result;
    }

    private String getTypeTitle(String code)
    {
        return availibleTypesProvider.getAttributeTitle(code);
    }
}
