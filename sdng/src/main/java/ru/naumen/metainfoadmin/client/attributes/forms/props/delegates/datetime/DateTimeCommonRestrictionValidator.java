package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.validation.HasValidation;
import ru.naumen.core.client.validation.ValidateEvent;
import ru.naumen.core.client.validation.ValidationMessages;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.metainfo.shared.elements.CommonRestriction;

/**
 * Валидатор параметра "Значение допустимо указывать" для полей типа Дата и Дата / Время
 * <AUTHOR>
 * @since 12 февр. 2019 г.
 *
 */
@Singleton
public class DateTimeCommonRestrictionValidator implements Validator<Collection<CommonRestriction>>
{

    private final ValidationMessages messages;

    @Inject
    public DateTimeCommonRestrictionValidator(ValidationMessages messages)
    {
        this.messages = messages;
    }

    @Override
    public boolean validate(HasValueOrThrow<Collection<CommonRestriction>> hasValue)
    {
        boolean isValid = hasValue.getValue() != null && !hasValue.getValue().isEmpty();
        if (!isValid && hasValue instanceof HasValidation)
        {
            addNotEmptyMessage((HasValidation)hasValue);
        }
        return isValid;
    }

    @Override
    public void validateAsync(HasValueOrThrow<Collection<CommonRestriction>> hasValue, ValidateEvent event)
    {
        validate(hasValue);
    }

    protected void addNotEmptyMessage(HasValidation property)
    {
        property.addValidationMessage(messages.dateTimeCommonRestrictionCantBeEmpty());
    }

}
