/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import static ru.naumen.metainfo.shared.ui.Tool.PresentationType.*;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.common.collect.Lists;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.gwt.user.client.DOM;
import com.google.gwt.user.client.ui.RootPanel;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.content.toolbar.ToolDNDController;
import ru.naumen.core.client.content.toolbar.ToolDNDControllerFactory;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.Tool.AppliedToType;
import ru.naumen.metainfo.shared.ui.Tool.PresentationType;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.commands.EditToolPanelCommandGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.EditToolPanelFormPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.EditToolPanelFormPresenterFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.ToolPanelContentPresenterEdit;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.InvocationMethodValidator;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.InvocationMethodValidatorFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyControllerFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyParametersDescriptorFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.QuickFormBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.QuickFormBindDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.CustomFormRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.CustomFormRefreshDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.QuickFormVCHDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.QuickFormVCHDelegete;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.UseCustomFormVCHDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.UseCustomFormVCHDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.view.ToolPanelContentPresenterView;

/**
 * <AUTHOR>
 * @since 19 мая 2015 г.
 *
 */
public class EditableToolPanelGinModule extends AbstractGinModule
{
    /**
     * Provider для div'а, который отмечает место между тулами, куда будет помещен другой перетягиваемый тул
     * Просто черная вертикальная полоска, которая помещается за границы экрана, когда dnd закончился
     * <AUTHOR>
     * @since 05 июня 2015 г.
     *
     */
    public static class InsertionMarkerProvider implements Provider<Element>
    {
        @Inject
        private WidgetResources resources;

        @Override
        public Element get()
        {
            Element result = DOM.getElementById(INSERTION_MARKER);
            if (result != null)
            {
                return result;
            }
            result = DOM.createDiv();
            result.setId(INSERTION_MARKER);
            result.addClassName(resources.buttons().dndInsertionMarker());
            RootPanel.getBody().getElement().appendChild(result);

            result.getStyle().setTop(0, Unit.PX);
            result.getStyle().setLeft(-4, Unit.PX);

            return result;
        }
    }

    public static class ToolApplicabilityProvider implements Provider<List<Pair<String, String>>>
    {
        @Inject
        private EditableToolPanelMessages messages;

        @Override
        public List<Pair<String, String>> get()
        {
            List<Pair<String, String>> result = new ArrayList<>();
            result.add(Pair.create(AppliedToType.CURRENT_OBJECT, messages.currentObject()));
            result.add(Pair.create(AppliedToType.RELATED_OBJECT, messages.relatedObject()));
            result.add(Pair.create(AppliedToType.LIST_OBJECTS, messages.listObjects()));
            return result;
        }
    }

    public static class ToolPresentationsProvider implements Provider<List<Pair<PresentationType, String>>>
    {
        @Inject
        private EditableToolPanelMessages messages;

        @Override
        public List<Pair<PresentationType, String>> get()
        {
            List<Pair<PresentationType, String>> result = new ArrayList<>();
            result.add(Pair.create(DEFAULT, messages.withIconAndText()));
            result.add(Pair.create(DEFAULT_TEXT_ONLY, messages.withoutIcon()));
            result.add(Pair.create(DEFAULT_ICON_ONLY, messages.withoutText()));
            result.add(Pair.create(LINK, messages.link()));
            result.add(Pair.create(ICON, messages.iconsForControls()));
            result.add(Pair.create(ICON_TEXT, messages.iconWithText()));
            return result;
        }
    }

    public static final String TOOLS_PREFIX = "tools";
    public static final String SEPARATOR = "separator";
    public static final String FAST_CHANGE_STATE_SEPARATOR = "fastChangeStateSeparator";
    public static final String INSERTION_MARKER = "insertionMarker";
    public static final String TOOL_PRESENTATIONS = "toolModes";
    public static final String TOOL_APPLICABILITY = "toolApplicability";
    public static final List<String> SEPARATORS = Lists.newArrayList(EditableToolPanelGinModule.SEPARATOR,
            EditableToolPanelGinModule.FAST_CHANGE_STATE_SEPARATOR);

    @Override
    protected void configure()
    {
        //@formatter:off      
        bind(new TypeLiteral<ToolPanelContentPresenterEdit<UIContext>>(){});
        bind(new TypeLiteral<ToolPanelContentPresenterView<UIContext>>(){});
        install(new EditToolPanelCommandGinModule());
        install(new GinFactoryModuleBuilder()
                .implement(new TypeLiteral<ToolDNDController>(){}, new TypeLiteral<ToolDNDControllerAdminImpl>(){})
                .build(new TypeLiteral<ToolDNDControllerFactory>(){}));  
        install(new GinFactoryModuleBuilder()
                .implement(new TypeLiteral<EditToolPanelFormPresenter>(){}, new TypeLiteral<EditToolPanelFormPresenter>(){})
                .build(new TypeLiteral<EditToolPanelFormPresenterFactory>(){}));  
        
        bind(Element.class)
            .annotatedWith(Names.named(INSERTION_MARKER))
            .toProvider(InsertionMarkerProvider.class)
            .in(Singleton.class);
        bind(new TypeLiteral<List<Pair<PresentationType, String>>>(){})
            .annotatedWith(Names.named(TOOL_PRESENTATIONS))
            .toProvider(ToolPresentationsProvider.class)
            .in(Singleton.class);
        bind(new TypeLiteral<List<Pair<String, String>>>(){})
            .annotatedWith(Names.named(TOOL_APPLICABILITY))
            .toProvider(ToolApplicabilityProvider.class)
            .in(Singleton.class);

        install(PropertyControllerGinModule.create(Tool.class, ObjectForm.class)
                .setPropertyControllerFactory(new TypeLiteral<ToolFormPropertyControllerFactory>(){})
                .setPropertyParametersDescriptorFactory(new TypeLiteral<ToolFormPropertyParametersDescriptorFactory>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(UseCustomFormVCHDelegate.class, UseCustomFormVCHDelegate.class)
                .build(UseCustomFormVCHDelegateFactory.class));
        install(new GinFactoryModuleBuilder()
                .implement(CustomFormRefreshDelegate.class, CustomFormRefreshDelegate.class)
                .build(CustomFormRefreshDelegateFactory.class));
        install(new GinFactoryModuleBuilder()
                .implement(QuickFormBindDelegate.class, QuickFormBindDelegate.class)
                .build(QuickFormBindDelegateFactory.class));
        install(new GinFactoryModuleBuilder()
                .implement(InvocationMethodValidator.class, InvocationMethodValidator.class)
                .build(InvocationMethodValidatorFactory.class));
        install(new GinFactoryModuleBuilder()
                .implement(QuickFormVCHDelegete.class, QuickFormVCHDelegete.class)
                .build(QuickFormVCHDelegateFactory.class));
        //@formatter:on
    }
}