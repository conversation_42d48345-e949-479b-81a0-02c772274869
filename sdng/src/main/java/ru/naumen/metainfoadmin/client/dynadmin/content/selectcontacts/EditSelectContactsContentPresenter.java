package ru.naumen.metainfoadmin.client.dynadmin.content.selectcontacts;

import java.util.Collection;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ui.SelectContacts;
import ru.naumen.metainfo.shared.ui.SelectContacts.AttributeDescription;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.EditPropertyListContentPresenterBase;

/**
 * {@link Presenter} для редактирования контента типа {@link SelectContacts}
 *
 * <AUTHOR>
 *
 */
public class EditSelectContactsContentPresenter extends EditPropertyListContentPresenterBase<SelectContacts>
{
    @Named(PropertiesGinModule.MULTI_SELECT_BOX)
    @Inject
    private SelectListProperty<Collection<String>, Collection<SelectItem>> attributes;

    @Inject
    public EditSelectContactsContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void bindCustomProperties()
    {
        bindAttributes();
    }

    private void bindAttributes()
    {
        attributes = contentUtils.createContactAttributesProperty(content);
        DebugIdBuilder.ensureDebugId(attributes, "contactAttributes");
        getDisplay().add(attributes);
    }

    @Override
    public void updateCurrentContent()
    {
        super.updateCurrentContent();
        content.getAttributes().clear();
        content.getAttributes().addAll(AttributeDescription.parse(SelectListPropertyValueExtractor.getCollectionValue(
                attributes)));
    }

    @Override
    protected void restoreContent(SelectContacts oldContent)
    {
        super.restoreContent(oldContent);
        content.getAttributes().clear();
        content.getAttributes().addAll(oldContent.getAttributes());
    }

    @Override
    protected boolean isContentEquals(SelectContacts oldContent)
    {
        return super.isContentEquals(oldContent)
               && eq(oldContent.getAttributes(), content.getAttributes());
    }
}
