package ru.naumen.metainfoadmin.client.escalation.schemes.commands;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;

/**
 * Параметры для команд схем эскалации
 *
 * <AUTHOR>
 * @since 14.12.2018
 */
public class EscalationSchemesCommandParam extends CommandParam<DtoContainer<EscalationScheme>, Void>
{
    private final EscalationSchemeCommandContext context;

    public EscalationSchemesCommandParam(@Nullable ValueSource<DtoContainer<EscalationScheme>> valueSource,
            @Nullable AsyncCallback<Void> callback,
            EscalationSchemeCommandContext context)
    {
        super(valueSource, callback);
        this.context = context;
    }

    @SuppressWarnings("unchecked")
    @Override
    public EscalationSchemesCommandParam cloneIt()
    {
        return new EscalationSchemesCommandParam(getValueSource(), getCallback(), getContext());
    }

    public EscalationSchemeCommandContext getContext()
    {
        return context;
    }
}
