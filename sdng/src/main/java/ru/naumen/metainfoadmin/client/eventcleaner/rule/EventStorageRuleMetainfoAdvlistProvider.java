package ru.naumen.metainfoadmin.client.eventcleaner.rule;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

import jakarta.inject.Inject;

import ru.naumen.admin.client.eventcleaner.EventCleanerSettingsMessages;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.EventStorageRuleEvents;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventStorageRule;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventStorageRule.Attributes;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType_SnapshotObject;
import ru.naumen.metainfo.shared.elements.Attribute_SnapshotObject;
import ru.naumen.metainfo.shared.elements.Presentation_SnapshotObject;
import ru.naumen.metainfoadmin.client.eventcleaner.EventCleanerJobPresenterSettings;
import ru.naumen.objectlist.client.metainfo.FakeMetainfoAdvlistProviderBase;

/**
 * Провайдер метаинформации для списка правил
 * <AUTHOR>
 * @since 09.07.2023
 */
public class EventStorageRuleMetainfoAdvlistProvider extends FakeMetainfoAdvlistProviderBase
{
    @Inject
    private EventCleanerSettingsMessages eventCleanerMessages;
    @Inject
    private EventCleanerJobPresenterSettings settings;

    @Override
    protected Map<String, Attribute> createAttributes()
    {
        Map<String, Attribute> attrs = new HashMap<>(super.createAttributes());

        attrs.put(Attributes.ATTR_EVENTS.toString(),
                createEventsAttribute(Attributes.ATTR_EVENTS, eventCleanerMessages.eventStorageRuleEventTypes()));
        attrs.put(Attributes.ATTR_ONLY_CLASSES.toString(),
                createClassesAttr(Attributes.ATTR_ONLY_CLASSES, cmessages.objects()));
        attrs.put(Attributes.ATTR_STORAGE_TIME.toString(),
                createIntegerAttribute(Attributes.ATTR_STORAGE_TIME,
                        eventCleanerMessages.eventStorageRuleStorageTime()));
        attrs.put(Attributes.ATTR_ENABLED.toString(), createBooleanAttr(Attributes.ATTR_ENABLED, cmessages.on()));

        attrCodes.add(Attributes.ATTR_EVENTS.toString());
        attrCodes.add(Attributes.ATTR_ONLY_CLASSES.toString());
        attrCodes.add(Attributes.ATTR_STORAGE_TIME.toString());
        attrCodes.add(Attributes.ATTR_ENABLED.toString());

        return attrs;
    }

    @Override
    protected AttributeFqn getAttrCode()
    {
        return null;
    }

    @Override
    protected AttributeFqn getAttrTitle()
    {
        return Attributes.ATTR_TITLE;
    }

    @Override
    protected ClassFqn getClassFqn()
    {
        return EventStorageRule.FQN;
    }

    private Attribute createEventsAttribute(AttributeFqn attrFqn, String title)
    {
        Attribute_SnapshotObject attr = new Attribute_SnapshotObject();
        attr.__init__fqn(attrFqn);
        attr.__init__code(attrFqn.getCode());

        AttributeType_SnapshotObject actionType = new AttributeType_SnapshotObject();
        actionType.__init__attribute(attr);
        actionType.__init__code(EventStorageRuleEvents.CODE);
        actionType.__init__permittedTypes(new HashSet<>());

        attr.__init__type(actionType);
        attr.__init__computable(false);
        attr.__init__title(title);
        attr.__init__metaClass(getMetaClass());
        attr.__init__metaClassLite(getMetaClassLite());
        attr.__init__filteredByScript(false);
        attr.__init__hiddenAttrCaption(false);

        Presentation_SnapshotObject editPresentation = new Presentation_SnapshotObject();
        editPresentation.__init__code(Presentations.EVENT_STORAGE_RULE_EVENTS_EDIT);
        attr.__init__editPresentation(editPresentation);

        Presentation_SnapshotObject viewPresentation = new Presentation_SnapshotObject();
        viewPresentation.__init__code(Presentations.EVENT_STORAGE_RULE_EVENTS_VIEW);
        attr.__init__viewPresentation(viewPresentation);

        return attr;
    }

    private Attribute createClassesAttr(AttributeFqn attrFqn, String title)
    {
        Attribute attr = super.createLinkedClassesAttr(attrFqn, title);
        if (settings.isWithObjectLinks())
        {
            return attr;
        }
        Presentation_SnapshotObject viewPresentation = new Presentation_SnapshotObject();
        viewPresentation.__init__code(Presentations.UNKNOWN_VIEW);
        ((Attribute_SnapshotObject)attr).__init__viewPresentation(viewPresentation);
        return attr;
    }
}
