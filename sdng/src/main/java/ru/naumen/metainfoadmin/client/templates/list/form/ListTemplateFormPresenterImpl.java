package ru.naumen.metainfoadmin.client.templates.list.form;

import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isClass;
import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isNotHidden;
import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isNotSystem;
import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isPossible;
import static ru.naumen.metainfoadmin.client.dynadmin.ContentUtils.MAX_CODE_LENGTH;

import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.dom.client.BlurEvent;
import com.google.gwt.event.dom.client.BlurHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorMessages;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMessages;

/**
 * Базовая форма шаблона списка
 * <AUTHOR>
 * @since 06.04.2018
 */
public abstract class ListTemplateFormPresenterImpl extends OkCancelPresenter<PropertyDialogDisplay>
        implements CallbackPresenter<DtObject, DtObject>
{
    @Inject
    protected Processor validation;
    @Inject
    protected NotEmptyValidator notEmptyValidator;
    @Inject
    protected DispatchAsync service;
    @Inject
    protected ListTemplatesMessages messages;
    @Inject
    private SecurityHelper security;
    @Inject
    private TransliterationService transliterationService;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private MetainfoServiceAsync metainfoService;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> code;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    protected SelectListProperty<String, SelectItem> classList;
    @Named(PropertiesGinModule.MULTI_SELECT_BOX)
    @Inject
    protected SelectListProperty<Collection<String>, Collection<SelectItem>> caseList;
    protected Property<SelectItem> settingsSet;
    @Inject
    protected ContentCreatorMessages contentCreatorMessages;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    protected AsyncCallback<DtObject> refreshCallback;
    protected DtObject listTemplate;

    public ListTemplateFormPresenterImpl(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(DtObject listTemplate, AsyncCallback<DtObject> refreshCallback)
    {
        this.listTemplate = listTemplate;
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void refreshDisplay()
    {
    }

    protected void bindProperties()
    {
        title.setCaption(cmessages.title());
        title.setValidationMarker(true);
        title.setMaxLength(Constants.MAX_METAINFO_TITLE_LENGTH);
        code.setCaption(cmessages.code());
        code.setValidationMarker(true);
        code.setMaxLength(Constants.MAX_METAINFO_KEY_LENGTH);

        classList.setCaption(contentCreatorMessages.objectClass());
        caseList.setCaption(contentCreatorMessages.objectsTypes());

        getDisplay().add(title);
        getDisplay().add(code);
        getDisplay().add(classList);
        getDisplay().add(caseList);
        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getDisplay(), getInitialSettingsSetValue());

        validation.validate(title, notEmptyValidator);
        addCodeValidator();
        ensureDebugIds();
    }

    protected void addCodeValidator()
    {
        //на форме редактирования валидатор не нужен
    }

    protected abstract String getInitialSettingsSetValue();

    @Override
    protected void onBind()
    {
        bindProperties();
        super.onBind();

        if (listTemplate != null)
        {
            code.setValue(listTemplate.getUUID());
            title.setValue(listTemplate.getTitle());
        }

        showClasses();

        classList.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                showCases(ClassFqn.parse(SelectListPropertyValueExtractor.getValue(classList)));
            }
        });

        registerHandler(title.asWidget().addHandler(new BlurHandler()
        {
            @Override
            public void onBlur(BlurEvent event)
            {
                if (!StringUtilities.isEmpty(code.getValue()))
                {
                    return;
                }
                String specialSymbols = security.hasVendorProfile() ? Constants.CODE_SPECIAL_CHARS_FOR_VENDOR
                        : Constants.CODE_SPECIAL_CHARS;
                code.setValue(
                        transliterationService.transliterateToCode(title.getValue(), MAX_CODE_LENGTH, specialSymbols));
            }
        }, BlurEvent.getType()));

    }

    abstract Collection<String> getCaseValue();

    abstract String getClassValue();

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(classList, "classList");
        DebugIdBuilder.ensureDebugId(caseList, "caseList");
    }

    private void showCases(ClassFqn fqn)
    {
        caseList.<MultiSelectCellList<?>> getValueWidget().clear();
        metainfoService.getDescendantClasses(fqn, fqn.isCase(), new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> casesList)
            {
                metainfoUtils.sort(casesList);
                for (MetaClassLite clz : metainfoUtils.getPossible(casesList, true))
                {
                    caseList.<MultiSelectCellList<?>> getValueWidget().addItem(clz.getTitle(), clz.getFqn().toString());
                }
                Collection<String> caseValue = getCaseValue();
                if (caseValue != null)
                {
                    // помечаем ранее выбранные Типы
                    caseList.trySetObjValue(caseValue);
                }
            }
        });
    }

    private void showClasses()
    {
        metainfoService.getMetaClasses(MetaClassFilters.and(isClass(), isNotSystem(), isPossible(), isNotHidden()),
                new BasicCallback<List<MetaClassLite>>()
                {
                    @Override
                    protected void handleSuccess(List<MetaClassLite> classesList)
                    {
                        classesList.sort(CommonUtils.METACLASSLITE_COMPARATOR);
                        for (MetaClassLite clz : classesList)
                        {
                            if (!Root.FQN.isSameClass(clz.getFqn()))
                            {
                                classList.<SingleSelectCellList<?>> getValueWidget().addItem(clz.getTitle(),
                                        clz.getFqn().getId());
                            }
                        }
                        String classValue = getClassValue();
                        if (classValue != null)
                        {
                            classList.trySetObjValue(classValue);
                        }
                        if (!classesList.isEmpty())
                        {
                            showCases(ClassFqn.parse(SelectListPropertyValueExtractor.getValue(classList)));
                        }
                    }
                });
    }
}
