package ru.naumen.metainfoadmin.client.customforms.commands;

import static ru.naumen.metainfoadmin.client.customforms.commands.CustomFormCommands.DELETE_PARAMETER;
import static ru.naumen.metainfoadmin.client.customforms.commands.CustomFormCommands.EDIT_PARAMETER;
import static ru.naumen.metainfoadmin.client.customforms.commands.CustomFormCommands.MOVE_PARAMETER_DOWN;
import static ru.naumen.metainfoadmin.client.customforms.commands.CustomFormCommands.MOVE_PARAMETER_UP;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;

/**
 * Инициализатор {@link CommandFactory} интерфейса настройки настраиваемых форм
 *
 * <AUTHOR>
 * @since 26 апр. 2016 г.
 */
public class CustomFormsCommandFactoryInitializer
{
    @Inject
    //@formatter:off
    public CustomFormsCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<DeleteCommand, AttributeCommandParam> deleteProvider,
            CommandProvider<EditCommand, AttributeCommandParam> editProvider,
            CommandProvider<MoveUpCommand, AttributeCommandParam> moveUpProvider,
            CommandProvider<MoveDownCommand, AttributeCommandParam> moveDownProvider)
    //@formatter:on
    {
        factory.register(DELETE_PARAMETER, deleteProvider);
        factory.register(EDIT_PARAMETER, editProvider);
        factory.register(MOVE_PARAMETER_UP, moveUpProvider);
        factory.register(MOVE_PARAMETER_DOWN, moveDownProvider);
    }
}
