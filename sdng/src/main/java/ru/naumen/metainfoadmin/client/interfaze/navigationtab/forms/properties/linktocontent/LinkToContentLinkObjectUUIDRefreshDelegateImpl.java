package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import static ru.naumen.metainfo.shared.Constants.LinkObjectType.CURRENT_USER;
import static ru.naumen.metainfo.shared.Constants.LinkObjectType.OBJECT_LINKED_TO_CURRENT_USER;

import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.shared.ui.ObjectList;

/**
 * Делегат обновления свойства "UUID объекта"
 *
 * <AUTHOR>
 * @since 12.11.2020
 */
@Singleton
public class LinkToContentLinkObjectUUIDRefreshDelegateImpl implements PropertyDelegateRefresh<String, TextBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean isLinkToContentType = LinkToContentMetaClassPropertiesProcessor.isLinkToContentElementType(context);

        String contentTypeStr = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);

        String linkObject = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT);

        boolean isVisible = isLinkToContentType
                            && !ObjectList.class.getSimpleName().equals(contentTypeStr)
                            && null != linkObject
                            && !OBJECT_LINKED_TO_CURRENT_USER.equals(linkObject)
                            && !CURRENT_USER.equals(linkObject)
                            && !Root.FQN.getId().equals(linkObject);

        property.setValue(context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT_UUID));

        /* Небольшой хак: связан с тем, что нужна асинхронная валидация в момент смены значения поля
         MenuItemLinkToContentCode.LINK_OBJECT на соответствие выбранного класса и указанного UUID объекта. Валидаторы
         будут прибинденны заново и вызваны(!!) в методе bindProperty() */
        context.getPropertyControllers().get(MenuItemLinkToContentCode.LINK_OBJECT_UUID).unbindValidators();
        callback.onSuccess(isVisible);
    }
}
