package ru.naumen.metainfoadmin.client.embeddedapplications.usagelist.commands;

import java.util.Collection;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;

/**
 * Параметр для команд, совершающих действия для мест использования встроенного приложения
 * <AUTHOR>
 * @since 25.10.2021
 */
public class UsagePointApplicationCommandParam extends CommandParam<Collection<DtObject>, Void>
{
    private final EmbeddedApplicationAdminSettingsDto embeddedApplication;

    public UsagePointApplicationCommandParam(EmbeddedApplicationAdminSettingsDto embeddedApplication,
            Collection<DtObject> values,
            AsyncCallback<Void> callback)
    {
        super(values, callback);
        this.embeddedApplication = embeddedApplication;
    }

    public EmbeddedApplicationAdminSettingsDto getEmbeddedApplication()
    {
        return embeddedApplication;
    }
}
