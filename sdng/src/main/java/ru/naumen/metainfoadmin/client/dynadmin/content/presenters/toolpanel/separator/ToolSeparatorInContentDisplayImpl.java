/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.separator;

import ru.naumen.core.client.content.toolbar.display.ToolSeparatorInContentDisplay;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.dom.client.HasAllDragAndDropHandlers;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FocusPanel;

/**
 * <AUTHOR>
 * @since 26 мая 2015 г.
 *
 */
public class ToolSeparatorInContentDisplayImpl extends Composite implements ToolSeparatorInContentDisplay
{
    interface ToolSeparatorInContentDisplayImplUiBinder extends UiBinder<FocusPanel, ToolSeparatorInContentDisplayImpl>
    {

    }

    private static ToolSeparatorInContentDisplayImplUiBinder uiBinder = GWT
            .create(ToolSeparatorInContentDisplayImplUiBinder.class);

    @UiField
    FocusPanel panel;

    public ToolSeparatorInContentDisplayImpl()
    {
        initWidget(uiBinder.createAndBindUi(this));
    }

    @Override
    public void destroy()
    {
        removeFromParent();
    }

    @Override
    public HasAllDragAndDropHandlers getDndHandler()
    {
        return panel;
    }

    @Override
    public void setDraggable()
    {
        getElement().setDraggable(Element.DRAGGABLE_TRUE);
    }

    @Override
    public void startProcessing()
    {
    }

    @Override
    public void stopProcessing()
    {
    }
}