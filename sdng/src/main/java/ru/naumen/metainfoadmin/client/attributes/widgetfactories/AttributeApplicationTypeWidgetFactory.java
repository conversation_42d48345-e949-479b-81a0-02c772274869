package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;

/**
 * Фабрика по созданию представления для редактирования "типа" встроенного приложения. 
 *
 * <AUTHOR>
 * @since June 11, 2016
 */
public class AttributeApplicationTypeWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;

    @Inject
    EmbeddedApplicationMessages messages;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();
        widget.addItem(new SimpleDtObject(EmbeddedApplicationType.ExternalApplication.toString(), messages
                .applicationHostedOnExternalServer(), ClassFqn.parse("")));
        widget.addItem(new SimpleDtObject(EmbeddedApplicationType.InternalApplication.toString(), messages
                .applicationHostedOnInternalServer(), ClassFqn.parse("")));
        widget.addItem(new SimpleDtObject(EmbeddedApplicationType.ClientSideApplication.toString(), messages
                .applicationNoServer(), ClassFqn.parse("")));

        callback.onSuccess(widget);
    }
}
