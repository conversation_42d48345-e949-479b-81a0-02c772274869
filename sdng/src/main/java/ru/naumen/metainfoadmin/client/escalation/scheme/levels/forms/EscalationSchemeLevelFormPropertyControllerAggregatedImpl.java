package ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms;

import java.util.HashMap;
import java.util.function.Function;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactoryAggregatedImpl;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinModule.EscalationSchemeLevelPropertyCode;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.name.Named;

/**
 * <AUTHOR>
 * @since 22.08.2012
 *
 */
public class EscalationSchemeLevelFormPropertyControllerAggregatedImpl<F extends ObjectForm> extends
        PropertyControllerFactoryAggregatedImpl<EscalationSchemeLevel, F>
{
    private final Function<PropertyContainerContext, String> VALUE_SELECTOR = new Function<PropertyContainerContext,
            String>()
    {
        @Override
        public String apply(PropertyContainerContext input)
        {
            return valueSelector
                    .get(input.getPropertyValues().getProperty(EscalationSchemeLevelPropertyCode.CONDITION));
        }
    };

    @Inject
    @Named(EscalationSchemeLevelFormsGinModule.ESCALATION_SCHEME_LEVEL_CONDITION_SELECTOR)
    HashMap<String, String> valueSelector;

    @Inject
    public EscalationSchemeLevelFormPropertyControllerAggregatedImpl(
            @Assisted PropertyControllerFactory<EscalationSchemeLevel, F> aggregatedPropertyFactory)
    {
        super(aggregatedPropertyFactory);
    }

    @Override
    protected void build()
    {
        register(EscalationSchemeLevelPropertyCode.VALUE, VALUE_SELECTOR,
                EscalationSchemeLevelPropertyCode.VALUE_PERCENT, EscalationSchemeLevelPropertyCode.VALUE_DTI);
    }
}
