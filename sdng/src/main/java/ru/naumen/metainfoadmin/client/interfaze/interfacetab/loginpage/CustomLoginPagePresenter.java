package ru.naumen.metainfoadmin.client.interfaze.interfacetab.loginpage;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Provider;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.buttons.Button;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.shared.loginpage.CustomLoginPageSettings;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingPresenterBase;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.command.DisableCustomLoginPageCommand;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.command.EnableCustomLoginPageCommand;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.command.TestCustomLoginPageCommand;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms.EditCustomLoginPagePresenter;

/**
 * Презентер блока настройки пользовательской формы входа в систему
 *
 * <AUTHOR>
 * @since 11.04.2018
 */
public class CustomLoginPagePresenter extends InterfaceSettingPresenterBase<InfoDisplay>
{
    @Inject
    private CustomLoginPageMessages messages;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> enabled;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> pageTemplate;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> additionalDefaultPageUrl;
    @Inject
    private Provider<EditCustomLoginPagePresenter> editLoginPagePresenter;

    private AsyncCallback<InterfaceSettingsContext> refreshCallback;

    @Inject
    public CustomLoginPagePresenter(InfoDisplay display, EventBus eventBus, CommonMessages cmessages,
            ButtonFactory buttonFactory)
    {
        super(display, eventBus, cmessages, buttonFactory);
    }

    @Override
    protected void bindContent()
    {
        enabled.setCaption(messages.enabled());
        getDisplay().add(enabled);

        pageTemplate.setCaption(messages.pageTemplate());
        getDisplay().add(pageTemplate);

        additionalDefaultPageUrl.setCaption(messages.additionalPageAddress());
        getDisplay().add(additionalDefaultPageUrl);

        ensureDebugIds();
    }

    @Override
    protected void refreshContent()
    {
        CustomLoginPageSettings customLoginPageSettings = context.getSettings().getCustomLoginPageSettings();
        enabled.setValue(customLoginPageSettings.isEnabled());
        pageTemplate.setValue(customLoginPageSettings.getPageTemplate());
        additionalDefaultPageUrl.setValue(customLoginPageSettings.getAdditionalDefaultPageAhref());
    }

    @Override
    protected String getCaption()
    {
        return messages.title();
    }

    @Override
    protected String getEditCommandId()
    {
        return null;
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected void bindToolBar()
    {
        CommandParam<InterfaceSettingsContext, InterfaceSettingsContext> param =
                new CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>(
                        context, refreshCallback);

        ButtonPresenter<?> testPageButton = buttonFactory.create(ButtonCode.PREVIEW_CUSTOM_LOGIN_PAGE,
                messages.testTemplate(), TestCustomLoginPageCommand.ID, param);
        CustomLoginPageSettings customLoginPage = context.getSettings().getCustomLoginPageSettings();
        if (customLoginPage.getSystemPageTemplate().equals(customLoginPage.getPageTemplate()))
        {
            ((Button)testPageButton.getDisplay()).setEnabled(false);
        }

        // Передаем кнопку тестирования шаблона в форму редактирования для изменения ее состояния
        toolBar.add((ButtonPresenter)buttonFactory.create(ButtonCode.EDIT, cmessages.edit(), event ->
        {
            EditCustomLoginPagePresenter presenter = editLoginPagePresenter.get();
            presenter.init(param.getValue(), (Button)testPageButton.getDisplay());
            presenter.bind();
            presenter.getDisplay().display();
        }));

        toolBar.add((ButtonPresenter)buttonFactory.create(ButtonCode.SWITCH, cmessages.switchOn(),
                EnableCustomLoginPageCommand.ID, param));
        toolBar.add((ButtonPresenter)buttonFactory.create(ButtonCode.SWITCH, cmessages.switchOff(),
                DisableCustomLoginPageCommand.ID, param));
        toolBar.add((ButtonPresenter<InterfaceSettingsContext>)testPageButton);

        toolBar.bind();
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(enabled, "enabled");
        DebugIdBuilder.ensureDebugId(pageTemplate, "page-template");
        DebugIdBuilder.ensureDebugId(additionalDefaultPageUrl, "login");
    }
}
