package ru.naumen.metainfoadmin.client.attributes.commands;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.Context;
import ru.naumen.metainfo.client.HasContextCommandParam;
import ru.naumen.metainfo.shared.elements.MetaclassSortingCriteria;

/**
 * Параметр команды редактирования критерия сортировки метакласса.
 *
 * <AUTHOR>
 * @since 08 мая 2018 г.
 *
 */
public class MetaclassSortingCriteriaCommandParam extends CommandParam<MetaclassSortingCriteria, Void>
        implements HasContextCommandParam
{
    private Context context;

    @Inject
    public MetaclassSortingCriteriaCommandParam(@Nullable @Assisted MetaclassSortingCriteria value,
            @Nullable @Assisted AsyncCallback<Void> callback,
            @Assisted Context context)
    {
        super(value, callback);
        setContext(context);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <V extends FactoryParam<MetaclassSortingCriteria, Void>> V cloneIt()
    {
        return (V)new MetaclassSortingCriteriaCommandParam(getValue(), getCallback(), getContext());
    }

    @Override
    public Context getContext()
    {
        return context;
    }

    public void setContext(Context context)
    {
        this.context = context;
    }
}
