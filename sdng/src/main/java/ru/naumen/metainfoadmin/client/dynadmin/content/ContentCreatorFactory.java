package ru.naumen.metainfoadmin.client.dynadmin.content;

import ru.naumen.core.shared.ITitled;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.UIContextPredicate;

/**
 * Фабрика создающая конкретный {@link ContentCreator}.
 *
 * <AUTHOR>
 * @since 07.08.2012
 */
public interface ContentCreatorFactory extends ITitled
{
    /**
     * @return {@link ContentCreator} для создания контента
     */
    ContentCreator<?> create();

    /**
     * @return предикат проверки доступности контента в конкретном контексте {@link UIContext}
     */
    UIContextPredicate getContextPredicate();
}