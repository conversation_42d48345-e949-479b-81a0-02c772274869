package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import jakarta.inject.Inject;

import static ru.naumen.core.client.attr.DateTimeRestrictionAttributeClientTool.getRestrictionType;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

/**
 * <AUTHOR>
 * @since 26 сент. 2018 г.
 *
 */
public class DateTimeRestrictionTypeRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{

    private final AvailableRestrictionTypesProvider<F> typesProvider;

    @Inject
    public DateTimeRestrictionTypeRefreshDelegateImpl(AvailableRestrictionTypesProvider<F> typesProvider)
    {
        this.typesProvider = typesProvider;
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        boolean editable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(EDITABLE));
        boolean determinable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(DETERMINABLE));
        boolean computable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(COMPUTABLE));
        boolean isDateTimeAttr = Constants.DATE_TIME_TYPES.contains(attrType);
        if (isDateTimeAttr)
        {
            SingleSelectCellList<String> propertyWidget = property.getValueWidget();
            propertyWidget.clear();
            typesProvider.getTypes().forEach(entry -> propertyWidget.addItem(entry.getKey(), entry.getValue()));
            initListBox(property);
            property.trySetObjValue(getRestrictionType(context).name());
        }
        context.getRefreshProcess().startCustomProcess(Lists.newArrayList(DATE_TIME_RESTRICTION_SCRIPT,
                DATE_TIME_RESTRICTION_ATTRIBUTE, DATE_TIME_RESTRICTION_CONDITION));
        callback.onSuccess(!computable && !determinable && isDateTimeAttr && editable);
    }

    private void initListBox(ListBoxProperty property)
    {
        SingleSelectCellList<String> propertyWidget = property.getValueWidget();
        propertyWidget.clear();
        typesProvider.getTypes().forEach(entry -> propertyWidget.addItem(entry.getKey(), entry.getValue()));
    }

}
