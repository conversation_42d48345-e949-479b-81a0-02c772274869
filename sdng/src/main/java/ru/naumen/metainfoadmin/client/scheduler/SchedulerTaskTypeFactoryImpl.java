package ru.naumen.metainfoadmin.client.scheduler;

import java.util.HashMap;
import java.util.Map;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.SchedulerTaskCreator;

import com.google.common.base.Preconditions;

/**
 * Реализация {@link SchedulerTaskTypeFactory}
 *
 * <AUTHOR>
 *
 */
@Singleton
public class SchedulerTaskTypeFactoryImpl implements SchedulerTaskTypeFactory
{
    private final HashMap<String, SchedulerTaskType<?>> types = new HashMap<>();

    @Inject
    public SchedulerTaskTypeFactoryImpl(final SchedulerGinjector injector, final SchedulerTaskMessages messages)
    {
        registerSchedulerTaskType(ExecuteScriptTask.NAME, new SchedulerTaskType<ExecuteScriptTask>()
        {
            @Override
            public SchedulerTaskCreator create()
            {
                return injector.executeScriptCreator();
            }

            @Override
            public ExecuteScriptTaskInfoPresenter createTaskInfoPresenter()
            {
                return injector.executeScriptTaskInfoPresenter();
            }

            @Override
            public SchedulerTaskPresenter createTaskPresenter()
            {
                return injector.schedulerTaskPresenter();
            }

            @Override
            public String getTitle()
            {
                return messages.script();
            }
        });
    }

    @Override
    public SchedulerTaskCreator getCreator(String type)
    {
        return getType(type).create();
    }

    @Override
    public Map<String, SchedulerTaskType<?>> getSchedulerTaskTypes()
    {
        return types;
    }

    @Override
    public <T extends SchedulerTask> SchedulerTaskInfoPresenterBase<T> getTaskInfoPresenter(T task)
    {
        return this.<T> getType(task.getType()).createTaskInfoPresenter();
    }

    @Override
    public SchedulerTaskPresenter getTaskPresenter(String type)
    {
        return getType(type).createTaskPresenter();
    }

    @Override
    public String getTypeTitle(String code)
    {
        return getType(code).getTitle();
    }

    @Override
    public void registerSchedulerTaskType(String code, SchedulerTaskType<?> type)
    {
        this.types.put(code, type);
    }

    @SuppressWarnings("unchecked")
    protected <T extends SchedulerTask> SchedulerTaskType<T> getType(String code)
    {
        SchedulerTaskType<T> type = (SchedulerTaskType<T>)types.get(code);
        Preconditions.checkNotNull(type, "Type '%s' isn't registered", code);
        return type;
    }
}
