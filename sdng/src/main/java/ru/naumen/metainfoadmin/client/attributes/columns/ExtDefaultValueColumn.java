package ru.naumen.metainfoadmin.client.attributes.columns;

import java.util.logging.Logger;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HasValue;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.inject.name.Named;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.attr.presentation.PresentationFactoryView;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.client.widgets.grouplist.AbstractBaseColumn;
import ru.naumen.metainfoadmin.client.widgets.script.component.ScriptComponentService;
import ru.naumen.core.shared.dispatch.FastSelectionChangesWithValue;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.AttrWidgetsHelper;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.DefaultValuePrsCustomizersRegistry;
import ru.naumen.metainfoadmin.client.script.ScriptPlace;

/**
 * В колонке отображается значение по умолчанию и вычислимое значение по умолчанию. 
 * Колонка аналогичная колонке в текущем списке с таким же названием.
 * Для ссылочных значений атрибутов отображаются ссылки на выбранные объекты, которые ведут на их карточку.
 * Для обычных атрибутов отображается их выбранное значение.
 * Для параметра вычислимое по умолчанию отображается название скрипта, которое ведёт на карточку этого скрипта. 
 * Формат: "Скрипт %Название скрипта%".
 *
 * <AUTHOR>
 * @since 24 авг. 2018 г.
 *
 */
public class ExtDefaultValueColumn extends AbstractBaseColumn<Attribute>
{
    Logger logger = java.util.logging.Logger.getLogger(DefaultValueColumn.class.getName());
    PresentationFactories presentationFactories;
    AttributesMessages messages;
    ScriptComponentService scriptComponentService;
    DefaultValuePrsCustomizersRegistry registry;
    ReadyState rs;

    @Inject
    public ExtDefaultValueColumn(PresentationFactories presentationFactories, AttributesMessages messages,
            ScriptComponentService scriptComponentService, DefaultValuePrsCustomizersRegistry registry,
            @Named(AttributeListColumnGinModule.ATTR_LIST_READY_STATE) ReadyState rs)
    {
        super(AttributeColumnCode.DEFAULT_VALUE);
        this.presentationFactories = presentationFactories;
        this.messages = messages;
        this.scriptComponentService = scriptComponentService;
        this.registry = registry;
        this.rs = rs;
    }

    @Override
    @SuppressWarnings("unchecked")
    public IsWidget createWidget(WidgetContext<Attribute> context)
    {
        PresentationContext presentationContext = (PresentationContext)context;
        presentationContext.setIsDefaultValue(true);
        Attribute attr = context.getContextObject();
        final IsWidget result;
        if (attr.getHasDefaultValue())
        {
            if (attr.isDefaultByScript())
            {
                result = new FlowPanel();
                scriptComponentService.getScript(attr.getScriptForDefault(), new BasicCallback<ScriptDto>(rs)
                {
                    @Override
                    public void handleSuccess(ScriptDto script)
                    {
                        ((FlowPanel)result).add(new InlineLabel(messages.script() + ": "));
                        Anchor anchor = new Anchor(script.getTitle(), false,
                                AttrWidgetsHelper.createLink(ScriptPlace.PLACE_PREFIX, script.getCode()));
                        ((FlowPanel)result).add(anchor);
                        anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
                    }
                });
            }
            else
            {
                try
                {
                    String prsCode = attr.getViewPresentation().getCode();

                    if (registry.get(prsCode) != null)
                    {
                        result = registry.get(prsCode).createWidget(context);
                    }
                    else
                    {
                        presentationContext.setPresentationCode(prsCode);
                        PresentationFactoryView<Object> pf = presentationFactories.getViewPresentationFactory(prsCode);

                        result = pf.createWidget(presentationContext);
                        if (null == result)
                        {
                            throw new UnsupportedOperationException();
                        }
                        //Виджеты, которые возвращает PrsFactoryBase, - это CellWidget
                        //Необходимо дополнительно инициализировать их значением по умолчанию, которое будет
                        // отображаться
                        Object defaultValue = attr.getDefaultValue();
                        if (defaultValue instanceof FastSelectionChangesWithValue)
                        {
                            defaultValue = ((FastSelectionChangesWithValue)defaultValue).getTreeValue();
                        }
                        if (DateTimeIntervalAttributeType.CODE.equals(attr.getType().getCode()) && defaultValue == null)
                        {
                            defaultValue = new DateTimeInterval(Interval.SECOND);
                        }
                        try
                        {
                            AttrWidgetsHelper.setUseLineNumbers(result.asWidget(), false);
                            ((HasValue<Object>)result.asWidget()).setValue(defaultValue);
                        }
                        catch (Exception e)
                        {
                            logger.warning(e.getMessage());
                        }
                    }
                }
                catch (Exception e)
                {
                    throw new FxException("Can't create widget for " + attr.getCode() + ", presentation="
                                          + attr.getViewPresentation().getCode(), e);
                }
            }
        }
        else
        {
            result = new HTML(); // NOPMD NSDPRD-28509 unsafe html
        }
        ensureDebugId(result, attr.getCode());
        return result;
    }
}