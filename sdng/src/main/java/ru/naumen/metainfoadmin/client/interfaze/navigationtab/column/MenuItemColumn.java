package ru.naumen.metainfoadmin.client.interfaze.navigationtab.column;

import jakarta.inject.Inject;

import com.google.gwt.user.cellview.client.Column;

import ru.naumen.core.client.widgets.columns.ClickableTextCellWithId;
import ru.naumen.core.shared.navigationsettings.AddButtonValue;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableResources;

/**
 * <AUTHOR>
 * @since 08 окт. 2013 г.
 */
public class MenuItemColumn<M extends IMenuItem> extends Column<M, String>
{
    @Inject
    private MetainfoUtils metainfoUtils;

    @Inject
    public MenuItemColumn(WithArrowsCellTableResources cellTableResources, ClickableTextCellWithId cell)
    {
        super(cell);
        setCellStyleNames(cellTableResources.cellTableStyle().widthLimit());
        cell.ensureDebugId("gwt-debug-menuitem-value");
    }

    @Override
    public String getValue(M item)
    {
        if (item instanceof MenuItem)
        {
            MenuItem mi = (MenuItem)item;
            switch (mi.getType())
            {
                case addButton:
                    return ((AddButtonValue)mi.getValue()).getValueAsString();
                case reference:
                    Object v = mi.getValue();
                    return (v instanceof Reference) ? ((Reference)v).getTitle() : null;
                case customLink:
                    return (String)mi.getValue();
                case customButton:
                    return metainfoUtils.getLocalizedValue(((UserEventTool)mi.getValue()).getTitle());
                default:
                    break;
            }
        }
        else if (item instanceof LeftMenuItemSettingsDTO)
        {
            LeftMenuItemSettingsDTO lm = (LeftMenuItemSettingsDTO)item;

            if (MenuItemType.TYPES_WITH_REFERENCE.contains(lm.getType()))
            {
                return (lm.getReference() != null) ? lm.getReference().getTitle() : null;
            }
            if (MenuItemType.customButton.equals(lm.getType()))
            {
                return metainfoUtils.getLocalizedValue(lm.getUserEventTool().getTitle());
            }
        }
        return null;
    }
}
