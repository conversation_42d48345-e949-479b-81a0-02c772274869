package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.exportNDAP;

import static ru.naumen.metainfo.shared.Constants.ATTRIBUTE_CATALOG_TO_SKIP_MONITORING;
import static ru.naumen.metainfo.shared.Constants.ATTRIBUTE_FQNS_TO_FORBID_MONITORING;
import static ru.naumen.metainfo.shared.Constants.HAS_TARGET_METACLASS_ATTRIBUTE_TYPES;
import static ru.naumen.metainfo.shared.Constants.SIMPLE_ATTRIBUTE_TYPES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.EXPORT_NDAP;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.common.collect.Collections2;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления свойства "Атрибуты, доступные из системы мониторинга"
 *
 * @param <F>
 * <AUTHOR>
 * @since 23.09.19
 */
public class RelatedAttributeToExportRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, Collection<SelectItem>, MultiSelectBoxProperty>
{
    private class GetAttributeCatalogCallback extends BasicCallback<MetaClass>
    {
        private MultiSelectBoxProperty property;

        GetAttributeCatalogCallback(MultiSelectBoxProperty property)
        {
            this.property = property;
        }

        @Override
        protected void handleSuccess(MetaClass catalog)
        {
            List<Attribute> attrs = catalog.getAttributes().stream()
                    .filter(this::isFilterableAttribute)
                    .collect(Collectors.toList());
            setContextValues(property, attrs);
        }

        private boolean isFilterableAttribute(Attribute attr)
        {
            return ATTRIBUTE_CATALOG_TO_SKIP_MONITORING.contains(attr.getFqn());
        }
    }

    private class GetAttributeClassCallback extends BasicCallback<List<MetaClass>>
    {
        private MultiSelectBoxProperty property;

        GetAttributeClassCallback(MultiSelectBoxProperty property)
        {
            this.property = property;
        }

        @Override
        protected void handleSuccess(final List<MetaClass> metaClasses)
        {
            long countMc = metaClasses.size();
            List<Attribute> attrsList = new ArrayList<>();
            if (countMc == 1)
            {
                attrsList = metaClasses.get(0).getAttributes().stream()
                        .filter(this::isFilterableAttribute)
                        .collect(Collectors.toList());
            }
            else if (countMc > 1)
            {
                // посчитаем совпадения атрибутов у разрешенных типов
                Map<AttributeFqn, Long> attrsMap = metaClasses.stream()
                        .flatMap(mc -> mc.getAttributes().stream())
                        .filter(this::isFilterableAttribute)
                        .collect(Collectors.groupingBy(Attribute::getFqn, Collectors.counting()));
                // найдем общие (пересекающиеся) атрибуты
                MetaClass metaClass = metaClasses.stream().findFirst().get();
                attrsList = attrsMap.entrySet().stream()
                        .filter(el -> el.getValue().equals(countMc))
                        .map(el -> metaClass.getAttribute(el.getKey().getCode()))
                        .collect(Collectors.toList());
            }
            setContextValues(property, attrsList);
        }

        private boolean isFilterableAttribute(Attribute attr)
        {
            return !attr.isComputable()
                   && !attr.getType().isAttributeOfRelatedObject()
                   && !attr.getCode().equals(AbstractBO.METACLASS)
                   && SIMPLE_ATTRIBUTE_TYPES.contains(attr.getType().getCode())
                   && !ATTRIBUTE_FQNS_TO_FORBID_MONITORING.contains(attr.getFqn())
                   && !(attr.getMetaClassLite().getFqn().equals(Constants.Employee.FQN) && attr.getCode()
                    .equals(AbstractBO.TITLE));
        }
    }

    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    private Collection<String> contextValues;

    private IProperties contextProperties;

    @Override
    public void refreshProperty(PropertyContainerContext context, MultiSelectBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        // запомним ранее выбранные значения, и сбросим контекс для установки новых значений
        contextProperties = context.getPropertyValues();
        contextValues = contextProperties.getProperty(AttributeFormPropertyCode.RELATED_ATTRS_TO_EXPORT);
        String attrType = contextProperties.getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        // проверка необходимости отображения поля
        if (!isSuitableAttrType(contextProperties))
        {
            callback.onSuccess(false);
            return;
        }

        // для справочников
        if (attrType.equals(CatalogItemAttributeType.CODE) || attrType.equals(CatalogItemsAttributeType.CODE))
        {
            ClassFqn catalogFqn = ClassFqn.parse(contextProperties.getProperty(
                    AttributeFormPropertyCode.TARGET_CATALOG));
            metainfoService.getMetaClass(catalogFqn, new GetAttributeCatalogCallback(property));
        }
        // для классов
        else if (attrType.equals(ObjectAttributeType.CODE) || attrType.equals(BOLinksAttributeType.CODE)
                 || attrType.equals(BackLinkAttributeType.CODE))
        {
            Collection<ClassFqn> listFqn = Collections2.transform(contextProperties.getProperty(
                    AttributeFormPropertyCode.PERMITTED_TYPES), DtObject.FQN_EXTRACTOR);

            // если не выбран тип объекта то загрузим атрибуты класса
            if (listFqn.contains(Constants.NOONE))
            {
                listFqn = new ArrayList<>();

                ClassFqn classFqn = null;
                if (attrType.equals(BackLinkAttributeType.CODE) && context.getContextValues()
                        .hasProperty(AttributeFormContextValues.DIRECT_LINK_METAINFO))
                {
                    classFqn = context.getContextValues()
                            .<MetaClass> getProperty(AttributeFormContextValues.DIRECT_LINK_METAINFO).getFqn();
                }
                else
                {
                    classFqn = ClassFqn.parseNullSafe(
                            contextProperties.getProperty(AttributeFormPropertyCode.TARGET_CLASS));
                }
                if (classFqn != null)
                {
                    listFqn.add(classFqn);
                }

            }
            if (!listFqn.isEmpty())
            {
                metainfoService.getFullMetaInfo(listFqn, new GetAttributeClassCallback(property));
            }
        }
        callback.onSuccess(true);
    }

    private String getAttrFqn(Attribute attr)
    {
        return AttributeFqn.toString(attr.getDeclaredMetaClass(), attr.getCode());
    }

    private String getAttrTitle(Attribute attr)
    {
        return attr.getTitle() + " (" + attr.getCode() + ')';
    }

    /**
     * Возвращает признак необходимости отображения поля "Атрибуты, доступные из системы мониторинга"
     * в зависимости от типа атрибута
     *
     * @param contexProperties контекст контейнера свойств
     * @return признак необходимости отображения
     */
    private boolean isSuitableAttrType(IProperties contexProperties)
    {
        String attrType = contexProperties.getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        Boolean exportNDAP = contexProperties.getProperty(EXPORT_NDAP);
        return HAS_TARGET_METACLASS_ATTRIBUTE_TYPES.contains(attrType) && exportNDAP;
    }

    /**
     * Устанавливает значения контекста исходя из загруженных атрибутов объекта
     *
     * @param property представление свойства в виде ListBox с множественным выбором
     * @param attrsList список значений для установки
     */
    private void setContextValues(MultiSelectBoxProperty property, List<Attribute> attrsList)
    {
        property.getValueWidget().clear();

        attrsList = attrsList.stream().sorted(ITitled.COMPARATOR).collect(Collectors.toList());

        Collection<String> contextAttr = new ArrayList<>();

        attrsList.stream()
                .collect(Collectors.toMap(this::getAttrFqn, this::getAttrTitle))
                .forEach((fqn, title) ->
                {
                    property.getValueWidget().addItem(title, fqn);
                    if (null != contextValues && contextValues.contains(fqn))
                    {
                        contextAttr.add(fqn);
                    }
                });

        property.getValueWidget().setObjValue(contextAttr);
        contextProperties.setProperty(AttributeFormPropertyCode.RELATED_ATTRS_TO_EXPORT, contextAttr);
    }
}
