package ru.naumen.metainfoadmin.client.attributes.columns;

import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TITLE_SEPARATOR;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.client.widgets.grouplist.AbstractBaseColumn;
import ru.naumen.metainfoadmin.client.widgets.script.component.ScriptComponentService;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.AttrWidgetsHelper;
import ru.naumen.metainfoadmin.client.script.ScriptPlace;

/**
 * Восьмая колонка "Значения атрибута / Фильтрация при редактировании"
 * В колонке отображается параметр "Фильтрация значений при редактировании".
 * Название скрипта — ссылка на его карточку в каталоге.
 * Формат: "Скрипт %Название скрипта%".
 *
 * <AUTHOR>
 * @since 27 июл. 2018 г.
 *
 */
public class AttrFilteringOnEditColumn extends AbstractBaseColumn<Attribute>
{
    private ScriptComponentService scriptComponentService;
    private AttributesMessages messages;

    @Inject
    public AttrFilteringOnEditColumn(ScriptComponentService scriptComponentService,
            AttributesMessages messages)
    {
        super(AttributeColumnCode.FILTERING_ON_EDIT);
        this.scriptComponentService = scriptComponentService;
        this.messages = messages;
    }

    @Override
    public IsWidget createWidget(WidgetContext<Attribute> context)
    {
        final FlowPanel result = new FlowPanel();
        Attribute attr = context.getContextObject();
        if (Boolean.TRUE.equals(attr.isFilteredByScript()))
        {
            scriptComponentService.getScript(attr.getScriptForFiltration(), new BasicCallback<ScriptDto>()
            {
                @Override
                public void handleSuccess(ScriptDto script)
                {
                    result.add(new InlineLabel(messages.script() + TITLE_SEPARATOR));
                    Anchor anchor = new Anchor(script.getTitle(), false,
                            AttrWidgetsHelper.createLink(ScriptPlace.PLACE_PREFIX, script.getCode()));
                    result.add(anchor);
                    anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
                }
            });
        }
        return result;
    }
}