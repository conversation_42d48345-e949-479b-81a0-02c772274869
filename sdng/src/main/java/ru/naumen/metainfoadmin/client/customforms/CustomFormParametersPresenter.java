package ru.naumen.metainfoadmin.client.customforms;

import static ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode.*;
import static ru.naumen.metainfoadmin.client.customforms.commands.CustomFormCommands.DELETE_PARAMETER;
import static ru.naumen.metainfoadmin.client.customforms.commands.CustomFormCommands.EDIT_PARAMETER;
import static ru.naumen.metainfoadmin.client.customforms.commands.CustomFormCommands.MOVE_PARAMETER_DOWN;
import static ru.naumen.metainfoadmin.client.customforms.commands.CustomFormCommands.MOVE_PARAMETER_UP;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NodeList;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.name.Named;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.group.ListEditorDnDGroupControllerBase;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfoadmin.client.attributes.AttributeList;
import ru.naumen.metainfoadmin.client.attributes.AttributesDisplay;
import ru.naumen.metainfoadmin.client.attributes.StandardAttributeListCustomizer;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributesListType;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnInfoFactory;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributePresenterFactory;
import ru.naumen.metainfoadmin.client.customforms.parameters.ParameterFormAdd;
import ru.naumen.metainfoadmin.shared.customforms.GetCustomFormAction;
import ru.naumen.metainfoadmin.shared.customforms.ModificationContext;
import ru.naumen.metainfoadmin.shared.customforms.MoveParameterAction;

/**
 * {@link Presenter} списка параметров настраиваемой формы
 *
 * <AUTHOR>
 * @since 19 апр. 2016 г.
 */
public class CustomFormParametersPresenter extends BasicPresenter<AttributesDisplay<StandardAttributeListCustomizer>>
        implements FormSettingsChangedEventHandler
{
    private class FormParametersListDnDController extends ListEditorDnDGroupControllerBase
    {
        public FormParametersListDnDController()
        {
            super(getDisplay().getTableContainer().getElement());
        }

        @Override
        public void move(final int oldPosition, final int newPosition, ReadyState readyState)
        {
            @SuppressWarnings("unchecked")
            final List<Attribute> attributes = (List<Attribute>)context.getForm().getAttributes();
            Attribute formParameter = attributes.get(oldPosition);

            dispatch.execute(new MoveParameterAction(formParameter.getFqn(), newPosition - oldPosition),
                    new BasicCallback<EmptyResult>(readyState)
                    {
                        @Override
                        protected void handleSuccess(EmptyResult value)
                        {
                            attributes.add(newPosition, attributes.remove(oldPosition));
                            context.getEventBus().fireEvent(new FormSettingsChangedEvent(context.getCustomForm()));
                        }
                    });
        }

        @Override
        protected List<Element> retrieveChildren()
        {
            List<Element> elements = new ArrayList<>();
            NodeList<Element> trs = getRootElement().getElementsByTagName("tr");
            for (int i = 0; i < trs.getLength(); ++i)
            {
                elements.add(trs.getItem(i));
            }
            return elements;
        }

        @Override
        public boolean canDragStart(@Nullable Element element)
        {
            int index = indexOf(element);
            List<? extends Attribute> attributes = context.getCustomForm().get().getAttributes();
            if (index < 0 || index >= attributes.size())
            {
                return false;
            }
            Attribute formParameter = attributes.get(index);
            return AdminPermissionHolderUtils.hasPermission(context, PermissionType.EDIT, formParameter);
        }
    }

    //Коды столбцов в том порядке, в каком они будут добавляться в дисплей
    // @formatter:off
    private static final String[] COLUMNS = new String[] {
        TITLE,
        CODE,
        TYPE,
        EDITABLE,
        REQUIRED,
        DEFAULT_VALUE };
    // @formatter:on

    @Inject
    private DispatchAsync dispatch;
    @Inject
    private EventActionMessages messages;
    @Inject
    private AttributePresenterFactory<ParameterFormAdd> addParamFormPresenterFactory;
    @Inject
    @Named(AttributesListType.STANDARD)
    private AttributeListColumnInfoFactory columnInfoFactory;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private SecurityHelper security;

    private final ToolBarDisplayMediator<EventActionWithScript> toolBar;
    private final CustomFormContext context;
    private final ListEditorDnDController dndController;
    private EventActionWithScript eventAction;

    @Inject
    public CustomFormParametersPresenter(@Assisted ModificationContext modificationContext,
            AttributesDisplay<StandardAttributeListCustomizer> display,
            EventBus eventBus, ListEditorDnDControllerFactory dndControllerFactory)
    {
        super(display, eventBus);
        context = new CustomFormContext(modificationContext);
        toolBar = new ToolBarDisplayMediator<>(display.getToolBar());
        dndController = dndControllerFactory.create(new FormParametersListDnDController());
    }

    @Override
    public void onSettingsChanged(FormSettingsChangedEvent event)
    {
        context.setCustomForm(event.getCustomForm());
        context.setPermissions(event.getCustomForm().getProperty(Constants.SettingsSet.ADMIN_PERMISSIONS));
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();

        getDisplay().getTable().clear();

        CustomForm form = context.getForm();
        if (form != null)
        {
            for (Attribute attribute : form.getAttributes())
            {
                getDisplay().getTable().addElement(attribute);
            }
        }
        getDisplay().getTable().refresh();
        if (security.isAdmin())
        {
            Scheduler.get().scheduleDeferred(dndController::update);
        }
        toolBar.refresh(eventAction);
    }

    public void show(boolean show)
    {
        getDisplay().asWidget().setVisible(show);
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaption(messages.parameters());
        getDisplay().getTable().enableSorting(false);

        registerHandler(context.getEventBus().addHandler(FormSettingsChangedEvent.getType(), this));

        bindToolbar();
        bindColumns();

        if (context.getFormCode() == null)
        {
            refreshDisplay();
            return;
        }

        dispatch.execute(new GetCustomFormAction(context.getFormCode()),
                new BasicCallback<SimpleResult<DtoContainer<CustomForm>>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtoContainer<CustomForm>> result)
                    {
                        DtoContainer<CustomForm> container = result.get();
                        context.setPermissions(container.getProperty(Constants.SettingsSet.ADMIN_PERMISSIONS));
                        context.setCustomForm(container);
                        refreshDisplay();
                    }
                });

    }

    private void bindColumns()
    {
        AttributeList table = getDisplay().getTable();

        AttributeCommandParam param = new AttributeCommandParam(null, null, context);
        if (security.isAdmin())
        {
            table.addColumn(columnInfoFactory.createCommandColumn(MOVE_UP_BUTTON, param, MOVE_PARAMETER_UP));
            table.addColumn(columnInfoFactory.createCommandColumn(MOVE_DOWN_BUTTON, param, MOVE_PARAMETER_DOWN));
        }

        for (String code : COLUMNS)
        {
            table.addColumn(columnInfoFactory.create(code, registrationContainer));
        }

        if (security.isAdmin())
        {
            table.addColumn(columnInfoFactory.createCommandColumn(EDIT_BUTTON, param, EDIT_PARAMETER));
            table.addColumn(columnInfoFactory.createCommandColumn(DELETE_BUTTON, param, DELETE_PARAMETER));
        }
    }

    public void init(EventActionWithScript eventAction)
    {
        this.eventAction = eventAction;
    }

    @SuppressWarnings("unchecked")
    private void bindToolbar()
    {
        if (security.isAdmin())
        {
            ButtonPresenter<EventActionWithScript> button =
                    (ButtonPresenter<EventActionWithScript>)buttonFactory.create(
                            ButtonCode.ADD,
                            messages.addParameter(), event -> addParamFormPresenterFactory.create(context).bind());
            button.addPossibleFilter(eventActionWithScript ->
                    AdminPermissionUtils.hasEditPermission(eventActionWithScript.getDtObject()));
            toolBar.add(button);
            toolBar.bind();
        }
    }
}
