/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.toolbar.command;

import java.util.Collection;

import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.toolbar.commands.VMapRowSetConverter;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.EscalationValueMapItemContext;

import com.google.common.collect.Collections2;
import com.google.common.collect.Sets;

/**
 * <AUTHOR>
 * @since 02.11.2012
 *
 */
public class EscalationVMapRowSetConverter extends VMapRowSetConverter<EscalationValueMapItemContext>
{
    @Override
    protected Object prepareValue(String code, Object value)
    {
        if (ValueMapCatalogItem.ESCALATION_TARGET_DATA.equals(code))
        {
            Collection<EscalationScheme> schemes = (Collection<EscalationScheme>)value;
            return Sets.newHashSet(Collections2.transform(schemes, HasCode.CODE_EXTRACTOR));
        }
        else
        {
            return super.prepareValue(code, value);
        }
    }
}