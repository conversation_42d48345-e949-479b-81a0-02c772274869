/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.view;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;

import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 31 янв. 2014 г.
 *
 */
@Singleton
public class ExampleWidgetFactoryMetaClass extends ExampleWidgetFactoryView<ClassFqn>
{
    @Override
    public IsWidget createExampleWidget(PresentationContext context)
    {
        Attribute attribute = context.getAttribute();
        CommonUtils.assertAttrNotNull(attribute);
        ClassFqn obj = attribute.getDefaultValue();
        return new HTML(null == obj ? attribute.getExample() : obj.toString()); // NOPMD NSDPRD-28509 unsafe html
    }
}