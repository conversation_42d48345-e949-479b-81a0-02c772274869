package ru.naumen.metainfoadmin.client.structuredobjectsviews.card;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.STRUCTURES;

import java.util.Collection;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.activity.PrevLinkContainer;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.shared.structuredobjectsviews.dispatch.GetStructuredObjectsViewAction;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsMessages;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsPlace;

/**
 * Представление карточки структуры в интерфейсе администратора.
 * <AUTHOR>
 * @since 21.10.2019
 */
public class StructuredObjectsViewPresenter extends AdminTabPresenter<StructuredObjectsViewPlace>
{
    private final DispatchAsync service;
    private final StructuredObjectsViewInfoPresenter infoPresenter;
    private final StructuredObjectsViewItemsPresenter itemsPresenter;
    private final StructuredObjectsViewUsagePresenter usagePresenter;
    private final CommonMessages commonMessages;
    private final StructuredObjectsViewsMessages structuredObjectsViewsMessages;
    private final PrevLinkContainer prevLinkContainer;

    private String code = null;

    private final OnStartCallback<DtObject> refreshCallback = new SafeOnStartBasicCallback<DtObject>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            infoPresenter.setStructuredObjectsView(value);
        }
    };

    @Inject
    public StructuredObjectsViewPresenter(AdminTabDisplay display,
            EventBus eventBus,
            DispatchAsync service,
            StructuredObjectsViewInfoPresenter infoPresenter,
            StructuredObjectsViewItemsPresenter itemsPresenter,
            StructuredObjectsViewUsagePresenter usagePresenter,
            CommonMessages commonMessages,
            StructuredObjectsViewsMessages structuredObjectsViewsMessages,
            PrevLinkContainer prevLinkContainer)
    {
        super(display, eventBus);
        this.service = service;
        this.infoPresenter = infoPresenter;
        this.itemsPresenter = itemsPresenter;
        this.usagePresenter = usagePresenter;
        this.commonMessages = commonMessages;
        this.structuredObjectsViewsMessages = structuredObjectsViewsMessages;
        this.prevLinkContainer = prevLinkContainer;
    }

    @Override
    public void init(StructuredObjectsViewPlace place)
    {
        code = place.getCode();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        infoPresenter.refreshDisplay();
        itemsPresenter.refreshDisplay();
        usagePresenter.refreshDisplay();
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();
        infoPresenter.init(refreshCallback);
        service.execute(new GetStructuredObjectsViewAction(code),
                new BasicCallback<SimpleResult<Collection<DtObject>>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<Collection<DtObject>> value)
                    {
                        value.get()
                                .stream()
                                .findFirst()
                                .ifPresent(structure -> afterStructureLoaded(structure)); // NOPMD
                    }
                });
    }

    @Override
    protected void onUnbind()
    {
        infoPresenter.unbind();
        itemsPresenter.unbind();
        usagePresenter.unbind();
        super.onUnbind();
    }

    private void afterStructureLoaded(DtObject structure)
    {
        Place previousPlace = prevLinkContainer.getPreviousPlace();
        String backLinkCaption = null == previousPlace || previousPlace instanceof StructuredObjectsViewsPlace
                ? structuredObjectsViewsMessages.backToStructuredObjectsViews()
                : commonMessages.back();
        prevPageLinkPresenter.bind(backLinkCaption, StructuredObjectsViewsPlace.INSTANCE);

        getDisplay().setTitle(structure.getTitle());
        infoPresenter.setStructuredObjectsView(structure);
        addContent(infoPresenter, "info");

        itemsPresenter.init(structure);
        addContent(itemsPresenter, "items");

        usagePresenter.setStructuredObjectsView(structure);
        addContent(usagePresenter, "usage");
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return STRUCTURES;
    }
}
