package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import static ru.naumen.metainfo.shared.Constants.LinkObjectType.OBJECT_LINKED_TO_CURRENT_USER;

import java.util.function.Predicate;

import java.util.stream.StreamSupport;

import jakarta.inject.Inject;

import com.google.common.base.Preconditions;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.filters.AttributeFilters;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist.attrseltree.RelationAttrsTreeFactoryContext;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Контроллер свойства "Атрибут объекта связи"
 *
 * <AUTHOR>
 * @since 29.10.2020
 */
public class LinkToContentLinkObjectAttributeControllerImpl extends LinkToContentAttrTreePropertyControllerBase
{
    private static final Predicate<RelationsAttrTreeObject> BO_ATTR_SELECT_FILTER = ato ->
    {
        Preconditions.checkNotNull(ato);
        return StreamSupport.stream(ato.toAncestors().spliterator(), false)
                .map(RelationsAttrTreeObject::getAttribute)
                .allMatch(AttributeFilters.inTypeCodes(ObjectAttributeType.CODE));
    };

    @Inject
    public LinkToContentLinkObjectAttributeControllerImpl(
            //@formatter:off
            @Assisted String code,
            @Assisted PropertyContainerContext context,
            @Assisted PropertyParametersDescriptor propertyParams,
            @Assisted PropertyDelegateDescriptor<RelationsAttrTreeObject,
                    PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<?, RelationsAttrTreeObject, ?>>> propertyDelegates)
            //@formatter:on
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    protected void doBind(AsyncCallback<Void> callback)
    {
        callback.onSuccess(null);
    }

    @Override
    public void refresh()
    {
        boolean isLinkToContentType = LinkToContentMetaClassPropertiesProcessor.isLinkToContentElementType(context);

        String contentTypeStr = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);

        String linkObject = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT);

        if (!isLinkToContentType || ObjectList.class.getSimpleName().equals(contentTypeStr)
            || !OBJECT_LINKED_TO_CURRENT_USER.equals(linkObject))
        {
            new DefaultRefreshCallback().onSuccess(false);
            context.getPropertyControllers().get(MenuItemLinkToContentCode.LINK_OBJECT_ATTR).unbindValidators();
            return;
        }

        ClassFqn metaclass = Employee.FQN;
        String linkObjectCase = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT_CASE);

        if (linkObjectCase != null)
        {
            metaclass = ClassFqn.parse(linkObjectCase);
        }

        RelationAttrsTreeFactoryContext treeContext = new RelationAttrsTreeFactoryContext(metaclass,
                BO_ATTR_SELECT_FILTER, null);

        setTreeProperty(treeContext);
    }
}
