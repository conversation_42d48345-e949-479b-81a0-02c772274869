package ru.naumen.metainfoadmin.client.dynadmin;

import java.util.List;

import com.google.gwt.event.shared.GwtEvent;

import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Событие посылаемое при изменении заголовка карточки объъекта
 *
 * <AUTHOR>
 * @since 21.11.2014
 */
public class ChangeObjectCardCaptionEvent extends GwtEvent<ChangeObjectCardCaptionEventEventHandler>
{

    public static Type<ChangeObjectCardCaptionEventEventHandler> getType()
    {
        return TYPE;
    }

    private static final Type<ChangeObjectCardCaptionEventEventHandler> TYPE =
            new Type<ChangeObjectCardCaptionEventEventHandler>();

    private final String captionAttributeCode;
    private final List<? extends LocalizedString> captionString;

    public ChangeObjectCardCaptionEvent(String captionAttributeCode,
            List<? extends LocalizedString> captionString)
    {
        this.captionAttributeCode = captionAttributeCode;
        this.captionString = captionString;
    }

    @Override
    public Type<ChangeObjectCardCaptionEventEventHandler> getAssociatedType()
    {
        return TYPE;
    }

    public String getCaptionAttributeCode()
    {
        return captionAttributeCode;
    }

    public List<? extends LocalizedString> getCaptionString()
    {
        return captionString;
    }

    @Override
    protected void dispatch(ChangeObjectCardCaptionEventEventHandler handler)
    {
        handler.onChangeObjectCardCaption(this);
    }
}
