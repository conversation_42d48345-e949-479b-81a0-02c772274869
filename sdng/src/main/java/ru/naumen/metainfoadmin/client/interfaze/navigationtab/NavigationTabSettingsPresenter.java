package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.client.TabLayoutDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfoadmin.client.AdminTabContainerPresenter;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb.BreadCrumbsPresenter;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.HomePageListPresenter;

/**
 * Презентер вкладки с настройками Навигации
 * <AUTHOR>
 * @since 18 сент. 2013 г.
 */
public class NavigationTabSettingsPresenter extends AdminTabContainerPresenter
{
    private final AdminMetainfoServiceAsync service;
    private final NavigationSettingsInfoPresenter infoPresenter;
    private final NavigationTopMenuItemsPresenter topItemsPresenter;
    private final NavigationLeftMenuItemsPresenter leftItemsPresenter;
    private final QuickAccessPanelTilesPresenter quickTilesPresenter;
    private final BreadCrumbsPresenter breadCrumbPresenter;
    private final HomePageListPresenter homePagePresenter;

    @Inject
    public NavigationTabSettingsPresenter(TabLayoutDisplay display, EventBus eventBus,
            AdminMetainfoServiceAsync service,
            NavigationSettingsInfoPresenter infoPresenter,
            NavigationTopMenuItemsPresenter topItemsPresenter,
            NavigationLeftMenuItemsPresenter leftItemsPresenter,
            QuickAccessPanelTilesPresenter quickTilesPresenter,
            BreadCrumbsPresenter breadCrumbPresenter,
            HomePageListPresenter homePagePresenter)
    {
        super(display, eventBus);
        this.service = service;
        this.infoPresenter = infoPresenter;
        this.topItemsPresenter = topItemsPresenter;
        this.leftItemsPresenter = leftItemsPresenter;
        this.quickTilesPresenter = quickTilesPresenter;
        this.breadCrumbPresenter = breadCrumbPresenter;
        this.homePagePresenter = homePagePresenter;
    }

    @Override
    protected void onBind()
    {
        service.getNavigationSettings(new BasicCallback<DtoContainer<NavigationSettings>>()
        {
            @Override
            protected void handleSuccess(DtoContainer<NavigationSettings> container)
            {
                infoPresenter.init(container);
                addContent(infoPresenter, "info");

                topItemsPresenter.init(container);
                addContent(topItemsPresenter, "topMenuItems");

                leftItemsPresenter.init(container);
                addContent(leftItemsPresenter, "leftMenuItems");

                quickTilesPresenter.init(container);
                addContent(quickTilesPresenter, "quickPanelTiles");

                breadCrumbPresenter.init(container);
                addContent(breadCrumbPresenter, "breadCrumb");

                homePagePresenter.init(container);
                addContent(homePagePresenter, "homePage");

                refreshDisplay();
            }
        });
    }
}
