package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation;

import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

/**
 * Фабрика {@link AttributeFormPropertyDelegateRefresh} для групп атрибутов на сложной форме
 * добавления связи для агрегирующих атрибутов
 *
 * <AUTHOR>
 * @since 03 февр. 2016 г.
 */
public interface ComplexRelationAggrAttrGroupRefreshDelegateFactory<T extends ObjectForm>
{
    /**
     * Создать делегат
     *
     * @param classFqn - класс агрегируемого объекта
     * @param propertyCode - код свойства на форме
     */
    AttributeFormPropertyDelegateRefresh<T, SelectItem, ListBoxWithEmptyOptProperty> create(ClassFqn classFqn,
            String propertyCode);
}
