package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage;

import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import java.util.ArrayList;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.client.widgets.properties.container.AttributePropertyDescription;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsPlace;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands.HomePageItemsListCommandCode;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;

/**
 * Презентер списка атрибутов на карточке домашней страницы в ИА
 */
public class NavigationHomePageAttributesPresenter extends BasicPresenter<InfoDisplay>
{
    private DtoContainer<NavigationSettings> settings;
    private HomePageDtObject homePageDtObject;

    private final ArrayList<AttributePropertyDescription<?, HomePageDtObject>> properties;
    private final NavigationSettingsMessages messages;
    private final ButtonFactory buttonFactory;
    private final CommonMessages cmessages;
    private final PlaceController placeController;
    private final TagsMessages tagsMessages;

    private ToolBarDisplayMediator<HomePageDtObject> toolBar;
    private HomePageCommandParam refreshParam;

    private final AsyncCallback<DtoContainer<NavigationSettings>> refreshCallback =
            new SafeBasicCallback<DtoContainer<NavigationSettings>>()
            {
                @Override
                protected void handleSuccess(DtoContainer<NavigationSettings> settings)
                {
                    NavigationHomePageAttributesPresenter.this.settings = settings;
                    homePageDtObject = settings.get().findHomePage(homePageDtObject.getUUID());
                    refreshParam.update(homePageDtObject, settings);
                    refreshDisplay();
                }
            };

    private final AsyncCallback<DtoContainer<NavigationSettings>> deleteCallback =
            new BasicCallback<DtoContainer<NavigationSettings>>()
            {
                @Override
                protected void handleSuccess(DtoContainer<NavigationSettings> settings)
                {
                    unbind();
                    placeController.goTo(new InterfaceSettingsPlace("navigation"));
                }
            };

    @Inject
    public NavigationHomePageAttributesPresenter(
            InfoDisplay display,
            EventBus eventBus,
            @Named(NavigationHomePageGinModule.NAVIGATION_HOME_PAGE_ATTRIBUTES)
            ArrayList<AttributePropertyDescription<?, HomePageDtObject>> properties,
            NavigationSettingsMessages messages,
            ButtonFactory buttonFactory, CommonMessages cmessages,
            PlaceController placeController, TagsMessages tagsMessages)
    {
        super(display, eventBus);
        this.properties = properties;
        this.messages = messages;
        this.buttonFactory = buttonFactory;
        this.cmessages = cmessages;
        this.placeController = placeController;
        this.tagsMessages = tagsMessages;
    }

    public void init(HomePageDtObject homePageDtObject, DtoContainer<NavigationSettings> settings)
    {
        this.homePageDtObject = homePageDtObject;
        this.settings = settings;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        properties.stream()
                .filter(propertyDescription -> propertyDescription.isVisible(homePageDtObject))
                .forEach(propertyDescription -> propertyDescription.setValue(homePageDtObject));
        String attentionMessage = StringUtilities.EMPTY;
        if (!homePageDtObject.isEnabledByTags())
        {
            attentionMessage = SafeHtmlUtils.htmlEscape(tagsMessages.disabledHomePageWarning());
        }
        getDisplay().getAttention().setVisible(!homePageDtObject.isEnabledByTags());
        getDisplay().getAttention().setHTML(attentionMessage); // NOPMD NSDPRD-28509 unsafe html
        toolBar.refresh(homePageDtObject);
    }

    @Override
    protected void onBind()
    {
        toolBar = new ToolBarDisplayMediator(getDisplay().getToolBar());
        getDisplay().setTitle(messages.homePageCardName(homePageDtObject.getTitle()));
        refreshParam = new HomePageCommandParam(settings, homePageDtObject, refreshCallback);
        ButtonPresenter<HomePageDtObject> editBtn = (ButtonPresenter<HomePageDtObject>)buttonFactory.create(
                ButtonCode.EDIT, cmessages.edit(),
                HomePageItemsListCommandCode.EDIT,
                refreshParam);
        editBtn.addPossibleFilter(homePageDto -> hasPermission(settings, PermissionType.EDIT, homePageDto));
        toolBar.add(editBtn);

        ButtonPresenter<HomePageDtObject> delBtn = (ButtonPresenter<HomePageDtObject>)buttonFactory.create(
                ButtonCode.DELETE, cmessages.delete(),
                HomePageItemsListCommandCode.DELETE,
                new HomePageCommandParam(settings, homePageDtObject, deleteCallback));
        delBtn.addPossibleFilter(homePageDto -> hasPermission(settings, PermissionType.DELETE, homePageDto));
        toolBar.add(delBtn);
        toolBar.bind();
        properties.stream()
                .filter(propertyDescription -> propertyDescription.isVisible(homePageDtObject))
                .forEach(propertyDescription ->
                        getDisplay().add(propertyDescription.getProperty(), propertyDescription.getDebugId()));
        refreshDisplay();
    }
}
