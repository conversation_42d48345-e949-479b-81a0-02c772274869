package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determinablebytemplate;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.List;

import com.google.common.collect.Lists;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;

/**
 *
 * <AUTHOR>
 *
 */
public class CompositeVCHDelegateAddImpl<F extends ObjectForm> extends CompositeVCHDelegateImpl<F>
{
    @Override
    protected List<String> getPropertiesToRefresh()
    {
        return Lists.newArrayList(DETERMINABLE, DETERMINER, COMPUTABLE, REQUIRED, REQUIRED_IN_INTERFACE, EDITABLE,
                EDITABLE_IN_LISTS, UNIQUE, TARGET_CLASS, TARGET_CATALOG, DIRECT_LINK_TARGET, PERMITTED_TYPES, SCRIPT,
                DATE_TIME_COMMON_RESTRICTIONS, DATE_TIME_RESTRICTION_SCRIPT, EDIT_PRS, SUGGEST_CATALOG, SELECT_SORTING,
                FILTERED_BY_SCRIPT, COMPUTABLE_ON_FORM, COMPUTABLE_ON_FORM_SCRIPT, SCRIPT_FOR_FILTRATION,
                DEFAULT_VALUE_LABEL, DEFAULT_BY_SCRIPT, SCRIPT_FOR_DEFAULT, TEMPLATE, USE_GEN_RULE, DEFAULT_VALUE);
    }
}
