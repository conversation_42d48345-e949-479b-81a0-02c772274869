package ru.naumen.metainfoadmin.client.jmsqueue.card;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Карточка очереди
 * <AUTHOR>
 * @since 16.02.2021
 **/
public class JMSQueuePlace extends Place
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<JMSQueuePlace>
    {
        @Override
        public JMSQueuePlace getPlace(String token)
        {
            return new JMSQueuePlace(token);
        }

        @Override
        public String getToken(JMSQueuePlace place)
        {
            return place == null ? "" : place.getCode();
        }
    }

    public static final String PLACE_PREFIX = "jmsQueue";

    private transient DtObject jmsQueue = null;
    private String code = null;

    public JMSQueuePlace()
    {
    }

    public JMSQueuePlace(DtObject jmsQueue)
    {
        this.code = jmsQueue.getUUID();
        this.jmsQueue = jmsQueue;
    }

    public JMSQueuePlace(String code)
    {
        this.code = code;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null || this.getClass() != obj.getClass())
        {
            return false;
        }
        return ObjectUtils.equals(this.code, ((JMSQueuePlace)obj).code);
    }

    public String getCode()
    {
        return code;
    }

    public DtObject getJMSQueue()
    {
        return jmsQueue;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(code);
    }

    @Override
    public String toString()
    {
        return "JMSQueuePlace [" + code + "]";
    }
}