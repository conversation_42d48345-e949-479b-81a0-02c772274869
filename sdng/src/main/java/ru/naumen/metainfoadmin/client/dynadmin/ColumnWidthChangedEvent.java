package ru.naumen.metainfoadmin.client.dynadmin;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие посылаемое по факту изменения ширины колонки в режиме разметки
 *
 * <AUTHOR>
 * @since 25.01.17
 *
 */
public class ColumnWidthChangedEvent extends GwtEvent<ColumnWidthChangedEventHandler>
{
    private static final Type<ColumnWidthChangedEventHandler> TYPE = new Type<ColumnWidthChangedEventHandler>();

    public static Type<ColumnWidthChangedEventHandler> getType()
    {
        return TYPE;
    }

    public ColumnWidthChangedEvent()
    {

    }

    @Override
    public Type<ColumnWidthChangedEventHandler> getAssociatedType()
    {
        return TYPE;
    }

    @Override
    protected void dispatch(ColumnWidthChangedEventHandler handler)
    {
        handler.onChangeWidth(this);
    }
}
