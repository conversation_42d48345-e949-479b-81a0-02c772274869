package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import static ru.naumen.core.client.attr.DateTimeRestrictionAttributeClientTool.getRestrictionType;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DATE_TIME_RESTRICTION_CONDITION;

import java.util.Objects;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.DateTimeRestrictionCondition;
import ru.naumen.metainfo.shared.elements.HasDateTimeRestriction.RestrictionType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;

/**
 * Делегат биндинга свойства "Условие" для атрибутов с ограничением типа "Задать зависимость от атрибута"
 * <AUTHOR>
 * @since 11 дек. 2018 г.
 */
public class DateTimeRestrictionConditionBindDelegateImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<SelectItem, ListBoxProperty> implements
        AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxProperty>
{

    private final AvailableRestrictionConditionsProvider availableRestrictionConditionsProvider;

    @Inject
    public DateTimeRestrictionConditionBindDelegateImpl(
            AvailableRestrictionConditionsProvider availableRestrictionConditionsProvider)
    {
        this.availableRestrictionConditionsProvider = availableRestrictionConditionsProvider;
    }

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxProperty property, AsyncCallback<Void> callback)
    {
        super.bindProperty(context, property, callback);
        initListBox(property);
        String attrType = context.getPropertyValues().getProperty(ATTR_TYPE);
        boolean isDateTimeAttr = Constants.DATE_TIME_TYPES.contains(attrType);
        RestrictionType restrictionType = isDateTimeAttr ? getRestrictionType(context) : null;
        boolean isRestrictionAttribute = RestrictionType.ATTRIBUTE_RESTRICTION == restrictionType;
        if (isDateTimeAttr && isRestrictionAttribute)
        {
            String condition = context.getPropertyValues().getProperty(DATE_TIME_RESTRICTION_CONDITION);
            property.trySetObjValue(
                    Objects.equals(DateTimeRestrictionCondition.NO.name(), condition) ? null : condition);
        }
    }

    private void initListBox(ListBoxProperty property)
    {
        SingleSelectCellList<String> valueWidget = property.getValueWidget();
        valueWidget.setHasEmptyOption(true);
        valueWidget.clear();
        availableRestrictionConditionsProvider.getTypes()
                .forEach(entry -> valueWidget.addItem(entry.getKey(), entry.getValue()));
    }

}
