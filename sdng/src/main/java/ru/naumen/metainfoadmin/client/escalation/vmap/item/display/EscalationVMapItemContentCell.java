/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.display;

import java.util.Collection;

import jakarta.inject.Inject;

import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.display.VMapItemContentCell;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.EscalationValueMapItemContext;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

/**
 * <AUTHOR>
 * @since 01.11.2012
 *
 */
public class EscalationVMapItemContentCell extends VMapItemContentCell<EscalationValueMapItemContext>
{
    @Inject
    I18nUtil i18nUtil;

    @Override
    @SuppressWarnings("unchecked")
    protected SafeHtml generateHtml(Object value, Attribute attr)
    {
        if (ValueMapCatalogItem.TARGET_ATTRS.equals(attr.getCode()))
        {
            Collection<EscalationScheme> schemes = (Collection<EscalationScheme>)value;
            return SafeHtmlUtils.fromString(formatters.formatLocalizedWithEllipsis(schemes, 3,
                    i18nUtil.getTitleExtractor()));
        }
        else
        {
            return super.generateHtml(value, attr);
        }
    }
}