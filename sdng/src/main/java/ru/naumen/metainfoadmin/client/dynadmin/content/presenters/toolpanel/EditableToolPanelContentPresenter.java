/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import ru.naumen.core.client.content.AbstractContentPresenter;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.metainfo.shared.ui.HasToolPanel;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.common.content.commands.TabContentCommandCode;
import ru.naumen.metainfoadmin.client.common.content.commands.TabContentCommandPermissionChecker;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeUITemplateModeEvent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.EditToolPanelFormPresenterFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.view.ToolPanelContentPresenterView;

/**
 * Презентер для тулпанели в режиме настройки.
 * Работает только в режиме просмотра. За редактирование отвечает {link EditToolPanelFormPresenter}
 *
 * <AUTHOR>
 * @since 13 мая 2015 г.
 */
public class EditableToolPanelContentPresenter<R extends Context>
        extends AbstractContentPresenter<EditableToolPanelDisplay, ToolPanel, R>
{
    private final EditToolPanelFormPresenterFactory editFormPresenterFactory;
    private final ToolPanelContentPresenterView<R> viewPresenter;
    private final TabContentCommandPermissionChecker permissionChecker;

    @Inject
    public EditableToolPanelContentPresenter(EditableToolPanelDisplay display, EventBus eventBus,
            ToolPanelContentPresenterView<R> viewPresenter,
            EditToolPanelFormPresenterFactory editFormPresenterFactory,
            TabContentCommandPermissionChecker permissionChecker)
    {
        super(display, eventBus, "editableToolPanel");
        this.viewPresenter = viewPresenter;
        this.editFormPresenterFactory = editFormPresenterFactory;
        this.permissionChecker = permissionChecker;
    }

    @Override
    public void init(ToolPanel content, R context)
    {
        super.init(content, context);
        viewPresenter.init(content, context);
    }

    @Override
    protected void refreshContentByEvent()
    {
        init(getContent(), this.context);
        super.refreshContentByEvent();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        viewPresenter.rebuild();
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        registerChildPresenter(viewPresenter);

        final UIContext context = (UIContext)this.context;
        FontIconDisplay<?> editIcon = getDisplay().getEditIcon();
        if (context.isEditable() && permissionChecker.hasPermission(
                content.getParent(), context, TabContentCommandCode.EDIT_TOOL_PANEL))
        {
            editIcon.addClickHandler(event -> editFormPresenterFactory.create(
                    (HasToolPanel)getContent().getParent(), context).bind());
            editIcon.setVisible(null == context.getUITemplateContext());
            registerHandler(getContext().getEventBus().addHandler(ChangeUITemplateModeEvent.TYPE,
                    event -> editIcon.setVisible(null == event.getTemplate())));
        }
        else
        {
            editIcon.setVisible(false);
        }

        getDisplay().add(viewPresenter.getDisplay());
        viewPresenter.revealDisplay();
    }
}