package ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;
import com.google.inject.Singleton;

/**
 * Локализация блока "Темы интерфейса"
 *
 * <AUTHOR>
 * @since 15.07.2016
 */
@DefaultLocale("ru")
@Singleton
public interface ThemeMessages extends Messages
{
    @Description("Фон контрастных элементов в навигационном меню и цвет кнопок на панелях действий карточки объекта и"
                 + " ее вкладок")
    String activeElementBackground();

    @Description("Добавить тему")
    String addTheme();

    @Description("Редактирование базовых настроек интерфейса")
    String interfaceBaseSettingsEditForm();

    @Description("Фон")
    String backgroundBlock();

    @Description("к настройкам интерфейса")
    String backToInterfaceSettings();

    @Description("Базовые настройки")
    String baseSettings();

    @Description("На основе темы-шаблона (тема \"Светлая\")")
    String basedOnTemplate();

    @Description("На основе существующей пользовательской темы")
    String basedOnUserTheme();

    @Description("Цвет названия контента")
    String blockTitleColor();

    @Description("Границы и разделители")
    String borderBlock();

    @Description("Цвет границ кнопок и полей ввода")
    String borderColor();

    @Description("Тема ''{0}'' не может быть удалена, так как используется в качестве темы по умолчанию для "
                 + "интерфейса отображения.")
    String cantBeDeleted(String themeTitle);

    String cantBeSwitched(String theme);

    @Description("Основные параметры темы")
    String commonParameters();

    @Description("Фон шапки карточки объекта")
    String contentHeaderBackground();

    @Description("Цвет линии между шапкой и содержимым карточки объекта")
    String contentHeaderBorderColor();

    @Description("Цвет ссылок в шапке карточки объекта")
    String contentHeaderLinkColor();

    @Description("Цвет названия карточки объекта")
    String contentHeaderTextColor();

    @Description("Тема интерфейса по умолчанию")
    String defaultTheme();

    @Description("Изменить тему по умолчанию")
    String defaultThemeEditForm();

    @Description("Выгрузить шаблон")
    String exportTemplate();

    @Description("В интерфейсе настройки")
    String forAdmin();

    @Description("В интерфейсе отображения")
    String forOperator();

    String fromFile();

    @Description("Темы интерфейса")
    String interfaceThemes();

    @Description("Доступна для выбора пользователем")
    String isEnabledForUsers();

    @Description("Системная")
    String isSytem();

    @Description("Основной цвет ссылок")
    String linkColor();

    @Description("Цвет ссылок")
    String linkColorBlock();

    String loginLogoFile();

    String loginLogotype();

    @Description("Логотип окна входа")
    String loginWindowLogo();

    @Description("Логотип системы")
    String logo();

    String logoFile();

    @Description("Файл с логотипом не выбран.")
    String logoFileNotSelected();

    String logotypes();

    @Description("[новая тема]")
    String newTheme();

    String operatorThemePolicy(@Select String policy);

    String operatorThemePolicyCaption();

    String operatorThemePolicyDescription(@Select String policy, @Optional String oldThemeName);

    @Description("Фон нижней части модального окна")
    String popupFooterBackground();

    @Description("Фон шапки модального окна")
    String popupHeaderBackground();

    @Description("Цвет названия модальной формы")
    String popupHeaderTextColor();

    @Description("Фон шапки карточки архивного объекта")
    String removedBOHeaderColor();

    @Description("Файлы ресурсов")
    String resourceFiles();

    @Description("Цвет разделителей строк в списках объектов")
    String rowBorderColor();

    @Description("Вспомогательный цвет текста")
    String secondaryTextColor();

    String standart();

    @Description("Файл со стилями темы не выбран.")
    String styleFileNotSelected();

    @Description("Загрузка файла со стилями")
    String styleFileUpload();

    @Description("Способ задания стилей")
    String styleMode();

    @Description("Стили темы")
    String styleParameters();

    @Description("Системные цвета")
    String systemColors();

    String systemLogotype();

    String systemLogotypes();

    @Description("Основной цвет текста")
    String textColor();

    @Description("Цвет текста")
    String textColorBlock();

    @Description("тему интерфейса")
    String theme();

    @Description("Добавление темы интерфейса")
    String themeAddForm();

    @Description("Редактирование темы интерфейса")
    String themeEditForm();

    @Description("Файл со стилями темы")
    String themeTemplateFile();

    @Description("Загрузить файл шаблона")
    String uploadTemplateFile();

    @Description("Сделать темой по умолчанию")
    String useAsDefaultTheme();
}
