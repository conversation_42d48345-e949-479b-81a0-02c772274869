package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.impl.DialogsImpl;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.SwitchLeftNavigationMenuItemAction;
import ru.naumen.core.shared.navigationsettings.dispatch.SwitchNavigationMenuItemAction;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;

/**
 * Команда включения элемента левого меню
 *
 * <AUTHOR>
 * @since 19.06.2020
 */
public class EnableLeftMenuItemCommand extends EnableMenuItemCommand<LeftMenuItemSettingsDTO>
{
    public static final String ID = "enableLeftMenuItemCommand";

    @Inject
    public EnableLeftMenuItemCommand(@Assisted NavigationSettingsLMCommandParam param)
    {
        super(param);
    }

    @Override
    protected SwitchNavigationMenuItemAction getAction()
    {
        return new SwitchLeftNavigationMenuItemAction();
    }

    @Override
    public void execute(CommandParam<LeftMenuItemSettingsDTO, DtoContainer<NavigationSettings>> cparam)
    {
        NavigationSettingsMenuItemAbstractCommandParam p = (NavigationSettingsMenuItemAbstractCommandParam)prepareParam(
                cparam);
        LeftMenuItemSettingsDTO value = (LeftMenuItemSettingsDTO)p.getValue();
        if (null != value.getItemError())
        {
            new DialogsImpl().error(value.getItemError());
            return;
        }
        super.execute(cparam);
    }
}
