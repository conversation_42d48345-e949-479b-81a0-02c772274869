package ru.naumen.metainfoadmin.client.customforms;

import com.google.gwt.event.shared.EventHandler;

/**
 * Обработчик события изменения настроек формы
 *
 * <AUTHOR>
 * @since 21 апр. 2016 г.
 */
public interface FormSettingsChangedEventHandler extends EventHandler
{
    /**
     * Вызывается при изменении настроек формы
     *
     * @param event
     */
    void onSettingsChanged(FormSettingsChangedEvent event);
}
