package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms;

import jakarta.annotation.Nullable;

import com.google.gwt.event.shared.GwtEvent;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

/**
 * <AUTHOR>
 * @since 18 дек. 2013 г.
 *
 */
public class MenuItemChangedEvent extends GwtEvent<MenuItemChangedHandler>
{

    private static Type<MenuItemChangedHandler> TYPE = new Type<MenuItemChangedHandler>();

    public static Type<MenuItemChangedHandler> getType()
    {
        return TYPE;
    }

    private final DtoContainer<NavigationSettings> settings;
    private final String menuItemCode;

    public MenuItemChangedEvent(DtoContainer<NavigationSettings> settings, @Nullable String menuItemCode)
    {
        this.settings = settings;
        this.menuItemCode = menuItemCode;
    }

    @Override
    public Type<MenuItemChangedHandler> getAssociatedType()
    {
        return TYPE;
    }

    public String getMenuItemCode()
    {
        return menuItemCode;
    }

    public DtoContainer<NavigationSettings> getSettings()
    {
        return settings;
    }

    @Override
    protected void dispatch(MenuItemChangedHandler handler)
    {
        handler.onMenuItemChanged(this);
    }
}