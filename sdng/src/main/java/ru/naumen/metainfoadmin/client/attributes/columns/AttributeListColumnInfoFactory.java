/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.columns;

import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.grouplist.GroupList.ColumnInfo;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;

/**
 * Фабрика, генерирующая структуры данных ColumnInfo - генератор содержимого ячейки определенной колонки и информация
 * о её внешнем виде
 * <AUTHOR>
 *
 */
public interface AttributeListColumnInfoFactory
{
    /**
     * Создание структуры данных с информацией о колонке
     * @param code код колонки
     * @param registrationContainer контейнер для регистрации обработчиков событий
     * @return
     */
    ColumnInfo<Attribute> create(String code, RegistrationContainer registrationContainer);

    /**
     * Создание структуры данных с информацией о колонке с кнопкой
     * @param code код колонки
     * @param param параметры команды
     * @param cmd команда
     * @return
     */
    ColumnInfo<Attribute> createCommandColumn(String code, AttributeCommandParam param, String cmd);

    /**
     * Создание структуры данных с информацией о колонке с кнопкой
     * @param code код колонки
     * @param command команда
     * @param context контекст
     * @return
     */
    ColumnInfo<Attribute> createCommandColumn(String code, BaseCommand<?, ?> command, Context context);

    /**
     * Выполнить переинициализацию фабрики
     * @param compact признак использования компактного режима
     */
    default void reInit(boolean compact)
    {

    }
}
