package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import java.util.List;
import java.util.function.Predicate;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.collect.ImmutableSet;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.Provider;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.client.widgets.properties.PropertyUtils;
import ru.naumen.core.shared.CommonL10nUtils;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.client.ui.template.UITemplateMetainfoServiceAsync;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.templates.ui.UITemplate;
import ru.naumen.metainfo.shared.templates.ui.WindowTemplate;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeObjectCardCaptionEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeUITemplateEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ReloadUIEvent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.templates.ui.UITemplateHelper;

/**
 * {@link Presenter} для редактирования заголовка карточки объекта
 *
 * <AUTHOR>
 * @since 14.11.2014
 */
public class EditWindowCaptionPresenter extends OkCancelPresenter<PropertyDialogDisplay>
{
    private final CommonMessages cmessages;
    private final Provider<SimpleSelectCellListBuilder<String>> provider;
    private final PropertyCreator propertyCreator;
    private final MetainfoModificationServiceAsync metainfoModificationService;
    private final MetainfoServiceAsync metainfoService;
    private final UITemplateMetainfoServiceAsync templateMetainfoService;
    private final UITemplateHelper templateHelper;
    private final CommonL10nUtils localeUtils;
    private final MetainfoUtils metainfoUtils;
    private final Processor validation;
    private final NotEmptyValidator notEmptyValidator;

    private final Property<String> captionString;
    private PropertyRegistration<String> captionStringPR;
    private ValidationUnit<String> captionStringVU;

    private SingleSelectCellList<String> selectObjectCaptionList;
    private UIContext context;

    private final ImmutableSet<String> SYSTEM_COMPUTABLE_STRING_ATTR = ImmutableSet.of(
            //@formatter:off
            Employee.PHONES_INDEX,
            Employee.PASSWORD,
            AbstractBO.UUID,
            ServiceCall.WF_PROFILE_CODE,
            Association.CLIENT_LINK_NAME);
            //@formatter:on

    private final Predicate<Attribute> STRING_TYPE_FILTER = attribute ->
    {
        String code = attribute.getCode();
        return !SYSTEM_COMPUTABLE_STRING_ATTR.contains(code) && !attribute.isComputable()
               && attribute.getType().getCode().equals(StringAttributeType.CODE);
    };

    @Inject
    public EditWindowCaptionPresenter(PropertyDialogDisplay display, EventBus eventBus,
            CommonMessages cmessages,
            Provider<SimpleSelectCellListBuilder<String>> provider,
            PropertyCreator propertyCreator,
            MetainfoModificationServiceAsync metainfoModificationService,
            MetainfoServiceAsync metainfoService,
            UITemplateMetainfoServiceAsync templateMetainfoService,
            UITemplateHelper templateHelper,
            CommonL10nUtils localeUtils,
            MetainfoUtils metainfoUtils,
            NotEmptyValidator notEmptyValidator,
            Processor validation,
            @Named(PropertiesGinModule.TEXT_BOX) Property<String> captionString)
    {
        super(display, eventBus);
        this.cmessages = cmessages;
        this.provider = provider;
        this.propertyCreator = propertyCreator;
        this.metainfoModificationService = metainfoModificationService;
        this.metainfoService = metainfoService;
        this.templateMetainfoService = templateMetainfoService;
        this.templateHelper = templateHelper;
        this.localeUtils = localeUtils;
        this.metainfoUtils = metainfoUtils;
        this.notEmptyValidator = notEmptyValidator;
        this.validation = validation;
        this.captionString = captionString;
    }

    public void init(UIContext context)
    {
        this.context = context;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }

        final String value = getCaptionValue();
        if (isTemplate())
        {
            previewTemplate(value);
        }
        else
        {
            Window content = (Window)context.getRootContent();
            saveContent(content, value);
        }
    }

    private String getCaptionValue()
    {
        return SelectListPropertyValueExtractor.getValue(selectObjectCaptionList);
    }

    private boolean isTemplate()
    {
        return context.getUITemplateContext() != null;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().clearProperties();
        getDisplay().setCaptionText(cmessages.editObjectCaption());

        selectObjectCaptionList = provider.get().setHasSearch(true).setCellGlobalPaddingLeft(0).build();
        selectObjectCaptionList.setHasSearchLite(true);
        selectObjectCaptionList.addValueChangeHandler(value -> refreshCaptionStringVisibility());

        bindSelectList(context.getMetainfo());
        Property<String> selectProperty = propertyCreator.create(cmessages.objectCaption(), selectObjectCaptionList);
        selectProperty.setValidationMarker(true);

        captionString.setCaption(cmessages.string());
        captionString.setValidationMarker(true);
        captionString.setMaxLength(255);
        PropertyUtils.addDescriptionHint(captionString, cmessages.captionTextDescription());

        getDisplay().add(selectProperty);

        fillValues();

        ensureDebugId();
        getDisplay().display();
    }

    private void fillValues()
    {
        String currentCaption = ((Window)context.getRootContent()).getObjectCardCaptionAttributeCode();
        selectObjectCaptionList.setObjValue(currentCaption, true);
        if (Constants.STRING_CAPTION_CODE.equals(currentCaption))
        {
            List<LocalizedString> objectCardCaptionAttributeString =
                    ((WindowTemplate)getTemplate().getContent()).getObjectCardCaptionString();
            captionString.setValue(localeUtils.getLocalizedValue(objectCardCaptionAttributeString));
        }
    }

    private void ensureDebugId()
    {
        DebugIdBuilder.ensureDebugId(selectObjectCaptionList, "selectObjectCaptionList");
        DebugIdBuilder.ensureDebugId(captionString, "captionString");
    }

    private void refreshCaptionStringVisibility()
    {
        boolean isVisible = Constants.STRING_CAPTION_CODE.equals(getCaptionValue());
        if (isVisible && captionStringPR == null)
        {
            captionStringPR = getDisplay().add(captionString);
            captionStringVU = validation.validate(captionString, notEmptyValidator);
        }
        if (!isVisible && captionStringPR != null)
        {
            getDisplay().unregister(captionStringPR);
            validation.unregister(captionStringVU);
            captionStringPR = null;
        }
    }

    private void bindSelectList(MetaClass metaclass)
    {
        selectObjectCaptionList.clear();
        selectObjectCaptionList.addItem(cmessages.brackets(cmessages.noCaption()), Constants.WITHOUT_CAPTION_CODE);

        // добавляем элементы, используемые только на карточке шаблона
        if (isTemplate())
        {
            selectObjectCaptionList.addItem(cmessages.cardCaption(), Constants.CARD_CAPTION_CODE);
            selectObjectCaptionList.addItem(cmessages.string(), Constants.STRING_CAPTION_CODE);
        }

        // добавляем элементы, используемые только для не рута
        if (!context.getMetainfo().getFqn().equals(Root.FQN))
        {
            selectObjectCaptionList.addItem(
                    cmessages.typeTitleWithAttrTitle(context.getMetainfo().getAttribute(AbstractBO.TITLE).getTitle()),
                    Constants.TYPE_NAME_AND_TITLE_CODE);
        }

        // добавляем атрибуты
        metaclass.getAttributes().stream()
                .filter(STRING_TYPE_FILTER)
                .sorted(CommonUtils.ITITLED_COMPARATOR)
                .forEach(attribute -> selectObjectCaptionList.addItem(attribute.getTitle(), attribute.getCode()));
    }

    private UITemplate getTemplate()
    {
        return context.getUITemplateContext().getTemplate();
    }

    private void previewTemplate(String value)
    {
        UITemplate template = getTemplate();
        WindowTemplate windowTemplate = (WindowTemplate)template.getContent();
        windowTemplate.setObjectCardCaptionAttributeCode(value);
        if (Constants.STRING_CAPTION_CODE.equals(value))
        {
            metainfoUtils.setCaption(windowTemplate.getObjectCardCaptionString(), captionString.getValue());
        }
        else
        {
            windowTemplate.getObjectCardCaptionString().clear();
        }

        templateMetainfoService.previewContent(template, context.getMetainfo().getFqn(), context.getCode(),
                new BasicCallback<Content>(context.getReadyState(), getDisplay())
                {
                    @Override
                    protected void handleSuccess(Content content)
                    {
                        ContentInfo contentInfo = templateHelper.createContentInfo(context, content);
                        context.getEventBus().fireEvent(new ChangeUITemplateEvent(true));
                        context.getEventBus().fireEvent(new ReloadUIEvent(contentInfo));
                        unbind();
                    }
                });
    }

    private void saveContent(Window content, String value)
    {
        final String oldValue = content.getObjectCardCaptionAttributeCode();
        content.setObjectCardCaptionAttributeCode(value);

        metainfoModificationService.saveUI(context.getMetainfo().getFqn(), content, null, context.getCode(), null,
                true, new BasicCallback<Void>(getDisplay())
                {
                    @Override
                    protected void handleFailure(Throwable t)
                    {
                        content.setObjectCardCaptionAttributeCode(oldValue);
                        metainfoService.getMetaClass(context.getMetainfo().getFqn(), new BasicCallback<MetaClass>()
                        {
                            @Override
                            public void handleSuccess(MetaClass metaClass)
                            {
                                bindSelectList(metaClass);
                            }
                        });
                        getDisplay().addAttentionMessage(t.getMessage());
                    }

                    @Override
                    protected void handleSuccess(Void v)
                    {
                        context.getEventBus()
                                .fireEvent(
                                        new ChangeObjectCardCaptionEvent(content.getObjectCardCaptionAttributeCode(),
                                                content.getObjectCardCaptionString()));
                        unbind();
                    }
                });
    }

    @Override
    public void onCancel()
    {
        super.onCancel();
        captionStringPR = null;
    }
}
