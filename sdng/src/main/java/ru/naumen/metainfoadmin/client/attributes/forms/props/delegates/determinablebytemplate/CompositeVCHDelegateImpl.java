package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determinablebytemplate;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.List;

import com.google.common.collect.Lists;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 *
 * <AUTHOR>
 *
 * @param <F>
 */
public class CompositeVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{

    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        Boolean composite = context.getPropertyValues().getProperty(COMPOSITE);
        if (composite)
        {
            context.setProperty(COMPUTABLE, false);
            context.setProperty(DETERMINABLE, false);
            context.setProperty(EDITABLE, false);
            context.setProperty(EDITABLE_IN_LISTS, false);
            context.setProperty(UNIQUE, false);
            context.setProperty(DEFAULT_BY_SCRIPT, false);
            context.setProperty(COMPUTABLE_ON_FORM, false);
        }
        else
        {
            context.setProperty(EDITABLE, true);
        }

        context.getRefreshProcess().startCustomProcess(getPropertiesToRefresh());
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }

    protected List<String> getPropertiesToRefresh()
    {
        return Lists.newArrayList(DETERMINABLE, DETERMINER, COMPUTABLE, REQUIRED, REQUIRED_IN_INTERFACE, EDITABLE,
                EDITABLE_IN_LISTS, UNIQUE, PERMITTED_TYPES, SCRIPT, DATE_TIME_COMMON_RESTRICTIONS,
                DATE_TIME_RESTRICTION_SCRIPT, EDIT_PRS, SUGGEST_CATALOG, SELECT_SORTING, FILTERED_BY_SCRIPT,
                SCRIPT_FOR_FILTRATION, COMPUTABLE_ON_FORM, COMPUTABLE_ON_FORM_SCRIPT, DEFAULT_VALUE_LABEL,
                DEFAULT_BY_SCRIPT, SCRIPT_FOR_DEFAULT, TEMPLATE, USE_GEN_RULE, DEFAULT_VALUE);
    }
}
