package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import com.google.gwt.place.shared.Place;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * <AUTHOR>
 * @since 26 сент. 2013 г.
 */
public abstract class NavigationMenuItemPlace<M extends IMenuItem> extends Place
{
    private String code = null;
    private M menuItem = null;
    private DtoContainer<NavigationSettings> settings = null;

    protected NavigationMenuItemPlace(M menuItem, DtoContainer<NavigationSettings> settings)
    {
        this.menuItem = menuItem;
        this.code = menuItem.getCode();
        this.settings = settings;
    }

    public NavigationMenuItemPlace(String code)
    {
        this.code = code;
    }

    protected NavigationMenuItemPlace()
    {
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null || this.getClass() != obj.getClass())
        {
            return false;
        }
        NavigationMenuItemPlace other = (NavigationMenuItemPlace)obj;
        return ObjectUtils.equals(this.code, other.code);
    }

    public String getCode()
    {
        return code;
    }

    public M getMenuItem()
    {
        return menuItem;
    }

    public DtoContainer<NavigationSettings> getSettings()
    {
        return settings;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(code);
    }

    @Override
    public String toString()
    {
        return "NavigationMenuItemPlace: " + getCode();
    }
}