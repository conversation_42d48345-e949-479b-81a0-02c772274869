package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

/**
 * Позволяет обрабатывать события об изменении размера колонок
 *
 * <AUTHOR>
 * @since 20.01.17
 *
 */
public interface ResizableColumnsOwner
{
    /**
     * Идет процесс изменения ширины колонки
     */
    void onColumnResize();

    /**
     * Завершен процесс изменения ширины колонки
     */
    void onColumnResizeCompleted();
}
