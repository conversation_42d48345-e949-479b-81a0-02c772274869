package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.base.Predicate;
import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.core.shared.navigationsettings.menu.MenuItemTypeOfCard;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.Constants.TextAttributeType;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.GetNavigationReferencesAction;
import ru.naumen.metainfo.shared.dispatch2.GetNavigationReferencesResponse;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentMetaClassPropertiesProcessor;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 *
 * Вспомогательный класс получения данных для форм "Верхнее меню" и "Левое меню"
 * <AUTHOR>
 * @since 14.03.2022
 */
public class ReferenceHelper
{
    private final DispatchAsync dispatch;
    private final MetainfoServiceAsync metainfoService;
    private final MetainfoUtils metainfoUtils;

    @Inject
    public ReferenceHelper(DispatchAsync dispatch, MetainfoServiceAsync metainfoService, MetainfoUtils metainfoUtils)
    {
        this.dispatch = dispatch;
        this.metainfoService = metainfoService;
        this.metainfoUtils = metainfoUtils;
    }

    public static final List<String> AVAILABLE_MENU_ITEM_TYPES_OF_CARD =
            Lists.newArrayList(MenuItemTypeOfCard.REFERENCE_TO_USER.name(),
                    MenuItemTypeOfCard.REFERENCE_TO_OU.name(),
                    MenuItemTypeOfCard.REFERENCE_TO_ROOT.name());

    /**
     * Заполнить виджет {@link ru.naumen.metainfo.shared.Constants.ReferenceCode#REFERENCE_VALUE)}
     * @param classFqn fqn объекта
     * @param context контекст контейнера свойств
     * @param property представление свойства в виде списка одиночного выбора
     */
    public void fillReferenceValueProperty(@Nullable ClassFqn classFqn, DtObjectSelectProperty property,
            PropertyContainerContext context, @Nullable RelationsAttrTreeObject attrTree)
    {
        final SingleSelectCellList<DtObject> selectList = property.getValueWidget();
        selectList.clear();
        selectList.setHasEmptyOption(true);
        if (classFqn == null)
        {
            property.clearValue();
            return;
        }
        Collection<String> selectedCases = context.getPropertyValues().getProperty(ReferenceCode.OBJECT_CASES);

        if (selectedCases == null || selectedCases.isEmpty())
        {
            selectedCases = new ArrayList<>(getFilterPermittedTypes(attrTree, classFqn));
        }
        GetNavigationReferencesAction action = new GetNavigationReferencesAction(Lists.newArrayList(selectedCases));
        dispatch.execute(action, new BasicCallback<GetNavigationReferencesResponse>()
        {
            @Override
            protected void handleSuccess(GetNavigationReferencesResponse from)
            {
                for (Reference reference : from.res)
                {
                    SimpleDtObject dto = new SimpleDtObject(reference.getCode(), reference.getTitle());
                    dto.setProperty(ReferenceCode.TAB_UUIDS, reference.getTabUUIDs());
                    dto.setProperty(ReferenceCode.CLASS_FQN, reference.getClassFqn());
                    dto.setProperty(Constants.TREE_LEVEL, reference.getLevel() - 1);
                    selectList.addItem(dto);
                }
                DtObject currentRef = context.getPropertyValues().getProperty(ReferenceCode.REFERENCE_VALUE);
                if (currentRef != null)
                {
                    selectList.setObjValue(currentRef);
                    property.trySetObjValue(currentRef);
                }
            }
        });
    }

    private static List<String> getFilterPermittedTypes(@Nullable RelationsAttrTreeObject attrTree, ClassFqn classFqn)
    {
        if (attrTree != null && attrTree.getAttribute() != null
            && attrTree.getAttribute().getType().getPermittedTypes() != null
            && !attrTree.getAttribute().getType().getPermittedTypes().contains(classFqn))
        {
            return attrTree.getAttribute().getType().getPermittedTypes()
                    .stream()
                    .map(ClassFqn::toString)
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList(classFqn.toString());
    }

    /**
     * Заполнить виджет {@link MenuItemPropertyCode#ATTR_FOR_USE_IN_TITLE}
     * @param metaClassFqn fqn объекта
     * @param context контекст контейнера свойств
     * @param property представление свойства в виде списка одиночного выбора
     */
    public void fillMenuItemAttrForUseInTitle(ClassFqn metaClassFqn, DtObjectSelectProperty property,
            AsyncCallback<Boolean> callback, PropertyContainerContext context)
    {
        DtObject currentObject = context.getPropertyValues().getProperty(MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE);
        final SingleSelectCellList<DtObject> selectList = property.getValueWidget();
        selectList.setHasEmptyOption(false);
        selectList.clear();
        property.clearValue();
        metainfoService.getAllAttributes(metaClassFqn, new BasicCallback<Collection<Attribute>>()
        {
            @Override
            protected void handleSuccess(Collection<Attribute> attributes)
            {
                attributes.stream()
                        .filter(a -> (StringAttributeType.CODE.equals(a.getType().getCode())
                                      || TextAttributeType.CODE.equals(a.getType().getCode()))
                                     && !Employee.PASSWORD.equals(a.getCode())
                                     && !Presentations.PASSWORD_VIEW.equals(a.getViewPresentation().getCode()))
                        .forEach(a -> selectList.addItem(
                                currentObject != null && currentObject.getUUID().equals(a.getCode()) ? currentObject :
                                        new SimpleDtObject(a.getFqn().toString(),
                                                a.getTitle() + "(" + a.getCode() + ")")));
                if (currentObject != null)
                {
                    property.trySetObjValue(currentObject);
                    context.getPropertyValues().setProperty(MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE, currentObject);
                }
                callback.onSuccess(true);
            }
        });
    }

    /**
     * Заполнить типы объектов
     * @param fqn fqn объекта
     * @param context контекст контейнера свойств
     * @param property представление свойства в виде ListBox с множественным выбором
     * @param filter предикат для фильтрации
     */
    public void fillObjectCasesProperty(ClassFqn fqn, PropertyContainerContext context,
            MultiSelectBoxProperty property,
            Predicate<? extends MetaClassLite> filter,
            AsyncCallback<Boolean> callback)
    {
        Collection<String> selectedCases = context.getPropertyValues()
                .getProperty(ReferenceCode.OBJECT_CASES, new ArrayList<>());
        metainfoService.getDescendantClasses(fqn, fqn.isCase(), filter, new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> casesList)
            {
                metainfoUtils.sort(casesList);
                for (MetaClassLite clz : metainfoUtils.getPossible(casesList, true))
                {
                    property.getValueWidget().addItem(clz.getTitle(),
                            clz.getFqn().toString());
                }

                Set<String> cases = new HashSet<>();
                for (String caseStr : selectedCases)
                {
                    ClassFqn caseFqn = ClassFqn.parse(caseStr);
                    if (fqn.isSameClass(caseFqn))
                    {
                        cases.add(caseStr);
                    }
                }
                property.trySetObjValue(cases);
                context.getPropertyValues().setProperty(ReferenceCode.OBJECT_CASES, cases);
                callback.onSuccess(true);
            }
        });
    }

    /**
     * Проверяет наличие метакласса в разрешённых типах Атрибута связи. Используется метод
     * {@link  AttributeType#getPermittedTypes()} для получения списка доступных метаклассов.
     * @param attribute атрибут связи, в
     * @return true, если список атрибутов связи пустой или метакласс находится в разрешённых типах у одного из
     * атрибутов связи
     */
    public static Predicate<? extends MetaClassLite> getAttributeChainPermittedClasses(@Nullable Attribute attribute)
    {
        if (attribute == null)
        {
            return reference -> true;
        }
        Set<ClassFqn> permittedTypes = attribute.getType().getPermittedTypes();
        if (CollectionUtils.isNotEmpty(permittedTypes))
        {
            return MetaClassFilters.in(permittedTypes);
        }
        return reference -> true;
    }

    /**
     * Получить FQN класса в зависимости от {@link MenuItemTypeOfCard типа карточки}
     * @param typeOfCard объект дто, хранящий ссылку на карточку объекта
     * @param attrTree атрибут связи меню
     */
    public static ClassFqn getObjectClassFqnByCardType(@Nullable DtObject typeOfCard,
            @Nullable RelationsAttrTreeObject attrTree)
    {
        String referenceCardType = typeOfCard == null ? null : typeOfCard.getUUID();
        if (attrTree != null && attrTree.getAttribute() != null)
        {
            return attrTree.getAttribute().getType().<ObjectAttributeType> cast().getRelatedMetaClass();
        }

        if (referenceCardType == null || MenuItemTypeOfCard.CURRENT_USER.name().equals(referenceCardType)
            || MenuItemTypeOfCard.REFERENCE_TO_USER.name().equals(referenceCardType))
        {
            return Employee.FQN;
        }
        if (MenuItemTypeOfCard.CURRENT_USER_OU.name().equals(referenceCardType)
            || MenuItemTypeOfCard.REFERENCE_TO_OU.name().equals(referenceCardType))
        {
            return OU.FQN;
        }
        if (MenuItemTypeOfCard.ROOT.name().equals(referenceCardType)
            || MenuItemTypeOfCard.REFERENCE_TO_ROOT.name().equals(referenceCardType))
        {
            return Root.FQN;
        }
        return Employee.FQN;
    }

    /**
     * Заполнить значение виджета "Класс объекта" в зависимости от типа карточки объекта. Записывает в контекстные
     * свойства название класса
     * @param typeOfCard тип каточки меню
     * @param attrTree атрибут связи
     * @param property Редактируемое строковое свойство
     * @param context контекст контейнера свойств
     */
    public void fillObjectClassValue(@Nullable DtObject typeOfCard, @Nullable RelationsAttrTreeObject attrTree,
            TextBoxProperty property, PropertyContainerContext context, AsyncCallback<Boolean> callback)
    {
        metainfoService.getMetaClass(getObjectClassFqnByCardType(typeOfCard, attrTree),
                new BasicCallback<MetaClass>()
                {
                    @Override
                    public void handleSuccess(MetaClass metaClass)
                    {
                        property.clearValue();
                        property.setValue(metaClass.getTitle());
                        context.setProperty(ReferenceCode.OBJECT_CLASS, metaClass.getTitle());
                        callback.onSuccess(true);
                    }
                });
    }

    /**
     * Метод преобразует Атрибут связи attrChain в объект дерева AttrTreeObject и сохраняет его в
     * контекстные настройки с ключом propertyName.
     * @param propertyName название поля настроек, в которое сохранится итоговое дерево атрибутов.
     * @param properties настройки контекста. Используются для сохранения итогового дерева атрибутов в контекст.
     * @param readyState состояние готовности.
     * @param attrChain список атрибутов связи. Используется для преобразования в древовидную форму и дальнейшего
     *                  сохранения в контекст.
     * @param titleWrapper разделитель заголовка. Используется для форматирования названия атрибута.
     */
    public void transformAttrReferenceToAttrTreeObject(
            String propertyName,
            IProperties properties,
            SuccessReadyState readyState,
            @Nullable List<AttrReference> attrChain,
            @Nullable Function<MetaClass, String> titleWrapper)
    {
        if (null == attrChain)
        {
            return;
        }
        if (!attrChain.isEmpty() && attrChain.get(0).getClassFqn() == null)
        {
            attrChain.set(0, new AttrReference(attrChain.get(0).getClassFqn(), attrChain.get(0).getAttrCode()));
        }
        readyState.notReady();
        metainfoService.getFullMetaInfo(
                attrChain.stream().map(AttrReference.FQN_EXTRACTOR).collect(Collectors.toList()),
                new BasicCallback<List<MetaClass>>()
                {
                    @Override
                    protected void handleSuccess(List<MetaClass> value)
                    {
                        RelationsAttrTreeObject relationsAttribute = null;
                        for (AttrReference attributeReference : attrChain)
                        {
                            MetaClass metaClass = value.stream()
                                    .filter(mc -> mc.getFqn().equals(attributeReference.getClassFqn()))
                                    .findFirst()
                                    .orElse(null);
                            if (metaClass == null)
                            {
                                break;
                            }
                            Attribute attr = metaClass.getAttribute(attributeReference.getAttrCode());
                            if (attr == null && ru.naumen.metainfo.shared.Constants.CURRENT_OBJECT.equals(
                                    attributeReference.getAttrCode()))
                            {
                                attr = LinkToContentMetaClassPropertiesProcessor
                                        .buildCurrentObjectAttribute(
                                                titleWrapper == null
                                                        ? metaClass.getTitle()
                                                        : titleWrapper.apply(metaClass),
                                                metaClass);
                            }
                            relationsAttribute = new RelationsAttrTreeObject(relationsAttribute, attr);
                        }
                        properties.setProperty(propertyName, relationsAttribute);
                        readyState.ready();
                    }
                });
    }
}