package ru.naumen.metainfoadmin.client.templates.list;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TEMPLATES;

import java.util.Set;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Provider;

import jakarta.inject.Inject;
import ru.naumen.admin.client.advlists.AdminAdvListPresenterBase;
import ru.naumen.core.client.DisplayHolder;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.templates.list.form.AddListTemplateFormPresenter;
import ru.naumen.objectlist.client.AddMetainfoObjectEvent;
import ru.naumen.objectlist.client.AddMetainfoObjectHandler;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionEvent;

/**
 * Презентер списка шаблонов списков
 * <AUTHOR>
 * @since 06.04.2018
 */
public class ListTemplatesListPresenter extends AdminAdvListPresenterBase<DtObject>
{
    private class AddListTemplateHandler implements AddMetainfoObjectHandler
    {
        @Override
        public void onAddMetainfoObject(AddMetainfoObjectEvent event)
        {
            AddListTemplateFormPresenter presenter = formProvider.get();
            presenter.init(null, refreshCallback);
            presenter.bind();
        }
    }

    private final Provider<AddListTemplateFormPresenter> formProvider;

    @Inject
    public ListTemplatesListPresenter(DisplayHolder display,
            EventBus eventBus,
            CommandFactory commandFactory,
            ListTemplatesAdvlistFactory advlistFactory,
            Provider<AddListTemplateFormPresenter> formProvider)
    {
        super(display, eventBus, commandFactory, advlistFactory);
        this.formProvider = formProvider;
    }

    @Override
    protected AddMetainfoObjectHandler getAddMetainfoObjectHandler()
    {
        return new AddListTemplateHandler();
    }

    @Override
    protected Set<String> getActionCommands()
    {
        return ListTemplatesCommandCode.COMMANDS_IN_LIST;
    }

    @Override
    protected Object getContextValue(ObjectListActionEvent event)
    {
        if (event.getAction().equals(ListTemplatesCommandCode.DELETE))
        {
            return event.getValues();
        }
        else
        {
            return event.getValue();
        }
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Override
    public void onObjectListAction(ObjectListActionEvent event)
    {
        if (getActionCommands().contains(event.getAction()))
        {
            CommandParam commandParam = new CommandParam(getContextValue(event), refreshCallback);
            BaseCommand<?, ?> command = commandFactory.create(event.getAction(), commandParam);
            command.execute(commandParam);
        }
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return TEMPLATES;
    }
}
