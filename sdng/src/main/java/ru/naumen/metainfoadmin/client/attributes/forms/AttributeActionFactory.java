/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.forms;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.metainfo.shared.dispatch2.AttributeModificationAction;
import ru.naumen.metainfo.shared.elements.AttributeType;

import com.google.inject.assistedinject.Assisted;

public interface AttributeActionFactory<F extends ObjectForm>
{
    AttributeModificationAction createAction(@Assisted("contextProps") IProperties contextProps,
            @Assisted("propertyValues") IProperties propertyValues, AttributeType attrType);
}