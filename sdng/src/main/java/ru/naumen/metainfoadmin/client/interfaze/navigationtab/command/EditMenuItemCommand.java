package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import com.google.inject.Provider;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.MenuItemFormPresenter;

/**
 * Абстрактная команда редактирования элемент меню
 *
 * <AUTHOR>
 * @since 30 сент. 2013 г.
 */
public abstract class EditMenuItemCommand<M extends IMenuItem>
        extends BaseCommandImpl<M, DtoContainer<NavigationSettings>>
{
    private final Provider<? extends MenuItemFormPresenter<ObjectFormEdit, M>> presenterProvider;

    public EditMenuItemCommand(NavigationSettingsMenuItemAbstractCommandParam param,
            Provider<? extends MenuItemFormPresenter<ObjectFormEdit, M>> presenterProvider)
    {
        super(param);
        this.presenterProvider = presenterProvider;
    }

    @Override
    public void execute(CommandParam<M, DtoContainer<NavigationSettings>> param)
    {
        NavigationSettingsMenuItemAbstractCommandParam<?> p = (NavigationSettingsMenuItemAbstractCommandParam)param;

        MenuItemFormPresenter<ObjectFormEdit, M> presenter = presenterProvider.get();

        presenter.init(p.getSettings().get(), (M)p.getValue(), p.getCallback());
        presenter.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}
