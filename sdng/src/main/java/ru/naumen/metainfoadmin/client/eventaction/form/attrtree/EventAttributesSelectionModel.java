package ru.naumen.metainfoadmin.client.eventaction.form.attrtree;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.tree.selection.FilteredMultiSelectionModel;
import ru.naumen.core.client.tree.selection.TreeKeyProvider;
import ru.naumen.core.shared.dto.DtObject;

import jakarta.inject.Inject;

import java.util.List;

import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.AttrTree.AGGREGATED_ATTRS;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.AttrTree.AGGREGATING_ATTR;

/**
 * Модель выбора атрибутов в дереве атрибутов ДПС
 * При выборе агрегирующего атрибута автоматически выбираются агрегируемые атрибуты
 * При выборе агрегируемого атрибута автоматически выбирается агрегирующий атрибут
 * <AUTHOR>
 * @since 17.05.2022
 */
public class EventAttributesSelectionModel extends FilteredMultiSelectionModel<DtObject>
{
    @Inject
    public EventAttributesSelectionModel(TreeKeyProvider<DtObject> keyProvider)
    {
        super(obj -> true, keyProvider);
    }

    @Override
    public void setSelected(DtObject object, boolean selected)
    {
        DtObject aggregatingAttr = (DtObject)object.get(AGGREGATING_ATTR);
        if (aggregatingAttr != null)
        {
            setSelected(aggregatingAttr, selected);
            return;
        }
        super.setSelected(object, selected);
        List<DtObject> aggregatedAttrs = (List<DtObject>)object.get(AGGREGATED_ATTRS);
        if (!CollectionUtils.isEmpty(aggregatedAttrs))
        {
            aggregatedAttrs.forEach(attr -> super.setSelected(attr, selected));
        }
    }
}
