package ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.PresenterCommandEvent;
import ru.naumen.core.client.mvp.PresenterCommandEvent.PresenterCommandCode;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector.PropertyContainerPresenterFactory;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.core.shared.escalation.EscalationSchemeLevelClient;
import ru.naumen.core.shared.escalation.EscalationSchemeLevelCondition;
import ru.naumen.core.shared.escalation.EscalationSchemeLevelCondition.EscalationSchemeLevelConditionCode;
import ru.naumen.core.shared.escalation.EscalationSchemeLevelConditionTimeImpl;
import ru.naumen.core.shared.escalation.EscalationSchemeLevelConditionTimePartImpl;
import ru.naumen.metainfo.shared.dispatch2.SaveEscalationSchemeLevelAction;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinModule.EscalationSchemeLevelContextValueCode;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinModule.EscalationSchemeLevelPropertyCode;

/**
 * <AUTHOR>
 * @since 22.08.2012
 *
 */
public class EscalationSchemeLevelForm<F extends ObjectForm> extends OkCancelPresenter<DefaultPropertyFormDisplayImpl>
{
    private static final List<String> PROPERTIES = Arrays.asList(EscalationSchemeLevelPropertyCode.CONDITION,
            EscalationSchemeLevelPropertyCode.VALUE, EscalationSchemeLevelPropertyCode.ACTION,
            EscalationSchemeLevelPropertyCode.EXEC_ACTION, EscalationSchemeLevelPropertyCode.SETTINGS_SET);
    @Inject
    PropertyContainerPresenterFactory containerFactory;
    @Inject
    PropertyControllerFactory<EscalationSchemeLevel, F> propertyControllerFactory;
    @Inject
    EscalationSchemeLevelFormMessages<F> messages;
    @Inject
    Processor validation;
    @Inject
    DispatchAsync dispatch;

    @Inject
    EscalationSchemeLevelPropertyContainerAfterBindHandler afterBindHandler;

    protected IProperties contextProps = new MapProperties();
    protected IProperties propertyValues = new MapProperties();
    protected PropertyContainerPresenter propertyContainer;
    private final EventBus localEventBus;
    protected final EscalationScheme escalationScheme;

    @Inject
    public EscalationSchemeLevelForm(DefaultPropertyFormDisplayImpl display, EventBus eventBus,
            @Assisted EventBus localEventBus,
            @Assisted EscalationScheme escalationScheme)
    {
        super(display, eventBus);
        this.localEventBus = localEventBus;
        this.escalationScheme = escalationScheme;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        EscalationSchemeLevelClient level = new EscalationSchemeLevelClient();
        level.setCondition(createCondition());
        level.setActions(Lists.newArrayList(propertyValues
                .<List<String>> getProperty(EscalationSchemeLevelPropertyCode.ACTION)));
        level.setActionExecuted(propertyValues.<Boolean> getProperty(EscalationSchemeLevelPropertyCode.EXEC_ACTION));
        level.setLevel(contextProps.<Integer> getProperty(EscalationSchemeLevelContextValueCode.LEVEL));
        level.setSettingsSet(propertyValues.<String> getProperty(EscalationSchemeLevelPropertyCode.SETTINGS_SET));
        dispatch.execute(new SaveEscalationSchemeLevelAction(level, escalationScheme.getCode()),
                new SafeBasicCallback<SimpleResult<EscalationSchemeLevel>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<EscalationSchemeLevel> response)
                    {
                        unbind();
                        localEventBus.fireEvent(new PresenterCommandEvent(PresenterCommandCode.REFRESH));
                    }
                });
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.title());
        setProperties();
        propertyContainer = containerFactory.createSimple(PROPERTIES, getDisplay(), propertyControllerFactory,
                contextProps, propertyValues, afterBindHandler, validation);
        registerHandler(propertyContainer.addUpdateTabOrderHandler(this));
        propertyContainer.bind();
        getDisplay().display();
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        if (null != propertyContainer)
        {
            propertyContainer.unbind();
        }
    }

    protected void setProperties()
    {
        contextProps.setProperty(EscalationSchemeLevelContextValueCode.ESCALATION_SCHEME, escalationScheme);
    }

    private EscalationSchemeLevelCondition createCondition()
    {
        String conditionCode = propertyValues.<String> getProperty(EscalationSchemeLevelPropertyCode.CONDITION);
        if (EscalationSchemeLevelConditionCode.TIME_EXCEEDED.equals(conditionCode))
        {
            return new EscalationSchemeLevelConditionTimeImpl(conditionCode,
                    propertyValues.<DateTimeInterval> getProperty(EscalationSchemeLevelPropertyCode.VALUE_DTI));
        }
        if (EscalationSchemeLevelConditionCode.TIME_PART_EXCEEDED.equals(conditionCode))
        {
            return new EscalationSchemeLevelConditionTimePartImpl(conditionCode,
                    propertyValues.<Long> getProperty(EscalationSchemeLevelPropertyCode.VALUE_PERCENT));
        }
        return null;
    }
}
