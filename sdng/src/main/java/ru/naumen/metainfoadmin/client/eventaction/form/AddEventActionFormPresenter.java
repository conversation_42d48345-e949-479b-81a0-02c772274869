package ru.naumen.metainfoadmin.client.eventaction.form;

import static ru.naumen.metainfo.shared.Constants.MAX_METAINFO_KEY_LENGTH;
import static ru.naumen.metainfo.shared.Constants.PREFIXED_CODE_SPECIAL_CHARS_FOR_VENDOR;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.google.gwt.event.shared.EventBus;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithoutFolders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactory;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.validation.MetainfoPrefixedCodeValidator;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormGinModule.EventAttributesTree;
import ru.naumen.metainfoadmin.client.eventaction.form.attrtree.EventAttributesTreeFactoryContext;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * <AUTHOR>
 * @since 02.12.2011
 */
public class AddEventActionFormPresenter extends AbstractEventActionFormPresenter
{
    @Inject
    private MetainfoPrefixedCodeValidator codeValidator;
    @Inject
    private TransliterationService transliterator;
    @Inject
    private SecurityHelper security;

    @Inject
    public AddEventActionFormPresenter(
            @Named(EventActionFormGinModule.EVENT_ACTION_TREE) SelectFqnsPropertyFactory treeProvider,
            EventActionFormDisplay display,
            EventBus eventBus,
            DtoTreeFactory<Collection<DtObject>, EventAttributesTree, WithoutFolders,
                    EventAttributesTreeFactoryContext> attrTreeFactory,
            SettingsSetOnFormCreator settingsSetOnFormCreator)
    {
        super(treeProvider, display, eventBus, settingsSetOnFormCreator, attrTreeFactory);
    }

    @Override
    protected List<EventType> getAllowedEvents(@Nullable List<MetaClassLite> metaClasses, Set<ClassFqn> selectedFqns,
            @Nullable String actionType)
    {
        List<EventType> result = super.getAllowedEvents(metaClasses, selectedFqns, actionType);
        result.remove(EventType.escalation);
        return result;
    }

    @Override
    protected void initValidation()
    {
        super.initValidation();
        getCode().setValidationMarker(true);
        getCode().setMaxLength(MAX_METAINFO_KEY_LENGTH);

        validation.validate(getCode(), codeValidator);
    }

    @Override
    protected boolean isNewEventAction()
    {
        return true;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(getEventActionMessages().addFormCaption());
        initActionTypes();
    }

    @Override
    protected void registerHandlers()
    {
        super.registerHandlers();
        registerHandler(getTitle().addValueChangeHandler(event ->
        {
            final String title = getTitle().getValue();
            String code = getCode().getValue();
            if (StringUtilities.isEmptyTrim(title) || !StringUtilities.isEmptyTrim(code))
            {
                return;
            }
            code = transliterator.transliterateToCode(title, MAX_METAINFO_KEY_LENGTH,
                    security.hasVendorProfile() ? PREFIXED_CODE_SPECIAL_CHARS_FOR_VENDOR : StringUtilities.EMPTY);
            getCode().setValue(code);
        }));
    }

    @Override
    protected void initActionTypes(Set<ActionType> unavailableActionTypes)
    {
        final Set<ActionType> availableActionTypes = availableActionTypes(unavailableActionTypes);
        SingleSelectCellList<?> list = getAction().getValueWidget();

        getEventActionFactory().getEventActionCreators().keySet().stream()
                .filter(availableActionTypes::contains)
                .map(Enum::name)
                .map(actionType -> new SimpleDtObject(actionType, eventActionConstants.actionTypes().get(actionType)))
                .sorted(ITitled.COMPARATOR)
                .forEach(item -> list.addItem(item.getTitle(), item.getUUID()));
    }
}
