package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.directLink;

import static ru.naumen.core.shared.attr.AggregateAttributeUtils.getAggregator;
import static ru.naumen.core.shared.attr.AggregateAttributeUtils.isAggregated;
import static ru.naumen.core.shared.attr.AggregateAttributeUtils.isResponsibleAttr;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Класс с базовой логикой обработки полученного метакласса для свойства "Прямая ссылка" в атрибуте "Обратная ссылка".
 * После получения метакласса в контексте заполняются свойства прямой ссылки через которую образуется обратная.
 * Не вызывает принудительного обновления других свойств атрибута на форме
 *
 * <AUTHOR>
 * @since 30.10.2023
 */
public class GetMetaClassBaseCallback extends BasicCallback<MetaClass>
{
    protected final PropertyContainerContext context;
    protected final String directLinkAttrCode;

    public GetMetaClassBaseCallback(PropertyContainerContext context, String directLinkAttrCode)
    {
        this.context = context;
        this.directLinkAttrCode = directLinkAttrCode;
    }

    @Override
    protected void handleSuccess(MetaClass metaClass)
    {
        Attribute attr = metaClass.getAttribute(directLinkAttrCode);
        context.getContextValues().setProperty(AttributeFormContextValues.DIRECT_LINK_METAINFO, metaClass);
        context.getContextValues().setProperty(AttributeFormContextValues.DIRECT_LINK_ATTRIBUTE, attr);
        context.getContextValues().setProperty(AttributeFormContextValues.RELATED_METAINFO, metaClass);
        boolean isAddForm = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE) == null;
        processEditableProperty(attr, isAddForm, metaClass);
    }

    private void processEditableProperty(Attribute directLinkAttribute, boolean isAddForm, MetaClass metaClass)
    {
        boolean isEditableEnabled = isSystemEditable(directLinkAttribute, metaClass)
                                    && !directLinkAttribute.isDeterminable();
        context.setEnabled(AttributeFormPropertyCode.EDITABLE, isEditableEnabled);
        if (isAddForm)
        {
            boolean isEditable = isEditableEnabled && isEditable(directLinkAttribute, metaClass);
            context.setProperty(AttributeFormPropertyCode.EDITABLE, isEditable);
        }
    }

    private static boolean isEditable(Attribute directLinkAttribute, MetaClass metaClass)
    {
        if (isAggregated(directLinkAttribute) && !isResponsibleAttr(getAggregator(directLinkAttribute, metaClass)))
        {
            return getAggregator(directLinkAttribute, metaClass).isEditable();
        }
        return directLinkAttribute.isEditable();
    }

    private static boolean isSystemEditable(Attribute directLinkAttribute, MetaClass metaClass)
    {
        if (isAggregated(directLinkAttribute) && !isResponsibleAttr(getAggregator(directLinkAttribute, metaClass)))
        {
            return getAggregator(directLinkAttribute, metaClass).isSystemEditable();
        }
        return directLinkAttribute.isSystemEditable();
    }
}