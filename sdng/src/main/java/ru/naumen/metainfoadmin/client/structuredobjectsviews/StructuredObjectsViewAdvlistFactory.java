package ru.naumen.metainfoadmin.client.structuredobjectsviews;

import java.util.ArrayList;

import com.google.inject.Singleton;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;
import ru.naumen.objectlist.shared.AdvListMassOperationLightContext;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Фабрика списка структур
 * <AUTHOR>
 * @since 11.10.2019
 */
@Singleton
public class StructuredObjectsViewAdvlistFactory extends AdminCustomAdvlistFactoryBase
{
    @Inject
    private StructuredObjectsViewsMessages structuredObjectsViewsMessages;

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        ArrayList<ExtendedListActionCellContext> actionColumns = new ArrayList<>();
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.EDIT, StructuredObjectsViewsCommandCode.EDIT,
                AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT)));
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.DEL, StructuredObjectsViewsCommandCode.DELETE,
                AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE)));
        return actionColumns;
    }

    @Override
    protected AdvListMassOperationLightContext createAdvListMassOperationLightContext()
    {
        return massContextWithDelTool(StructuredObjectsViewsCommandCode.DELETE);
    }

    @Override
    protected CustomList createContent(@Nullable Context context, MetaClass metaclass)
    {
        CustomList content = super.createContent(context, metaclass);
        content.setDefaultPageSize(20);
        return content;
    }

    @Override
    protected ToolPanel createToolPanel(CustomList content)
    {
        final ToolPanel panel = super.createToolPanel(content);
        panel.addToolBar(createAddButtonToolBar(structuredObjectsViewsMessages.addStructuredObjectsView()));
        return panel;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return FakeMetaClassesConstants.StructuredObjectsView.FQN;
    }
}
