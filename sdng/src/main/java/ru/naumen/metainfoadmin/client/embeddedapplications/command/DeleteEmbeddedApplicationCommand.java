package ru.naumen.metainfoadmin.client.embeddedapplications.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;

/**
 * <AUTHOR>
 * @since 07.07.2016
 *
 */
public class DeleteEmbeddedApplicationCommand extends ObjectCommandImpl<EmbeddedApplicationAdminSettingsDto, Void>
{
    @Inject
    protected CommonMessages cmessages;
    @Inject
    protected EmbeddedApplicationMessages messages;
    @Inject
    MetainfoModificationServiceAsync metainfoModificationService;

    @Inject
    public DeleteEmbeddedApplicationCommand(@Assisted CommandParam<EmbeddedApplicationAdminSettingsDto, Void> param,
            Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(EmbeddedApplicationAdminSettingsDto value)
    {
        return cmessages.confirmDeleteQuestion(messages.application(), value.getDisplayTitle());
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    protected void onDialogSuccess(CommandParam<EmbeddedApplicationAdminSettingsDto, Void> param)
    {
        metainfoModificationService.deleteApplication(param.getValue().getCode(), param.getCallbackSafe());
    }
}
