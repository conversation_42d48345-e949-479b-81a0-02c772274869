package ru.naumen.metainfoadmin.client.attributes.forms;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.attr.AvailibleTypesProvider;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.forms.info.*;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Реализация {@link AttributeInfoPropertyCreator}
 *
 * <AUTHOR>
 * @since 26 июл. 2018 г.
 */
public class AttributeInfoPropertyCreatorImpl implements AttributeInfoPropertyCreator
{

    @Inject
    private BooleanAttributeInfoPropCreator booleanAttributeInfoPropCreator;

    @Inject
    private LongAttributeInfoPropCreator longAttributeInfoPropCreator;

    @Inject
    private DeterminerAttributeInforPropCreator determinerAttributeInfoPropCreator;

    @Inject
    private ScriptAttributeInfoPropCreator scriptAttributeInfoPropCreator;

    @Inject
    private CatalogAttributeInfoPropCreator catalogAttributeInfoPropCreator;

    @Inject
    private TimerDefinitionInfoPropCreator timerDefinitionInfoPropCreator;

    @Inject
    private DirectLinkAttributeInfoPropCreator directLinkAttributeInfoPropCreator;

    @Inject
    private PresentationAttributeInfoPropCreator presentationAttributeInfoPropCreator;

    @Inject
    private SelectSortAttributeInfoPropCreator selectSortAttributeInfoPropCreator;

    @Inject
    private ComplexRelationAttributeInfoPropCreator complexRelationInfoPropCreator;

    @Inject
    private QuickFormAttributeInfoPropCreator quickFormAttributeInfoPropCreator;

    @Inject
    private DefaultValueAttributeInfoPropCreator defaultValueAttributeInfoPropCreator;

    @Inject
    private CommonAttributeInfoPropCreator commonAttributeInfoPropCreator;

    @Inject
    private PermittedTypesAttributeInfoPropCreator permittedTypesAttributeInfoPropCreator;

    @Inject
    private IntervalAvailableAttributeInfoPropCreator intervalAvalibleAttributeInfoPropCreator;

    @Inject
    private TargetClassAttributeInfoPropCreator targetClassAttributeInfoPropCreator;

    @Inject
    private RelatedObjectAttributeInfoPropCreator relatedObjectAttributeInfoPropCreator;

    @Inject
    private RelatedObjectAttrChainAttributeInfoPropCreator relatedObjectAttrChainAttributeInfoPropCreator;

    @Inject
    private RelatedObjectHierarchyAttributeInfoPropCreator relatedObjectHierarchyAttributeInfoPropCreator;

    @Inject
    private TagsAttributeInfoPropCreator tagsAttributeInfoPropCreator;

    @Inject
    private SettingsSetAttributeInfoPropCreator settingsSetAttributeInfoPropCreator;

    @Inject
    private AvailibleTypesProvider<ObjectFormEdit> typesProvider;

    @Inject
    private DateTimeCommonRestrictionsAttrInfoCreator commonRestrictionsAttrInfoCreator;

    @Inject
    private DateTimeRestrictedByAttributeInfoCreator dateTimeRestrictedByAttributeInfoCreator;

    @Inject
    protected AttributesMessages messages;

    private IProperties propertyValues;

    private ReadyState rs;

    private Map<Integer, Property<?>> createdProperties;

    private Attribute attribute;

    @Override
    public Map<Integer, Property<?>> createProperties(ReadyState rs)
    {
        this.rs = rs;
        createdProperties = new TreeMap<>();
        createPropertiesInt();
        return createdProperties;
    }

    @Override
    public void setAttribute(Attribute attribute)
    {
        this.attribute = attribute;

    }

    @Override
    public void setPropertyValues(IProperties propertyValues)
    {
        this.propertyValues = propertyValues;
    }

    private void createPropertiesInt()
    {
        initCreators();
        propertyValues.propertyNames().stream()
                .filter(code -> propertyValues.getProperty(code) != null)
                .filter(code -> !(propertyValues.getProperty(code) instanceof List && ((List<?>)propertyValues
                        .getProperty(code)).isEmpty()))
                .filter(code -> !(propertyValues.getProperty(code) instanceof String && ((String)propertyValues
                        .getProperty(code)).trim().isEmpty()))
                .forEach(this::createProperty);
        permittedTypesAttributeInfoPropCreator.create();
    }

    private void createProperty(String code)
    {
        switch (code) //NOPMD
        {
            case AttributeFormPropertyCode.INHERIT:
            case AttributeFormPropertyCode.EDITABLE:
            case AttributeFormPropertyCode.EDITABLE_IN_LISTS:
            case AttributeFormPropertyCode.REQUIRED:
            case AttributeFormPropertyCode.REQUIRED_IN_INTERFACE:
            case AttributeFormPropertyCode.UNIQUE:
            case AttributeFormPropertyCode.HIDDEN_WHEN_EMPTY:
            case AttributeFormPropertyCode.HIDDEN_WHEN_NO_POSSIBLE_VALUES:
            case AttributeFormPropertyCode.EDIT_ON_COMPLEX_FORM_ONLY:
            case AttributeFormPropertyCode.NEED_STORE_UNITS:
            case AttributeFormPropertyCode.HAS_GROUP_SEPARATORS:
            case AttributeFormPropertyCode.EXPORT_NDAP:
            case AttributeFormPropertyCode.ADVLIST_SEMANTIC_FILTERING:
            case AttributeFormPropertyCode.HIDE_ARCHIVED:
                booleanAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.DETERMINABLE:
                determinerAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.DEFAULT_BY_SCRIPT:
                scriptAttributeInfoPropCreator.create(AttributeFormPropertyCode.DEFAULT_BY_SCRIPT,
                        AttributeFormPropertyCode.SCRIPT_FOR_DEFAULT);
                break;
            case AttributeFormPropertyCode.FILTERED_BY_SCRIPT:
                scriptAttributeInfoPropCreator.create(AttributeFormPropertyCode.FILTERED_BY_SCRIPT,
                        AttributeFormPropertyCode.SCRIPT_FOR_FILTRATION);
                break;
            case AttributeFormPropertyCode.COMPUTABLE_ON_FORM:
                scriptAttributeInfoPropCreator.create(AttributeFormPropertyCode.COMPUTABLE_ON_FORM,
                        AttributeFormPropertyCode.COMPUTABLE_ON_FORM_SCRIPT);
                break;
            case AttributeFormPropertyCode.COMPUTABLE:
                scriptAttributeInfoPropCreator.create(code, AttributeFormPropertyCode.SCRIPT);
                break;
            case AttributeFormPropertyCode.TARGET_CATALOG:
            case AttributeFormPropertyCode.SUGGEST_CATALOG:
                catalogAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.TARGET_TIMER:
                timerDefinitionInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.DIRECT_LINK_TARGET:
                directLinkAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.TARGET_CLASS:
                targetClassAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.ATTR_TYPE:
                commonAttributeInfoPropCreator.create(code, typesProvider.getAttributeTitle(propertyValues.getProperty(
                        code)));
                break;
            case AttributeFormPropertyCode.COMPOSITE:
                commonAttributeInfoPropCreator.create(code,
                        propertyValues.getProperty(AttributeFormPropertyCode.TEMPLATE));
                break;
            case AttributeFormPropertyCode.CODE:
            case AttributeFormPropertyCode.TITLE:
            case AttributeFormPropertyCode.DESCRIPTION:
            case AttributeFormPropertyCode.AGGREGATE_ATTRIBUTES:
                commonAttributeInfoPropCreator.create(code, propertyValues.getProperty(code));
                break;
            case AttributeFormPropertyCode.USE_GEN_RULE:
                commonAttributeInfoPropCreator.create(code,
                        propertyValues.getProperty(AttributeFormPropertyCode.GEN_RULE));
                break;
            case AttributeFormPropertyCode.DEFAULT_VALUE:
                defaultValueAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.SHOW_PRS:
            case AttributeFormPropertyCode.EDIT_PRS:
                presentationAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.SELECT_SORTING:
                selectSortAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.COMPLEX_RELATION:
                complexRelationInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.QUICK_ADD_FORM_CODE:
            case AttributeFormPropertyCode.QUICK_EDIT_FORM_CODE:
                quickFormAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.INTERVAL_AVAILABLE_UNITS:
                intervalAvalibleAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.ATTR_CHAIN:
                relatedObjectAttrChainAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE:
                relatedObjectAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.RELATED_OBJECT_HIERARCHY_LEVEL:
                relatedObjectHierarchyAttributeInfoPropCreator.create(code);
                break;
            case AttributeFormPropertyCode.DATE_TIME_COMMON_RESTRICTIONS:
                commonRestrictionsAttrInfoCreator.create(code);
                break;
            case AttributeFormPropertyCode.DATE_TIME_RESTRICTION_TYPE:
                scriptAttributeInfoPropCreator.create(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_TYPE,
                        AttributeFormPropertyCode.DATE_TIME_RESTRICTION_SCRIPT);
                break;
            case AttributeFormPropertyCode.DATE_TIME_RESTRICTION_ATTRIBUTE:
                dateTimeRestrictedByAttributeInfoCreator.create(
                        AttributeFormPropertyCode.DATE_TIME_RESTRICTION_ATTRIBUTE);
                break;
            case AttributeFormPropertyCode.TAGS:
                tagsAttributeInfoPropCreator.create(AttributeFormPropertyCode.TAGS);
                break;
            case AttributeFormPropertyCode.DIGITS_COUNT_RESTRICTION:
                longAttributeInfoPropCreator.create(code, propertyValues.getProperty(code));
                break;
            case AttributeFormPropertyCode.SETTINGS_SET:
                settingsSetAttributeInfoPropCreator.create(code);
                break;
        }
    }

    private void initCreators()
    {
        booleanAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        longAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        commonAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        catalogAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        timerDefinitionInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        targetClassAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        selectSortAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        scriptAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        quickFormAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        presentationAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        permittedTypesAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        intervalAvalibleAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        directLinkAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        determinerAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        complexRelationInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        defaultValueAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        relatedObjectAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        relatedObjectAttrChainAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        relatedObjectHierarchyAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        commonRestrictionsAttrInfoCreator.init(createdProperties, propertyValues, attribute, rs);
        dateTimeRestrictedByAttributeInfoCreator.init(createdProperties, propertyValues, attribute, rs);
        tagsAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
        settingsSetAttributeInfoPropCreator.init(createdProperties, propertyValues, attribute, rs);
    }
}
