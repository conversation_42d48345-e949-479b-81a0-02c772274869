package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determine;

import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_CODE;
import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_IS_FOLDER;
import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_TITLE;
import static ru.naumen.core.shared.Constants.ValueMapCatalogItem.*;
import static ru.naumen.core.shared.filters.Filters.*;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import com.google.common.collect.Iterables;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.ObjectService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.criteria.Order;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

/**
 * <AUTHOR>
 */
public class DeterminerBindDelegateImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<SelectItem, ListBoxWithEmptyOptProperty>
        implements AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxWithEmptyOptProperty>
{
    private class GetVMapItemsCallback extends BasicCallback<List<DtObject>>
    {
        private final PropertyContainerContext context;
        private final ListBoxWithEmptyOptProperty property;
        private final Predicate<ClassFqn> fqnFilter;
        private final AsyncCallback<Void> callback;

        public GetVMapItemsCallback(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
                Predicate<ClassFqn> fqnFilter, AsyncCallback<Void> callback)
        {
            this.context = context;
            this.property = property;
            this.fqnFilter = fqnFilter;
            this.callback = callback;
        }

        @Override
        protected void handleSuccess(List<DtObject> items)
        {
            Attribute attr = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
            for (DtObject item : items)
            {
                boolean isFolder = item.<Boolean> getProperty(ITEM_IS_FOLDER);
                boolean matchFqn = false;
                if (!isFolder)
                {
                    matchFqn = Iterables.any(item.<Collection<DtObject>> getProperty(LINKED_CLASSES),
                            Predicates.compose(fqnFilter, DtObject.FQN_EXTRACTOR));
                }
                //@formatter:off
                if (isFolder 
                    || 
                    matchFqn && item.<Collection<String>> getProperty(TARGET_ATTRS).contains(attr.getCode()))
                //@formatter:on
                {
                    property.getValueWidget().addItem(item.getTitle(), item.<String> getProperty(ITEM_CODE), true,
                            !isFolder, item.<Integer> getProperty(Constants.TREE_LEVEL));
                }
            }
            callback.onSuccess(null);
        }
    }

    @Inject
    ObjectService objectService;
    @Inject
    MetainfoServiceAsync metainfoService;

    @Override
    public void bindProperty(final PropertyContainerContext context, final ListBoxWithEmptyOptProperty property,
            final AsyncCallback<Void> callback)
    {
        Attribute attr = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        if (attr == null)
        {
            callback.onSuccess(null);
            return;
        }
        final ClassFqn fqn = context.getContextValues().<MetaClass> getProperty(AttributeFormContextValues.METAINFO)
                .getFqn();
        List<ClassFqn> fqns = Arrays.asList(fqn, fqn.fqnOfClass());
        //@formatter:off
        DtoCriteria criteria = new DtoCriteria(FQN)
            .addFilters(removed(false),
                or(eq(ITEM_IS_FOLDER, true),
                    and(eq(ITEM_IS_FOLDER, false),
                        or(eq(TYPE, null), not(eq(TYPE, ESCALATION_TYPE))),
                        fqnHierarhyDescendants(LINKED_CLASS, fqn.fqnOfClass(), true)
                    )
                )
            )
            .setProperties(ITEM_CODE, ITEM_TITLE, TARGET_ATTRS, LINKED_CLASSES, ITEM_IS_FOLDER)
            .addOrders(new Order(ITEM_TITLE))
            .treeSort(true);
        //@formatter:on
        objectService.getObjects(criteria, new GetVMapItemsCallback(context, property, Predicates.in(fqns), callback));
    }
}
