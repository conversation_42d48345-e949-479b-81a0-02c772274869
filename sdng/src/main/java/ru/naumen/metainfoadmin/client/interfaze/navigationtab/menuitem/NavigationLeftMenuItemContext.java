package ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;

/**
 * Контекст отображения карточки элемента левого меню
 *
 * <AUTHOR>
 * @since 30.07.2020
 */
public class NavigationLeftMenuItemContext extends NavigationMenuItemContext<LeftMenuItemSettingsDTO>
{
    public NavigationLeftMenuItemContext(LeftMenuItemSettingsDTO menuItem, DtoContainer<NavigationSettings> settings)
    {
        super(menuItem, settings);
    }
}