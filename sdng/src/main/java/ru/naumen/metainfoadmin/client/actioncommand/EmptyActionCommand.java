package ru.naumen.metainfoadmin.client.actioncommand;

import ru.naumen.core.client.actionbar.ActionCommand;
import ru.naumen.core.client.actionbar.ActionCommandBase;
import ru.naumen.core.client.content.Context;

/**
 * Реализация пустой команды {@link ActionCommand} для интерфейса администратора
 *
 * <AUTHOR>
 * @since 02.12.2010
 */
public class EmptyActionCommand extends ActionCommandBase<Context>
{

    /**
     *
     */
    public EmptyActionCommand()
    {
        super(false);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected void executeInt()
    {
        // Ничего не делаем
    }

}
