package ru.naumen.metainfoadmin.client.scheduler.command;

import java.util.Collection;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskMessages;

/**
 * Команда удаления задачи планировщика.
 * <AUTHOR>
 */
public class DeleteSchTaskCommand extends ObjectCommandImpl<Collection<DtObject>, Void>
{
    @Inject
    protected MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    protected SchedulerTaskMessages messages;
    @Inject
    protected CommonMessages cmessages;

    @Inject
    public DeleteSchTaskCommand(@Assisted CommandParam<Collection<DtObject>, Void> param, Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    public void execute(CommandParam<Collection<DtObject>, Void> param)
    {
        param.getValue().forEach(task -> LOG.finest("Removing schedulerTask: " + task.getUUID()));
        super.execute(param);
    }

    @Override
    protected String getDialogMessage(Collection<DtObject> values)
    {
        if (values.size() == 1)
        {
            return cmessages.confirmDeleteQuestion(messages.theSchedulerTask(), values.iterator().next().getTitle());
        }
        return cmessages.confirmDeleteQuestion2(messages.schedulerTasksSelected());
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    protected void onDialogSuccess(CommandParam<Collection<DtObject>, Void> param)
    {
        metainfoModificationService.deleteSchedulerTask(
                param.getValue().stream().map(DtObject::getUUID).collect(Collectors.toList()),
                param.getCallbackSafe());
    }

}
