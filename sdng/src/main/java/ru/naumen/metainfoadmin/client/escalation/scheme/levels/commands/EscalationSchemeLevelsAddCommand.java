package ru.naumen.metainfoadmin.client.escalation.scheme.levels.commands;

import jakarta.inject.Inject;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemeContext;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.AddEscalationSchemeLevelForm;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinjector.EscalationSchemeLevelFormFactory;

import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 21.08.2012
 *
 */
public class EscalationSchemeLevelsAddCommand implements ClosureCommand<EscalationSchemeContext>
{
    private final AddEscalationSchemeLevelForm addForm;

    @Inject
    public EscalationSchemeLevelsAddCommand(@Assisted EscalationSchemeContext context,
            EscalationSchemeLevelFormFactory<AddEscalationSchemeLevelForm> formFactory)
    {
        this.addForm = formFactory.create(context.getLocalEventBus(), context.getEscalationScheme().get());
    }

    @Override
    public void execute()
    {
        addForm.bind();
    }

    @Override
    public void execute(EscalationSchemeContext object)
    {
    }

    @Override
    public boolean isPossible(Object object)
    {
        return true;
    }
}
