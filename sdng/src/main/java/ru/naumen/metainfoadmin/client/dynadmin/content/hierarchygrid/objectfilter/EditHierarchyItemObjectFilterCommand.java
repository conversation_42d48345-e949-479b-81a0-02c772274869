package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.objectfilter;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.condition.FilterFormPresenter;
import ru.naumen.core.client.widgets.properties.condition.FilterFormPresenterFactory;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filtersettings.FilterSettingsMessages;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.shared.dynadmin.HierarchyItemSettingsContext;
import ru.naumen.objectlist.client.mode.active.extended.advlist.filter.ListFilterConditionTextFactory;

/**
 * Команда настройки ограничения содержимого для уровня иерархического списка.
 * <AUTHOR>
 * @since Jan 17, 2020
 */
public class EditHierarchyItemObjectFilterCommand
        extends BaseCommandImpl<HierarchyItemSettingsContext, HierarchyItemSettingsContext>
{
    private final MetainfoModificationServiceAsync metainfoServiceAsync;
    private final FilterFormPresenterFactory filterFormPresenterFactory;
    private final Provider<ListFilterConditionTextFactory> conditionTextFactoryProvider;
    private final FilterSettingsMessages messages;
    private final CommonMessages commonMessages;
    private final TagsMessages tagsMessages;

    @Inject
    public EditHierarchyItemObjectFilterCommand(@Assisted HierarchyObjectFilterCommandParam param,
            MetainfoModificationServiceAsync metainfoServiceAsync,
            FilterFormPresenterFactory filterFormPresenterFactory,
            Provider<ListFilterConditionTextFactory> conditionTextFactoryProvider,
            FilterSettingsMessages messages, CommonMessages commonMessages, TagsMessages tagsMessages)
    {
        super(param);
        this.metainfoServiceAsync = metainfoServiceAsync;
        this.filterFormPresenterFactory = filterFormPresenterFactory;
        this.conditionTextFactoryProvider = conditionTextFactoryProvider;
        this.messages = messages;
        this.commonMessages = commonMessages;
        this.tagsMessages = tagsMessages;
    }

    @Override
    public void execute(CommandParam<HierarchyItemSettingsContext, HierarchyItemSettingsContext> param)
    {
        HierarchyObjectFilterCommandParam commandParam = (HierarchyObjectFilterCommandParam)getParam();
        String itemCode = param.getValue().getCode();
        ListFilter currentFilter = param.getValue().getObjectFilter();

        ObjectFilterContext filterContext = new ObjectFilterContext(commandParam.getContext(), param.getValue());
        FilterFormPresenter filterFrom = filterFormPresenterFactory.create(filterContext, messages.editObjectFilter(),
                "hierarchyObjectFilterFrom", ObjectUtils.clone(currentFilter), new BasicCallback<ListFilter>()
                {
                    @Override
                    protected void handleSuccess(ListFilter value)
                    {
                        value.removeEmptyElements();
                        commandParam.getContent().getObjectFilters().put(itemCode, value);
                        metainfoServiceAsync.saveUI(
                                commandParam.getContext().getMetainfo().getFqn(),
                                commandParam.getContext().getRootContent(),
                                null,
                                commandParam.getContext().getCode(),
                                commandParam.getContent(),
                                false, new BasicCallback<Void>()
                                {
                                    @Override
                                    protected void handleFailure(Throwable t)
                                    {
                                        if (!currentFilter.getElements().isEmpty())
                                        {
                                            commandParam.getContent().getObjectFilters()
                                                    .put(itemCode, currentFilter);
                                        }
                                        else
                                        {
                                            commandParam.getContent().getObjectFilters().remove(itemCode);
                                        }
                                        super.handleFailure(t);
                                    }

                                    @Override
                                    protected void handleSuccess(Void fake)
                                    {
                                        param.getValue().setObjectFilter(value);
                                        param.getCallback().onSuccess(param.getValue());
                                    }
                                });
                    }
                });
        if (!param.getValue().getItemObjectFilter().getElements().isEmpty())
        {
            ListFilterConditionTextFactory conditionTextFactory = conditionTextFactoryProvider.get();
            conditionTextFactory.setAttributes(param.getValue().getAttributes());
            filterFrom.getDisplay().getAttention().setTitle(commonMessages.attention());
            filterFrom.getDisplay()
                    .getAttention()
                    .setHTML(messages.objectFilterAlreadyDefined( // NOSONAR NOPMD NSDPRD-28509 unsafe html
                            conditionTextFactory.create(param.getValue().getItemObjectFilter()).asString()));
        }
        filterFrom.getDisplay()
                .getAttention()
                .setHTML(
                        tagsMessages.disabledInRestrictionContentStructureElement()); // NOSONAR NOPMD NSDPRD-28509
        // unsafe html
        filterFrom.getDisplay().getAttention().setVisible(false);
        filterFrom.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}
