package ru.naumen.metainfoadmin.client.scheduler;

import jakarta.inject.Inject;

import ru.naumen.core.client.CellTableWithRowsId;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfoadmin.client.CatalogCellTableResources;
import ru.naumen.metainfoadmin.client.TableDisplayImpl;

/**
 * Дисплей для списка триггеров
 * <AUTHOR>
 * @since 10.06.2011
 *
 */
public class TriggersDisplay extends TableDisplayImpl<DtoContainer<Trigger>>
{
    @Inject
    public TriggersDisplay(CellTableWithRowsId<DtoContainer<Trigger>, CatalogCellTableResources> cellTable)
    {
        super(cellTable);
        table.asWidget().ensureDebugId("triggersTable");
    }
}
