package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.editable;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.ArrayList;
import java.util.Arrays;

import com.google.common.collect.Lists;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * Делегат изменения значения свойства "Редактируемый" - включает или выключает свойства "Обязательный", 
 * "Уникальный", "Редактируемый в списках"
 * <AUTHOR>
 * @since 17.05.2012
 */
public class EditableVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        Boolean editable = context.getPropertyValues().getProperty(EDITABLE);
        context.setPropertyEnabled(UNIQUE, editable);

        if (!Boolean.TRUE.equals(editable))
        {
            context.setProperty(COMPUTABLE_ON_FORM, false);
            context.setProperty(HIDDEN_WHEN_NO_POSSIBLE_VALUES, false);
            context.setProperty(QUICK_ADD_FORM_CODE, null);
            context.setProperty(QUICK_EDIT_FORM_CODE, null);
            context.setProperty(DATE_TIME_COMMON_RESTRICTIONS, new ArrayList<>());
            context.setProperty(DATE_TIME_RESTRICTION_TYPE, null);
            context.setProperty(DATE_TIME_RESTRICTION_ATTRIBUTE, null);
            context.setProperty(DATE_TIME_RESTRICTION_CONDITION, null);
        }

        String attrType = context.getPropertyValues().getProperty(ATTR_TYPE);
        if (Constants.EDITABLE_IN_LISTS_ATTRIBUTE_TYPES.contains(attrType) && !ResponsibleAttributeType.CODE.equals(
                attrType))
        {
            Boolean inherited = context.getPropertyValues().getProperty(INHERIT);
            context.setProperty(EDITABLE_IN_LISTS, false);
            context.setPropertyEnabled(EDITABLE_IN_LISTS, editable && !Boolean.TRUE.equals(inherited));
        }

        context.getRefreshProcess().startCustomProcess(
                Lists.newArrayList(COMPUTABLE_ON_FORM, EDIT_PRS, COMPLEX_RELATION, COMPLEX_RELATION_ATTR_GROUP,
                        DATE_TIME_COMMON_RESTRICTIONS, DATE_TIME_RESTRICTION_TYPE,
                        COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();

        if (context.getPropertyControllers().get(HIDDEN_WHEN_NO_POSSIBLE_VALUES) != null)
        {
            context.getRefreshProcess().startCustomProcess(Arrays.asList(HIDDEN_WHEN_NO_POSSIBLE_VALUES));
            context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
        }
        if (context.getPropertyControllers().get(QUICK_ADD_FORM_CODE) != null)
        {
            context.getRefreshProcess().startCustomProcess(Arrays.asList(QUICK_ADD_FORM_CODE));
            context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
        }
        if (context.getPropertyControllers().get(QUICK_EDIT_FORM_CODE) != null)
        {
            context.getRefreshProcess().startCustomProcess(Arrays.asList(QUICK_EDIT_FORM_CODE));
            context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
        }
    }
}
