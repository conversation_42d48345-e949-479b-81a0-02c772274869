package ru.naumen.metainfoadmin.client.scheduler;

import java.util.Map;

import ru.naumen.core.shared.ITitled;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.SchedulerTaskCreator;

/**
 * Фабрика и реестр типов задач планировщика и компонентов зависящих от этих типов.
 *
 * <AUTHOR>
 *
 */
public interface SchedulerTaskTypeFactory
{
    /**
     * Фабрика создающая конкретный {@link SchedulerTaskCreator}.
     */
    interface SchedulerTaskType<T extends SchedulerTask> extends ITitled
    {
        SchedulerTaskCreator create();

        SchedulerTaskInfoPresenterBase<T> createTaskInfoPresenter();

        SchedulerTaskPresenter createTaskPresenter();
    }

    /**
     * Создает {@link SchedulerTaskCreator} для задачи планировщика с заданным типом.
     * <p>
     * @param type идентификатор типа задачи планировщика
     * @return
     */
    SchedulerTaskCreator getCreator(String type);

    /**
     * Возвращает коллекцию всех доступных задач планировщика для создания (точнее их описании в фабрике).
     */
    Map<String, SchedulerTaskType<?>> getSchedulerTaskTypes();

    /**
     * Создает {@link SchedulerTaskInfoPresenterBase} для задачи планировщика с заданным типом.
     * <p>
     * @param <T> идентификатор типа задачи планировщика
     * @return
     */
    <T extends SchedulerTask> SchedulerTaskInfoPresenterBase<T> getTaskInfoPresenter(T task);

    /**
     * Создает {@link SchedulerTaskPresenter} для задачи планировщика с заданным типом.
     * <p>
     * @param type идентификатор типа задачи планировщика
     * @return
     */
    SchedulerTaskPresenter getTaskPresenter(String type);

    String getTypeTitle(String code);

    /**
     * Регистрирует тип задачи планировщика
     * @param code код типа
     * @param type реализация типа
     */
    void registerSchedulerTaskType(String code, SchedulerTaskType<?> type);
}
