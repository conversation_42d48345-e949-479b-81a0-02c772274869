package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.Relation;
import ru.naumen.metainfo.shared.filters.RelationFilters;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.metainfoadmin.shared.dynadmin.content.objectlist.relobjectlist.PossibleLinkAttributeFilter;

/**
 * Делегат обновления свойства "В иерархии потомки связаны с предками через атрибут"
 *
 * <AUTHOR>
 * @since 03.11.2020
 */
public class LinkToContentNestedAttrLinkRefreshDelegateImpl implements PropertyDelegateRefresh<SelectItem,
        ListBoxWithEmptyOptProperty>
{
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Boolean> callback)
    {
        final Boolean showHierarchy = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.SHOW_HIERARCHY);

        boolean isVisible =
                LinkToContentMetaClassPropertiesProcessor.getRelObjectListFragmentVisibility(context)
                && Boolean.TRUE.equals(showHierarchy);

        callback.onSuccess(isVisible);

        if (!isVisible)
        {
            context.getPropertyControllers().get(MenuItemLinkToContentCode.NESTED_ATTR_LINK).unbindValidators();
            return;
        }

        RelationsAttrTreeObject beforeHierarchyAttr = context.getPropertyValues().<RelationsAttrTreeObject> getProperty(
                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR);

        Attribute attrBeforeHierarchy = beforeHierarchyAttr == null ? null : beforeHierarchyAttr.getAttribute();
        if ((beforeHierarchyAttr == null) || (attrBeforeHierarchy == null))
        {
            property.setValue(null);
            return;
        }
        String hierarchyValue = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.NESTED_ATTR_LINK);

        ClassFqn hierarchyFqn = attrBeforeHierarchy.getType().<ObjectAttributeType> cast().getRelatedMetaClass();

        metainfoService.getMetaClass(hierarchyFqn, new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass metaClass)
            {
                property.getValueWidget().clear();
                property.getValueWidget().refreshPopupCellList();

                List<Attribute> possibleLinkAttributes = metaClass.getAttributes()
                        .stream()
                        .filter(new PossibleLinkAttributeFilter(metaClass.getFqn()))
                        .sorted(ITitled.COMPARATOR)
                        .collect(Collectors.toList());

                for (Attribute possibleLinkAttribute : possibleLinkAttributes)
                {
                    property.getValueWidget().addItem(possibleLinkAttribute.getTitle(),
                            AttributeFqn.toString(metaClass.getFqn(), possibleLinkAttribute.getCode()));
                }
                Collection<Relation> parentRelations = metaClass.getOutgoingRelation()
                        .stream()
                        .filter(RelationFilters.isParent().and(RelationFilters.isSelf()))
                        .collect(Collectors.toList());
                if (hierarchyValue != null)
                {
                    property.trySetObjValue(hierarchyValue);
                    context.getPropertyValues().setProperty(MenuItemLinkToContentCode.NESTED_ATTR_LINK, hierarchyValue);
                }
                else if (!parentRelations.isEmpty())
                {
                    property.trySetObjValue(AttributeFqn.toString(metaClass.getFqn(), Constants.PARENT_ATTR));
                    context.getPropertyValues()
                            .setProperty(MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                    AttributeFqn.toString(metaClass.getFqn(), Constants.PARENT_ATTR));
                }
                else
                {
                    property.setValue(null);
                }
            }
        });
    }
}
