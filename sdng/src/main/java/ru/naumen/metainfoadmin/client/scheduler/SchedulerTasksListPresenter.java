package ru.naumen.metainfoadmin.client.scheduler;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.SCHEDULER;

import java.util.Set;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Provider;

import jakarta.inject.Inject;
import ru.naumen.admin.client.advlists.AdminAdvListPresenterBase;
import ru.naumen.core.client.DisplayHolder;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.filters.SimpleFilter;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.mailreader.shared.task.ReceiveMailTask;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfoadmin.client.scheduler.command.SchedulerCommandCode;
import ru.naumen.metainfoadmin.client.scheduler.forms.AddSchedulerTaskFormPresenter;
import ru.naumen.objectlist.client.AddMetainfoObjectEvent;
import ru.naumen.objectlist.client.AddMetainfoObjectHandler;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionEvent;

/**
 * Презентер списка задач планировщика
 * <AUTHOR>
 * @since 23.10.2017
 */
public class SchedulerTasksListPresenter extends AdminAdvListPresenterBase<SchedulerTask>
{
    private class AddSchedulerTaskHandler implements AddMetainfoObjectHandler
    {
        @Override
        public void onAddMetainfoObject(AddMetainfoObjectEvent event)
        {
            AddSchedulerTaskFormPresenter presenter = formProvider.get();
            presenter.init(null, saveCallback);
            presenter.bind();
        }
    }

    protected AsyncCallback<DtoContainer<SchedulerTask>> saveCallback =
            new OnStartBasicCallback<DtoContainer<SchedulerTask>>()
            {
                @Override
                protected void handleSuccess(DtoContainer<SchedulerTask> value)
                {
                    placeController.goTo(new SchedulerTaskPlace(value));
                }
            };

    protected final SchedulerTaskTypeFactory factory;
    private final SchedulerTasksPresenterSettings settings;
    private final Provider<AddSchedulerTaskFormPresenter> formProvider;
    private final PlaceController placeController;

    @Inject
    public SchedulerTasksListPresenter(DisplayHolder display,
            EventBus eventBus,
            CommandFactory commandFactory,
            SchedulerTasksAdvlistFactory advlistFactory,
            SchedulerTaskTypeFactory factory,
            SchedulerTasksPresenterSettings settings,
            Provider<AddSchedulerTaskFormPresenter> formProvider,
            PlaceController placeController)
    {
        super(display, eventBus, commandFactory, advlistFactory);
        this.factory = factory;
        this.settings = settings;
        this.formProvider = formProvider;
        this.placeController = placeController;
    }

    @Override
    protected Set<String> getActionCommands()
    {
        return SchedulerCommandCode.COMMANDS_IN_LIST;
    }

    @Override
    protected AddMetainfoObjectHandler getAddMetainfoObjectHandler()
    {
        return new AddSchedulerTaskHandler();
    }

    @Override
    protected Object getContextValue(ObjectListActionEvent event)
    {
        if (event.getAction().equals(SchedulerCommandCode.DELETE_SCH_TASK))
        {
            return event.getValues();
        }
        else
        {
            DtObject dto = event.getValue();
            SchedulerTask task = dto.getProperty(OBJECT);
            DtoContainer<SchedulerTask> container = new DtoContainer<>(task);
            container.setProperty(Constants.Tag.IS_ELEMENT_ENABLED, dto.getProperty(Constants.Tag.IS_ELEMENT_ENABLED));
            return container;
        }
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        if (settings.getTypes() != null)
        {
            SimpleFilter<String> filter = new SimpleFilter<>(
                    FakeMetaClassesConstants.SchedulerTask.Attributes.ATTR_TYPE.toString(), ReceiveMailTask.NAME);
            listPresenter.getContent().getCriteria().addFilters(filter);
        }
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return SCHEDULER;
    }
}
