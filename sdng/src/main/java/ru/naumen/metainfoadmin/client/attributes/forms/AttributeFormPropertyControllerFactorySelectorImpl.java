package ru.naumen.metainfoadmin.client.attributes.forms;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySelectorImpl;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Фабрика делегирует создание свойства либо обычной syncPropsFactory, либо фабрике агрегирующих свойств
 * aggregatedFactory
 * <AUTHOR>
 * @since 15.05.2012
 */
public class AttributeFormPropertyControllerFactorySelectorImpl<F extends ObjectForm>
        extends PropertyControllerFactorySelectorImpl<Attribute, F>
{
    private final PropertyControllerFactory<Attribute, F> aggregatedFactory;
    private final PropertyControllerFactory<Attribute, F> syncPropsFactory;

    @Inject
    public AttributeFormPropertyControllerFactorySelectorImpl(
            AbstractFormPropertyControllerFactorySyncImpl<F> syncPropsFactory,
            AttributeFormAggregatedFactoryFactory<F> aggregatedFactoryFactory)
    {
        this.syncPropsFactory = syncPropsFactory;
        this.aggregatedFactory = aggregatedFactoryFactory.create(this);
    }

    @Override
    protected void build()
    {
        register(syncPropsFactory);
        register(aggregatedFactory);
    }
}
