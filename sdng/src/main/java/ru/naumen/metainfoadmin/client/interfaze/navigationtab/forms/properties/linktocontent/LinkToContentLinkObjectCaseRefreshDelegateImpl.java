package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import static ru.naumen.metainfo.shared.Constants.LinkObjectType.OBJECT_LINKED_TO_CURRENT_USER;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.ui.ObjectList;

/**
 * Делегат обновления свойства "Тип объекта связи"
 *
 * <AUTHOR>
 * @since 06.11.2020
 */
@Singleton
public class LinkToContentLinkObjectCaseRefreshDelegateImpl implements PropertyDelegateRefresh<SelectItem,
        ListBoxWithEmptyOptProperty>
{
    @Inject
    protected MetainfoServiceAsync metainfoService;

    @Inject
    protected MetainfoUtils metainfoUtils;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean isLinkToContentType = LinkToContentMetaClassPropertiesProcessor.isLinkToContentElementType(context);

        String contentTypeStr = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);

        String linkObject = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT);

        context.getPropertyControllers().get(MenuItemLinkToContentCode.LINK_OBJECT_ATTR).refresh();

        if (!isLinkToContentType || ObjectList.class.getSimpleName().equals(contentTypeStr)
            || !OBJECT_LINKED_TO_CURRENT_USER.equals(linkObject))
        {
            callback.onSuccess(false);
            return;
        }

        metainfoService.getDescendantClasses(Employee.FQN, false, new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> casesList)
            {
                property.getValueWidget().clear();
                property.getValueWidget().refreshPopupCellList();

                metainfoUtils.sort(casesList);
                for (MetaClassLite clz : metainfoUtils.getPossible(casesList, true))
                {
                    property.getValueWidget().addItem(clz.getTitle(),
                            clz.getFqn().toString());
                }

                callback.onSuccess(true);
            }
        });
    }
}
