package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import jakarta.annotation.Nullable;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

/**
 * <AUTHOR>
 * @since 30 сент. 2013 г.
 */
public class NavigationSettingsTMCommandParam extends NavigationSettingsMenuItemAbstractCommandParam<MenuItem>
{
    public NavigationSettingsTMCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, (MenuItem)null, callback);
    }

    public NavigationSettingsTMCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable FactoryParam.ValueSource<MenuItem> valueSource,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, valueSource, callback);
    }

    public NavigationSettingsTMCommandParam(DtoContainer<NavigationSettings> settings, @Nullable MenuItem value,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, value, callback);
    }

    @Override
    @SuppressWarnings("unchecked")
    public NavigationSettingsTMCommandParam cloneIt()
    {
        return new NavigationSettingsTMCommandParam(getSettings(), getValueSource(), getCallback());
    }

    public List<MenuItem> getSiblings(MenuItem item)
    {
        return item.getParent() != null ? item.getParent().getChildren() : getSettings().get().getTopMenuItems();
    }

    @Override
    public List<MenuItem> getMenuItems()
    {
        return getSettings().get().getTopMenuItems();
    }

    @Override
    public Map<String, LinkedList<String>> getMenuItemPaths()
    {
        return getSettings().get().getTopMenuItemPaths();
    }
}
