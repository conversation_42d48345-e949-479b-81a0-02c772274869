package ru.naumen.metainfoadmin.client.scheduler.forms.creators;

import java.util.Date;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.validation.AfterCurrentDateValidator;
import ru.naumen.core.client.validation.DateTimeValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.datepicker.HasTimeRoundingMode;
import ru.naumen.core.client.widgets.datepicker.HasTimeRoundingMode.TimeRoundingMode;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.scheduler.ConcreteDateTrigger;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskMessages;

/**
 * <AUTHOR>
 * @since 31.05.2011
 *
 */
public class ConcreteDateTriggerCreator extends TriggerCreatorBase implements TriggerCreator
{
    @Inject
    Processor validation;
    @Inject
    private AfterCurrentDateValidator afterCurrentDateValidator;
    @Inject
    private DateTimeValidator dateTimeValidator;
    @Inject
    SchedulerTaskMessages messages;
    @Inject
    Formatters formatters;
    @Inject
    @Named(PropertiesGinModule.DATE_TIME)
    Property<Date> executionDate;
    String schTaskCode;

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {
        add(validation.validate(executionDate, dateTimeValidator));
        add(validation.validate(executionDate, afterCurrentDateValidator));
    }

    @Override
    public void bindProperties()
    {
        executionDate.setCaption(messages.executionDate());
        executionDate.setValidationMarker(true);
        ((HasTimeRoundingMode)executionDate.getValueWidget()).setTimeRoundingMode(TimeRoundingMode.START_OF_MINUTE);
        add(executionDate);
        if (null != trigger)
        {
            executionDate.setValue(((ConcreteDateTrigger)trigger).getExecutionDate());
        }
        DebugIdBuilder.ensureDebugId(executionDate, "executionDate");
        addRandomizeDelay(messages.randomizeDelay());
    }

    @Override
    public DtoContainer<Trigger> getTrigger()
    {
        validation.validateAsync(executionDate);
        ConcreteDateTrigger newTrigger = ((ConcreteDateTrigger)trigger);
        if (null == newTrigger)
        {
            newTrigger = new ConcreteDateTrigger();
        }
        newTrigger.setExecutionDate(executionDate.getValue());
        newTrigger.setTitle(messages.executeAtConcreteDate(formatters.formatDateTime(executionDate.getValue())));
        newTrigger.setSchTaskCode(schTaskCode);
        newTrigger.setRandomizeDelayOn(sharedSettings.useRandomizeDelay() && randomizeDelayOn.getValue());
        return new DtoContainer<>(newTrigger);
    }

    @Override
    public void init(String schTaskCode, @Nullable DtoContainer<Trigger> trigger)
    {
        this.schTaskCode = schTaskCode;
        this.trigger = trigger == null ? null : trigger.get();
    }
}
