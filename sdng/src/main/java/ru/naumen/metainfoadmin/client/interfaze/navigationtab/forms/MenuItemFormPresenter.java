package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.ObjectFormMessages;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector.PropertyContainerPresenterFactory;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.common.objectcommands.form.ObjectFormAfterBindHandler;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemContextValueCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * <AUTHOR>
 * @since 30 сент. 2013 г.
 */
public abstract class MenuItemFormPresenter<F extends ObjectForm, M extends IMenuItem> extends OkCancelPresenter<PropertyFormDisplay>
{
    @Inject
    protected NavigationSettingsMessages messages;
    @Inject
    private PropertyContainerPresenterFactory containerFactory;
    @Inject
    private ObjectFormMessages<F> formMessages;
    @Inject
    private Processor validation;
    @Inject
    protected I18nUtil i18nUtil;
    @Inject
    protected DispatchAsync dispatch;
    @Inject
    protected AdminMetainfoServiceAsync metainfoService;

    SimpleResultCallbackDecorator<DtoContainer<NavigationSettings>> simpleResultCallback;
    PropertyContainerPresenter propertyContainer;
    NavigationSettings settings;
    M menuItem;
    boolean isNew;

    private final PropertyControllerFactory<M, ObjectFormEdit> propertyControllerFactory;
    private final ObjectFormAfterBindHandler<F, M> afterBindHandler;

    protected final IProperties contextProps = new MapProperties();
    protected final IProperties propertyValues = new MapProperties();

    public MenuItemFormPresenter(PropertyFormDisplay display, EventBus eventBus,
            ObjectFormAfterBindHandler<F, M> afterBindHandler,
            PropertyControllerFactory<M, ObjectFormEdit> propertyControllerFactory)
    {
        super(display, eventBus);
        this.afterBindHandler = afterBindHandler;
        this.propertyControllerFactory = propertyControllerFactory;
    }

    public void init(NavigationSettings settings, @Nullable M value,
            AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        this.settings = settings;
        this.menuItem = value;
        simpleResultCallback = new SimpleResultCallbackDecorator<DtoContainer<NavigationSettings>>(callback)
        {
            @Override
            public void onFailure(Throwable caught)
            {
                if (isNew)
                {
                    menuItem = null;
                }
                super.onFailure(caught);
            }

            @Override
            public void onSuccess(SimpleResult<DtoContainer<NavigationSettings>> result)
            {
                super.onSuccess(result);
                eventBus.fireEvent(new MenuItemChangedEvent(result.get(), menuItem.getCode()));
                getDisplay().destroy();
            }
        };
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        super.onApply();
        isNew = menuItem == null;
        if (isNew)
        {
            menuItem = getNewMenuItem();
            menuItem.setCode(UUIDGenerator.get().nextUUID());
        }
        fillMenuItem();
        Action<SimpleResult<DtoContainer<NavigationSettings>>> action = getEditNavigationMenuItemAction(menuItem);
        doExecuteAction(action);
    }

    protected void doExecuteAction(Action<SimpleResult<DtoContainer<NavigationSettings>>> action)
    {
        dispatch.execute(action, simpleResultCallback);
    }

    protected abstract Map<String, LinkedList<String>> getMenuItemPaths();

    protected abstract Action<SimpleResult<DtoContainer<NavigationSettings>>> getEditNavigationMenuItemAction(
            M menuItem);

    protected abstract M getNewMenuItem();

    protected void fillPropertyValues(SuccessReadyState readyState)
    {
        fillPropertyValuesByType(readyState);
        contextProps.setProperty(MenuItemContextValueCode.SETTINGS, settings);
        if (menuItem == null)
        {
            propertyValues.setProperty(MenuItemPropertyCode.TYPE, MenuItemType.reference.name());
            return;
        }
        contextProps.setProperty(MenuItemContextValueCode.MENU_ITEM, menuItem);
        propertyValues.setProperty(MenuItemPropertyCode.TITLE, i18nUtil.getLocalizedTitle(menuItem));
        propertyValues.setProperty(MenuItemPropertyCode.TYPE, menuItem.getType().name());
        propertyValues.setProperty(MenuItemPropertyCode.PARENT, menuItem.getParent());
        propertyValues.setProperty(MenuItemPropertyCode.SETTINGS_SET, menuItem.getSettingsSet());
    }

    protected abstract void fillPropertyValuesByType(SuccessReadyState readyState);

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        fillPropertyValues(readyState);
        setCaption(formMessages.formCaption(getMenuItemCaption()));
        propertyContainer = containerFactory.createSimple(getPropertiesList(), getDisplay(),
                propertyControllerFactory, contextProps, propertyValues, afterBindHandler, validation);
    }

    @Override
    protected void onBind()
    {
        propertyContainer.bind();
        super.onBind();
    }

    protected void fillMenuItem()
    {
        menuItem.setType(MenuItemType.valueOf(propertyValues.getProperty(MenuItemPropertyCode.TYPE)));
        String title = propertyValues.getProperty(MenuItemPropertyCode.TITLE, StringUtilities.EMPTY);
        i18nUtil.updateI18nObjectTitle(menuItem, title);
        menuItem.setSettingsSet(propertyValues.getProperty(MenuItemPropertyCode.SETTINGS_SET));
        fillMenuItemByType();
    }

    protected abstract void fillMenuItemByType();

    protected LinkedList<String> getNewPath(IMenuItem menuItem)
    {
        LinkedList<String> result = Lists.newLinkedList();
        for (; menuItem != null; menuItem = menuItem.getParent())
        {
            result.addFirst(menuItem.getCode());
        }
        return result;
    }

    protected abstract List<String> getPropertiesList();

    protected abstract String getMenuItemCaption();

    protected void setTitle()
    {
        DtObject attrTitle = propertyValues.getProperty(MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE);
        String title;
        if (menuItem.isUseAttributeTitle() && attrTitle != null)
        {
            title = messages.attrForTitle(attrTitle.getTitle());
            menuItem.setAttrForUseInTitle(attrTitle);
        }
        else
        {
            title = i18nUtil.getLocalizedTitle(menuItem);
            menuItem.setAttrForUseInTitle(null);
        }
        i18nUtil.updateI18nObjectTitle(menuItem, title);
    }
}