package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import jakarta.annotation.Nullable;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления для виджета {@link ReferenceCode#ATTRIBUTE_CHAIN}
 *
 * <AUTHOR>
 * @since 09.03.2022
 */
public class AttrChainReferenceDelegateRefreshImpl implements PropertyDelegateRefresh<RelationsAttrTreeObject,
        PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<?, RelationsAttrTreeObject, ?>>>
{
    @Override
    public void refreshProperty(PropertyContainerContext context,
            @Nullable PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<?, RelationsAttrTreeObject, ?>> property,
            AsyncCallback<Boolean> callback)
    {
        DtObject objectClass = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE_OF_CARD);
        boolean isVisible = objectClass != null
                            && ReferenceHelper.AVAILABLE_MENU_ITEM_TYPES_OF_CARD.contains(objectClass.getUUID());
        if (!isVisible)
        {
            if (property != null)
            {
                property.clearValue();
            }
            context.getPropertyControllers().get(ReferenceCode.ATTRIBUTE_CHAIN).unbindValidators();
        }
        callback.onSuccess(isVisible);
    }
}