package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage;

import static ru.naumen.core.client.adminpermission.AdminPermissionUtils.createPermissionPredicate;
import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safecss.shared.SafeStyles;
import com.google.gwt.safecss.shared.SafeStylesBuilder;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.Header;
import com.google.gwt.user.cellview.client.RowStyles;
import com.google.gwt.user.cellview.client.TextHeader;
import com.google.gwt.view.client.AbstractDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.inject.name.Named;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.group.DataTableDnDGroupController;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.columns.ClickableSafeHtmlTextCell;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumnFactory;
import ru.naumen.core.client.widgets.columns.LinkToPlaceWithIndentColumn;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.AbstractDtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfo.shared.homepage.HomePageType;
import ru.naumen.metainfoadmin.client.CatalogCellTableResources.CatalogCellTableStyle;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableResources;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableStyle;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationTabSettingsGinModule;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands.HomePageItemsListCommandCode;
import ru.naumen.metainfoadmin.shared.homepage.MoveHomePageItemAction;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Презентер списка элементов домашней страницы в разделе "Навигация"
 *
 * <AUTHOR>
 * @since 10.01.2023
 */
public class HomePageListPresenter extends BasicPresenter<TableDisplay<HomePageDtObject>>
{
    private final class ItemsDataProvider extends AbstractDataProvider<HomePageDtObject>
    {
        @Override
        protected void onRangeChanged(final HasData<HomePageDtObject> display)
        {
            List<HomePageDtObject> objects = navigationSettings.get().getHomePageDtObjects();
            display.setRowData(0, objects);
            display.setRowCount(objects.size(), true);

            if (dndController != null)
            {
                Scheduler.get().scheduleDeferred(dndController::update);
            }
        }
    }

    //TODO: Вынести DnD в базовый класс NSDPRD-29647 Рефакторинг DnD в простых таблицах
    // https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$200698643:smrmTask$development
    private final class ItemsListDnDController extends DataTableDnDGroupController
    {
        public ItemsListDnDController()
        {
            super(getDisplay().getTableContainer().getElement());
        }

        @Override
        public void move(final int oldPosition, final int newPosition, ReadyState readyState)
        {
            HomePageDtObject item = getDisplay().getTable().getVisibleItem(oldPosition);
            MoveHomePageItemAction action = new MoveHomePageItemAction(item, newPosition);
            dispatch.execute(action, new BasicCallback<SimpleResult<DtoContainer<NavigationSettings>>>(readyState)
            {
                @Override
                protected void handleSuccess(SimpleResult<DtoContainer<NavigationSettings>> settings)
                {
                    refreshCallback.onSuccess(settings.get());
                }
            });
        }

        @Override
        public boolean canDragStart(@Nullable Element element)
        {
            int index = indexOf(element);
            DataTable<HomePageDtObject> table = getDisplay().getTable();
            if (index < 0 || index >= table.getVisibleItemCount())
            {
                return false;
            }
            return hasPermission(permissions, PermissionType.EDIT, table.getVisibleItem(index));
        }
    }

    private final FieldUpdater<HomePageDtObject, SafeHtml> htmlFieldUpdater =
            new FieldUpdater<HomePageDtObject, SafeHtml>()
            {
                @Override
                public void update(int index, HomePageDtObject item, SafeHtml value)
                {
                    placeController.goTo(getNewPlace(item));
                }
            };

    private final OnStartCallback<DtoContainer<NavigationSettings>> refreshCallback =
            new SafeOnStartBasicCallback<DtoContainer<NavigationSettings>>(
                    getDisplay())
            {
                @Override
                protected void handleSuccess(DtoContainer<NavigationSettings> result)
                {
                    navigationSettings = result;
                    param.setSettings(result);
                    PermissionHolder newPermHolder = result.getProperty(SettingsSet.ADMIN_PERMISSIONS);
                    newPermHolder.fillPermissionHolder(permissions);
                    refreshDisplay();
                }
            };

    private class ItemsRowStyles implements RowStyles<HomePageDtObject>
    {
        @Override
        public String getStyleNames(HomePageDtObject homePageDtObject, int rowIndex)
        {
            CatalogCellTableStyle cellTableStyle = cellTableResources.cellTableStyle();
            if (!homePageDtObject.isEnabledByTags())
            {
                return cellTableStyle.itemAttentionRow();
            }
            return null;
        }
    }

    // TODO: https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$200698644
    //  Вынести общие моменты в базовый презентер (во всех подобных местах)
    private final Function<HomePageDtObject, SafeStyles> TITLE_STYLES = input ->
    {
        SafeStylesBuilder sb = new SafeStylesBuilder();
        sb.appendTrustedString("font-weight: 800;");
        return sb.toSafeStyles();
    };

    @Inject
    private CommonMessages cmessages;
    @Inject
    private PlaceController placeController;
    @Inject
    private NavigationSettingsMessages navSettingsMessages;
    @Inject
    private ObjectListColumnBuilder tableBuilder;
    @Inject
    private LinkToPlaceColumnFactory<HomePageDtObject> linkToPlaceColumnFactory;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private WithArrowsCellTableResources cellTableResources;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    @Named(NavigationTabSettingsGinModule.HOME_PAGE_TYPE_TITLES)
    private Map<HomePageType, String> homePageTypeTitles;

    private final ToolBarDisplayMediator<HomePageDtObject> toolBar;
    private final ListEditorDnDControllerFactory dndControllerFactory;
    private ListEditorDnDController dndController = null;
    private HomePageCommandParam param;
    private DtoContainer<NavigationSettings> navigationSettings;
    protected PermissionHolder permissions;

    @Inject
    public HomePageListPresenter(TableDisplay<HomePageDtObject> display, EventBus eventBus,
            ListEditorDnDControllerFactory dndControllerFactory)
    {
        super(display, eventBus);
        this.dndControllerFactory = dndControllerFactory;
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Override
    protected void onBind()
    {
        toolBar.add((ButtonPresenter)buttonFactory.create(ButtonCode.ADD, cmessages.addItem(),
                HomePageItemsListCommandCode.ADD, param));

        getDisplay().setCaption(navSettingsMessages.homePage());

        initTable();
        ItemsDataProvider itemsDataProvider = new ItemsDataProvider();
        itemsDataProvider.addDataDisplay(getDisplay().getTable());

        toolBar.bind();
    }

    public void init(DtoContainer<NavigationSettings> container)
    {
        navigationSettings = new DtoContainer<>(new NavigationSettings().copyFrom(container.get()));
        this.permissions = container.getProperty(SettingsSet.ADMIN_PERMISSIONS);
        param = new HomePageCommandParam(container, refreshCallback);
        dndController = dndControllerFactory.create(new ItemsListDnDController());
    }

    @Override
    public void refreshDisplay()
    {
        getDisplay().refresh();
    }

    protected void initTable()
    {
        DataTable<HomePageDtObject> table = getDisplay().getTable();
        table.setVisibleRange(0, DefaultCellTable.DEFAULT_PAGESIZE);

        addMoveItemColumn();
        addTitleColumn(table);
        addTypeColumn(table);
        addContentColumn(table);
        addProfilesColumn(table);
        addActionColumns();

        table.setRowStyles(new ItemsRowStyles());
        table.asWidget().ensureDebugId("homePageListTable");
    }

    private void addMoveItemColumn()
    {
        addActionColumn(HomePageItemsListCommandCode.MOVE_ITEM_UP);
        addActionColumn(HomePageItemsListCommandCode.MOVE_ITEM_DOWN);
    }

    private void addTitleColumn(DataTable<HomePageDtObject> table)
    {
        WithArrowsCellTableStyle tableStyle = cellTableResources.cellTableStyle();
        LinkToPlaceWithIndentColumn<HomePageDtObject> titleColumn =
                (LinkToPlaceWithIndentColumn<HomePageDtObject>)linkToPlaceColumnFactory.getColumn(this::getNewPlace);
        titleColumn.setCellStyleNames(tableStyle.titleColumn());
        titleColumn.setFieldUpdater(htmlFieldUpdater);
        titleColumn.setStyleFunction(TITLE_STYLES);
        Header<?> titleHeader = new TextHeader(cmessages.title());
        titleHeader.setHeaderStyleNames(tableStyle.titleColumn());
        titleColumn.setRenderFunction(AbstractDtObject::getTitle);
        table.addColumn(titleColumn, titleHeader);
    }

    private Place getNewPlace(HomePageDtObject item)
    {
        return new NavigationHomePagePlace(item, navigationSettings);
    }

    private void addTypeColumn(DataTable<HomePageDtObject> table)
    {
        Column<HomePageDtObject, SafeHtml> typeColumn = new Column<HomePageDtObject, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(HomePageDtObject item)
            {
                return new SafeHtmlBuilder()
                        .appendEscaped(homePageTypeTitles.get(HomePageType.valueOf(item.getType())))
                        .toSafeHtml();
            }
        };
        typeColumn.setFieldUpdater(htmlFieldUpdater);
        table.addColumn(typeColumn, cmessages.homePageView());
    }

    private void addContentColumn(DataTable<HomePageDtObject> table)
    {
        Column<HomePageDtObject, SafeHtml> contentColumn =
                new Column<HomePageDtObject, SafeHtml>(new ClickableSafeHtmlTextCell())
                {
                    @Override
                    public SafeHtml getValue(HomePageDtObject item)
                    {
                        return new SafeHtmlBuilder()
                                .appendEscaped(item.getContentValue())
                                .toSafeHtml();
                    }
                };
        contentColumn.setFieldUpdater(htmlFieldUpdater);
        table.addColumn(contentColumn, cmessages.content());
    }

    protected void addProfilesColumn(DataTable<HomePageDtObject> table)
    {
        Column<HomePageDtObject, SafeHtml> profilesColumn = new Column<HomePageDtObject, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(HomePageDtObject item)
            {
                String profilesTitles = item.getProfiles().stream().map(ITitled::getTitle)
                        .collect(Collectors.joining(", "));
                return new SafeHtmlBuilder().appendEscaped(profilesTitles).toSafeHtml();
            }
        };
        WithArrowsCellTableStyle tableStyle = cellTableResources.cellTableStyle();
        profilesColumn.setFieldUpdater(htmlFieldUpdater);
        profilesColumn.setCellStyleNames(tableStyle.titleCellWithHint());
        table.addColumn(profilesColumn, navSettingsMessages.profiles());
    }

    private void addActionColumns()
    {
        addActionColumn(HomePageItemsListCommandCode.EDIT);
        addActionColumn(HomePageItemsListCommandCode.DELETE);
    }

    private void addActionColumn(String... commands)
    {
        PermissionType permissionType = HomePageItemsListCommandCode.DELETE.equals(commands[0])
                ? PermissionType.DELETE
                : PermissionType.EDIT;
        tableBuilder.addActionColumn(display, param, createPermissionPredicate(permissionType, permissions), commands);
    }
}
