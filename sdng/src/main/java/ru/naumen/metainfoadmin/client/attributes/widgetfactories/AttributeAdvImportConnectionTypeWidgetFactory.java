package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.advimport.shared.connect.ConnectionType;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;

/**
 * Фабрика по созданию представления для редактирования "типа" подключений адвимпорта. 
 *
 * <AUTHOR>
 * @since 01.12.2017
 */
public class AttributeAdvImportConnectionTypeWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();
        widget.addItem(new SimpleDtObject(ConnectionType.LDAP.getTitle(), ConnectionType.LDAP.getTitle()));
        widget.addItem(new SimpleDtObject(ConnectionType.SQL.getTitle(), ConnectionType.SQL.getTitle()));
        callback.onSuccess(widget);
    }
}
