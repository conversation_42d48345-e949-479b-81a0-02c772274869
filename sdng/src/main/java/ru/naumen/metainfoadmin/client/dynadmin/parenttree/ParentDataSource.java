package ru.naumen.metainfoadmin.client.dynadmin.parenttree;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.annotation.Nullable;

import com.google.gwt.core.client.GWT;
import com.google.gwt.safehtml.client.SafeHtmlTemplates;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.tree.datasource.AbstractAsyncTreeDataSource;
import ru.naumen.core.shared.Constants.DtoTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentTitles;

/**
 * Абстрактная реализация DataSource для выбора родителя
 *
 * <AUTHOR>
 * @since 10.08.2021
 */
public abstract class ParentDataSource extends AbstractAsyncTreeDataSource<DtObject, ParentTreeFactoryContext>
{
    protected interface SafeFormatter extends SafeHtmlTemplates
    {
        @Template("{0} ({1})")
        SafeHtml getCaption(SafeHtml caption, String code);
    }

    private final SafeFormatter formatter = GWT.create(SafeFormatter.class);
    private final ContentTitles contentTitles;
    private final MetainfoUtils metainfoUtils;
    protected final ParentTreeFactoryContext treeContext;

    protected ParentDataSource(ParentTreeFactoryContext context, ContentTitles contentTitles,
            MetainfoUtils metainfoUtils)
    {
        super(context);
        this.treeContext = context;
        this.contentTitles = contentTitles;
        this.metainfoUtils = metainfoUtils;
        initItemsMap();
    }

    /**
     * Метод создает элементы дерева
     */
    protected void initItemsMap()
    {
        treeContext.setRootDTO(createRootTreeDtObject());
        initTreeChildByParent(treeContext.getRootDTO(), getFirstLevel(treeContext.getRootContent()));
        updateLeaf();
        updateSelectable();
    }

    /**
     * Получить список элементов на первом уровне дерева (родителем является rootDTO)
     * @param rootContent - Window или Form, на котором инициирована форма
     * @return - лист элементов, у которых родителем является rootDTO
     */
    protected abstract List<? extends Content> getFirstLevel(Content rootContent);

    /**
     * Устанавливает у элементов доступность выбора в интерфейсе
     */
    protected abstract void updateSelectable();

    @Override
    protected void asyncGetChildren(DtObject parent, AsyncCallback<List<DtObject>> callback)
    {
        callback.onSuccess(getChildren(parent));
    }

    private List<DtObject> getChildren(DtObject parent)
    {
        return treeContext.getChildren(parent);
    }

    /**
     * Создает корневой TreeDtObject с заголовком "карточка объекта/форма (код)"
     * @return TreeDtObject представление root в дереве выбора
     */
    protected SimpleTreeDtObject createRootTreeDtObject()
    {
        String caption = formatter.getCaption(SafeHtmlUtils.fromString(
                        metainfoUtils.getLocalizedValue(treeContext.getRootContent().getCaption())),
                treeContext.getUiContext().getMetainfo().getCode()).asString();

        return createTreeDtObject(treeContext.getRootContent().getUuid(), caption, false, null, null);
    }

    /**
     * Создает TreeDtObject для контента типа "панель вкладок" с заголовком формата "Панель вкладок (%код контента%)"
     * @param tabBar - контент "панель вкладок"
     * @param parent - родительский элемент для панели вкладок
     * @return TreeDtObject представление tabBar в дереве выбора
     */
    protected TreeDtObject createTreeDtObject(TabBar tabBar, @Nullable DtObject parent)
    {
        String caption = formatter.getCaption(SafeHtmlUtils.fromString(contentTitles.content("TabBar")),
                tabBar.getUuid()).asString();
        return createTreeDtObject(tabBar.getUuid(), caption, false, parent, tabBar);
    }

    /**
     * Создает TreeDtObject для контента типа "вкладка" с заголовком формата "%Название вкладки% (%код вкладки%)"
     * @param tab - контент типа "вкладка"
     * @param parent - родительский элемент для панели вкладок
     * @return TreeDtObject представление tab в дереве выбора
     */
    protected SimpleTreeDtObject createTreeDtObject(Tab tab, @Nullable DtObject parent)
    {
        //т.к. санитайзится компонентом PropertyBase, строка является доверенной
        String caption = formatter.getCaption(
                        SafeHtmlUtils.fromTrustedString(metainfoUtils.getLocalizedValue(tab.getCaption())),
                        tab.getUuid())
                .asString();
        return createTreeDtObject(tab.getUuid(), caption, true, parent, tab);
    }

    /**
     * Создает TreeDtObject для контента, и устанавливает связь между TreeDtObject и Content
     * @param uuid - UUID элемента
     * @param caption - отображаемый заголовок элемента
     * @param isLeaf - есть ли потомки у этого элемента
     * @param parent - родительский элемент
     * @param content - контент
     * @return TreeDtObject представление content в дереве выбора
     */
    protected SimpleTreeDtObject createTreeDtObject(String uuid, String caption, boolean isLeaf,
            @Nullable DtObject parent, @Nullable Content content)
    {
        DtObject item = new SimpleDtObject(uuid, caption);
        item.setProperty(DtoTree.IS_LEAF, isLeaf);
        SimpleTreeDtObject treeDtObject = new SimpleTreeDtObject(parent, item);
        treeContext.setRelation(treeDtObject, content);
        return treeDtObject;
    }

    /**
     * Проходит по элементам дерева, у которых имеются потомки и устанавливает флаг наличия потомков
     */
    protected void updateLeaf()
    {
        treeContext.getItems().forEach((key, value) ->
        {
            if (key != null)
            {
                key.setProperty(DtoTree.IS_LEAF, CollectionUtils.isEmpty(value));
            }
        });
    }

    /**
     * Устанавливает возможность выбирать элемент в дереве
     * @param item - элемент дерева
     * @param selectable - true, если элемент можно выбрать, в противном случае false
     */
    protected void setSelectable(DtObject item, boolean selectable)
    {
        item.setProperty(DtoTree.SELECTABLE, selectable);
    }

    /**
     * Рекурсивно создает элементы дерева
     * @param parent - родительский элемент
     * @param contents - список дочерних контентов
     */
    protected void initTreeChildByParent(DtObject parent, List<? extends Content> contents)
    {
        List<DtObject> children = treeContext.getItems().getOrDefault(parent, new ArrayList<>());
        for (Content content : contents)
        {
            if (content instanceof TabBar)
            {
                TabBar tabBar = (TabBar)content;
                TreeDtObject tabBarDTO = createTreeDtObject(tabBar, parent);
                children.add(tabBarDTO);
                initTreeChildByParent(tabBarDTO, tabBar.getTab());
            }
            else if (content instanceof Tab)
            {
                Tab tab = (Tab)content;
                TreeDtObject tabDTO = createTreeDtObject(tab, parent);
                children.add(tabDTO);
                for (Content internalContent : tab.getLayout().getChilds())
                {
                    if (internalContent instanceof TabBar || internalContent instanceof Tab)
                    {
                        initTreeChildByParent(tabDTO, Collections.singletonList(internalContent));
                    }
                }
            }
        }
        treeContext.getItems().put(parent, children);
    }
}