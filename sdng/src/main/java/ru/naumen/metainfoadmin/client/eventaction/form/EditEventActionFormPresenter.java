package ru.naumen.metainfoadmin.client.eventaction.form;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.general.StringResult;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactory;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfoadmin.client.eventaction.form.attrtree.EventAttributesTreeFactoryContext;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.sec.shared.GetAllowedForUnlicensedActionWarningAction;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 06.12.2011
 */
public class EditEventActionFormPresenter extends AbstractEventActionFormPresenter
{
    private static ClassFqn getUserEventFqn(String code)
    {
        return ClassFqn.parse("userEvent", ClassFqn.parse(code).getCode());
    }

    @Inject
    private DispatchAsync dispatch;

    @Inject
    public EditEventActionFormPresenter(
            @Named(EventActionFormGinModule.EVENT_ACTION_TREE) SelectFqnsPropertyFactory treeProvider,
            EventActionFormDisplay display,
            EventBus eventBus,
            DtoTreeFactory<Collection<DtObject>, EventActionFormGinModule.EventAttributesTree,
                    DtoTreeGinModule.WithoutFolders, EventAttributesTreeFactoryContext> attrTreeFactory,
            SettingsSetOnFormCreator settingsSetOnFormCreator)
    {
        super(treeProvider, display, eventBus, settingsSetOnFormCreator, attrTreeFactory);
    }

    @Override
    protected void initPropertiesValues(final EventActionWithScript eventAction, final AsyncCallback<Void> callback)
    {
        getMetainfoService().getMetaClasses(eventAction.getObject().getLinkedClasses(),
                new BasicCallback<List<MetaClassLite>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(List<MetaClassLite> metaClasses)
                    {
                        getFqn().setValue(CollectionUtils.transform(metaClasses, DtObject.CREATE_FROM_METACLASSLITE));
                        EditEventActionFormPresenter.super.initPropertiesValues(eventAction, callback);
                        eventBus.fireEvent(new UpdateTabOrderEvent(true));
                    }
                });
    }

    @Override
    protected boolean isNewEventAction()
    {
        return false;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getCode().setDisable();
        getDisplay().setCaptionText(getEventActionMessages().editFormCaption());
        SingleSelectCellList<?> selList = getAction().getValueWidget();
        String actionType = getEventAction().getObject().getAction().getActionType().name();
        selList.addItem(eventActionConstants.actionTypes().get(actionType), actionType);
        getAction().setDisable();
        ((ListBoxWithEmptyOptProperty)getAction()).trySetObjValue(actionType);
        EventType eventType = getEventAction().getObject().getEvent().getEventType();
        if (eventType == EventType.userEvent
            && !CollectionUtils.isEmpty(getEventAction().getObject().getUsagePlaces()) ||
            (eventType == EventType.arriveMessageOnQueue))
        {
            getEvent().setDisable();
        }
        visibleFqnField(eventType != EventType.arriveMessageOnQueue);
        showAttentionForUserEvent();
    }

    @Override
    protected String getSettingsSetInitialValue()
    {
        return getEventAction().getObject().getSettingsSet();
    }

    private void showAttention(String code)
    {
        dispatch.execute(new GetAllowedForUnlicensedActionWarningAction(getUserEventFqn(code)),
                new BasicCallback<StringResult>()
                {
                    @Override
                    public void onSuccess(StringResult result)
                    {
                        if (!result.get().isEmpty())
                        {
                            display.addAttentionMessage(result.get());
                        }
                    }
                });
    }

    private void showAttentionForUserEvent()
    {
        if (getEventAction().getObject().getEvent().getEventType() == EventType.userEvent)
        {
            showAttention(getEventAction().getCode());
        }
    }

    @Override
    protected List<EventType> getAllowedEvents(@Nullable List<MetaClassLite> metaClasses,
            @Nullable Set<ClassFqn> selectedFqns, @Nullable String actionType)
    {
        List<EventType> result = super.getAllowedEvents(metaClasses, selectedFqns, actionType);
        result.removeAll(availableActionsProvider.getUnavailableEvents(
                getEventAction().getObject().getAction().getActionType()));
        return result;
    }
}
