package ru.naumen.metainfoadmin.client.fastlink.settings.forms;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector.PropertyContainerPresenterFactory;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSetting;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSettingWithTitles;
import ru.naumen.metainfo.shared.fastlink.settings.dispatch.AddFastLinkSettingAction;
import ru.naumen.metainfo.shared.fastlink.settings.dispatch.EditFastLinkSettingAction;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsGinModule.FastLinkSettingFormPropertyCode;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsMessages;

/**
 * <AUTHOR>
 * @since 01.03.18
 *
 */
public class FastLinkSettingForm extends OkCancelPresenter<DefaultPropertyFormDisplayImpl>
{
    @Inject
    private PropertyContainerPresenterFactory containerFactory;
    @Inject
    private PropertyControllerFactory<FastLinkSetting, ObjectForm> propertyFactory;
    @Inject
    private Processor validation;
    @Inject
    protected I18nUtil i18nUtil;
    @Inject
    protected DispatchAsync dispatch;
    @Inject
    protected MetainfoUtils metainfoUtils;
    @Inject
    protected FastLinkSettingsMessages fastLikSettingsMessages;
    @Inject
    protected AdminMetainfoServiceAsync metainfoService;

    private AsyncCallback<List<DtoContainer<FastLinkSettingWithTitles>>> refreshCallback;
    protected IProperties contextProps = new MapProperties();
    protected IProperties propertyValues = new MapProperties();
    protected PropertyContainerPresenter propertyContainer;

    @Inject
    public FastLinkSettingForm(DefaultPropertyFormDisplayImpl display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public IProperties getPropertyValues()
    {
        return propertyValues;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }

        FastLinkSetting fastLinkSetting = new FastLinkSetting();
        fastLinkSetting.setCode(propertyValues.<String> getProperty(FastLinkSettingFormPropertyCode.CODE));
        i18nUtil.updateI18nObjectTitle(fastLinkSetting,
                propertyValues.<String> getProperty(FastLinkSettingFormPropertyCode.TITLE));
        fastLinkSetting.setSettingsSet(
                propertyValues.<String> getProperty(FastLinkSettingFormPropertyCode.SETTINGS_SET));
        fastLinkSetting.setMentionTypes(
                propertyValues.<Collection<DtObject>> getProperty(FastLinkSettingFormPropertyCode.MENTION_TYPES)
                        .stream().map(DtObject::getMetainfo).collect(Collectors.toCollection(ArrayList::new)));
        fastLinkSetting.setContextTypes(
                propertyValues.<Collection<DtObject>> getProperty(FastLinkSettingFormPropertyCode.CONTEXT_TYPES)
                        .stream().map(DtObject::getMetainfo).collect(Collectors.toCollection(ArrayList::new)));
        fastLinkSetting.setMentionAttribute(
                propertyValues.<String> getProperty(FastLinkSettingFormPropertyCode.MENTION_ATTRIBUTE));
        fastLinkSetting.setAlias(propertyValues.<String> getProperty(FastLinkSettingFormPropertyCode.ALIAS));
        fastLinkSetting.setAttributeGroup(
                propertyValues.<String> getProperty(FastLinkSettingFormPropertyCode.ATTRIBUTE_GROUP));
        fastLinkSetting.setProfiles(Lists.newArrayList(
                propertyValues.<Collection<String>> getProperty(FastLinkSettingFormPropertyCode.PROFILES)));

        if (isNewFastLinkSetting())
        {
            dispatch.execute(new AddFastLinkSettingAction(fastLinkSetting),
                    new BasicCallback<SimpleResult<List<DtoContainer<FastLinkSettingWithTitles>>>>()
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<List<DtoContainer<FastLinkSettingWithTitles>>> value)
                        {
                            FastLinkSettingForm.this.unbind();
                            refreshCallback.onSuccess(value.get());
                        }
                    });
        }
        else
        {
            dispatch.execute(new EditFastLinkSettingAction(fastLinkSetting),
                    new BasicCallback<SimpleResult<List<DtoContainer<FastLinkSettingWithTitles>>>>()
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<List<DtoContainer<FastLinkSettingWithTitles>>> value)
                        {
                            FastLinkSettingForm.this.unbind();
                            refreshCallback.onSuccess(value.get());
                        }
                    });
        }
    }

    public void setRefreshCallback(AsyncCallback<List<DtoContainer<FastLinkSettingWithTitles>>> refreshCallback)
    {
        this.refreshCallback = refreshCallback;
    }

    protected boolean isNewFastLinkSetting()
    {
        return false;
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        metainfoService.getSettings(new BasicCallback<Settings>()
        {
            @Override
            protected void handleSuccess(Settings value)
            {
                List<String> properties = Lists.newArrayList(FastLinkSettingFormPropertyCode.TITLE,
                        FastLinkSettingFormPropertyCode.CODE, FastLinkSettingFormPropertyCode.MENTION_TYPES,
                        FastLinkSettingFormPropertyCode.ALIAS,
                        FastLinkSettingFormPropertyCode.CONTEXT_TYPES,
                        FastLinkSettingFormPropertyCode.MENTION_ATTRIBUTE,
                        FastLinkSettingFormPropertyCode.ATTRIBUTE_GROUP,
                        FastLinkSettingFormPropertyCode.PROFILES,
                        FastLinkSettingFormPropertyCode.SETTINGS_SET);

                setProperties();
                propertyContainer = containerFactory.createSimple(properties, getDisplay(), propertyFactory,
                        contextProps, propertyValues, validation);
                registerHandler(propertyContainer.addUpdateTabOrderHandler(FastLinkSettingForm.this));
                propertyContainer.bind();

                if (isNewFastLinkSetting())
                {
                    getDisplay().display();
                }
                else
                {
                    propertyContainer.getContext().setDisabled(FastLinkSettingFormPropertyCode.CODE);
                    getDisplay().display();
                }
            }
        });

    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        if (null != propertyContainer)
        {
            propertyContainer.unbind();
        }
    }

    protected void setProperties()
    {
    }
}
