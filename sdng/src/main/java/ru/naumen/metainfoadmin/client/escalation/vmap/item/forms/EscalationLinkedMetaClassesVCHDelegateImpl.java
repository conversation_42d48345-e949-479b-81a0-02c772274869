/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import java.util.Arrays;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap.LinkedMetaClassesVCHDelegateImpl;

/**
 * <AUTHOR>
 * @since 06.11.2012
 *
 */
public class EscalationLinkedMetaClassesVCHDelegateImpl extends
        LinkedMetaClassesVCHDelegateImpl<EscalationValueMapItemFormContext>
{
    @Override
    protected void startRefreshProcess(PropertyContainerContext context)
    {
        context.getRefreshProcess().startCustomProcess(
                Arrays.asList(ValueMapCatalogItem.SOURCE_ATTRS, ValueMapCatalogItem.DEFAULT_OBJECT));
        context.getPropertyControllers().get(ValueMapCatalogItem.TARGET_ATTRS).refresh();
    }
}