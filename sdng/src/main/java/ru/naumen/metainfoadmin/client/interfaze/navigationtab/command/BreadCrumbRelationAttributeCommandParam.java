package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.CrumbRelationAttribute;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public class BreadCrumbRelationAttributeCommandParam extends
        NavigationSettingsAbstractCommandParam<CrumbRelationAttribute>
{
    private Crumb crumb;

    public BreadCrumbRelationAttributeCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, (CrumbRelationAttribute)null, callback);
    }

    public BreadCrumbRelationAttributeCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable CrumbRelationAttribute value,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, value, callback);
    }

    public BreadCrumbRelationAttributeCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable FactoryParam.ValueSource<CrumbRelationAttribute> valueSource,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, valueSource, callback);
    }

    @Override
    @SuppressWarnings("unchecked")
    public BreadCrumbRelationAttributeCommandParam cloneIt()
    {
        BreadCrumbRelationAttributeCommandParam clone = new BreadCrumbRelationAttributeCommandParam(getSettings(),
                getValueSource(), getCallback());
        clone.setCrumb(crumb);
        return clone;
    }

    public Crumb getCrumb()
    {
        return crumb;
    }

    public void setCrumb(Crumb crumb)
    {
        this.crumb = crumb;
    }
}
