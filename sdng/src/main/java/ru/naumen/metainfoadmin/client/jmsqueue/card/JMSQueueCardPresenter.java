package ru.naumen.metainfoadmin.client.jmsqueue.card;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.QUEUES;
import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.JMSQueue.PUB_SUB_DOMAIN;
import static ru.naumen.metainfoadmin.client.eventaction.EventActionsTabsPresenter.JMS_QUEUES_TAB_ID;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.GetJMSQueueAction;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.eventaction.EventActionsPlace;
import ru.naumen.metainfoadmin.client.jmsqueue.JMSQueueMessages;

/**
 * Презентер карточки очереди JMS
 * <AUTHOR>
 * @since 16.02.2021
 **/
public class JMSQueueCardPresenter extends AdminTabPresenter<JMSQueuePlace>
{
    private class BindCallback extends BasicCallback<SimpleResult<DtObject>>
    {
        @Override
        protected void handleSuccess(SimpleResult<DtObject> response)
        {
            final DtObject queue = response.get();
            infoPresenter.setJMSQueue(queue);
            addContent(infoPresenter, "info");
            getDisplay().setTitle(queue.getTitle());
            afterJMSQueueLoad(queue);
        }

        private void afterJMSQueueLoad(DtObject jmsQueue)
        {
            countMessagesPresenter.init(jmsQueue);
            addContent(countMessagesPresenter, "countMessages");
            setVisibleCountMessagesBlock(jmsQueue);
            eventActionsInQueuePresenter.init(jmsQueue);
            addContent(eventActionsInQueuePresenter, "eventActionsInQueue");
        }
    }

    private final DispatchAsync dispatch;
    private final JMSQueueMessages jmsQueueMessages;
    private final JMSQueueInfoPresenter infoPresenter;
    private final JMSQueueCountMessagesPresenter countMessagesPresenter;
    private final JMSQueueEventActionsInQueuePresenter eventActionsInQueuePresenter;
    private final SharedSettingsClientService sharedSettingsService;

    private final AsyncCallback<SimpleResult<DtObject>> bindCallback;

    private final OnStartCallback<DtObject> refreshCallback = new SafeOnStartBasicCallback<DtObject>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            infoPresenter.setJMSQueue(value);
            infoPresenter.onBindPubSubDomainProperties();
            setVisibleCountMessagesBlock(value);
        }
    };

    private void setVisibleCountMessagesBlock(DtObject value)
    {
        countMessagesPresenter.getDisplay().asWidget()
                .setVisible(!sharedSettingsService.isPubSubDomainEnabled()
                            || !Boolean.parseBoolean(value.getProperty(PUB_SUB_DOMAIN)));
    }

    @Inject
    public JMSQueueCardPresenter(AdminTabDisplay display,
            EventBus eventBus,
            DispatchAsync dispatch,
            JMSQueueMessages jmsQueueMessages,
            JMSQueueInfoPresenter infoPresenter,
            JMSQueueCountMessagesPresenter countMessagesPresenter,
            JMSQueueEventActionsInQueuePresenter eventActionsInQueuePresenter,
            SharedSettingsClientService sharedSettingsService)
    {
        super(display, eventBus);
        this.dispatch = dispatch;
        this.jmsQueueMessages = jmsQueueMessages;
        this.infoPresenter = infoPresenter;
        this.countMessagesPresenter = countMessagesPresenter;
        this.eventActionsInQueuePresenter = eventActionsInQueuePresenter;
        this.sharedSettingsService = sharedSettingsService;
        bindCallback = new BindCallback();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        infoPresenter.refreshDisplay();
        countMessagesPresenter.refreshDisplay();
        eventActionsInQueuePresenter.refreshDisplay();
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();
        infoPresenter.init(refreshCallback);
        prevPageLinkPresenter.bind(jmsQueueMessages.back(), new EventActionsPlace(JMS_QUEUES_TAB_ID), true);
        if (getPlace().getJMSQueue() == null)
        {
            dispatch.execute(new GetJMSQueueAction(getPlace().getCode()), bindCallback);
        }
        else
        {
            bindCallback.onSuccess(new SimpleResult<DtObject>(getPlace().getJMSQueue()));
        }
    }

    @Override
    protected void onUnbind()
    {
        infoPresenter.unbind();
        countMessagesPresenter.unbind();
        eventActionsInQueuePresenter.unbind();
        super.onUnbind();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return QUEUES;
    }
}