package ru.naumen.metainfoadmin.client.attributes.forms;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.client.validation.NamingRuleDelegateValidator;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.StringLengthValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.FootedTextBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.TextAreaProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindTextBoxFactory;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateValidator;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.props.PermittedTypesPropertyControllerFactory;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation.ComplexRelationAggrAttrGroupRefreshDelegateFactory;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.iconsforcontrols.BooleanCheckBoxInfoProperty;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.metainfoadmin.client.widgets.properties.ScriptComponentEditProperty;

/**
 * Фабрика обычных свойств формы атрибута - связывает вместе коды атрибутов и
 * соотв. им обработчики различных событий и фабрики виджетов
 *
 * <AUTHOR>
 * @since 12.05.2012
 */
@Singleton
public class AttributeFormPropertyControllerFactorySyncImpl<F extends ObjectForm>
        extends AbstractFormPropertyControllerFactorySyncImpl<F>
{
    @Inject
    PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Boolean, BooleanCheckBoxProperty> booleanPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Boolean, BooleanCheckBoxInfoProperty> booleanInfoPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<SelectItem, ListBoxProperty> listBoxPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<String, TextBoxProperty> textPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxWithEmptyPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<String, TextAreaProperty> textAreaPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Collection<SelectItem>, TagsProperty> tagsPropertyFactory;
    @Inject
    PropertyDelegateBindTextBoxFactory textBoxBindDelegateFactory;
    @Inject
    PermittedTypesPropertyControllerFactory<F> permittedTypesPropertyFactory;
    @Inject
    NamingRuleDelegateValidator namingRuleDelegateValidator;
    @Inject
    PropertyControllerSyncFactoryInj<Collection<SelectItem>, MultiSelectBoxProperty> multiSelectListPropertyFactory;

    //@formatter:off
    @Inject
    PropertyControllerSyncFactoryInj<RelationsAttrTreeObject,
                 PropertyBase<RelationsAttrTreeObject,
                              PopupValueCellTree<RelationsAttrTreeObject,
                                     RelationsAttrTreeObject,
                                     FilteredSingleSelectionModel<RelationsAttrTreeObject>>>> popupSelectAttrTreePropertyFactory;
    //@formatter:on

    @Inject
    @Named(COMPLEX_RELATION)
    AttributeFormPropertyDelegateVCH<F> complexVCHDelegate;
    @Inject
    @Named(INHERIT)
    AttributeFormPropertyDelegateVCH<F> inheritVCHDelegate;
    @Inject
    @Named(COMPUTABLE)
    AttributeFormPropertyDelegateVCH<F> computableVCHDelegate;
    @Inject
    @Named(EXPORT_NDAP)
    AttributeFormPropertyDelegateVCH<F> exportNDAPVCHDelegate;
    @Inject
    @Named(USE_GEN_RULE)
    AttributeFormPropertyDelegateVCH<F> useGeneratedRuleVCHDelegate;
    @Inject
    @Named(GEN_RULE)
    AttributeFormPropertyDelegateVCH<F> generatedRuleVCHDelegate;
    @Inject
    @Named(DETERMINABLE)
    AttributeFormPropertyDelegateVCH<F> determinableVCHDelegate;
    @Inject
    @Named(COMPOSITE)
    AttributeFormPropertyDelegateVCH<F> compositeVCHDelegate;
    @Inject
    @Named(INTERVAL_AVAILABLE_UNITS)
    AttributeFormPropertyDelegateVCH<F> intervalAvailableUnitsVCHDelegate;
    @Inject
    @Named(NEED_STORE_UNITS)
    AttributeFormPropertyDelegateVCH<F> needStoreUnitsVCHDelegate;
    @Inject
    @Named(ATTR_CHAIN)
    AttributeFormPropertyDelegateVCH<F> attrChainVCHDelegate;
    @Inject
    @Named(RELATED_OBJECT_ATTRIBUTE)
    AttributeFormPropertyDelegateVCH<F> relatedObjectAttributeVCHDelegate;

    @Inject
    @Named(TARGET_TIMER)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> targetTimerRefreshDelegate;
    @Inject
    @Named(INHERIT)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> inheritRefreshDelegate;
    @Inject
    @Named(COMPUTABLE)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> computableRefreshDelegate;
    @Inject
    @Named(USE_GEN_RULE)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> useGeneratedRuleRefreshDelegate;
    @Inject
    @Named(USE_GEN_RULE)
    AttributeFormPropertyDelegateBind<F, Boolean, BooleanCheckBoxProperty> useGeneratedRulePropertyDelegate;
    @Inject
    @Named(GEN_RULE)
    AttributeFormPropertyDelegateRefresh<F, String, FootedTextBoxProperty> generatedRuleRefreshDelegate;
    @Inject
    @Named(GEN_RULE)
    AttributeFormPropertyDelegateBind<F, String, FootedTextBoxProperty> generatedRulePropertyDelegate;
    @Inject
    @Named(TEMPLATE)
    AttributeFormPropertyDelegateBind<F, String, FootedTextBoxProperty> templatePropertyDelegate;

    @Inject
    @Named(DETERMINABLE)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> determinableRefreshDelegate;
    @Inject
    @Named(DETERMINER)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxWithEmptyOptProperty> ruleDetermineRefreshDelegate;
    @Inject
    @Named(SCRIPT)
    AttributeFormPropertyDelegateRefresh<F, ScriptDto, ScriptComponentEditProperty> scriptRefreshDelegate;
    @Inject
    @Named(NEED_STORE_UNITS)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxInfoProperty> needStoreUnitsRefreshDelegate;
    @Inject
    @Named(INTERVAL_AVAILABLE_UNITS)
    AttributeFormPropertyDelegateRefresh<F, Collection<SelectItem>, MultiSelectBoxProperty> intervalAvailableUnitsRefreshDelegate;
    @Inject
    @Named(COMPLEX_RELATION)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> complexRefreshDelegate;
    @Inject
    @Named(COMPLEX_RELATION_ATTR_GROUP)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> complexAttrGroupRefreshDelegate;
    @Inject
    @Named(COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> complexStructuredObjectsViewRefreshDelegate;
    @Inject
    @Named(EDITABLE_IN_LISTS)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> editableInListsRefreshDelegate;
    @Inject
    @Named(REQUIRED_IN_INTERFACE)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> requiredInInterfaceRefreshDelegate;
    @Inject
    @Named(HAS_GROUP_SEPARATORS)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> hasGroupSeparatorsRefreshDelegate;
    @Inject
    @Named(HAS_GROUP_SEPARATORS)
    AttributeFormPropertyDelegateVCH<F> hasGroupSeparatorsVCHDelegate;
    @Inject
    @Named(UNIQUE)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> uniqueRefreshDelegate;
    @Inject
    @Named(COMPOSITE)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> compositeRefereshDelegate;
    @Inject
    @Named(TEMPLATE)
    AttributeFormPropertyDelegateRefresh<F, String, FootedTextBoxProperty> templateRefereshDelegate;
    @Inject
    @Named(EXPORT_NDAP)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxInfoProperty> exportNDAPRefreshDelegate;
    @Inject
    @Named(RELATED_ATTRS_TO_EXPORT)
    AttributeFormPropertyDelegateRefresh<F, Collection<SelectItem>, MultiSelectBoxProperty> relatedAttrToExportRefreshDelegate;
    @Inject
    @Named(HIDDEN_WHEN_EMPTY)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> hideWhenEmptyRefreshDelegate;
    @Inject
    @Named(HIDDEN_WHEN_NO_POSSIBLE_VALUES)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> hiddenWhenNoPossibleValuesRefreshDelegate;
    @Inject
    @Named(EDIT_ON_COMPLEX_FORM_ONLY)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> editOnComplexFormOnlyRefreshDelegate;
    @Inject
    @Named(HIDE_ARCHIVED)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> showArchiveRefreshDelegate;
    @Inject
    @Named(QUICK_ADD_FORM_CODE)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> quickAddFormRefreshDelegate;
    @Inject
    @Named(QUICK_EDIT_FORM_CODE)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> quickEditFormRefreshDelegate;
    @Inject
    @Named(ATTR_CHAIN)
    AttributeFormPropertyDelegateRefresh<F, RelationsAttrTreeObject, PropertyBase<RelationsAttrTreeObject,
            PopupValueCellTree<RelationsAttrTreeObject, RelationsAttrTreeObject,
                    FilteredSingleSelectionModel<RelationsAttrTreeObject>>>> attrChainRefreshDelegate;
    @Inject
    @Named(RELATED_OBJECT_ATTRIBUTE)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> relatedObjectAtributeRefreshDelegate;
    @Inject
    @Named(RELATED_OBJECT_METACLASS)
    AttributeFormPropertyDelegateRefresh<F, String, TextBoxProperty> relatedObjectMetaclassRefreshDelegate;
    @Inject
    @Named(ATTR_CHAIN_VIEW)
    AttributeFormPropertyDelegateRefresh<F, String, TextBoxProperty> attrChainViewRefreshDelegate;
    @Inject
    @Named(RELATED_OBJECT_HIERARCHY_LEVEL)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> relatedObjectHierarchyLevelRefreshDelegate;

    @Inject
    ComplexRelationAggrAttrGroupRefreshDelegateFactory<F> complexAggrAttrGroupRefreshDelegateFactory;

    @Inject
    @Named(INHERIT)
    AttributeFormPropertyDelegateBind<F, Boolean, BooleanCheckBoxProperty> inheritBindDelegate;
    @Inject
    @Named(INTERVAL_AVAILABLE_UNITS)
    AttributeFormPropertyDelegateBind<F, Collection<SelectItem>, MultiSelectBoxProperty> intervalAvailableUnitsBindDelegate;
    @Inject
    @Named(NEED_STORE_UNITS)
    AttributeFormPropertyDelegateBind<F, Boolean, BooleanCheckBoxInfoProperty> needStoreUnitsBindDelegate;
    @Inject
    @Named(DETERMINER)
    AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxWithEmptyOptProperty> determinerBindDelegate;
    @Inject
    @Named(EXPORT_NDAP)
    AttributeFormPropertyDelegateBind<F, Boolean, BooleanCheckBoxInfoProperty> exportNDAPBindDelegate;
    @Inject
    @Named(SCRIPT)
    AttributeFormPropertyDelegateBind<F, ScriptDto, ScriptComponentEditProperty> scriptBindDelegate;
    @Inject
    @Named(TAGS)
    AttributeFormPropertyDelegateBind<F, Collection<SelectItem>, TagsProperty> tagsBindDelegate;
    @Inject
    @Named(SETTINGS_SET)
    AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxWithEmptyOptProperty> settingsSetBindDelegate;
    @Inject
    @Named(TAGS)
    AttributeFormPropertyDelegateRefresh<F, Collection<SelectItem>, TagsProperty> tagsRefreshDelegate;
    @Inject
    @Named(SETTINGS_SET)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxWithEmptyOptProperty> settingsSetRefreshDelegate;
    @Inject
    @Named(ADVLIST_SEMANTIC_FILTERING)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> advlistSemanticFilteringRefreshDelegate;
    @Inject
    @Named(STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> structuredObjectsViewForBuildingTreeRefreshDelegate;

    protected final Map<Validator<String>, String> codeValidators = new HashMap<>(); // NOSONAR
    private final Map<Validator<String>, String> scriptValidators = new HashMap<>();
    private final Map<Validator<String>, String> genRuleValidators = new HashMap<>();
    private final Map<Validator<String>, String> templateValiators = new HashMap<>();
    private final Map<Validator<Collection<SelectItem>>, String> intervalAvailableUnitsValiators = new HashMap<>();
    private final Map<Validator<RelationsAttrTreeObject>, String> attrChainValiators = new HashMap<>();

    @Inject
    public void setUpValidators(NotEmptyValidator notEmptyValidator, StringLengthValidator lengthValidator,
            NotEmptyCollectionValidator<Collection<SelectItem>> notEmptyCollectionValidator,
            NotNullValidator<RelationsAttrTreeObject> notNullValidator)
    {
        scriptValidators.put(notEmptyValidator, AttributeFormValidationCode.DEFAULT);
        genRuleValidators.put(notEmptyValidator, AttributeFormValidationCode.DEFAULT);
        genRuleValidators.put(lengthValidator.setMaxLength(256), AttributeFormValidationCode.DEFAULT);
        templateValiators.put(notEmptyValidator, AttributeFormValidationCode.DEFAULT);
        intervalAvailableUnitsValiators.put(notEmptyCollectionValidator, AttributeFormValidationCode.DEFAULT);
        attrChainValiators.put(notNullValidator, AttributeFormValidationCode.DEFAULT);
    }

    @Override
    protected void build()
    {
        super.build();

        HashSet<PropertyDelegateValidator<String>> nameRuleVDelegate = new HashSet<>();
        nameRuleVDelegate.add(namingRuleDelegateValidator);

        // @formatter:off
        register(INHERIT, booleanPropertyFactory)
                .setVchDelegate(inheritVCHDelegate)
                .setBindDelegate(inheritBindDelegate);
        register(EXPORT_NDAP, booleanInfoPropertyFactory)
                .setRefreshDelegate(exportNDAPRefreshDelegate)
                .setVchDelegate(exportNDAPVCHDelegate)
                .setBindDelegate(exportNDAPBindDelegate);
        register(RELATED_ATTRS_TO_EXPORT, multiSelectListPropertyFactory)
                .setRefreshDelegate(relatedAttrToExportRefreshDelegate)
                .setValidators(selectListRelatedAttrsToExportValidators);
        register(COMPUTABLE, booleanPropertyFactory)
                .setVchDelegate(computableVCHDelegate)
                .setRefreshDelegate(computableRefreshDelegate);
        register(USE_GEN_RULE, booleanPropertyFactory)
                .setVchDelegate(useGeneratedRuleVCHDelegate)
                .setRefreshDelegate(useGeneratedRuleRefreshDelegate);
        register(GEN_RULE, footedTextPropertyFactory)
                .setVchDelegate(generatedRuleVCHDelegate)
                .setRefreshDelegate(generatedRuleRefreshDelegate)
                .setBindDelegate(generatedRulePropertyDelegate)
                .setValidators(genRuleValidators)
                .setValidatorDelegates(nameRuleVDelegate);
        register(DETERMINABLE, booleanPropertyFactory)
                .setVchDelegate(determinableVCHDelegate)
                .setRefreshDelegate(determinableRefreshDelegate);
        register(NEED_STORE_UNITS, booleanInfoPropertyFactory)
                .setBindDelegate(needStoreUnitsBindDelegate)
                .setVchDelegate(needStoreUnitsVCHDelegate)
                .setRefreshDelegate(needStoreUnitsRefreshDelegate);
        register(INTERVAL_AVAILABLE_UNITS, multiSelectListPropertyFactory)
                .setBindDelegate(intervalAvailableUnitsBindDelegate)
                .setVchDelegate(intervalAvailableUnitsVCHDelegate)
                .setRefreshDelegate(intervalAvailableUnitsRefreshDelegate)
                .setValidators(intervalAvailableUnitsValiators);
        register(COMPLEX_RELATION, listBoxPropertyFactory)
                .setVchDelegate(complexVCHDelegate)
                .setRefreshDelegate(complexRefreshDelegate);
        register(COMPLEX_RELATION_ATTR_GROUP, listBoxPropertyFactory)
                .setRefreshDelegate(complexAttrGroupRefreshDelegate)
                .setValidators(selectListTargetValidators);
        register(COMPLEX_EMPLOYEE_ATTR_GROUP, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(complexAggrAttrGroupRefreshDelegateFactory.create(Employee.FQN, COMPLEX_EMPLOYEE_ATTR_GROUP));
        register(COMPLEX_TEAM_ATTR_GROUP, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(complexAggrAttrGroupRefreshDelegateFactory.create(Team.FQN, COMPLEX_TEAM_ATTR_GROUP));
        register(COMPLEX_OU_ATTR_GROUP, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(complexAggrAttrGroupRefreshDelegateFactory.create(OU.FQN, COMPLEX_OU_ATTR_GROUP));
        register(COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW, listBoxPropertyFactory)
                .setRefreshDelegate(complexStructuredObjectsViewRefreshDelegate)
                .setValidators(selectListTargetValidators);
        register(EDITABLE_IN_LISTS, booleanPropertyFactory)
                .setRefreshDelegate(editableInListsRefreshDelegate);
        register(REQUIRED_IN_INTERFACE, booleanPropertyFactory)
                .setRefreshDelegate(requiredInInterfaceRefreshDelegate);
        register(HAS_GROUP_SEPARATORS, booleanPropertyFactory)
                .setRefreshDelegate(hasGroupSeparatorsRefreshDelegate)
                .setVchDelegate(hasGroupSeparatorsVCHDelegate);
        register(UNIQUE, booleanPropertyFactory)
                .setRefreshDelegate(uniqueRefreshDelegate);
        register(TARGET_TIMER, listBoxPropertyFactory)
                .setRefreshDelegate(targetTimerRefreshDelegate)
                .setValidators(selectListTargetValidators);
        register(SCRIPT, scriptEditorPropertyFactory)
                .setBindDelegate(scriptBindDelegate)
                .setRefreshDelegate(scriptRefreshDelegate);
        register(DETERMINER, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(determinerBindDelegate)
                .setRefreshDelegate(ruleDetermineRefreshDelegate);
        register(COMPOSITE, booleanPropertyFactory)
                .setRefreshDelegate(compositeRefereshDelegate)
                .setVchDelegate(compositeVCHDelegate);
        register(TEMPLATE, footedTextPropertyFactory)
                .setBindDelegate(templatePropertyDelegate)
                .setRefreshDelegate(templateRefereshDelegate)
                .setValidators(templateValiators);
        register(HIDDEN_WHEN_EMPTY, booleanPropertyFactory)
                .setRefreshDelegate(hideWhenEmptyRefreshDelegate);
        register(HIDDEN_WHEN_NO_POSSIBLE_VALUES, booleanPropertyFactory)
                .setRefreshDelegate(hiddenWhenNoPossibleValuesRefreshDelegate);
        register(EDIT_ON_COMPLEX_FORM_ONLY, booleanPropertyFactory)
                .setRefreshDelegate(editOnComplexFormOnlyRefreshDelegate);
        register(HIDE_ARCHIVED, booleanPropertyFactory)
                .setRefreshDelegate(showArchiveRefreshDelegate);
        register(QUICK_ADD_FORM_CODE, listBoxPropertyFactory)
                .setRefreshDelegate(quickAddFormRefreshDelegate);
        register(QUICK_EDIT_FORM_CODE, listBoxPropertyFactory)
                .setRefreshDelegate(quickEditFormRefreshDelegate);
        register(ATTR_CHAIN, popupSelectAttrTreePropertyFactory)
                .setRefreshDelegate(attrChainRefreshDelegate)
                .setVchDelegate(attrChainVCHDelegate)
                .setValidators(attrChainValiators);
        register(ATTR_CHAIN_VIEW, textPropertyFactory)
                .setRefreshDelegate(attrChainViewRefreshDelegate);
        register(RELATED_OBJECT_ATTRIBUTE, listBoxPropertyFactory)
                .setRefreshDelegate(relatedObjectAtributeRefreshDelegate)
                .setVchDelegate(relatedObjectAttributeVCHDelegate)
                .setValidators(selectListTargetValidators);
        register(RELATED_OBJECT_METACLASS, textPropertyFactory)
                .setRefreshDelegate(relatedObjectMetaclassRefreshDelegate);
        register(RELATED_OBJECT_HIERARCHY_LEVEL, listBoxPropertyFactory)
                .setRefreshDelegate(relatedObjectHierarchyLevelRefreshDelegate)
                .setValidators(selectListTargetValidators);
        register(TAGS, tagsPropertyFactory)
                .setBindDelegate(tagsBindDelegate)
                .setRefreshDelegate(tagsRefreshDelegate);
        register(SETTINGS_SET, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(settingsSetBindDelegate)
                .setRefreshDelegate(settingsSetRefreshDelegate);
        register(ADVLIST_SEMANTIC_FILTERING, booleanPropertyFactory)
                .setRefreshDelegate(advlistSemanticFilteringRefreshDelegate);
        register(STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE, listBoxPropertyFactory)
                .setRefreshDelegate(structuredObjectsViewForBuildingTreeRefreshDelegate)
                .setValidators(selectListTargetValidators);
        // @formatter:on
    }
}
