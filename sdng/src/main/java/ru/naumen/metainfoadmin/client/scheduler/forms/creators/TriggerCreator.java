package ru.naumen.metainfoadmin.client.scheduler.forms.creators;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.IFormPropertiesCreator;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfoadmin.client.scheduler.forms.TriggerFormPresenter;

/**
 * Интерфейс для {@link Presenter} , создающего правило задачи планировщика.
 * <p>
 * По сути является частью {@link TriggerFormPresenter формы} добавления правила задачи планировщика.
 *
 * <AUTHOR>
 *
 */
public interface TriggerCreator extends IFormPropertiesCreator
{
    /**
     * Возвращает правило задачи планировщика созданное на основе данных введенных пользователем на форме добавления.
     * Если правило не может
     * быть создано (например, заполнены не все поля), то возвращает null
     *
     * @return созданное правило, или возвращает null если правило не может быть создано
     */
    DtoContainer<Trigger> getTrigger();

    void init(String schTaskCode, @Nullable DtoContainer<Trigger> schTask);

}
