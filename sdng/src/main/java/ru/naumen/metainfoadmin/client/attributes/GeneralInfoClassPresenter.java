package ru.naumen.metainfoadmin.client.attributes;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.client.TabLayoutDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.metainfo.shared.Constants.MetaClassProperties;
import ru.naumen.metainfoadmin.client.AdminTabContainerHasContextPresenter;

/**
 * {@link Presenter} общей информации о классе.
 *
 * <AUTHOR>
 * @since 11.10.2010
 */
public class GeneralInfoClassPresenter extends AdminTabContainerHasContextPresenter
{
    @Inject
    AttributesPresenter attributesPresenter;
    @Inject
    InfoPresenter currentMetaclassInfoPresenter;

    @Inject
    public GeneralInfoClassPresenter(TabLayoutDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    // TODO dzevako это скорее всего не нужно
    @Override
    public void onHide()
    {
        attributesPresenter.hideDisplay();
        currentMetaclassInfoPresenter.hideDisplay();
    }

    // TODO dzevako это скорее всего не нужно
    @Override
    public void onReveal()
    {
        attributesPresenter.revealDisplay();
        currentMetaclassInfoPresenter.revealDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        currentMetaclassInfoPresenter.setMetaClass(getContext().getMetainfo());
        super.refreshDisplay();
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        // TODO dzevako тут просто передавать контекст
        currentMetaclassInfoPresenter.init(getContext().getMetainfo(),
                getContext().getContextProperty(Tag.ELEMENT_TAGS),
                getContext().getContextProperty(MetaClassProperties.QUOTING_PROPERTIES),
                getContext().getPermissions());
        addContent(currentMetaclassInfoPresenter, "metaClassInfo");

        attributesPresenter.init(getContext());
        addContent(attributesPresenter, "attributesList");

        refreshDisplay();
    }

    // TODO dzevako это скорее всего не нужно
    @Override
    protected void onUnbind()
    {
        attributesPresenter.unbind();
        currentMetaclassInfoPresenter.unbind();
    }
}
