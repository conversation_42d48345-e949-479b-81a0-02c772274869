package ru.naumen.metainfoadmin.client.attributes;

import java.util.List;
import java.util.Objects;

import jakarta.annotation.Nullable;

import com.google.gwt.event.shared.GwtEvent;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Событие изменения набора меток класса.
 * <AUTHOR>
 * @since Feb 18, 2019
 */
public class MetaClassTagsChangedEvent extends GwtEvent<MetaClassTagsChangedHandler>
{
    public static final Type<MetaClassTagsChangedHandler> TYPE = new Type<>();

    private final ClassFqn classFqn;
    private final List<DtObject> tags;

    public MetaClassTagsChangedEvent(ClassFqn classFqn, List<DtObject> tags)
    {
        this.classFqn = classFqn;
        this.tags = tags;
    }

    @Override
    public Type<MetaClassTagsChangedHandler> getAssociatedType()
    {
        return TYPE;
    }

    @Override
    protected void dispatch(MetaClassTagsChangedHandler handler)
    {
        handler.onMetaClassTagsChanged(this);
    }

    public ClassFqn getClassFqn()
    {
        return classFqn;
    }

    public List<DtObject> getTags()
    {
        return tags;
    }

    public boolean isSuitableForContext(@Nullable Context context)
    {
        return null != context && null != context.getMetainfo()
               && Objects.equals(context.getMetainfo().getFqn(), getClassFqn());
    }
}
