package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

/**
 * Провайдер виджетов для колонки "Ссылается на" в списке атрибутов на карточке метакласса. 
 *
 * <AUTHOR>
 * @since 31 июл. 2018 г.
 *
 */
public class LinkedToColumnAttrTypeWidgetsProvider implements Provider<List<AttrTypeColumnWidget>>
{
    @Inject
    AttrLinkedToBOWidget boWidget;
    @Inject
    AttrLinkedToBackLinkWidget backLinkWidget;
    @Inject
    AttrLinkedToTimerWidget timerWidget;
    @Inject
    AttrLinkedToCatalogWidget catalogItemsWidget;
    @Inject
    AttrLinkedToCaseListWidget caseListWidget;

    @Override
    public List<AttrTypeColumnWidget> get()
    {
        return Arrays.asList(boWidget, backLinkWidget, timerWidget, catalogItemsWidget, caseListWidget);
    }

}
