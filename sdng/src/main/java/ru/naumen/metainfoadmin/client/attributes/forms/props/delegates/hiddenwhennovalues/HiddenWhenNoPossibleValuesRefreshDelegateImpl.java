package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hiddenwhennovalues;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.METAINFO;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.CODE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.EDITABLE;

import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.impl.AttributePropertyUtils;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

/**
 * Делегат обновления свойства "Скрывать при редактировании, если нет значений для выбора".
 * <AUTHOR>
 * @since Jul 31, 2017
 */
@Singleton
public class HiddenWhenNoPossibleValuesRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeCode = context.getPropertyValues().getProperty(ATTR_TYPE);
        MetaClass metaClass = context.getContextValues().getProperty(METAINFO);
        String attrCode = context.getPropertyValues().getProperty(CODE);
        boolean isEditable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(EDITABLE))
                             && !Boolean.TRUE.equals(context.getPropertyValues().getProperty(COMPUTABLE));
        boolean canBeHidden = AttributePropertyUtils.canBeHiddenWhenNoPossibleValues(typeCode, metaClass.getFqn(),
                attrCode, isEditable);
        callback.onSuccess(canBeHidden);
    }
}
