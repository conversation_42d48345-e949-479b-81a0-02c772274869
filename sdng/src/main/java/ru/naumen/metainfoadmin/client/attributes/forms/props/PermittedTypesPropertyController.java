package ru.naumen.metainfoadmin.client.attributes.forms.props;

import java.util.function.Function;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * PropertyController для свойства, обрабатывающего ограничения по типам. 
 * У него виджет - дерево, при изменении типа надо генерить заново, поэтому приходится делать это при обновлении
 * свойства.
 * Поэтому оно не вписывается в обычную архитектуру и реализовано отдельно. 
 * <AUTHOR>
 * @since 31.05.2012
 */
public interface PermittedTypesPropertyController<F extends ObjectForm>
{
    Function<PropertyContainerContext, ClassFqn> PERMITTED_TYPES_CLASS_FQN_EXTRACTOR =
            new Function<PropertyContainerContext, ClassFqn>()
            {
                @Override
                public ClassFqn apply(PropertyContainerContext context)
                {
                    if (context == null)
                    {
                        return ClassFqn.parse("");
                    }
                    String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
                    if (BackLinkAttributeType.CODE.equals(attrType))
                    {
                        if (context.getContextValues().getProperty(AttributeFormContextValues.DIRECT_LINK_METAINFO)
                            == null)
                        {
                            //Если на этот метакласс нет вообще ни одной ссылки
                            return ClassFqn.parse("");
                        }
                        return context.getContextValues()
                                .<MetaClass> getProperty(AttributeFormContextValues.DIRECT_LINK_METAINFO).getFqn();
                    }
                    else
                    {
                        String targetClass = context.getPropertyValues()
                                .getProperty(AttributeFormPropertyCode.TARGET_CLASS);
                        return targetClass != null ? ClassFqn.parse(targetClass) : ClassFqn.parse("");
                    }
                }
            };
}
