package ru.naumen.metainfoadmin.client.embeddedapplications.form;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.script.places.OtherCategories;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.widgets.script.component.ScriptComponentEditWidget;

public class ApplicationWithScriptPropertiesProvider implements AdditionalApplicationPropertiesProvider
{
    @Inject
    protected CommonMessages cmessages;

    @Inject
    @Named(PropertiesGinModule.SCRIPT_COMPONENT_EDIT)
    Property<ScriptDto> script;
    private PropertyRegistration<ScriptDto> scriptPR;

    @Inject
    protected Processor validation;

    protected static final ScriptCategory scriptCategory = OtherCategories.APPLICATION;

    @Override
    public void addAdditionalProperties(PropertyDialogDisplay display, PropertyRegistration<?> previousProperty,
            RegistrationContainer registrationContainer)
    {
        script.setCaption(cmessages.script());
        ScriptComponentEditWidget scriptWidget = (ScriptComponentEditWidget)script.getValueWidget();
        scriptWidget.init(false, scriptCategory, display);
        scriptWidget.initValidation(validation);
        scriptWidget.enableValidation();
        scriptPR = display.addProperty(script, display.getPropertiesCount());
        DebugIdBuilder.ensureDebugId(script, "edit-script");
    }

    @Override
    public EmbeddedApplication createApplication()
    {
        return null;
    }

    @Override
    public void fillAdditionalProperties(EmbeddedApplicationAdminSettingsDto embeddedApplication)
    {
        if (embeddedApplication.getScriptDto() != null)
        {
            script.setValue(embeddedApplication.getScriptDto());
        }

    }

    @Override
    public Boolean getValueOfAdditionalProperties(EmbeddedApplicationAdminSettingsDto embeddedApplication)
    {
        ScriptDto scriptDto = script.getValue();
        if (scriptDto != null)
        {
            embeddedApplication.setScriptDto(scriptDto);
            embeddedApplication.setScript(scriptDto.getCode());
        }
        return null;
    }

    @Override
    public void removeAdditionalProperties()
    {
        if (scriptPR != null)
        {
            scriptPR.unregister();
            scriptPR = null;
        }
    }

}
