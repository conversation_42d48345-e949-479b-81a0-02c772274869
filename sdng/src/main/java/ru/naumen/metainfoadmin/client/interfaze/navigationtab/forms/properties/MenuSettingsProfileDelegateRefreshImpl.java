package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.ResultProfilesDTO;
import ru.naumen.core.shared.navigationsettings.ResultProfilesDTO.ResultProfilesType;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.RoleUtils;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityProfilesAction;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityProfilesResponse;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemContextValueCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 27.07.2020
 */
public class MenuSettingsProfileDelegateRefreshImpl
        implements PropertyDelegateRefresh<Collection<SelectItem>, MultiSelectBoxProperty>
{
    private final DispatchAsync dispatch;
    private final MetainfoUtils metainfoUtils;
    private final NavigationSettingsMessages messages;

    @Inject
    public MenuSettingsProfileDelegateRefreshImpl(
            final DispatchAsync dispatch,
            final MetainfoUtils metainfoUtils,
            final NavigationSettingsMessages messages)
    {
        this.dispatch = dispatch;
        this.metainfoUtils = metainfoUtils;
        this.messages = messages;
    }

    @Override
    public void refreshProperty(final PropertyContainerContext context, final MultiSelectBoxProperty property,
            final AsyncCallback<Boolean> callback)
    {
        ResultProfilesDTO parentProfiles = context.getContextValues()
                .<ResultProfilesDTO> getProperty(
                        MenuItemContextValueCode.PARENT_PROFILES);

        Set<String> parentProfilesCodes =
                parentProfiles != null && ResultProfilesType.LIST.equals(parentProfiles.getType()) ?
                        parentProfiles.getProfiles().stream().map(DtObject::getUUID).collect(Collectors.toSet())
                        : new HashSet<>();

        if (parentProfiles == null || ResultProfilesType.ALL.equals(parentProfiles.getType()))
        {
            property.setDescription(messages.allProfilesDescription());
        }
        else
        {
            property.setDescription(messages.listOfProfilesDescription());
        }

        final Predicate<Profile> parentProfilePredicate;
        if (parentProfiles != null && ResultProfilesType.LIST.equals(parentProfiles.getType()))
        {
            parentProfilePredicate = profile -> parentProfilesCodes.contains(profile.getCode());
        }
        else if (parentProfiles == null || ResultProfilesType.ALL.equals(parentProfiles.getType()))
        {
            parentProfilePredicate = profile -> true;
        }
        else
        {
            parentProfilePredicate = profile -> false;
        }

        final Predicate<Profile> absoluteRoleProfilePredicate = profile -> !RoleUtils.hasOnlyRelativeRole(
                profile.getRoles());

        final Collection<String> selectedProfiles = context.getPropertyValues()
                .<Collection<String>> getProperty(MenuSettingsPropertyCode.PROFILES);

        dispatch.execute(new GetSecurityProfilesAction(null, false), new BasicCallback<GetSecurityProfilesResponse>()
        {
            @Override
            protected void handleSuccess(GetSecurityProfilesResponse value)
            {
                property.getValueWidget().clear();
                List<Profile> profiles = value.get();
                metainfoUtils.sort(profiles);
                profiles.stream().filter(parentProfilePredicate)
                        .filter(absoluteRoleProfilePredicate)
                        .forEach(p -> property.getValueWidget().addItem(p.getTitle(), p.getCode()));
                property.trySetObjValue(selectedProfiles);
                context.getPropertyValues()
                        .setProperty(MenuSettingsPropertyCode.PROFILES,
                                selectedProfiles);
                callback.onSuccess(true);
            }
        });
    }
}