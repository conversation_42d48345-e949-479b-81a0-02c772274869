package ru.naumen.metainfoadmin.client.structuredobjectsviews;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.STRUCTURES;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminSingleTabAdvlistPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;

/**
 * Презентер вкладки структуры
 * <AUTHOR>
 * @since 11.10.2019
 */
public class StructuredObjectsViewsPresenter extends
        AdminSingleTabAdvlistPresenterBase<StructuredObjectsViewsPlace, DtObject>
{
    @Inject
    public StructuredObjectsViewsPresenter(AdminTabDisplay display, EventBus eventBus,
            StructuredObjectsViewsListPresenter advlistPresenter)
    {
        super(display, eventBus, advlistPresenter);
    }

    @Override
    protected String getTitle()
    {
        return messages.structuredObjectsViews();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return STRUCTURES;
    }
}
