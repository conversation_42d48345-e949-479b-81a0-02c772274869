package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attrType;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.METAINFO;

import java.util.Map.Entry;
import java.util.function.Predicate;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.attr.AvailibleTypesProvider;
import ru.naumen.core.client.widgets.WidgetStyleUpdater;
import ru.naumen.core.client.widgets.WidgetStyleUpdater.WidgetTypeCode;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.BackTimerAttributeType;
import ru.naumen.metainfo.shared.Constants.TimerAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;

/**
 * Делегат, описывающий биндинг свойства "Типы атрибутов" - заполняет его доступными типами
 * <AUTHOR>
 * @since 17.05.2012
 */
public class AttrTypeBindDelegateImpl<F extends ObjectForm>
        extends PropertyDelegateBindImpl<SelectItem, ListBoxProperty>
        implements AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxProperty>
{
    /**
     * Предикат, определяющий возможность добавления атрибута определенного типа 
     * для конкретного метакласса
     *
     * <AUTHOR>
     * @since 12 авг. 2016 г.
     */
    private static class AttributeTypeAllowed implements Predicate<Entry<String, String>>
    {
        private final MetaClass metaClass;

        public AttributeTypeAllowed(MetaClass metaClass)
        {
            this.metaClass = metaClass;
        }

        @Override
        public boolean test(Entry<String, String> type)
        {
            return !Comment.FQN.isSameClass(metaClass.getFqn()) || !BackLinkAttributeType.CODE.equals(type.getValue())
                                                                   && !TimerAttributeType.CODE.equals(type.getValue())
                                                                   && !BackTimerAttributeType.CODE.equals(
                    type.getValue());
        }

    }

    @Inject
    private AvailibleTypesProvider<F> typesProvider;
    @Inject
    private WidgetStyleUpdater styleUpdater;

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxProperty property, AsyncCallback<Void> callback)
    {
        final SingleSelectCellList<?> selList = property.getValueWidget();
        styleUpdater.setFocusAndBlurHandlers(WidgetTypeCode.SIMPLE, selList);
        styleUpdater.setValidationHandler(WidgetTypeCode.SIMPLE, selList);
        typesProvider.getTypes().stream()
                .filter(new AttributeTypeAllowed(context.getContextValues().<MetaClass> getProperty(METAINFO)))
                .forEach(type -> selList.addItem(type.getKey(), type.getValue()));
        super.bindProperty(context, property, callback);
    }
}
