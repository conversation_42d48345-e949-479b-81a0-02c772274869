/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.columns;

import static ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode.*;

import java.util.Map;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.HashMap;

import com.google.inject.name.Named;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.grouplist.GroupList.ColumnInfo;
import ru.naumen.core.client.widgets.grouplist.WidgetCreator;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributesListType;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;

/**
 * <AUTHOR>
 *
 */
@Singleton
public class AttributeListColumnInfoFactoryImpl implements AttributeListColumnInfoFactory
{
    private final AttributeListColumnFactory columnFactory;

    private final Map<String, ColumnInfo<Attribute>> columnInfos;

    @Inject
    private CommandFactory commandFactory;

    @Inject
    public AttributeListColumnInfoFactoryImpl(AttributesMessages messages, CommonMessages cmessages,
            @Named(AttributesListType.STANDARD) AttributeListColumnFactory columnFactory)
    {
        this.columnFactory = columnFactory;

        columnInfos = new HashMap<>();
        columnInfos.put(TITLE, new ColumnInfo<Attribute>(cmessages.title(),
                AdminWidgetResources.INSTANCE.tables().tableElemsTDLeft(), "26%", TITLE, null));
        columnInfos.put(CODE, new ColumnInfo<Attribute>(cmessages.code(), "", "9%", CODE, null));
        columnInfos.put(TYPE, new ColumnInfo<Attribute>(cmessages.typeOfValue(), "", "9%", TYPE, null));
        columnInfos.put(EDITABLE, new ColumnInfo<Attribute>(messages.editable(),
                WidgetResources.INSTANCE.all().alignCenter(), "9%", EDITABLE, null));
        columnInfos.put(REQUIRED, new ColumnInfo<Attribute>(messages.mandatory(),
                WidgetResources.INSTANCE.all().alignCenter(), "9%", REQUIRED, null));
        columnInfos.put(UNIQUE, new ColumnInfo<Attribute>(messages.unique(),
                WidgetResources.INSTANCE.all().alignCenter(), "9%", UNIQUE, null));
        columnInfos.put(DEFAULT_VALUE, new ColumnInfo<Attribute>(cmessages.defaultValue(),
                WidgetResources.INSTANCE.all().breakWord(), "", DEFAULT_VALUE, null));
        columnInfos.put(SHOW_USAGE_BUTTON, new ColumnInfo<Attribute>(
                "", WidgetResources.INSTANCE.root().tableElemsTDIcons(), "12px", SHOW_USAGE_BUTTON, null));
        columnInfos.put(EDIT_BUTTON, new ColumnInfo<Attribute>("", WidgetResources.INSTANCE.root().tableElemsTDIcons(),
                "12px", EDIT_BUTTON, null));
        columnInfos.put(DELETE_BUTTON, new ColumnInfo<Attribute>("",
                WidgetResources.INSTANCE.root().tableElemsTDIcons(), "12px", DELETE_BUTTON, null));

        columnInfos.put(MOVE_UP_BUTTON, new ColumnInfo<Attribute>("", "", "12px", MOVE_UP_BUTTON, null));
        columnInfos.put(MOVE_DOWN_BUTTON, new ColumnInfo<Attribute>("", "", "12px", MOVE_DOWN_BUTTON, null));
    }

    @Override
    public ColumnInfo<Attribute> create(String code, RegistrationContainer registrationContainer)
    {
        WidgetCreator<Attribute> column = columnFactory.get(code, registrationContainer);
        if (null == column)
        {
            return null;
        }
        return buildColumnInfo(code, column);
    }

    @Override
    public ColumnInfo<Attribute> createCommandColumn(String code, AttributeCommandParam param, String cmd)
    {
        return createCommandColumn(code, commandFactory.create(cmd, param), param.getContext());
    }

    @Override
    public ColumnInfo<Attribute> createCommandColumn(String code, BaseCommand<?, ?> command, Context context)
    {
        WidgetCreator<Attribute> column = columnFactory.getButtonColumn(code, command, context);
        if (null == column)
        {
            return null;
        }
        return buildColumnInfo(code, column);
    }

    private ColumnInfo<Attribute> buildColumnInfo(String code, WidgetCreator<Attribute> column)
    {
        ColumnInfo<Attribute> source = columnInfos.get(code);
        if (null == source)
        {
            return null;
        }
        ColumnInfo<Attribute> result = new ColumnInfo<>(source);
        result.setColumn(column);
        return result;
    }
}
