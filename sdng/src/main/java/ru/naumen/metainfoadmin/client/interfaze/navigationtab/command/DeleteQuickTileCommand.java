package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelElementWrapper;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.dispatch.DeleteQuickAccessTileAction;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;

/**
 * Команда удаления плитки быстрого доступа
 *
 * <AUTHOR>
 * @since 17.07.2020
 */
public class DeleteQuickTileCommand extends BaseCommandImpl<QuickAccessPanelElementWrapper,
        DtoContainer<NavigationSettings>>
{
    public static final String ID = "deleteQuickTileCommand";

    @Inject
    private DispatchAsync dispatch;
    @Inject
    private Dialogs dialogs;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private NavigationSettingsMessages messages;
    @Inject
    private MetainfoUtils metainfoUtils;

    @Inject
    public DeleteQuickTileCommand(@Assisted QuickAccessPanelTileCommandParam param)
    {
        super(param);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    public void execute(CommandParam<QuickAccessPanelElementWrapper, DtoContainer<NavigationSettings>> cparam)
    {
        final QuickAccessPanelTileCommandParam p = (QuickAccessPanelTileCommandParam)prepareParam(cparam);
        dialogs.question(cmessages.confirmDelete(), question(p), null, new DialogCallback()
        {
            @Override
            public void handleSuccess(Dialogs.DialogResult result)
            {
                result.getWidget().hide();
                if (Dialogs.Buttons.YES.equals(result.getButtons()))
                {
                    yesDelete(p);
                }
                else
                {
                    cancelDelete(p);
                }
            }
        });
    }

    @Override
    public boolean isPossible(Object input)
    {
        return input instanceof QuickAccessPanelElementWrapper && ((QuickAccessPanelElementWrapper)input)
                .getWrappable() instanceof QuickAccessTileDTO;
    }

    private static void cancelDelete(QuickAccessPanelTileCommandParam param)
    {
        param.getCallback().onSuccess(null);
    }

    protected String question(final QuickAccessPanelTileCommandParam p)
    {
        return cmessages.confirmDeleteQuestion(messages.quickTileAccus(), getTitle(p.getValue()));
    }

    protected void yesDelete(final QuickAccessPanelTileCommandParam param)
    {
        QuickAccessTileDTO value = (QuickAccessTileDTO)param.getValue().getWrappable();

        DeleteQuickAccessTileAction action = new DeleteQuickAccessTileAction();
        action.setTile(value);
        dispatch.execute(action, new SimpleResultCallbackDecorator<>(param.getCallback()));
    }

    private String getTitle(QuickAccessPanelElementWrapper wrapper)
    {
        return wrapper.getWrappable() instanceof QuickAccessTileDTO
                ? metainfoUtils.getLocalizedValue(((QuickAccessTileDTO)wrapper.getWrappable()).getHint())
                : "";
    }
}