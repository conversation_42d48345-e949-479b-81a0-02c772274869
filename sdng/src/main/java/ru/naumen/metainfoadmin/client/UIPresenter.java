package ru.naumen.metainfoadmin.client;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.USER_INTERFACE;

import java.util.logging.Logger;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceChangeRequestEvent;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.TabLayoutDisplay;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextualCallback;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolBarDisplay;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonUtils;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.client.events.DeleteAttributeEvent;
import ru.naumen.metainfo.client.events.DeleteAttributeHandler;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.dispatch2.GetFormActionResponse;
import ru.naumen.metainfo.shared.templates.ui.UITemplateUtils;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.metainfoadmin.client.commands.UICommandCode;
import ru.naumen.metainfoadmin.client.commands.UICommandParam;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.dynadmin.BasicUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeUITemplateModeEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ReloadUIEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ReloadUIHandler;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UITemplateContext;
import ru.naumen.metainfoadmin.client.templates.ui.UITemplateHelper;
import ru.naumen.metainfoadmin.client.templates.ui.UITemplateUserSettingsStorage;

/**
 * Презентер карточки (формы) метакласса
 *
 * <AUTHOR>
 *
 */
public class UIPresenter extends AdminTabContainerPresenter implements DeleteAttributeHandler,
        PlaceChangeRequestEvent.Handler, ReloadUIHandler
{
    private static Logger LOG;

    @Inject
    MetainfoServiceAsync metainfoService;
    @Inject
    AdminContentFactory factory;
    @Inject
    AdminDialogMessages messages;
    @Inject
    ButtonFactory buttonFactory;
    @Inject
    ButtonUtils buttonUtils;
    @Inject
    UITemplateHelper templateHelper;
    @Inject
    IconsCssLoader iconsCssLoader;

    @Inject
    private UITemplateUserSettingsStorage userSettingsStorage;
    @Inject
    private CommonMessages commonMessages;
    protected ToolBarDisplayMediator<ContentInfo> toolBar;
    private String formId;
    private Context context;

    protected Presenter contentPresenter;
    private UIContext contentContext;
    private ContentInfo contentInfo;
    private UICommandParam commandParam;
    private ButtonToolBarDisplay toolBarDisplay;

    /**
     * Показывает, что контент уже проинициализирован или находится в стадии инициализации. Требуется т.к. инициализация
     * происходит не во время вызова bind(), а во время первого отображения на экран (выбора вкладки)
     */
    private boolean initialized = false;

    @Inject
    public UIPresenter(TabLayoutDisplay display, EventBus eventBus,
            ButtonToolBarDisplay toolBarDisplay, AdminWidgetResources resources)
    {
        super(display, eventBus);
        if (null == LOG)
        {
            LOG = Logger.getLogger("UIPresenter");
        }
        this.toolBarDisplay = toolBarDisplay;
        getDisplay().setToolBar(toolBarDisplay);
        toolBar = new ToolBarDisplayMediator<ContentInfo>(toolBarDisplay);

        resources.metainfoAdmin().ensureInjected();
    }

    public void init(Context context, String formId)
    {
        this.formId = formId;
        this.context = context;
    }

    @Override
    public void onDeleteAttribute(DeleteAttributeEvent event)
    {
        initialized = false;
        if (isRevealed)
        {
            onReveal();
        }
    }

    @Override
    public void onHide()
    {
        if (null != contentPresenter)
        {
            LOG.finest("Hide");
            contentPresenter.hideDisplay();
        }

        templateHelper.setTemplateMode(false);
    }

    @Override
    public void onPlaceChangeRequest(PlaceChangeRequestEvent event)
    {
        UITemplateContext templateContext = null == contentContext ? null : contentContext.getUITemplateContext();
        if (null != templateContext && templateContext.isTemplateChanged())
        {
            event.setWarning(commonMessages.doYouReallyWantToLeaveThePage());
        }
    }

    @Override
    public void onReveal()
    {
        if (!initialized)
        {
            initialized = true;
            metainfoService.getForm(context.getMetainfo().getFqn(), formId, new BasicCallback<GetFormActionResponse>(
                    getDisplay())
            {
                @Override
                protected void handleSuccess(GetFormActionResponse value)
                {
                    init(value.getContentInfo(), value.getPermissions());
                }
            });
        }

        if (null != contentPresenter)
        {
            LOG.finest("Reveal");
            contentPresenter.revealDisplay();
        }

        UITemplateContext templateContext = null == contentContext ? null : contentContext.getUITemplateContext();
        if (null != templateContext)
        {
            templateHelper.setTemplateMode(true);
        }
    }

    @Override
    public void onUIReloaded(ReloadUIEvent event)
    {
        Content content = event.getContentInfo().getContent();
        UITemplateContext templateContext = contentContext.getUITemplateContext();
        if (null != templateContext)
        {
            templateContext.getReducedContents().clear();
            if (content instanceof Window)
            {
                TabBar rootTabBar = UITemplateUtils.getRootTabBar(((Window)content).getTabBar());
                if (rootTabBar.getTab().size() == 1)
                {
                    templateContext.getReducedContents().add(rootTabBar.getUuid());
                }
                Content currentContent = rootTabBar.getParent();
                while (null != currentContent)
                {
                    templateContext.getReducedContents().add(currentContent.getUuid());
                    currentContent = currentContent.getParent();
                }
            }
        }
        init(event.getContentInfo(), null);
    }

    @Override
    public void refreshDisplay()
    {
        toolBar.refresh(contentInfo);
        if (contentPresenter != null)
        {
            contentPresenter.refreshDisplay();
        }
    }

    protected void clearChild()
    {
        if (null != contentPresenter)
        {
            contentPresenter.unbind();
        }
        if (null != contentContext)
        {
            contentContext.destroy();
        }
    }

    protected void init(ContentInfo contentInfo, @Nullable PermissionHolder permissionHolder)
    {
        clearChild();

        boolean isInherit = !context.getMetainfo().getFqn().equals(contentInfo.getDeclaredMetaclass());
        boolean isCase = context.getMetainfo().getFqn().isCase();

        this.contentInfo = contentInfo;
        commandParam.setValue(contentInfo);
        if (isCase)
        {
            toolBar.refresh(contentInfo);
        }
        else
        {
            toolBarDisplay.asWidget().setVisible(false);
        }
        boolean editable = !isInherit || !isCase;

        UIContext oldContext = contentContext;
        BasicUIContext newContentContext = new BasicUIContext(context, contentInfo, editable);
        newContentContext.setPermissions(permissionHolder != null ? permissionHolder : oldContext.getPermissions());
        contentContext = newContentContext;
        if (null != oldContext)
        {
            contentContext.setUITemplateContext(oldContext.getUITemplateContext());
        }
        registerHandler(contentContext.getEventBus().addHandler(ReloadUIEvent.TYPE, this));
        registerHandler(contentContext.getEventBus().addHandler(ChangeUITemplateModeEvent.TYPE,
                event ->
                {
                    templateHelper.setTemplateMode(null != event.getTemplate());
                    Scheduler.get().scheduleDeferred(
                            () -> toolBarDisplay.asWidget().setVisible(isCase && event.getTemplate() == null));
                    if (null != event.getTemplate())
                    {
                        userSettingsStorage.setCurrentTemplate(contentContext,
                                event.isNew() ? null : event.getTemplate().getCode());
                    }
                }));

        factory.build(contentInfo.getContent(), contentContext,
                new ContextualCallback<ContentPresenter<Content, UIContext>>(contentContext, getDisplay())
                {
                    @Override
                    protected void handleSuccess(ContentPresenter<Content, UIContext> value)
                    {
                        contentPresenter = value;
                        // Установка содержимого карточки (возможно, лучше заменить на презентеры)
                        getDisplay().addNoDecoratedContentDisplay(contentPresenter.getDisplay(), "metaClassCard");
                        if (isRevealed)
                        {
                            LOG.finest("Reveal");
                            contentPresenter.revealDisplay();
                        }
                    }
                });
        iconsCssLoader.loadIcons();
    }

    @Override
    protected void onBind()
    {
        bindToolBar();
        registerHandler(eventBus.addHandler(DeleteAttributeEvent.TYPE, this));
        registerHandler(eventBus.addHandler(PlaceChangeRequestEvent.TYPE, this));
    }

    @Override
    protected void onUnbind()
    {
        templateHelper.setTemplateMode(false);
        toolBar.unbind();
        clearChild();
    }

    @SuppressWarnings("unchecked")
    private void bindToolBar()
    {
        BasicCallback<ContentInfo> callback = new BasicCallback<ContentInfo>()
        {
            @Override
            protected void handleSuccess(ContentInfo value)
            {
                init(value, null);
            }
        };
        commandParam = new UICommandParam(contentInfo, callback, context);
        ButtonPresenter<ContentInfo> resetBtn = (ButtonPresenter<ContentInfo>)buttonFactory.create(ButtonCode.REFRESH,
                messages.resetUI(),
                UICommandCode.RESET_UI, commandParam);
        resetBtn.addPossibleFilter(buttonUtils.isShowResetButtonsPredicate());
        toolBar.add(resetBtn);
        toolBar.add((ButtonPresenter<ContentInfo>)buttonFactory.create(ButtonCode.EDIT, messages.editUI(),
                UICommandCode.EDIT_UI, commandParam));
        toolBar.bind();
        toolBar.refresh(contentInfo);
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return USER_INTERFACE;
    }
}
