package ru.naumen.metainfoadmin.client.dynadmin;

import ru.naumen.core.client.content.Context;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие посылаемое при изменении {@link UIContext#isEditable() признака возможности редактирования} формы/
 * карточки объекта
 * <p>
 * Событие посылается в {@link Context#getEventBus() EventBus} контекста
 *
 * <AUTHOR>
 *
 */
public class ChangeContextEditableEvent extends GwtEvent<ChangeContextEditableHandler>
{
    private static final Type<ChangeContextEditableHandler> TYPE = new Type<ChangeContextEditableHandler>();

    public static Type<ChangeContextEditableHandler> getType()
    {
        return TYPE;
    }

    private final UIContext context;

    public ChangeContextEditableEvent(UIContext context)
    {
        this.context = context;
    }

    @Override
    public Type<ChangeContextEditableHandler> getAssociatedType()
    {
        return TYPE;
    }

    public UIContext getContext()
    {
        return context;
    }

    @Override
    protected void dispatch(ChangeContextEditableHandler handler)
    {
        handler.onChangeEditable(this);
    }
}
