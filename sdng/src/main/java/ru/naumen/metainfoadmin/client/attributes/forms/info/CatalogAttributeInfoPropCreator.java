package ru.naumen.metainfoadmin.client.attributes.forms.info;

import java.util.Objects;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.IHyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.utils.CIUtils;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.CatalogPlace;
import ru.naumen.metainfo.shared.elements.Catalog;

/**
 * Создает {@link Property} для отображения информации о
 * справочнике на модальной форме свойств атрибута 
 *
 * <AUTHOR>
 * @since 3 авг. 2018 г.
 */
public class CatalogAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    protected AdminMetainfoServiceAsync metainfoService;

    @Override
    protected void createInt(String code)
    {
        String catalogFqn = Objects.requireNonNull(propertyValues.getProperty(code));
        String catalogCode = CIUtils.getCatalogCode(catalogFqn);
        if (StringUtilities.isEmptyTrim(catalogCode))
        {
            return;
        }
        createInt(code, catalogCode);
    }

    private String createHyperLink(String code, String title)
    {
        String url = "#" + CatalogPlace.PLACE_PREFIX + ":" + code;
        IHyperlink link = new Hyperlink(title + " (" + code + ")",
                url);
        return link.toString();
    }

    private void createInt(String code, String catalogCode)
    {
        metainfoService.getCatalog(catalogCode, new BasicCallback<DtoContainer<Catalog>>(rs)
        {
            @Override
            protected void handleSuccess(DtoContainer<Catalog> catalog)
            {
                createProperty(code, createHyperLink(catalogCode, catalog.get().getTitle()));
            }
        });
    }
}
