package ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import com.google.gwt.user.client.ui.IsWidget;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.edit.ExampleWidgetFactoryEditAggregateSingle;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.edit.ExampleWidgetFactoryEditBusinessObject;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.edit.ExampleWidgetFactoryEditBusinessObjects;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.edit.ExampleWidgetFactoryEditCaseList;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.edit.ExampleWidgetFactoryEditCatalogItem;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.edit.ExampleWidgetFactoryEditCatalogItems;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.edit.ExampleWidgetFactoryEditResponsible;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.view.ExampleWidgetFactoryBusinessObject;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.view.ExampleWidgetFactoryBusinessObjects;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.view.ExampleWidgetFactoryCaseList;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.view.ExampleWidgetFactoryCatalogItem;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.view.ExampleWidgetFactoryMetaClass;

/**
 * <AUTHOR>
 * @since Jun 22, 2015
 */
@Singleton
public class ExampleWidgetFactoriesImpl implements ExampleWidgetFactories
{
    private final Provider<ExampleWidgetFactoryImpl<?, ? extends IsWidget>> defaultFactory;
    private final Map<String, Provider<? extends ExampleWidgetFactory<?, ?>>> factories = new HashMap<>();

    @Inject
    protected ExampleWidgetFactoriesImpl(
            Provider<ExampleWidgetFactoryImpl<?, ? extends IsWidget>> defaultFactory,

            //view
            Provider<ExampleWidgetFactoryBusinessObject> exampleWidgetBO,
            Provider<ExampleWidgetFactoryBusinessObjects> exampleWidgetBOs,
            Provider<ExampleWidgetFactoryCaseList> exampleWidgetCaseList,
            Provider<ExampleWidgetFactoryCatalogItem<DtObject>> exampleWidgetCatalogItem,
            Provider<ExampleWidgetFactoryCatalogItem<Collection<DtObject>>> exampleWidgetCatalogItems,
            Provider<ExampleWidgetFactoryMetaClass> exampleWidgetMetaClass,

            //edit
            Provider<ExampleWidgetFactoryEditResponsible> responsibleListEdit,
            Provider<ExampleWidgetFactoryEditAggregateSingle> aggregateEditSingle,
            Provider<ExampleWidgetFactoryEditBusinessObjects> businessObjects,
            Provider<ExampleWidgetFactoryEditCatalogItem> catalogItem,
            Provider<ExampleWidgetFactoryEditCatalogItems> catalogItems,
            Provider<ExampleWidgetFactoryEditCaseList> caseEdit,
            Provider<ExampleWidgetFactoryEditBusinessObject> businessObject)
    {
        this.defaultFactory = defaultFactory;

        //view
        register(Presentations.AGGREGATE_VIEW, exampleWidgetBO);
        register(Presentations.BO_REFERENCE, exampleWidgetBO);
        register(Presentations.BO_LINKS_VIEW, exampleWidgetBOs);
        register(Presentations.MULTI_CLASS_OBJ_VIEW, exampleWidgetBOs);
        register(Presentations.COMMENT_OBJECTS_VIEW, exampleWidgetBOs);
        register(Presentations.CASE_LIST_VIEW, exampleWidgetCaseList);
        register(Presentations.CATALOG_ITEM_TITLE_VIEW, exampleWidgetCatalogItem);
        register(Presentations.CATALOG_ITEMS_VIEW, exampleWidgetCatalogItems);
        register(Presentations.FILE_DOWNLOAD, exampleWidgetBOs);
        register(Presentations.METACLASS_VIEW, exampleWidgetMetaClass);
        register(Presentations.LINKED_CLASSES_VIEW, exampleWidgetCaseList);

        //edit
        register(Presentations.RESPONSIBLE_LIST_EDIT, responsibleListEdit);
        register(Presentations.AGGREGATE_EDIT_ALL_TYPES, aggregateEditSingle);
        register(Presentations.AGGREGATE_EDIT_WITH_FOLDERS, aggregateEditSingle);
        register(Presentations.AGGREGATE_EDIT, aggregateEditSingle);
        register(Presentations.RESPONSIBLE_TREE_EDIT_ALL_TYPES, aggregateEditSingle);
        register(Presentations.RESPONSIBLE_TREE_EDIT_WITH_FOLDERS, aggregateEditSingle);
        register(Presentations.RESPONSIBLE_TREE_EDIT, aggregateEditSingle);
        register(Presentations.AGGREGATES_EDIT_WITH_FOLDERS, businessObjects);
        register(Presentations.AGGREGATES_EDIT, businessObjects);
        register(Presentations.RESPONSIBLES_TREE_EDIT_WITH_FOLDERS, businessObjects);
        register(Presentations.RESPONSIBLES_TREE_EDIT, businessObjects);
        register(Presentations.CATALOG_ITEM_TREE_EDIT, catalogItem);
        register(Presentations.CATALOG_ITEMS_TREE_EDIT, catalogItems);
        register(Presentations.CATALOG_ITEM_EDIT, catalogItem);
        register(Presentations.SERVICE_TIME_EDIT, catalogItem);
        register(Presentations.CATALOG_ITEMS_EDIT, catalogItems);
        register(Presentations.CATALOG_ITEMS_EDIT_ANY, catalogItems);
        register(Presentations.CASE_LIST_EDIT, caseEdit);
        register(Presentations.CASE_TREE_EDIT, caseEdit);
        register(Presentations.LINKED_CLASSES_EDIT, caseEdit);
        register(Presentations.BO_LINKS_EDIT_ALL_TYPES, businessObjects);
        register(Presentations.BO_LINKS_EDIT_FOLDERS, businessObjects);
        register(Presentations.BO_LINKS_EDIT, businessObjects);
        register(Presentations.BO_LINKS_LIST_EDIT, businessObjects);
        register(Presentations.BO_LINKS_LIST_EDIT_FOLDERS, businessObjects);
        register(Presentations.QUICK_SELECTION_FIELD, businessObjects);
        register(Presentations.BO_SELECT_ALL_TYPES, businessObject);
        register(Presentations.BO_SELECT_FOLDERS, businessObject);
        register(Presentations.BO_SELECT, businessObject);
        register(Presentations.BO_SELECT_LIST, businessObject);
        register(Presentations.BO_SELECT_LIST_FOLDERS, businessObject);
        register(Presentations.SEC_GROUPS_EDIT, businessObject);
        register(Presentations.STRUCTURE_BASED_SELECTION_TREE_EDIT, businessObject);
        register(Presentations.STRUCTURE_BASED_SELECTION_TREE_WITH_FOLDERS_EDIT, businessObject);
        register(Presentations.STRUCTURE_BASED_SELECTION_TREE_LINKS_EDIT, businessObjects);
        register(Presentations.STRUCTURE_BASED_SELECTION_TREE_WITH_FOLDERS_LINKS_EDIT, businessObjects);
    }

    @Override
    public <T, W extends IsWidget> ExampleWidgetFactory<T, W> getFactory(String code)
    {
        Provider<? extends ExampleWidgetFactory<?, ?>> provider = factories.get(code);

        @SuppressWarnings("unchecked")
        ExampleWidgetFactory<T, W> factory = provider != null
                ? (ExampleWidgetFactory<T, W>)provider.get()
                : (ExampleWidgetFactory<T, W>)defaultFactory.get();
        return factory;
    }

    private void register(String code, Provider<? extends ExampleWidgetFactory<?, ?>> factoryProvider)
    {
        if (factories.containsKey(code))
        {
            throw new FxException("ExampleWidgetFactory with code '" + code + "' already registered");
        }
        factories.put(code, factoryProvider);
    }

}
