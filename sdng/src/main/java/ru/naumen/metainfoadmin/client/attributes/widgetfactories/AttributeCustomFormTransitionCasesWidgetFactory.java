package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Provider;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * Фабрика представление атрибута "Объекты" пользовательской формы смены типа.
 *
 * <AUTHOR>
 * @since May 11, 2016
 */
public class AttributeCustomFormTransitionCasesWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private MetainfoServiceAsync metainfoService;
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(final PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        final SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();

        metainfoService.getMetaClasses(context.getPermittedTypeFqns(), new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> value)
            {
                value.sort(CommonUtils.METACLASSLITE_COMPARATOR);
                widget.addItems(Lists.newArrayList(CollectionUtils.transform(value,
                        DtObject.CREATE_FROM_METACLASSLITE)));
                callback.onSuccess(widget);
            }
        });
    }
}
