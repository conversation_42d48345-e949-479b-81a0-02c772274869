package ru.naumen.metainfoadmin.client.attributes.forms.info;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.IHyperlink;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.CaseListAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * Создает {@link Property} для отображения информации о классе объекта
 * на модальной форме свойств атрибута
 *
 * <AUTHOR>
 * @since 1 авг. 2018 г.
 */
public class TargetClassAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    protected void createInt(String code)
    {
        if (attribute.getType().isAttributeOfRelatedObject())
        {
            return;
        }
        String fqnAsString = getMetaClassFqn(attribute);
        metainfoService.getMetaClasses(Arrays.asList(ClassFqn.parse(fqnAsString)),
                new BasicCallback<List<MetaClassLite>>(rs)
                {
                    @Override
                    protected void handleSuccess(List<MetaClassLite> mcList)
                    {
                        if (!mcList.isEmpty())
                        {
                            createProperty(code, createHyperLink(mcList.get(0).getTitle(), fqnAsString));
                        }
                    }
                });
    }

    private String createHyperLink(String classTitle, String code)
    {
        String url = "#" + MetaClassPlace.PLACE_PREFIX + ":" + code;
        IHyperlink link = new Hyperlink(classTitle, url);
        return link.toString();
    }

    private String getMetaClassFqn(Attribute attribute)
    {
        return attribute.getType().getCode().equals(CaseListAttributeType.CODE)
                ? attribute.getType().getProperty(CaseListAttributeType.METACLASS_ID)
                : attribute.getType().getProperty(ObjectAttributeType.METACLASS_FQN);
    }
}
