package ru.naumen.metainfoadmin.client.customforms.form;

import java.util.Collection;
import java.util.Set;

import jakarta.inject.Inject;

import ru.naumen.core.client.tree.metainfo.DtoFilteredMetaClassHierarchicalTreeContext;
import ru.naumen.core.client.tree.metainfo.MetaClassMultiSelectionModelWithoutBypassChidren;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.tree.view.ITreeViewModel;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.client.widgets.tree.dto.DtoPopupMultiValueCellTree;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * Фабрика деревьев выбора метаклассов на форме добавления/редактирования пользовательской формы.
 * Не выбирает дочерние типы при выборе родителя.
 * <AUTHOR>
 * @since Feb 26, 2018
 */
public class MetaClassTreeFactory
{
    @Inject
    private DtoMetaClassesTreeFactory<MetaClassMultiSelectionModelWithoutBypassChidren,
            DtoFilteredMetaClassHierarchicalTreeContext> treeModelFactory;
    @Inject
    private PopupValueCellTreeFactory<DtObject, Collection<DtObject>,
            MetaClassMultiSelectionModelWithoutBypassChidren> treeFactory;

    public DtoPopupMultiValueCellTree<MetaClassMultiSelectionModelWithoutBypassChidren> createTree(
            ClassFqn rootClassFqn, Collection<MetaClassLite> cases, Set<ClassFqn> notSelectable)
    {
        ITreeViewModel<DtObject, MetaClassMultiSelectionModelWithoutBypassChidren> treeModel = createTreeViewModel(
                rootClassFqn, cases, notSelectable);
        return (DtoPopupMultiValueCellTree<MetaClassMultiSelectionModelWithoutBypassChidren>)treeFactory
                .create(treeModel);
    }

    private ITreeViewModel<DtObject, MetaClassMultiSelectionModelWithoutBypassChidren> createTreeViewModel(
            ClassFqn rootClassFqn, Collection<MetaClassLite> cases, Set<ClassFqn> notSelectable)
    {
        DtoFilteredMetaClassHierarchicalTreeContext treeContext = new DtoFilteredMetaClassHierarchicalTreeContext(
                rootClassFqn.fqnOfClass(), cases, notSelectable);
        ITreeViewModel<DtObject, MetaClassMultiSelectionModelWithoutBypassChidren> treeViewModel = treeModelFactory
                .createMetaClassTreeViewModel(Container.create(treeContext));
        treeViewModel.getSelectionModel().setDisabledFqns(notSelectable);

        return treeViewModel;
    }
}
