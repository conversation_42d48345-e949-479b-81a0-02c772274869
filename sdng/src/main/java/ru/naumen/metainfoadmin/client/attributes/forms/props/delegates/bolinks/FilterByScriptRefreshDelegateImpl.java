package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.bolinks;

import static ru.naumen.metainfo.shared.Constants.FILTERED_BY_SCRIPT_ATTRIBUTE_TYPES;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 15.08.2012
 *
 */
public class FilterByScriptRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, <PERSON><PERSON><PERSON>, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Boolean computable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        Boolean determinable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);
        callback.onSuccess(!computable && !determinable && isFilteredByScript(context));
    }

    protected boolean isFilteredByScript(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        boolean filteredByScript = FILTERED_BY_SCRIPT_ATTRIBUTE_TYPES.contains(attrType);
        return filteredByScript;
    }
}
