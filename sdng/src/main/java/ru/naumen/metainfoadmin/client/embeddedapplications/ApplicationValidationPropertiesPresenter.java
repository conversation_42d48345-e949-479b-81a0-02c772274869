package ru.naumen.metainfoadmin.client.embeddedapplications;

import jakarta.inject.Inject;

import ru.naumen.metainfoadmin.client.common.content.ValidationPropertiesPresenter;

/**
 * Презентер свойств конфигураций подключения к встроенным приложениям.
 * Предназначен для использования в InfoPresenter'ах на карточках подключений.
 *
 * <AUTHOR>
 * @since 23.11.16
 */
public class ApplicationValidationPropertiesPresenter extends ValidationPropertiesPresenter
{
    @Inject
    private EmbeddedApplicationMessages messages;

    @Override
    public void bindProperties()
    {
        lastConnectionStatus.setCaption(messages.lastConnectionStatus());
        display.add(lastConnectionStatus);

        lastConnectionDate.setCaption(messages.lastConnectionDate());
        display.add(lastConnectionDate);

        lastSuccessfulConnectionDate.setCaption(messages.lastSuccessfulConnectionDate());
        display.add(lastSuccessfulConnectionDate);

        ensureDebugIds();
    }
}
