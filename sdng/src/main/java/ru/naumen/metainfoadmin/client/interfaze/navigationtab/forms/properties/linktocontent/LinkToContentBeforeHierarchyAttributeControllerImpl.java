package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import static ru.naumen.metainfo.shared.Constants.LinkObjectType.CURRENT_USER;
import static ru.naumen.metainfo.shared.Constants.LinkObjectType.OBJECT_LINKED_TO_CURRENT_USER;

import jakarta.inject.Inject;

import com.google.common.base.Predicates;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute_SnapshotObject;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist.attrseltree.RelationAttrsTreeFactoryContext;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Контроллер свойства "Построить иерархию объектов (вниз), начиная с объекта"
 * <AUTHOR>
 * @since 03.11.2020
 */
public class LinkToContentBeforeHierarchyAttributeControllerImpl extends LinkToContentAttrTreePropertyControllerBase
{
    @Inject
    private ContentCreatorMessages cmessages;
    @Inject
    protected MetainfoServiceAsync metainfoService;

    @Inject
    protected LinkToContentBeforeHierarchyAttributeControllerImpl(@Assisted String code,
            @Assisted PropertyContainerContext context,
            @Assisted PropertyParametersDescriptor propertyParams,
            @Assisted PropertyDelegateDescriptor<RelationsAttrTreeObject, PropertyBase<RelationsAttrTreeObject,
                    PopupValueCellTree<?, RelationsAttrTreeObject, ?>>> propertyDelegates)
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    @Override
    public void refresh()
    {
        final Boolean showHierarchy = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.SHOW_HIERARCHY);

        String linkObject = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT);

        boolean isVisible = LinkToContentMetaClassPropertiesProcessor.getRelObjectListFragmentVisibility(context)
                            && Boolean.TRUE.equals(showHierarchy) && linkObject != null;

        if (!isVisible)
        {
            new DefaultRefreshCallback().onSuccess(false);
            context.getPropertyControllers().get(MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR).unbindValidators();
            return;
        }

        ClassFqn metaclass;

        if (CURRENT_USER.equals(linkObject))
        {
            metaclass = Employee.FQN;
        }
        else if (OBJECT_LINKED_TO_CURRENT_USER.equals(linkObject))
        {
            RelationsAttrTreeObject linkObjectAttr =
                    context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT_ATTR);

            if (linkObjectAttr == null || linkObjectAttr.getAttribute() == null)
            {
                new DefaultRefreshCallback().onSuccess(false);
                return;
            }

            metaclass = linkObjectAttr.getAttribute().getType().<ObjectAttributeType> cast().getRelatedMetaClass();
        }
        else
        {
            metaclass = ClassFqn.parse(linkObject);
        }

        metainfoService.getMetaClass(metaclass, new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass relatedClass)
            {
                String title = relatedClass.getFqn().isClass()
                        ? cmessages.currentObjectBeforeHierarchyClass(relatedClass.getTitle())
                        : cmessages.currentObjectBeforeHierarchyType(relatedClass.getTitle());
                Attribute_SnapshotObject currentObjectBeforeHierarchy =
                        LinkToContentMetaClassPropertiesProcessor.buildCurrentObjectAttribute(title,
                                relatedClass);

                RelationAttrsTreeFactoryContext treeContext = new RelationAttrsTreeFactoryContext(relatedClass.getFqn(),
                        Predicates.<RelationsAttrTreeObject> alwaysTrue(),
                        new RelationsAttrTreeObject(null, currentObjectBeforeHierarchy));

                setTreeProperty(treeContext);
            }
        });
    }

    @Override
    protected void doBind(AsyncCallback<Void> callback)
    {
        callback.onSuccess(null);
    }
}
