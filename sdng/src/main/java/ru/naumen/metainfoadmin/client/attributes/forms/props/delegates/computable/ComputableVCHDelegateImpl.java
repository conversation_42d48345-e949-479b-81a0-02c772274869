package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.computable;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.Arrays;

import java.util.ArrayList;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * Делегат изменения значения свойства "Вычислимый" - обновляет некоторые из свойств
 * <AUTHOR>
 * @since 05.07.2012
 *
 */
public class ComputableVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        Boolean computable = context.getPropertyValues().getProperty(COMPUTABLE);
        if (computable)
        {
            context.setProperty(DEFAULT_BY_SCRIPT, false);
            context.setProperty(DETERMINABLE, false);
            context.setProperty(COMPUTABLE_ON_FORM, false);
            context.setProperty(EXPORT_NDAP, false);
            context.setProperty(HIDDEN_WHEN_NO_POSSIBLE_VALUES, false);
            context.setProperty(QUICK_ADD_FORM_CODE, null);
            context.setProperty(QUICK_EDIT_FORM_CODE, null);
            context.setProperty(DATE_TIME_COMMON_RESTRICTIONS, new ArrayList<>());
            context.setProperty(DATE_TIME_RESTRICTION_TYPE, null);
            context.setProperty(DATE_TIME_RESTRICTION_ATTRIBUTE, null);
            context.setProperty(DATE_TIME_RESTRICTION_CONDITION, null);
            context.setProperty(ADVLIST_SEMANTIC_FILTERING, false);
        }

        context.getRefreshProcess()
                .startCustomProcess(Arrays.asList(DETERMINABLE, DETERMINER, REQUIRED, REQUIRED_IN_INTERFACE, EDITABLE,
                        EDITABLE_IN_LISTS, UNIQUE, PERMITTED_TYPES, SCRIPT, DATE_TIME_RESTRICTION_SCRIPT, EDIT_PRS,
                        FILTERED_BY_SCRIPT, COMPUTABLE_ON_FORM, COMPUTABLE_ON_FORM_SCRIPT, SCRIPT_FOR_FILTRATION,
                        DEFAULT_VALUE_LABEL, DEFAULT_BY_SCRIPT, SCRIPT_FOR_DEFAULT, SUGGEST_CATALOG, SELECT_SORTING,
                        USE_GEN_RULE, COMPOSITE, EXPORT_NDAP, INTERVAL_AVAILABLE_UNITS, DEFAULT_VALUE,
                        HIDDEN_WHEN_NO_POSSIBLE_VALUES, QUICK_ADD_FORM_CODE, QUICK_EDIT_FORM_CODE,
                        DATE_TIME_COMMON_RESTRICTIONS, DATE_TIME_RESTRICTION_TYPE, DATE_TIME_RESTRICTION_ATTRIBUTE,
                        DATE_TIME_RESTRICTION_CONDITION, ADVLIST_SEMANTIC_FILTERING, RELATED_ATTRS_TO_EXPORT,
                        HIDE_ARCHIVED));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
        context.getPropertyControllers().get(COMPUTABLE).fireUpdateTabOrderEvent();
    }
}