package ru.naumen.metainfoadmin.client.attributes.forms.typeEmul;

import java.util.Map;

import jakarta.inject.Inject;

import java.util.HashMap;
import java.util.HashSet;

import com.google.inject.Singleton;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.attr.AttrService;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.BackTimerAttributeType;
import ru.naumen.metainfo.shared.Constants.CaseListAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType;
import ru.naumen.metainfo.shared.Constants.DoubleAttributeType;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.Constants.TimerAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.AttributeTypeEmul;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 21.05.2012
 */
@Singleton
public class AttributeTypeFactoryImpl implements AttributeTypeFactory
{
    @Inject
    AttrService attrService;

    private Map<String, AttributeTypeBuilder> attrTypeBuilders = new HashMap<>();

    @Inject
    public AttributeTypeFactoryImpl(AttributeTypeBuilderAggregateImpl aggregateAttrTypeBuilder,
            AttributeTypeBuilderBackLinkImpl backLinkAttrTypeBuilder,
            AttributeTypeBuilderCatalogImpl catalogAttrTypeBuilder,
            AttributeTypeBuilderDirectLinkImpl directLinkAttrTypeBuilder,
            AttributeTypeBuilderTimerImpl timerAttrTypeBuilder,
            AttributeTypeBuilderDateTimeIntervalImpl dateTimeIntervalAttrTypeBuilder,
            AttributeTypeBuilderCaseListImpl caseListAttrTypeBuilder,
            AttributeTypeBuilderStringImpl stringAttrTypeBuilder,
            AttributeTypeBuilderIntegerImpl integerAttrTypeBuilder,
            AttributeTypeBuilderDoubleImpl doubleAttrTypeBuilder)
    {
        attrTypeBuilders.put(ObjectAttributeType.CODE, directLinkAttrTypeBuilder);
        attrTypeBuilders.put(BOLinksAttributeType.CODE, directLinkAttrTypeBuilder);
        attrTypeBuilders.put(CatalogItemAttributeType.CODE, catalogAttrTypeBuilder);
        attrTypeBuilders.put(CatalogItemsAttributeType.CODE, catalogAttrTypeBuilder);
        attrTypeBuilders.put(BackLinkAttributeType.CODE, backLinkAttrTypeBuilder);
        attrTypeBuilders.put(AggregateAttributeType.CODE, aggregateAttrTypeBuilder);
        attrTypeBuilders.put(ResponsibleAttributeType.CODE, aggregateAttrTypeBuilder);
        attrTypeBuilders.put(DateTimeIntervalAttributeType.CODE, dateTimeIntervalAttrTypeBuilder);
        attrTypeBuilders.put(TimerAttributeType.CODE, timerAttrTypeBuilder);
        attrTypeBuilders.put(BackTimerAttributeType.CODE, timerAttrTypeBuilder);
        attrTypeBuilders.put(CaseListAttributeType.CODE, caseListAttrTypeBuilder);
        attrTypeBuilders.put(StringAttributeType.CODE, stringAttrTypeBuilder);
        attrTypeBuilders.put(IntegerAttributeType.CODE, integerAttrTypeBuilder);
        attrTypeBuilders.put(DoubleAttributeType.CODE, doubleAttrTypeBuilder);
    }

    @Override
    public AttributeType create(IProperties contextProps, IProperties propertyValues)
    {
        String attrTypeCode = propertyValues.getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        AttributeType result = new AttributeTypeEmul(attrTypeCode);
        attrService.initType(attrTypeCode, result);
        if (attrTypeBuilders.containsKey(attrTypeCode))
        {
            attrTypeBuilders.get(attrTypeCode).build(result, propertyValues);
        }
        if (result.getProperty(ObjectAttributeType.PERMITTED_TYPES) == null)
        {
            result.setProperty(ObjectAttributeType.PERMITTED_TYPES, new HashSet<>());
        }
        return result;
    }

    @Override
    public void invert(AttributeType attrType, IProperties propertyValues)
    {
        if (attrTypeBuilders.containsKey(attrType.getCode()))
        {
            attrTypeBuilders.get(attrType.getCode()).invert(attrType, propertyValues);
        }
    }
}