package ru.naumen.metainfoadmin.client.attributes.forms.typeEmul;

import com.google.inject.Singleton;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.CoreConstants.AttributeTypeProperties;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.DoubleAttributeType;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Класс позволяет сконструировать тип атрибута вещественного числа на основе данных из контекста и заполнить
 * контекст на основе типа атрибута
 * <AUTHOR>
 * @since 10.11.2019
 */
@Singleton
public class AttributeTypeBuilderDoubleImpl implements AttributeTypeBuilder
{
    @Override
    public void build(AttributeType type, IProperties propertyValues)
    {
        type.setProperty(IntegerAttributeType.HAS_GROUP_SEPARATOR, isHasGroupSeparators(propertyValues));
        type.setProperty(AttributeTypeProperties.PROP_DECIMALS_COUNT, getDigitsCountRestriction(propertyValues));
    }

    @Override
    public void invert(AttributeType type, IProperties propertyValues)
    {
        DoubleAttributeType doubleAttrType = type.cast();
        propertyValues.setProperty(AttributeFormPropertyCode.HAS_GROUP_SEPARATORS,
                doubleAttrType.isHasGroupSeparator());
        propertyValues.setProperty(AttributeFormPropertyCode.DIGITS_COUNT_RESTRICTION,
                doubleAttrType.getDecimalsCountRestriction());
    }

    private boolean isHasGroupSeparators(IProperties propertyValues)
    {
        Object hasGroupSeparators = propertyValues.getProperty(AttributeFormPropertyCode.HAS_GROUP_SEPARATORS);
        boolean hasGroupSeparatorsValue = hasGroupSeparators == null
                ? IntegerAttributeType.HAS_GROUP_SEPARATOR_DEFAULT
                : hasGroupSeparators instanceof String ? Boolean.parseBoolean((String)hasGroupSeparators)
                        : (Boolean)hasGroupSeparators;
        return hasGroupSeparatorsValue;
    }

    private Long getDigitsCountRestriction(IProperties propertyValues)
    {
        Object digitsCountValue = propertyValues.getProperty(AttributeFormPropertyCode.DIGITS_COUNT_RESTRICTION,
                IntegerAttributeType.DECIMALS_COUNT_RESTRICTION_DEFAULT);
        Long decimalsCountRestriction = null;
        if (digitsCountValue instanceof String && !StringUtilities.isEmpty(((String)digitsCountValue)))
        {
            decimalsCountRestriction = Long.valueOf((String)digitsCountValue);
        }
        else if (digitsCountValue instanceof Long)
        {
            decimalsCountRestriction = (Long)digitsCountValue;
        }
        return decimalsCountRestriction;
    }
}