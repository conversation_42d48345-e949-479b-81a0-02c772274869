package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.clientinfo;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.ui.ClientInfo;
import ru.naumen.metainfoadmin.client.common.content.HasPropertiesContentDisplay;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.RelObjPropertyListContentPresenterBase;

/**
 * Презентер контента "Информация о пользователе"
 *
 * <AUTHOR>
 * @since 29.03.2012
 */
public class ClientInfoContentPresenter extends RelObjPropertyListContentPresenterBase<ClientInfo>
{
    @Inject
    public ClientInfoContentPresenter(HasPropertiesContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "ClientInfo");
    }

    @Override
    protected String getHelpText()
    {
        final AttributeGroup attributeGroup = getContext().getMetainfo()
                .getAttributeGroup(getContent().getAttributeGroup());
        return attributeGroup == null
                ? StringUtilities.EMPTY : messages.clientInfoProperty() + ' '
                                          + messages.helpText(attributeGroup.getTitle());
    }
}
