package ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.widgets.columns.LinkToPlaceColumn;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumnFactory;
import ru.naumen.core.client.widgets.columns.LinkToPlaceWithIndentColumn;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.CrumbRelationAttribute;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelElementWrapper;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay;

/**
 * <AUTHOR>
 * @since 11 июня 2014 г.
 */
public class BreadCrumbGinModule extends AbstractGinModule
{
    //@formatter:off
    @Override
    protected void configure()
    {
        bind(BreadCrumbHelper.class);
        
        bind(new TypeLiteral<TableDisplay<CrumbRelationAttribute>>() {}).to(new TypeLiteral<TableWithArrowsDisplay<CrumbRelationAttribute>>() {});
        
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<LinkToPlaceColumn<MenuItem>>(){}, new TypeLiteral<LinkToPlaceWithIndentColumn<MenuItem>>(){})
            .build(new TypeLiteral<LinkToPlaceColumnFactory<MenuItem>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(new TypeLiteral<LinkToPlaceColumn<LeftMenuItemSettingsDTO>>(){}, new TypeLiteral<LinkToPlaceWithIndentColumn<LeftMenuItemSettingsDTO>>(){})
                .build(new TypeLiteral<LinkToPlaceColumnFactory<LeftMenuItemSettingsDTO>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(new TypeLiteral<LinkToPlaceColumn<QuickAccessPanelElementWrapper>>(){}, new TypeLiteral<LinkToPlaceWithIndentColumn<QuickAccessPanelElementWrapper>>(){})
                .build(new TypeLiteral<LinkToPlaceColumnFactory<QuickAccessPanelElementWrapper>>(){}));

        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<LinkToPlaceColumn<Crumb>>(){}, new TypeLiteral<LinkToPlaceWithIndentColumn<Crumb>>(){})
            .build(new TypeLiteral<LinkToPlaceColumnFactory<Crumb>>(){}));
    }
    //@formatter:on
}
