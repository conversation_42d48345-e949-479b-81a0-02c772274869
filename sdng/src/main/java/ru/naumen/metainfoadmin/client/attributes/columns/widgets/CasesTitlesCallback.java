package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TITLE_SEPARATOR;
import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TYPE_LIST_SEPARATOR;

import java.util.List;

import com.google.gwt.dom.client.BRElement;
import com.google.gwt.user.client.DOM;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineLabel;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;

/**
 * Выводит названия типов в переданный контейнер
 *
 * <AUTHOR>
 * @since 8 авг. 2018 г.
 *
 */
public class CasesTitlesCallback extends BasicCallback<List<MetaClassLite>>
{
    private final FlowPanel result;
    private final AttributesMessages messages;

    public CasesTitlesCallback(FlowPanel result, AttributesMessages messages)
    {
        this.result = result;
        this.messages = messages;
    }

    @Override
    protected void handleSuccess(final List<MetaClassLite> values)
    {
        result.getElement().appendChild(DOM.createElement(BRElement.TAG));
        result.add(new InlineLabel(messages.type() + TITLE_SEPARATOR));
        int i = 0;
        for (MetaClassLite type : values)
        {
            Anchor anchor = new Anchor(type.getTitle() + ((i++ < values.size() - 1) ? TYPE_LIST_SEPARATOR : ""),
                    false, AttrWidgetsHelper.createLink(MetaClassPlace.PLACE_PREFIX, type.getCode()));
            anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
            result.add(anchor);
        }
    }
}
