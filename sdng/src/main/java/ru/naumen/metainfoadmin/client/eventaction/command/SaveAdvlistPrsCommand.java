package ru.naumen.metainfoadmin.client.eventaction.command;

import jakarta.inject.Inject;

import com.google.gwt.user.client.Command;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.common.command.AdvlistCommandParam;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfoadmin.client.MGinjector;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.AdvlistUIContext;
import ru.naumen.metainfoadmin.client.eventaction.form.SaveEventActionsAdvlistPrsPresenter;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.shared.advlist.AdvlistSettingsClient;

/**
 * {@link Command} сохранения названия настроек advlist'а (для кнопки в списке UserEvents)
 * <AUTHOR>
 * @since 23.08.2017
 */
public class SaveAdvlistPrsCommand
        extends PresenterCommandImpl<AdvlistSettingsClient, AdvlistSettingsClient, AdvlistSettingsClient>
{
    @Inject
    private SecurityHelper securityHelper;
    @Inject
    protected MGinjector injector;

    @Inject
    public SaveAdvlistPrsCommand(@Assisted CommandParam<AdvlistSettingsClient, AdvlistSettingsClient> param)
    {
        super(param);
    }

    @Override
    public boolean isPossible(Object input)
    {
        AdvlistSettingsClient s = (AdvlistSettingsClient)input;
        String userUuid = securityHelper.getCurrentUser().getUUID();
        return CommonUtils.isSuperUserUUID(userUuid) || userUuid.equals(s.getAuthor().getUUID());
    }

    @Override
    public void onExecute(AdvlistSettingsClient result,
            CallbackDecorator<AdvlistSettingsClient, AdvlistSettingsClient> callback)
    {
        callback.onSuccess(result);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.SAVE;
    }

    @Override
    protected CallbackPresenter<AdvlistSettingsClient, AdvlistSettingsClient> getPresenter(AdvlistSettingsClient value)
    {
        SaveEventActionsAdvlistPrsPresenter p = injector.saveEventActionsAdvlistPrsPresenter();
        ObjectListContext context = ((AdvlistCommandParam)param).getContext();
        p.init(((AdvlistUIContext)context).getComponents());
        return p;
    }

}
