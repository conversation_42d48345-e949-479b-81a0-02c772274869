/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap;

import java.util.Set;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;

import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.catalog.impl.valuemap.ValueMapContext;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 30.10.2012
 *
 */
public class EscalationValueMapContext extends ValueMapContext
{
    @Inject
    public EscalationValueMapContext(@Nullable @Assisted DtoContainer<Catalog> catalog,
            @Nullable @Assisted EventBus eventBus,
            @Nullable @Assisted MetaClass metaClass)
    {
        super(catalog, eventBus, metaClass);
    }

    @Override
    @CheckForNull
    public DtoContainer<Catalog> getCatalog()
    {
        return super.getCatalog();
    }

    @Override
    @CheckForNull
    public EventBus getEventBus()
    {
        return super.getEventBus();
    }

    @Override
    @CheckForNull
    public MetaClass getMetaClass()
    {
        return super.getMetaClass();
    }

    @Override
    protected Set<String> getExcludeCodes()
    {
        Set<String> result = super.getExcludeCodes();
        result.add(CatalogItem.ITEM_CODE);
        result.add(ValueMapCatalogItem.TARGET_ATTRS);
        return result;
    }
}