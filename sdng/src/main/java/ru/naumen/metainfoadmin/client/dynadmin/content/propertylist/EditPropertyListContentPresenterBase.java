package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.client.ui.CustomFormMessages;
import ru.naumen.metainfo.shared.ui.EditablePropertiesContentBase;
import ru.naumen.metainfoadmin.client.dynadmin.content.forms.SimpleEditContentPresenter;

/**
 * Базовый функционал для форм редактирования контентов со списком свойств
 *
 * <AUTHOR>
 * @since 16.12.21
 */
// TODO dzevako переделать на extends EditHasAttrDescriptionContentPresenterBase<T extends
//  HasAttrDescriptionContentBase> в NSDPRD-28019
public class EditPropertyListContentPresenterBase<T extends EditablePropertiesContentBase>
        extends SimpleEditContentPresenter<T>
{
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> showAttrDescription;

    @Inject
    private CustomFormMessages formMessages;

    @Inject
    public EditPropertyListContentPresenterBase(PropertyDialogDisplay display,
            EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void bindPropertiesInner()
    {
        bindCustomProperties();
        bindShowAttrsDescription();
    }

    protected void bindCustomProperties()
    {
    }

    private void bindShowAttrsDescription()
    {
        showAttrDescription.setCaption(formMessages.attributeDescription());
        showAttrDescription.setValue(content.isShowAttrDescription());
        DebugIdBuilder.ensureDebugId(showAttrDescription, "showAttrDescription");
        getDisplay().add(showAttrDescription);
    }

    @Override
    public void updateCurrentContent()
    {
        super.updateCurrentContent();
        content.setShowAttrDescription(showAttrDescription.getValue());
    }

    @Override
    protected void restoreContent(T oldContent)
    {
        super.restoreContent(oldContent);
        content.setShowAttrDescription(oldContent.isShowAttrDescription());
    }

    @Override
    protected boolean isContentEquals(T oldContent)
    {
        return super.isContentEquals(oldContent)
               && eq(oldContent.isShowAttrDescription(), content.isShowAttrDescription());
    }
}
