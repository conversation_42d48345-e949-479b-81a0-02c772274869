package ru.naumen.metainfoadmin.client.eventaction.form.creator;

import static ru.naumen.core.client.widgets.id.DebugIdBuilder.ensureDebugId;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.tree.selection.FilteredMultiSelectionModel;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.script.places.EventActionCategories;
import ru.naumen.metainfo.client.eventaction.EventActionConstants;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.tracking.ChangeTrackingEventAction;
import ru.naumen.metainfo.shared.eventaction.tracking.PageRefreshArea;
import ru.naumen.metainfo.shared.eventaction.tracking.TrackingUiAction;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;

/**
 * Представление для редактирования свойств действия «Отслеживание изменений».
 * <AUTHOR>
 * @since Mar 25, 2022
 */
public class ChangeTrackingEventActionFormPropertiesCreator
        extends EventActionWithRecipientsFormPropertiesCreator<ChangeTrackingEventAction>
{
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> useDefaultMessage;
    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    private SelectListProperty<String, SelectItem> uiAction;
    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    private SelectListProperty<String, SelectItem> refreshArea;

    @Inject
    private EventActionConstants eventActionConstants;

    private final List<String> excludedAttrValuesReloadEvents;
    private ValidationUnit<?> messageValidationUnit;
    private HandlerRegistration updateMessageHandlerRegistration;
    private HandlerRegistration updatePageHandlerRegistration;

    @Inject
    public ChangeTrackingEventActionFormPropertiesCreator(
            @Named(PropertiesGinModule.TEXT_AREA) Property<String> message)
    {
        this.message = message;
        excludedAttrValuesReloadEvents = new ArrayList<>();
        excludedAttrValuesReloadEvents.add(EventType.addFile.name());
        excludedAttrValuesReloadEvents.add(EventType.editComment.name());
        excludedAttrValuesReloadEvents.add(EventType.addComment.name());
    }

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {
        initHandlers(display);
        updateMessageProperty(display);
    }

    @Override
    public void bindProperties(EventActionFormDisplay display, List<ClassFqn> fqns)
    {
        super.bindProperties(display, fqns);
        disableHtmlProperty(); // В этом дпс это свойство не нужно
        add(messages.useDefaultMessage(), useDefaultMessage);
        add(messages.actionInWeb(), uiAction);
        add(messages.updateArea(), refreshArea);

        updateMessageExample(null);
        message.setCaption(messages.pushContent());
        message.setValidationMarker(true);
        uiAction.setValidationMarker(true);
        refreshArea.setValidationMarker(true);

        updateUiActions(null);
        updatePageRefreshArea(display);
        bindPropertiesAfter(display, fqns, EventActionCategories.EVENTACTION_WSMESSAGE_CUSTOMIZATION);
    }

    @Override
    protected void setActionProperties(ChangeTrackingEventAction action)
    {
        super.setActionProperties(action);

        action.setUseDefaultMessage(Boolean.TRUE.equals(useDefaultMessage.getValue()));
        if (action.isUseDefaultMessage())
        {
            action.getMessage().clear();
        }
        else
        {
            metainfoUtils.setLocalizedValue(action.getMessage(), message.getValue());
        }

        action.setUiAction(TrackingUiAction.fromString(SelectListPropertyValueExtractor.getValue(uiAction),
                TrackingUiAction.MESSAGE_WITH_REFRESH));
        action.setPageRefreshArea(PageRefreshArea.fromString(
                SelectListPropertyValueExtractor.getValue(refreshArea), PageRefreshArea.CHANGED_CONTENTS));
    }

    @Override
    protected Action newEventActionTypeInstance()
    {
        return new ChangeTrackingEventAction();
    }

    @Override
    public void init(@Nullable EventActionWithScript eventAction, Property<SelectItem> event)
    {
        super.init(eventAction, event);
        if (eventAction != null && !(eventAction.getObject().getAction() instanceof ChangeTrackingEventAction))
        {
            throw new IllegalArgumentException("EventAction must be ChangeTrackingEventAction");
        }
        this.event = event.getValue() == null ? null : event.getValue().getCode();
        this.eventAction = eventAction;
    }

    @Override
    public void refreshProperties(EventActionFormDisplay display, List<ClassFqn> fqns, @Nullable String event)
    {
        super.refreshProperties(display, fqns, event);
        updateMessageExample(event);
        updateUiActions(event);
        updatePageRefreshArea(display);
    }

    @Override
    protected Property<Collection<DtObject>> createRecipients(
            PopupValueCellTree<DtObject, Collection<DtObject>, FilteredMultiSelectionModel<DtObject>> tree)
    {
        Property<Collection<DtObject>> recipients = new PropertyBase<>(messages.to(), tree);
        ensureDebugId(recipients, "recipients");
        return recipients;
    }

    @Override
    protected void ensureDebugIds()
    {
        super.ensureDebugIds();
        ensureDebugId(useDefaultMessage, "useDefaultMessage");
        ensureDebugId(uiAction, "uiAction");
        ensureDebugId(refreshArea, "refreshArea");
    }

    @Override
    protected void setPropertiesValues(final EventActionFormDisplay display)
    {
        super.setPropertiesValues(display);

        updateMessageExample(null);
        updateUiActions(null);
    }

    @Override
    protected void setPropertiesValuesForNew()
    {
        super.setPropertiesValuesForNew();
        useDefaultMessage.setValue(true);
        excludeAuthor.setValue(true);
        message.setValue(StringUtilities.EMPTY);
        uiAction.trySetObjValue(TrackingUiAction.MESSAGE_WITH_REFRESH.getName());
        refreshArea.trySetObjValue(PageRefreshArea.CHANGED_CONTENTS.getName());
    }

    @Override
    protected void setPropertiesValuesForExisted(ChangeTrackingEventAction action)
    {
        super.setPropertiesValuesForExisted(action);
        useDefaultMessage.setValue(action.isUseDefaultMessage());
        message.setValue(action.isUseDefaultMessage() ? StringUtilities.EMPTY
                : metainfoUtils.getLocalizedValue(action.getMessage()));
        uiAction.trySetObjValue(action.getUiAction().getName());
        refreshArea.trySetObjValue(action.getPageRefreshArea().getName());
    }

    private void initHandlers(PropertyDialogDisplay display)
    {
        updateMessageHandlerRegistration = useDefaultMessage.addValueChangeHandler(event ->
        {
            updateMessageExample(null);
            updateMessageProperty(display);
        });
        updatePageHandlerRegistration = uiAction.addValueChangeHandler(event ->
        {
            updateMessageExample(this.event);
            updatePageRefreshArea(display);
        });
    }

    private void updateMessageProperty(PropertyDialogDisplay display)
    {
        PropertyRegistration<?> messagePR = getPropertyRegistration(message);
        boolean useDefault = Boolean.TRUE.equals(useDefaultMessage.getValue());
        if (useDefault && null != messagePR)
        {
            remove(message);
            if (null != messageValidationUnit)
            {
                unregister(messageValidationUnit);
                messageValidationUnit = null;
            }
        }
        else if (!useDefault && null == messagePR)
        {
            register(message, display.addPropertyAfter(message, getPropertyRegistration(useDefaultMessage)));
            add(message, indexOf(useDefaultMessage) + 1);
            messageValidationUnit = validation.validate(message, notEmptyValidator);
            add(messageValidationUnit);
        }
    }

    private void updateMessageExample(@Nullable String event)
    {
        String eventType = StringUtilities.toNonNullString(getEventTypeName(event));
        String description = messages.changeTrackingDefaultMessageExample(eventType);
        TrackingUiAction trackingUiAction = TrackingUiAction.fromString(
                SelectListPropertyValueExtractor.getValue(uiAction),
                TrackingUiAction.MESSAGE_WITH_REFRESH);
        if (TrackingUiAction.AUTO_RELOAD.equals(trackingUiAction) && !EventType.openEditForm.name().equals(eventType))
        {
            description = String.join(" ", description, messages.changeTrackingAutoReload());
        }
        useDefaultMessage.setDescription(description);

        boolean showExample =
                Boolean.TRUE.equals(useDefaultMessage.getValue()) && !eventType.equals(StringUtilities.EMPTY);
        useDefaultMessage.getDescriptionWidget().asWidget().setVisible(showExample);
    }

    @Nullable
    private String getEventTypeName(@Nullable String event)
    {
        if (null != event)
        {
            return event;
        }
        else if (null != eventAction)
        {
            return eventAction.getObject().getEvent().getEventType().name();
        }
        else if (null != this.event)
        {
            return this.event;
        }
        else
        {
            return null;
        }
    }

    private void updatePageRefreshArea(PropertyDialogDisplay display)
    {
        if (TrackingUiAction.MESSAGE.getName().equals(uiAction.getValue().getCode()))
        {
            remove(refreshArea);
            return;
        }
        PropertyRegistration<?> refreshAreaPR = getPropertyRegistration(refreshArea);
        if (refreshAreaPR == null)
        {
            register(refreshArea, display.addPropertyAfter(refreshArea, getPropertyRegistration(uiAction)));
            add(refreshArea, indexOf(uiAction) + 1);
        }
        preparePageRefreshAreaValues();
    }

    private void preparePageRefreshAreaValues()
    {
        SingleSelectCellList<?> refreshAreaList = refreshArea.getValueWidget();
        refreshAreaList.clear();
        boolean isAutoreloadSelected = TrackingUiAction.AUTO_RELOAD.getName().equals(uiAction.getValue().getCode());
        boolean isExcludedFromReload = excludedAttrValuesReloadEvents.contains(this.event);
        Map<String, String> refreshAreas = eventActionConstants.trackingRefreshAreas().entrySet()
                .stream()
                .filter(entry ->
                        !(isExcludedFromReload && PageRefreshArea.ATTR_VALUES.getName().equals(entry.getKey())
                          || isAutoreloadSelected && PageRefreshArea.WHOLE_PAGE.getName().equals(entry.getKey())))
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));

        refreshAreas.forEach((key, value) -> refreshAreaList.addItem(value, key));
        if (!refreshAreas.containsKey(refreshArea.getValue().getCode()))
        {
            refreshArea.trySetObjValue(refreshAreas.keySet().stream().findFirst().orElse(null), true);
        }
    }

    private void updateUiActions(@Nullable String event)
    {
        String eventType = eventAction == null || event != null
                ? event
                : eventAction.getObject().getEvent().getEventType().name();
        SingleSelectCellList<?> uiActionList = uiAction.getValueWidget();
        uiActionList.clear();
        if (EventType.openEditForm.name().equals(eventType))
        {
            String messageCode = TrackingUiAction.MESSAGE.getName();
            uiActionList.addItem(eventActionConstants.trackingUiActions().get(messageCode), messageCode);
            uiAction.trySetObjValue(messageCode, true);
            return;
        }
        eventActionConstants.trackingUiActions().forEach((key, value) -> uiActionList.addItem(value, key));
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        useDefaultMessage.unbind(callback);
        uiAction.unbind(callback);
        refreshArea.unbind(callback);
        if (updateMessageHandlerRegistration != null)
        {
            updateMessageHandlerRegistration.removeHandler();
        }
        if (updatePageHandlerRegistration != null)
        {
            updatePageHandlerRegistration.removeHandler();
        }
        super.unbind(callback);
    }
}
