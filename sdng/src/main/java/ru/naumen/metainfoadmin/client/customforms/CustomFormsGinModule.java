package ru.naumen.metainfoadmin.client.customforms;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;

import ru.naumen.metainfoadmin.client.customforms.commands.CommandsGinModule;
import ru.naumen.metainfoadmin.client.customforms.parameters.FormParameterAddFormMessages;
import ru.naumen.metainfoadmin.client.customforms.parameters.FormParameterEditFormMessages;
import ru.naumen.metainfoadmin.client.customforms.parameters.ParameterFormAdd;
import ru.naumen.metainfoadmin.client.customforms.parameters.ParameterFormEdit;
import ru.naumen.metainfoadmin.client.customforms.parameters.ParameterFormGinModule;

/**
 *
 * <AUTHOR>
 * @since 20 апр. 2016 г.
 */
public class CustomFormsGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
                .build(CustomFormParametersPresenterFactory.class));
        
        install(new CommandsGinModule());
        
        install(ParameterFormGinModule.create(ParameterFormAdd.class)
                .setMessages(FormParameterAddFormMessages.class));
        install(ParameterFormGinModule.create(ParameterFormEdit.class)
                .setMessages(FormParameterEditFormMessages.class));
        //@formatter:on
    }
}
