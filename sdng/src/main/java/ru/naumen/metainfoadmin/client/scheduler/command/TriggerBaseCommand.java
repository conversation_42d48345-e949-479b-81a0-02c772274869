package ru.naumen.metainfoadmin.client.scheduler.command;

import java.util.logging.Logger;

import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.actionbar.ActionCommandBase;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 *
 * <AUTHOR>
 */
@SuppressWarnings("rawtypes")
public abstract class TriggerBaseCommand<T> extends ActionCommandBase
{
    protected final Logger LOG = Logger.getLogger(this.getClass().getName());

    Trigger trigger;

    OnStartCallback<T> callback;

    private String schTaskCode;

    public TriggerBaseCommand()
    {
        super(true);
    }

    public TriggerBaseCommand(boolean lockDisplay)
    {
        super(lockDisplay);
    }

    public void execute(OnStartCallback<T> callback)
    {
        execute(this.schTaskCode, this.trigger, callback);
    }

    public void execute(String schTaskCode, Trigger trigger, OnStartCallback<T> callback)
    {

    }

    public void execute(Trigger trigger)
    {
        execute(this.schTaskCode, trigger, this.callback);
    }

    @Override
    public void executeInt()
    {
        execute(this.schTaskCode, this.trigger, this.callback);
    }

    public TriggerBaseCommand<T> init(String schTaskCode, OnStartCallback<T> callback)
    {
        return init(schTaskCode, this.trigger, callback);
    }

    public TriggerBaseCommand<T> init(String schTaskCode, Trigger trigger)
    {
        return init(schTaskCode, trigger, this.callback);
    }

    public TriggerBaseCommand<T> init(String schTaskCode, Trigger trigger, OnStartCallback<T> callback)
    {
        this.trigger = trigger;
        this.callback = callback;
        this.schTaskCode = schTaskCode;
        return this;
    }
}
