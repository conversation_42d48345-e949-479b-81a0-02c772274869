package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands;

import java.util.List;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.MoveDirection;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.HomePageCommandParam;
import ru.naumen.metainfoadmin.shared.homepage.MoveHomePageItemAction;

/**
 * Базовая команда перемещения элемента домашней страницы
 * <AUTHOR>
 * @since 09.01.2023
 */
public abstract class MoveHomePageItemCommand extends BaseCommandImpl<HomePageDtObject,
        DtoContainer<NavigationSettings>>
{
    protected final MoveDirection direction;

    private final DispatchAsync dispatch;

    /**
     * @param param параметры команды
     * @param direction направление перемещения элемента
     * @param dispatch сервис вызова действий
     */
    protected MoveHomePageItemCommand(CommandParam<HomePageDtObject, DtoContainer<NavigationSettings>> param,
            MoveDirection direction,
            DispatchAsync dispatch)
    {
        super(param);
        this.dispatch = dispatch;
        this.direction = direction;
    }

    @Override
    public void execute(CommandParam<HomePageDtObject, DtoContainer<NavigationSettings>> param)
    {
        HomePageCommandParam homePageCommandParam = (HomePageCommandParam)param;
        HomePageDtObject homePageDtObject = param.getValue();
        DtoContainer<NavigationSettings> settings = homePageCommandParam.getSettings();
        int idx = settings.get().getHomePageDtObjects().indexOf(homePageDtObject);
        if (idx >= 0)
        {
            homePageCommandParam.getCallback().onSuccess(settings);
            dispatch.execute(new MoveHomePageItemAction(homePageDtObject, idx + direction.getStep()),
                    new BasicCallback<SimpleResult<DtoContainer<NavigationSettings>>>()
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<DtoContainer<NavigationSettings>> result)
                        {
                            DtoContainer<NavigationSettings> navigationSettings = result.get();
                            homePageCommandParam.setSettings(navigationSettings);
                            param.getCallback().onSuccess(navigationSettings);
                        }
                    });
        }
    }

    @Override
    public boolean isPossible(Object obj)
    {
        if (!(obj instanceof HomePageDtObject) || null == param)
        {
            return false;
        }
        HomePageDtObject homePage = (HomePageDtObject)obj;
        List<HomePageDtObject> homePageDtObjects =
                ((HomePageCommandParam)getParam()).getSettings().get().getHomePageDtObjects();
        int itemIndex = homePageDtObjects.indexOf(homePage);
        return isPossible(itemIndex, itemIndex + direction.getStep(), homePageDtObjects.size());
    }

    /**
     * Метод, предназначенный для проверки возможности перемещения объекта. Если метод возвращает false, тогда на
     * {@link ru.naumen.metainfoadmin.client.TableWithArrowsDisplay стрелочном дисплее} не будут отображены стрелки
     * перемещения элемента.
     * @param fromIndex текущий индекс объекта. -1, если такого объекта нет в списке элементов.
     * @param toIndex индекс, на который нужно переместить домашнюю страницу.
     * @param count количество элементов в списке.
     * @return true, если возможно совершить перемещение объекта с одного индекса на другой
     */
    protected abstract boolean isPossible(int fromIndex, int toIndex, int count);
}