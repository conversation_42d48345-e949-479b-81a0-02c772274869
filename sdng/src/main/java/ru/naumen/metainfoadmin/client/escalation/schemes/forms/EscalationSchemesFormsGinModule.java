package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinjector.EscalationSchemeFormCallbackFactory;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 24.07.2012
 *
 */
public class EscalationSchemesFormsGinModule extends AbstractGinModule
{
    public interface EscalationSchemeFormFactory<T extends EscalationSchemeForm<?>>
    {
        T create(EventBus localEventBus);
    }

    public static class EscalationSchemeFormPropertyCode
    {
        private EscalationSchemeFormPropertyCode()
        {
        }

        public static final String TITLE = "title";
        public static final String DESCRIPTION = "description";
        public static final String TARGET_OBJECTS = "targetObjects";
        public static final String TIMER = AttributeFormPropertyCode.TARGET_TIMER;
        public static final String STATE = "state";
        public static final String CODE = "code";
        public static final String SETTINGS_SET = "settingsSet";
    }

    /**
     * Максимальная длина названий
     */
    protected static final int MAX_TITLE_LENGTH = 255;

    /**
     * Максимальная длина кода
     */
    protected static final int MAX_CODE_LENGTH = 255;

    @Override
    protected void configure()
    {
        configureAddForm();
        configureEditForm();
        bind(EscalationSchemeTargetTimerRefreshDelegateImpl.class).in(Singleton.class);
        bind(EscalationSchemeTargetObjectsVCHDelegateImpl.class).in(Singleton.class);

    }

    private void configureAddForm()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(AddEscalationSchemeForm.class, AddEscalationSchemeForm.class)
            .build(new TypeLiteral<EscalationSchemeFormFactory<AddEscalationSchemeForm>>(){}));
        bind(new TypeLiteral<EscalationSchemeFormMessages<ObjectFormAdd>>(){})
            .to(AddEscalationSchemeFormMessages.class).in(Singleton.class);
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<AsyncCallback<SimpleResult<DtoContainer<EscalationScheme>>>>(){}, AddEscalationSchemeFormApplyCallback.class)
            .build(new TypeLiteral<EscalationSchemeFormCallbackFactory<ObjectFormAdd>>(){}));
        install(PropertyControllerGinModule.create(EscalationScheme.class, ObjectFormAdd.class)
            .setPropertyControllerFactory(new TypeLiteral<EscalationSchemeFormPropertyControllerFactoryImpl<ObjectFormAdd>>(){})
            .setPropertyParametersDescriptorFactory(new TypeLiteral<EscalationSchemeFormPropertyParametersDescriptorFactoryImpl<ObjectFormAdd>>(){}));
        bind(new TypeLiteral<EscalationSchemePropertyContainerAfterBindHandler<ObjectFormAdd>>(){})
            .to(EscalationSchemePropertyContainerAfterBindHandlerAddImpl.class)
            .in(Singleton.class);
        //@formatter:on
    }

    private void configureEditForm()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(EditEscalationSchemeForm.class, EditEscalationSchemeForm.class)
            .build(new TypeLiteral<EscalationSchemeFormFactory<EditEscalationSchemeForm>>(){}));
        bind(new TypeLiteral<EscalationSchemeFormMessages<ObjectFormEdit>>(){})
            .to(EditEscalationSchemeFormMessages.class).in(Singleton.class);
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<AsyncCallback<SimpleResult<DtoContainer<EscalationScheme>>>>(){}, EditEscalationSchemeFormApplyCallback.class)
            .build(new TypeLiteral<EscalationSchemeFormCallbackFactory<ObjectFormEdit>>(){}));
        install(PropertyControllerGinModule.create(EscalationScheme.class, ObjectFormEdit.class)
            .setPropertyControllerFactory(new TypeLiteral<EscalationSchemeFormPropertyControllerFactoryImpl<ObjectFormEdit>>(){})
            .setPropertyParametersDescriptorFactory(new TypeLiteral<EscalationSchemeFormPropertyParametersDescriptorFactoryImpl<ObjectFormEdit>>(){}));
        bind(new TypeLiteral<EscalationSchemePropertyContainerAfterBindHandler<ObjectFormEdit>>(){})
            .to(EscalationSchemePropertyContainerAfterBindHandlerEditImpl.class)
            .in(Singleton.class);
       
        //@formatter:on
    }
}
