package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.RichTextAreaProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;

/**
 * <AUTHOR>
 *
 */
public class RichTextAreaBindDeletageImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<String, RichTextAreaProperty>
{
    @Override
    public void bindProperty(PropertyContainerContext context, RichTextAreaProperty property,
            AsyncCallback<Void> callback)
    {
        property.getValueWidget().setAreaHeight(100);
        callback.onSuccess(null);
    }
}
