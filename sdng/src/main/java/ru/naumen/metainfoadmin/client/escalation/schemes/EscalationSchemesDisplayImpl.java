package ru.naumen.metainfoadmin.client.escalation.schemes;

import com.google.inject.Inject;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.CoreGinjector.CellTableWithRowsIdFactory;
import ru.naumen.core.client.common.DefaultAsyncDataProviderSimpleResult;
import ru.naumen.core.client.components.table.TableWithToolPanelDisplayImpl;
import ru.naumen.core.client.styles.HoverCellTableResources;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfo.shared.dispatch2.GetEscalationSchemesAction;

/**
 * <AUTHOR>
 * @since 23.07.2012
 *
 */
public class EscalationSchemesDisplayImpl extends TableWithToolPanelDisplayImpl<DtoContainer<EscalationScheme>>
{
    @Inject
    public EscalationSchemesDisplayImpl(
            CellTableWithRowsIdFactory<DtoContainer<EscalationScheme>, HoverCellTableResources> tableFactory,
            DefaultAsyncDataProviderSimpleResult<DtoContainer<EscalationScheme>, GetEscalationSchemesAction> dataProvider)
    {
        super(tableFactory.create(dataProvider));
        table.addStyleName(AdminWidgetResources.INSTANCE.tables().tableElems());
        dataProvider.addDataDisplay(table);
        setCaptionVisible(false);
    }
}