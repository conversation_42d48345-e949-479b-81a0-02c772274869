package ru.naumen.metainfoadmin.client.attributes.forms.edit;

import java.util.Collection;

import com.google.common.collect.Collections2;

import java.util.ArrayList;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.events.ApplyFormEvent;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormApplyHandlerImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormValidationCode;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Обработчик нажатия на ОК формы редатиктирования атрибута. В зависимости от значения свойства inherit посылаем
 * разные action.
 * <AUTHOR>
 * @since 01.06.2012
 */
public class AttributeFormApplyHandlerEditImpl extends AttributeFormApplyHandlerImpl<ObjectFormEdit>
{
    @Inject
    public AttributeFormApplyHandlerEditImpl(@Assisted("contextProps") IProperties contextProps,
            @Assisted("propertyValues") IProperties propertyValues,
            @Assisted PropertyContainerPresenter propertyContainer, @Assisted Context context,
            @Assisted Presenter formPresenter, @Assisted AsyncCallback<MetaClass> callback)
    {
        super(contextProps, propertyValues, propertyContainer, context, formPresenter, callback);
    }

    @Override
    public void onApplyForm(ApplyFormEvent event)
    {
        Boolean inherit = propertyValues.getProperty(AttributeFormPropertyCode.INHERIT);
        if (Boolean.TRUE.equals(inherit))
        {
            resetAttribute();
        }
        else
        {
            modifyAttribute();
        }
    }

    private Collection<ClassFqn> getPermittedTypes()
    {
        Collection<DtObject> permittedTypes = propertyValues.getProperty(AttributeFormPropertyCode.PERMITTED_TYPES);
        if (permittedTypes == null)
        {
            return new ArrayList<>();
        }
        else
        {
            return Collections2.transform(permittedTypes, DtObject.FQN_EXTRACTOR);
        }
    }

    private void resetAttribute()
    {
        Attribute attr = contextProps.getProperty(AttributeFormContextValues.ATTRIBUTE);
        Collection<ClassFqn> permittedTypes = getPermittedTypes();
        if (!propertyContainer.validate(AttributeFormValidationCode.PERM_TYPES))
        {
            return;
        }
        metainfoModificationService.resetAttribute(attr, permittedTypes,
                new Callback(eventBus, context, formPresenter, true, callback));
    }
}
