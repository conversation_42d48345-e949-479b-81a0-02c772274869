package ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms;

import com.google.gwt.dom.client.Element;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.RootPanel;

import jakarta.inject.Inject;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsPlace;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.ThemeMessages;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.ThemePlace;

/**
 * <AUTHOR>
 * @since 09 сент. 2016 г.
 *
 */
public class ThemePresenter<P extends ThemePlace> extends AdminTabPresenter<P>
{
    @Inject
    protected ThemeMessages themeMessages;
    @Inject
    private ThemeFormPresenterBase<P> innerPresenter;
    @Inject
    private ThemeFormResources resources;

    private Element previousScrollableRootPanelElement;

    @Inject
    public ThemePresenter(AdminTabDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void unbind()
    {
        super.unbind();
        RootPanel.setScrollableRootPanel(previousScrollableRootPanelElement, null, false);
        RootPanel.removeRootPanel(getScrollableRootElement());
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();
        resources.css().ensureInjected();
        innerPresenter.getDisplay().asWidget().addStyleName(resources.css().themeForm());

        innerPresenter.init(getPlace());
        addContentWithoutDecoration(innerPresenter, "themeForm");

        prevPageLinkPresenter.bind(themeMessages.backToInterfaceSettings(), InterfaceSettingsPlace.INSTANCE);

        previousScrollableRootPanelElement = RootPanel.getScrollableRootPanelElement();
        RootPanel.setScrollableRootPanel(getScrollableRootElement(),
                innerPresenter.getDisplay(), false);
    }

    private Element getScrollableRootElement()
    {
        return innerPresenter.getDisplay().asWidget().getElement().getParentElement();
    }

}