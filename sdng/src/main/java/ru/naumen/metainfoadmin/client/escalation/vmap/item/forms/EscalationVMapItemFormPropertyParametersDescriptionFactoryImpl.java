/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import com.google.inject.Inject;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap.VMapItemFormPropertyParametersDescriptionFactoryImpl;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationVMapItemFormPropertyParametersDescriptionFactoryImpl<F extends ObjectForm>
        extends VMapItemFormPropertyParametersDescriptionFactoryImpl<EscalationValueMapItemFormContext, F>
{
    @Inject
    EscalationVMapItemFormMessages escalationMessages;

    @Override
    protected void build()
    {
        //@formatter:off
        registerOrModifyProperty(CatalogItem.ITEM_TITLE,                cmessages.title(),                   true,  CatalogItem.ITEM_TITLE,      0, true, true);
        registerOrModifyProperty(CatalogItem.ITEM_CODE,                 cmessages.code(),                    true,  CatalogItem.ITEM_CODE,       1, true, true);
        registerOrModifyProperty(ValueMapCatalogItem.LINKED_CLASSES,    cmessages.objects(),                 true,  "targetMetaClass",           2, true, true);
        registerOrModifyProperty(ValueMapCatalogItem.TARGET_ATTRS,      messages.dependentAttributes(),      false, "targetAttrs",               3, true, true);
        registerOrModifyProperty(ValueMapCatalogItem.DEFAULT_OBJECT,    escalationMessages.defaultObject(),  false, "defaultObject",             4, true, true);
        registerOrModifyProperty(ValueMapCatalogItem.SOURCE_ATTRS,      messages.definableAttributes(),      true,  "sourceAttrs",               5, true, true);
        registerOrModifyProperty(ValueMapCatalogItem.DESCRIPTION,       cmessages.description(),             false, "description",               6, true, true);
        registerOrModifyProperty(ValueMapCatalogItem.SETTINGS_SET,       adminDialogMessages.settingsSet(),             false,
                "settingsSet",               7, true, false);

        //@formatter:on
    }
}
