package ru.naumen.metainfoadmin.client.embeddedapplications.form;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.EmbeddedApplicationAsyncServiceImpl;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;

/**
 * <AUTHOR>
 * @since 07.07.2016
 *
 */
public class AddEmbeddedApplicationFormPresenter extends AbstractEmbeddedApplicationFormPresenter
{
    private final TransliterationService transliterator;
    private final SecurityHelper security;
    private final SharedSettingsClientService settingsService;
    private final EmbeddedApplicationAsyncServiceImpl adminSettingsService;

    @Inject
    public AddEmbeddedApplicationFormPresenter(PropertyDialogDisplay display, EventBus eventBus,
            TransliterationService transliterator, SecurityHelper security,
            SharedSettingsClientService settingsService,
            EmbeddedApplicationAsyncServiceImpl adminSettingsService)
    {
        super(display, eventBus);
        this.transliterator = transliterator;
        this.security = security;
        this.settingsService = settingsService;
        this.adminSettingsService = adminSettingsService;
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaptionText(messages.applicationAdding());
        super.onBind();

        initialHeight.setValue(400L); //NOPMD
        applicationType.trySetObjValue(EmbeddedApplicationType.ExternalApplication.name());
        fullscreenAllowed.setValue(settingsService.isContentFullscreenEnabled());
    }

    @Override
    protected void registerHandlers()
    {
        super.registerHandlers();
        registerHandler(getTitle().addValueChangeHandler(event ->
        {
            String title = getTitle().getValue();
            String code = getCode().getValue();
            if (StringUtilities.isEmptyTrim(title) || !StringUtilities.isEmptyTrim(code))
            {
                return;
            }
            code = transliterator.transliterateToCode(title, Constants.MAX_ID_LENGTH,
                    security.hasVendorProfile() ? Constants.CODE_SPECIAL_CHARS_FOR_VENDOR_FOR_EMBEDDED_APPLICATION
                            : StringUtilities.EMPTY);
            getCode().setValue(code);
        }));
    }

    @Override
    protected void saveApplication(ReadyState readyState)
    {
        adminSettingsService.saveEmbeddedApplication(application, true, true, null, false,
                new CallbackDecorator<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto>(
                        readyState,
                        saveCallback)
                {
                    @Override
                    public void onFailure(Throwable caught)
                    {
                        getDisplay().addErrorMessage(caught.getMessage());
                    }
                });
    }
}
