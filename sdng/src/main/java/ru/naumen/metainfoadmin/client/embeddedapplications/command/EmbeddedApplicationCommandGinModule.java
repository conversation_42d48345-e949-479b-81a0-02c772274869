package ru.naumen.metainfoadmin.client.embeddedapplications.command;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;

/**
 * <AUTHOR>
 * @since 07.07.2016
 *
 */
public class EmbeddedApplicationCommandGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        //@formatter:off
        bind(EmbeddedApplicationCommandFactoryInitializer.class).asEagerSingleton();
        
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, new TypeLiteral<AddEmbeddedApplicationCommand>(){})
            .build(new TypeLiteral<CommandProvider<AddEmbeddedApplicationCommand, CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, EditEmbeddedApplicationCommand.class)
            .build(new TypeLiteral<CommandProvider<EditEmbeddedApplicationCommand, CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, DeleteEmbeddedApplicationCommand.class)
            .build(new TypeLiteral<CommandProvider<DeleteEmbeddedApplicationCommand, CommandParam<EmbeddedApplicationAdminSettingsDto, Void>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, EnableEmbeddedApplicationCommand.class)
            .build(new TypeLiteral<CommandProvider<EnableEmbeddedApplicationCommand, CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, DisableEmbeddedApplicationCommand.class)
            .build(new TypeLiteral<CommandProvider<DisableEmbeddedApplicationCommand,
                    CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, ToggleEmbeddedApplicationCommand.class)
            .build(new TypeLiteral<CommandProvider<ToggleEmbeddedApplicationCommand,
                    CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto>>>() {}));
        //@formatter:on
    }
}
