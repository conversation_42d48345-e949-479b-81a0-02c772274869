package ru.naumen.metainfoadmin.client.templates.list.card;

import static java.util.stream.Collectors.joining;

import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.FactoryParam.ValueSource;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ListTemplate;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorMessages;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesCommandCode;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMessages;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesPlace;

/**
 * Блок "Свойства" на карточке.
 * <AUTHOR>
 * @since 19.04.2018
 */
public class ListTemplateInfoPresenter extends BasicPresenter<InfoDisplay>
{
    private class ListTemplateInfoCommandParam extends CommandParam<List<DtObject>, DtObject>
    {
        public ListTemplateInfoCommandParam(AsyncCallback<DtObject> callback)
        {
            super(valueSource, callback);
        }
    }

    private class ListTemplateInfoEditCommandParam extends CommandParam<DtObject, DtObject>
    {
        public ListTemplateInfoEditCommandParam(AsyncCallback<DtObject> callback)
        {
            super(valueSourceEdit, callback);
        }
    }

    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> clazz;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> cases;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> creationDate;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> lastModifiedDate;
    private Property<String> settingsSet;

    @Inject
    private CommonMessages cmessages;
    @Inject
    private ListTemplatesMessages messages;
    @Inject
    private ContentCreatorMessages contentCreatorMessages;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private PlaceController placeController;
    @Inject
    private Formatters formatters;
    @Inject
    private AdminMetainfoServiceAsync metainfoService;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    private DtObject template;
    private OnStartCallback<DtObject> refreshCallback;
    private ToolBarDisplayMediator<DtObject> toolBar;

    private OnStartCallback<DtObject> removeCallback = new SafeOnStartBasicCallback<DtObject>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            placeController.goTo(ListTemplatesPlace.INSTANCE);
        }

        ;
    };

    private ValueSource<List<DtObject>> valueSource = new ValueSource<List<DtObject>>()
    {
        @Override
        public List<DtObject> getValue()
        {
            return Lists.newArrayList(template);
        }

        ;
    };

    private ValueSource<DtObject> valueSourceEdit = new ValueSource<DtObject>()
    {
        @Override
        public DtObject getValue()
        {
            return template;
        }

        ;
    };

    @Inject
    public ListTemplateInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        display.asWidget().ensureDebugId("props");
        toolBar = new ToolBarDisplayMediator<>(display.getToolBar());
    }

    public void init(OnStartCallback<DtObject> refreshCallback)
    {
        settingsSet = settingsSetOnFormCreator.createFieldOnCard();
        this.refreshCallback = refreshCallback;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();

        title.setValue(template.getTitle());
        code.setValue(template.getUUID());
        MetaClassLite classValue = ((Collection<MetaClassLite>)template.getProperty(ListTemplate.CLASS)).iterator()
                .next();
        clazz.setValue(classValue.getTitle());
        Collection<ClassFqn> caseValue = template.getProperty(ListTemplate.CASE);
        settingsSetOnFormCreator.setValueOnCardProperty(template.getProperty(ListTemplate.SETTINGS_SET), settingsSet);
        metainfoService.getMetaClassesTitle(caseValue, new BasicCallback<HashMap<ClassFqn, String>>(getDisplay())
        {
            @Override
            protected void handleSuccess(HashMap<ClassFqn, String> map)
            {
                cases.setValue(caseValue.stream().map(map::get).collect(joining(", ")));
            }
        });
        creationDate.setValue(formatters.formatDateTime((Date)template.getProperty(ListTemplate.CREATION_DATE)));
        lastModifiedDate
                .setValue(formatters.formatDateTime((Date)template.getProperty(ListTemplate.LAST_MODIFIED_DATE)));
        toolBar.refresh(template);
    }

    public void setTemplate(DtObject template)
    {
        this.template = template;
        refreshDisplay();
    }

    protected void bindProperties()
    {
        title.setCaption(cmessages.title());
        getDisplay().add(title);
        code.setCaption(cmessages.code());
        getDisplay().add(code);
        clazz.setCaption(contentCreatorMessages.objectClass());
        getDisplay().add(clazz);
        cases.setCaption(contentCreatorMessages.objectsTypes());
        getDisplay().add(cases);
        creationDate.setCaption(messages.creationDate());
        getDisplay().add(creationDate);
        lastModifiedDate.setCaption(messages.lastModifiedDate());
        getDisplay().add(lastModifiedDate);
        if (settingsSet != null)
        {
            getDisplay().add(settingsSet);
        }
    }

    protected void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(clazz, "class");
        DebugIdBuilder.ensureDebugId(cases, "cases");
        DebugIdBuilder.ensureDebugId(creationDate, "creationDate");
        DebugIdBuilder.ensureDebugId(lastModifiedDate, "lastModifiedDate");
    }

    @SuppressWarnings("unchecked")
    protected void initToolBar()
    {
        ButtonPresenter<DtObject> buttonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(ButtonCode.EDIT,
                cmessages.edit(), ListTemplatesCommandCode.EDIT, new ListTemplateInfoEditCommandParam(refreshCallback));
        buttonPresenter.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        toolBar.add(buttonPresenter);
        buttonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(ButtonCode.DELETE, cmessages.delete(),
                ListTemplatesCommandCode.DELETE, new ListTemplateInfoCommandParam(removeCallback));
        buttonPresenter.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE));
        toolBar.add(buttonPresenter);
        toolBar.bind();
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(cmessages.properties());
        initToolBar();
        bindProperties();
        ensureDebugIds();
        refreshDisplay();
    }
}
