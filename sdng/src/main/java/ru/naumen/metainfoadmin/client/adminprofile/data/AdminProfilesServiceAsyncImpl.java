package ru.naumen.metainfoadmin.client.adminprofile.data;

import java.util.List;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.adminprofile.dispatch.AddAdminProfileAction;
import ru.naumen.metainfo.shared.adminprofile.dispatch.DeleteAdminProfilesAction;
import ru.naumen.metainfo.shared.adminprofile.dispatch.EditAdminProfileAction;
import ru.naumen.metainfo.shared.adminprofile.dispatch.GetAdminProfileAction;

/**
 * Реализация асинхронного сервиса управления профилями администрирования
 * <AUTHOR>
 * @since 24.01.2023
 */
@Singleton
public class AdminProfilesServiceAsyncImpl implements AdminProfilesServiceAsync
{
    @Inject
    private DispatchAsync dispatch;

    @Override
    public void addAdminProfile(DtObject adminProfile, AsyncCallback<DtObject> callback)
    {
        dispatch.execute(new AddAdminProfileAction(adminProfile),
                new SimpleResultCallbackDecorator<DtObject>(callback));
    }

    @Override
    public void deleteAdminProfile(String code, AsyncCallback<String> callback)
    {
        dispatch.execute(new DeleteAdminProfilesAction(Lists.<String> newArrayList(code)),
                new SimpleResultCallbackDecorator<String>(callback));
    }

    @Override
    public void deleteAdminProfiles(List<String> codes, AsyncCallback<String> callback)
    {
        dispatch.execute(new DeleteAdminProfilesAction(codes),
                new SimpleResultCallbackDecorator<String>(callback));
    }

    @Override
    public void editAdminProfile(String code, DtObject adminProfile, AsyncCallback<DtObject> callback)
    {
        dispatch.execute(new EditAdminProfileAction(code, adminProfile),
                new SimpleResultCallbackDecorator<DtObject>(callback));
    }

    @Override
    public void getAdminProfile(String code, AsyncCallback<DtObject> callback)
    {
        dispatch.execute(new GetAdminProfileAction(code), new SimpleResultCallbackDecorator<DtObject>(callback));
    }
}
