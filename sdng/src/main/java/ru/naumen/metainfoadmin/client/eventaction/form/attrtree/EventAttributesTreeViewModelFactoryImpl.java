package ru.naumen.metainfoadmin.client.eventaction.form.attrtree;

import ru.naumen.core.client.tree.dto.DtoTreeGinModule;
import ru.naumen.core.client.tree.dto.view.DtoTreeViewModelFactoryImpl;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormGinModule;

import jakarta.inject.Inject;

/**
 * Фабрика модели для дерева атрибутов ДПС {@link EventActionFormGinModule.EventAttributesTree}
 *
 * <AUTHOR>
 * @since 09.03.2022
 */
public class EventAttributesTreeViewModelFactoryImpl extends
        DtoTreeViewModelFactoryImpl<EventActionFormGinModule.EventAttributesTree, DtoTreeGinModule.WithoutFolders,
                EventAttributesSelectionModel, EventAttributesTreeFactoryContext>
{
    @Inject
    private EventActionFormGinModule.EventAttributesSelectionModelFactory modelFactory;

    @Override
    protected EventAttributesSelectionModel createSelectionModel(EventAttributesTreeFactoryContext context)
    {
        return modelFactory.create();
    }
}
