package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelElementWrapper;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.QuickAccessTileFormPresenter;

/**
 * Команда редактирования плитки быстрого доступа
 *
 * <AUTHOR>
 * @since 17.07.2020
 */
public class EditQuickAccessTileCommand extends BaseCommandImpl<QuickAccessPanelElementWrapper,
        DtoContainer<NavigationSettings>>
{
    public static final String ID = "editQuickTileCommand";

    @Inject
    Provider<QuickAccessTileFormPresenter<ObjectFormEdit>> editQuickTileFormProvider;

    @Inject
    public EditQuickAccessTileCommand(@Assisted QuickAccessPanelTileCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<QuickAccessPanelElementWrapper, DtoContainer<NavigationSettings>> param)
    {
        QuickAccessPanelTileCommandParam p = (QuickAccessPanelTileCommandParam)param;

        QuickAccessTileFormPresenter<ObjectFormEdit> presenter = editQuickTileFormProvider.get();

        QuickAccessTileDTO tile = (QuickAccessTileDTO)p.getValue().getWrappable();
        String lmItemCode = tile.getMenuItemCode();
        presenter.init(p.getSettings().get(), lmItemCode, tile, p.getCallback());
        presenter.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    public boolean isPossible(Object input)
    {
        return input instanceof QuickAccessPanelElementWrapper && ((QuickAccessPanelElementWrapper)input)
                .getWrappable() instanceof QuickAccessTileDTO;
    }
}