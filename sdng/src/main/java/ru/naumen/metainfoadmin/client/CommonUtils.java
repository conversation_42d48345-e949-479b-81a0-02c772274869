package ru.naumen.metainfoadmin.client;

import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.metainfo.shared.MetainfoUtils.getStateResponsibleEventFqn;
import static ru.naumen.metainfo.shared.filters.RelationFilters.and;
import static ru.naumen.metainfo.shared.filters.RelationFilters.isParent;
import static ru.naumen.metainfo.shared.filters.RelationFilters.left;
import static ru.naumen.metainfo.shared.filters.RelationFilters.not;
import static ru.naumen.metainfo.shared.filters.RelationFilters.right;

import java.util.List;

import jakarta.inject.Inject;

import com.google.common.base.Predicate;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.Dialogs.Buttons;
import ru.naumen.core.client.common.Dialogs.DialogResult;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.ITitled;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.Relation;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Вспомогательные методы, многократно используемый код
 *
 * <AUTHOR>
 *
 */

public class CommonUtils
{

    @Inject
    AdminDialogMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    MetainfoUtils metainfoUtils;
    @Inject
    AdminMetainfoServiceAsync metainfoService;
    @Inject
    MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    Dialogs dialogs;
    @Inject
    EventBus eventBus;

    public void archiveClassMetainfo(final MetaClassLite cls, final AsyncCallback<MetaClass> callback)
    {
        String type = cmessages.type();
        String additionalInfo = cmessages.removeTypeMessage();
        if (StringUtilities.isEmptyTrim(cls.getFqn().getCase()))
        {
            type = cmessages.clazzToLowerCase();
            additionalInfo = cmessages.removeClassMessage();
        }
        dialogs.question(cmessages.confirmRemove(), cmessages.confirmRemoveQuestion(type, cls.getTitle()),
                additionalInfo, new DialogCallback()
                {
                    @Override
                    protected void handleSuccess(DialogResult result)
                    {
                        if (Buttons.YES.equals(result.getButtons()))
                        {
                            result.getWidget().hide();
                            metainfoModificationService.removeClassMetainfo(cls.getFqn(), callback);
                        }
                        else
                        {
                            result.getWidget().hide();
                            callback.onSuccess(null);
                        }
                    }
                });
    }

    public void delClassMetainfo(final MetaClassLite metaClass, final AsyncCallback<Void> callback)
    {
        metainfoService.getDescendantClasses(metaClass.getFqn(), false, new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> cases)
            {
                final StringBuilder additionalInfo = new StringBuilder();
                if (!cases.isEmpty())
                {
                    String caseTitles = collectTitles(cases);
                    String caseTitleMsg = metaClass.getFqn().isClass() ? cmessages.deleteClassWithCases(caseTitles)
                            : cmessages.deleteTypeMessageWithCases(caseTitles);
                    additionalInfo.append(caseTitleMsg);
                }
                if (metaClass.getFqn().isClass())
                {
                    additionalInfo.append(cases.isEmpty() ? "" : "<br>");
                    Predicate<Relation> filter = and(and(right(metaClass.getFqn()), isParent()),
                            not(left(getStateResponsibleEventFqn(metaClass.getFqn()))));
                    metainfoService.getRelatedMetaClasses(filter, true, new BasicCallback<List<MetaClassLite>>()
                    {
                        @Override
                        protected void handleSuccess(List<MetaClassLite> classes)
                        {
                            if (!classes.isEmpty())
                            {
                                String classTitleMsg = cmessages.deleteClassWithInnerClasses(collectTitles(classes));
                                additionalInfo.append(classTitleMsg).append("<br>")
                                        .append(cmessages.deleteClassClearParent());
                            }
                            questAndDelete(metaClass, callback, cmessages.type(), additionalInfo.toString());
                        }
                    });
                }
                else
                {
                    questAndDelete(metaClass, callback, cmessages.type(), additionalInfo.toString());
                }
            }

            private String collectTitles(List<MetaClassLite> metaClasses)
            {
                return make(metaClasses).map(ITitled.TITLE_EXTRACTOR).join(", ");
            }
        });
    }

    /**
     * Возвращает подтверждающий вопрос при сбросе настроек
     * @param objForResetSettings настройки чего сбрасываются (групп атрибутов, настройки карточки объекта и т.д.)
     * @param fqn метакласс, в котором происходит сброс настроек
     * @return
     */
    public String getConfirmResetQuestion(String objForResetSettings, ClassFqn fqn)
    {
        return fqn.isCase() ? cmessages.confirmResetSettingsQuestion(objForResetSettings) : cmessages
                .confirmUseSystemSettingsQuestion(objForResetSettings);
    }

    public void restoreClassMetainfo(final MetaClassLite cls, final AsyncCallback<MetaClass> callback)
    {
        String type = cmessages.type();
        String additionalInfo = cmessages.restoreTypeMessage();
        if (StringUtilities.isEmptyTrim(cls.getFqn().getCase()))
        {
            type = cmessages.clazzToLowerCase();
            additionalInfo = cmessages.restoreClassMessage();
        }
        dialogs.question(cmessages.confirmRestore(), cmessages.confirmRestoreQuestion(type, cls.getTitle()),
                additionalInfo, new DialogCallback()
                {
                    @Override
                    protected void handleSuccess(DialogResult result)
                    {
                        if (Buttons.YES.equals(result.getButtons()))
                        {
                            result.getWidget().hide();
                            metainfoModificationService.restoreClassMetainfo(cls.getFqn(), callback);
                        }
                        else
                        {
                            result.getWidget().hide();
                            callback.onSuccess(null);
                        }
                    }
                });
    }

    /**
     * Наследуется ли настройка карточки объекта из родительского метакласса
     * @param context
     * @return
     */
    public boolean uiNotInherit(UIContext context)
    {
        return context.getMetainfo().getFqn().isClass()
               || context.getMetainfo().getFqn().equals(context.getRootContentInfo().getDeclaredMetaclass());
    }

    private void questAndDelete(final MetaClassLite cls, final AsyncCallback<Void> callback, String type,
            String additionalInfo)
    {
        dialogs.question(cmessages.confirmDelete(), cmessages.confirmDeleteQuestion(type, cls.getTitle()),
                additionalInfo, new DialogCallback()
                {
                    @Override
                    protected void handleSuccess(DialogResult result)
                    {
                        if (Buttons.YES.equals(result.getButtons()))
                        {
                            result.getWidget().hide();
                            metainfoModificationService.delClassMetainfo(cls.getFqn(), cls.getParent(), callback);
                        }
                        else
                        {
                            result.getWidget().hide();
                            callback.onSuccess(null);
                        }
                    }
                });
    }
}
