package ru.naumen.metainfoadmin.client.eventaction.form.creator;

import static ru.naumen.core.client.widgets.id.DebugIdBuilder.ensureDebugId;

import java.util.List;

import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.script.places.EventActionCategories;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.push.PushEventAction;
import ru.naumen.metainfo.shared.eventaction.push.PushPosition;
import ru.naumen.metainfo.shared.eventaction.push.PushPresentationType;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;
import ru.naumen.metainfoadmin.client.eventaction.template.TemplateEditPropertiesController;

/**
 * Создание свойств на форме для Действия по событию типа Пуш-уведомление
 *
 * <AUTHOR>
 * @since 16.10.2015
 */
public class PushEventActionFormPropertiesCreator
        extends PushEventActionFormPropertiesCreatorBase<PushEventAction>
{
    @Inject
    private TemplateEditPropertiesController templateProperties;
    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    private SelectListProperty<String, SelectItem> pushPresentationType;
    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    private SelectListProperty<String, SelectItem> pushPosition;
    private HandlerRegistration typeChangeHandlerRegistration = null;

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {
        super.afterAddProperties(display);
        typeChangeHandlerRegistration = pushPresentationType.addValueChangeHandler(
                event -> onPushPresentationTypeChanged(display));
    }

    @Override
    public void bindProperties(EventActionFormDisplay display, List<ClassFqn> fqns)
    {
        super.bindProperties(display, fqns);

        message = templateProperties.getMessageProperty();

        add(messages.template(), templateProperties.getTemplateProperty());

        bindPushMessageProperties();

        bindPresentationList();
        bindPushPositionList();

        bindPropertiesAfter(display, fqns, EventActionCategories.EVENTACTION_PUSH_CUSTOMIZATION);

        prepareRichTextWidget();

        templateProperties.initProperties(display);
    }

    private void bindPresentationList()
    {
        add(messages.howToDisplayNotice(), pushPresentationType);

        final SingleSelectCellList<?> presentationList = pushPresentationType.getValueWidget();
        presentationList.addItem(messages.onlyInTheInterface(), PushPresentationType.OnlyInTheInterface.toString());
        presentationList.addItem(messages.onlyExternalPush(), PushPresentationType.OnlyExternalPush.toString());
        presentationList.addItem(messages.alwaysInTheInterface(),
                PushPresentationType.AlwaysInTheInterface.toString());
        presentationList.addItem(messages.interfaceAndBrowserNotices(),
                PushPresentationType.InterfaceAndBrowserNotices.toString());
    }

    private void bindPushPositionList()
    {
        if (null == eventAction
            || getAction(eventAction).getPushPresentationType() != PushPresentationType.OnlyExternalPush)
        {
            add(messages.pushPosition(), pushPosition);
        }

        SingleSelectCellList<?> positionList = pushPosition.getValueWidget();
        positionList.addItem(messages.positionBottomRight(), PushPosition.BottomRight.name());
        positionList.addItem(messages.positionTopRight(), PushPosition.TopRight.name());
        positionList.addItem(messages.positionBottomLeft(), PushPosition.BottomLeft.name());
        positionList.addItem(messages.positionTopLeft(), PushPosition.TopLeft.name());
        positionList.addItem(messages.positionSystem(), PushPosition.System.name());
    }

    @Override
    protected void setActionProperties(PushEventAction action)
    {
        super.setActionProperties(action);
        templateProperties.getPropertiesValues(action);

        PushPosition position = PushPosition.fromString(SelectListPropertyValueExtractor.getValue(pushPosition),
                PushPosition.BottomRight);
        action.setPosition(position);
        action.setPushPresentationType(extractPushPresentationType());
    }

    @Override
    protected Action newEventActionTypeInstance()
    {
        return new PushEventAction();
    }

    @Override
    public void init(@Nullable EventActionWithScript eventAction, Property<SelectItem> event)
    {
        super.init(eventAction, event);
        if (eventAction != null && !(eventAction.getObject().getAction() instanceof PushEventAction))
        {
            throw new IllegalArgumentException("EventAction must be PushEventAction");
        }
        this.eventAction = eventAction;
    }

    @Override
    protected void ensureDebugIds()
    {
        super.ensureDebugIds();
        templateProperties.ensureDebugIds();
        ensureDebugId(pushPosition, "pushPosition");
        ensureDebugId(pushPresentationType, "pushPresentationType");
    }

    @Override
    protected void setPropertiesValuesForNew()
    {
        super.setPropertiesValuesForNew();
        pushPosition.trySetObjValue(PushPosition.BottomRight.name());
        pushPresentationType.trySetObjValue(PushPresentationType.AlwaysInTheInterface.toString());
    }

    @Override
    protected void setPropertiesValuesForExisted(PushEventAction action)
    {
        super.setPropertiesValuesForExisted(action);
        if (null != action.getPosition())
        {
            pushPosition.trySetObjValue(action.getPosition().name());
        }
        if (action.getPushPresentationType() != null)
        {
            pushPresentationType.trySetObjValue(action.getPushPresentationType().toString());
        }
        else
        {
            pushPresentationType.trySetObjValue(PushPresentationType.AlwaysInTheInterface.toString());
        }
    }

    @Override
    protected void setPropertiesValuesAsync(ReadyState readyState)
    {
        super.setPropertiesValuesAsync(readyState);
        templateProperties.setPropetiesValues(isEventActionExists() ? getAction(eventAction) : null, readyState);
    }

    private PushPresentationType extractPushPresentationType()
    {
        PushPresentationType pushPresentationValue;
        try
        {
            pushPresentationValue = PushPresentationType
                    .valueOf(SelectListPropertyValueExtractor.getValue(pushPresentationType));
        }
        catch (Exception e)
        {
            pushPresentationValue = PushPresentationType.AlwaysInTheInterface;
        }
        return pushPresentationValue;
    }

    private void onPushPresentationTypeChanged(PropertyDialogDisplay display)
    {
        PushPresentationType pushPresentation = extractPushPresentationType();
        PropertyRegistration<SelectItem> pushPositionPR = getPropertyRegistration(pushPosition);
        if (PushPresentationType.OnlyExternalPush == pushPresentation && null != pushPositionPR)
        {
            remove(pushPosition);
        }
        else if (PushPresentationType.OnlyExternalPush != pushPresentation && null == pushPositionPR)
        {
            pushPosition.setCaption(messages.pushPosition());
            register(pushPosition, display.addPropertyAfter(pushPosition,
                    getPropertyRegistration(pushPresentationType)));
            add(pushPosition, indexOf(pushPresentationType) + 1);
        }
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        if (typeChangeHandlerRegistration != null)
        {
            typeChangeHandlerRegistration.removeHandler();
        }
        templateProperties.unbind(callback);
        super.unbind(callback);
    }
}