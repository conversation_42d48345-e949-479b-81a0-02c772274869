package ru.naumen.metainfoadmin.client.customforms;

import com.google.gwt.event.shared.GwtEvent;

import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.dto.DtoContainer;

/**
 * Событие изменения настройки формы
 *
 * <AUTHOR>
 * @since 21 апр. 2016 г.
 */
public class FormSettingsChangedEvent extends GwtEvent<FormSettingsChangedEventHandler>
{
    private static Type<FormSettingsChangedEventHandler> TYPE = new Type<FormSettingsChangedEventHandler>();

    public static Type<FormSettingsChangedEventHandler> getType()
    {
        return TYPE;
    }

    private DtoContainer<CustomForm> customForm;

    public FormSettingsChangedEvent(DtoContainer<CustomForm> customForm)
    {
        this.customForm = customForm;
    }

    @Override
    public Type<FormSettingsChangedEventHandler> getAssociatedType()
    {
        return TYPE;
    }

    /**
     * Возвращает форму
     *
     * @return
     */
    public DtoContainer<CustomForm> getCustomForm()
    {
        return customForm;
    }

    @Override
    protected void dispatch(FormSettingsChangedEventHandler handler)
    {
        handler.onSettingsChanged(this);
    }
}
