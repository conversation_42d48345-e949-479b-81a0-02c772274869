package ru.naumen.metainfoadmin.client.fastlink.settings.forms;

import java.util.ArrayList;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsGinModule.FastLinkSettingFormPropertyCode;

/**
 * <AUTHOR>
 * @since 01.03.18
 */
public class AddFastLinkSettingForm extends FastLinkSettingForm
{
    @Inject
    public AddFastLinkSettingForm(DefaultPropertyFormDisplayImpl display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected boolean isNewFastLinkSetting()
    {
        return true;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(fastLikSettingsMessages.addObjectMentionSetting());
    }

    @Override
    protected void setProperties()
    {
        propertyValues = new MapProperties();
        contextProps = new MapProperties();
        propertyValues.setProperty(FastLinkSettingFormPropertyCode.MENTION_TYPES, new ArrayList<>());
        propertyValues.setProperty(FastLinkSettingFormPropertyCode.CONTEXT_TYPES, new ArrayList<>());
        propertyValues.setProperty(FastLinkSettingFormPropertyCode.PROFILES, new ArrayList<>());
    }
}
