package ru.naumen.metainfoadmin.client.customforms.commands;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.customforms.CustomFormContext;
import ru.naumen.metainfoadmin.client.customforms.CustomFormsMessages;
import ru.naumen.metainfoadmin.client.customforms.FormSettingsChangedEvent;
import ru.naumen.metainfoadmin.shared.customforms.DeleteCustomFormParameterAction;

/**
 * Команды удаления параметра настраиваемой формы
 *
 * <AUTHOR>
 * @since 26 апр. 2016 г.
 */
public class DeleteCommand extends ObjectCommandImpl<Attribute, Void>
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private CustomFormsMessages messages;

    private CustomFormContext context;

    @Inject
    public DeleteCommand(@Assisted AttributeCommandParam param, Dialogs dialogs)
    {
        super(param, dialogs);
        context = (CustomFormContext)param.getContext();
    }

    @Override
    protected String getDialogMessage(Attribute value)
    {
        return messages.deleteParameterConfirmation(value.getTitle());
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    protected void onDialogSuccess(CommandParam<Attribute, Void> param)
    {
        dispatch.execute(
                new DeleteCustomFormParameterAction(param.getValue().getCode(), context.getModificationContext()),
                new BasicCallback<SimpleResult<DtoContainer<CustomForm>>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtoContainer<CustomForm>> result)
                    {
                        DtoContainer<CustomForm> container = result.get();
                        context.setPermissions(container.getProperty(Constants.SettingsSet.ADMIN_PERMISSIONS));
                        context.getEventBus().fireEvent(new FormSettingsChangedEvent(container));
                    }
                });
    }
}
