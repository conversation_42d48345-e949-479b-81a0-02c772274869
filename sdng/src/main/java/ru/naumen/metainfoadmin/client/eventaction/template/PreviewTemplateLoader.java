package ru.naumen.metainfoadmin.client.eventaction.template;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.gwt.event.shared.HandlerRegistration;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfoadmin.shared.Constants;

/**
 * Загрузчик шаблонов для предварительного просмотра.
 * Позволяет автоматически подгружать шаблон при его выборе из выпадающего списка.
 * <AUTHOR>
 * @since Jan 31, 2017
 */
public class PreviewTemplateLoader
{
    private Property<SelectItem> templateProperty;
    private TogglePreviewPropertyDecorator previewProperty;
    private HandlerRegistration changeHandlerRegistration;

    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    public void init(TogglePreviewPropertyDecorator messageProperty,
            SelectListProperty<String, SelectItem> templateProperty)
    {
        this.previewProperty = messageProperty;
        this.templateProperty = templateProperty;
        updateChangeHandler();
        loadTemplate();
    }

    private void loadTemplate()
    {
        if (null == previewProperty || null == templateProperty)
        {
            return;
        }
        String code = SelectListPropertyValueExtractor.getValue(templateProperty);
        if (null == code)
        {
            previewProperty.setTemplate(null);
            return;
        }

        metainfoService.getStyleTemplate(code, true, new BasicCallback<DtObject>()
        {
            @Override
            protected void handleSuccess(@Nullable DtObject value)
            {
                String template =
                        value == null ? null : value.<String> getProperty(Constants.StyleTemplate.TEMPLATE_TEXT);
                previewProperty.setTemplate(template);
            }
        });
    }

    private void updateChangeHandler()
    {
        if (null != changeHandlerRegistration)
        {
            changeHandlerRegistration.removeHandler();
            changeHandlerRegistration = null;
        }
        if (null != templateProperty)
        {
            changeHandlerRegistration = templateProperty.addValueChangeHandler(event -> loadTemplate());
        }
    }
}
