package ru.naumen.metainfoadmin.client.scheduler;

import jakarta.inject.Singleton;

import java.util.function.Function;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.gwt.place.shared.Place;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.core.client.CellTableWithRowsId;
import ru.naumen.core.client.CoreGinjector.CellTableWithRowsIdFactory;
import ru.naumen.core.client.widgets.columns.PlaceProvider;
import ru.naumen.core.shared.HasCode.HasCodeExtractor;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfoadmin.client.CatalogCellTableResources;
import ru.naumen.metainfoadmin.client.scheduler.command.SchedulerCommandGinjector;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.TriggerCreatorFactory;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.TriggerCreatorFactoryImpl;

/**
 *
 * <AUTHOR>
 */
public class SchedulerGinModule extends AbstractGinModule
{

    /**
     * {@inheritDoc}
     */
    @Override
    protected void configure()
    {
        bind(SchedulerTaskTypeFactory.class).to(SchedulerTaskTypeFactoryImpl.class).in(Singleton.class);
        bind(TriggerCreatorFactory.class).to(TriggerCreatorFactoryImpl.class).in(Singleton.class);
        bind(SchedulerCommandGinjector.class).to(SchedulerGinjector.class).in(Singleton.class);
        bind(SchedulerPlace.class).in(Singleton.class);

        //@formatter:off
        bind(new TypeLiteral<Function<? super DtoContainer<Trigger>, String>>(){})
            .annotatedWith(Names.named("rowIdExtractor"))
            .to(HasCodeExtractor.class)
            .in(Singleton.class);

        install(new GinFactoryModuleBuilder()
            .implement(Place.class, SchedulerTaskPlace.class)
            .build(new TypeLiteral<PlaceProvider<DtoContainer<SchedulerTask>>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(Place.class, TriggerPlace.class)
            .build(new TypeLiteral<PlaceProvider<DtoContainer<Trigger>>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<CellTableWithRowsId<DtoContainer<Trigger>, CatalogCellTableResources>>(){}, new TypeLiteral<CellTableWithRowsId<DtoContainer<Trigger>, CatalogCellTableResources>>(){})
            .build(new TypeLiteral<CellTableWithRowsIdFactory<DtoContainer<Trigger>, CatalogCellTableResources>>(){}));
        //@formatter:on
    }
}
