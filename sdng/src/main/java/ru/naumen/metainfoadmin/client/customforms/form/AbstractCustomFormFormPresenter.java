package ru.naumen.metainfoadmin.client.customforms.form;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.view.client.MultiSelectionModel;
import com.google.inject.Provider;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.tree.selection.HierarchicalMetaClassMultiSelectionModel;
import ru.naumen.core.client.validation.MetainfoShortKeyCodeValidator;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.IRadioButtonGroup;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.properties.SingleSelectPropertyWithEmptyOpt;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.client.widgets.tree.dto.DtoPopupMultiValueCellTree;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.client.ui.CustomFormMessages;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.AttributeGroupInfo;
import ru.naumen.metainfo.shared.dispatch2.GetDefaultMassEditFormAction;
import ru.naumen.metainfo.shared.dispatch2.GetPermittedTypesForSetupCustomFormResponse;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.customform.ChangeCaseForm;
import ru.naumen.metainfo.shared.ui.customform.Constants;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;
import ru.naumen.metainfo.shared.ui.customform.CustomFormBase;
import ru.naumen.metainfo.shared.ui.customform.CustomFormType;
import ru.naumen.metainfo.shared.ui.customform.MassEditForm;
import ru.naumen.metainfo.shared.ui.customform.QuickForm;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.AttrWidgetsHelper;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * Базовый презентер для добавления и редактирования кастомных форм
 * Карточка метакласса -> Другие формы.
 *
 * <AUTHOR>
 * @since 26.04.2016
 */
public abstract class AbstractCustomFormFormPresenter extends OkCancelPresenter<PropertyDialogDisplay>
        implements CallbackPresenter<CustomForm, CustomForm>
{
    /**
     * Вспомогательный класс для хранения информации о группах атрибутов метакласса
     */
    static class MetaClassAttributeGroups
    {
        private final List<DtObject> groups;

        public MetaClassAttributeGroups(Collection<AttributeGroupInfo> groupsToAdd)
        {
            groups = new ArrayList<>();
            for (AttributeGroupInfo groupToAdd : groupsToAdd)
            {
                groups.add(new SimpleDtObject(groupToAdd.getCode(), groupToAdd.getTitle(),
                        groupToAdd.getDeclaredMetaClass()));
            }
        }

        public List<DtObject> getGroups()
        {
            return groups;
        }
    }

    /**
     * Под полем должно отображаться не более 10 плашек
     */
    private static final int VISIBLE_SELECTED_ITEMS_LIMIT = 10;

    private static final int TITLE_MAX_LENGTH = 64;

    @Inject
    protected AdminMetainfoServiceAsync metainfoService;
    @Inject
    protected Processor validation;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    protected CustomFormMessages messages;
    @Inject
    protected MetainfoUtils metainfoUtils;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private MetainfoModificationServiceAsync metainfoModificationService;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    protected SelectListProperty<String, SelectItem> formTypeProperty;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> titleProperty;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> codeProperty;
    @Inject
    protected SingleSelectPropertyWithEmptyOpt<String> attributeGroup;
    @CheckForNull
    private PropertyRegistration<SelectItem> attributeGroupsPropertyPR;
    @Inject
    private Provider<NotEmptyCollectionValidator<Collection<DtObject>>> notEmptyCollectionValidatorProvider;
    @Inject
    @Named(PropertiesGinModule.RADIO_GROUP)
    protected Property<String> commentOnFormProperty;
    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    protected SelectListProperty<String, SelectItem> commentAttributeGroup;
    private PropertyRegistration<SelectItem> commentAttributeGroupPR;

    protected Property<SelectItem> settingsSet;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> showAttributeDescription;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> useStandardAttributesSet;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> useAsDefaultProperty;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> immediateObjectSavingEnabled;
    protected PropertyRegistration<Boolean> immediateObjectSavingEnabledPR;
    @Inject
    private Provider<NotNullValidator<SelectItem>> notNullSelectItemValidatorProvider;
    @Inject
    private Provider<NotEmptyValidator> notEmptyValidator;
    @Inject
    private Provider<MetainfoShortKeyCodeValidator> codeValidator;
    @Inject
    private MetaClassTreeFactory treeFactory;
    @Inject
    private FastSelectionMetaClassTreeFactory fastSelectionTreeFactory;
    @Inject
    protected SettingsSetOnFormCreator settingsSetOnFormCreator;

    protected CustomForm customForm;
    protected Property<Collection<DtObject>> transitionCasesProperty;
    protected MetaClass metaClassUnderEdit;
    protected MetaClass context;
    protected Set<MetaClassLite> casesForSetupCustomForm = new HashSet<>();
    protected Set<ClassFqn> nonSelectableCasesForCustomForm = new HashSet<>();
    private AsyncCallback<CustomForm> saveCallback;
    private Collection<MetaClassLite> initialFqns;
    private String parentClassTitle = "";
    protected final Map<ClassFqn, MetaClassAttributeGroups> attributeGroupsCache = new HashMap<>();
    protected boolean isEditForm = false;
    protected List<CustomFormType> formTypeOrder = new ArrayList<>();

    protected AbstractCustomFormFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(CustomForm customForm, AsyncCallback<CustomForm> saveCallback)
    {
        this.customForm = customForm;
        this.saveCallback = saveCallback;
        if (customForm == null)
        {
            this.initialFqns = new ArrayList<>();
        }
        else
        {
            this.initialFqns = customForm.getTargetCases().getTargets();
        }
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        processSave();
    }

    /**
     * @param metaClassUnderEdit настраиваемый метакласс
     */
    public void setMetaClass(MetaClass metaClassUnderEdit)
    {
        this.metaClassUnderEdit = metaClassUnderEdit;
    }

    protected Property<Collection<DtObject>> createTreeProperty(boolean withFastSelection)
    {
        DtoPopupMultiValueCellTree<? extends HierarchicalMetaClassMultiSelectionModel<DtObject>> valueCellTree =
                createTree(
                        withFastSelection);
        valueCellTree.setFormatterForCellOnly(new Function<Collection<DtObject>, String>()
        {
            @Override
            public String apply(Collection<DtObject> obj)
            {
                int number = valueCellTree.getTreeModel().getSelectionModel().getSelectedSet().size();
                if (number > 0)
                {
                    return messages.selectedCasesCount(number, parentClassTitle);
                }
                else
                {
                    return "";
                }
            }
        });
        valueCellTree.setSelectedItemsLimit(VISIBLE_SELECTED_ITEMS_LIMIT);

        //@formatter:off
        PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                ? extends HierarchicalMetaClassMultiSelectionModel<DtObject>>> treeProperty = new PropertyBase<>(
                        getTransitionCasesPropertyCaption(), valueCellTree);
        //@formatter:on

        treeProperty.setValidationMarker(true);
        validation.validate(treeProperty, notEmptyCollectionValidatorProvider.get());

        return treeProperty;
    }

    protected Collection<DtObject> getAvailableAttributeGroups(List<MetaClassAttributeGroups> metaClassesGroups)
    {
        Collection<DtObject> groups = null;
        if (metaClassesGroups.isEmpty())
        {
            groups = new HashSet<>();
        }
        else
        {
            groups = metaClassesGroups.get(0).getGroups();
            for (MetaClassAttributeGroups metaClassGroups : metaClassesGroups.subList(1, metaClassesGroups.size()))
            {
                groups = CollectionUtils.intersect(groups, metaClassGroups.getGroups(), new Comparator<DtObject>()
                {
                    @Override
                    public int compare(DtObject o1, DtObject o2)
                    {
                        if (!o1.getMetaClass().equals(o2.getMetaClass()))
                        {
                            return -1;
                        }

                        return o1.getUUID().compareTo(o2.getUUID());
                    }
                });
            }
        }

        List<DtObject> result = Lists.newArrayList(groups);
        result.sort(ITitled.IGNORE_CASE_COMPARATOR);
        return result;
    }

    protected CustomFormType getFormType(String typeId)
    {
        if (null == typeId)
        {
            return null;
        }
        return CustomFormType.valueOf(typeId);
    }

    protected abstract void initPropertiesValues(CustomForm customForm, AsyncCallback<Void> callback);

    @Override
    protected void onBind()
    {
        super.onBind();
        final ReadyState readyState = new ReadyState(this);
        metainfoService.getMetaClass(this.metaClassUnderEdit.getFqn().fqnOfClass(),
                new BasicCallback<MetaClass>(readyState)
                {
                    @Override
                    protected void handleSuccess(MetaClass metaClass)
                    {
                        parentClassTitle = metaClass.getTitle();
                    }
                });

        metainfoService.getPermittedTypesForSetupCustomForm(metaClassUnderEdit.getFqn().fqnOfClass(),
                Form.CHANGE_CASE_FORM, CustomFormType.ChangeCaseForm.name(),
                new BasicCallback<GetPermittedTypesForSetupCustomFormResponse>(readyState)
                {
                    @Override
                    protected void handleSuccess(GetPermittedTypesForSetupCustomFormResponse permittedTypesResponse)
                    {
                        setUpPermittedTypes(permittedTypesResponse);
                    }
                });

        readyState.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                bindPropertiesAndShow();
            }
        });
    }

    protected void onFormTypeChange(CustomFormType formType, boolean onEditForm, boolean initialOpen)
    {
        String formTypeCode = getCustomFormTypeCode(formType);

        metainfoService.getPermittedTypesForSetupCustomForm(metaClassUnderEdit.getFqn().fqnOfClass(), formTypeCode,
                formType.name(), new BasicCallback<GetPermittedTypesForSetupCustomFormResponse>()
                {
                    @Override
                    protected void handleSuccess(GetPermittedTypesForSetupCustomFormResponse permittedTypesResponse)
                    {
                        setUpPermittedTypes(permittedTypesResponse);
                        if (initialOpen && checkSelectNextFormType())
                        {
                            return;
                        }
                        validation.unvalidate(transitionCasesProperty);
                        Collection<DtObject> tempValue = transitionCasesProperty.getValue();
                        transitionCasesProperty = createTreeProperty(
                                CustomFormType.QuickForm == formType || CustomFormType.MassEditForm == formType);
                        DebugIdBuilder.ensureDebugId(transitionCasesProperty, "transitionCasesProperty");
                        if (onEditForm)
                        {
                            transitionCasesProperty.setValue(tempValue);
                        }
                        else
                        {
                            setTransitionCases();
                        }
                        registerTreePropertyChangeHandler();
                        refreshProperties();
                    }

                    private void refreshProperties()
                    {
                        PropertyDialogDisplay display = getDisplay();
                        display.clearProperties();
                        display.add(formTypeProperty);
                        validation.unvalidate(titleProperty);
                        if (CustomFormType.QuickForm == formType || CustomFormType.MassEditForm == formType)
                        {
                            display.add(titleProperty);
                            if (CustomFormType.QuickForm == formType)
                            {
                                display.add(codeProperty);
                            }
                            titleProperty.setValidationMarker(true);
                            validation.validate(titleProperty, notEmptyValidator.get());
                        }
                        else
                        {
                            titleProperty.setValidationMarker(false);
                            validation.unvalidate(titleProperty);
                            titleProperty.initValidation();
                        }
                        if (CustomFormType.QuickForm == formType)
                        {
                            codeProperty.setValidationMarker(!onEditForm);
                            if (!onEditForm)
                            {
                                validation.validate(codeProperty, notEmptyValidator.get());
                                validation.validate(codeProperty, codeValidator.get());
                            }
                        }
                        else
                        {
                            codeProperty.setValidationMarker(false);
                            validation.unvalidate(codeProperty);
                            codeProperty.initValidation();
                        }
                        display.add(transitionCasesProperty);

                        if (CustomFormType.ChangeCaseForm == formType
                            || CustomFormType.ChangeResponsibleForm == formType)
                        {
                            getDisplay().add(useStandardAttributesSet);
                        }
                        else
                        {
                            useStandardAttributesSet.setValue(false, false);
                        }

                        updateAttributeGroupValidation(useStandardAttributesSet.getValue());
                        if (!Boolean.TRUE.equals(useStandardAttributesSet.getValue()))
                        {
                            attributeGroupsPropertyPR = display.add(attributeGroup);
                        }
                        else
                        {
                            attributeGroupsPropertyPR = null;
                        }

                        if (CustomFormType.QuickForm != formType)
                        {
                            display.add(commentOnFormProperty);
                            commentAttributeGroupPR =
                                    Constants.CustomUserForm.NOT_FILL.equals(commentOnFormProperty.getValue())
                                            ? null
                                            : display.add(commentAttributeGroup);
                        }
                        else
                        {
                            commentAttributeGroupPR = null;
                        }
                        display.add(showAttributeDescription);

                        if (CustomFormType.MassEditForm == formType)
                        {
                            display.add(useAsDefaultProperty);
                        }
                        updateImmediateObjectSavingProperty(formType);
                        if (settingsSet != null)
                        {
                            display.add(settingsSet);
                        }
                    }
                });
    }

    /**
     * Выполняется при изменении списка метаклассов
     */
    protected void onMetaClassesChanged(ValueChangeEvent<Collection<DtObject>> event)
    {
        List<MetaClassAttributeGroups> classesGroups = CollectionUtils.transform(event.getValue(),
                input -> attributeGroupsCache.get(input.getMetainfo()), new ArrayList<>());

        Collection<DtObject> groups = getAvailableAttributeGroups(classesGroups);
        attributeGroup.<SingleSelectCellList<?>> getValueWidget().clear();
        if (groups.isEmpty())
        {
            attributeGroup.trySetObjValue(null);
            return;
        }

        updateAttributeGroups(groups);

        SelectItem value = attributeGroup.getValue();
        if (value != null)
        {
            attributeGroup.trySetObjValue(value.getUUID());
        }
    }

    protected void updateAttributeGroups(Collection<DtObject> groups)
    {
        for (DtObject grp : groups)
        {
            attributeGroup.<SingleSelectCellList<?>> getValueWidget().addItem(grp.getTitle(), grp.getUUID());
        }
    }

    protected CustomForm prepareCustomForm()
    {
        CustomFormType formType = getFormType(SelectListPropertyValueExtractor.getValue(formTypeProperty));
        CustomForm form = null;
        switch (formType)
        {
            case QuickForm:
                form = customForm instanceof QuickForm ? (CustomForm)customForm.clone() : new QuickForm();
                break;
            case MassEditForm:
                form = customForm instanceof MassEditForm ? (MassEditForm)customForm.clone() : new MassEditForm();
                break;
            default:
                form = customForm instanceof ChangeCaseForm ? (CustomForm)customForm.clone() : new ChangeCaseForm();
                break;
        }
        form.setFormType(formType);
        return form;
    }

    protected abstract void setTransitionCases();

    private void bindPropertiesAndShow()
    {
        formTypeProperty.setCaption(messages.typeOfForm());
        formTypeProperty.setValidationMarker(true);
        getDisplay().add(formTypeProperty);

        SingleSelectCellList<String> selList = formTypeProperty.getValueWidget();
        String changeCaseFormName = CustomFormType.ChangeCaseForm.name();
        String changeResponsibleFormName = CustomFormType.ChangeResponsibleForm.name();
        String quickAddAndEditFormName = CustomFormType.QuickForm.name();
        String massEditFormName = CustomFormType.MassEditForm.name();

        selList.addItem(messages.changeCaseForm(), changeCaseFormName);
        formTypeOrder.add(CustomFormType.ChangeCaseForm);
        if (changeResponsibleFormAvaliable())
        {
            selList.addItem(messages.changeResponsibleForm(), changeResponsibleFormName);
            formTypeOrder.add(CustomFormType.ChangeResponsibleForm);
        }
        selList.addItem(messages.quickAddAndEditForm(), quickAddAndEditFormName);
        selList.addItem(messages.massEditForm(), massEditFormName);
        formTypeOrder.add(CustomFormType.QuickForm);
        formTypeOrder.add(CustomFormType.MassEditForm);
        formTypeProperty.trySetObjValue(changeCaseFormName);

        validation.validate(formTypeProperty, notNullSelectItemValidatorProvider.get());

        selList.addValueChangeHandler(event ->
        {
            transitionCasesProperty.setCaption(getTransitionCasesPropertyCaption());
            onFormTypeChange(getFormType(SelectItemValueExtractor.extract(event.getValue())), false, false);
        });

        DebugIdBuilder.ensureDebugId(formTypeProperty, "customFormType");

        titleProperty.setCaption(cmessages.title());
        titleProperty.setMaxLength(TITLE_MAX_LENGTH);
        DebugIdBuilder.ensureDebugId(titleProperty, "title");

        codeProperty.setCaption(cmessages.code());
        codeProperty.setMaxLength(ru.naumen.metainfo.shared.Constants.MAX_CODE_LENGTH);
        DebugIdBuilder.ensureDebugId(codeProperty, "code");

        transitionCasesProperty = createTreeProperty(false);
        DebugIdBuilder.ensureDebugId(transitionCasesProperty, "transitionCasesProperty");

        getDisplay().add(transitionCasesProperty);
        registerTreePropertyChangeHandler();

        useStandardAttributesSet.setCaption(messages.useStandardAttributesSet());
        DebugIdBuilder.ensureDebugId(useStandardAttributesSet, "useStandardAttributesSetProperty");
        getDisplay().add(useStandardAttributesSet);
        useStandardAttributesSet.addValueChangeHandler((event) ->
        {
            if (attributeGroupsPropertyPR != null)
            {
                attributeGroupsPropertyPR.unregister();
                attributeGroupsPropertyPR = null;
            }

            updateAttributeGroupValidation(event.getValue());
            if (!event.getValue())
            {
                attributeGroupsPropertyPR = getDisplay().addProperty(attributeGroup, 3);
            }
        });

        attributeGroup.setCaption(messages.attributeGroup());
        attributeGroup.setValidationMarker(true);
        attributeGroupsPropertyPR = getDisplay().add(attributeGroup);
        validation.validate(attributeGroup, notNullSelectItemValidatorProvider.get());
        DebugIdBuilder.ensureDebugId(attributeGroup, "selectAttributeGroup");

        commentOnFormProperty.setCaption(messages.commentOnForm());
        commentOnFormProperty.setValidationMarker(true);
        DebugIdBuilder.ensureDebugId(commentOnFormProperty, "commentOnFormProperty");

        commentOnFormProperty.addValueChangeHandler(event ->
        {
            if (Constants.CustomUserForm.NOT_FILL.equals(event.getValue()))
            {
                if (commentAttributeGroupPR != null)
                {
                    commentAttributeGroupPR.unregister();
                    commentAttributeGroupPR = null;
                }
            }
            else
            {
                if (commentAttributeGroupPR == null)
                {
                    commentAttributeGroupPR = getDisplay().addProperty(commentAttributeGroup, 5);
                }
            }
        });

        IRadioButtonGroup commentOnFormGroup = commentOnFormProperty.getValueWidget();

        commentOnFormGroup.addItem(Constants.CustomUserForm.NOT_FILL, messages.notFill());
        commentOnFormGroup.addItem(Constants.CustomUserForm.FILL, messages.fill());
        commentOnFormGroup.addItem(Constants.CustomUserForm.MUST_FILL, messages.mustFill());

        getDisplay().add(commentOnFormProperty);
        DebugIdBuilder.ensureDebugId(commentOnFormProperty, "commentOnFormProperty");

        commentAttributeGroup.setCaption(messages.commentOnFormAttributeGroup());
        commentAttributeGroup.setValidationMarker(true);

        metainfoService.getMetaClass(Comment.FQN, new BasicCallback<MetaClass>()
        {
            @Override
            public void onSuccess(MetaClass commentMetaClass)
            {
                SingleSelectCellList<String> commentAttributeGroupWidget = commentAttributeGroup.getValueWidget();
                AttrWidgetsHelper.fillCommentAttributeGroupsToWidget(commentMetaClass, commentAttributeGroupWidget);
            }
        });

        DebugIdBuilder.ensureDebugId(commentAttributeGroup, "commentAttributeGroup");

        showAttributeDescription.setCaption(messages.attributeDescription());
        DebugIdBuilder.ensureDebugId(showAttributeDescription, "attributeDescriptionProperty");
        display.add(showAttributeDescription);

        useAsDefaultProperty.setCaption(messages.useAsDefault());
        DebugIdBuilder.ensureDebugId(useAsDefaultProperty, "useAsDefaultProperty");
        useAsDefaultProperty.addValueChangeHandler(new ValueChangeHandler<Boolean>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<Boolean> event)
            {
                if (event.getValue())
                {
                    dispatch.execute(new GetDefaultMassEditFormAction(metaClassUnderEdit.getFqn().fqnOfClass()),
                            new BasicCallback<SimpleResult<MassEditForm>>()
                            {
                                @Override
                                protected void handleSuccess(SimpleResult<MassEditForm> value)
                                {
                                    String message = value.get() == null
                                            ? messages.defaultMassEditFormDoesNotExist(parentClassTitle)
                                            : messages.defaultMassEditFormExist(parentClassTitle);
                                    getDisplay().addAttentionMessage(message);
                                }
                            });
                }
                else
                {
                    getDisplay().clearAttentionMessage();
                }
            }
        });
        bindSettingsSetProperty();
        initPropertiesValues(customForm, new BasicCallback<Void>(getDisplay())
        {
            @Override
            protected void handleSuccess(Void value)
            {
                //По умолчанию форма открывается с типом "Форма смены типа". Если таковая настроена
                //для всех типов - поменяем на "Форма смены ответственного" (только там, где они доступны)
                checkSelectNextFormType();
                getDisplay().setFixed(false);
                getDisplay().display();
            }
        });

        immediateObjectSavingEnabled.setCaption(messages.immediateObjectSaving());
        DebugIdBuilder.ensureDebugId(immediateObjectSavingEnabled, "immediateObjectSavingEnabled");
    }

    protected void bindSettingsSetProperty()
    {
        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getDisplay(), null);
    }

    private boolean changeResponsibleFormAvaliable()
    {
        return metaClassUnderEdit.isHasResponsible();
    }

    protected boolean checkSelectNextFormType()
    {
        if (casesForSetupCustomForm.size() == 1)
        {
            int index = formTypeOrder.indexOf(customForm.getFormType()) + 1;
            if (index >= formTypeOrder.size())
            {
                return false;
            }
            CustomFormType formType = formTypeOrder.get(index);
            formTypeProperty.trySetObjValue(formType.name());
            customForm.setFormType(formType);
            onFormTypeChange(customForm.getFormType(), isEditForm, true);
            return true;
        }
        return false;
    }

    private DtoPopupMultiValueCellTree<? extends HierarchicalMetaClassMultiSelectionModel<DtObject>> createTree(
            boolean withFastSelection)
    {
        DtoPopupMultiValueCellTree<? extends HierarchicalMetaClassMultiSelectionModel<DtObject>> valueCellTree = null;
        if (withFastSelection)
        {
            valueCellTree = fastSelectionTreeFactory.createTree(casesForSetupCustomForm,
                    nonSelectableCasesForCustomForm);
        }
        else
        {
            valueCellTree = treeFactory.createTree(metaClassUnderEdit.getFqn(), casesForSetupCustomForm,
                    nonSelectableCasesForCustomForm);
        }
        return valueCellTree;
    }

    private String getCustomFormTypeCode(CustomFormType formType)
    {
        switch (formType)
        {
            case ChangeCaseForm:
                return Form.CHANGE_CASE_FORM;
            case ChangeResponsibleForm:
                return Form.CHANGE_RESPONSIBLE_FORM;
            case QuickForm:
                return Form.QUICK_ADD_AND_EDIT_FORM;
            case MassEditForm:
                return Form.MASS_EDIT;
            default:
                return Form.CHANGE_CASE_FORM;
        }
    }

    private String getTransitionCasesPropertyCaption()
    {
        if (CustomFormType.ChangeCaseForm.name().equals(SelectListPropertyValueExtractor.getValue(formTypeProperty)))
        {
            return messages.forTransitionsInTypes();
        }
        else
        {
            return messages.forTypes();
        }
    }

    private void processSave()
    {
        CustomForm form = prepareCustomForm();
        if (attributeGroup.getValue() != null)
        {
            form.setAttrGroup(attributeGroup.getValue().getUUID());
        }
        else
        {
            form.setAttrGroup(null);
        }

        saveFormSequence(form);
    }

    @SuppressWarnings("unchecked")
    private void registerTreePropertyChangeHandler()
    {
        registerHandler(
                ((PopupValueCellTree<DtObject, Collection<DtObject>, MultiSelectionModel<DtObject>>)transitionCasesProperty
                        .getValueWidget()).addValueChangeHandler(this::onMetaClassesChanged));
    }

    private void saveFormSequence(Content formClone)
    {
        CustomFormBase customUserForm = (CustomFormBase)formClone;
        CustomFormType formType = customUserForm.getFormType();
        String formTypeCode = getCustomFormTypeCode(formType);
        formClone.setSettingsSet(SelectListPropertyValueExtractor.getValue(settingsSet));

        if (formClone instanceof ChangeCaseForm)
        {
            customUserForm.setUuid(formTypeCode);
            ((ChangeCaseForm)formClone).setCommentOnFormProperty(commentOnFormProperty.getValue());
            ((ChangeCaseForm)formClone).setCommentOnFormAttrGroupCode(commentAttributeGroup.getValue().getCode());
            ((ChangeCaseForm)formClone).setShowAttrDescription(showAttributeDescription.getValue());
        }

        if (formClone instanceof QuickForm)
        {
            ((QuickForm)customUserForm).setCode(codeProperty.getValue());
            metainfoUtils.setLocalizedValue(((QuickForm)formClone).getTitle(), titleProperty.getValue());
            ((QuickForm)formClone).setShowAttrDescription(showAttributeDescription.getValue());
            ((QuickForm)formClone).setImmediateObjectSavingEnabled(
                    Boolean.TRUE.equals(immediateObjectSavingEnabled.getValue()));
        }

        if (formClone instanceof MassEditForm)
        {
            metainfoUtils.setLocalizedValue(((MassEditForm)formClone).getTitle(), titleProperty.getValue());
            ((MassEditForm)formClone).setCommentOnFormProperty(commentOnFormProperty.getValue());
            ((MassEditForm)formClone).setCommentOnFormAttrGroupCode(commentAttributeGroup.getValue().getCode());
            ((MassEditForm)formClone).setShowAttrDescription(showAttributeDescription.getValue());
            ((MassEditForm)formClone).setUseAsDefault(useAsDefaultProperty.getValue());
        }

        customUserForm.getTransitionClasses().clear();
        customUserForm.getTransitionClasses()
                .addAll(Collections2.transform(transitionCasesProperty.getValue(), DtObject.FQN_EXTRACTOR));

        final ReadyState readyState = new ReadyState(this);
        getDisplay().startProcessing();
        // Удаляем пользовательские формы для тех типов, выделение с которых было снято
        Map<String, Collection<ClassFqn>> fqns = new HashMap<>();
        fqns.put(customUserForm.getUuid(),
                CollectionUtils.subtract(Collections2.transform(initialFqns, MetaClassLite.FQN_EXTRACTOR),
                        customUserForm.getTransitionClasses()));

        if (formClone instanceof QuickForm || formClone instanceof MassEditForm)
        {
            ClassFqn fqnOfClass = customUserForm.getTransitionClasses().isEmpty() ? null
                    : customUserForm.getTransitionClasses().iterator().next().fqnOfClass();
            if (null != fqnOfClass)
            {
                metainfoModificationService.saveUIs(Lists.newArrayList(fqnOfClass), formClone, isEditForm, null,
                        customUserForm.getUuid(), new BasicCallback<Void>(readyState)
                        {
                            @Override
                            public void handleFailure(Throwable arg)
                            {
                                saveCallback.onFailure(arg);
                            }
                        });
            }
        }
        else
        {
            metainfoModificationService.resetUIs(fqns, false, new BasicCallback<Void>(readyState)
            {
                @Override
                public void handleFailure(Throwable arg)
                {
                    saveCallback.onFailure(arg);
                }

                @Override
                protected void handleSuccess(Void value)
                {
                    metainfoModificationService.saveUIs(customUserForm.getTransitionClasses(), formClone, isEditForm,
                            null, customUserForm.getUuid(), new BasicCallback<Void>(readyState)
                            {
                                @Override
                                public void handleFailure(Throwable arg)
                                {
                                    saveCallback.onFailure(arg);
                                }
                            });
                }
            });
        }

        readyState.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                saveCallback.onSuccess(customForm);
                getDisplay().stopProcessing();
                unbind();
            }
        });
    }

    private void setUpPermittedTypes(GetPermittedTypesForSetupCustomFormResponse permittedTypesResponse)
    {
        casesForSetupCustomForm = Sets.newHashSet(permittedTypesResponse.getPermittedTypes());
        casesForSetupCustomForm.addAll(initialFqns);
        nonSelectableCasesForCustomForm = Sets.newHashSet(permittedTypesResponse.getNonSelectableCases());
        nonSelectableCasesForCustomForm.removeAll(Collections2.transform(initialFqns, MetaClassLite.FQN_EXTRACTOR));

        // Подготовим локальный кэш групп атрибутов метаклассов для быстрого получения
        // списка допустимых групп атрибутов в обработчике onMetaClassesChanged
        for (Map.Entry<ClassFqn, ? extends List<AttributeGroupInfo>> entry : permittedTypesResponse.getGroupInfos()
                .entrySet())
        {
            attributeGroupsCache.put(entry.getKey(), new MetaClassAttributeGroups(entry.getValue()));
        }
    }

    private void updateAttributeGroupValidation(Boolean useDefaultAttributeSet)
    {
        if (Boolean.TRUE.equals(useDefaultAttributeSet))
        {
            attributeGroup.setValue(null);
            attributeGroup.setValidationMarker(false);
            validation.unvalidate(attributeGroup);
            attributeGroup.initValidation();
        }
        else
        {
            attributeGroup.setValidationMarker(true);
            validation.validate(attributeGroup, notNullSelectItemValidatorProvider.get());
        }
    }

    private void updateImmediateObjectSavingProperty(CustomFormType formType)
    {
        if (formType == CustomFormType.QuickForm && null == immediateObjectSavingEnabledPR)
        {
            immediateObjectSavingEnabledPR = getDisplay().add(immediateObjectSavingEnabled);
        }
        else if (formType != CustomFormType.QuickForm && null != immediateObjectSavingEnabledPR)
        {
            immediateObjectSavingEnabledPR.unregister();
            immediateObjectSavingEnabledPR = null;
        }
    }
}
