package ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms;

import jakarta.inject.Inject;

import com.google.inject.Singleton;

import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.validation.HasValidation;
import ru.naumen.core.client.validation.ValidateEvent;
import ru.naumen.core.client.validation.ValidationMessages;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.MetainfoUtils;

/**
 * <AUTHOR>
 * @since 06 сент. 2016 г.
 *
 */
@Singleton
public class ThemeCodeValidator implements Validator<String>
{
    @Inject
    private ValidationMessages vmessages;
    @Inject
    private SecurityHelper security;

    @Override
    public boolean validate(HasValueOrThrow<String> hasValue)
    {
        String value = hasValue.getValue();
        String specialSymbols = security.hasVendorProfile() ? Constants.PREFIXED_CODE_SPECIAL_CHARS_FOR_VENDOR : "";
        if (!MetainfoUtils.isValidMetainfoKeyCode(value, specialSymbols) && hasValue instanceof HasValidation)
        {
            ((HasValidation)hasValue).addValidationMessage(
                    vmessages.codeValidationError(ru.naumen.metainfo.shared.Constants.MAX_METAINFO_KEY_LENGTH));
            return false;
        }
        return true;
    }

    @Override
    public void validateAsync(HasValueOrThrow<String> hasValue, ValidateEvent event)
    {
        validate(hasValue);
    }
}