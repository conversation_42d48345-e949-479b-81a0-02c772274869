package ru.naumen.metainfoadmin.client.adminprofile.command;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.events.AdminProfileUpdatedEvent;
import ru.naumen.metainfoadmin.client.adminprofile.data.AdminProfilesServiceAsync;
import ru.naumen.metainfoadmin.client.adminprofile.form.EditAdminProfileFormPresenter;

/**
 * Команда редактирования профиля администрирования
 * <AUTHOR>
 * @since 17.01.2024
 */
public class EditAdminProfileCommand extends PresenterCommandImpl<DtObject, DtObject, DtObject>
{
    @Inject
    private Provider<EditAdminProfileFormPresenter> editAdminProfileFormProvider;
    @Inject
    private AdminProfilesServiceAsync adminProfilesService;
    @Inject
    private EventBus eventBus;

    @Inject
    public EditAdminProfileCommand(@Assisted CommandParam<DtObject, DtObject> param)
    {
        super(param);
    }

    @Override
    public void onExecute(final DtObject result, CallbackDecorator<DtObject, DtObject> callback)
    {
        adminProfilesService.editAdminProfile(result.getUUID(), result, new BasicCallback<DtObject>()
        {
            @Override
            public void handleSuccess(DtObject dto)
            {
                callback.onSuccess(dto);
                eventBus.fireEvent(new AdminProfileUpdatedEvent(dto));
            }

            @Override
            protected void handleFailure(Throwable t)
            {
                callback.onFailure(t);
            }
        });
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected CallbackPresenter<DtObject, DtObject> getPresenter(DtObject value)
    {
        EditAdminProfileFormPresenter formPresenter = editAdminProfileFormProvider.get();
        if (param.getCallbackSafe() instanceof BasicCallback<?>)
        {
            ((BasicCallback<DtObject>)param.getCallbackSafe()).setErrorMessageHandler(formPresenter.getDisplay());
        }
        return formPresenter;
    }
}
