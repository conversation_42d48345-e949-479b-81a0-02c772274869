package ru.naumen.metainfoadmin.client.fastlink.settings.forms;

import java.util.Collection;
import java.util.Map;

import java.util.HashMap;

import com.google.inject.Inject;
import com.google.inject.name.Named;

import ru.naumen.core.client.tree.metainfo.MetaClassMultiSelectionModelSameClassOnly;
import ru.naumen.core.client.tree.selection.HierarchicalMetaClassMultiSelectionModel;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyObjectValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindTextBoxFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSetting;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsGinModule;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsGinModule.FastLinkSettingFormPropertyCode;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetBindDelegate;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetRefreshDelegate;

/**
 * <AUTHOR>
 * @since 01.03.18
 */
public class FastLinkSettingFormPropertyControllerFactoryImpl
        extends PropertyControllerFactorySyncImpl<FastLinkSetting, ObjectForm>
{
    @Inject
    private PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<Collection<SelectItem>, MultiSelectBoxProperty> multiListBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxWithEmptyPropertyFactory;
    //@formatter:off
    @Inject
    private PropertyControllerSyncFactoryInj<Collection<DtObject>, 
                         PropertyBase<Collection<DtObject>, 
                                      PopupValueCellTree<DtObject,  
                                             Collection<DtObject>, 
                                             MetaClassMultiSelectionModelSameClassOnly>>> sameMetaClassTreePropertyFactory;
    
    @Named(value = FastLinkSettingFormPropertyCode.CONTEXT_TYPES)
    @Inject
    private PropertyControllerSyncFactoryInj<Collection<DtObject>, 
                         PropertyBase<Collection<DtObject>, 
                                      PopupValueCellTree<DtObject, 
                                             Collection<DtObject>, 
                                             HierarchicalMetaClassMultiSelectionModel<DtObject>>>> metaClassTreePropertyFactory;
    //@formatter:on    

    @Inject
    private CodeRefreshDelegateImpl codeRefreshDelegate;
    @Inject
    private TitleVCHDelegateImpl titleVCHDelegate;
    @Inject
    private MentionTypesVCHDelegateImpl mentionTypesVCHDelegateImpl;
    @Inject
    private ProfileRefreshDelegateImpl profileRefreshDelegateImpl;
    @Inject
    private PropertyDelegateBindTextBoxFactory textBoxBindDelegateFactory;
    @Inject
    private MentionAttributeRefreshDelegateImpl mentionAttributeRefreshDelegateImpl;
    @Inject
    private FastLinkSettingCodeValidator metainfoKeyCodeValidator;
    @Inject
    private AttributeGroupRefreshDelegateImpl attributeGroupRefreshDelegateImpl;
    @Inject
    private SettingsSetBindDelegate settingsSetBindDelegate;
    @Inject
    private SettingsSetRefreshDelegate settingsSetRefreshDelegate;
    private final Map<Validator<String>, String> titleValidators = new HashMap<>();
    private final Map<Validator<String>, String> codeValidators = new HashMap<>();
    private final Map<Validator<String>, String> aliasValidators = new HashMap<>();
    private final Map<Validator<Collection<DtObject>>, String> objectsValidators = new HashMap<>();
    private final Map<Validator<SelectItem>, String> objectValidators = new HashMap<>();

    @Inject
    public void setUpValidators(NotEmptyCollectionValidator<Collection<DtObject>> notEmptyCollectionValidator,
            NotEmptyValidator notEmptyValidator, NotEmptyObjectValidator<SelectItem> notEmptyObjectValidator)
    {
        titleValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        codeValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        codeValidators.put(metainfoKeyCodeValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        aliasValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        objectsValidators.put(notEmptyCollectionValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        objectValidators.put(notEmptyObjectValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }

    @Override
    protected void build()
    {
        PropertyDelegateBind<String, TextBoxProperty> titleBindDelegate = textBoxBindDelegateFactory
                .create(FastLinkSettingsGinModule.MAX_TITLE_LENGTH);
        PropertyDelegateBind<String, TextBoxProperty> codeBindDelegate = textBoxBindDelegateFactory
                .create(FastLinkSettingsGinModule.MAX_CODE_LENGTH);
        PropertyDelegateBind<String, TextBoxProperty> aliasBindDelegate = textBoxBindDelegateFactory
                .create(FastLinkSettingsGinModule.MAX_ALIAS_LENGTH);
        //@formatter:off
        register(FastLinkSettingFormPropertyCode.TITLE, textBoxPropertyFactory)
            .setBindDelegate(titleBindDelegate)
            .setValidators(titleValidators)
            .setVchDelegate(titleVCHDelegate);
        register(FastLinkSettingFormPropertyCode.CODE, textBoxPropertyFactory)
            .setBindDelegate(codeBindDelegate)
            .setRefreshDelegate(codeRefreshDelegate)
            .setValidators(codeValidators);        
        register(FastLinkSettingFormPropertyCode.MENTION_TYPES, sameMetaClassTreePropertyFactory)
            .setVchDelegate(mentionTypesVCHDelegateImpl)
            .setValidators(objectsValidators);
        register(FastLinkSettingFormPropertyCode.ALIAS, textBoxPropertyFactory)
            .setBindDelegate(aliasBindDelegate)
            .setValidators(aliasValidators);
        register(FastLinkSettingFormPropertyCode.CONTEXT_TYPES, metaClassTreePropertyFactory);       
        register(FastLinkSettingFormPropertyCode.MENTION_ATTRIBUTE, listBoxPropertyFactory)
            .setRefreshDelegate(mentionAttributeRefreshDelegateImpl)
            .setValidators(objectValidators);
        register(FastLinkSettingFormPropertyCode.ATTRIBUTE_GROUP, listBoxPropertyFactory)
            .setRefreshDelegate(attributeGroupRefreshDelegateImpl);
        register(FastLinkSettingFormPropertyCode.PROFILES, multiListBoxPropertyFactory)
            .setRefreshDelegate(profileRefreshDelegateImpl);
        register(FastLinkSettingFormPropertyCode.SETTINGS_SET, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(settingsSetBindDelegate)
                .setRefreshDelegate(settingsSetRefreshDelegate);
        //@formatter:on
    }
}
