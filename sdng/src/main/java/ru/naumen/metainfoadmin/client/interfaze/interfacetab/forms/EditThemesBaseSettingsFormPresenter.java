package ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.FileUploadProperty;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataResponse;
import ru.naumen.core.shared.dispatch.ValidateThemeParamsAction;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.file.FileUtils;
import ru.naumen.core.shared.interfacesettings.dispatch.EditThemesBaseSettingsAction;
import ru.naumen.metainfoadmin.client.FileUploadCompleteHandler;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContextChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.ThemeMessages;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.ThemesBaseSettingsPresenter;

/**
 * Форма редактирование базовых настроек тем интерфейса
 *
 * <AUTHOR>
 * @since 11.07.22
 */
public class EditThemesBaseSettingsFormPresenter extends OkCancelPresenter<PropertyFormDisplay>
{
    private static final Set<String> validFileExtensions = ImmutableSet.of(FileUtils.PROPERTIES_FILE_EXTENTION);

    private final ThemeMessages messages;
    private final DispatchAsync dispatch;
    private final FileUploadProperty baseParamsFileUpload;
    private InterfaceSettingsContext context;

    @Inject
    public EditThemesBaseSettingsFormPresenter(PropertyFormDisplay display, EventBus eventBus,
            ThemeMessages messages, DispatchAsync dispatch, FileUploadProperty baseParamsFileUpload)
    {
        super(display, eventBus);
        this.messages = messages;
        this.dispatch = dispatch;
        this.baseParamsFileUpload = baseParamsFileUpload;
    }

    public void init(InterfaceSettingsContext context)
    {
        this.context = context;
    }

    @Override
    public void onApply()
    {
        dispatch.execute(new EditThemesBaseSettingsAction(getUploadFileUuid()),
                new BasicCallback<GetInterfaceTabDataResponse>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(GetInterfaceTabDataResponse response)
                    {
                        super.handleSuccess(response);
                        eventBus.fireEvent(new InterfaceSettingsContextChangedEvent(response));
                        unbind();
                    }
                });
    }

    protected void bindProperties()
    {
        baseParamsFileUpload.setCaption(messages.systemColors());
        baseParamsFileUpload.getValueWidget().setValidFileExtension(validFileExtensions);
        registerHandler(baseParamsFileUpload.getValueWidget()
                .addUploadCompleteHandler(new FileUploadCompleteHandler(getDisplay(), baseParamsFileUpload,
                        (uuid) -> new ValidateThemeParamsAction(uuid, true), dispatch)));
        DebugIdBuilder.ensureDebugId(baseParamsFileUpload, "propertiesFileUpload");
        getDisplay().add(baseParamsFileUpload);
    }

    @Override
    protected void onBind()
    {
        setCaption(messages.interfaceBaseSettingsEditForm());
        bindProperties();
        fillProperties();
        super.onBind();
    }

    private void fillProperties()
    {
        List<DtObject> files = null;

        String fileUuid = context.getSettings().getThemesBaseParamsFileUuid();
        if (fileUuid != null)
        {
            files = Lists.<DtObject> newArrayList(
                    new SimpleDtObject(fileUuid, ThemesBaseSettingsPresenter.ALL_THEMES_FILE_NAME, null));
        }

        baseParamsFileUpload.setValue(files);
    }

    private String getUploadFileUuid()
    {
        Collection<DtObject> files = baseParamsFileUpload.getValue();
        return CollectionUtils.isEmpty(files) ? null : files.iterator().next().getUUID();
    }
}
