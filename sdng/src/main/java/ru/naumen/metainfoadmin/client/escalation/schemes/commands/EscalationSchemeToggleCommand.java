package ru.naumen.metainfoadmin.client.escalation.schemes.commands;

import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.PresenterCommandEvent;
import ru.naumen.core.client.mvp.PresenterCommandEvent.PresenterCommandCode;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.core.shared.escalation.EscalationSchemeClient;
import ru.naumen.metainfo.shared.dispatch2.SaveEscalationSchemeAction;

/**
 * Команда включения-выключения
 *
 * <AUTHOR>
 * @since 21.12.2018
 *
 */
public class EscalationSchemeToggleCommand extends BaseCommandImpl<DtoContainer<EscalationScheme>, Void>
{
    @Inject
    private DispatchAsync dispatch;

    @Inject
    public EscalationSchemeToggleCommand(@Assisted EscalationSchemesCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<DtoContainer<EscalationScheme>, Void> param)
    {
        final EscalationSchemeClient result = toggleState(param.getValue().get());
        EscalationSchemeCommandContext context = ((EscalationSchemesCommandParam)param).getContext();
        dispatch.execute(new SaveEscalationSchemeAction(result, false),
                new BasicCallback<SimpleResult<DtoContainer<EscalationScheme>>>(context.getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtoContainer<EscalationScheme>> response)
                    {
                        param.getValue().get().setState(result.getState());
                        context.getLocalEventBus().fireEvent(new PresenterCommandEvent(PresenterCommandCode.REFRESH));
                    }
                });
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.SWITCH;
    }

    private EscalationSchemeClient toggleState(EscalationScheme object)
    {
        EscalationSchemeClient result = new EscalationSchemeClient(object);
        String state = result.getState();
        if (EscalationScheme.StateCode.ON.equals(state))
        {
            state = EscalationScheme.StateCode.OFF;
        }
        else
        {
            state = EscalationScheme.StateCode.ON;
        }
        result.setState(state);
        return result;
    }
}