package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import com.google.gwt.user.cellview.client.CellTable;

import ru.naumen.core.client.table.builder.WithDebugIdCellTableBuilder;
import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay;

/**
 * Дисплей таблицы, в котором таблица оборачивается в
 * {@link ru.naumen.core.client.content.scroll.ScrollableContentPanel}
 * <AUTHOR>
 * @since 14.10.2021
 */
public class ScrollableTableDisplayImpl<T extends HasCode> extends TableWithArrowsDisplay<T>
{
    public ScrollableTableDisplayImpl()
    {
        super();
        wrapTableInScrollableContainer();
        CellTable<T> cellTable = (CellTable<T>)getTable();
        cellTable.setTableBuilder(new WithDebugIdCellTableBuilder<>(cellTable, HasCode.PROVIDES_KEY));
    }
}
