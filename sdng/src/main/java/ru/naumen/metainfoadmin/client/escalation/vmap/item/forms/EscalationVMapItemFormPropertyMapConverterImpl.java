/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap.VMapItemFormPropertyMapConverterImpl;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationVMapItemFormPropertyMapConverterImpl<F extends ObjectForm> extends
        VMapItemFormPropertyMapConverterImpl<EscalationValueMapItemFormContext, F>
{
    @Override
    public IProperties convert(IProperties propertyValues, EscalationValueMapItemFormContext context)
    {
        IProperties result = super.convert(propertyValues, context);
        result.setProperty(ValueMapCatalogItem.TYPE, ValueMapCatalogItem.ESCALATION_TYPE);
        result.setProperty(ValueMapCatalogItem.TARGET_ATTRS,
                Lists.newArrayList(ValueMapCatalogItem.ESCALATION_TARGET_DATA));
        //result.removeProperty(ValueMapCatalogItem.TARGET_ATTRS);
        return result;
    }
}