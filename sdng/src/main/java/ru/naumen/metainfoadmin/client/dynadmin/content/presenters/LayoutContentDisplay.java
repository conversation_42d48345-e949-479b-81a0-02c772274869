package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import java.util.ArrayList;

import com.google.gwt.user.client.ui.SimplePanel;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.content.LayoutContentDisplayBase;
import ru.naumen.core.client.layout.Band;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfoadmin.client.common.content.FlowPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.LayoutContentPresenter.ResizeColumnCommand;

/**
 * {@link Display} для контента {@link Layout}
 *
 * <AUTHOR>
 *
 */
public interface LayoutContentDisplay extends LayoutContentDisplayBase
{
    /**
     * Отображает новые контенты. Ранее присутствовавшие контенты будут удалены.
     *
     * @param bands логическая структура расположения контентов, формируемая {@link LayoutContentPresenter}'ом
     * @param leftColumnWidth ширина левой колонки
     * @param isRootLayout признак того, что layout находится на корневой панели вкладок
     */
    void drawContent(ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands, int leftColumnWidth,
            boolean isRootLayout);

    /**
     * Позволяет выключить или включить режим редактирования разметки
     */
    void enableLayoutMode(boolean enable);

    void setSplitterEnabled(boolean enabled);

    Widget getLayoutEditPanel();

    void setLayoutEditPanel(SimplePanel panel);

    /**
     * @param resizeColumnCommand команда для применения измененных настроек ширины
     */
    void initResizeColumnCommand(ResizeColumnCommand resizeColumnCommand);

    /**
     * Поместить разделитель между колонками
     * @param resizeContent признак необходимости подстраивать контенты под положение разделителя
     * @return статус перемещения (false - переместить не удалось так как колонок нет)
     */
    boolean moveSplitterBetweenColumns(boolean resizeContent);

    void setLayoutModeOffset();
}
