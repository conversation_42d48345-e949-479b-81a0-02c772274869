package ru.naumen.metainfoadmin.client.eventaction.form;

import static ru.naumen.metainfo.shared.ui.ListFilterOrElement.ConditionCode.CONTAINS;
import static ru.naumen.metainfo.shared.ui.ListFilterOrElement.PropertyCode.CONDITION_CODE;

import java.util.Arrays;
import java.util.HashSet;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.content.SafeContextualCallback;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dispatch.GetAdvListPropertiesAction;
import ru.naumen.core.shared.dispatch.GetAdvListPropertiesResult;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.SettingsManagementContext;
import ru.naumen.objectlist.client.extended.advlist.AdvlistSettingsHasChangedEvent;
import ru.naumen.objectlist.client.extended.advlist.tempsettings.AdvlistSessionSettingsHelper;
import ru.naumen.objectlist.client.mode.active.extended.advlist.AdvlistActionFactory;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.AddAdvlistSettingsToSelectToolEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.SetAdvlistSettingsEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.singlesettings.AdvlistPrsPresenter;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.singlesettings.AdvlistSettingsFormGinModule;
import ru.naumen.objectlist.shared.advlist.AdvlistSettings;
import ru.naumen.objectlist.shared.advlist.AdvlistSettingsClient;
import ru.naumen.objectlist.shared.advlist.AdvlistUtils;
import ru.naumen.objectlist.shared.advlist.SaveAdvlistSettingsAction;
import ru.naumen.objectlist.shared.advlist.SaveAdvlistSettingsResponse;

/**
 * Базовая реализация презентера для формы добавления/сохранения состояния advlist-а в админке
 * <AUTHOR>
 * @since 25.08.2017
 */
public class SaveEventActionsAdvlistPrsPresenter extends AdvlistPrsPresenter<ObjectFormAdd, ListComponents>
        implements CallbackPresenter<AdvlistSettingsClient, AdvlistSettingsClient>
{
    private class SaveAdvlistCallback extends SafeContextualCallback<SaveAdvlistSettingsResponse>
    {
        private final boolean newSettings;

        public SaveAdvlistCallback(boolean newSettings)
        {
            super(contextProvider.getSettingsContext(), SaveEventActionsAdvlistPrsPresenter.this.getDisplay());
            this.newSettings = newSettings;
        }

        @Override
        protected void handleSuccess(SaveAdvlistSettingsResponse response)
        {
            SaveEventActionsAdvlistPrsPresenter.this.getDisplay().hide();
            SaveEventActionsAdvlistPrsPresenter.this.unbind();
            SettingsManagementContext context = contextProvider.getSettingsContext();
            sessionSettingsHelper.removeSettingsFromSession((ObjectListBase)context.getContent());
            AdvlistSettingsClient advlistSettingsClient = response.getSettings();
            context.getEventBus()
                    .fireEvent(new AddAdvlistSettingsToSelectToolEvent(advlistSettingsClient, newSettings));
            boolean noCommonOwners = advlistSettingsClient.getCommonOwners().isEmpty()
                                     & !securityHelper.getCurrentUser().isAdmin();
            context.getEventBus().fireEvent(new SetAdvlistSettingsEvent(advlistSettingsClient));
            context.getEventBus().fireEvent(new AdvlistSettingsHasChangedEvent(context.getContent().getUuid(),
                    noCommonOwners, false, false));
        }
    }

    @Inject
    private AdvlistSessionSettingsHelper sessionSettingsHelper;
    @Inject
    private PropertiesEngineGinjector.PropertyContainerPresenterFactory containerFactory;
    @Inject
    private PropertyControllerFactory<AdvlistSettings, ObjectFormAdd> propertyFactory;
    @Inject
    private AdvlistActionFactory actionFactory;
    @Inject
    private I18nUtil i18nUtil;

    private PropertyContainerPresenter propertyContainer;//NOPMD

    private IProperties propertyValues = new MapProperties();

    @Inject
    public SaveEventActionsAdvlistPrsPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(AdvlistSettingsClient value, AsyncCallback<AdvlistSettingsClient> callback)
    {

    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        Boolean newSettings = AdvlistSettingsFormGinModule.MODE_NEW
                .equals(propertyValues.getProperty(AdvlistSettingsFormGinModule.MODE));
        AdvlistSettings settings = buildAdvlistSettings();
        SaveAdvlistSettingsAction action = actionFactory.saveAdvlistSettingsAction(settings, newSettings, false, true,
                contextProvider.getSettingsContext());
        service.execute(action, new SaveAdvlistCallback(newSettings));
    }

    @Override
    protected void bindProperties()
    {
        initPropertyValues();
        getDisplay().setCaptionText(messages.settingsSaving());
        propertyContainer = containerFactory.createSimple(
                Arrays.asList(AdvlistSettingsFormGinModule.MODE, AdvlistSettingsFormGinModule.TITLE), getDisplay(),
                propertyFactory, contextProps, propertyValues, validation);
        propertyContainer.bind();
        getDisplay().display();
    }

    @Override
    protected AdvlistSettings buildAdvlistSettings()
    {
        HashSet<String> commonUuids = new HashSet<>();
        boolean newSettings = AdvlistSettingsFormGinModule.MODE_NEW
                .equals(propertyValues.getProperty(AdvlistSettingsFormGinModule.MODE));
        AdvlistSettingsClient advlistSettingsClient = contextProvider.getSettings();
        // Подменяем критерии "название содержит" из быстрой фильтрации на сходные по смыслу
        // критерии, существующие для строковых атрибутов
        for (ListFilterOrElement<?> element : advlistSettingsClient.getFastSubstringFilter().values())
        {
            if (StringUtilities.isEmpty(element.getAttributeFqn()))
            {
                continue;
            }
            AttributeFqn attributeFqn = AttributeFqn.parse(element.getAttributeFqn());
            Attribute currAttribute = contextProvider.getMode().getAttributes().get(attributeFqn.toString());
            String typeCode = currAttribute.getType().getCode();
            if (Constants.STRING_ATTRIBUTE_TYPES.contains(typeCode))
            {
                element.setProperty(CONDITION_CODE, CONTAINS);
            }
        }

        AdvlistSettings resultSettings = AdvlistUtils.toServerSettings(advlistSettingsClient);
        if (newSettings)
        {
            resultSettings.getTitle().clear();
        }
        i18nUtil.updateI18nObjectTitle(resultSettings,
                propertyValues.<String> getProperty(AdvlistSettingsFormGinModule.TITLE));
        resultSettings.setOwnerUuids(commonUuids);
        return resultSettings;
    }

    @Override
    protected void initPropertyValues()
    {
        // Изначально новый
        propertyValues.setProperty(AdvlistSettingsFormGinModule.MODE, "new");
        propertyValues.setProperty(AdvlistSettingsFormGinModule.TITLE, "");
        AdvlistSettingsClient advlistSettingsClient = settings.isGlobalDefaultSettings() ? contextProvider
                .getActiveSettings() : contextProvider.getSettings();
        contextProps.setProperty(AdvlistSettingsFormGinModule.ADVLIST_SETTINGS, advlistSettingsClient);
        contextProps.setProperty(AdvlistSettingsFormGinModule.IS_CURRENT_USER_SETTINGS, isCurrentUserSetting());
        contextProps.setProperty(AdvlistSettingsFormGinModule.CLASS_FQN,
                contextProvider.getContext().getSettingsClass());
        String currentMode = getMode();
        propertyValues.setProperty(AdvlistSettingsFormGinModule.MODE, currentMode);
        if (AdvlistSettingsFormGinModule.MODE_NEW.equals(currentMode))
        {
            propertyValues.setProperty(AdvlistSettingsFormGinModule.TITLE, "");

        }
        else
        {
            propertyValues.setProperty(AdvlistSettingsFormGinModule.TITLE,
                    i18nUtil.getLocalizedTitle(advlistSettingsClient));
        }
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        service.execute(new GetAdvListPropertiesAction(),
                new BasicCallback<GetAdvListPropertiesResult>(contextProvider.getContext().getReadyState())
                {
                    @Override
                    protected void handleSuccess(GetAdvListPropertiesResult response)
                    {
                        bindProperties();
                    }
                });
    }
}
