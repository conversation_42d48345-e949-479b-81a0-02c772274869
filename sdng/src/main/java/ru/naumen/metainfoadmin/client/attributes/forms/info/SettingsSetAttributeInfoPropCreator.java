package ru.naumen.metainfoadmin.client.attributes.forms.info;

import jakarta.inject.Inject;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.CachedMetainfoServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfoadmin.client.sets.formatters.SettingsSetPropertyFormatter;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * Создает {@link Property} для отображения информации о комплекте на модальной форме свойств атрибута
 *
 * <AUTHOR>
 * @since 19.02.2024
 */
public class SettingsSetAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private CachedMetainfoServiceAsync cachedMetainfoServiceAsync;
    @Inject
    private SettingsSetPropertyFormatter settingsSetPropertyFormatter;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    @Override
    protected void createInt(String code)
    {
        if (settingsSetOnFormCreator.isDisplayedOnCards())
        {
            DtObject settingsSet = cachedMetainfoServiceAsync.getSettingsSet(attribute.getSettingsSet());
            if (settingsSet != null)
            {
                createProperty(code,
                        settingsSetPropertyFormatter.format(settingsSet.getUUID(), settingsSet.getTitle()).asString());
            }
        }
    }
}
