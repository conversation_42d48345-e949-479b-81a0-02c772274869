package ru.naumen.metainfoadmin.client.embeddedapplications.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfoadmin.client.embeddedapplications.form.AddEmbeddedApplicationFormPresenter;

/**
 * <AUTHOR>
 * @since 07.07.2016
 *
 */
public class AddEmbeddedApplicationCommand extends EmbeddedApplicationPresenterCommandBase
{
    @Inject
    private Provider<AddEmbeddedApplicationFormPresenter> formProvider;

    @Inject
    public AddEmbeddedApplicationCommand(@Assisted CommandParam<EmbeddedApplicationAdminSettingsDto,
            EmbeddedApplicationAdminSettingsDto> param)
    {
        super(param);
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

    @Override
    protected CallbackPresenter<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto> getPresenter(
            EmbeddedApplicationAdminSettingsDto value)
    {
        AddEmbeddedApplicationFormPresenter presenter = formProvider.get();
        return presenter;
    }
}
