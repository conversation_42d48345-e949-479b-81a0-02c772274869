package ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.tree.view.TreeViewModelContext;
import ru.naumen.core.client.tree.view.TreeViewModelImpl;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;

/**
 * Модель представления дерева событий.
 * <AUTHOR>
 * @since 06.08.2023
 */
public class EventsDtoTreeViewModel extends
        TreeViewModelImpl<DtObject, EventsDtoTreeSelectionModel, TreeViewModelContext<DtObject,
                EventsDtoTreeSelectionModel>>
{
    @Inject
    public EventsDtoTreeViewModel(@Assisted TreeViewModelContext<DtObject, EventsDtoTreeSelectionModel> context)
    {
        super.init(context);
    }

    @Override
    public DtObject getHiddenRoot()
    {
        return new SimpleTreeDtObject(null, new SimpleDtObject(null, null));
    }

    @Override
    public int getPageSize()
    {
        return Integer.MAX_VALUE;
    }

    @Override
    public boolean isLeaf(Object obj)
    {
        return Boolean.TRUE.equals(((DtObject)obj).getProperty(Constants.DtoTree.IS_LEAF));
    }
}