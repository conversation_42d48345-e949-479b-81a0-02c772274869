package ru.naumen.metainfoadmin.client.scheduler.forms.creators;

import java.util.Map;

import ru.naumen.core.shared.ITitled;

/**
 * Фабрика создания {@link TriggerCreator}-ов создающих заданные правила. Так же является реестром правил которые
 * могут быть созданы.
 *
 * <AUTHOR>
 *
 */
public interface TriggerCreatorFactory
{
    /**
     * Фабрика создающая конкретный {@link TriggerCreator}.
     */
    interface Factory extends ITitled
    {
        TriggerCreator create();

    }

    Map<String, String> getCalculateStrategies();

    /**
     * Создает {@link TriggerCreator} для правила задачи планировщика с заданным именем.
     * <p>
     *
     * @param name
     *
     * @return
     */
    TriggerCreator getCreator(String name);

    /**
     * Возвращает коллекцию всех доступных типов правил задач планировщика для создания (точнее их описании в фабрике).
     */
    Map<String, Factory> getCreators();

    Map<String, String> getPeriods();

    String getTypeTitle(String code);
}
