package ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.view;

import java.util.Collection;

import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.inject.Inject;
import com.google.inject.Singleton;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationUtils;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.shared.dynadmin.presentation.examplevalue.ExampleValueFactoryBusinessObjects;

/**
 * <AUTHOR>
 * @since 31 янв. 2014 г.
 *
 */
@Singleton
public class ExampleWidgetFactoryBusinessObjects extends ExampleWidgetFactoryView<Collection<DtObject>>
{
    @Inject
    private PresentationUtils presentationUtils;
    @Inject
    private ExampleValueFactoryBusinessObjects exampleValueFactory;

    @Override
    public IsWidget createExampleWidget(PresentationContext context)
    {
        Collection<DtObject> obj = exampleValueFactory.create(context.getAttribute());
        return new HTML(presentationUtils.getEmptyLink(obj)); // NOPMD NSDPRD-28509 unsafe html
    }
}