/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.FlowPanel;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.ToolPanelDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;

/**
 * Дисплей редактируемой тул панели
 *
 * <AUTHOR>
 * @since 26 мая 2015 г.
 */
public class EditableToolPanelDisplayImpl extends FlowPanel implements EditableToolPanelDisplay
{
    private final FontIconDisplay<?> editIcon;

    @Inject
    public EditableToolPanelDisplayImpl(AdminWidgetResources resources, FontIconFactory<?> iconFactory,
            EditableToolPanelMessages messages)
    {
        addStyleName(resources.metainfoAdmin().editableBlockView());
        editIcon = iconFactory.create(IconCodes.EDIT_TOOL_PANEL);
        editIcon.setTitle(messages.startEditToolBar());
        add(editIcon);
    }

    @Override
    public void add(ToolPanelDisplay display)
    {
        add(display.asWidget());
    }

    @Override
    public FontIconDisplay<?> getEditIcon()
    {
        return editIcon;
    }
}