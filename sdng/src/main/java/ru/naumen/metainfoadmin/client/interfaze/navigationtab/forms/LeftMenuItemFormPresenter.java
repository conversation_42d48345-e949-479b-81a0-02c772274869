package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms;

import static ru.naumen.metainfoadmin.client.dynadmin.ContentUtils.MAX_CODE_LENGTH;
import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemContextValueCode.FQNS;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncImpl;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.navigationsettings.Constants;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.core.shared.navigationsettings.dispatch.AddLeftMenuItemAction;
import ru.naumen.core.shared.navigationsettings.dispatch.EditLeftMenuItemAction;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO.MenuItemFormatting;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.core.shared.navigationsettings.menu.MenuIconSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.MenuIconType;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.client.TagActionExecutor;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ContentTemplate;
import ru.naumen.metainfo.shared.templates.list.dispatch.AddListTemplateForLeftMenuItemAction;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.common.objectcommands.form.ObjectFormAfterBindHandler;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemContextValueCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.ReferenceHelper;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentMetaClassPropertiesProcessor;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateServiceAsync;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Презентер формы добавления/редактирования элемента левого меню
 *
 * <AUTHOR>
 * @since 25.06.2020
 */
public class LeftMenuItemFormPresenter<F extends ObjectForm> extends MenuItemFormPresenter<F, LeftMenuItemSettingsDTO>
{
    private static final List<String> LM_PROPERTIES = Arrays
            .asList(MenuItemPropertyCode.TITLE,
                    MenuSettingsPropertyCode.PRESENTATION,
                    MenuSettingsPropertyCode.ABBREVIATION,
                    MenuSettingsPropertyCode.TAGS,
                    MenuSettingsPropertyCode.SETTINGS_SET,
                    MenuSettingsPropertyCode.ICON,
                    MenuItemPropertyCode.FORMATTING,
                    MenuItemPropertyCode.PARENT,
                    MenuItemPropertyCode.TYPE,
                    MenuItemPropertyCode.USE_ATTR_TITLE,
                    MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE,
                    MenuSettingsPropertyCode.PROFILES,
                    MenuItemPropertyCode.TYPE_OF_CARD,
                    ReferenceCode.ATTRIBUTE_CHAIN,
                    ReferenceCode.OBJECT_CLASS,
                    ReferenceCode.OBJECT_CASES,
                    ReferenceCode.REFERENCE_VALUE,
                    MenuItemPropertyCode.REFERENCE_TAB_VALUE,
                    MenuItemPropertyCode.REFERENCE_UI_TEMPLATE,
                    MenuItemPropertyCode.CUSTOM_LINK_VALUE,
                    MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE,
                    MenuItemPropertyCode.NEW_TAB_VALUE,
                    MenuItemPropertyCode.ADD_BUTTON_VALUE,
                    MenuItemLinkToContentCode.CONTENT_TYPE,
                    MenuItemLinkToContentCode.LINK_OBJECT,
                    MenuItemLinkToContentCode.LINK_OBJECT_CASE,
                    MenuItemLinkToContentCode.LINK_OBJECT_ATTR,
                    MenuItemLinkToContentCode.LINK_OBJECT_UUID,
                    MenuItemLinkToContentCode.SHOW_HIERARCHY,
                    MenuItemLinkToContentCode.SHOW_NESTED_IN_NESTED,
                    MenuItemLinkToContentCode.ATTR_CHAIN,
                    MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                    MenuItemLinkToContentCode.HIERARCHY_CLASS,
                    MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                    MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                    MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                    MenuItemLinkToContentCode.OBJECT_CLASS,
                    MenuItemLinkToContentCode.OBJECT_CASES,
                    MenuItemLinkToContentCode.ATTRIBUTE_GROUP,
                    MenuItemLinkToContentCode.LIST_PRESENTATION,
                    MenuItemLinkToContentCode.LIST_TEMPLATE,
                    MenuItemLinkToContentCode.CONTENT_TEMPLATE,
                    MenuItemLinkToContentCode.LIST_PAGE_TITLE,
                    MenuItemLinkToContentCode.HIERARCHY_STRUCTURE,
                    ToolFormPropertyCodes.ACTION,
                    ToolFormPropertyCodes.USE_QUICK_ADD_FORM,
                    ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION,
                    ToolFormPropertyCodes.QUICK_ADD_FORM,
                    ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT);

    private final TransliterationService transliterationService;
    private final SecurityHelper security;
    private final ContentCreatorMessages cmessages;
    private final ContentTemplateServiceAsync contentTemplateService;
    private final TagActionExecutor tagActionExecutor;
    private final ReferenceHelper referenceHelper;

    @Inject
    public LeftMenuItemFormPresenter(PropertyFormDisplay display, EventBus eventBus,
            ObjectFormAfterBindHandler<F, LeftMenuItemSettingsDTO> afterBindHandler,
            PropertyControllerFactory<LeftMenuItemSettingsDTO, ObjectFormEdit> propertyControllerFactory,
            TransliterationService transliterationService,
            SecurityHelper security, ContentCreatorMessages cmessages,
            ContentTemplateServiceAsync contentTemplateService,
            TagActionExecutor tagActionExecutor, ReferenceHelper referenceHelper)
    {
        super(display, eventBus, afterBindHandler, propertyControllerFactory);
        this.transliterationService = transliterationService;
        this.security = security;
        this.cmessages = cmessages;
        this.contentTemplateService = contentTemplateService;
        this.tagActionExecutor = tagActionExecutor;
        this.referenceHelper = referenceHelper;
    }

    @Override
    public void init(NavigationSettings settings, @Nullable LeftMenuItemSettingsDTO value,
            AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super.init(settings, null != value ? value.clone() : null, callback);
    }

    @Override
    protected Map<String, LinkedList<String>> getMenuItemPaths()
    {
        return settings.getLeftMenuItemPaths();
    }

    @Override
    protected Action<SimpleResult<DtoContainer<NavigationSettings>>> getEditNavigationMenuItemAction(
            LeftMenuItemSettingsDTO menuItem)
    {
        LinkedList<String> newPath = getNewPath(propertyValues.getProperty(MenuItemPropertyCode.PARENT));
        if (isNew)
        {
            return new AddLeftMenuItemAction(menuItem, newPath);
        }
        else
        {
            LinkedList<String> oldPath = getMenuItemPaths().get(menuItem.getCode());
            if (oldPath == null)
            {
                oldPath = new LinkedList<>();
            }
            return new EditLeftMenuItemAction(menuItem, newPath, oldPath);
        }
    }

    @Override
    protected void doExecuteAction(Action<SimpleResult<DtoContainer<NavigationSettings>>> action)
    {
        if (menuItem.getType() != MenuItemType.linkToContent)
        {
            doExecuteActionInt(action);
            return;
        }

        String contentType = propertyValues.getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);
        if (HierarchyGrid.class.getSimpleName().equals(contentType))
        {
            processContentLink(action);
        }
        else
        {
            processListLink(action);
        }
    }

    @Override
    protected LinkedList<String> getNewPath(IMenuItem menuItem)
    {
        LinkedList<String> result = Lists.newLinkedList();
        for (; menuItem != null; menuItem = menuItem.getParent())
        {
            if (!Constants.LeftMenuRootValue.CODE.equals(menuItem.getCode()))
            {
                result.addFirst(menuItem.getCode());
            }
        }
        return result;
    }

    @Override
    protected LeftMenuItemSettingsDTO getNewMenuItem()
    {
        return new LeftMenuItemSettingsDTO();
    }

    @Override
    protected List<String> getPropertiesList()
    {
        return LM_PROPERTIES;
    }

    protected void fillPropertyValuesByType(SuccessReadyState readyState)
    {
        if (menuItem == null)
        {
            propertyValues.setProperty(MenuSettingsPropertyCode.PRESENTATION, MenuIconType.ABBREVIATION.name());
            propertyValues.setProperty(MenuItemPropertyCode.FORMATTING, MenuItemFormatting.section.name());
            propertyValues.setProperty(MenuItemLinkToContentCode.LIST_PRESENTATION, PresentationType.ADVLIST.getCode());
            return;
        }
        propertyValues.setProperty(MenuSettingsPropertyCode.ABBREVIATION, menuItem.getIcon().getAbbreviation());
        propertyValues.setProperty(MenuSettingsPropertyCode.TAGS, menuItem.getTags());
        propertyValues.setProperty(MenuItemPropertyCode.FORMATTING, menuItem.getUiFormatting());
        propertyValues.setProperty(MenuSettingsPropertyCode.PRESENTATION, menuItem.getIcon().getType().name());
        propertyValues.setProperty(MenuSettingsPropertyCode.ICON,
                new SimpleDtObject(menuItem.getIcon().getCode(), StringUtilities.EMPTY));
        propertyValues.setProperty(MenuSettingsPropertyCode.PROFILES, menuItem.getProfileCodes());

        contextProps.setProperty(MenuItemContextValueCode.PARENT_PROFILES,
                menuItem.getParent() == null || Constants.LeftMenuRootValue.CODE.equals(
                        menuItem.getParent().getCode()) ? null : menuItem.getParent().getResultProfiles());

        switch (menuItem.getType())
        {
            case addButton:
            {
                Reference refValue = menuItem.getReference();
                if (!ObjectUtils.isEmpty(refValue))
                {
                    Reference refMetaClass = new Reference(refValue.getClassFqn());
                    SimpleDtObject refDTO = new SimpleDtObject(refMetaClass.getCode(), refMetaClass.getTitle());
                    refDTO.setProperty(ReferenceCode.CLASS_FQN, refValue.getClassFqn());
                    propertyValues.<DtObject> setProperty(MenuItemPropertyCode.ADD_BUTTON_VALUE, refDTO);
                }
                contextProps.setProperty(FQNS, CollectionUtils.transform(menuItem.getFqns(), DtObject.CREATE_FROM_FQN));
                propertyValues.setProperty(MenuItemPropertyCode.NEW_TAB_VALUE, menuItem.isNewTab());
                break;
            }
            case chapter:
            case reference:
            {
                Reference refValue = menuItem.getReference();
                ArrayList<String> refMetaClassTabs = new ArrayList<>();
                ArrayList<String> refContentTabs = new ArrayList<>();
                if (!ObjectUtils.isEmpty(refValue) && !ObjectUtils.isEmpty(refValue.getTabUUIDs()))
                {
                    refMetaClassTabs.addAll(refValue.getTabUUIDs().subList(0, 1));
                    if (refValue.getTabUUIDs().size() > 1)
                    {
                        refContentTabs.addAll(refValue.getTabUUIDs().subList(1, refValue.getTabUUIDs().size()));
                    }
                }
                if (!ObjectUtils.isEmpty(refValue))
                {
                    Reference refMetaClass = new Reference(refValue.getClassFqn(), refMetaClassTabs);
                    SimpleDtObject refDTO = new SimpleDtObject(refMetaClass.getCode(), refMetaClass.getTitle());
                    refDTO.setProperty(ReferenceCode.TAB_UUIDS, refMetaClassTabs);
                    refDTO.setProperty(ReferenceCode.CLASS_FQN, refValue.getClassFqn());
                    refDTO.setProperty(ReferenceCode.UI_TEMPLATE, refValue.getTemplateCode());
                    Reference refContent = new Reference(refValue.getClassFqn(), refContentTabs);
                    refContent.setTemplateCode(refValue.getTemplateCode());
                    propertyValues.<DtObject> setProperty(ReferenceCode.REFERENCE_VALUE, refDTO);
                    propertyValues.setProperty(MenuItemPropertyCode.REFERENCE_TAB_VALUE, refContent);
                    propertyValues.setProperty(MenuItemPropertyCode.REFERENCE_UI_TEMPLATE, refValue.getTemplateCode());
                }
                else
                {
                    propertyValues.setProperty(ReferenceCode.REFERENCE_VALUE, null);
                    propertyValues.setProperty(MenuItemPropertyCode.REFERENCE_TAB_VALUE, null);
                    propertyValues.setProperty(MenuItemPropertyCode.REFERENCE_UI_TEMPLATE, null);
                }
                propertyValues.setProperty(MenuItemPropertyCode.NEW_TAB_VALUE, menuItem.isNewTab());
                propertyValues.setProperty(MenuItemPropertyCode.USE_ATTR_TITLE, menuItem.isUseAttributeTitle());
                propertyValues.setProperty(MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE, menuItem.getAttrForUseInTitle());
                propertyValues.setProperty(MenuItemPropertyCode.TYPE_OF_CARD, menuItem.getTypeOfCard());
                referenceHelper.transformAttrReferenceToAttrTreeObject(ReferenceCode.ATTRIBUTE_CHAIN, propertyValues,
                        readyState, menuItem.getAttrChain(), null);
                propertyValues.setProperty(ReferenceCode.OBJECT_CASES, menuItem.getObjectCases());
                break;
            }
            case linkToContent:
                propertyValues.setAll(menuItem);
                referenceHelper.transformAttrReferenceToAttrTreeObject(MenuItemLinkToContentCode.ATTR_CHAIN,
                        propertyValues, readyState, menuItem.getProperty(MenuItemLinkToContentCode.ATTR_CHAIN), null);
                referenceHelper.transformAttrReferenceToAttrTreeObject(MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                        propertyValues, readyState, menuItem.getProperty(MenuItemLinkToContentCode.CHAIN_ATTR_CLASS),
                        null);
                referenceHelper.transformAttrReferenceToAttrTreeObject(MenuItemLinkToContentCode.LINK_ATTRIBUTES_CHAIN,
                        propertyValues, readyState,
                        menuItem.getProperty(MenuItemLinkToContentCode.LINK_ATTRIBUTES_CHAIN),
                        null);
                referenceHelper.transformAttrReferenceToAttrTreeObject(MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                        propertyValues, readyState,
                        menuItem.getProperty(MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR),
                        relatedClass -> relatedClass.getFqn().isClass()
                                ? cmessages.currentObjectBeforeHierarchyClass(relatedClass.getTitle())
                                : cmessages.currentObjectBeforeHierarchyType(relatedClass.getTitle()));
                referenceHelper.transformAttrReferenceToAttrTreeObject(MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                        propertyValues, readyState,
                        menuItem.getProperty(MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR),
                        relatedClass -> cmessages.currentObjectAfterHierarchy(relatedClass.getTitle()));
                referenceHelper.transformAttrReferenceToAttrTreeObject(MenuItemLinkToContentCode.LINK_OBJECT_ATTR,
                        propertyValues, readyState, menuItem.getProperty(MenuItemLinkToContentCode.LINK_OBJECT_ATTR),
                        null);
                propertyValues.setProperty(MenuItemPropertyCode.NEW_TAB_VALUE, menuItem.isNewTab());
                break;
            case customLink:
                propertyValues.setProperty(MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE,
                        menuItem.isCustomLinkSystem());
                propertyValues.setProperty(MenuItemPropertyCode.CUSTOM_LINK_VALUE, menuItem.getReference().getTitle());
                propertyValues.setProperty(MenuItemPropertyCode.NEW_TAB_VALUE, menuItem.isNewTab());
                break;
            case customButton:
                UserEventTool userEventTool = menuItem.getUserEventTool();
                propertyValues.setProperty(ToolFormPropertyCodes.ACTION, userEventTool.getEventUuid());
                propertyValues.setProperty(ToolFormPropertyCodes.USE_QUICK_ADD_FORM, userEventTool.isUseQuickForm());
                propertyValues.setProperty(ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION,
                        Boolean.TRUE.equals(userEventTool.getGoToCardAfterAction()));
                propertyValues.setProperty(ToolFormPropertyCodes.QUICK_ADD_FORM, userEventTool.getQuickForm());
                if (CollectionUtils.isNotEmpty(userEventTool.getAttributesFqnFilledByCurrent()))
                {
                    propertyValues.setProperty(ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT,
                            userEventTool.getAttributesFqnFilledByCurrent().get(0));
                }
                break;
            default:
                break;
        }
    }

    private void processContentLink(Action<SimpleResult<DtoContainer<NavigationSettings>>> action)
    {
        String templateCode = propertyValues.getProperty(MenuItemLinkToContentCode.CONTENT_TEMPLATE);
        if (!ContentTemplate.NEW_TEMPLATE.equals(templateCode))
        {
            doExecuteActionInt(action);
            return;
        }

        String title = propertyValues.getProperty(MenuItemPropertyCode.TITLE, StringUtilities.EMPTY);
        String specialChars = security.hasVendorProfile()
                ? ru.naumen.metainfo.shared.Constants.CODE_SPECIAL_CHARS_FOR_VENDOR
                : ru.naumen.metainfo.shared.Constants.CODE_SPECIAL_CHARS;
        String code = transliterationService.transliterateToCode(title, MAX_CODE_LENGTH, specialChars);

        IProperties environment = ContentTemplatePropertiesTranslator.translate(propertyValues);
        contentTemplateService.addContentTemplate(title, code, environment, new BasicCallback<DtObject>(getDisplay())
        {
            @Override
            protected void handleSuccess(DtObject value)
            {
                menuItem.setProperty(MenuItemLinkToContentCode.CONTENT_TEMPLATE, value.getUUID());
                doExecuteActionInt(action);
            }
        });
    }

    private void processListLink(Action<SimpleResult<DtoContainer<NavigationSettings>>> action)
    {
        String metaClassStr =
                Objects.requireNonNull(LinkToContentMetaClassPropertiesProcessor.getMetaClassString(propertyValues));
        menuItem.setProperty(MenuItemLinkToContentCode.OBJECT_CLASS, metaClassStr);

        DtObject template = propertyValues.getProperty(MenuItemLinkToContentCode.LIST_TEMPLATE);

        if (template == null || !FakeMetaClassesConstants.ListTemplate.NEW_TEMPLATE.equals(template.getUUID()))
        {
            doExecuteActionInt(action);
            return;
        }

        Collection<String> metaCases = propertyValues.getProperty(MenuItemLinkToContentCode.OBJECT_CASES);

        String specialSymbols = security.hasVendorProfile()
                ? ru.naumen.metainfo.shared.Constants.CODE_SPECIAL_CHARS_FOR_VENDOR
                : ru.naumen.metainfo.shared.Constants.CODE_SPECIAL_CHARS;

        String title = propertyValues.getProperty(MenuItemPropertyCode.TITLE, StringUtilities.EMPTY);
        // TODO
        // Возможно, хак, но как иначе передать в шаблон группу атрибутов, не сообразила
        ObjectListBase fakeList = null;
        String attrGroup = propertyValues.getProperty(MenuItemLinkToContentCode.ATTRIBUTE_GROUP);
        String listPresentation = propertyValues.getProperty(MenuItemLinkToContentCode.LIST_PRESENTATION);
        if (StringUtilities.isNotEmpty(attrGroup))
        {
            fakeList = new ObjectList();
            fakeList.setClazz(ClassFqn.parse(metaClassStr));
            fakeList.setAttributeGroup(attrGroup);
            fakeList.getToolPanel().setUseSystemSettings(true);
            if (StringUtilities.isNotEmpty(listPresentation))
            {
                fakeList.setPresentation(listPresentation);
            }
        }
        dispatch.execute(
                new AddListTemplateForLeftMenuItemAction(title,
                        transliterationService.transliterateToCode(title, MAX_CODE_LENGTH,
                                specialSymbols), Objects.requireNonNull(metaClassStr),
                        metaCases == null ? new ArrayList<>()
                                : new ArrayList<>(metaCases), fakeList, null, null),
                new BasicCallback<SimpleResult<DtObject>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtObject> value)
                    {
                        menuItem.setProperty(MenuItemLinkToContentCode.LIST_TEMPLATE, value.get());
                        doExecuteActionInt(action);
                    }
                });
    }

    private void doExecuteActionInt(Action<SimpleResult<DtoContainer<NavigationSettings>>> action)
    {
        PropertyControllerSyncImpl controller = (PropertyControllerSyncImpl)propertyContainer.getContext()
                .getPropertyControllers().get(MenuSettingsPropertyCode.TAGS);
        tagActionExecutor.execute(action, ((TagsProperty)controller.property).getPendingTags(), simpleResultCallback);
    }

    protected void fillMenuItemByType()
    {
        MenuIconSettingsDTO iconDTO = new MenuIconSettingsDTO();
        iconDTO.setType(MenuIconType.valueOf(propertyValues.getProperty(MenuSettingsPropertyCode.PRESENTATION)));
        if (MenuIconType.ABBREVIATION.equals(iconDTO.getType()))
        {
            iconDTO.setAbbreviation(propertyValues.getProperty(MenuSettingsPropertyCode.ABBREVIATION));
        }
        else
        {
            iconDTO.setCode(propertyValues.<DtObject> getProperty(MenuSettingsPropertyCode.ICON).getUUID());
        }
        menuItem.setIcon(iconDTO);
        menuItem.setTags(propertyValues.getProperty(MenuSettingsPropertyCode.TAGS));
        menuItem.setUiFormatting(propertyValues.getProperty(MenuItemPropertyCode.FORMATTING));
        menuItem.setProfiles(getProfiles());
        Reference refMetaClass = null;
        Reference refContent;
        switch (menuItem.getType()) // NOPMD
        {
            case addButton:
                // Не проставляем здесь Reference, так как нужно вычислить форму добавления какого типа необходимо
                // вывести при выборе нескольких типов. Это проще сделать на сервере.
                List<DtObject> value = new ArrayList<>(propertyValues.getProperty(
                        EditNavigationSettingsFormGinModule.MenuItemPropertyCode.ADD_BUTTON_VALUE));
                menuItem.setNewTab(Boolean.TRUE.equals(propertyValues.getProperty(MenuItemPropertyCode.NEW_TAB_VALUE)));
                menuItem.setFqns(CollectionUtils.transformList(value, DtObject.FQN_EXTRACTOR));
                break;
            case chapter:
                DtObject refDTO = propertyValues.getProperty(ReferenceCode.REFERENCE_VALUE);
                if (refDTO != null)
                {
                    refContent = propertyValues.getProperty(MenuItemPropertyCode.REFERENCE_TAB_VALUE);
                    if (refContent != null && refContent.getTabUUIDs() != null)
                    {
                        refDTO.<List<String>> getProperty(ReferenceCode.TAB_UUIDS).addAll(refContent.getTabUUIDs());
                    }
                    ArrayList<String> tabUUIDs =
                            refDTO.<List<String>> getProperty(ReferenceCode.TAB_UUIDS) != null ?
                                    new ArrayList<>(refDTO.<List<String>> getProperty(ReferenceCode.TAB_UUIDS)) :
                                    null;
                    refMetaClass = new Reference(refDTO.getProperty(ReferenceCode.CLASS_FQN), tabUUIDs);
                    refMetaClass.setTemplateCode(refDTO.getProperty(ReferenceCode.UI_TEMPLATE));
                }
                menuItem.setReference(refMetaClass);
                break;
            case reference:
                refDTO = propertyValues.getProperty(ReferenceCode.REFERENCE_VALUE);
                refContent = propertyValues.getProperty(MenuItemPropertyCode.REFERENCE_TAB_VALUE);
                if (refContent != null && refContent.getTabUUIDs() != null)
                {
                    refDTO.<List<String>> getProperty(ReferenceCode.TAB_UUIDS).addAll(refContent.getTabUUIDs());
                }
                ArrayList<String> tabUUIDs = refDTO.<List<String>> getProperty(ReferenceCode.TAB_UUIDS) != null ?
                        new ArrayList<>(refDTO.<List<String>> getProperty(ReferenceCode.TAB_UUIDS)) : null;
                menuItem.setNewTab(Boolean.TRUE.equals(propertyValues.getProperty(MenuItemPropertyCode.NEW_TAB_VALUE)));
                menuItem.setUseAttributeTitle(
                        Boolean.TRUE.equals(propertyValues.getProperty(MenuItemPropertyCode.USE_ATTR_TITLE)));
                setTitle();
                DtObject typeOfCard = propertyValues.getProperty(MenuItemPropertyCode.TYPE_OF_CARD);
                menuItem.setTypeOfCard(typeOfCard != null ? typeOfCard.getUUID() : null);
                menuItem.setAttrChain(ContentTemplatePropertiesTranslator.transformAttrTreeDtoToAttrChain(
                        propertyValues.getProperty(ReferenceCode.ATTRIBUTE_CHAIN)));
                refMetaClass = new Reference(refDTO.getProperty(ReferenceCode.CLASS_FQN), tabUUIDs);
                refMetaClass.setTemplateCode(propertyValues.getProperty(MenuItemPropertyCode.REFERENCE_UI_TEMPLATE));
                List<AttrReference> chain = menuItem.getAttrChain();
                if (chain != null)
                {
                    refMetaClass.setAttrChain(chain);
                }
                menuItem.setReference(refMetaClass);
                menuItem.setObjectCases(propertyValues.getProperty(ReferenceCode.OBJECT_CASES));
                break;
            case linkToContent:
                menuItem.setProperties(propertyValues);
                transformAttrTreeObjectToAttrReference(MenuItemLinkToContentCode.ATTR_CHAIN);
                transformAttrTreeObjectToAttrReference(MenuItemLinkToContentCode.CHAIN_ATTR_CLASS);
                transformAttrTreeObjectToAttrReference(MenuItemLinkToContentCode.LINK_ATTRIBUTES_CHAIN);
                transformAttrTreeObjectToAttrReference(MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR);
                transformAttrTreeObjectToAttrReference(MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR);
                transformAttrTreeObjectToAttrReference(MenuItemLinkToContentCode.LINK_OBJECT_ATTR);
                DtObject template = propertyValues.getProperty(MenuItemLinkToContentCode.LIST_TEMPLATE);
                if (null != template && FakeMetaClassesConstants.ListTemplate.NEW_TEMPLATE.equals(template.getUUID()))
                {
                    menuItem.setProperty(MenuItemLinkToContentCode.LIST_TEMPLATE, template.getUUID());
                }
                menuItem.setNewTab(Boolean.TRUE.equals(propertyValues.getProperty(MenuItemPropertyCode.NEW_TAB_VALUE)));
                break;
            case customLink:
                String customLinkValue = propertyValues.getProperty(MenuItemPropertyCode.CUSTOM_LINK_VALUE);
                menuItem.setCustomLinkSystem(
                        Boolean.TRUE.equals(propertyValues.getProperty(MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE)));
                Reference reference = new Reference();
                reference.setTitle(customLinkValue);
                menuItem.setReference(reference);
                menuItem.setNewTab(Boolean.TRUE.equals(propertyValues.getProperty(MenuItemPropertyCode.NEW_TAB_VALUE)));
            case customButton:
                UserEventTool userEventTool = new UserEventTool();
                String eventUuid = propertyValues.getProperty(ToolFormPropertyCodes.ACTION);
                userEventTool.setEventUuid(eventUuid);
                Boolean useQuickForm = propertyValues.getProperty(ToolFormPropertyCodes.USE_QUICK_ADD_FORM);
                userEventTool.setUseQuickForm(Boolean.TRUE.equals(useQuickForm));
                Boolean goToCard = propertyValues.getProperty(ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION);
                userEventTool.setGoToCardAfterAction(Boolean.TRUE.equals(goToCard));
                String quickAddFormCode = propertyValues.getProperty(ToolFormPropertyCodes.QUICK_ADD_FORM);
                userEventTool.setQuickForm(quickAddFormCode);
                String attributeFqnForFill =
                        propertyValues.getProperty(ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT);
                userEventTool.setAttributesFqnFilledByCurrent(
                        attributeFqnForFill != null ? Collections.singletonList(attributeFqnForFill) : null);
                menuItem.setUserEventTool(userEventTool);
                break;
            default:
                break;
        }
    }

    private List<DtObject> getProfiles()
    {
        Collection<? extends String> value = propertyValues.getProperty(MenuSettingsPropertyCode.PROFILES);
        // XXX Данный метод используется для того, чтобы данные, в итоге, отправить на сервер. Там нам нужен только
        // код. Так что надеюсь, что пустой title нигде не вылезет боком.
        return value == null ? new ArrayList<>() :
                value.stream().map(code -> new SimpleDtObject(code, "")).collect(Collectors.toList());
    }

    protected String getMenuItemCaption()
    {
        return messages.menuLeftElementBy();
    }

    protected void transformAttrTreeObjectToAttrReference(String propertyName)
    {
        RelationsAttrTreeObject ato = propertyValues.getProperty(propertyName);
        if (null == ato)
        {
            return;
        }
        menuItem.setProperty(propertyName, ContentTemplatePropertiesTranslator.transformAttrTreeDtoToAttrChain(ato));
    }
}