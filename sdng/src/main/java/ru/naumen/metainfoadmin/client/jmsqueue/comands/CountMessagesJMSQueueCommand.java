package ru.naumen.metainfoadmin.client.jmsqueue.comands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.jmsqueue.service.JMSQueueServiceAsync;

/**
 * Команда "Посчитать" в блоке "Количество сообщений в очереди"
 * <AUTHOR>
 * @since 26.04.2021
 **/
public class CountMessagesJMSQueueCommand extends BaseCommandImpl<DtObject, SimpleResult<Integer>>
{
    private final JMSQueueServiceAsync jmsQueueServiceAsync;

    @Inject
    public CountMessagesJMSQueueCommand(@Assisted CommandParam<DtObject, SimpleResult<Integer>> param,
            JMSQueueServiceAsync jmsQueueServiceAsync)
    {
        super(param);
        this.jmsQueueServiceAsync = jmsQueueServiceAsync;
    }

    @Override
    public void execute(CommandParam<DtObject, SimpleResult<Integer>> param)
    {
        jmsQueueServiceAsync.getCountMessage(param.getValue().getUUID(), param.getCallback());
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }
}