package ru.naumen.metainfoadmin.client.fastlink.settings;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.USER_INTERFACE;

import java.util.List;

import com.google.gwt.event.shared.EventBus;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSetting;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSettingWithTitles;
import ru.naumen.metainfo.shared.fastlink.settings.dispatch.GetFastLinkSettingAction;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;

/**
 * <AUTHOR>
 * @since 01.03.18
 */
public class FastLinkSettingPresenter extends AdminTabPresenter<FastLinkSettingPlace>
{
    private final DispatchAsync dispatch;
    private final FastLinkSettingInfoPresenter infoPresenter;
    private final Dialogs dialogs;
    private final CommonMessages commonMessages;
    private final MetainfoUtils metainfoUtils;
    protected final AdminMetainfoServiceAsync metainfoService;

    private String fastLinkSettingCode = null;

    private OnStartCallback<List<DtoContainer<FastLinkSettingWithTitles>>> refreshCallback =
            new SafeOnStartBasicCallback<List<DtoContainer<FastLinkSettingWithTitles>>>(getDisplay())
            {
                @Override
                protected void handleSuccess(List<DtoContainer<FastLinkSettingWithTitles>> value)
                {
                    if (fastLinkSettingCode != null)
                    {
                        DtoContainer<? extends FastLinkSetting> foundContainer = value.stream()
                                .filter(dto -> dto.get().getCode().equals(fastLinkSettingCode))
                                .findFirst().orElse(new DtoContainer<>(null));
                        @SuppressWarnings("unchecked")
                        DtoContainer<FastLinkSetting> dtoContainer = (DtoContainer<FastLinkSetting>)foundContainer;
                        infoPresenter.setFastLinkSetting(dtoContainer);
                        getDisplay().setTitle(metainfoUtils.getLocalizedValue(foundContainer.get().getTitle()));
                    }

                    refreshDisplay();
                }
            };

    @Inject
    public FastLinkSettingPresenter(AdminTabDisplay display, // NOSONAR старый код, кол-во зависимостей > 7
            EventBus eventBus,
            DispatchAsync dispatch,
            FastLinkSettingInfoPresenter infoPresenter,
            Dialogs dialogs,
            CommonMessages commonMessages,
            MetainfoUtils metainfoUtils,
            AdminMetainfoServiceAsync metainfoService)
    {
        super(display, eventBus);
        this.dispatch = dispatch;
        this.infoPresenter = infoPresenter;
        this.dialogs = dialogs;
        this.commonMessages = commonMessages;
        this.metainfoUtils = metainfoUtils;
        this.metainfoService = metainfoService;
    }

    @Override
    public void init(FastLinkSettingPlace place)
    {
        fastLinkSettingCode = place.getCode();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        infoPresenter.refreshDisplay();
    }

    protected void afterFastLinkSettingLoaded(DtoContainer<FastLinkSetting> fastLinkSetting)
    {
        String backLinkCaption = commonMessages.back();
        prevPageLinkPresenter.bind(backLinkCaption, FastLinksSettingsListPlace.INSTANCE);

        metainfoService.getSettings(new BasicCallback<Settings>()
        {
            @Override
            protected void handleSuccess(Settings value)
            {
                getDisplay().setTitle(metainfoUtils.getLocalizedValue(fastLinkSetting.get().getTitle()));
                infoPresenter.setFastLinkSetting(fastLinkSetting);
                addContent(infoPresenter, "info");
            }
        });
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();
        infoPresenter.init(refreshCallback);

        dispatch.execute(new GetFastLinkSettingAction(fastLinkSettingCode),
                new BasicCallback<SimpleResult<DtoContainer<FastLinkSettingWithTitles>>>()
                {
                    @Override
                    protected void handleSuccess(@Nullable SimpleResult<DtoContainer<FastLinkSettingWithTitles>> value)
                    {
                        if (value == null || value.get() == null)
                        {
                            dialogs.error(commonMessages.resourceNotFoundUserMessage());
                        }
                        else
                        {
                            afterFastLinkSettingLoaded(transform(value.get()));
                        }
                    }
                });

    }

    private DtoContainer<FastLinkSetting> transform(DtoContainer<FastLinkSettingWithTitles> source)
    {
        FastLinkSetting fastLinkSetting = source.get();
        DtoContainer<FastLinkSetting> dtoContainer = new DtoContainer<>(fastLinkSetting);
        dtoContainer.put(SettingsSet.ADMIN_PERMISSIONS, source.getProperty(SettingsSet.ADMIN_PERMISSIONS));
        return dtoContainer;
    }

    @Override
    protected void onUnbind()
    {
        infoPresenter.unbind();
        super.onUnbind();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return USER_INTERFACE;
    }
}
