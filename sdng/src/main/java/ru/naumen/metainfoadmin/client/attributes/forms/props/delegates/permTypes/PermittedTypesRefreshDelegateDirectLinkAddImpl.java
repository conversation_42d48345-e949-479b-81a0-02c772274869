package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes;

import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.selection.HierarchicalMultiSelectionModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.attributes.forms.props.PermittedTypesPropertyController;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Обновление свойства "Ограничение по типам" для прямой ссылки на форме добавления атрибута
 * <AUTHOR>
 * @since 28.05.2012
 */
public class PermittedTypesRefreshDelegateDirectLinkAddImpl<F extends ObjectFormAdd>
        implements PermittedTypesRefreshDelegate<F>
{
    @Inject
    AdminMetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(final PropertyContainerContext context,
            PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                    HierarchicalMultiSelectionModel<DtObject>>> property,
            final AsyncCallback<Boolean> callback)
    {
        Collection<ClassFqn> fqns = Lists
                .newArrayList(PermittedTypesPropertyController.PERMITTED_TYPES_CLASS_FQN_EXTRACTOR.apply(context));
        metainfoService.getMetaClasses(fqns, new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> response)
            {
                DtObject parentDto = DtObject.CREATE_FROM_METACLASSLITE.apply(response.get(0));
                context.setProperty(AttributeFormPropertyCode.PERMITTED_TYPES, Lists.newArrayList(parentDto));
                callback.onSuccess(true);
            }
        });
    }
}
