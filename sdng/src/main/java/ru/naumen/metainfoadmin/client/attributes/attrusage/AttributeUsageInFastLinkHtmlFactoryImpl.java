package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingPlace;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInFastLink;

/**
 * Представление для отображения значения места использования "Упоминание объектов" на форме "Используется в
 * настройках" в таблице атрибутов
 * <AUTHOR>
 * @since 3 Jul 18
 */
@Singleton
public class AttributeUsageInFastLinkHtmlFactoryImpl extends AttributeHtmlFactoryImpl<AttributeUsageInFastLink>
{
    @Inject
    private Formatters formatters;
    @Inject
    private CommonMessages messages;
    @Inject
    private PlaceHistoryMapper historyMapper;

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInFastLink usage)
    {
        return new SafeHtmlBuilder().append(formatters.formatHyperlinkAsHtml(createLinkToFastLinkCard(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLinkToFastLinkCard(AttributeUsageInFastLink usage)
    {
        FastLinkSettingPlace linkPlace = new FastLinkSettingPlace(usage.getCode());
        //@formatter:off
        return new Hyperlink(
                    messages.fastLinkSetting() + " \"" + usage.getTitle() + "\"", 
                    StringUtilities.getHrefByToken(historyMapper.getToken(linkPlace)));
        //@formatter:on
    }
}
