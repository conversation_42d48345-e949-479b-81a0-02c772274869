package ru.naumen.metainfoadmin.client.attributes.commands;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.client.events.DeleteAttributeEvent;
import ru.naumen.metainfo.client.events.WorkflowTemplateChangedEvent;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.MetaClassProperties;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesWithAttributeInBrowserTabTitleAction;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeDescription;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.attributes.DisabledAttributesChangedEvent;
import ru.naumen.metainfoadmin.client.commands.CommandDialogDescription;
import ru.naumen.metainfoadmin.client.commands.ObjectAsyncCommandImpl;

/**
 * Команда удаления атрибута
 *
 * <AUTHOR>
 */
public class DelCommand extends ObjectAsyncCommandImpl<Attribute, Void>
{
    @Inject
    CommonMessages messages;
    @Inject
    AdminDialogMessages dialogMessages;
    @Inject
    MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    EventBus eventBus;

    private Context context;
    private final DispatchAsync dispatch;

    @Inject
    public DelCommand(@Assisted AttributeCommandParam param, Dialogs dialogs, DispatchAsync dispatch)
    {
        super(param, dialogs);
        context = param.getContext();
        this.dispatch = dispatch;
    }

    @Override
    public void execute(CommandParam<Attribute, Void> param)
    {
        if (param.getValue().isHardcoded()
            || !context.getMetainfo().getFqn().equals(param.getValue().getDeclaredMetaClass()))
        {
            getDialogs().error("Can't delete this attribute");
            return;
        }
        super.execute(param);
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof Attribute))
        {
            return false;
        }
        Attribute attr = (Attribute)input;
        String code = attr.getCode();

        if (Constants.ObjectAttributeType.CODE.equals(attr.getType().getCode()) && code.contains("_"))
        {
            Attribute aggrAttribute = context.getMetainfo().getAttribute(code.substring(0, code.lastIndexOf('_')));
            if (null != aggrAttribute
                && Constants.AggregateAttributeType.CODES.contains(aggrAttribute.getType().getCode()))
            {
                return false;
            }
        }
        return true;
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    protected void onDialogSuccess(CommandParam<Attribute, Void> param)
    {
        metainfoModificationService.delAttribute(param.getValue(), new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass value)
            {
                Set<String> disabledAttributes = context.getContextProperty(MetaClassProperties.DISABLED_ATTRIBUTES);
                if (null != disabledAttributes)
                {
                    Set<String> newDisabledAttributes = new HashSet<>(disabledAttributes);
                    newDisabledAttributes.removeIf(code -> !value.hasAttribute(code));
                    context.setContextProperty(MetaClassProperties.DISABLED_ATTRIBUTES, newDisabledAttributes);
                    eventBus.fireEvent(new DisabledAttributesChangedEvent(value.getFqn(), newDisabledAttributes));
                }
                eventBus.fireEvent(new WorkflowTemplateChangedEvent(value));
                eventBus.fireEvent(new DeleteAttributeEvent());
            }
        });
    }

    @Override
    protected void questionAsync(Attribute value, AsyncCallback<CommandDialogDescription> callback)
    {
        dispatch.execute(new GetMetaClassesWithAttributeInBrowserTabTitleAction(value.getFqn()),
                new CallbackDecorator<SimpleResult<List<MetaClassLite>>, CommandDialogDescription>(callback)
                {
                    @Override
                    protected CommandDialogDescription apply(SimpleResult<List<MetaClassLite>> from)
                    {
                        CommandDialogDescription dialogDescription = new CommandDialogDescription();
                        dialogDescription.setTitle(messages.confirmDelete());
                        StringBuilder sb = new StringBuilder(
                                messages.confirmDeleteQuestion(messages.attributeToLowerCase(), value.getTitle()));
                        if (Constants.AggregateAttributeType.CODES.contains(value.getType().getCode())
                            && !value.getType().isAttributeOfRelatedObject())
                        {
                            sb.append(' ');
                            AggregateAttributeType casted = value.getType().<AggregateAttributeType> cast();
                            ArrayList<String> attrTitles = new ArrayList<>();
                            for (AttributeDescription attr : casted.getAttributes())
                            {
                                attrTitles.add("'" + context.getMetainfo().getAttribute(attr.getAttribute()).getTitle()
                                               + "'");
                            }
                            sb.append(messages.confirmDeleteAggregateAttrPart(StringUtilities.join(attrTitles)));
                        }
                        if (!from.get().isEmpty())
                        {
                            List<MetaClassLite> metaClasses = from.get();
                            sb.append("<br><br>");
                            String metaClassesString = metaClasses.stream()
                                    .map(ITitled::getTitle)
                                    .map(StringUtilities.doubleQuoted())
                                    .collect(Collectors.joining(", "));
                            if (metaClasses.size() == 1)
                            {
                                sb.append(dialogMessages.attributeIsUsedInTabTitle(value.getTitle(),
                                        metaClassesString));
                            }
                            else
                            {
                                sb.append(dialogMessages.attributeIsUsedInTabTitleMultiple(value.getTitle(),
                                        metaClassesString));
                            }
                        }
                        dialogDescription.setMessage(sb.toString());
                        return dialogDescription;
                    }
                });
    }
}
