package ru.naumen.metainfoadmin.client.escalation.schemes.commands;

import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfo.shared.dispatch2.DeleteEscalationSchemeAction;
import ru.naumen.metainfoadmin.client.escalation.EscalationMessages;

/**
 * Команда удаления
 *
 * <AUTHOR>
 * @since 21.12.2018
 *
 */
public class EscalationSchemeDeleteCommand extends BaseCommandImpl<DtoContainer<EscalationScheme>, Void>
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private Dialogs dialogs;
    @Inject
    private I18nUtil i18nUtil;
    @Inject
    private CommonMessages messages;
    @Inject
    private EscalationMessages escalationMessages;

    @Inject
    public EscalationSchemeDeleteCommand(@Assisted EscalationSchemesCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<DtoContainer<EscalationScheme>, Void> param)
    {
        dialogs.question(
                messages.confirmDelete(),
                messages.confirmDeleteQuestion(escalationMessages.escalationSchemeGenitive(),
                        i18nUtil.getLocalizedTitle(param.getValue().get())), new DialogCallback()
                {
                    @Override
                    protected void onYes(final Dialog widget)
                    {
                        widget.hide();
                        dispatch.execute(new DeleteEscalationSchemeAction(param.getValue().get().getCode()),
                                CallbackDecorator.<EmptyResult, Void> nullReturn(param.getCallback()));
                    }
                });
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }
}