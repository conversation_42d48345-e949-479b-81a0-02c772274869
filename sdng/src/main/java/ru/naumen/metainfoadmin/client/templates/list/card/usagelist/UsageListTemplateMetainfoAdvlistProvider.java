package ru.naumen.metainfoadmin.client.templates.list.card.usagelist;

import java.util.Map;

import java.util.HashSet;

import com.google.gwt.safehtml.shared.SafeUri;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.UsageInContentAttributeType;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsageListTemplate;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType_SnapshotObject;
import ru.naumen.metainfo.shared.elements.Attribute_SnapshotObject;
import ru.naumen.metainfo.shared.elements.Presentation_SnapshotObject;
import ru.naumen.objectlist.client.metainfo.FakeMetainfoAdvlistProviderBase;

/**
 * Провайдер метаинформации для списка мест использования шаблона списка
 * <AUTHOR>
 * @since 06.08.2018
 */
public class UsageListTemplateMetainfoAdvlistProvider extends FakeMetainfoAdvlistProviderBase
{
    @Override
    protected Map<String, Attribute> createAttributes()
    {
        Map<String, Attribute> attrs = super.createAttributes();

        Attribute title = createStringAttr(UsageListTemplate.Attributes.ATTR_TITLE, cmessages.contentTitle());
        attrs.put(UsageListTemplate.Attributes.ATTR_TITLE.toString(), title);
        attrCodes.add(UsageListTemplate.Attributes.ATTR_TITLE.toString());

        Attribute settingsParts = createStringAttr(UsageListTemplate.Attributes.ATTR_SETTINGS_PARTS, cmessages
                .settingsParts());
        attrs.put(UsageListTemplate.Attributes.ATTR_SETTINGS_PARTS.toString(), settingsParts);
        attrCodes.add(UsageListTemplate.Attributes.ATTR_SETTINGS_PARTS.toString());

        Attribute usage = createUsageAttrGroupAttribute();
        attrs.put(UsageListTemplate.Attributes.ATTR_USAGE_PLACES.toString(), usage);
        attrCodes.add(UsageListTemplate.Attributes.ATTR_USAGE_PLACES.toString());

        return attrs;
    }

    @Override
    protected SafeUri createTargetUri(DtObject object)
    {
        return null;
    }

    @Override
    protected AttributeFqn getAttrCode()
    {
        return null;
    }

    @Override
    protected AttributeFqn getAttrTitle()
    {
        return null;
    }

    private Attribute createUsageAttrGroupAttribute()
    {
        Attribute_SnapshotObject usageListTemplate = new Attribute_SnapshotObject();
        usageListTemplate.__init__fqn(UsageListTemplate.Attributes.ATTR_USAGE_PLACES);
        usageListTemplate.__init__code(UsageListTemplate.Attributes.ATTR_USAGE_PLACES.getCode());

        AttributeType_SnapshotObject usageListTemplateType = new AttributeType_SnapshotObject();
        usageListTemplateType.__init__attribute(usageListTemplate);
        usageListTemplateType.__init__code(UsageInContentAttributeType.CODE);
        usageListTemplateType.__init__permittedTypes(new HashSet<>());
        usageListTemplateType.put(FakeMetaClassesConstants.TARGET_URI_CREATOR, getTargetUriCreator());

        usageListTemplate.__init__type(usageListTemplateType);
        usageListTemplate.__init__computable(false);
        usageListTemplate.__init__title(cmessages.usagePlaces());
        usageListTemplate.__init__metaClass(getMetaClass());
        usageListTemplate.__init__metaClassLite(getMetaClassLite());
        usageListTemplate.__init__filteredByScript(false);
        usageListTemplate.__init__hiddenAttrCaption(false);

        Presentation_SnapshotObject editPresentation = new Presentation_SnapshotObject();
        editPresentation.__init__code(Presentations.UNKNOWN_EDIT);
        usageListTemplate.__init__editPresentation(editPresentation);

        Presentation_SnapshotObject viewPresentation = new Presentation_SnapshotObject();
        viewPresentation.__init__code(Presentations.USAGE_IN_CONTENT_VIEW);
        usageListTemplate.__init__viewPresentation(viewPresentation);
        return usageListTemplate;
    }

    @Override
    protected ClassFqn getClassFqn()
    {
        return UsageListTemplate.FQN;
    }
}
