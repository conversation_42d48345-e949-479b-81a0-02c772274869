/**
 *
 */
package ru.naumen.metainfoadmin.client.interfaze.interfacetab;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.TabLayoutDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataAction;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataResponse;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfoadmin.client.AdminTabContainerPresenter;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.content.ContentSettingsPresenter;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.language.LanguageSettingsPresenter;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.loginpage.CustomLoginPagePresenter;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.tabheader.TabHeaderPresenter;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.DefaultThemePresenter;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.ThemesBaseSettingsPresenter;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.ThemesPresenter;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.ui2.logo.Ui2LogoPresenter;

/**
 * Презентер страницы "Интерфейс". Добавляет на страницы презентеры для редактирования
 * настроек интерфейса
 *
 * <AUTHOR>
 * @since 15.07.2016
 */
public class InterfaceTabSettingsPresenter extends AdminTabContainerPresenter
        implements InterfaceSettingsContextChangedHandler
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private ThemesPresenter themesPresenter;
    @Inject
    private Ui2LogoPresenter logoPresenter;
    @Inject
    private ThemesBaseSettingsPresenter themesBaseSettingsPresenter;
    @Inject
    private DefaultThemePresenter defaultThemePresenter;
    @Inject
    private LanguageSettingsPresenter languageSettingsPresenter;
    @Inject
    private TabHeaderPresenter tabHeaderPresenter;
    @Inject
    private CustomLoginPagePresenter customLoginPagePresenter;
    @Inject
    private ContentSettingsPresenter contentSettingsPresenter;
    @Inject
    private SharedSettingsClientService settingsClientService;
    private final InterfaceSettingsContext context = new InterfaceSettingsContext();

    @Inject
    public InterfaceTabSettingsPresenter(TabLayoutDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void onInterfaceSettingsContextChanged(InterfaceSettingsContextChangedEvent event)
    {
        if (event.getSettings() != null)
        {
            context.setSettings(event.getSettings());
        }
        if (event.getThemes() != null)
        {
            context.setThemes(event.getThemes());
        }
        refreshDisplay();
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        dispatch.execute(new GetInterfaceTabDataAction(),
                new BasicCallback<GetInterfaceTabDataResponse>(readyState, getDisplay())
                {
                    @Override
                    protected void handleSuccess(GetInterfaceTabDataResponse response)
                    {
                        context.setSettings(response.getSettings());
                        context.setThemes(response.getThemes());
                    }
                });
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(InterfaceSettingsContextChangedEvent.getType(), this));

        themesPresenter.init(context);
        addContent(themesPresenter, "themes");

        if (settingsClientService.isUI2AdminLogoEnabled())
        {
            logoPresenter.init(context);
            addContent(logoPresenter, "ui2Logo");
        }
        themesBaseSettingsPresenter.init(context);
        addContent(themesBaseSettingsPresenter, "baseSettings");

        defaultThemePresenter.init(context);
        addContent(defaultThemePresenter, "defaultTheme");

        languageSettingsPresenter.init(context);
        addContent(languageSettingsPresenter, "language");

        tabHeaderPresenter.init(context);
        addContent(tabHeaderPresenter, "tabHeader");

        customLoginPagePresenter.init(context);
        addContent(customLoginPagePresenter, "customLoginPage");

        contentSettingsPresenter.init(context);
        addContent(contentSettingsPresenter, "contents");

        refreshDisplay();
    }
}
