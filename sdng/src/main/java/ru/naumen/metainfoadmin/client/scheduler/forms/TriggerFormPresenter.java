package ru.naumen.metainfoadmin.client.scheduler.forms;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.mailreader.client.InboundMailClientService;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;
import ru.naumen.mailreader.shared.task.ReceiveMailTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfo.shared.scheduler.TriggerType;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskMessages;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.TriggerCreator;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.TriggerCreatorFactory;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * Правило задачи планировщика
 * <AUTHOR>
 * @since 28.06.2011
 */
public abstract class TriggerFormPresenter extends OkCancelPresenter<PropertyDialogDisplay> implements
        CallbackPresenter<DtoContainer<Trigger>, DtoContainer<Trigger>>
{

    @Inject
    Processor validation;
    @Inject
    private NotNullValidator<SelectItem> notNullValidator;
    @Inject
    TriggerCreatorFactory factory;
    @Inject
    SchedulerTaskMessages messages;
    @Inject
    private InboundMailClientService inboundMailClientService;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    AsyncCallback<DtoContainer<Trigger>> saveCallback;

    String schTaskCode;

    DtoContainer<Trigger> trigger;

    TriggerCreator creator;

    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    @Inject
    SelectListProperty<String, SelectItem> type;
    Property<SelectItem> settingsSet;

    public TriggerFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(@Nullable DtoContainer<Trigger> trigger, AsyncCallback<DtoContainer<Trigger>> saveCallback)
    {
        this.trigger = trigger;
        this.saveCallback = saveCallback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        DtoContainer<Trigger> triggerContainer = creator.getTrigger();
        if (null == triggerContainer)
        {
            return;
        }
        checkTriggerAndInboundMailServerConfig(triggerContainer);
    }

    @Override
    public void refreshDisplay()
    {

    }

    public void setSchTaskCode(String schTaskCode)
    {
        this.schTaskCode = schTaskCode;
    }

    protected void bindProperties()
    {
        type.setCaption(messages.triggerType());
        type.setValidationMarker(true);
        getDisplay().add(type);

        if (trigger != null)
        {
            initTriggerCreator(trigger.get().getType().toString());
        }
        else
        {
            settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getDisplay(), null);
        }
        validation.validate(type, notNullValidator);
        DebugIdBuilder.ensureDebugId(type, "type");
    }

    protected void initTriggerCreator(String code)
    {
        if (null != creator)
        {
            creator.removeProperties();
        }
        if (null == code)
        {
            creator = null;
        }
        else
        {
            display.clearProperties();
            display.add(type);
            creator = factory.getCreator(code);
            creator.init(schTaskCode, trigger);
            creator.bindProperties();
            creator.addProperties(getDisplay());
            settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getDisplay(),
                    trigger != null ? trigger.get().getSettingsSet() : null);
        }
    }

    @Override
    protected void onBind()
    {
        bindProperties();
        super.onBind();
        registerHandler(type.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                initTriggerCreator(SelectListPropertyValueExtractor.getValue(type));
                eventBus.fireEvent(new UpdateTabOrderEvent(false));
                TabOrderHelper.setFocusDeffered(type.getValueWidget(), true);
            }
        }));
    }

    /**
     * Проверка конфигурацию входящей почты у планировщика задачи, на котором добавляется новое правило.
     * @param trigger добавляемое правило.
     */
    private void checkTriggerAndInboundMailServerConfig(DtoContainer<Trigger> trigger)
    {
        if (trigger.getCode() == null || trigger.isEnabled()) //пустой код воспринимать как новый создаваемый триггер
        {
            inboundMailClientService.getTaskAndInboundMailServerConfig(
                    trigger.get().getSchTaskCode(),
                    new AsyncCallback<Pair<ReceiveMailTask, InboundMailServerConfig>>()
                    {
                        @Override
                        public void onFailure(Throwable throwable)
                        {
                        }

                        @Override
                        public void onSuccess(
                                Pair<ReceiveMailTask, InboundMailServerConfig> taskAndInboundMailServerConfig)
                        {
                            inboundMailClientService.showDialogMessageBeforeEnableTrigger(
                                    trigger,
                                    taskAndInboundMailServerConfig.left,
                                    taskAndInboundMailServerConfig.right,
                                    () -> applyTrigger(trigger));
                        }
                    }
            );
        }
        else
        {
            applyTrigger(trigger);
        }
    }

    /**
     * Выполнить сохранение триггера
     * @param trigger триггер, который нужно сохранить.
     */
    private void applyTrigger(DtoContainer<Trigger> trigger)
    {
        trigger.get().setType(TriggerType.valueOf(SelectListPropertyValueExtractor.getValue(type)));
        trigger.get().setSchTaskCode(schTaskCode);
        trigger.get().setSettingsSet(SelectListPropertyValueExtractor.getValue(settingsSet));
        saveCallback.onSuccess(trigger);
    }
}