package ru.naumen.metainfoadmin.client.interfaze.interfacetab;

import com.google.gwt.resources.client.CssResource;

/**
 * Стили для окна настроек интерфейса в админке
 * <AUTHOR>
 * @since 29.07.16
 */
public interface InterfaceSettingsCss extends CssResource
{
    @ClassName("loginFormLogo")
    String loginFormLogo();

    @ClassName("systemLogo")
    String systemLogo();

    @ClassName("tableImageBlue")
    String tableImageBlue();

    @ClassName("tableImageDiv")
    String tableImageDiv();

    @ClassName("tableSvgTitle")
    String tableSvgTitle();

    @ClassName("tableSvgDiv")
    String tableSvgDiv();

    @ClassName("tableSvgDark")
    String tableSvgDark();

    @ClassName("tableImageScheme1")
    String tableImageScheme1();

    @ClassName("tableImageScheme2")
    String tableImageScheme2();

    @ClassName("tableImageScheme3")
    String tableImageScheme3();

    @ClassName("tableImageScheme4")
    String tableImageScheme4();

    @ClassName("tableImageScheme5")
    String tableImageScheme5();

    @ClassName("tableImageScheme6")
    String tableImageScheme6();

    @ClassName("tableImageScheme7")
    String tableImageScheme7();

    @ClassName("tableImageSite")
    String tableImageSite();
}
