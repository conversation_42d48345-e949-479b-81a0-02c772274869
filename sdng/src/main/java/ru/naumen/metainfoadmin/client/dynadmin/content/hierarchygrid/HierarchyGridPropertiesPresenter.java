package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.content.toolbar.ToolBarUtils;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.structuredobjectsviews.dispatch.GetStructuredObjectsViewSimpleInfoAction;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.HierarchyGrid.CardObjectFocus;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentFormType;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentPropertiesPresenterBase;

/**
 * Представление редактируемых свойств контента «Иерархическое дерево».
 * <AUTHOR>
 * @since Mar 22, 2021
 */
public class HierarchyGridPropertiesPresenter extends ContentPropertiesPresenterBase<HierarchyGrid>
{
    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    private SelectListProperty<String, SelectItem> structuredObjectsViewCode;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> buildHierarchyFromCurrentObject;
    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    private SelectListProperty<String, SelectItem> cardObjectFocus;

    @Inject
    private NotNullValidator<SelectItem> notNullValidator;
    @Inject
    private HierarchyGridMessages messages;
    @Inject
    private ToolBarUtils toolBarUtils;
    @Inject
    private DispatchAsync dispatch;

    @Override
    public void bindHandlers(HierarchyGrid content)
    {
        if (getPropertiesOwner().getContentFormType() == ContentFormType.ADD_TEMPLATE)
        {
            return;
        }
        registerHandler(buildHierarchyFromCurrentObject.addValueChangeHandler(event ->
        {
            PropertyRegistration<SelectItem> cardObjectFocusPR = getPropertiesOwner().getPropertyRegistration(
                    cardObjectFocus);
            if (!event.getValue() && null == cardObjectFocusPR)
            {
                getPropertiesOwner().addPropertyAfter(cardObjectFocus, buildHierarchyFromCurrentObject);
            }
            else if (event.getValue() && null != cardObjectFocusPR)
            {
                cardObjectFocus.trySetObjValue(CardObjectFocus.OFF.name());
                cardObjectFocusPR.unregister();
            }
        }));
        registerHandler(structuredObjectsViewCode.addValueChangeHandler(event ->
        {
            if (null == getPropertiesOwner().getAttention())
            {
                return;
            }
            if (!content.getObjectFilters().isEmpty() && null != content.getStructuredObjectsViewCode()
                && !content.getStructuredObjectsViewCode().equals(event.getValue().getUUID()))
            {
                getPropertiesOwner().getAttention()
                        .setHTML(messages.changeStructureCodeWarning()); // NOPMD NSDPRD-28509 unsafe html
            }
            else
            {
                getPropertiesOwner().getAttention().clear();
            }
        }));
    }

    @Override
    public List<Property<?>> bindProperties(HierarchyGrid content)
    {
        List<Property<?>> properties = new ArrayList<>();
        structuredObjectsViewCode.setValidationMarker(true);
        DebugIdBuilder.ensureDebugId(structuredObjectsViewCode, "structuredObjectsViewItemCode");
        structuredObjectsViewCode.setCaption(messages.structuredObjectsView());
        registerValidation(getPropertiesOwner().getValidationProcessor()
                .validate(structuredObjectsViewCode, notNullValidator));
        updateStructureCodeProperty(structuredObjectsViewCode);
        structuredObjectsViewCode.trySetObjValue(content.getStructuredObjectsViewCode());
        properties.add(structuredObjectsViewCode);

        if (getPropertiesOwner().getContentFormType() != ContentFormType.ADD_TEMPLATE)
        {
            DebugIdBuilder.ensureDebugId(buildHierarchyFromCurrentObject, "buildHierarchyFromCurrentObject");
            buildHierarchyFromCurrentObject.setCaption(messages.buildHierarchyFromCurrentObject());
            buildHierarchyFromCurrentObject.setValue(content.isBuildHierarchyFromCurrentObject());
            properties.add(buildHierarchyFromCurrentObject);

            DebugIdBuilder.ensureDebugId(cardObjectFocus, "cardObjectFocus");
            cardObjectFocus.setCaption(messages.cardObjectFocus());
            updateCardObjectFocusProperty(cardObjectFocus);
            cardObjectFocus.trySetObjValue(content.getFocusOnCardObject());
            if (!content.isBuildHierarchyFromCurrentObject())
            {
                properties.add(cardObjectFocus);
            }
        }
        return properties;
    }

    @Override
    public HierarchyGrid createContent()
    {
        HierarchyGrid grid = new HierarchyGrid();
        grid.setToolPanel(toolBarUtils.createDefault(grid));
        grid.setMassOperationsPanel(toolBarUtils.createDefault(grid));
        grid.getToolPanel().setUseSystemSettings(true);
        return grid;
    }

    @Override
    public void setContentProperties(HierarchyGrid content)
    {
        String structuredObjectsViewCodeValue = SelectListPropertyValueExtractor.getValue(structuredObjectsViewCode);
        if (null != structuredObjectsViewCodeValue)
        {
            content.setStructuredObjectsViewCode(structuredObjectsViewCodeValue);
        }
        if (!Objects.equals(structuredObjectsViewCodeValue, content.getStructuredObjectsViewCode()))
        {
            ToolPanel toolPanel = toolBarUtils.createDefault(content);
            toolPanel.setShowToolCaptions(content.getToolPanel().isShowToolCaptions());
            content.setToolPanel(toolPanel);
        }

        String cardObjectFocusValue = SelectListPropertyValueExtractor.getValue(cardObjectFocus);
        if (null != cardObjectFocusValue)
        {
            content.setFocusOnCardObject(cardObjectFocusValue);
        }
        content.setBuildHierarchyFromCurrentObject(buildHierarchyFromCurrentObject.getValue());
    }

    @Override
    public void unbind()
    {
        getPropertiesOwner().unbindProperty(structuredObjectsViewCode);
        getPropertiesOwner().unbindProperty(buildHierarchyFromCurrentObject);
        getPropertiesOwner().unbindProperty(cardObjectFocus);
        super.unbind();
    }

    private void updateCardObjectFocusProperty(SelectListProperty<String, SelectItem> property)
    {
        Arrays.stream(CardObjectFocus.values())
                .forEach(value -> property.<SingleSelectCellList<SelectItem>> getValueWidget().addItem(
                        messages.cardObjectFocusTitle(value.name()), value.name()));
    }

    private void updateStructureCodeProperty(SelectListProperty<String, SelectItem> property)
    {
        dispatch.execute(new GetStructuredObjectsViewSimpleInfoAction(),
                new BasicCallback<SimpleResult<Collection<DtObject>>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<Collection<DtObject>> result)
                    {
                        property.<SingleSelectCellList<SelectItem>> getValueWidget().setHasEmptyOption(true);
                        result.get().forEach(item -> property.<SingleSelectCellList<SelectItem>> getValueWidget()
                                .addItem(item.getTitle(), item.getUUID()));
                    }
                });
    }
}
