package ru.naumen.metainfoadmin.client.escalation.schemes.commands;

import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EditEscalationSchemeForm;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.EscalationSchemeFormFactory;

/**
 * Команда редактирования
 *
 * <AUTHOR>
 * @since 21.12.2018
 *
 */
public class EscalationSchemeEditCommand extends BaseCommandImpl<DtoContainer<EscalationScheme>, Void>
{
    @Inject
    private EscalationSchemeFormFactory<EditEscalationSchemeForm> formFactory;

    @Inject
    public EscalationSchemeEditCommand(@Assisted EscalationSchemesCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<DtoContainer<EscalationScheme>, Void> param)
    {
        EscalationSchemeCommandContext context = ((EscalationSchemesCommandParam)param).getContext();
        EditEscalationSchemeForm editForm = formFactory.create(context.getLocalEventBus());
        editForm.setScheme(param.getValue().get());
        editForm.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}