package ru.naumen.metainfoadmin.client.escalation.scheme.levels.commands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;

/**
 * Команда перемещения вниз
 *
 * <AUTHOR>
 * @since 18.12.2018
 *
 */
public class EscalationSchemeLevelsMoveDownCommand extends EscalationSchemeLevelsMoveBaseCommand
{
    @Inject
    public EscalationSchemeLevelsMoveDownCommand(@Assisted EscalationSchemeLevelsCommandParam param)
    {
        super(param);
    }

    @Override
    public int getMoveDirection()
    {
        return 1;
    }

    @Override
    public boolean isPossible(Object input)
    {
        return !((EscalationSchemeLevel)input).isLastLevel();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DOWN;
    }
}
