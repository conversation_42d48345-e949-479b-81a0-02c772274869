package ru.naumen.metainfoadmin.client.attributes;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * <AUTHOR>
 */
@DefaultLocale("ru")
public interface AttributesMessages extends Messages
{
    String addUserAttribute();

    @Description("Фильтрация в сложных списках с учётом морфологии")
    String advlistSemanticFiltering();

    String aggregateClasses();

    String aggregatingAttributes();

    String attibuteOfRelatedClass();

    String attribute();

    String attributeParameters();

    String attributeParamsChanging();

    @Description("Настройки ограничения значения атрибута '%название атрибутав котором текущий установлен в "
                 + "ограничениях%'.")
    String attributeUsageRestrictionPlace(String title);

    @Description("Внимание! Для текущего атрибута не доступна настройка ограничения на ввод даты, так как данный "
                 + "атрибут используется в настройках ограничения атрибута “%Название атрибута%”.")
    String attributeUsedInOtherRestriction(String title);

    String attributeValue();

    String buildHierarchyFrom();

    String calculatingByScript();

    String calculatingOnEdit();

    String caseAtributes();

    String caseProperties();

    String catalog();

    String checkBoxDefValue(String yes);

    String classAtributes();

    String classProperties();

    String clazz();

    String complexAttrGroup();

    @Description("Группа атрибутов в списке для класса Сотрудник")
    String complexEmplAttrGroup();

    String complexFormEmplAttrGroup();

    String complexFormOuAttrGroup();

    String complexFormTeamAttrGroup();

    @Description("Группа атрибутов в списке для класса Отдел")
    String complexOuAttrGroup();

    String complexRelation();

    String complexRelationType(@Select String typeCode);

    @Description("Группа атрибутов в списке для класса Команда")
    String complexTeamAttrGroup();

    String composite();

    String compositeValue();

    String computableOnForm();

    String computableOnFormScript();

    String computeAnyCatalogElementsScript();

    @Description("Установлена зависимость от атрибута '%название атрибута ссылкой%'.")
    String dateTimeAttributeRestriction(String condition, String title);

    @Description("%условие% атрибута %назавание атрибута%")
    String dateTimeAttributeRestrictionInfo(String conditionTitle, String sourceAttrTitle);

    String dateTimeCommonRestrictions();

    String dateTimeRestrictionAttribute();

    String dateTimeRestrictionCondition();

    /**
     * Ограничение значения для атрибутов Дата, Дата/время
     */
    @Description("Ограничение скриптом")
    String dateTimeRestrictionScript();

    /**
     * Ограничение значения для атрибутов Дата, Дата/время
     */
    @Description("Дополнительное ограничение на ввод даты")
    String dateTimeRestrictionType();

    @Description("Ограничение скриптом")
    String dateTimeScriptRestriction();

    String defaultByScript();

    String defaultValue();

    String defaultValueByScript();

    String description();

    String determinableByCorrespondanceTable();

    String determinableByNameRules();

    String determinableByNumbersFormationRule();

    String determinableByValueMap();

    String determinedBy();

    @Description("Ограничение на ввод и отображение десятичных знаков")
    String digitsCountRestrictions();

    String directLink();

    String displayValueWithHierarchy();

    String editAttribute();

    @Description("Редактирование только через расширенную форму")
    String editOnComplexFormOnly();

    String editPresentation();

    String editable();

    String editableInLists();

    String exportNDAP();

    String filterWhileEditing();

    String filteringOnEdit();

    String formDateTimeCommonRestrictions();

    String formDateTimeRestrictionAttribute();

    String formDateTimeRestrictionScript();

    @Description("Разделять по разрядам")
    String hasGroupSeparators();

    @Description("Скрывать архивные объекты")
    String hideArchived();

    @Description("Скрывать название атрибута")
    String hideCaptionAttribute();

    @Description("Скрывать при отображении, если не заполнен")
    String hideWhenEmpty();

    @Description("Скрывать при отображении, если значение \"Нет\"")
    String hideWhenNo();

    @Description("Скрывать при редактировании, если нет значений для выбора")
    String hideWhenNoPossibleValues();

    @Description("Скрывать при отображении, если значение \"0\"")
    String hideWhenZero();

    String hides();

    String inheritParams();

    @Description("Маска ввода")
    String inputmask();

    String inputmaskAliasHelp();

    String inputmaskAttributeValidationMessage();

    String inputmaskDefinitionsHelp();

    @Description("Режим маски ввода")
    String inputmaskMode();

    @Description("Псевдоним")
    String inputmaskModeAlias();

    @Description("Маска с сокращениями")
    String inputmaskModeDefinitions();

    @Description("Регулярное выражение")
    String inputmaskModeRegex();

    String inputmaskRegexHelp();

    @Description("Единицы измерения, доступные при редактировании")
    String intervalAvailableUnits();

    String levelOfHierarchy();

    String linkAttribute();

    String linkedTo();

    String mandatory();

    String mandatoryInInterface();

    @Description("Запоминать выбранные единицы измерения")
    String needStoreUnits();

    @Description("Предупреждение для поля: Запоминать выбранные единицы измерения")
    String needStoreUnitsAttention();

    @Description("Подсказка для поля: Запоминать выбранные единицы измерения")
    String needStoreUnitsInfo();

    String no();

    String objectClass();

    String parentHierarchy0();

    String parentHierarchy1();

    String parentHierarchy2();

    String parentHierarchy3();

    String parentHierarchyN(String n);

    String parentHierarchyTop();

    String quickAddForm();

    String quickEditForm();

    String relatedAttrsToExport();

    String required();

    String requiredInInterface();

    String ruleToDetermine();

    String script();

    String selectSorting();

    String showPresentation();

    String sortBy();

    String sortByCode();

    String sortByTitle();

    String sortByValueType();

    String sortSystemFirst();

    String sortUserFirst();

    String structuredObjectsView();

    @Description("Структура для построения дерева")
    String structuredObjectsViewForBuildingTree();

    @Description("Подсказка для поля: Доступен из системы мониторинга")
    String syncOnlyWhenObjectUpdated();

    String systemAttributes();

    String template();

    String timer();

    String timerDefinition();

    String type();

    String unique();

    String useSystemParams();

    String userAttributes();

    String yes();
}
