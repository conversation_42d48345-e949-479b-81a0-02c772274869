package ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.user.cellview.client.Column;

import ru.naumen.common.client.utils.HtmlSanitizeUtils;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.columns.HTMLCell;
import ru.naumen.core.client.widgets.columns.HasEnabledColumn;
import ru.naumen.core.shared.personalsettings.ThemeClient;
import ru.naumen.metainfoadmin.client.CatalogCellTableResources;
import ru.naumen.metainfoadmin.client.CatalogCellTableResources.CatalogCellTableStyle;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsPresenterSettings;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingPresenterBase;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsResources;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.command.AddThemeCommand;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.command.DeleteThemeCommand;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.command.DisableThemeCommand;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.command.EditThemeCommand;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.command.EnableThemeCommand;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.command.ExportThemePropertiesCommand;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.command.ThemeCommandParam;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Презентер списка тем интерфейса
 *
 * <AUTHOR>
 * @since 15.07.2016
 */
public class ThemesPresenter extends InterfaceSettingPresenterBase<TableDisplay<ThemeClient>>
{
    @Inject
    private InterfaceSettingsResources settingsResources;
    @Inject
    private InterfaceSettingsPresenterSettings interfaceSettingsConfig;
    @Inject
    private CatalogCellTableResources cellTableResources;
    @Inject
    private ThemeMessages messages;
    @Inject
    private ObjectListColumnBuilder tableBuilder;
    @Inject
    private CommonHtmlTemplates templates;
    @Inject
    private ThemeLogosAdmin themeLogos;
    @Inject
    private SystemThemeColumn systemThemeColumn;
    @Inject
    private HasEnabledColumn<ThemeClient> themeEnabledColumn;
    @Inject
    private HtmlSanitizeUtils htmlSanitizeUtils;

    private ThemeCommandParam themeCommandParam;

    @Inject
    public ThemesPresenter(TableDisplay<ThemeClient> display, EventBus eventBus, CommonMessages cmessages,
            ButtonFactory buttonFactory)
    {
        super(display, eventBus, cmessages, buttonFactory);
    }

    public void init(InterfaceSettingsContext context)
    {
        super.init(context);
        themeCommandParam = new ThemeCommandParam(context);
    }

    @Override
    protected void bindContent()
    {
        settingsResources.css().ensureInjected();
        initTable();
    }

    @Override
    protected void refreshContent()
    {
        getDisplay().refresh();
        getDisplay().getTable().setRowCount(context.getThemes().size());
        getDisplay().getTable().setRowData(0, context.getThemes());
    }

    @Override
    protected String getCaption()
    {
        return messages.interfaceThemes();
    }

    @Override
    protected String getEditCommandId()
    {
        return null;
    }

    private void addActionColumn(String... commands)
    {
        tableBuilder.addActionColumn(display, themeCommandParam, commands);
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    protected void bindToolBar()
    {
        if (interfaceSettingsConfig.isWithUserThemes())
        {
            CommandParam<InterfaceSettingsContext, InterfaceSettingsContext> param = new CommandParam<>(
                    context);
            toolBar.add((ButtonPresenter)buttonFactory.create(ButtonCode.ADD, messages.addTheme(), AddThemeCommand.ID,
                    param));
            toolBar.bind();
        }
    }

    private void initTable()
    {
        DataTable<ThemeClient> table = getDisplay().getTable();
        table.setVisibleRange(0, DefaultCellTable.DEFAULT_PAGESIZE);

        CatalogCellTableStyle tableStyle = cellTableResources.cellTableStyle();

        Column<ThemeClient, String> textColumn = new Column<ThemeClient, String>(
                new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
        {
            @Override
            public String getValue(ThemeClient item)
            {
                return htmlSanitizeUtils.sanitize(item.getTitle());
            }
        };
        textColumn.setSortable(false);
        textColumn.setCellStyleNames(tableStyle.cellTableCell());
        table.addColumn(textColumn, cmessages.title());

        textColumn = new Column<ThemeClient, String>(new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
        {
            @Override
            public String getValue(ThemeClient item)
            {
                return item.getCode();
            }
        };
        textColumn.setSortable(false);
        textColumn.setCellStyleNames(tableStyle.cellTableCell());
        table.addColumn(textColumn, cmessages.code());

        table.addColumn(systemThemeColumn, messages.isSytem());

        textColumn = new Column<ThemeClient, String>(new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
        {
            @Override
            public String getValue(ThemeClient item)
            {
                SafeHtml template = null;
                if (context.getSettings().getThemeLogoSettings().get(item.getCode()) == null
                    || context.getSettings().getThemeLogoSettings().get(item.getCode()).isLogoStandart())
                {
                    template = templates.imgAsTableCellWithSizeLimit(
                            themeLogos.getStandartThemeLogo(item.getCode()),
                            settingsResources.css().tableImageDiv(), themeLogos.getThemeImageStyle(item.getCode()));
                }
                else
                {
                    template = templates.imgAsTableCellWithSizeLimit(
                            context.getSettings().getThemeLogoSettings().get(item.getCode()).getLogoUuid(),
                            settingsResources.css().tableImageDiv(), themeLogos.getThemeImageStyle(item.getCode()));
                }

                return template.asString();
            }
        };
        textColumn.setSortable(false);
        textColumn.setCellStyleNames(tableStyle.cellTableCell() + " " + settingsResources.css().systemLogo());
        table.addColumn(textColumn, messages.logo());

        textColumn = new Column<ThemeClient, String>(new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
        {
            @Override
            public String getValue(ThemeClient item)
            {
                SafeHtml template = null;
                if (context.getSettings().getThemeLoginLogoSettings().get(item.getCode()) == null
                    || context.getSettings().getThemeLoginLogoSettings().get(item.getCode()).isLogoStandart())
                {
                    if (context.getSettings().getThemeLogoSettings().get(item.getCode()) == null
                        || context.getSettings().getThemeLogoSettings().get(item.getCode()).isLogoStandart())
                    {
                        template = templates.imgAsTableCellWithSizeLimit(
                                themeLogos.getStandartLoginFormLogo(item.getCode()),
                                settingsResources.css().tableImageDiv(), themeLogos.getThemeImageStyle(item.getCode()));
                    }
                    else
                    {
                        // Показываем загруженный логотип системы
                        template = templates.imgAsTableCellWithSizeLimit(
                                context.getSettings().getThemeLogoSettings().get(item.getCode()).getLogoUuid(),
                                settingsResources.css().tableImageDiv(), themeLogos.getThemeImageStyle(item.getCode()));
                    }
                }
                else
                {
                    template = templates.imgAsTableCellWithSizeLimit(
                            context.getSettings().getThemeLoginLogoSettings().get(item.getCode()).getLogoUuid(),
                            settingsResources.css().tableImageDiv(), themeLogos.getThemeImageStyle(item.getCode()));
                }

                return template.asString();
            }
        };
        textColumn.setSortable(false);
        textColumn.setCellStyleNames(tableStyle.cellTableCell() + " " + settingsResources.css().loginFormLogo());
        table.addColumn(textColumn, messages.loginWindowLogo());

        table.addColumn(themeEnabledColumn, messages.isEnabledForUsers());

        addActionColumn(ExportThemePropertiesCommand.ID);

        addActionColumn(EnableThemeCommand.ID, DisableThemeCommand.ID);
        addActionColumn(EditThemeCommand.ID);
        addActionColumn(DeleteThemeCommand.ID);

        table.asWidget().ensureDebugId("themes-table");
    }
}
