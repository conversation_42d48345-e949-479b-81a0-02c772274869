package ru.naumen.metainfoadmin.client.dynadmin.content.embeddedapplications;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import java.util.ArrayList;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.Window;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.select.selmodel.SelectListCellGinModule;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.GetEmbeddedApplicationsAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;
import ru.naumen.metainfoadmin.client.dynadmin.content.forms.SimpleEditContentPresenter;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;

/**
 * {@link Presenter} для редактирования контента типа {@link EmbeddedApplicationContent}
 *
 * <AUTHOR>
 * @since 22.08.2016
 */
public class EditEmbeddedApplicationContentPresenter extends SimpleEditContentPresenter<EmbeddedApplicationContent>
{
    @Inject
    private EmbeddedApplicationMessages embeddedApplicationMessages;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private I18nUtil i18nUtil;
    @Inject
    private NotNullValidator<SelectItem> notNullValidator;

    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    @Inject
    private SelectListProperty<String, SelectItem> embeddedApplication;
    @Inject
    @Named(SelectListCellGinModule.NOT_SELECTED_CSS)
    private String notSelectedCssClass;
    @Inject
    private EmbeddedApplicationContentDelegate embeddedApplicationContentDelegate;
    private HasProperties.PropertyRegistration<SelectItem> embeddedApplicationPr;

    @Inject
    public EditEmbeddedApplicationContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void bindPropertiesInner()
    {
        embeddedApplicationContentDelegate.init(display, validation, context.getMetainfo().getFqn(), content.getUuid(),
                context.getCode());
        embeddedApplication.setCaption(embeddedApplicationMessages.applicationProperty());
        embeddedApplication.setValidationMarker(true);
        dispatch.execute(new GetEmbeddedApplicationsAction(),
                new BasicCallback<SimpleScriptedResult<List<EmbeddedApplication>>>()
                {
                    @Override
                    public void handleSuccess(SimpleScriptedResult<List<EmbeddedApplication>> response)
                    {
                        List<EmbeddedApplication> sorted = new ArrayList<>();
                        sorted.addAll(response.get());
                        sorted.sort(i18nUtil.getCaseInsensitiveTitleComparator());

                        EmbeddedApplication selectedApplication = null;

                        for (EmbeddedApplication application : sorted)
                        {
                            ((SingleSelectCellList<String>)embeddedApplication.getValueWidget()).addItemWithStyle(
                                    metainfoUtils.getLocalizedValue(application.getTitle()), application.getCode(),
                                    application.isOn() ? null : notSelectedCssClass);

                            if (application.getCode().equals(content.getApplication()))
                            {
                                selectedApplication = application;
                            }
                        }

                        if (selectedApplication != null)
                        {
                            embeddedApplication.trySetObjValue(selectedApplication
                                    .getCode());
                        }
                        onEmbeddedApplicationChange();
                        embeddedApplication.addValueChangeHandler(event -> onEmbeddedApplicationChange());
                    }
                });
        validation.validate(embeddedApplication, notNullValidator);
        DebugIdBuilder.ensureDebugId(embeddedApplication, "embeddedApplication");
        embeddedApplicationPr = display.add(embeddedApplication);
        registerHandler(Window.addCloseHandler(event -> embeddedApplicationContentDelegate.clearProperties()));
    }

    @Override
    protected void updateCurrentContent()
    {
        super.updateCurrentContent();
        content.setApplication(Objects.requireNonNull(SelectListPropertyValueExtractor.getValue(embeddedApplication)));
        embeddedApplicationContentDelegate.setAttributesProperties(content);
    }

    @Override
    protected void restoreContent(EmbeddedApplicationContent oldContent)
    {
        super.restoreContent(oldContent);
        content.setApplication(oldContent.getApplication());
        content.setParametersValues(new HashMap<>(oldContent.getParametersValues()));
    }

    @Override
    protected boolean isContentEquals(EmbeddedApplicationContent oldContent)
    {
        return super.isContentEquals(oldContent)
               && eq(oldContent.getApplication(), content.getApplication())
               && eq(oldContent.getParametersValues(), content.getParametersValues());
    }

    @Override
    protected void updateAfterSave(EmbeddedApplicationContent savedContent)
    {
        super.updateAfterSave(savedContent);
        content.setSerializableParametersValues(savedContent.getSerializableParametersValues());
    }

    private void onEmbeddedApplicationChange()
    {
        embeddedApplicationContentDelegate.onEmbeddedApplicationChange(SelectListPropertyValueExtractor.getValue(
                embeddedApplication), embeddedApplicationPr);
    }

    @Override
    protected void onUnbind()
    {
        embeddedApplicationContentDelegate.clearProperties();
    }
}
