package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import java.util.List;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.attr.presentation.PresentationFactoryEdit;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfo.shared.ui.SelectCase;
import ru.naumen.metainfoadmin.client.common.content.commands.RefreshSelectCaseContentEvent;
import ru.naumen.metainfoadmin.client.common.content.commands.RefreshSelectCaseContentHandler;
import ru.naumen.metainfoadmin.client.common.content.commands.TabContentCommandCode;
import ru.naumen.metainfoadmin.client.dynadmin.content.AbstractEditablePropsContentPresenter;

/**
 * <AUTHOR>
 * @since 26 сент. 2013 г.
 *
 */
public class SelectCaseContentPresenter
        extends AbstractEditablePropsContentPresenter<SelectCase>
        implements RefreshSelectCaseContentHandler
{
    @Inject
    private PresentationFactories prsFactories;
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Inject
    public SelectCaseContentPresenter(PropertyGridFlowContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "SelectCase");
    }

    @Override
    public void onRefreshSelectCaseContentHandler(RefreshSelectCaseContentEvent event)
    {
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();

        setVisibleIconDisplay(TabContentCommandCode.MOVE_TAB_CONTENT, commonUtils.uiNotInherit(getContext()));
        setVisibleIconDisplay(TabContentCommandCode.MOVE_TAB_CONTENT, commonUtils.uiNotInherit(getContext())
                                                                      && SelectCase.getSelectCaseContents(
                context.getRootContent()).size() > 1);
    }

    @Override
    protected boolean isEditable()
    {
        return true;
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        registerHandler(context.getEventBus().addHandler(RefreshSelectCaseContentEvent.getType(), this));
        metainfoService.getMetaClasses(MetaClassFilters.caseOf(context.getMetainfo().getFqn()),
                new BasicCallback<List<MetaClassLite>>()
                {
                    @Override
                    protected void handleSuccess(List<MetaClassLite> value)
                    {
                        Attribute caseAttr = context.getMetainfo().getAttribute(AbstractBO.METACLASS);
                        PresentationContext presentationContext = new PresentationContext(caseAttr);
                        presentationContext.setPermittedTypeFqns(Lists.transform(value, MetaClassLite.FQN_EXTRACTOR));
                        presentationContext.setParentContext(context);
                        String editPrsCode = caseAttr.getEditPresentation().getCode();
                        PresentationFactoryEdit<DtObject> editPrsFactory = prsFactories
                                .getEditPresentationFactory(editPrsCode);
                        editPrsFactory.createWidget(presentationContext, new BasicCallback<HasValueOrThrow<DtObject>>()
                        {
                            @Override
                            protected void handleSuccess(HasValueOrThrow<DtObject> widget)
                            {
                                Property<DtObject> property = propertyCreator.create(messages.metaCase(), widget);
                                if (context.getMetainfo().getFqn().isCase())
                                {
                                    SimpleDtObject item = new SimpleDtObject(context.getMetainfo().getFqn().asString(),
                                            context.getMetainfo().getTitle(), context.getMetainfo().getFqn());
                                    property.setValue(new SelectItem(item));
                                }
                                String style = caseAttr.isHiddenAttrCaption() ?
                                        WidgetResources.INSTANCE.form().hiddenAttrCaption() :
                                        StringUtilities.EMPTY;
                                property.getCaptionWidget().asWidget().setStyleName(style);
                                property.setValidationMarker(true);
                                getDisplay().add(property);
                            }
                        });
                        refreshDisplay();
                    }

                    ;
                });
    }

    @Override
    protected String getHelpText()
    {
        return messages.selectCase();
    }

    @Override
    protected void refreshEditable()
    {
    }
}