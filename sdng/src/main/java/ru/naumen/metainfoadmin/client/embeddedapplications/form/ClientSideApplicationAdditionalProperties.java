package ru.naumen.metainfoadmin.client.embeddedapplications.form;

import ru.naumen.metainfo.shared.embeddedapplication.ClientSideApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;

public class ClientSideApplicationAdditionalProperties extends InternalApplicationAdditionalProperties
        implements AdditionalApplicationPropertiesProvider
{
    @Override
    protected boolean showScript()
    {
        return false;
    }

    @Override
    public EmbeddedApplication createApplication()
    {
        ClientSideApplication clientSideApplication = new ClientSideApplication();
        clientSideApplication.setApplicationType(EmbeddedApplicationType.ClientSideApplication);
        return clientSideApplication;
    }

    @Override
    protected EmbeddedApplicationType getApplicationType()
    {
        return EmbeddedApplicationType.ClientSideApplication;
    }
}
