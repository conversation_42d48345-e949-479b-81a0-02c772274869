package ru.naumen.metainfoadmin.client.dynadmin.content;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * Локализованные сообщения пакета
 * <AUTHOR>
 */
@DefaultLocale("ru")
public interface ContentCreatorMessages extends Messages
{
    @Description("Группа атрибутов для формы добавления комментария")
    String addFormAttributeGroup();

    @Description("Объекты иерархии связаны с объектами списка через атрибут")
    String afterHierarchy();

    @Description("Построить иерархию объектов (вниз), начиная с объекта")
    String beforeHierarchy();

    @Description("Значение по-умолчанию для поля 'Название' контента 'История изменений объекта'")
    String changeHistory();

    @Description("Значение по-умолчанию для поля 'Название' контента 'Список комментариев'")
    String comments();

    @Description("Текущий объект")
    String currentObject();

    @Description("Объект иерархии (класс: {0})")
    String currentObjectAfterHierarchy(String classTitle);

    @Description("Текущий объект (класс: {0})")
    String currentObjectBeforeHierarchyClass(String metaClassTitle);

    @Description("Текущий объект (тип: {0})")
    String currentObjectBeforeHierarchyType(String metaClassTitle);

    @Description("В случае сохранения изменений, с панели действий  будут удалены кнопки  '{0}', так как их настройки"
                 + " перестанут совпадать с настройками контента.")
    String deleteToolsAttention(String toolTitles);

    @Description("Отображать комментарии")
    String displayType();

    @Description("Группа атрибутов для формы редактирования комментария")
    String editFormAttributeGroup();

    @Description("Значение по-умолчанию для поля 'Название' контента 'Список файлов'")
    String files();

    @Description("Из атрибутов")
    String fromAttributes();

    @Description("Скрывать корешок вкладки")
    String hideSingleTab();

    @Description("Скрывать контур вкладки")
    String hideTabBorder();

    @Description("В иерархии потомки связаны с предками через атрибут")
    String hierarchyAttribute();

    @Description("Класс объектов иерархии")
    String hierarchyClass();

    @Description("ссылающиеся на объект")
    String linkedWithObject();

    @Description("ссылающиеся на связанные объекты")
    String linkedWithRelatedObject();

    @Description("Группа атрибутов для блока Подробнее")
    String moreAttributesGroup();

    @Description("Значение по-умолчанию для поля 'Название' контента 'Список вложенных объектов'")
    String nestedObjects();

    @Description("Значение по-умолчанию для поля 'Название' контента 'Схема сети'")
    String networkScheme();

    @Description("Название поля")
    String objectClass();

    @Description("Значение по-умолчанию для поля 'Название' контентов 'Список объектов' и 'Список связанных объектов'")
    String objectsList();

    @Description("Название поля")
    String objectsTypes();

    @Description("текущего объекта")
    String ofCurrentObject();

    @Description("Файлы объекта")
    String ownObjectFiles();

    @Description("связанного объекта")
    String relatedObject();

    @Description("Название поля")
    String relatedObjectClass();

    @Description("Значение по-умолчанию для поля 'Название' контента 'Список связанных объектов'")
    String relatedObjects();

    @Description("набора связанных объектов")
    String relatedObjectSet();

    @Description("Атрибут связи")
    String relationAttribute();

    @Description("Атрибут связи в классе Комментарий")
    String relationAttributeInCommentClass();

    @Description("Атрибут связи в классе Файл")
    String relationAttributeInFileClass();

    @Description("Значение по-умолчанию для поля 'Название' контента 'Схема связей'")
    String relationScheme();

    @Description("Отображать файлы")
    String relationType();

    @Description("Значение по-умолчанию для поля 'Название' контента 'Отчет, печатная форма'")
    String reportPrintingForm();

    @Description("Значение по-умолчанию для поля 'Название' контента 'Список отчетов, печатных форм'")
    String reportsPrintingFormsList();

    @Description("Сбросить введенные значения атрибутов, если контент скрыт по условию")
    String resetIfHidden();

    @Description("Значение по-умолчанию для поля 'Название' контента 'Выбор типа объекта'")
    String selectCase();

    @Description("Значение по-умолчанию для поля 'Название' контента 'Выбор контактного лица'")
    String selectContacts();

    @Description("Показывать прикрепленные файлы из атрибута")
    String showAttributeAttachedFiles();

    @Description("Показывать описание атрибутов для формы добавления")
    String showAttrDescriptionOnAddForm();

    @Description("Показывать описание атрибутов для формы редактирования")
    String showAttrDescriptionOnEditForm();

    @Description("Показывать с вписке объекты, вложенные во вложенные")
    String showNestedInNested();

    @Description("Показывать в списке объекты, связанные со вложенными")
    String showRelatedWithNested();

    @Description("Условие отображения вкладки")
    String tabVisibilityCondition();

    @Description("Настройка условия отображения вкладки")
    String tabVisibilityConditionForm();

    @Description("Атрибуту {0}")
    String toAttribute(String attrTitle);

    @Description("История изменения ответственного и статуса")
    String userHistory();

    @Description("Условие отображения контента")
    String visibilityCondition();

    @Description("Настройка условия отображения контента")
    String visibilityConditionForm();

    @Description("Ограничить содержимое контента")
    String restrictContent();

    @Description("Настройка ограничений содержимого контента")
    String restrictContentForm();

    @Description("Значение по-умолчанию для поля 'Название' контента 'Диаграмма жизненного цикла'")
    String workflowDiagram();

    @Description("Свёрнут по умолчанию")
    String collapsedByDefault();

    @Description("Свёрнут : {0}")
    String collapsedHelpText(String value);
}
