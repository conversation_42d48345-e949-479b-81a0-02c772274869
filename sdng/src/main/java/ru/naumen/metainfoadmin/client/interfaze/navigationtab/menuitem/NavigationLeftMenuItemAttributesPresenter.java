package ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem;

import java.util.ArrayList;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.widgets.properties.container.AttributePropertyDescription;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DeleteLeftMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DisableLeftMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EditLeftMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EnableLeftMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsLMCommandParam;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;

/**
 * Презентер списка атрибутов элемента левого меню
 * <AUTHOR>
 * @since 16.07.2020
 */
public class NavigationLeftMenuItemAttributesPresenter
        extends NavigationMenuItemAttributesPresenter<LeftMenuItemSettingsDTO, NavigationLeftMenuItemContext,
        NavigationSettingsLMCommandParam>
{
    @Inject
    public NavigationLeftMenuItemAttributesPresenter(InfoDisplay display, EventBus eventBus,
            @Named(NavigationMenuItemGinModule.NAVIGATION_LEFT_MENU_ITEM_ATTRIBUTES)
            ArrayList<AttributePropertyDescription<?, NavigationLeftMenuItemContext>> properties,
            NavigationSettingsMessages messages,
            ButtonFactory buttonFactory, CommonMessages cmessages,
            PlaceController placeController, TagsMessages tagsMessages)
    {
        super(display, eventBus, properties, messages, buttonFactory, cmessages, placeController, tagsMessages);
    }

    protected NavigationSettingsLMCommandParam getParam(AsyncCallback<DtoContainer<NavigationSettings>> refreshCallback)
    {
        return new NavigationSettingsLMCommandParam(context.getSettings(), context.getItem(),
                refreshCallback);
    }

    protected LeftMenuItemSettingsDTO getMenuItem(DtoContainer<NavigationSettings> settings)
    {
        return settings.get().findLeftMenuItem(context.getItem().getCode());
    }

    protected String getDeleteCommandId()
    {
        return DeleteLeftMenuItemCommand.ID;
    }

    protected String getEditCommandId()
    {
        return EditLeftMenuItemCommand.ID;
    }

    protected String getDisableCommandId()
    {
        return DisableLeftMenuItemCommand.ID;
    }

    protected String getEnableCommandId()
    {
        return EnableLeftMenuItemCommand.ID;
    }
}