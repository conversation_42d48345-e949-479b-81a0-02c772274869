package ru.naumen.metainfoadmin.client.attributes.forms.props;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;

/**
 * Фабрика контроллеров значений по умолчанию
 *
 * <AUTHOR>
 * @since 16 июня 2016 г.
 */
public interface DefaultValuePropertyControllerFactory<F extends ObjectForm>
        extends PropertyControllerSyncFactoryInj<Object, Property<Object>>
{

}
