package ru.naumen.metainfoadmin.client.adminprofile;

import com.google.gwt.core.client.GWT;
import com.google.gwt.resources.client.ClientBundle;

/**
 * Ресурсы для профилей администрирования
 *
 * <AUTHOR>
 * @since 01.03.2024
 */
public interface AdminProfilesResources extends ClientBundle
{
    AdminProfilesResources INSTANCE = GWT.create(AdminProfilesResources.class);

    @Source({ "adminSettingsMatrix.css" })
    AdminSettingsMatrixCss adminSettingsMatrix();

    @Source({ "accessMarkerMatrix.css" })
    AccessMarkerMatrixCss accessMarkerMatrix();
}