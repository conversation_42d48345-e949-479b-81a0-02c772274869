package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import java.util.Map;

import jakarta.inject.Inject;

import java.util.HashMap;

import ru.naumen.metainfo.shared.Constants;

/**
 * Реализация реестра кастомайзеров создания виджетов для отображения в колонке "Значение по умолчанию"
 *
 * <AUTHOR>
 * @since 18 сент. 2018 г.
 *
 */
public class DefaultValuePrsCustomizersRegistryImpl implements DefaultValuePrsCustomizersRegistry
{
    private Map<String, DefaultValuePrsCustomizer> registry = new HashMap<>();

    @Inject
    public DefaultValuePrsCustomizersRegistryImpl(
            CatalogItemDefaultValuePrsCustomizer catalogItemDefaultValuePrsCustomizer,
            CaseListDefaultValuePrsCustomizer caseListDefaultValuePrsCustomizer,
            CatalogItemsListDefaultValuePrsCustomizer catalogItemsListDefaultValuePrsCustomizer)
    {
        register(Constants.Presentations.CATALOG_ITEM_TITLE_VIEW, catalogItemDefaultValuePrsCustomizer);
        register(Constants.Presentations.CATALOG_ITEMS_VIEW, catalogItemsListDefaultValuePrsCustomizer);
        register(Constants.Presentations.CASE_LIST_VIEW, caseListDefaultValuePrsCustomizer);
    }

    @Override
    public void register(String code, DefaultValuePrsCustomizer prsCustomizer)
    {
        registry.put(code, prsCustomizer);
    }

    @Override
    public DefaultValuePrsCustomizer get(String code)
    {
        return registry.get(code);
    }
}
