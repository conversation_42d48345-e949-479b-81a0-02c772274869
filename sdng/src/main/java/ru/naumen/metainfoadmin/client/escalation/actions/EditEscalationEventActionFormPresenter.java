package ru.naumen.metainfoadmin.client.escalation.actions;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithoutFolders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactory;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfoadmin.client.eventaction.form.EditEventActionFormPresenter;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormGinModule.EventAttributesTree;
import ru.naumen.metainfoadmin.client.eventaction.form.SelectFqnsPropertyFactory;
import ru.naumen.metainfoadmin.client.eventaction.form.attrtree.EventAttributesTreeFactoryContext;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 01.08.2012
 */
public class EditEscalationEventActionFormPresenter extends EditEventActionFormPresenter
{
    @Inject
    // @formatter:off
    public EditEscalationEventActionFormPresenter(
            @Named(EscalationActionsGinModule.ESCALATION_EVENT_ACTION_TREE) SelectFqnsPropertyFactory treeProvider,
            EventActionFormDisplay display,
            EventBus eventBus,
            DtoTreeFactory<Collection<DtObject>, EventAttributesTree, WithoutFolders,
            EventAttributesTreeFactoryContext> attrTreeFactory, SettingsSetOnFormCreator settingsSetOnFormCreator)
    // @formatter:on
    {
        super(treeProvider, display, eventBus, attrTreeFactory, settingsSetOnFormCreator);
    }

    @Override
    protected void initPropertiesValues(EventActionWithScript eventAction, AsyncCallback<Void> callback)
    {
        getEvent().setDisable();
        super.initPropertiesValues(eventAction, callback);
    }
}
