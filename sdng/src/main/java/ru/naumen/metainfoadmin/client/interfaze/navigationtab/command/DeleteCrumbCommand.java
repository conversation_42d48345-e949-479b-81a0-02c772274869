package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.Dialogs.DialogResult;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.DeleteBreadCrumbAction;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb.BreadCrumbHelper;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb.BreadCrumbMessages;

/**
 * Команда уделения хлебной крошки
 *
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public class DeleteCrumbCommand extends BaseCommandImpl<Crumb, DtoContainer<NavigationSettings>>
{
    public static final String ID = "deleteCrumbCommand";

    @Inject
    private BreadCrumbHelper helper;
    @Inject
    private Dialogs dialogs;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private BreadCrumbMessages messages;

    @Inject
    public DeleteCrumbCommand(@Assisted BreadCrumbCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<Crumb, DtoContainer<NavigationSettings>> param)
    {
        final BreadCrumbCommandParam p = (BreadCrumbCommandParam)prepareParam(param);
        dialogs.question(cmessages.confirmDelete(), question(p), new DialogCallback()
        {
            @Override
            public void handleSuccess(DialogResult result)
            {
                result.getWidget().hide();
                if (Dialogs.Buttons.YES.equals(result.getButtons()))
                {
                    yesDelete(p);
                }
                else
                {
                    cancelDelete(p);
                }
            }
        });
    }

    protected void cancelDelete(BreadCrumbCommandParam p)
    {
        param.getCallback().onSuccess(null);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    protected void yesDelete(BreadCrumbCommandParam p)
    {
        Crumb item = p.getValue();
        DeleteBreadCrumbAction action = new DeleteBreadCrumbAction();
        action.setCrumb(item);
        dispatch.execute(action, new SimpleResultCallbackDecorator<>(param.getCallback()));
    }

    private String question(BreadCrumbCommandParam p)
    {
        String title = helper.getCrumbTitle(p.getValue());
        return messages.deleteQuestion(title);
    }
}