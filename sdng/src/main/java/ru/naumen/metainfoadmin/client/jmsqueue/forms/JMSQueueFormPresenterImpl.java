package ru.naumen.metainfoadmin.client.jmsqueue.forms;

import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.JMSQueue.*;
import static ru.naumen.metainfoadmin.client.dynadmin.ContentUtils.MAX_CODE_LENGTH; //NOPMD
import static ru.naumen.metainfoadmin.client.jmsqueue.JMSQueueHelper.isUserJMSQueue;

import java.util.EnumSet;
import java.util.Set;

import com.google.gwt.event.dom.client.BlurEvent; //NOPMD
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.commons.shared.utils.StringUtilities; //NOPMD
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.validation.IntegerPositiveValidator;
import ru.naumen.core.client.validation.MetainfoKeyCodeValidatorForJMSQueue;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.ThreadCountJMSQueueValidator;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.eventaction.EventActionConstants;
import ru.naumen.metainfo.client.eventaction.EventActionsPresenterSettings;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfoadmin.client.jmsqueue.JMSQueueHelper;
import ru.naumen.metainfoadmin.client.jmsqueue.JMSQueueMessages;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * Базовая форма jms очереди
 * <AUTHOR>
 * @since 01.03.2021
 */
public abstract class JMSQueueFormPresenterImpl extends OkCancelPresenter<PropertyDialogDisplay>
        implements CallbackPresenter<DtObject, DtObject>
{
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT_AREA)
    protected Property<String> description;
    @Inject
    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    protected SelectListProperty<String, SelectItem> type;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> typeStr;
    @Inject
    @Named(PropertiesGinModule.INTEGER)
    protected Property<Long> threadCount;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> pubSubDomain;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> reportQueue;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> individualAck;

    protected Property<SelectItem> settingsSet;

    protected AsyncCallback<DtObject> refreshCallback;
    protected DtObject jmsQueue;

    @Inject
    protected Processor validation;
    @Inject
    protected DispatchAsync service;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    private MetainfoKeyCodeValidatorForJMSQueue metainfoKeyCodeValidator;
    @Inject
    private IntegerPositiveValidator integerPositiveValidator;
    @Inject
    private SecurityHelper security;
    @Inject
    private TransliterationService transliterationService;
    @Inject
    private ThreadCountJMSQueueValidator threadCountJMSQueueValidator;
    @Inject
    private EventActionsPresenterSettings eventActionsSettings;
    @Inject
    protected SharedSettingsClientService sharedSettings;
    @Inject
    protected EventActionConstants eventActionConstants;
    @Inject
    protected JMSQueueMessages messages;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    public JMSQueueFormPresenterImpl(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(@Nullable DtObject jmsQueue, AsyncCallback<DtObject> refreshCallback)
    {
        this.jmsQueue = jmsQueue;
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void refreshDisplay()
    {
    }

    protected void bindProperties()
    {
        title.setCaption(cmessages.title());
        title.setValidationMarker(true);
        title.setMaxLength(Constants.MAX_METAINFO_TITLE_LENGTH);
        code.setCaption(cmessages.code());
        code.setValidationMarker(true);
        code.setMaxLength(Constants.MAX_METAINFO_KEY_LENGTH);
        description.setCaption(cmessages.description());
        type.setCaption(messages.type());
        typeStr.setCaption(messages.type());
        type.setValidationMarker(true);
        threadCount.setCaption(messages.threadCount());
        threadCount.setValidationMarker(true);
        pubSubDomain.setCaption(messages.pubSubDomain());
        reportQueue.setCaption(messages.reportQueue());
        individualAck.setCaption(messages.individualAck());

        getDisplay().add(title);
        getDisplay().add(code);
        getDisplay().add(description);
        if (jmsQueue == null)
        {
            getDisplay().add(type);
        }
        else
        {
            getDisplay().add(typeStr);
        }
        getDisplay().add(threadCount);
        if (sharedSettings.isPubSubDomainEnabled() &&
            (jmsQueue == null || isUserJMSQueue(jmsQueue)))
        {
            getDisplay().add(pubSubDomain);
            getDisplay().add(reportQueue);
        }

        if (jmsQueue == null || isUserJMSQueue(jmsQueue))
        {
            getDisplay().add(individualAck);
        }
        if (jmsQueue == null)
        {
            validation.validate(code, metainfoKeyCodeValidator);
        }

        validation.validate(title, notEmptyValidator);
        validation.validate(code, notEmptyValidator);
        validation.validate(threadCount, integerPositiveValidator);
        if (needValidateMaxThreadCounts())
        {
            validation.validate(threadCount, threadCountJMSQueueValidator);
        }
        ensureDebugIds();
        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getDisplay(), null);
    }

    @Override
    protected void onBind()
    {
        bindProperties();
        super.onBind();

        if (jmsQueue != null)
        {
            code.setValue(jmsQueue.getUUID());
            title.setValue(jmsQueue.getTitle());
            description.setValue(jmsQueue.getProperty(DESCRIPTION));
            typeStr.setValue(jmsQueue.getProperty(TYPE));
            String threadCountValue = jmsQueue.getProperty(THREAD_COUNT);
            threadCount.setValue(Long.parseLong(threadCountValue));
            pubSubDomain.setValue(Boolean.valueOf(jmsQueue.getProperty(PUB_SUB_DOMAIN)));
            reportQueue.setValue(Boolean.valueOf(jmsQueue.getProperty(REPORT_QUEUE)));
            individualAck.setValue(Boolean.valueOf(jmsQueue.getProperty(INDIVIDUAL_ACK)));
            settingsSetOnFormCreator.setValue(settingsSet, jmsQueue.getProperty(SETTINGS_SET));
        }

        visibleAckField(jmsQueue != null && Boolean.parseBoolean(jmsQueue.getProperty(CAN_CHANGE_ACK)));

        initActionTypes();

        type.addValueChangeHandler(valueChangeEvent -> visibleAckField(isScriptQueue()));

        registerHandler(title.asWidget().addHandler(event ->
        {
            if (!StringUtilities.isEmpty(code.getValue()))
            {
                return;
            }
            String specialSymbols = security.hasVendorProfile() ? Constants.CODE_SPECIAL_CHARS_FOR_VENDOR_FOR_JMS_QUEUE
                    : Constants.CODE_SPECIAL_CHARS;
            code.setValue(
                    transliterationService.transliterateToCode(title.getValue(), MAX_CODE_LENGTH, specialSymbols));
        }, BlurEvent.getType()));

        pubSubDomain.addValueChangeHandler((ValueChangeEvent<Boolean> event) ->
                executeIfPubSubDomainChanged(event.getValue() == Boolean.TRUE));
        individualAck.addValueChangeHandler(
                (ValueChangeEvent<Boolean> event) -> visibleConfirmIndividualAckMessage(event.getValue()));
    }

    /**
     * Реакция на изменение параметра pubSubDomain
     */
    void executeIfPubSubDomainChanged(boolean pubSubDomain)
    {
        threadCount.setEnabled(!pubSubDomain);
        reportQueue.asWidget().setVisible(pubSubDomain);
        if (pubSubDomain)
        {
            threadCount.setValue(1L);
        }
        if (reportQueue.getValue() && !pubSubDomain)
        {
            reportQueue.setValue(false);
        }
    }

    protected Property<SelectItem> getType()
    {
        return type;
    }

    /**
     * Доступные типы (тип "Внешняя очередь" должен быть недоступен для выбора всегда)
     */
    private Set<ActionType> getActionTypes()
    {
        Set<ActionType> types = EnumSet.noneOf(ActionType.class);
        types.addAll(eventActionsSettings.getActionTypes());
        types.remove(ActionType.IntegrationEventAction);
        return types;
    }

    private void initActionTypes()
    {
        final Set<ActionType> availableActionTypes = getActionTypes();
        SingleSelectCellList<?> list = getType().getValueWidget();
        for (ActionType type : availableActionTypes)
        {
            final String actionTypeName = type.name();
            list.addItem(eventActionConstants.actionTypes().get(actionTypeName), actionTypeName);
        }
    }

    /**
     * нужна ли валидация количества потоков для обработки
     * Нужна для создания (т.к. создавать можно только пользовательские)
     * и при редактировании пользовательских
     */
    private boolean needValidateMaxThreadCounts()
    {
        return jmsQueue == null || JMSQueueHelper.isUserJMSQueue(jmsQueue);
    }

    /**
     * Управление видимостью поля "Индивидуальное подтверждение обработки сообщения"
     */
    private void visibleAckField(boolean visible)
    {
        individualAck.getCaptionWidget().asWidget().setVisible(visible);
        individualAck.asWidget().setVisible(visible);
        if (!visible)
        {
            individualAck.setValue(false, true);
        }
    }

    /**
     * Реакция на изменение параметра individualAck
     */
    private void visibleConfirmIndividualAckMessage(boolean individualAck)
    {
        if (individualAck)
        {
            getDisplay().addAttentionMessage(messages.individualAckMessage());
        }
        else
        {
            getDisplay().clearAttentionMessage();
        }
    }

    /**
     * Скриптовая ли очередь
     */
    private boolean isScriptQueue()
    {
        if (getType().getValue() == null)
        {
            return false;
        }
        return ActionType.valueOf(SelectListPropertyValueExtractor.getValue(getType())) == ActionType.ScriptEventAction;
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(description, "description");
        DebugIdBuilder.ensureDebugId(type, "type");
        DebugIdBuilder.ensureDebugId(threadCount, "threadCount");
        DebugIdBuilder.ensureDebugId(pubSubDomain, "pubSubDomain");
        DebugIdBuilder.ensureDebugId(reportQueue, "reportQueue");
        DebugIdBuilder.ensureDebugId(individualAck, "individualAck");
    }
}