package ru.naumen.metainfoadmin.client.attributes.forms.info;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.IHyperlink;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.script.ScriptPlace;

/**
 * Создает {@link Property} для отображения информации о
 * скриптах на модальной форме свойств атрибута
 *
 * <AUTHOR>
 * @since 3 авг. 2018 г.
 */
public class ScriptAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    private String scriptBodyCode;

    public void create(String code, String scriptBodyCode)
    {
        this.scriptBodyCode = scriptBodyCode;
        createInt(code);
    }

    @Override
    protected void createInt(String code)
    {
        ScriptDto script = propertyValues.getProperty(scriptBodyCode);
        if (null == script)
        {
            return;
        }
        createProperty(code, createHyperLink(script.getTitle(), script.getCode()));
    }

    private String createHyperLink(String title, String code)
    {
        String url = "#" + ScriptPlace.PLACE_PREFIX + ":" + code;
        IHyperlink link = new Hyperlink(title + " (" + code + ")",
                url);
        return link.toString();
    }
}
