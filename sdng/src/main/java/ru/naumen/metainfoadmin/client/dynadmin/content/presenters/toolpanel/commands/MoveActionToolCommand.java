package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.commands;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.client.HasContextCommandParamUtils;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.ObjectActionsMenuHolder;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.MoveToolEvent;

/**
 * <AUTHOR>
 * @since 01.03.17
 */
public abstract class MoveActionToolCommand extends BaseCommandImpl<ActionTool, ObjectActionsMenuHolder>
{
    public enum MoveDirection
    {
        UP("up"), DOWN("down");

        String code;

        private MoveDirection(String code)
        {
            this.code = code;
        }

        public String getCode()
        {
            return code;
        }
    }

    protected final MoveDirection direction;

    /**
     * @param param
     * @param direction направление перемещения -1 к началу, 1 к концу.  
     */
    protected MoveActionToolCommand(EditObjectActionsMenuCommandParam param, MoveDirection direction)
    {
        super(param);
        this.direction = direction;
    }

    @Override
    public void execute(CommandParam<ActionTool, ObjectActionsMenuHolder> param)
    {
        ((EditObjectActionsMenuCommandParam)this.param)
                .getContext()
                .getEventBus()
                .fireEvent(
                        new MoveToolEvent(param.getValue(), true, getNeighborTool(param.getValue(),
                                direction == MoveDirection.UP), direction == MoveDirection.UP));
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!HasContextCommandParamUtils.hasPermission(param, input, PermissionType.EDIT))
        {
            return false;
        }
        if (!(input instanceof ActionTool) || null == param)
        {
            return false;
        }
        ActionTool item = (ActionTool)input;
        int idx = item.getParent().getTools().indexOf(item);
        return idx >= 0 && isPossible(idx, (item.getParent()).getTools().size());
    }

    protected abstract boolean isPossible(int index, int count);

    private Tool getNeighborTool(Tool tool, boolean left)
    {
        int toolIndex = tool.getParent().getTools().indexOf(tool);
        if (left)
        {
            toolIndex--;
        }
        else
        {
            toolIndex++;
        }

        return tool.getParent().getTools().get(toolIndex);
    }
}