package ru.naumen.metainfoadmin.client.escalation.scheme.levels.commands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemeContext;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EditEscalationSchemeLevelForm;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinjector.EscalationSchemeLevelFormFactory;

/**
 * <AUTHOR>
 * @since 21.08.2012
 *
 */
public class EscalationSchemeLevelsEditCommand extends BaseCommandImpl<EscalationSchemeLevel, Void>
{
    @Inject
    private EscalationSchemeLevelFormFactory<EditEscalationSchemeLevelForm> formFactory;

    @Inject
    public EscalationSchemeLevelsEditCommand(@Assisted EscalationSchemeLevelsCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<EscalationSchemeLevel, Void> param)
    {
        EscalationSchemeContext context = ((EscalationSchemeLevelsCommandParam)param).getContext();
        EditEscalationSchemeLevelForm editForm = formFactory.create(context.getLocalEventBus(), context
                .getEscalationScheme().get());
        editForm.setLevel(param.getValue());
        editForm.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}