package ru.naumen.metainfoadmin.client.attributes.forms;

import static ru.naumen.core.client.utils.ScrollTimer.REPEAT_DELAY;

import java.util.List;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ErrorAndAttentionMessageHandler;
import ru.naumen.core.client.content.SafeContextualCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.utils.ScrollTimer;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.MetaClassPermissionsUpdatedEvent;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.client.events.MetainfoUpdatedEvent;
import ru.naumen.metainfo.client.events.WorkflowTemplateChangedEvent;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassResponse;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.DisabledAttributesChangedEvent;
import ru.naumen.metainfoadmin.client.attributes.forms.typeEmul.AttributeTypeFactory;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

/**
 * Общая часть обработчика нажатия на ОК
 * <AUTHOR>
 * @since 01.06.2012
 */
public abstract class AttributeFormApplyHandlerImpl<F extends ObjectForm> implements AttributeFormApplyHandler<F>
{
    public static class Callback extends SafeContextualCallback<GetMetaClassResponse>
    {
        private final Presenter formPresenter;
        private final AsyncCallback<MetaClass> callback;
        private final boolean fireEvent;
        private final EventBus eventBus;

        public Callback(EventBus eventBus, Context context, Presenter formPresenter, boolean fireEvent,
                AsyncCallback<MetaClass> callback)
        {
            super(context);
            this.eventBus = eventBus;
            this.formPresenter = formPresenter;
            this.callback = callback;
            this.fireEvent = fireEvent;
        }

        @Override
        protected void handleFailure(String msg, String details)
        {
            super.handleFailure(msg, details);
            ErrorAndAttentionMessageHandler msgHandler = getContext().getErrorAndAttentionMsgHandler();
            if (scrollTimer != null && msgHandler != null)
            {
                scrollTimer.scheduleRepeating(REPEAT_DELAY);
            }
        }

        @Override
        protected void handleSuccess(GetMetaClassResponse value)
        {
            if (null != value.getDisabledAttributes())
            {
                getContext().setContextProperty(Constants.MetaClassProperties.DISABLED_ATTRIBUTES,
                        value.getDisabledAttributes());
                if (fireEvent)
                {
                    eventBus.fireEvent(new DisabledAttributesChangedEvent(value.getMetaClass().getFqn(),
                            value.getDisabledAttributes()));
                }
            }
            if (fireEvent)
            {
                getContext().getEventBus().fireEvent(new MetainfoUpdatedEvent(value.getMetaClass()));
                eventBus.fireEvent(new WorkflowTemplateChangedEvent(value.getMetaClass()));
                eventBus.fireEvent(new MetaClassPermissionsUpdatedEvent(value.getMetaClass().getFqn(),
                        value.getAdminPermissions()));
            }
            formPresenter.unbind();
            callback.onSuccess(value.getMetaClass());
        }
    }

    private static ScrollTimer scrollTimer;
    @Inject
    protected MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    protected AttributeActionFactory<F> actionFactory;
    @Inject
    protected AttributeTypeFactory attrTypeFactory;

    @Inject
    protected EventBus eventBus;
    protected final IProperties contextProps;
    protected final IProperties propertyValues;
    protected final PropertyContainerPresenter propertyContainer;
    protected final Context context;
    protected final Presenter formPresenter;
    protected final AsyncCallback<MetaClass> callback;

    protected AttributeFormApplyHandlerImpl(IProperties contextProps, IProperties propertyValues,
            PropertyContainerPresenter propertyContainer, Context context, Presenter formPresenter,
            AsyncCallback<MetaClass> callback)
    {
        this.contextProps = contextProps;
        this.propertyValues = propertyValues;
        this.propertyContainer = propertyContainer;
        this.context = context;
        this.formPresenter = formPresenter;
        this.callback = callback;
        scrollTimer = new ScrollTimer();
    }

    protected void modifyAttribute()
    {
        if (!propertyContainer.validate())
        {
            return;
        }
        AttributeType attrType = attrTypeFactory.create(contextProps, propertyValues);
        List<DtObject> pendingTags = contextProps.getProperty(AttributeFormContextValues.PENDING_TAGS);
        metainfoModificationService.modifyAttribute(actionFactory.createAction(contextProps, propertyValues, attrType),
                pendingTags, new Callback(eventBus, context, formPresenter, true, callback));
    }
}