package ru.naumen.metainfoadmin.client.fastlink.settings;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.USER_INTERFACE;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSettingWithTitles;
import ru.naumen.metainfo.shared.fastlink.settings.dispatch.GetFastLinkSettingsAction;
import ru.naumen.metainfoadmin.client.AdminSingleTabPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;

/**
 * Презентер вкладки упоминаний объектов
 * <AUTHOR>
 * @since 06.03.2018
 */
public class ObjectMentionsPresenter extends AdminSingleTabPresenterBase<FastLinksSettingsListPlace>
{
    private final DispatchAsync dispatch;
    protected final AdminMetainfoServiceAsync metainfoService;
    private final ObjectMentionsListPresenter objectMentionsListPresenter;
    private final CommonFastLinksSettingsPresenter commonFastLinksSettingsPresenter;

    private Boolean isListRightsEnabled;
    private ArrayList<DtoContainer<FastLinkSettingWithTitles>> settingsForInit = new ArrayList<>();

    @Inject
    public ObjectMentionsPresenter(AdminTabDisplay display,
            EventBus eventBus,
            DispatchAsync dispatch,
            AdminMetainfoServiceAsync metainfoService,
            ObjectMentionsListPresenter objectMentionsListPresenter,
            CommonFastLinksSettingsPresenter commonFastLinksSettingsPresenter)
    {
        super(display, eventBus);
        this.dispatch = dispatch;
        this.metainfoService = metainfoService;
        this.objectMentionsListPresenter = objectMentionsListPresenter;
        this.commonFastLinksSettingsPresenter = commonFastLinksSettingsPresenter;
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        metainfoService.getSettings(new BasicCallback<Settings>(readyState)
        {
            @Override
            protected void handleSuccess(Settings value)
            {
                isListRightsEnabled = value.isListRightsEnabled();
            }
        });

        dispatch.execute(new GetFastLinkSettingsAction(),
                new BasicCallback<SimpleResult<List<DtoContainer<FastLinkSettingWithTitles>>>>(readyState)
                {
                    @Override
                    protected void handleSuccess(SimpleResult<List<DtoContainer<FastLinkSettingWithTitles>>> value)
                    {
                        settingsForInit.clear();
                        settingsForInit.addAll(value.get());
                    }
                });
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        objectMentionsListPresenter.init(settingsForInit, isListRightsEnabled);
        addContent(commonFastLinksSettingsPresenter, "commonFastLinkSettings");
        addContent(objectMentionsListPresenter, "objectMentionsListPresenter");
    }

    @Override
    protected String getTitle()
    {
        return messages.mentions();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return USER_INTERFACE;
    }
}
