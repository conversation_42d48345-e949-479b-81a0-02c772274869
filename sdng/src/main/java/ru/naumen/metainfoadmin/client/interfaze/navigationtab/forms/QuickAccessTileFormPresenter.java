package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.ObjectFormMessages;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector.PropertyContainerPresenterFactory;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncImpl;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.Constants.QuickAccessPanelSettings;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.MenuIconSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.MenuIconType;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelAreaSettingsDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.dispatch.AddQuickAccessTileAction;
import ru.naumen.core.shared.navigationsettings.quickaccess.dispatch.EditQuickAccessTileAction;
import ru.naumen.core.shared.navigationsettings.quickaccess.dispatch.IQuickAccessTileAction;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.client.TagActionExecutor;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.common.objectcommands.form.ObjectFormAfterBindHandler;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemContextValueCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.QuickAccessTilePropertyCode;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;

/**
 * Презентер формы добавления/редактирования плитки быстрого доступа
 * <AUTHOR>
 * @since 15.07.2020
 */
public class QuickAccessTileFormPresenter<F extends ObjectForm> extends OkCancelPresenter<PropertyFormDisplay>
{
    private static final List<String> PROPERTIES = Arrays.asList(
            MenuSettingsPropertyCode.PRESENTATION,
            MenuSettingsPropertyCode.ABBREVIATION,
            MenuSettingsPropertyCode.ICON,
            QuickAccessTilePropertyCode.MENU_ITEM,
            QuickAccessTilePropertyCode.HINT,
            MenuSettingsPropertyCode.PROFILES,
            QuickAccessTilePropertyCode.AREA,
            MenuSettingsPropertyCode.TAGS,
            MenuSettingsPropertyCode.SETTINGS_SET);

    private final NavigationSettingsMessages messages;
    private final PropertyContainerPresenterFactory containerFactory;
    private final ObjectFormMessages<F> formMessages;
    private final Processor validation;
    private final PropertyControllerFactory<QuickAccessTileDTO, ObjectFormEdit> propertyControllerFactory;
    private final TagActionExecutor tagActionExecutor;
    private final MetainfoUtils metainfoUtils;

    private final ObjectFormAfterBindHandler<F, QuickAccessTileDTO> afterBindHandler;
    private final IProperties contextProps = new MapProperties();
    protected final IProperties propertyValues = new MapProperties();

    private AsyncCallback<DtoContainer<NavigationSettings>> saveCallback;

    private NavigationSettings settings;
    private QuickAccessTileDTO tile;
    private LeftMenuItemSettingsDTO menuItem;
    private PropertyContainerPresenter propertyContainer;

    @Inject
    public QuickAccessTileFormPresenter(PropertyFormDisplay display, EventBus eventBus,
            NavigationSettingsMessages messages,
            PropertyContainerPresenterFactory containerFactory,
            ObjectFormMessages<F> formMessages,
            Processor validation,
            ObjectFormAfterBindHandler<F, QuickAccessTileDTO> afterBindHandler,
            PropertyControllerFactory<QuickAccessTileDTO, ObjectFormEdit> propertyControllerFactory,
            TagActionExecutor tagActionExecutor, MetainfoUtils metainfoUtils)
    {
        super(display, eventBus);
        this.messages = messages;
        this.containerFactory = containerFactory;
        this.formMessages = formMessages;
        this.validation = validation;
        this.afterBindHandler = afterBindHandler;
        this.propertyControllerFactory = propertyControllerFactory;
        this.tagActionExecutor = tagActionExecutor;
        this.metainfoUtils = metainfoUtils;
    }

    public void init(NavigationSettings settings, String menuItemCode, QuickAccessTileDTO tile,
            AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        this.settings = settings;
        this.tile = tile;
        saveCallback = callback;
        menuItem = settings.findLeftMenuItem(menuItemCode);
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        super.onApply();

        IQuickAccessTileAction action;
        if (tile == null)
        {
            tile = new QuickAccessTileDTO();
            tile.setCode(UUIDGenerator.get().nextUUID());
            action = new AddQuickAccessTileAction();
        }
        else
        {
            action = new EditQuickAccessTileAction();
            action.setTile(tile);
        }

        fillMenuItem();
        action.setTile(tile);
        action.setAreaCode(propertyValues.getProperty(QuickAccessTilePropertyCode.AREA));

        PropertyControllerSyncImpl controller = (PropertyControllerSyncImpl)propertyContainer.getContext()
                .getPropertyControllers().get(MenuSettingsPropertyCode.TAGS);
        tagActionExecutor.execute(action, ((TagsProperty)controller.property).getPendingTags(),
                new SimpleResultCallbackDecorator<DtoContainer<NavigationSettings>>(saveCallback)
                {
                    @Override
                    public void onSuccess(SimpleResult<DtoContainer<NavigationSettings>> result)
                    {
                        super.onSuccess(result);
                        eventBus.fireEvent(new MenuItemChangedEvent(result.get(), menuItem.getCode()));
                        getDisplay().destroy();
                    }
                });
    }

    private void fillPropertyValues()
    {
        contextProps.setProperty(MenuItemContextValueCode.SETTINGS, settings);
        if (tile == null)
        {
            propertyValues
                    .setProperty(QuickAccessTilePropertyCode.AREA, QuickAccessPanelSettings.ADMIN_AREA_CODE);
            if (menuItem == null)
            {
                propertyValues.setProperty(MenuSettingsPropertyCode.PRESENTATION, MenuIconType.ABBREVIATION.name());
                return;
            }
            propertyValues.setProperty(QuickAccessTilePropertyCode.MENU_ITEM, menuItem);
            propertyValues.setProperty(MenuSettingsPropertyCode.ABBREVIATION, menuItem.getIcon().getAbbreviation());
            propertyValues.setProperty(MenuSettingsPropertyCode.PRESENTATION, menuItem.getIcon().getType().name());
            propertyValues.setProperty(MenuSettingsPropertyCode.ICON,
                    new SimpleDtObject(menuItem.getIcon().getCode(), ""));
            propertyValues.setProperty(MenuSettingsPropertyCode.PROFILES, menuItem.getProfileCodes());
            propertyValues.setProperty(QuickAccessTilePropertyCode.HINT,
                    metainfoUtils.getLocalizedValue(menuItem.getTitle()));

            contextProps.setProperty(MenuItemContextValueCode.MENU_ITEM, menuItem);
            contextProps.setProperty(MenuItemContextValueCode.PARENT_PROFILES, menuItem.getResultProfiles());
            return;
        }

        propertyValues.setProperty(QuickAccessTilePropertyCode.MENU_ITEM, menuItem);
        propertyValues.setProperty(MenuSettingsPropertyCode.ABBREVIATION, tile.getIcon().getAbbreviation());
        propertyValues.setProperty(MenuSettingsPropertyCode.PRESENTATION, tile.getIcon().getType().name());
        propertyValues.setProperty(QuickAccessTilePropertyCode.HINT, metainfoUtils.getLocalizedValue(tile.getHint()));
        propertyValues.setProperty(MenuSettingsPropertyCode.ICON, new SimpleDtObject(tile.getIcon().getCode(), ""));
        propertyValues.setProperty(MenuSettingsPropertyCode.PROFILES, tile.getProfileCodes());
        Optional<String> areaCodeOptional = settings.getQuickAccessPanelSettings()
                .getAreas()
                .stream()
                .filter(area -> area.getTiles().contains(tile))
                .map(QuickAccessPanelAreaSettingsDTO::getCode)
                .findFirst();
        String areaCode = areaCodeOptional.orElse(QuickAccessPanelSettings.ADMIN_AREA_CODE);
        propertyValues.setProperty(QuickAccessTilePropertyCode.AREA, areaCode);
        propertyValues.setProperty(MenuSettingsPropertyCode.TAGS, tile.getTags());
        propertyValues.setProperty(MenuSettingsPropertyCode.SETTINGS_SET, tile.getSettingsSet());

        contextProps.setProperty(MenuItemContextValueCode.SETTINGS, settings);
        contextProps.setProperty(MenuItemContextValueCode.MENU_ITEM, menuItem);
        contextProps.setProperty(MenuItemContextValueCode.PARENT_PROFILES,
                menuItem == null ? null : menuItem.getResultProfiles());
    }

    @Override
    protected void onBind()
    {
        fillPropertyValues();
        setCaption(formMessages.formCaption(messages.quickAccessTileBy()));
        propertyContainer = containerFactory.createSimple(PROPERTIES, getDisplay(),
                propertyControllerFactory, contextProps, propertyValues, afterBindHandler, validation);
        propertyContainer.bind();

        super.onBind();
    }

    private void fillMenuItem()
    {
        menuItem = propertyValues.getProperty(QuickAccessTilePropertyCode.MENU_ITEM);
        tile.setMenuItemCode(menuItem.getCode());
        tile.setEnabled(menuItem.isEnabled());
        String hint = propertyValues.getProperty(QuickAccessTilePropertyCode.HINT, StringUtilities.EMPTY);
        metainfoUtils.setLocalizedValue(tile.getHint(), hint);
        MenuIconSettingsDTO iconDTO = new MenuIconSettingsDTO();
        iconDTO.setType(MenuIconType.valueOf(propertyValues.getProperty(MenuSettingsPropertyCode.PRESENTATION)));
        if (MenuIconType.ABBREVIATION.equals(iconDTO.getType()))
        {
            iconDTO.setAbbreviation(propertyValues.getProperty(MenuSettingsPropertyCode.ABBREVIATION));
        }
        else
        {
            iconDTO.setCode(propertyValues.<DtObject> getProperty(MenuSettingsPropertyCode.ICON).getUUID());
        }
        tile.setIcon(iconDTO);
        tile.setProfiles(getProfiles());
        tile.setTags(propertyValues.getProperty(MenuSettingsPropertyCode.TAGS));
        tile.setSettingsSet(propertyValues.getProperty(MenuSettingsPropertyCode.SETTINGS_SET));
    }

    private List<DtObject> getProfiles()
    {
        Collection<String> value = propertyValues.getProperty(MenuSettingsPropertyCode.PROFILES);
        if (value == null)
        {
            return new ArrayList<>();
        }
        // XXX Данный метод используется для того, чтобы данные, в итоге, отправить на сервер. Там нам нужен только
        // код. Так что надеюсь, что пустой title нигде не вылезет боком.
        return value.stream().map(code -> new SimpleDtObject(code, "")).collect(Collectors.toList());
    }
}