package ru.naumen.metainfoadmin.client.eventaction;

import java.util.List;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.ConcurrentCallbacks;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.common.ObjectService;
import ru.naumen.core.client.mvp.BasicNestedCallbacks;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.eventaction.EventActionWithRecipients;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;

/**
 * Базовый Инфо презентер карточки ДПС с получателями
 *
 * <AUTHOR>
 * @since 16.10.2015
 *
 */
public class EventActionWithRecipientsInfoPresenter<T extends EventActionWithRecipients, M extends Property<String>>
        extends EventActionInfoPresenterBase<T>
{
    public static final String RECIPIENT_DELIMITER = "; ";
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Property<String> to;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    Property<Boolean> excludeAuthor;
    @Inject
    protected M message;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    protected Property<String> format;
    @Inject
    @Named(PropertiesGinModule.SCRIPT_COMPONENT_VIEW)
    Property<ScriptDto> script;

    @Inject
    private ObjectService objectService;
    @Inject
    protected WidgetResources resources;
    @Inject
    protected MetainfoUtils metainfoUtils;

    @Inject
    public EventActionWithRecipientsInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void addActionProperties()
    {
        super.addActionProperties();
        addToAndExcludeAuthor();
        addActionPropertiesAfter();
    }

    protected void addToAndExcludeAuthor()
    {
        addProperty(messages.to(), to);
        addProperty(messages.excludeAuthor(), excludeAuthor);
    }

    protected void addActionPropertiesAfter()
    {
        addMessageWithFormat();
        addScript();
    }

    protected void addMessageWithFormat()
    {
        addProperty(messages.pushContent(), message);
        message.getValueWidget().asWidget().addStyleName(resources.all().richText());

        addProperty(messages.format(), format);
    }

    protected void addScript()
    {
        if (security.isAdmin())
        {
            addProperty(cmessages.script(), script);
        }
    }

    @Override
    protected void ensureDebugIds()
    {
        super.ensureDebugIds();
        DebugIdBuilder.ensureDebugId(to, "to");
        DebugIdBuilder.ensureDebugId(excludeAuthor, "excludeAuthor");
        DebugIdBuilder.ensureDebugId(message, "message");
        DebugIdBuilder.ensureDebugId(format, "format");
        DebugIdBuilder.ensureDebugId(script, "script-value");
    }

    @Override
    protected void setPropertiesValues()
    {
        super.setPropertiesValues();

        if (!isEventActionTypeDefined())
        {
            return;
        }

        final T action = getAction();
        format.setValue(action.isHtml() ? messages.htmlFormat() : messages.textFormat());
        message.setValue(metainfoUtils.getLocalizedValue(action.getMessage()));

        String scriptCode = action.getScript();
        ScriptDto scriptDto = eventAction.getScript(scriptCode);
        script.setValue(scriptDto);

        ConcurrentCallbacks callbacks = new BasicNestedCallbacks(getDisplay())
        {
            @SuppressWarnings("unchecked")
            @Override
            protected void handleSuccess(Object[] results)
            {
                final StringBuilder recipientString = new StringBuilder();

                List<DtObject> recipientObjects = (List<DtObject>)results[0];

                recipientString.append(formatters.title(recipientObjects, RECIPIENT_DELIMITER, true));

                List<Role> roles = (List<Role>)results[1];

                if (CollectionUtils.isNotEmpty(roles))
                {
                    recipientString.append(recipientString.length() > 0 ? RECIPIENT_DELIMITER : "")
                            .append(formatters.title(roles, RECIPIENT_DELIMITER, true));
                }
                to.setValue(recipientString.toString());
            }
        };
        DtoProperties properties = new DtoProperties().add(AbstractBO.TITLE);
        objectService.getObjects(CollectionUtils.asArrayList(action.getRecipients()), properties, false,
                callbacks.next());
        metainfoService.getSecurityRoles(action.getRoles(), callbacks.next());
        callbacks.commit();

        excludeAuthor.setValue(action.isExcludeAuthor());
    }

    /**
     * Признак того, что тип действия по событию определён
     */
    protected boolean isEventActionTypeDefined()
    {
        return null != eventAction && eventAction.getObject().getAction() != null;
    }
}
