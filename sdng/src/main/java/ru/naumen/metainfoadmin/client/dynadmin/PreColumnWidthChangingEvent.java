package ru.naumen.metainfoadmin.client.dynadmin;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие предваряющее изменения ширины колонки в режиме разметки.
 * Подписка на событие позволяет контентам подстроиться под желаемую ширину
 *
 * <AUTHOR>
 * @since 25.01.17
 *
 */
public class PreColumnWidthChangingEvent extends GwtEvent<PreColumnWidthChangingEventHandler>
{
    private static final Type<PreColumnWidthChangingEventHandler> TYPE = new Type<PreColumnWidthChangingEventHandler>();

    public static Type<PreColumnWidthChangingEventHandler> getType()
    {
        return TYPE;
    }

    private final int leftAreaWidth;
    private final int rightAreaWidth;

    public PreColumnWidthChangingEvent(int leftAreaWidth, int rightAreaWidth)
    {
        this.leftAreaWidth = leftAreaWidth;
        this.rightAreaWidth = rightAreaWidth;
    }

    @Override
    public Type<PreColumnWidthChangingEventHandler> getAssociatedType()
    {
        return TYPE;
    }

    public int getLeftWidthToSet()
    {
        return this.leftAreaWidth;
    }

    public int getRightWidthToSet()
    {
        return this.rightAreaWidth;
    }

    @Override
    protected void dispatch(PreColumnWidthChangingEventHandler handler)
    {
        handler.beforeChangeWidth(this);
    }
}
