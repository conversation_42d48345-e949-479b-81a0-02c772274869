package ru.naumen.metainfoadmin.client.embeddedapplications.command;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfoadmin.shared.Constants;

/**
 * <AUTHOR>
 * @since 07.07.2016
 *
 */
@Singleton
public class EmbeddedApplicationCommandFactoryInitializer
{
    @Inject
    public EmbeddedApplicationCommandFactoryInitializer(
            CommandFactory factory,
            CommandProvider<AddEmbeddedApplicationCommand, CommandParam<EmbeddedApplicationAdminSettingsDto,
                    EmbeddedApplicationAdminSettingsDto>> addProvider,
            CommandProvider<EditEmbeddedApplicationCommand, CommandParam<EmbeddedApplicationAdminSettingsDto,
                    EmbeddedApplicationAdminSettingsDto>> editProvider,
            CommandProvider<DeleteEmbeddedApplicationCommand,
                    CommandParam<EmbeddedApplicationAdminSettingsDto, Void>> deleteProvider,
            CommandProvider<EnableEmbeddedApplicationCommand, CommandParam<EmbeddedApplicationAdminSettingsDto,
                    EmbeddedApplicationAdminSettingsDto>> enableProvider,
            CommandProvider<DisableEmbeddedApplicationCommand, CommandParam<EmbeddedApplicationAdminSettingsDto,
                    EmbeddedApplicationAdminSettingsDto>> disableProvider,
            CommandProvider<ToggleEmbeddedApplicationCommand, CommandParam<EmbeddedApplicationAdminSettingsDto,
                    EmbeddedApplicationAdminSettingsDto>> toggleProvider)
    {
        // @formatter:off
        factory.register(Constants.EmbeddedAppCommandCode.ADD_APPLICATION, addProvider);
        factory.register(Constants.EmbeddedAppCommandCode.EDIT_APPLICATION, editProvider);
        factory.register(Constants.EmbeddedAppCommandCode.DELETE_APPLICATION, deleteProvider);
        factory.register(Constants.EmbeddedAppCommandCode.ENABLE_APPLICATION, enableProvider);
        factory.register(Constants.EmbeddedAppCommandCode.DISABLE_APPLICATION, disableProvider);  
        factory.register(Constants.EmbeddedAppCommandCode.TOGGLE_APPLICATION, toggleProvider);  
        // @formatter:on
    }
}
