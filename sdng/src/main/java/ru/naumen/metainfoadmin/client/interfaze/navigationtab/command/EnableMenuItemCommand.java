package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.SwitchNavigationMenuItemAction;

/**
 * Абстрактная команда включения элемента меню
 *
 * <AUTHOR>
 * @since 01 окт. 2013 г.
 */
public abstract class EnableMenuItemCommand<M extends IMenuItem> extends BaseCommandImpl<M,
        DtoContainer<NavigationSettings>>
{
    @Inject
    private DispatchAsync dispatch;

    public EnableMenuItemCommand(NavigationSettingsMenuItemAbstractCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<M, DtoContainer<NavigationSettings>> cparam)
    {
        NavigationSettingsMenuItemAbstractCommandParam p = (NavigationSettingsMenuItemAbstractCommandParam)prepareParam(
                cparam);
        M value = (M)p.getValue();
        setItemEnabled(p.getMenuItems(), value, true);
        SwitchNavigationMenuItemAction action = getAction();
        final Map<String, LinkedList<String>> menuItemPaths = p.getMenuItemPaths();
        action.setPathToMenuItem(menuItemPaths.get(value.getCode()));
        action.setEnabled(true);
        action.setMenuItemCode(value.getCode());
        dispatch.execute(action,
                new SimpleResultCallbackDecorator<DtoContainer<NavigationSettings>>(param.getCallback())
                {
                    @Override
                    public void onFailure(Throwable caught)
                    {
                        setItemEnabled(p.getMenuItems(), value, false);
                        super.onFailure(caught);
                    }
                });
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof IMenuItem))
        {
            return false;
        }
        M item = (M)input;
        return !item.isEnabled();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.SWITCH_ON;
    }

    /**
     * Устанавливает свойство Enabled для элемента меню из списка list.
     * @param list список элементов меню
     * @param item элемент меню, который нужно найти
     * @param enabled включить ли свойство
     * @return true, если найден предмет item и свойство установлено
     */
    private boolean setItemEnabled(Iterable<M> list, M item, boolean enabled)
    {
        for (M child : list)
        {
            if (item.equals(child))
            {
                child.setEnabled(enabled);
                return true;
            }
            else if (setItemEnabled((List<M>)child.getChildren(), item, enabled))
            {
                return true;
            }
        }
        return false;
    }

    protected abstract SwitchNavigationMenuItemAction getAction();
}
