package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

/**
 * <AUTHOR>
 * @since Mar 29, 2013
 */
public class NavigationSettingsCommandFactoryInitializer
{
    //@formatter:off
    @Inject
    public NavigationSettingsCommandFactoryInitializer(
            CommandFactory factory,
            CommandProvider<EditNavigationSettingsCommand, CommandParam<DtoContainer<NavigationSettings>,
                    DtoContainer<NavigationSettings>>> editNavigationSettingsCommandProvider,
            CommandProvider<MoveTopMenuItemUpCommand, NavigationSettingsTMCommandParam> moveTopMenuItemUpCommandProvider,
            CommandProvider<MoveLeftMenuItemUpCommand, NavigationSettingsLMCommandParam> moveLeftMenuItemUpCommandProvider,
            CommandProvider<MoveCrumbRelationAttributeUpComand, BreadCrumbRelationAttributeCommandParam> moveCrumbRelAttrUpCmd,
            CommandProvider<MoveTopMenuItemDownCommand, NavigationSettingsTMCommandParam> moveTopMenuItemDownCommandProvider,
            CommandProvider<MoveLeftMenuItemDownCommand, NavigationSettingsLMCommandParam> moveLeftMenuItemDownCommandProvider,
            CommandProvider<MoveCrumbRelationAttributeDownComand, BreadCrumbRelationAttributeCommandParam> moveCrumbRelAttrDownCmd,
            CommandProvider<AddTopMenuItemCommand, NavigationSettingsTMCommandParam> addTopMenuItemCommandProvider,
            CommandProvider<AddLeftMenuItemCommand, NavigationSettingsLMCommandParam> addLeftMenuItemCommandProvider,
            CommandProvider<AddCrumbCommand, BreadCrumbCommandParam> addCrumbCommandProvider,
            CommandProvider<EditCrumbCommand, BreadCrumbCommandParam> editCrumbCommandProvider,
            CommandProvider<DeleteCrumbCommand, BreadCrumbCommandParam> deleteCrumbCommandProvider,
            CommandProvider<EditTopMenuItemCommand, NavigationSettingsTMCommandParam> editTopMenuItemCommandProvider,
            CommandProvider<EditLeftMenuItemCommand, NavigationSettingsLMCommandParam> editLeftMenuItemCommandProvider,
            CommandProvider<DeleteTopMenuItemCommand, NavigationSettingsTMCommandParam> deleteTopMenuItemCommandProvider,
            CommandProvider<DeleteLeftMenuItemCommand, NavigationSettingsLMCommandParam> deleteLeftMenuItemCommandProvider,
            CommandProvider<EnableTopMenuItemCommand, NavigationSettingsTMCommandParam> enableTopMenuItemCommandProvider,
            CommandProvider<EnableLeftMenuItemCommand, NavigationSettingsLMCommandParam> enableLeftMenuItemCommandProvider,
            CommandProvider<DisableTopMenuItemCommand, NavigationSettingsTMCommandParam> disableTopMenuItemCommandProvider,
            CommandProvider<DisableLeftMenuItemCommand, NavigationSettingsLMCommandParam> disableLeftMenuItemCommandProvider,
            CommandProvider<PinLeftMenuItemCommand, NavigationSettingsLMCommandParam> pinLeftMenuItemCommandProvider,
            CommandProvider<UnpinLeftMenuItemCommand, NavigationSettingsLMCommandParam> unpinLeftMenuItemCommandProvider,

            CommandProvider<AddQuickAccessTileCommand, QuickAccessPanelTileCommandParam> addQuickAccessTileCommandProvider,
            CommandProvider<EditQuickAccessTileCommand, QuickAccessPanelTileCommandParam> editQuickAccessTileCommandProvider,
            CommandProvider<DeleteQuickTileCommand, QuickAccessPanelTileCommandParam> deleteQuickAccessTileCommandProvider,
            CommandProvider<MoveQuickAccessTileUpCommand, QuickAccessPanelTileCommandParam> moveUpQuickAccessTileCommandProvider,
            CommandProvider<MoveQuickAccessTileDownCommand, QuickAccessPanelTileCommandParam> moveDownQuickAccessTileCommandProvider,
            CommandProvider<EnableQuickAccessTileCommand, QuickAccessPanelTileCommandParam> enableQuickAccessTileCommandProvider,
            CommandProvider<DisableQuickAccessTileCommand, QuickAccessPanelTileCommandParam> disableQuickAccessTileCommandProvider)
    {
        //@formatter:on
        factory.register(EditNavigationSettingsCommand.ID, editNavigationSettingsCommandProvider);

        factory.register(MoveCrumbRelationAttributeUpComand.ID, moveCrumbRelAttrUpCmd);
        factory.register(MoveCrumbRelationAttributeDownComand.ID, moveCrumbRelAttrDownCmd);
        factory.register(AddCrumbCommand.ID, addCrumbCommandProvider);
        factory.register(EditCrumbCommand.ID, editCrumbCommandProvider);
        factory.register(DeleteCrumbCommand.ID, deleteCrumbCommandProvider);

        factory.register(MoveTopMenuItemUpCommand.ID, moveTopMenuItemUpCommandProvider);
        factory.register(MoveTopMenuItemDownCommand.ID, moveTopMenuItemDownCommandProvider);
        factory.register(AddTopMenuItemCommand.ID, addTopMenuItemCommandProvider);
        factory.register(EditTopMenuItemCommand.ID, editTopMenuItemCommandProvider);
        factory.register(DeleteTopMenuItemCommand.ID, deleteTopMenuItemCommandProvider);
        factory.register(EnableTopMenuItemCommand.ID, enableTopMenuItemCommandProvider);
        factory.register(DisableTopMenuItemCommand.ID, disableTopMenuItemCommandProvider);

        factory.register(MoveLeftMenuItemUpCommand.ID, moveLeftMenuItemUpCommandProvider);
        factory.register(MoveLeftMenuItemDownCommand.ID, moveLeftMenuItemDownCommandProvider);
        factory.register(AddLeftMenuItemCommand.ID, addLeftMenuItemCommandProvider);
        factory.register(EditLeftMenuItemCommand.ID, editLeftMenuItemCommandProvider);
        factory.register(DeleteLeftMenuItemCommand.ID, deleteLeftMenuItemCommandProvider);
        factory.register(EnableLeftMenuItemCommand.ID, enableLeftMenuItemCommandProvider);
        factory.register(DisableLeftMenuItemCommand.ID, disableLeftMenuItemCommandProvider);
        factory.register(PinLeftMenuItemCommand.ID, pinLeftMenuItemCommandProvider);
        factory.register(UnpinLeftMenuItemCommand.ID, unpinLeftMenuItemCommandProvider);

        factory.register(AddQuickAccessTileCommand.ID, addQuickAccessTileCommandProvider);
        factory.register(EditQuickAccessTileCommand.ID, editQuickAccessTileCommandProvider);
        factory.register(DeleteQuickTileCommand.ID, deleteQuickAccessTileCommandProvider);
        factory.register(MoveQuickAccessTileUpCommand.ID, moveUpQuickAccessTileCommandProvider);
        factory.register(MoveQuickAccessTileDownCommand.ID, moveDownQuickAccessTileCommandProvider);
        factory.register(EnableQuickAccessTileCommand.ID, enableQuickAccessTileCommandProvider);
        factory.register(DisableQuickAccessTileCommand.ID, disableQuickAccessTileCommandProvider);
    }
}
