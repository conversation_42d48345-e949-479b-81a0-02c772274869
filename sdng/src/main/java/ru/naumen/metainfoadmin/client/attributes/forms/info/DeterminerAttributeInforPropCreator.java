package ru.naumen.metainfoadmin.client.attributes.forms.info;

import static ru.naumen.core.shared.Constants.ValueMapCatalogItem.FQN;

import java.util.List;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.IHyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.ObjectService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.metainfoadmin.client.catalog.item.CatalogItemPlace;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Создает {@link Property} для отображения информации о таблице соответствий 
 * на модальной форме свойств атрибута  
 *
 * <AUTHOR>
 * @since 1 авг. 2018 г.
 */
public class DeterminerAttributeInforPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    private ObjectService objectService;

    @Override
    protected void createInt(String code)
    {
        String determinerCode = propertyValues.getProperty(AttributeFormPropertyCode.DETERMINER);
        if (StringUtilities.isEmptyTrim(determinerCode))
        {
            return;
        }
        createDeterminer(code, determinerCode);
    }

    private void createDeterminer(String code, String determinerCode)
    {
        DtoCriteria criteria = new DtoCriteria(FQN).addFilters(Filters.eq(CatalogItem.ITEM_CODE, determinerCode));
        objectService.getObjects(criteria, new BasicCallback<List<DtObject>>(rs)
        {
            @Override
            protected void handleSuccess(List<DtObject> objects)
            {
                if (objects.isEmpty())
                {
                    return;
                }
                DtObject catalogItem = objects.get(0);
                createProperty(code, createHyperLink(catalogItem.getUUID(), catalogItem.getTitle()));
            }
        });
    }

    private String createHyperLink(String uuid, String title)
    {
        String url = "#" + CatalogItemPlace.PLACE_PREFIX + ":" + uuid;
        IHyperlink link = new Hyperlink(title, url);
        return link.toString();
    }

}
