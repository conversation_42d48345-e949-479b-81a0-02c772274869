package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.defaultsettings;

import java.util.Objects;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ui.HierarchyGridDefaultViewSettings;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.DefaultPrsFormPresenterBase;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.RefreshDefaultPrsFormEvent;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.shared.dynadmin.HierarchyItemSettingsContext;
import ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridUtils;
import ru.naumen.objectlist.client.mode.active.ObjectListActive;

/**
 * Представление формы настройки вида по умолчанию для элемента структуры.
 * <AUTHOR>
 * @since 11.01.2021
 */
public class HierarchyDefaultSettingsFormPresenter
        extends DefaultPrsFormPresenterBase<HierarchyDefaultSettingsSortPresenter>
{
    private static final String HIERARCHY_WIDTH_COLUMNS_DEFAULT = "hierarchyWidthColumnsDefault";

    private HierarchyItemSettingsContext context;
    private AsyncCallback<HierarchyItemSettingsContext> refreshCallback;

    @Inject
    private DefaultAppearancePresenter defaultAppearancePresenter;
    @Inject
    private TitledHierarchyWidthColumnsDefaultPresenter hierarchyWidthColumnsDefaultPresenter;

    @Inject
    public HierarchyDefaultSettingsFormPresenter(FormDisplay display, EventBus eventBus,
            HierarchyDefaultSettingsSortPresenter sortPresenter)
    {
        super(display, eventBus, sortPresenter);
    }

    public void init(ObjectListUIContext parentContext, HierarchyItemSettingsContext context,
            AsyncCallback<HierarchyItemSettingsContext> refreshCallback)
    {
        this.parentContext = parentContext;
        this.context = context;
        setSettings(new HierarchyGridDefaultViewSettings(context.getDefaultSettings()));
        this.refreshCallback = refreshCallback;
    }

    @Override
    protected HierarchyGridDefaultViewSettings getSettings()
    {
        HierarchyGridDefaultViewSettings settings = (HierarchyGridDefaultViewSettings)super.getSettings();
        settings.setListSort(sortPresenter.getSort());
        settings.setInheritDefaultSortFromStructure(sortPresenter.isInherited());
        return settings;
    }

    @Override
    protected void initSortPresenter()
    {
        if (KendoGridUtils.isSortInherited(context.getDefaultSettings())
            && !context.getItemDefaultSort().getElements().isEmpty())
        {
            sortPresenter.init(ObjectUtils.clone(context.getItemDefaultSort()), parentContext);
            sortPresenter.setInherited(true);
        }
        else
        {
            sortPresenter.init(ObjectUtils.clone(context.getDefaultSettings().getListSort()), parentContext);
        }
        sortPresenter.setParentSort(context.getItemDefaultSort());
    }

    @Override
    public void onApply()
    {
        super.onApply();
        context.setDefaultSettings(getSettings());
        refreshCallback.onSuccess(null);
        unbind();
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        setCaption(messages.formItemCaption(context.getTitle()));
        createDefaultAppearancePresenter();
        createHierarchyWidthColumnsDefaultPresenter();
    }

    private void createDefaultAppearancePresenter()
    {
        getDisplay().addContent(defaultAppearancePresenter.getDisplay());
        defaultAppearancePresenter.init(getSettings(), context);
        defaultAppearancePresenter.bind();
        defaultAppearancePresenter.getDisplay().asWidget().ensureDebugId("appearance");
        defaultAppearancePresenter.getDisplay().asWidget().addStyleName(
                WidgetResources.INSTANCE.form().formBlock());
        defaultAppearancePresenter.refreshDisplay();
        registerChildPresenter(defaultAppearancePresenter);
    }

    private void createHierarchyWidthColumnsDefaultPresenter()
    {
        getDisplay().addContent(hierarchyWidthColumnsDefaultPresenter.getDisplay());
        hierarchyWidthColumnsDefaultPresenter.init(getSettings(), context);
        hierarchyWidthColumnsDefaultPresenter.bind();
        hierarchyWidthColumnsDefaultPresenter.getDisplay().asWidget().ensureDebugId(HIERARCHY_WIDTH_COLUMNS_DEFAULT);
        hierarchyWidthColumnsDefaultPresenter.getDisplay().asWidget().addStyleName(
                WidgetResources.INSTANCE.form().formBlock());
        registerChildPresenter(hierarchyWidthColumnsDefaultPresenter);
    }

    @Override
    protected void bindAttributesPresenter(ObjectListUIContext parentContext)
    {
        ObjectListUIContext parentContextForAttributes = new ObjectListUIContext(
                Objects.requireNonNull(parentContext.getParentContext()),
                parentContext.getRootContentInfo(), false, parentContext.getObjectList());
        parentContextForAttributes.setMode(new ObjectListActive());
        parentContextForAttributes.getMode()
                .setAttributeCodes(Lists.newArrayList(context.getAttributesInGroup().keySet()));
        parentContextForAttributes.getMode().setAttributes(context.getAttributesInGroup());

        super.bindAttributesPresenter(parentContextForAttributes);
    }

    @Override
    public void onRefreshDefaultPrsForm(RefreshDefaultPrsFormEvent event)
    {
        super.onRefreshDefaultPrsForm(event);
        hierarchyWidthColumnsDefaultPresenter.refreshDisplay();
    }
}
