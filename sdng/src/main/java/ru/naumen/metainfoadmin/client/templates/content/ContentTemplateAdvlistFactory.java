package ru.naumen.metainfoadmin.client.templates.content;

import java.util.ArrayList;

import jakarta.inject.Inject;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ContentTemplate;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.metainfoadmin.client.templates.content.command.ContentTemplateCommandCode;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;
import ru.naumen.objectlist.shared.AdvListMassOperationLightContext;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Фабрика сложных списков для шаблонов контентов.
 * <AUTHOR>
 * @since Mar 15, 2021
 */
public class ContentTemplateAdvlistFactory extends AdminCustomAdvlistFactoryBase
{
    private final ContentTemplateMessages messages;

    @Inject
    public ContentTemplateAdvlistFactory(ContentTemplateMessages messages)
    {
        this.messages = messages;
    }

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        ArrayList<ExtendedListActionCellContext> actionColumns = new ArrayList<>();
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.EDIT, ContentTemplateCommandCode.EDIT,
                AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT)));
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.DEL, ContentTemplateCommandCode.DELETE,
                AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE)));
        return actionColumns;
    }

    @Override
    protected AdvListMassOperationLightContext createAdvListMassOperationLightContext()
    {
        return massContextWithDelTool(ContentTemplateCommandCode.DELETE_MASS);
    }

    @Override
    protected ToolPanel createToolPanel(CustomList content)
    {
        ToolPanel toolPanel = super.createToolPanel(content);
        toolPanel.addToolBar(createAddButtonToolBar(messages.addTemplate()));
        return toolPanel;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return ContentTemplate.FQN;
    }
}
