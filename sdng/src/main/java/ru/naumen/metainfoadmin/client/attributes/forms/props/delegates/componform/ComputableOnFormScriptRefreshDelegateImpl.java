package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.componform;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE_ON_FORM;

import ru.naumen.metainfoadmin.client.widgets.properties.ScriptComponentEditProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 *
 * <AUTHOR>
 *
 * @param <F>
 */
public class ComputableOnFormScriptRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, ScriptDto, ScriptComponentEditProperty>
{

    @Override
    public void refreshProperty(PropertyContainerContext context, ScriptComponentEditProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean computeOnEdit = Boolean.TRUE.equals(context.getPropertyValues().getProperty(COMPUTABLE_ON_FORM));
        callback.onSuccess(computeOnEdit);
    }

}
