package ru.naumen.metainfoadmin.client.embeddedapplications;

import java.util.LinkedList;
import java.util.List;

import com.google.inject.Provider;
import com.google.inject.Singleton;

import jakarta.inject.Inject;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.NotFilter;
import ru.naumen.core.shared.filters.SimpleFilter;
import ru.naumen.core.shared.ui.toolbar.SimpleActionToolFactory;
import ru.naumen.core.shared.ui.toolbar.ToolFactoryInitializer;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.MetaClassLite_SnapshotObject;
import ru.naumen.metainfo.shared.elements.MetaClass_SnapshotObject;
import ru.naumen.metainfo.shared.embeddedapplication.Constants.Application.Attributes;
import ru.naumen.metainfo.shared.ui.ApplicationList;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.Constants.Titles;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.PagerPosition;
import ru.naumen.metainfo.shared.ui.PagingSettings;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.DefaultContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.extended.advlist.AdvListPresentationDisplayImpl;
import ru.naumen.objectlist.client.extended.advlist.AdvListPresenterImpl;

/**
 * Фабрика списка настройки встроенных приложений
 *
 * <AUTHOR>
 * @since 07.07.2016
 */
@Singleton
public class EmbeddedApplicationsAdvlistFactory
{
    public static MetaClass getApplicationMetaClass()
    {
        MetaClass_SnapshotObject metaclass = new MetaClass_SnapshotObject();
        metaclass.__init__fqn(ru.naumen.metainfo.shared.embeddedapplication.Constants.Application.FQN);
        return metaclass;
    }

    public static MetaClassLite getApplicationMetaclassLite()
    {
        MetaClassLite_SnapshotObject metaclass = new MetaClassLite_SnapshotObject();
        metaclass.__init__fqn(ru.naumen.metainfo.shared.embeddedapplication.Constants.Application.FQN);
        return metaclass;
    }

    @Inject
    private CommonMessages cmessages;

    @Inject
    private EmbeddedApplicationMessages messages;

    @Inject
    private ToolFactoryInitializer tfInitializer;

    @Inject
    private Provider<AdvListPresenterImpl<AdvListPresentationDisplayImpl, ApplicationList>> applicationsListPresenterProvider;

    @Inject
    private SharedSettingsClientService settingsService;

    /**
     * Создать список настроек приложений 
     */
    public ListPresenter<ApplicationList> create(String addApplicationCommandCode, List<AttributeFqn> attrs)
    {
        ListPresenter<ApplicationList> applicationsList = applicationsListPresenterProvider.get();

        MetaClass applicationMetaClass = getApplicationMetaClass();
        ApplicationList content = createContent(applicationMetaClass, addApplicationCommandCode, attrs);

        ObjectListUIContext context = new ObjectListUIContext(new DefaultContext(applicationMetaClass), null, false,
                null);

        applicationsList.init(content, context);

        return applicationsList;
    }

    private ApplicationList createContent(MetaClass metaclass, String addApplicationCommandCode,
            List<AttributeFqn> attrs)
    {
        ApplicationList content = new ApplicationList();
        content.setClazz(metaclass.getFqn());
        content.setPresentation(PresentationType.ADVLIST.getCode());
        content.setUuid("applications");

        if (!settingsService.isMobileJwtAuthEnabled())
        {
            List<IObjectFilter> filters = new LinkedList<>();
            SimpleDtObject value = new SimpleDtObject("CustomLoginFormApplication", null);
            filters.add(new NotFilter(new SimpleFilter<>(Attributes.APPLICATION_TYPE.toString(), value)));
            content.setFilters(filters);
        }

        PagingSettings pagingSettings = PagingSettings.getDefaultSettings();
        pagingSettings.setPosition(PagerPosition.BOTTOM);
        content.setPagingSettings(pagingSettings);

        content.getDisplayedAttrs().addAll(attrs);

        ToolPanel panel = new ToolPanel(content);
        content.setToolPanel(panel);

        ToolBar defaultToolBar = new ToolBar(panel);
        //FILTER
        Tool filtrationTool = tfInitializer
                .initFactory(new SimpleActionToolFactory(
                        Constants.SHOW_ADVLIST_FILTER, Titles.FILTRATION, Tool.PresentationType.DEFAULT,
                        cmessages.filtration()))
                .create().setToolBar(defaultToolBar);
        panel.getToolBars().add(defaultToolBar);

        Tool sortTool = tfInitializer
                .initFactory(new SimpleActionToolFactory(
                        Constants.SHOW_ADVLIST_SORT, Titles.SORT, Tool.PresentationType.DEFAULT, cmessages.sort()))
                .create().setToolBar(defaultToolBar);
        defaultToolBar.addTool(filtrationTool).addTool(sortTool);

        ToolBar specificToolBar = new ToolBar(panel);
        Tool addApplicationTool = tfInitializer
                .initFactory(new SimpleActionToolFactory(
                        addApplicationCommandCode, addApplicationCommandCode, Tool.PresentationType.DEFAULT,
                        messages.addApplication()))
                .create()
                .setToolBar(specificToolBar);
        specificToolBar.addTool(addApplicationTool);
        panel.getToolBars().add(specificToolBar);

        return content;
    }
}
