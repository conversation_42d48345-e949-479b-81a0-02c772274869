/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

import ru.naumen.metainfoadmin.client.catalog.item.CatalogItemsGinModule.CatalogItemSubPresenterFactory;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.VMapItemContentPresenter;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.ValueMapItemParameterizedGinModule;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.display.EscalationVMapItemContentCell;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.display.EscalationVMapItemContentColumnsProviderImpl;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.forms.EscalationVMapItemFormGinModule;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.toolbar.EscalationVMapItemContentButtonDescProvider;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.toolbar.EscalationVMapItemTargetPropertyContainer;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.toolbar.command.EscalationVMapItemCommandFactoryInitializer;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.toolbar.command.EscalationVMapRowSetConverter;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationVMapItemGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        install(new EscalationVMapItemFormGinModule());
        //@formatter:off
        ValueMapItemParameterizedGinModule<EscalationValueMapItemContext> ginModule = ValueMapItemParameterizedGinModule
                .create(EscalationValueMapItemContext.class);
        ginModule.forToolbar()
                    .setContentButtonDescProvider(EscalationVMapItemContentButtonDescProvider.class)
                    .forCommands()
                    .setVMapRowSetConverter(EscalationVMapRowSetConverter.class)
                    .setCommandFactoryInitializer(EscalationVMapItemCommandFactoryInitializer.class);
        ginModule.forRows()
                    .forRowProperties()
                    .setTargetPropertyContainer(EscalationVMapItemTargetPropertyContainer.class);
        ginModule.forDisplay()
                    .setContentCell(EscalationVMapItemContentCell.class)
                    .setContentColumnsProvider(EscalationVMapItemContentColumnsProviderImpl.class);
        ginModule.forCatalogItem()
                    .setSubPresenters(  new TypeLiteral<EscalationVMapItemInfoPresenter>(){}, 
                            new TypeLiteral<VMapItemContentPresenter<EscalationValueMapItemContext>>(){}, 
                            new TypeLiteral<CatalogItemSubPresenterFactory<EscalationValueMapItemContext>>(){})
                    .setInfoPropertyDescProvider(new TypeLiteral<EscalationVMapItemInfoPropertyDescProvider>(){});
        install(ginModule);
        //@formatter:on
    }
}