package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.quickform;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.METAINFO;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import java.util.HashSet;

import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.IHasI18nTitle.HasI18nTitleComparator;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.GetQuickFormsAction;
import ru.naumen.metainfo.shared.dispatch2.GetQuickFormsResponse;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.impl.AttributePropertyUtils;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления свойств "Форма быстрого добавления" и "Форма быстрого редактирования".
 * <AUTHOR>
 * @since Dec 22, 2017
 */
public abstract class QuickFormRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private MetainfoUtils metainfoUtils;

    private int asyncDataVersion = 0;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        IProperties propertyValues = context.getPropertyValues();
        IProperties contextValues = context.getContextValues();

        String typeCode = propertyValues.getProperty(ATTR_TYPE);
        MetaClass metaClass = contextValues.getProperty(METAINFO);
        String attrCode = propertyValues.getProperty(CODE);
        String targetClassValue = propertyValues.getProperty(TARGET_CLASS);
        ClassFqn targetClass = null == targetClassValue ? null : ClassFqn.parse(targetClassValue);
        if (null == targetClass)
        {
            MetaClass directLinkMetaClass = contextValues
                    .getProperty(AttributeFormContextValues.DIRECT_LINK_METAINFO);
            targetClass = null == directLinkMetaClass ? null : directLinkMetaClass.getFqn();
        }
        boolean isEditable = Boolean.TRUE.equals(propertyValues.getProperty(EDITABLE))
                             && !Boolean.TRUE.equals(propertyValues.getProperty(COMPUTABLE))
                             && !Boolean.TRUE.equals(propertyValues.getProperty(DETERMINABLE));
        boolean canHaveQuickForms = AttributePropertyUtils.canHaveQuickForms(typeCode, metaClass.getFqn(), attrCode,
                targetClass, isEditable);

        if (!canHaveQuickForms)
        {
            callback.onSuccess(false);
            return;
        }

        Set<ClassFqn> permittedTypes = getPermittedTypes(propertyValues);
        if (permittedTypes.isEmpty() && null != targetClass)
        {
            permittedTypes.add(targetClass);
        }

        property.getValueWidget().clear();
        callback.onSuccess(true);
        final int requestVersion = ++asyncDataVersion;
        dispatch.execute(new GetQuickFormsAction(permittedTypes), new BasicCallback<GetQuickFormsResponse>()
        {
            @Override
            protected void handleSuccess(GetQuickFormsResponse value)
            {
                if (requestVersion != asyncDataVersion)
                {
                    return;
                }
                property.getValueWidget().setHasEmptyOption(true);
                property.getValueWidget().clear();
                value.getQuickForms().values().stream().flatMap(List::stream)
                        .sorted(new HasI18nTitleComparator(metainfoUtils))
                        .forEach(form -> property.getValueWidget()
                                .addItem(metainfoUtils.getLocalizedValue(form.getTitle()), form.getUuid()));
                boolean valueExistsInList = value.getQuickForms().values().stream()
                        .flatMap(List::stream)
                        .anyMatch(form -> form.getUuid().equals(getCurrentValue(context)));
                if (getCurrentValue(context) == null)
                {
                    property.setValue(property.getValueWidget().getEmptyOption());
                }
                else if (valueExistsInList)
                {
                    property.trySetObjValue(getCurrentValue(context), false);
                }
            }
        });
    }

    protected abstract String getCurrentValue(PropertyContainerContext context);

    private Set<ClassFqn> getPermittedTypes(IProperties properties)
    {
        Collection<DtObject> permittedTypes = properties.getProperty(AttributeFormPropertyCode.PERMITTED_TYPES);
        return null == permittedTypes ? new HashSet<>()
                : permittedTypes.stream().map(DtObject::getMetaClass).collect(Collectors.toSet());
    }
}
