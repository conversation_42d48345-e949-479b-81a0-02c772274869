package ru.naumen.metainfoadmin.client.scheduler;

import java.util.Collection;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.HasEnabled;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.FactoryParam.ValueSource;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.impl.DialogsImpl;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.EnableDisplayCallback;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.client.events.SchedulerTaskUpdatedEvent;
import ru.naumen.metainfo.client.events.SchedulerTaskUpdatedHandler;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.scheduler.command.SchedulerCommandCode;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.client.tags.formatters.TagPropertyFormatter;

/**
 * Базовый класс для {@link Presenter}'ов параметров задач планировщика
 * oaleksandrova
 */
public class SchedulerTaskInfoPresenterBase<T extends SchedulerTask> extends BasicPresenter<InfoDisplay>
        implements SchedulerTaskUpdatedHandler
{
    static class SchedulerTaskCommandParam<V> extends CommandParam<V, V>
    {
        public SchedulerTaskCommandParam(ValueSource<V> vs, AsyncCallback<V> callback)
        {
            super(vs, callback);
        }
    }

    @Inject
    protected SchedulerTaskMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    protected TagsMessages tagsMessages;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    protected Property<String> title;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    protected Property<String> description;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    protected Property<String> code;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    protected Property<String> tags;
    protected Property<String> settingsSet;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;
    @Inject
    protected Formatters formatters;
    @Inject
    protected TagPropertyFormatter tagPropertyFormatter;
    @Inject
    private PlaceController placeController;
    @Inject
    private SchedulerPlace schedulerPlace;
    @Inject
    protected I18nUtil i18nUtil;

    protected DtoContainer<T> task;
    private final ToolBarDisplayMediator<DtoContainer<SchedulerTask>> toolBar;
    private ButtonPresenter<DtoContainer<SchedulerTask>> runButton;

    OnStartCallback<DtoContainer<SchedulerTask>> refreshCallback;

    @Inject
    ButtonFactory buttonFactory;

    @Inject
    public SchedulerTaskInfoPresenterBase(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    public void init(DtoContainer<T> schTask, OnStartCallback<DtoContainer<SchedulerTask>> refreshCallback)
    {
        this.task = schTask;
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void onSchedulerTaskUpdated(SchedulerTaskUpdatedEvent e)
    {
        this.task = e.getSchedulerTask();
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        setPropertiesValues();
        toolBar.refresh((DtoContainer<SchedulerTask>)task);
        boolean isEnabled = Boolean.TRUE.equals(task.getProperty(Constants.Tag.IS_ELEMENT_ENABLED));
        if (null != runButton)
        {
            ((HasEnabled)runButton.getDisplay()).setEnabled(isEnabled);
        }
        getDisplay().getAttention().setVisible(!isEnabled);
        getDisplay().getAttention()
                .setHTML(isEnabled ? StringUtilities.EMPTY : SafeHtmlUtils.htmlEscape( // NOPMD NSDPRD-28509 unsafe html
                        tagsMessages.disabledSchedulerTaskWarning()));
    }

    protected void addProperties()
    {
        getDisplay().add(title);
        title.setCaption(cmessages.title());
        getDisplay().add(code);
        code.setCaption(cmessages.code());
        getDisplay().add(description);
        description.setCaption(cmessages.description());
        getDisplay().add(tags);
        tags.setCaption(tagsMessages.tags());
        this.settingsSet = settingsSetOnFormCreator.createFieldOnCard(getDisplay());
    }

    protected void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(description, "description");
        DebugIdBuilder.ensureDebugId(tags, "tags");
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected void initToolBar()
    {
        OnStartCallback delCallback = new OnStartBasicCallback<Void>(getDisplay())
        {
            @Override
            protected void handleSuccess(Void value)
            {
                placeController.goTo(schedulerPlace);
            }
        };

        OnStartCallback runCallback = new EnableDisplayCallback<DtoContainer<SchedulerTask>>()
        {
            @Override
            protected void handleSuccess(DtoContainer<SchedulerTask> result)
            {
                task = (DtoContainer<T>)result;
                refreshCallback.onSuccess(result);
                super.handleSuccess(result);
            }

            @Override
            protected void handleFailure(String msg, @Nullable String details)
            {
                // Отобразим локализованную ошибку, с последующей разблокировкой кнопки "Выполнить сейчас"
                DialogCallback dialogCallback = new DialogCallback()
                {
                    @Override
                    protected void onOK(Dialog widget)
                    {
                        widget.hide();
                        ((HasEnabled)runButton.getDisplay()).setEnabled(true);
                    }
                };
                String formatted = new SafeHtmlBuilder().appendEscapedLines(msg).toSafeHtml().asString();
                new DialogsImpl().errorWithDetails(formatted, details, dialogCallback);
            }
        };

        ValueSource<DtoContainer<SchedulerTask>> vs = () -> (DtoContainer<SchedulerTask>)task;

        ValueSource<Collection<DtObject>> vsDel = () -> Lists.newArrayList(
                new SimpleDtObject(task.get().getCode(), metainfoUtils.getLocalizedValue(task.get().getTitle())));

        runButton = (ButtonPresenter<DtoContainer<SchedulerTask>>)buttonFactory.create(
                ButtonCode.RUN_IT_NOW, cmessages.runItNow(), SchedulerCommandCode.RUN_SCH_TASK,
                new SchedulerTaskCommandParam(vs, runCallback));
        toolBar.add(runButton);
        ((EnableDisplayCallback)runCallback).setDisplay((HasEnabled)runButton.getDisplay());

        ButtonPresenter<DtoContainer<SchedulerTask>> editBtn =
                (ButtonPresenter<DtoContainer<SchedulerTask>>)buttonFactory.create(ButtonCode.EDIT,
                        cmessages.edit(), SchedulerCommandCode.EDIT_SCH_TASK,
                        new SchedulerTaskCommandParam(vs, refreshCallback));
        editBtn.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        toolBar.add(editBtn);

        ButtonPresenter<DtoContainer<SchedulerTask>> delBtn =
                (ButtonPresenter<DtoContainer<SchedulerTask>>)buttonFactory.create(ButtonCode.DELETE,
                        cmessages.delete(), SchedulerCommandCode.DELETE_SCH_TASK,
                        new SchedulerTaskCommandParam(vsDel, delCallback));
        delBtn.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE));
        toolBar.add(delBtn);
        toolBar.bind();
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(SchedulerTaskUpdatedEvent.getType(), this));
        getDisplay().setTitle(cmessages.properties());
        initToolBar();

        addProperties();
        ensureDebugIds();
        refreshDisplay();
    }

    @Override
    protected void onUnbind()
    {
    }

    protected void setPropertiesValues()
    {
        code.setValue(task.get().getCodeOnForm());
        title.setValue(i18nUtil.getLocalizedTitle(task.get()));
        String desc = formatters.formatText(i18nUtil.getLocalizedDescription(task.get()));
        description.setValue(formatters.normalize(SafeHtmlUtils.fromTrustedString(desc)).asString());
        tags.setValue(tagPropertyFormatter.formatToAnchors(task.getProperty(Constants.Tag.ELEMENT_TAGS)).asString());
        String settingsSetProperty = task.getProperty(FakeMetaClassesConstants.SchedulerTask.SETTINGS_SET);
        if (StringUtilities.isNotEmpty(settingsSetProperty))
        {
            settingsSetOnFormCreator.setValueOnCardProperty(settingsSetProperty, settingsSet);
        }
    }
}
