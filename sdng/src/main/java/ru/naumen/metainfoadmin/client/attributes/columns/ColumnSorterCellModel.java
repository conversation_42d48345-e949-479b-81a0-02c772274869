package ru.naumen.metainfoadmin.client.attributes.columns;

import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.Style.Cursor;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.widgets.AttributeListCss;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;

/**
 * Существуют разные варианты отображения данных в ячейке заголовка колонки, по которой возможна сортировка. 
 * Причем в процессе реализации задачи требования меняются.
 * Нужно иметь возможность гибко управлять визуальным содержимым ячейки, а также обработкой событий на ее элементах.
 *
 * <AUTHOR>
 * @since 5 окт. 2018 г.
 *
 */
public class ColumnSorterCellModel
{
    /**
     * Элемент &lt;td&gt; заголовка колонки таблицы, по которой возможна сортировка
     */
    private Element headerCell;

    /**
     * Дочерний элемент в ячейке заголовка таблицы, обрабатывающий пользовательские события
     * (клик, по которому происходит сортировка или появляется всплывающее меню)
     */
    private Element controlElement;

    private AttributesMessages messages;

    public ColumnSorterCellModel(Element headerCell, Element controlElement, AttributesMessages messages)
    {
        this.headerCell = headerCell;
        this.controlElement = controlElement;
        this.messages = messages;
    }

    public void updateHeaderCell(boolean isActive, boolean isDirect, boolean isComplex, String sortingControlText)
    {
        headerCell.removeAllChildren();
        FlowPanel sortDirectionPlace = new FlowPanel();
        AttributeListCss css = AdminWidgetResources.INSTANCE.attributeList();
        sortDirectionPlace.addStyleName(css.sortDirection());
        sortDirectionPlace.addStyleName(css.headerTitle());
        if (isActive)
        {
            sortDirectionPlace.getElement().addClassName(isDirect ? css.sortAsc() : css.sortDesc());
            sortDirectionPlace.getElement().getStyle().setCursor(Cursor.POINTER);
            Event.sinkEvents(sortDirectionPlace.getElement(), Event.ONCLICK);
            Event.setEventListener(sortDirectionPlace.getElement(), Event.getEventListener(controlElement));
        }
        if (isComplex && isActive)
        {
            headerCell.appendChild(controlElement);
            Label sortBy = new Label(messages.sortBy());
            sortBy.addStyleName(css.headerTitle());
            sortBy.getElement().getStyle().setPaddingLeft(28, Unit.PX);
            headerCell.appendChild(sortBy.getElement());
            sortDirectionPlace.getElement().getStyle().setMarginLeft(4, Unit.PX);
            headerCell.appendChild(sortDirectionPlace.getElement());
            Label sorting = new Label(sortingControlText);
            sorting.addStyleName(css.headerTitle());
            headerCell.appendChild(sorting.getElement());
            sorting.getElement().getStyle().setCursor(Cursor.POINTER);
            Event.sinkEvents(sorting.getElement(), Event.ONCLICK);
            Event.setEventListener(sorting.getElement(), Event.getEventListener(controlElement));
        }
        else
        {
            if (!isComplex)
            {
                headerCell.appendChild(sortDirectionPlace.getElement());
            }
            headerCell.appendChild(controlElement);
        }
    }

}
