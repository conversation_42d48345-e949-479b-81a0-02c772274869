package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.base.Function;
import com.google.common.collect.Lists;

import java.util.HashSet;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Provider;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Фабрика представление атрибута "Группа атрибутов" пользовательской формы смены типа.
 *
 * <AUTHOR>
 * @since May 11, 2016
 */
public class AttributeCustomFormAttributeGroupWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private MetainfoServiceAsync metainfoService;
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(final PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        final SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();

        metainfoService.getFullMetaInfo(context.getPermittedTypeFqns(), new BasicCallback<List<MetaClass>>()
        {
            @Override
            protected void handleSuccess(List<MetaClass> metaClasses)
            {
                Set<String> addedCodes = new HashSet<>();
                List<AttributeGroup> attributeGroups = new ArrayList<>();
                for (MetaClass metaClass : metaClasses)
                {
                    for (AttributeGroup group : metaClass.getAttributeGroups())
                    {
                        if (!addedCodes.contains(group.getCode()))
                        {
                            attributeGroups.add(group);
                            addedCodes.add(group.getCode());
                        }
                    }
                }

                attributeGroups.sort(ITitled.IGNORE_CASE_COMPARATOR);
                widget.addItems(Lists.newArrayList(CollectionUtils.transform(attributeGroups,
                        new Function<AttributeGroup, DtObject>()
                        {
                            @Override
                            public DtObject apply(AttributeGroup group)
                            {
                                DtObject dto = new SimpleDtObject(group.getCode(), group.getTitle(), ClassFqn.parse(
                                        ""));
                                return dto;
                            }
                        })));

                callback.onSuccess(widget);
            }
        });
    }
}
