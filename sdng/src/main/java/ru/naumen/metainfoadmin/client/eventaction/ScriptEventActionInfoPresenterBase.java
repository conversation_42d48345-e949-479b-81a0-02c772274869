package ru.naumen.metainfoadmin.client.eventaction;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.shared.eventaction.ScriptEventActionBase;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;

/**
 * Базовый функционал презентера, содержащего свойство Скрипт
 *
 * <AUTHOR>
 * @since 05.07.22
 */
public abstract class ScriptEventActionInfoPresenterBase<T extends ScriptEventActionBase>
        extends EventActionInfoPresenterBase<T>
{
    @Inject
    @Named(PropertiesGinModule.SCRIPT_COMPONENT_VIEW)
    private Property<ScriptDto> script;

    protected ScriptEventActionInfoPresenterBase(InfoDisplay display,
            EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void addActionProperties()
    {
        super.addActionProperties();
        if (security.isAdmin())
        {
            addProperty(cmessages.script(), script);
        }
    }

    @Override
    protected void setPropertiesValues()
    {
        super.setPropertiesValues();

        String scriptCode = getAction().getScript();
        ScriptDto scriptDto = eventAction.getScript(scriptCode);
        script.setValue(scriptDto);
    }

    @Override
    void ensureDebugIds()
    {
        super.ensureDebugIds();
        DebugIdBuilder.ensureDebugId(script, "script-value");
    }
}
