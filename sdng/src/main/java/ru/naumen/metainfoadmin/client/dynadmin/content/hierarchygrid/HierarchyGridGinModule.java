package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay;
import ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.defaultsettings.*;
import ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.objectfilter.*;
import ru.naumen.metainfoadmin.shared.dynadmin.HierarchyItemSettingsContext;

/**
 * <AUTHOR>
 * @since Jan 14, 2020
 */
public class HierarchyGridGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        bind(HierarchyObjectFilterCommandFactoryInitializer.class).asEagerSingleton();
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, ResetHierarchyObjectFilterCommand.class)
                .build(new TypeLiteral<CommandProvider<ResetHierarchyObjectFilterCommand,
                        HierarchyObjectFilterCommandParam>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditHierarchyItemObjectFilterCommand.class)
                .build(new TypeLiteral<CommandProvider<EditHierarchyItemObjectFilterCommand,
                        HierarchyObjectFilterCommandParam>>()
                {
                }));
        bind(HierarchyDefaultSettingsCommandFactoryInitializer.class).asEagerSingleton();
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditHierarchyItemDefaultSettingsCommand.class)
                .build(new TypeLiteral<CommandProvider<EditHierarchyItemDefaultSettingsCommand,
                        HierarchyDefaultSettingsCommandParam>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, ResetHierarchyItemDefaultSettingsCommand.class)
                .build(new TypeLiteral<CommandProvider<ResetHierarchyItemDefaultSettingsCommand,
                        HierarchyDefaultSettingsCommandParam>>()
                {
                }));

        install(new GinFactoryModuleBuilder()
                .implement(EditHierarchyObjectFilterFormPresenter.class, EditHierarchyObjectFilterFormPresenter.class)
                .build(EditHierarchyObjectFilterFormPresenterFactory.class));
        install(new GinFactoryModuleBuilder()
                .implement(EditHierarchyDefaultSettingsFormPresenter.class,
                        EditHierarchyDefaultSettingsFormPresenter.class)
                .build(EditHierarchyDefaultSettingsFormPresenterFactory.class));
        install(new GinFactoryModuleBuilder()
                .implement(HierarchyObjectFilterItemsPresenter.class, HierarchyObjectFilterItemsPresenter.class)
                .build(HierarchyObjectFilterItemsPresenterFactory.class));
        install(new GinFactoryModuleBuilder()
                .implement(HierarchyDefaultSettingsItemsPresenter.class, HierarchyDefaultSettingsItemsPresenter.class)
                .build(HierarchyDefaultSettingsItemsPresenterFactory.class));
        bind(new TypeLiteral<TableDisplay<HierarchyItemSettingsContext>>()
        {
        })
                .to(new TypeLiteral<TableWithArrowsDisplay<HierarchyItemSettingsContext>>()
                {
                });
    }
}
