package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.metainfo.shared.Constants.MAX_CUSTOM_LINK;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import ru.naumen.core.client.validation.CustomLinkValidator;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.StringLengthValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindTextBoxFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.AttributeForFillByCurrentObjectBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.QuickFormBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.QuickFormBindDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.ActionRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.AttributeForFillByCurrentObjectRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.CustomFormRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.CustomFormRefreshDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.GoToCardAfterCreationRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.UseQuickAddFormRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.QuickFormVCHDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.QuickFormVCHDelegete;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.UseCustomFormVCHDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.UseCustomFormVCHDelegateFactory;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetBindDelegate;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetRefreshDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentCommonVCHDelegateFactory;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * <AUTHOR>
 * @since 14 окт. 2013 г.
 */
public class MenuItemPropertyControllerFactoryImpl extends PropertyControllerFactorySyncImpl<MenuItem, ObjectFormEdit>
{
    @Inject
    private PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxProperty> listBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<Collection<SelectItem>, MultiSelectBoxProperty> multiSelectPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, SingleSelectProperty<MenuItem>> selectMenuItemPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<Collection<DtObject>, Property<Collection<DtObject>>> addButtonPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, SingleSelectProperty<Reference>> referenceValuePropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, DtObjectSelectProperty> dtoSelectPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<Boolean, BooleanCheckBoxProperty> checkBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxWithEmptyPropertyFactory;
    @Inject
    private AttrChainMenuItemControllerFactory attrChainMenuItemControllerFactory;
    @Inject
    private CustomFormRefreshDelegateFactory customFormRefreshDelegateFactory;
    @Inject
    private QuickFormBindDelegateFactory quickFormBindDelegateFactory;
    @Inject
    private QuickFormVCHDelegateFactory quickFormVCHDelegateFactory;

    @Inject
    private TopMenuItemTypeBindDelegateImpl typeBindDelegate;
    @Inject
    private TopMenuItemParentBindDelegateImpl parentBindDelegate;
    @Inject
    private MenuItemTypeVCHDelegateImpl typeVCHDelegate;
    @Inject
    private TopMenuItemParentDelegateRefreshImpl parentRefreshDelegate;
    @Inject
    private AddButtonValuePropertyDelegateRefreshImpl addButtonRefreshDelegate;
    @Inject
    private ReferenceValuePropertyDelegateRefreshImpl referenceValueRefreshDelegate;
    @Inject
    private PropertyDelegateBindTextBoxFactory textBoxBindDelegateFactory;
    @Inject
    private UseCustomFormVCHDelegateFactory useCustomFormVCHDelegateFactory;
    @Inject
    private ReferenceValuePropertyVCHImpl referenceVCH;
    @Inject
    private CustomSystemLinkValuePropertyVCHImpl customSystemLinkVCH;
    @Inject
    private ReferenceValueTabBindDelegateImpl referenceTabBindDelegate;
    @Inject
    private ReferenceValueTabPropertyDelegateRefreshImpl referenceTabRefreshDelegate;
    @Inject
    private CustomLinkValuePropertyDelegateRefreshImpl customLinkValueRefreshDelegate;
    @Inject
    private CustomSystemLinkValuePropertyDelegateRefreshImpl customSystemLinkValueRefreshDelegate;
    @Inject
    private NewTabValuePropertyDelegateRefreshImpl newTabValueRefreshDelegate;
    @Inject
    private ActionRefreshDelegate actionRefreshDelegate;
    @Inject
    private UseQuickAddFormRefreshDelegate useQuickAddFormRefreshDelegate;
    @Inject
    private GoToCardAfterCreationRefreshDelegate goToCardAfterCreationRefreshDelegate;
    @Inject
    private AttributeForFillByCurrentObjectBindDelegate attributeForFillByCurrentObjectBindDelegate;
    @Inject
    private AttributeForFillByCurrentObjectRefreshDelegate attributeForFillByCurrentObjectRefreshDelegate;
    @Inject
    private ObjectClassReferenceBindDelegateImpl objectClassBindDelegate;
    @Inject
    private ObjectClassReferenceDelegateRefresh objectClassReferenceDelegateRefresh;
    @Inject
    private TypeOfCardReferenceDelegateRefreshImpl typeOfCardReferenceDelegateRefreshImpl;
    @Inject
    private UseAttrTitleReferenceDelegateRefreshImpl useAttrTitleReferenceDelegateRefreshImpl;
    @Inject
    private UseAttrTitleLinkValuePropertyVCHImpl useAttrTitleLinkValuePropertyVCH;
    @Inject
    private AttrForUseInTitleDelegateRefresh attrForUseInTitleDelegateRefresh;
    @Inject
    private TitleReferenceDelegateRefresh titleReferenceDelegateRefresh;
    @Inject
    private TypeOfCardReferenceValueBindDelegateImpl typeOfCardReferenceValueBindDelegate;
    @Inject
    private TypeOfCardReferenceVCHImpl typeOfCardReferenceVCH;
    @Inject
    private ObjectCasesReferenceDelegateRefreshImpl objectCasesReferenceDelegateRefresh;
    @Inject
    private ObjectCasesReferenceDelegateBindImpl objectCasesReferenceDelegateBind;
    @Inject
    private LinkToContentCommonVCHDelegateFactory commonVCHDelegateFactory;
    @Inject
    private UseAttrTitleReferenceBindDelegateImpl useAttrTitleReferenceBindDelegate;
    @Inject
    private AttrForUseInTitleDelegateBind attrForUseInTitleDelegateBind;
    @Inject
    private AttrChainReferenceDelegateRefreshImpl attrChainReferenceDelegateRefreshImpl;
    @Inject
    private ObjectCasesPropertyVCHImpl objectCasesPropertyVCH;
    @Inject
    private SettingsSetBindDelegate settingsSetBindDelegate;
    @Inject
    private SettingsSetRefreshDelegate settingsSetRefreshDelegate;

    private final Map<Validator<String>, String> titleValidators = new HashMap<>();
    private final Map<Validator<Collection<DtObject>>, String> addButtonValueValidators = new HashMap<>();
    private final Map<Validator<SelectItem>, String> referenceValueValidators = new HashMap<>();
    private final Map<Validator<String>, String> customLinkValidators = new HashMap<>();
    private final Map<Validator<RelationsAttrTreeObject>, String> notEmptyAttrObjectValidators = new HashMap<>();

    @Inject
    public void setUpValidators(NotEmptyValidator notEmptyValidator, StringLengthValidator stringLengthValidator,
            NotEmptyCollectionValidator<Collection<DtObject>> notEmptyCollectionValidator,
            NotNullValidator<SelectItem> notNullReferenceValidator, CustomLinkValidator customLinkValidator,
            NotNullValidator<RelationsAttrTreeObject> notNullAttrObjectValidator)
    {
        titleValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        titleValidators.put(stringLengthValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        addButtonValueValidators.put(notEmptyCollectionValidator,
                PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        referenceValueValidators.put(notNullReferenceValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        customLinkValidators.put(customLinkValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        notEmptyAttrObjectValidators.put(notNullAttrObjectValidator,
                PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }

    @Override
    protected void build()
    {
        PropertyDelegateBind<String, TextBoxProperty> titleBindDelegate = textBoxBindDelegateFactory
                .create(Constants.MAX_TITLE_LENGTH);
        PropertyDelegateBind<String, TextBoxProperty> linkBindDelegate = textBoxBindDelegateFactory
                .create(MAX_CUSTOM_LINK);
        UseCustomFormVCHDelegate useQuickAddFormVCHDelegate = useCustomFormVCHDelegateFactory.create(
                ToolFormPropertyCodes.USE_QUICK_ADD_FORM, ToolFormPropertyCodes.QUICK_ADD_FORM);
        CustomFormRefreshDelegate quickAddFormRefreshDelegate = customFormRefreshDelegateFactory.create(
                ToolFormPropertyCodes.USE_QUICK_ADD_FORM);
        QuickFormBindDelegate quickAddFormBindDelegate = quickFormBindDelegateFactory.create(
                ToolFormPropertyCodes.QUICK_ADD_FORM);
        QuickFormVCHDelegete quickAddFormVCHDelegete = quickFormVCHDelegateFactory.create(
                ToolFormPropertyCodes.QUICK_ADD_FORM);

        //@formatter:off
        register(MenuItemPropertyCode.TITLE, textBoxPropertyFactory)
            .setBindDelegate(titleBindDelegate)
            .setRefreshDelegate(titleReferenceDelegateRefresh)
            .setValidators(titleValidators);

        register(MenuItemPropertyCode.USE_ATTR_TITLE, checkBoxPropertyFactory)
            .setBindDelegate(useAttrTitleReferenceBindDelegate)
            .setRefreshDelegate(useAttrTitleReferenceDelegateRefreshImpl)
            .setVchDelegate(useAttrTitleLinkValuePropertyVCH);

        register(MenuItemPropertyCode.TYPE, listBoxPropertyFactory)
            .setBindDelegate(typeBindDelegate).setVchDelegate(typeVCHDelegate);

        register(MenuItemPropertyCode.PARENT, selectMenuItemPropertyFactory)
            .setBindDelegate(parentBindDelegate).setRefreshDelegate(parentRefreshDelegate);

        register(ReferenceCode.ATTRIBUTE_CHAIN, attrChainMenuItemControllerFactory)
            .setRefreshDelegate(attrChainReferenceDelegateRefreshImpl)
            .setVchDelegate(commonVCHDelegateFactory.create(
                Lists.newArrayList(
                        ReferenceCode.OBJECT_CLASS,
                        ReferenceCode.OBJECT_CASES,
                        ReferenceCode.REFERENCE_VALUE,
                        MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE
                ),new ArrayList<>()))
                .setValidators(notEmptyAttrObjectValidators);

        register(MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE, dtoSelectPropertyFactory)
                .setBindDelegate(attrForUseInTitleDelegateBind)
                .setRefreshDelegate(attrForUseInTitleDelegateRefresh)
                .setValidators(referenceValueValidators);

        register(MenuItemPropertyCode.TYPE_OF_CARD, dtoSelectPropertyFactory)
            .setBindDelegate(typeOfCardReferenceValueBindDelegate)
            .setRefreshDelegate(typeOfCardReferenceDelegateRefreshImpl)
            .setVchDelegate(typeOfCardReferenceVCH)
            .setValidators(referenceValueValidators);

        register(ReferenceCode.OBJECT_CLASS, textBoxPropertyFactory)
            .setBindDelegate(objectClassBindDelegate)
            .setRefreshDelegate(objectClassReferenceDelegateRefresh);

        register(ReferenceCode.OBJECT_CASES, multiSelectPropertyFactory)
            .setBindDelegate(objectCasesReferenceDelegateBind)
            .setRefreshDelegate(objectCasesReferenceDelegateRefresh)
            .setVchDelegate(objectCasesPropertyVCH);

        register(ReferenceCode.REFERENCE_VALUE, dtoSelectPropertyFactory)
            .setRefreshDelegate(referenceValueRefreshDelegate)
            .setVchDelegate(referenceVCH)
            .setValidators(referenceValueValidators);
        register(MenuItemPropertyCode.ADD_BUTTON_VALUE, addButtonPropertyFactory)
            .setRefreshDelegate(addButtonRefreshDelegate)
            .setValidators(addButtonValueValidators);
        register(MenuItemPropertyCode.REFERENCE_TAB_VALUE, referenceValuePropertyFactory)
            .setBindDelegate(referenceTabBindDelegate)
            .setRefreshDelegate(referenceTabRefreshDelegate);
        register(MenuItemPropertyCode.CUSTOM_LINK_VALUE, textBoxPropertyFactory)
                .setBindDelegate(linkBindDelegate)
                .setRefreshDelegate(customLinkValueRefreshDelegate)
                .setValidators(customLinkValidators);
        register(MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE, checkBoxPropertyFactory)
                .setVchDelegate(customSystemLinkVCH)
                .setRefreshDelegate(customSystemLinkValueRefreshDelegate);
        register(MenuItemPropertyCode.NEW_TAB_VALUE, checkBoxPropertyFactory)
                .setRefreshDelegate(newTabValueRefreshDelegate);
        register(ToolFormPropertyCodes.ACTION, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(actionRefreshDelegate);
        register(ToolFormPropertyCodes.USE_QUICK_ADD_FORM, checkBoxPropertyFactory)
                .setVchDelegate(useQuickAddFormVCHDelegate)
                .setRefreshDelegate(useQuickAddFormRefreshDelegate);
        register(ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION, checkBoxPropertyFactory)
                .setRefreshDelegate(goToCardAfterCreationRefreshDelegate);
        register(ToolFormPropertyCodes.QUICK_ADD_FORM, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(quickAddFormBindDelegate)
                .setRefreshDelegate(quickAddFormRefreshDelegate)
                .setVchDelegate(quickAddFormVCHDelegete);
        register(ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(attributeForFillByCurrentObjectBindDelegate)
                .setRefreshDelegate(attributeForFillByCurrentObjectRefreshDelegate);
        register(MenuSettingsPropertyCode.SETTINGS_SET, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(settingsSetBindDelegate)
                .setRefreshDelegate(settingsSetRefreshDelegate);
        //@formatter:on
    }
}
