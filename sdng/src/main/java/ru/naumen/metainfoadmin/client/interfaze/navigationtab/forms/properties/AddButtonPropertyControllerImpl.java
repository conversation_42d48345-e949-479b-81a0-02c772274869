package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.Collection;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerImpl;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.navigationsettings.AddButtonValue;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemContextValueCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.tree.FastCreateObjectsTreeProvider;

/**
 * <AUTHOR>
 * @since 16 окт. 2013 г.
 */
public class AddButtonPropertyControllerImpl extends
        PropertyControllerImpl<Collection<DtObject>, Property<Collection<DtObject>>>
{
    @Inject
    MetainfoServiceAsync metainfoService;

    @Inject
    FastCreateObjectsTreeProvider fastCreateObjectsTreeProvider;

    @Inject
    public AddButtonPropertyControllerImpl(@Assisted String code, @Assisted PropertyContainerContext context,
            @Assisted PropertyParametersDescriptor propertyParams,
            @Assisted
            PropertyDelegateDescriptor<Collection<DtObject>, Property<Collection<DtObject>>> propertyDelegates)
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    @Override
    protected void doBind(AsyncCallback<Void> callback)
    {
        property = fastCreateObjectsTreeProvider.get(Constants.AbstractBO.FQN, MetaClassFilters.isNotSingleton());

        if (propertyParams.isDisplayedOnBind())
        {
            addProperty();
        }
        bindProperty();
        fillValue();
        callback.onSuccess(null);
    }

    private void fillValue()
    {
        MenuItem menuItem = (MenuItem)context.getContextValues().getProperty(MenuItemContextValueCode.MENU_ITEM);
        if (menuItem == null || !MenuItemType.addButton.equals(menuItem.getType()))
        {
            return;
        }
        AddButtonValue addButtonValue = (AddButtonValue)menuItem.getValue();
        context.getPropertyValues().setProperty(MenuItemPropertyCode.ADD_BUTTON_VALUE, addButtonValue.getDtobjects());
    }
}
