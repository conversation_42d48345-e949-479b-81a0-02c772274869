package ru.naumen.metainfoadmin.client.escalation.scheme.levels;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.Header;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.name.Named;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.group.DataTableDnDGroupController;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.PresenterCommandEvent;
import ru.naumen.core.client.mvp.PresenterCommandEvent.PresenterCommandCode;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.dispatch2.MoveEscalationSchemeLevelAction;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemeContext;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.EscalationSchemeLevelsGinjector.EscalationSchemeLevelsDataProviderFactory;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.columns.EscalationSchemeLevelsColumnsGinModule;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.commands.EscalationSchemeLevelsCommandParam;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.commands.EscalationSchemeLevelsCommandsGinModule.EscalationSchemeLevelsCommandCode;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.commands.EscalationSchemeLevelsCommandsGinjector.EscalationSchemeLevelsAddCommandFactory;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * <AUTHOR>
 * @since 20.08.2012
 *
 */
public class EscalationSchemeLevelsPresenter extends BasicPresenter<EscalationSchemeLevelsDisplay>
{
    private class SchemeLevelsListDnDController extends DataTableDnDGroupController
    {
        public SchemeLevelsListDnDController()
        {
            super(null);
        }

        @Override
        public Element getRootElement()
        {
            return getDisplay().getTableContainer().getElement();
        }

        @Override
        public void move(int oldPosition, int newPosition, ReadyState readyState)
        {
            int diff = newPosition - oldPosition;
            EscalationSchemeLevel level = getDisplay().getTable().getVisibleItem(oldPosition);
            dispatch.execute(
                    new MoveEscalationSchemeLevelAction(context.getEscalationScheme().get().getCode(), level.getLevel(),
                            diff),
                    new BasicCallback<SimpleResult<Void>>()
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<Void> value)
                        {
                            context.getLocalEventBus().fireEvent(
                                    new PresenterCommandEvent(PresenterCommandCode.REFRESH));
                        }
                    });
        }

        @Override
        public boolean canDragStart(Element element)
        {
            int index = indexOf(element);
            List<EscalationSchemeLevel> levels = getDisplay().getTable().getVisibleItems();
            if (index < 0 || index >= levels.size())
            {
                return false;
            }
            EscalationSchemeLevel level = levels.get(index);
            return context.getEscalationScheme().get().getLevels().stream()
                    .filter(dto -> dto.get().equals(level))
                    .findFirst()
                    .map(AdminPermissionUtils::hasEditPermission)
                    .orElse(false);
        }
    }

    @Inject
    private EscalationSchemeLevelsMessages messages;
    @Inject
    @Named(EscalationSchemeLevelsColumnsGinModule.ESCALATION_SCHEME_LEVELS_COLUMNS)
    private ArrayList<Pair<? extends Header<?>, ? extends Column<EscalationSchemeLevel, ?>>> columnDescs;
    @Inject
    private EscalationSchemeLevelsDataProviderFactory dataProviderFactory;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private EscalationSchemeLevelsAddCommandFactory commandFactory;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private ObjectListColumnBuilder tableBuilder;

    private final EscalationSchemeContext context;
    private ListEditorDnDController dndController;

    private OnStartCallback<Void> refreshCallback = new SafeOnStartBasicCallback<Void>(getDisplay())
    {
        @Override
        protected void handleSuccess(Void value)
        {
            refreshDisplay();
        }
    };

    @Inject
    public EscalationSchemeLevelsPresenter(@Assisted EscalationSchemeContext context,
            EscalationSchemeLevelsDisplay display, EventBus eventBus,
            ListEditorDnDControllerFactory dndControllerFactory)
    {
        super(display, eventBus);
        this.context = context;
        dndController = dndControllerFactory.create(new SchemeLevelsListDnDController());
    }

    @Override
    public void refreshDisplay()
    {
        getDisplay().getTable().setVisibleRangeAndClearData(getDisplay().getTable().getVisibleRange(), true);
        Scheduler.get().scheduleDeferred(new ScheduledCommand()
        {
            @Override
            public void execute()
            {
                dndController.update();
            }
        });
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Override
    protected void onBind()
    {
        getDisplay().init(dataProviderFactory.create(context));
        ToolBarDisplayMediator toolBar = new ToolBarDisplayMediator<EscalationSchemeContext>(getDisplay().getToolBar());
        getDisplay().setCaption(messages.escalationLevels());
        toolBar.add(buttonFactory.create(ButtonCode.ADD_ELEMENT, messages.addLevel(), commandFactory.create(context)));
        toolBar.bind();
        Predicate<EscalationSchemeLevel> editPredicate = level -> AdminPermissionUtils.hasEditPermission(
                getPermissionsLevel(level));
        addActionColumn(EscalationSchemeLevelsCommandCode.MOVE_UP, editPredicate);
        addActionColumn(EscalationSchemeLevelsCommandCode.MOVE_DOWN, editPredicate);
        for (Pair<? extends Header<?>, ? extends Column<EscalationSchemeLevel, ?>> columnDesc : columnDescs)
        {
            getDisplay().getTable().addColumn(columnDesc.getRight(), columnDesc.getLeft());
        }
        addActionColumn(EscalationSchemeLevelsCommandCode.EDIT, editPredicate);
        addActionColumn(EscalationSchemeLevelsCommandCode.DELETE, level ->
                AdminPermissionUtils.hasDeletePermission(getPermissionsLevel(level)));
        refreshDisplay();
    }

    private void addActionColumn(String command, Predicate<EscalationSchemeLevel> visibilityCondition)
    {
        EscalationSchemeLevelsCommandParam param = new EscalationSchemeLevelsCommandParam(null, refreshCallback,
                context);
        tableBuilder.addActionColumn(getDisplay(), param, visibilityCondition, new String[] { command });
    }

    @Nullable
    public List<PermissionType> getPermissionsLevel(EscalationSchemeLevel level)
    {
        return context.getEscalationScheme().get().getLevels().stream()
                .filter(container -> container.get().equals(level))
                .map(AdminPermissionUtils::getPermissions)
                .filter(Objects::nonNull)
                .findAny().orElse(null);
    }
}
