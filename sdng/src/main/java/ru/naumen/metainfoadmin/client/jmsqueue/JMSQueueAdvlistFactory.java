package ru.naumen.metainfoadmin.client.jmsqueue;

import java.util.ArrayList;

import com.google.common.base.Predicates;
import com.google.inject.Inject;
import com.google.inject.Singleton;

import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.metainfoadmin.client.jmsqueue.comands.JMSQueueCommandCode;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Фабрика списка очередей JMS
 * <AUTHOR>
 * @since 15.02.2021
 */
@Singleton
public class JMSQueueAdvlistFactory extends AdminCustomAdvlistFactoryBase
{
    @Inject
    private JMSQueueMessages jmsQueueMessages;

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        ArrayList<ExtendedListActionCellContext> actionColumns = new ArrayList<>(2);
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.EDIT, JMSQueueCommandCode.EDIT,
                AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT)));
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.DEL, JMSQueueCommandCode.DELETE,
                Predicates.and(JMSQueueHelper::isCanDeleteJMSQueue,
                        AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE))));
        return actionColumns;
    }

    @Override
    protected ToolPanel createToolPanel(CustomList content)
    {
        final ToolPanel panel = super.createToolPanel(content);
        panel.addToolBar(createAddButtonToolBar(jmsQueueMessages.addingJMSQueue()));
        return panel;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return FakeMetaClassesConstants.JMSQueue.FQN;
    }
}