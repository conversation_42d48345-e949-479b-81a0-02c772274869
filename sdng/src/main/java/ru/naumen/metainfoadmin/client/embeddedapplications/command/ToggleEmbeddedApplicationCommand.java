package ru.naumen.metainfoadmin.client.embeddedapplications.command;

import java.util.Date;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.AnimatedFormBlocker;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.SwitchEmbeddedApplicationAction;
import ru.naumen.metainfo.shared.elements.ValidationProperties;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationUpdatedEvent;

/**
 * <AUTHOR>
 * @since 07.07.2016
 *
 */
public class ToggleEmbeddedApplicationCommand extends
        BaseCommandImpl<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto>
{
    @Inject
    private AnimatedFormBlocker formBlocker;

    @Inject
    private EventBus eventBus;

    @Inject
    private DispatchAsync dispatchAsync;

    @Inject
    public ToggleEmbeddedApplicationCommand(
            @Assisted CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto> param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto> param)
    {
        EmbeddedApplicationAdminSettingsDto application = param.getValue();
        boolean enabled = application.isOn();

        formBlocker.lockForm();
        final EmbeddedApplicationAdminSettingsDto application1 = application;
        final boolean newState = enabled;
        final AsyncCallback<EmbeddedApplicationAdminSettingsDto> callback = param.getCallback();

        SwitchEmbeddedApplicationAction action = new SwitchEmbeddedApplicationAction(
                application1.getCode(), !application1.isOn());
        action.setWithScripts(false);
        //noinspection Convert2Diamond
        dispatchAsync.execute(action,
                new SimpleResultCallbackDecorator<EmbeddedApplicationAdminSettingsDto>(callback)
                {
                    @Override
                    public void onFailure(Throwable caught)
                    {
                        if (StringUtilities.isNotEmpty(application1.getCode())
                            && application1.getEmbeddedApplicationType()
                               == EmbeddedApplicationType.InternalApplication && newState)
                        {
                            ValidationProperties validationProperties = application1.getValidationProperties();
                            if (validationProperties == null)
                            {
                                validationProperties = new ValidationProperties();
                            }
                            validationProperties.setLastConnectionDate(new Date());
                            validationProperties.setLastConnectionStatus(caught.getMessage());
                            application1.setValidationProperties(validationProperties);

                            eventBus.fireEvent(new EmbeddedApplicationUpdatedEvent(application1));
                        }
                        formBlocker.releaseForm();

                        super.onFailure(caught);
                    }

                    @Override
                    public void onSuccess(SimpleResult<EmbeddedApplicationAdminSettingsDto> response)
                    {
                        EmbeddedApplicationAdminSettingsDto responseApplication = response.get();
                        if (StringUtilities.isNotEmpty(application1.getCode()))
                        {
                            eventBus.fireEvent(new EmbeddedApplicationUpdatedEvent(responseApplication));
                        }
                        formBlocker.releaseForm();

                        callback.onSuccess(response.get());
                    }
                });
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.SWITCH_ON;
    }
}
