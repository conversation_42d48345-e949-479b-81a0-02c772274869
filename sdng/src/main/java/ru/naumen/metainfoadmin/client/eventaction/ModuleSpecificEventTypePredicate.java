package ru.naumen.metainfoadmin.client.eventaction;

import static com.google.common.base.Preconditions.checkNotNull;

import java.util.EnumMap;
import java.util.Map;
import java.util.function.Predicate;

import com.google.inject.Inject;
import com.google.inject.Singleton;

import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.metainfo.shared.eventaction.EventType;

/**
 * Предикат для фильтрации типов событий специфичных для загруженных модулей лицензий.
 * Можно в данном предикате зарегистрировать типа {@link EventType} и они будут видны в интерфейсе
 * только при загруженном модуле лицензии.
 *
 * <AUTHOR>
 * @since Apr 2, 2019
 */
@Singleton
public class ModuleSpecificEventTypePredicate implements Predicate<EventType>
{
    private Map<EventType, String> index = new EnumMap<>(EventType.class);

    @Inject
    private SharedSettingsClientService sharedSettings;

    @Override
    public boolean test(EventType eventType)
    {
        return !index.containsKey(eventType) || sharedSettings.isModuleInstalled(index.get(eventType));
    }

    /**
     * Регистрация специфических типов событий модуля
     * @param moduleName имя модуля
     * @param eventTypes специфичные для модуля типы событий
     */
    public void registerEventTypes(String moduleName, EventType... eventTypes)
    {
        checkNotNull(moduleName);
        for (EventType type : eventTypes)
        {
            index.put(type, moduleName);
        }
    }
}
