/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.separator;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.content.AbstractContentPresenter;
import ru.naumen.core.client.content.toolbar.DraggableToolDisplay;
import ru.naumen.core.client.content.toolbar.ToolDNDController;
import ru.naumen.core.client.content.toolbar.ToolDNDControllerFactory;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * <AUTHOR>
 * @since 26 мая 2015 г.
 *
 */
public class ToolSeparatorPresenter<D extends DraggableToolDisplay, R extends UIContext>
        extends AbstractContentPresenter<D, ToolSeparator, R>
{
    private ToolDNDController dndController;

    @Inject
    public ToolSeparatorPresenter(D display, EventBus eventBus)
    {
        super(display, eventBus, "separator");
    }

    @Override
    public void init(ToolSeparator content, R context)
    {
        super.init(content, context);
        dndController.init(content, context);
    }

    @Inject
    public void initDNDController(ToolDNDControllerFactory dndControllerFactory)
    {
        dndController = dndControllerFactory.create(getDisplay());
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        registerChildPresenter(dndController);
    }
}
