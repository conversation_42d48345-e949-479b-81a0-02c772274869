package ru.naumen.metainfoadmin.client.adminprofile;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.ADMINISTRATION_PROFILES;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminSingleTabAdvlistPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;

/**
 * Презентер страницы, на которой будет расположен список с профилями администрирования
 * <AUTHOR>
 * @since 16.01.2024
 */
public class AdminProfilesPresenter extends AdminSingleTabAdvlistPresenterBase<AdminProfilesPlace, DtObject>
{
    @Inject
    public AdminProfilesPresenter(AdminTabDisplay display,
            EventBus eventBus,
            AdminProfilesListPresenter advlistPresenter)
    {
        super(display, eventBus, advlistPresenter);
        display.asWidget().ensureDebugId("adminProfilesList");
    }

    @Override
    protected String getTitle()
    {
        return messages.adminProfiles();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return ADMINISTRATION_PROFILES;
    }
}