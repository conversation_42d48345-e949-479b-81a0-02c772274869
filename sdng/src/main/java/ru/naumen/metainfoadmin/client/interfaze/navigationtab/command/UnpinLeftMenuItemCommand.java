package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.dispatch.DeleteQuickAccessTileAction;

/**
 * Команда отпинивания(открепления с панели быстрого доступа) элемента левого меню
 * <AUTHOR>
 * @since 14.07.2020
 */
public class UnpinLeftMenuItemCommand extends BaseCommandImpl<LeftMenuItemSettingsDTO, DtoContainer<NavigationSettings>>
{
    public static final String ID = "unpinLeftMenuItem";
    @Inject
    private DispatchAsync dispatch;

    @Inject
    public UnpinLeftMenuItemCommand(@Assisted NavigationSettingsLMCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<LeftMenuItemSettingsDTO, DtoContainer<NavigationSettings>> cparam)
    {
        NavigationSettingsLMCommandParam p = (NavigationSettingsLMCommandParam)prepareParam(cparam);
        // TODO action
        DeleteQuickAccessTileAction action = new DeleteQuickAccessTileAction();
        getTileForItem(p, p.getValue()).ifPresent(action::setTile);
        dispatch.execute(action, new SimpleResultCallbackDecorator<>(param.getCallback()));
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof LeftMenuItemSettingsDTO))
        {
            return false;
        }
        LeftMenuItemSettingsDTO item = (LeftMenuItemSettingsDTO)input;
        NavigationSettingsLMCommandParam p = (NavigationSettingsLMCommandParam)prepareParam(param);
        return getTileForItem(p, item).isPresent();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.UNPIN;
    }

    protected static Optional<QuickAccessTileDTO> getTileForItem(NavigationSettingsLMCommandParam p,
            LeftMenuItemSettingsDTO item)
    {
        List<QuickAccessTileDTO> tiles = new ArrayList<>();
        p.getSettings().get().getQuickAccessPanelSettings().getAreas().
                forEach(area -> tiles.addAll(area.getTiles()));

        return tiles.stream().filter(tile -> tile.getMenuItemCode().equals(item.getCode())).findAny();
    }
}