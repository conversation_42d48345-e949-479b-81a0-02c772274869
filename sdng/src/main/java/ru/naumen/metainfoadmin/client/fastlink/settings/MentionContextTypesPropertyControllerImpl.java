package ru.naumen.metainfoadmin.client.fastlink.settings;

import java.util.Collection;
import java.util.HashSet;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassHierarchicalMetaClassTreeContext;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.tree.selection.HierarchicalMetaClassMultiSelectionModel;
import ru.naumen.core.client.tree.view.ITreeViewModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerImpl;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;

/**
 * <AUTHOR>
 * @since 06.03.2018
 */
public class MentionContextTypesPropertyControllerImpl
        extends
        PropertyControllerImpl<Collection<DtObject>, PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject,
                Collection<DtObject>, HierarchicalMetaClassMultiSelectionModel<DtObject>>>>
{
    @Inject
    private DtoMetaClassesTreeFactory<HierarchicalMetaClassMultiSelectionModel<DtObject>,
            DtoMetaClassHierarchicalMetaClassTreeContext> treeModelHelper;
    @Inject
    private PopupValueCellTreeFactory<DtObject, Collection<DtObject>,
            HierarchicalMetaClassMultiSelectionModel<DtObject>> treeFactory;
    @Inject
    private CommonHtmlTemplates templates;

    @Inject
    public MentionContextTypesPropertyControllerImpl(
            @Assisted String code,
            @Assisted PropertyContainerContext context,
            @Assisted PropertyParametersDescriptor propertyParams,
            @Assisted
            PropertyDelegateDescriptor<Collection<DtObject>, PropertyBase<Collection<DtObject>,
                    PopupValueCellTree<DtObject, Collection<DtObject>,
                            HierarchicalMetaClassMultiSelectionModel<DtObject>>>> propertyDelegates)
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    @Override
    protected void doBind(AsyncCallback<Void> callback)
    {
        ITreeViewModel<DtObject, HierarchicalMetaClassMultiSelectionModel<DtObject>> treeModel = treeModelHelper
                .createMetaClassTreeViewModel(Container.create(new DtoMetaClassHierarchicalMetaClassTreeContext(
                        AbstractBO.FQN, MetaClassFilters.isNotSystem(),
                        Lists.newArrayList(ru.naumen.core.shared.Constants.File.FQN), new HashSet<ClassFqn>())));
        PopupValueCellTree<DtObject, Collection<DtObject>, HierarchicalMetaClassMultiSelectionModel<DtObject>> tree =
                treeFactory
                        .create(treeModel);
        property = new PropertyBase<>(propertyParams.getCaption(), tree);
        property.setTemplates(templates);
        property.setValueFormatter(ITitled.TITLE_JOINER);

        if (propertyParams.isDisplayedOnBind())
        {
            addProperty();
        }
        bindProperty();
        PropertyDelegateBind<Collection<DtObject>, PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject,
                Collection<DtObject>, HierarchicalMetaClassMultiSelectionModel<DtObject>>>> bindDelegate =
                propertyDelegates
                        .getBindDelegate();
        if (bindDelegate != null)
        {
            bindDelegate.bindProperty(context, property, callback);
        }
        else
        {
            callback.onSuccess(null);
        }
    }

}
