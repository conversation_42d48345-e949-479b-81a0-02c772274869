package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import java.util.Objects;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.dom.client.Document;
import com.google.gwt.dom.client.Style.Cursor;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.ui.DockLayoutPanel;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.widgets.id.DebugIdBuilder;

/**
 * Панель, состоящая из областей с разделителями между ними
 *
 * <AUTHOR>
 * @since 20.01.17
 */
public class NauSplitLayoutPanel extends DockLayoutPanel //NOSONAR
{
    public class Splitter extends Widget
    {
        protected final Widget target;

        private int offset;
        private boolean mouseDown;
        private boolean mouseMoved;
        private boolean enabled;
        private ScheduledCommand layoutCommand;

        public Splitter(Widget target)
        {
            this.target = target;
            setElement(Document.get().createDivElement());
            sinkEvents(Event.ONMOUSEDOWN | Event.ONMOUSEUP | Event.ONMOUSEMOVE | Event.ONDBLCLICK);
        }

        @Override
        public void onBrowserEvent(Event event)
        {
            if (!enabled)
            {
                return;
            }

            switch (event.getTypeInt())
            {
                case Event.ONMOUSEDOWN:
                    mouseDown = true;
                    mouseMoved = false;
                    offset = event.getClientX() - getAbsoluteLeft();
                    Event.setCapture(getElement());
                    event.preventDefault();
                    getElement().getStyle().setCursor(Cursor.COL_RESIZE);
                    break;

                case Event.ONMOUSEUP:
                    mouseDown = false;
                    Event.releaseCapture(getElement());
                    event.preventDefault();
                    if (resizableColumnsOwner != null && mouseMoved)
                    {
                        resizableColumnsOwner.onColumnResizeCompleted();
                    }
                    getElement().getStyle().setCursor(Cursor.DEFAULT);
                    break;

                case Event.ONMOUSEMOVE:
                    getElement().getStyle().setCursor(Cursor.COL_RESIZE);
                    if (mouseDown)
                    {
                        int size = event.getClientX() - target.getAbsoluteLeft() - offset;
                        mouseMoved = true;

                        ((LayoutData)target.getLayoutData()).hidden = false;
                        if (size > -20)
                        {
                            setAssociatedWidgetSize(size);
                            if (resizableColumnsOwner != null)
                            {
                                resizableColumnsOwner.onColumnResize();
                            }
                        }
                        event.preventDefault();
                    }
                    break;
                default:
                    break;
            }
        }

        public void setEnabled(boolean enabled)
        {
            this.enabled = enabled;
        }

        public void setAssociatedWidgetSize(double size)
        {
            LayoutData layout = (LayoutData)target.getLayoutData();
            if (size == layout.size)
            {
                return;
            }

            layout.size = size;

            if (layoutCommand == null)
            {
                layoutCommand = new ScheduledCommand()
                {
                    @Override
                    public void execute()
                    {
                        layoutCommand = null;
                        forceLayout();
                    }
                };
                Scheduler.get().scheduleDeferred(layoutCommand);
            }
        }
    }

    private static final double DEFAULT_SPLITTER_SIZE = 4;

    private final double splitterSize;

    private ResizableColumnsOwner resizableColumnsOwner;

    public NauSplitLayoutPanel(ResizableColumnsOwner resizableColumnsOwner)
    {
        super(Unit.PX);
        this.resizableColumnsOwner = resizableColumnsOwner;
        this.splitterSize = DEFAULT_SPLITTER_SIZE;
    }

    public Splitter getAssociatedSplitter(Widget child)
    {
        int idx = getWidgetIndex(child);
        if (idx > -1 && idx < getWidgetCount() - 1)
        {
            Widget splitter = getWidget(idx + 1);
            return (Splitter)splitter;
        }
        return null;
    }

    @Override
    public void insert(Widget child, Direction direction, double size, Widget before)
    {
        super.insert(child, direction, size, before);
        if (direction != Direction.CENTER)
        {
            LayoutData layout = (LayoutData)child.getLayoutData();
            Splitter splitter = null;
            if (Objects.requireNonNull(getResolvedDirection(layout.direction)) == Direction.WEST)
            {
                splitter = new Splitter(child);
                DebugIdBuilder.ensureDebugId(splitter.asWidget(), "splitter");
            }
            else
            {
                assert false : "Unexpected direction";
            }

            super.insert(splitter, layout.direction, splitterSize, before);
        }
    }
}
