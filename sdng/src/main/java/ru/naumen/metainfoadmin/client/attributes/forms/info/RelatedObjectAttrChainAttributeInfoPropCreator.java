package ru.naumen.metainfoadmin.client.attributes.forms.info;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Создает {@link Property} для отображения информации о цепочки связи атрибуте связанного объекта
 *
 * <AUTHOR>
 * @since 14.09.18
 */
public class RelatedObjectAttrChainAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{

    @Override
    protected void createInt(String code)
    {
        if (!attribute.getType().isAttributeOfRelatedObject())
        {
            return;
        }

        String value = propertyValues.getProperty(AttributeFormPropertyCode.ATTR_CHAIN_VIEW);
        createProperty(code, value);
    }
}
