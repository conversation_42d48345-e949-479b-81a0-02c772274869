package ru.naumen.metainfoadmin.client.structuredobjectsviews.items.commands;

import static ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.MassEditToolPanelBlockPresenter.CAN_DISABLE_MASS_OPERATIONS;
import static ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.MassEditToolPanelBlockPresenter.USE_SYSTEM_SETTINGS;
import static ru.naumen.metainfoadmin.client.structuredobjectsviews.card.StructuredObjectsViewItemsForMassOperationsPresenter.NOT_USE_MASS_OPERATION_MAP;

import java.util.HashMap;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.StructuredObjectsViewItemClient;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.forms.StructuredObjectsViewItemFormMassOperationPresenter;

/**
 * Команда редактирования массовых операций для элемента структуры
 * <AUTHOR>
 * @since 16.12.2020
 */
public class StructuredObjectsViewItemMassOperationCommand extends BaseCommandImpl<StructuredObjectsViewItemClient,
        DtObject>
{
    private final Provider<StructuredObjectsViewItemFormMassOperationPresenter> editFormProvider;

    @Inject
    public StructuredObjectsViewItemMassOperationCommand(@Assisted StructuredObjectsViewItemsCommandParam param,
            Provider<StructuredObjectsViewItemFormMassOperationPresenter> editFormProvider)
    {
        super(param);
        this.editFormProvider = editFormProvider;
    }

    @Override
    public boolean isPossible(Object input)
    {
        DtObject structuredObjectsView = ((StructuredObjectsViewItemsCommandParam)param).getStructuredObjectsView();
        return Boolean.TRUE.equals(structuredObjectsView.getProperty(CAN_DISABLE_MASS_OPERATIONS));
    }

    @Override
    public void execute(CommandParam<StructuredObjectsViewItemClient, DtObject> param)
    {
        DtObject structuredObjectsView = ((StructuredObjectsViewItemsCommandParam)param).getStructuredObjectsView();
        if (!(structuredObjectsView.getProperty(USE_SYSTEM_SETTINGS, false)))
        {
            String code = param.getValue().getCode();
            HashMap<String, Boolean> notUseMassOperationMap = structuredObjectsView.getProperty(
                    NOT_USE_MASS_OPERATION_MAP);
            boolean notUseMassOperation =
                    notUseMassOperationMap != null && notUseMassOperationMap.containsKey(code) &&
                    notUseMassOperationMap.get(code);
            StructuredObjectsViewItemFormMassOperationPresenter formPresenter = editFormProvider.get();
            formPresenter.init(code, notUseMassOperation, param.getCallback());
            formPresenter.bind();
        }
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}