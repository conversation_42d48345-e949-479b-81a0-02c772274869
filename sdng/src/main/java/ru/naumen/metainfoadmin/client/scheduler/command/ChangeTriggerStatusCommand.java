package ru.naumen.metainfoadmin.client.scheduler.command;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.mailreader.client.InboundMailClientService;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;
import ru.naumen.mailreader.shared.task.ReceiveMailTask;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 * Команда изменения состояния правила выполнения задачи планировщика.
 * <AUTHOR>
 */
public abstract class ChangeTriggerStatusCommand extends BaseCommandImpl<DtoContainer<Trigger>, DtoContainer<Trigger>>
{
    @Inject
    protected MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    private InboundMailClientService inboundMailClientService;

    protected ChangeTriggerStatusCommand(CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>> param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>> param)
    {
        DtoContainer<Trigger> trigger = param.getValue();
        final AsyncCallback<DtoContainer<Trigger>> callback = param.getCallback();
        boolean newValue = !trigger.isEnabled();

        if (newValue)
        {
            inboundMailClientService.getTaskAndInboundMailServerConfig(
                    trigger.get().getSchTaskCode(),
                    new BasicCallback<Pair<ReceiveMailTask, InboundMailServerConfig>>()
                    {
                        @Override
                        public void onSuccess(
                                Pair<ReceiveMailTask, InboundMailServerConfig> taskAndInboundMailServerConfig)
                        {
                            showDialogMessageBeforeEnableTrigger(
                                    trigger,
                                    newValue,
                                    taskAndInboundMailServerConfig.left,
                                    taskAndInboundMailServerConfig.right);
                        }
                    }
            );
        }
        else
        {
            runCommand(trigger, newValue, callback);
        }
    }

    /**
     * Показать диалоговое окна подтверждения включения триггера обработки сервера входящей почты.
     * @param trigger триггер, который включается.
     * @param newValue новое состояние триггера, которое должно быть.
     * @param receiveMailTask планировщик задачи триггера.
     * @param inboundMailServerConfig параметры сервера входящей почты из планировщика задач.
     */
    private void showDialogMessageBeforeEnableTrigger(
            DtoContainer<Trigger> trigger,
            boolean newValue,
            @Nullable ReceiveMailTask receiveMailTask,
            @Nullable InboundMailServerConfig inboundMailServerConfig)
    {
        final AsyncCallback<DtoContainer<Trigger>> callback = param.getCallback();
        inboundMailClientService.showDialogMessageBeforeEnableTrigger(
                trigger,
                receiveMailTask,
                inboundMailServerConfig,
                () -> runCommand(trigger, newValue, callback));
    }

    /**
     * Запустить команду смены состояния триггера.
     * @param trigger триггер, у которого поменялось состояние.
     * @param newValue новое состояние триггера, которое должно быть.
     * @param callback обратный вызов после смены состояния.
     */
    private void runCommand(DtoContainer<Trigger> trigger, boolean newValue,
            AsyncCallback<DtoContainer<Trigger>> callback)
    {
        trigger.get().setEnabled(newValue);
        metainfoModificationService.saveTrigger(trigger,
                new CallbackDecorator<DtoContainer<Trigger>, DtoContainer<Trigger>>(callback)
                {
                    @Override
                    public void onSuccess(DtoContainer<Trigger> result)
                    {
                        callback.onSuccess(result);
                    }
                });
    }
}
