package ru.naumen.metainfoadmin.client.dynadmin.parenttree;

import java.util.List;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentTitles;

/**
 * DataSource для выбора родителя у контентов
 *
 * <AUTHOR>
 * @since 10.08.2021
 */
public class ContentParentDataSource extends ParentDataSource
{
    @Inject
    protected ContentParentDataSource(@Assisted ContentParentTreeFactoryContext context, ContentTitles contentTitles,
            MetainfoUtils metainfoUtils)
    {
        super(context, contentTitles, metainfoUtils);
    }

    @Override
    protected List<? extends Content> getFirstLevel(Content rootContent)
    {
        //Для карточки объекта первым уровнем будут "корневые вкладки"
        if (rootContent instanceof Window)
        {
            return ((Window)rootContent).getTabBar().getTab();
        }
        else
        {
            Form form = (Form)rootContent;
            //Т.к. корневой элемент формы можно выбрать, устанавливаем ему соответствующий контент
            treeContext.setRelation(treeContext.getRootDTO(), form);
            //Для формы первым уровнем будут размещенные на ней "панели вкладок"
            return form.getLayout().getContent()
                    .stream()
                    .filter(content -> content instanceof TabBar)
                    .collect(Collectors.toList());
        }
    }

    @Override
    protected void updateSelectable()
    {
        //Для контентов нельзя выбрать корневой элемент "карточка объекта" в качестве родителя
        if (treeContext.getRootContent() instanceof Window)
        {
            setSelectable(treeContext.getRootDTO(), false);
        }
        //Контент "панель вкладок" нельзя вложить в его дочерние вкладки
        Content editedContent = treeContext.getEditedContent();
        if (editedContent instanceof TabBar)
        {
            noSelectableRecursive((TabBar)editedContent);
        }
        //Для контентов нельзя выбирать "панель вкладок" в качестве родителя
        for (Entry<Content, DtObject> entry : treeContext.getContentToDtObjectMap().entrySet())
        {
            if (entry.getKey() instanceof TabBar)
            {
                setSelectable(entry.getValue(), false);
            }
        }
    }

    private void noSelectableRecursive(@Nullable TabBar content)
    {
        if (content == null)
        {
            return;
        }
        DtObject dtObject = treeContext.getDtObjectByContent(content);
        if (dtObject == null)
        {
            return;
        }
        setSelectable(dtObject, false);
        List<DtObject> child = treeContext.getItems().get(dtObject);
        if (!CollectionUtils.isEmpty(child))
        {
            child.forEach((item) ->
            {
                setSelectable(item, false);
                Content ch = treeContext.getContentByDtObject(item);
                ch.getChilds()
                        .stream()
                        .filter(children -> children instanceof TabBar)
                        .map(children -> (TabBar)children)
                        .forEach(this::noSelectableRecursive);
            });
        }
    }
}