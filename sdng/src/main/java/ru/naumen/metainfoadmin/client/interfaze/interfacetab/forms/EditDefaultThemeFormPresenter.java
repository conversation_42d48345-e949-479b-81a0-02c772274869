package ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms;

import java.util.Arrays;
import java.util.Objects;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.HTMLPanel;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.content.property.TitledPropertyGridDisplay;
import ru.naumen.core.client.content.property.TitledPropertyGridPresenter;
import ru.naumen.core.client.forms.DialogDisplay.DialogWidth;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataResponse;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.interfacesettings.InterfaceSettings;
import ru.naumen.core.shared.interfacesettings.InterfaceSettings.OperatorThemeApplicationPolicy;
import ru.naumen.core.shared.interfacesettings.dispatch.EditDefaultThemeAction;
import ru.naumen.core.shared.personalsettings.ThemeClient;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContextChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsResources;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.ThemeLogosAdmin;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.ThemeMessages;

/**
 * Форма редактирования настроек темы по умолчанию
 *
 * <AUTHOR>
 * @since 19.07.16
 */
public class EditDefaultThemeFormPresenter extends OkCancelPresenter<FormDisplay>
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private ThemeMessages messages;
    @Inject
    private Provider<TitledPropertyGridPresenter> propertyGridProvider;
    @Inject
    private ListBoxProperty operatorDefaultTheme;
    @Inject
    private ListBoxProperty operatorThemePolicy;
    private PropertyRegistration<SelectItem> operatorThemePolicyRegistration;
    @Inject
    private ListBoxProperty adminDefaultTheme;
    @Inject
    private ThemeLogosAdmin themeLogos;
    @Inject
    private CommonHtmlTemplates templates;

    private InterfaceSettingsContext context;
    private final InterfaceSettingsResources settingsResources;

    @Inject
    public EditDefaultThemeFormPresenter(FormDisplay display, EventBus eventBus,
            InterfaceSettingsResources settingsResources)
    {
        super(display, eventBus);
        this.settingsResources = settingsResources;
        settingsResources.css().ensureInjected();
    }

    public void init(InterfaceSettingsContext context)
    {
        this.context = context;
    }

    @Override
    public void onApply()
    {
        dispatch.execute(
                new EditDefaultThemeAction(SelectListPropertyValueExtractor.getValue(adminDefaultTheme),
                        SelectListPropertyValueExtractor.getValue(operatorDefaultTheme),
                        OperatorThemeApplicationPolicy
                                .valueOf(SelectListPropertyValueExtractor.getValue(operatorThemePolicy))),
                new BasicCallback<GetInterfaceTabDataResponse>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(GetInterfaceTabDataResponse response)
                    {
                        unbind();
                        if (response.getSettings().getThemeAdmin().equals(context.getSettings().getThemeAdmin()))
                        {
                            eventBus.fireEvent(new InterfaceSettingsContextChangedEvent(response));
                        }
                        else
                        {
                            // Изменение темы интерфейса настройки - перегружаем страницу
                            Window.Location.reload();
                        }
                    }
                });
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        setCaption(messages.defaultThemeEditForm());
        getDisplay().setDialogWidth(DialogWidth.W660);
        bindProperties();
        fillProperties();
    }

    private void addOperatorThemeChangeHandler(TitledPropertyGridPresenter grid)
    {
        registerHandler(operatorDefaultTheme.addValueChangeHandler(event ->
        {
            if (Objects.equals(SelectItemValueExtractor.<String> extract(event.getValue()),
                    context.getSettings().getThemeOperator()))
            {
                if (null != operatorThemePolicyRegistration)
                {
                    grid.setAttentionMessage(StringUtilities.EMPTY);
                    operatorThemePolicyRegistration.unregister();
                    operatorThemePolicyRegistration = null;
                }
            }
            else if (null == operatorThemePolicyRegistration)
            {
                operatorThemePolicyRegistration = grid.add(operatorThemePolicy);
                showPolicyDescription(grid);
            }
        }));
    }

    private void addPolicyChangeHandler(TitledPropertyGridPresenter grid)
    {
        registerHandler(operatorThemePolicy.addValueChangeHandler(event -> showPolicyDescription(grid)));
    }

    private void addThemeChangeHandler(TitledPropertyGridPresenter grid, PropertyRegistration<SelectItem> registration)
    {
        showThemeLogo(grid, registration);
        registerHandler(registration.getProperty().addValueChangeHandler(event -> showThemeLogo(grid, registration)));
    }

    private void bindProperties()
    {
        String formContentStyle = WidgetResources.INSTANCE.form().formBlock();

        TitledPropertyGridPresenter operatorGrid = propertyGridProvider.get();
        operatorGrid.setTitle(messages.forOperator());
        TitledPropertyGridDisplay operatorDisplay = operatorGrid.getDisplay();
        operatorDisplay.enableSplitMode();
        operatorDisplay.asWidget().addStyleName(formContentStyle);
        registerChildPresenter(operatorGrid, true);
        // TODO dzevako  переделать в NSDPRD-12486 Рефакторинг форм
        getDisplay().addContent(operatorGrid.getDisplay());

        TitledPropertyGridPresenter adminGrid = propertyGridProvider.get();
        adminGrid.setTitle(messages.forAdmin());
        TitledPropertyGridDisplay adminDisplay = adminGrid.getDisplay();
        adminDisplay.enableSplitMode();
        adminDisplay.asWidget().addStyleName(formContentStyle);
        registerChildPresenter(adminGrid, true);
        // TODO dzevako  переделать в NSDPRD-12486 Рефакторинг форм
        getDisplay().addContent(adminGrid.getDisplay());

        operatorDefaultTheme.asWidget().ensureDebugId("operatorDefaultTheme");
        operatorDefaultTheme.setCaption(messages.useAsDefaultTheme());
        PropertyRegistration<SelectItem> operatorThemeRegistration = operatorGrid.add(operatorDefaultTheme);

        operatorThemePolicy.asWidget().ensureDebugId("operatorThemePolicy");
        operatorThemePolicy.setCaption(messages.operatorThemePolicyCaption());
        Arrays.stream(InterfaceSettings.OperatorThemeApplicationPolicy.values()).forEach(item -> operatorThemePolicy
                .getValueWidget().addItem(messages.operatorThemePolicy(item.name()), item.name()));

        adminDefaultTheme.asWidget().ensureDebugId("adminDefaultTheme");
        adminDefaultTheme.setCaption(messages.useAsDefaultTheme());
        PropertyRegistration<SelectItem> adminThemeRegistration = adminGrid.add(adminDefaultTheme);

        for (ThemeClient theme : context.getThemes())
        {
            if (theme.isDisplayedInAdminMode())
            {
                adminDefaultTheme.getValueWidget().addItem(theme.getTitle(), theme.getCode());
            }
            if (theme.isEnabled())
            {
                operatorDefaultTheme.getValueWidget().addItem(theme.getTitle(), theme.getCode());
            }
        }

        addOperatorThemeChangeHandler(operatorGrid);
        addThemeChangeHandler(operatorGrid, operatorThemeRegistration);
        addThemeChangeHandler(adminGrid, adminThemeRegistration);
        addPolicyChangeHandler(operatorGrid);
    }

    private void fillProperties()
    {
        adminDefaultTheme.trySetObjValue(context.getSettings().getThemeAdmin(), true);
        operatorDefaultTheme.trySetObjValue(context.getSettings().getThemeOperator(), true);
        operatorThemePolicy.trySetObjValue(context.getSettings().getThemeOperatorApplicationPolicy().name(), true);
    }

    private void showPolicyDescription(TitledPropertyGridPresenter grid)
    {
        if (null == operatorThemePolicyRegistration)
        {
            grid.setAttentionMessage(StringUtilities.EMPTY);
            return;
        }
        String oldThemeCode = context.getSettings().getThemeOperator();
        String oldThemeTitle = context.getThemes().stream().filter(theme -> oldThemeCode.equals(theme.getCode()))
                .map(ThemeClient::getTitle).findFirst().orElse(StringUtilities.EMPTY);
        String value = SelectListPropertyValueExtractor.getValue(operatorThemePolicyRegistration.getProperty());
        if (null == value)
        {
            value = context.getSettings().getThemeOperatorApplicationPolicy().name();
        }
        String description = messages.operatorThemePolicyDescription(value, oldThemeTitle);
        grid.setAttentionMessage(description);
    }

    private void showThemeLogo(TitledPropertyGridPresenter grid, PropertyRegistration<SelectItem> registration)
    {
        String value = StringUtilities
                .toNonNullString(SelectListPropertyValueExtractor.getValue(registration.getProperty()));

        SafeHtml template = null;
        if (context.getSettings().getThemeLogoSettings().get(value) == null
            || context.getSettings().getThemeLogoSettings().get(value).isLogoStandart())
        {
            template = templates.imgAsTableCellWithSizeLimit(themeLogos.getStandartLoginFormLogo(value),
                    settingsResources.css().tableImageDiv(), StringUtilities.EMPTY);
        }
        else
        {
            template = templates.imgAsTableCellWithSizeLimit(
                    context.getSettings().getThemeLogoSettings().get(value).getLogoUuid(),
                    settingsResources.css().tableImageDiv(), StringUtilities.EMPTY);
        }

        HTMLPanel content = new HTMLPanel(template);
        content.addStyleName(settingsResources.css().systemLogo());
        grid.getDisplay().setSplitContent(registration, content);
    }
}
