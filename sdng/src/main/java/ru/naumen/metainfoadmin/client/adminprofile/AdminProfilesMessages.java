package ru.naumen.metainfoadmin.client.adminprofile;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * Константы локализации, относящиеся к профилям администрирования.
 * <AUTHOR>
 * @since 17.01.2024
 */
@DefaultLocale("ru")
public interface AdminProfilesMessages extends Messages
{
    @Description("Добавить профиль")
    String addAdminProfile();

    @Description("Добавление профиля администрирования")
    String addingAdminProfile();

    @Description("Матрица маркеров доступа")
    String adminAccessMarkerMatrix();

    @Description("Матрица настроек администрирования")
    String adminSettingsMatrix();

    @Description("к профилям")
    String backToProfiles();

    @Description("Вы действительно хотите удалить профиль администрирования?")
    String confirmDeleteAdminProfile();

    @Description("Вы действительно хотите удалить выбранные профили администрирования?")
    String confirmMassDeleteAdminProfiles();

    @Description("Редактирование профиля администрирования")
    String editingAdminProfile();

    @Description("удалить")
    String deleteMass();

    @Description("Пример настройки в матрице настроек администрирования (удалить при реализации настроек)")
    String setupExample();
}
