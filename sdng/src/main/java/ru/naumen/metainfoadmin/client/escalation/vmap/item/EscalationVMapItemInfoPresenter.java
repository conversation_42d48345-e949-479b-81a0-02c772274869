/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogCommandCode;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogItemCommandParam;
import ru.naumen.metainfoadmin.client.catalog.item.CatalogItemInfoDisplay;
import ru.naumen.metainfoadmin.client.catalog.item.CatalogItemInfoPresenter;
import ru.naumen.metainfoadmin.client.escalation.EscalationGinModule.EscalationPlaceTabs;
import ru.naumen.metainfoadmin.client.escalation.EscalationPlace;
import ru.naumen.metainfoadmin.client.escalation.vmap.command.EscalationVMapCommandGinModule.EscalationVMapItemCommandCode;

/**
 * <AUTHOR>
 * @since 02.11.2012
 *
 */
public class EscalationVMapItemInfoPresenter extends CatalogItemInfoPresenter<EscalationValueMapItemContext>
{
    private OnStartCallback<String> deleteCallback = new OnStartBasicCallback<String>(getDisplay())
    {
        @Override
        protected void handleSuccess(String uuid)
        {
            placeController.goTo(new EscalationPlace(EscalationPlaceTabs.VMAPS));
        }

    };

    @Inject
    public EscalationVMapItemInfoPresenter(CatalogItemInfoDisplay display, EventBus eventBus,
            @Assisted EscalationValueMapItemContext context)
    {
        super(display, eventBus, context);
    }

    @Override
    protected void initToolBar()
    {

        CatalogItemCommandParam<DtObject> param = new CatalogItemCommandParam<DtObject>(valueSource, refreshCallback,
                context.getCatalog().get(), false);
        CatalogItemCommandParam<DtObject> paramNoRefresh = new CatalogItemCommandParam<DtObject>(valueSource, null,
                context.getCatalog().get(), false);
        CatalogItemCommandParam<String> paramDelete = new CatalogItemCommandParam<String>(valueSource, deleteCallback,
                context.getCatalog().get(), false);

        addTool(ButtonCode.EDIT, cmessages.edit(),
                EscalationVMapItemCommandCode.EDIT_ESCALATION_VMAP_ITEM, param)
                .addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));

        addTool(ButtonCode.COPY, cmessages.copy(), EscalationVMapItemCommandCode.COPY_ESCALATION_VMAP_ITEM,
                paramNoRefresh)
                .addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));

        addTool(ButtonCode.RESTORE, cmessages.restore(), CatalogCommandCode.RESTORE_CATALOG_ITEM, param)
                .addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        addTool(ButtonCode.REMOVE, cmessages.remove(), CatalogCommandCode.REMOVE_CATALOG_ITEM, param)
                .addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        addTool(ButtonCode.DELETE, cmessages.delete(), CatalogCommandCode.DELETE_CATALOG_ITEM, paramDelete)
                .addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE));
    }
}