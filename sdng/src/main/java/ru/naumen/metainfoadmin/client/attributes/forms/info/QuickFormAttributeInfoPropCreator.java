package ru.naumen.metainfoadmin.client.attributes.forms.info;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.dispatch2.GetQuickFormTitleAction;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Создает {@link Property} для отображения информации о
 * формах быстрого добавления/редактирования на модальной 
 * форме свойств атрибута 
 *
 * <AUTHOR>
 * @since 3 авг. 2018 г.
 */
public class QuickFormAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    private DispatchAsync dispatch;

    @Override
    protected void createInt(String code)
    {
        String fqnAsString = attribute.getType().getProperty(ObjectAttributeType.METACLASS_FQN);
        ru.naumen.metainfo.shared.elements.ObjectAttributeType objectType = attribute.getType().cast();
        String quickFormId = (code.equals(AttributeFormPropertyCode.QUICK_ADD_FORM_CODE)) ? objectType
                .getQuickAddFormCode() : objectType.getQuickEditFormCode();
        dispatch.execute(new GetQuickFormTitleAction(ClassFqn.parse(fqnAsString), quickFormId),
                new BasicCallback<SimpleResult<String>>(rs)
                {
                    @Override
                    protected void handleSuccess(SimpleResult<String> result)
                    {
                        if (!result.get().isEmpty())
                        {
                            createProperty(code, result.get());
                        }
                    }
                });
    }
}
