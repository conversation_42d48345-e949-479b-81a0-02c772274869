package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import java.util.ArrayDeque;
import java.util.Collection;
import java.util.Deque;
import java.util.LinkedList;
import java.util.Map;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.Dialogs.DialogResult;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.DeleteNavigationMenuItemAction;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;

/**
 * Абстрактная команда удаления элемента меню
 *
 * <AUTHOR>
 * @since 01 окт. 2013 г.
 */
public abstract class DeleteMenuItemCommand<M extends IMenuItem> extends BaseCommandImpl<M,
        DtoContainer<NavigationSettings>>
{
    @Inject
    private Dialogs dialogs;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private NavigationSettingsMessages messages;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private I18nUtil i18nUtil;

    public DeleteMenuItemCommand(NavigationSettingsMenuItemAbstractCommandParam<M> param)
    {
        super(param);
    }

    @Override
    public void execute(final CommandParam<M, DtoContainer<NavigationSettings>> param)
    {
        final NavigationSettingsMenuItemAbstractCommandParam<M> p =
                (NavigationSettingsMenuItemAbstractCommandParam<M>)prepareParam(
                        param);
        dialogs.question(cmessages.confirmDelete(), question(p), additionalInfo(p), new DialogCallback()
        {
            @Override
            public void handleSuccess(DialogResult result)
            {
                result.getWidget().hide();
                if (Dialogs.Buttons.YES.equals(result.getButtons()))
                {
                    yesDelete(p);
                }
                else
                {
                    cancelDelete(p);
                }
            }
        });
    }

    @SuppressWarnings("unchecked")
    protected String additionalInfo(NavigationSettingsMenuItemAbstractCommandParam<M> param)
    {
        M item = param.getValue();
        if (item.getChildren().isEmpty())
        {
            return null;
        }
        StringBuilder sb = new StringBuilder("<br>");

        Deque<M> q = new ArrayDeque<>((Collection<? extends M>)item.getChildren());
        while (!q.isEmpty())
        {
            IMenuItem i = q.poll();
            sb.append('\'').append(i18nUtil.getLocalizedTitle(i)).append("'<br>");
            q.addAll((Collection<? extends M>)i.getChildren());
        }
        return messages.additionalDeleteMenuItemInfo(sb);
    }

    protected void cancelDelete(NavigationSettingsMenuItemAbstractCommandParam<M> param)
    {
        param.getCallback().onSuccess(null);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    protected String question(final NavigationSettingsMenuItemAbstractCommandParam<M> p)
    {
        return cmessages.confirmDeleteQuestion(messages.menuElement(), i18nUtil.getLocalizedTitle(p.getValue()));
    }

    protected void yesDelete(final NavigationSettingsMenuItemAbstractCommandParam<M> param)
    {
        M item = param.getValue();
        if (item.getParent() != null)
        {
            item.getParent().getChildren().remove(item);
        }
        else
        {
            param.getMenuItems().remove(item);
        }
        DeleteNavigationMenuItemAction action = getAction();
        final Map<String, LinkedList<String>> menuItemPaths = param.getMenuItemPaths();
        action.setPathToMenuItem(menuItemPaths.get(item.getCode()));
        action.setMenuItemCode(item.getCode());
        dispatch.execute(action, new SimpleResultCallbackDecorator<>(param.getCallback()));
    }

    protected abstract DeleteNavigationMenuItemAction getAction();
}