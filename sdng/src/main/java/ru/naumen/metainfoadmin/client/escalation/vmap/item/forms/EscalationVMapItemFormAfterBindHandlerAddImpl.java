/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap.VMapItemFormAfterBindHandlerAddImpl;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationVMapItemFormAfterBindHandlerAddImpl extends
        VMapItemFormAfterBindHandlerAddImpl<EscalationValueMapItemFormContext>
{
    @Override
    public void onAfterContainerBind(PropertyContainerContext context)
    {
        super.onAfterContainerBind(context);
        context.setDisabled(ValueMapCatalogItem.TARGET_ATTRS);
    }
}
