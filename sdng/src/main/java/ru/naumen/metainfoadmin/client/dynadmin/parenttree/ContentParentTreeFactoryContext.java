package ru.naumen.metainfoadmin.client.dynadmin.parenttree;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Контекст фабрики дерева выбора родительского элемента контента, содержит элементы дерева
 *
 * <AUTHOR>
 * @since 10.08.2021
 */
public class ContentParentTreeFactoryContext extends ParentTreeFactoryContext
{
    public ContentParentTreeFactoryContext(UIContext uiContext)
    {
        this(uiContext, null);
    }

    public ContentParentTreeFactoryContext(UIContext uiContext, Content editedContent)
    {
        super(uiContext, editedContent);
    }

    @Override
    public Layout getContentByDtObject(DtObject dtObject)
    {
        Content content = super.getContentByDtObject(dtObject);
        return content instanceof Form ? ((Form)content).getLayout() :
                content instanceof Tab ? ((Tab)content).getLayout() : ((TabBar)content).getTab().get(0).getLayout();
    }
}