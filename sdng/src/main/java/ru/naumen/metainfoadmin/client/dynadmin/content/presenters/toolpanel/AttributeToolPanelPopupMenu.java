package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import jakarta.inject.Inject;

import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.ui.MenuItem;

import ru.naumen.core.shared.ui.toolbar.ToolPanelLocation;
import ru.naumen.metainfo.shared.Constants.FileAttributeType;
import ru.naumen.metainfo.shared.Constants.RichTextAttributeType;
import ru.naumen.metainfo.shared.Constants.SourceCodeAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.AttributeToolPanel;

/**
 * Всплывающее меню для редактирования тулпанели атрибута
 *
 * <AUTHOR>
 * @since 17.01.22
 */
public class AttributeToolPanelPopupMenu extends AttributeToolPopupMenuBase<AttributeToolPanel>
{
    /**
     *  Пункт контекстного меню "Показывать всегда"
     */
    private MenuItem showAlways;

    /**
     *  Пункт контекстного меню "Показывать только при наведении"
     */
    private MenuItem showOnlyOnHover;

    /**
     *  Пункт контекстного меню "Показывать только при наведении"
     */
    private MenuItem placeRight;

    /**
     *  Пункт контекстного меню "Переместить вправо"
     */
    private MenuItem placeBottom;

    /**
     * Наличие некоторых пунктов меню может зависеть от свойств атрибута
     */
    private Attribute attribute;

    public MenuItem getPlaceRight()
    {
        return placeRight;
    }

    public MenuItem getPlaceBottom()
    {
        return placeBottom;
    }

    public MenuItem getShowAlways()
    {
        return showAlways;
    }

    public MenuItem getShowOnlyOnHover()
    {
        return showOnlyOnHover;
    }

    @Inject
    public void init(EditableToolPanelMessages messages, ToolMenuResources resources)
    {
        super.init(resources);
        showAlways = new MenuItem(SafeHtmlUtils.fromTrustedString(messages.showAlways()));
        showOnlyOnHover = new MenuItem(SafeHtmlUtils.fromTrustedString(messages.showOnlyOnHover()));
        placeRight = new MenuItem(SafeHtmlUtils.fromTrustedString(messages.placeRight()));
        placeBottom = new MenuItem(SafeHtmlUtils.fromTrustedString(messages.placeBottom()));

        showAlways.ensureDebugId("showAlways");
        showOnlyOnHover.ensureDebugId("showOnlyOnHover");
        placeRight.ensureDebugId("placeRight");
        placeBottom.ensureDebugId("placeBottom");

        showAlways.addStyleName(resources.css().removeFromContent());
        showOnlyOnHover.addStyleName(resources.css().removeFromContent());
        placeRight.addStyleName(resources.css().removeFromContent());
        placeBottom.addStyleName(resources.css().removeFromContent());
    }

    public void setAttribute(Attribute attribute)
    {
        this.attribute = attribute;
    }

    @Override
    protected void fillPopup(AttributeToolPanel content)
    {
        if (!isWideAttr())
        {
            if (content.getLocation() == ToolPanelLocation.DEFAULT)
            {
                menuBar.addItem(placeRight);
            }
            else
            {
                menuBar.addItem(placeBottom);
            }
        }

        if (content.isShowOnlyOnContentHover())
        {
            menuBar.addItem(showAlways);
        }
        else
        {
            menuBar.addItem(showOnlyOnHover);
        }
    }

    /**
     * Условно широкие атрибуты
     */
    private boolean isWideAttr()
    {
        String typeCode = attribute.getType().getCode();
        return RichTextAttributeType.CODE.equals(typeCode) || SourceCodeAttributeType.CODE.equals(typeCode)
               || FileAttributeType.CODE.equals(typeCode);
    }
}
