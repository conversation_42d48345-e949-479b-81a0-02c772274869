package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import java.util.ArrayList;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfoadmin.client.jmsqueue.service.JMSQueueServiceAsync;

/**
 * Фабрика виджета для фильтрации по очереди в списке ДПС
 * <AUTHOR>
 * @since 28.05.2021
 **/
public class AttributeEventActionJMSQueueWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;
    @Inject
    private JMSQueueServiceAsync jmsQueueServiceAsync;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        jmsQueueServiceAsync.getJMSQueuesForFiltration(new BasicCallback<SimpleResult<List<DtObject>>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<List<DtObject>> value)
            {
                SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();

                List<DtObject> jmsQueues = new ArrayList<>();
                for (DtObject dtObject : value.get())
                {
                    jmsQueues.add(new SimpleDtObject(dtObject.getUUID(), dtObject.getTitle(), ClassFqn.parse("")));
                }
                jmsQueues.sort(ITitled.COMPARATOR);
                widget.addItems(jmsQueues);
                callback.onSuccess(widget);
            }
        });
    }
}