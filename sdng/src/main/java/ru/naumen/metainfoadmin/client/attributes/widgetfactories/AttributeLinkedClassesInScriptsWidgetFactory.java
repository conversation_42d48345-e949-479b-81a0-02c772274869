package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import java.util.List;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.script.ScriptsAndModulesMessages;

/**
 * Фабрика представление атрибута "Классы" для каталога скриптов
 *
 * <AUTHOR>
 * @since 06.12.2017
 */
public class AttributeLinkedClassesInScriptsWidgetFactory extends AttributeEventActionLinkedClassesWidgetFactory
{
    @Inject
    private ScriptsAndModulesMessages scriptsAndModulesMessages;

    @Override
    protected void addItems(SingleSelectCellList<DtObject> widget, List<MetaClassLite> value)
    {
        super.addItems(widget, value);
        widget.addItem(new SimpleDtObject(Constants.NO_CLASS_FQN.asString(), scriptsAndModulesMessages.noClass()));
    }
}
