package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid;

import static ru.naumen.core.client.jsinterop.JQuery.$;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.DOT_K_GRID;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.KENDO_GRID;

import java.util.Collection;

import jakarta.inject.Inject;

import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.Style.Display;
import com.google.gwt.event.shared.EventBus;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.ToolPanelContentPresenter;
import ru.naumen.core.client.jsinterop.JQueryElement;
import ru.naumen.core.client.jsinterop.kendo.data.GridData;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.HasReadyState;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.hierarchy.grid.GetStructuredHierarchyGridLiteAction;
import ru.naumen.core.shared.hierarchy.grid.HierarchyGridLiteResponseItem;
import ru.naumen.core.shared.utils.IntegerRef;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfoadmin.client.common.content.AbstractInfoContentPresenter;
import ru.naumen.metainfoadmin.client.common.content.ActionContentPanelDisplayImpl.ContentCommandAction;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.common.content.DraggableContentDisplay;
import ru.naumen.metainfoadmin.client.common.content.commands.TabContentCommandCode;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentTitles;
import ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridUtils;

/**
 * {@link Presenter} для {@link HierarchyGrid}
 * Отображение контента "Иерархическое дерево" в интерфейсе администратора в пассивном режиме
 * <AUTHOR>
 * @since 22.03.2020
 */
public class HierarchyGridContentPresenterLite
        extends AbstractInfoContentPresenter<DraggableContentDisplay, HierarchyGrid, UIContext>
{
    @Inject
    private ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridPresenterLite gridPresenter;
    @Inject
    private DispatchAsync service;
    @Inject
    private ContentTitles contentTitles;
    @Inject
    private HierarchyGridMessages messages;
    @Inject
    private Formatters formatters;
    @Inject
    private AdminContentFactory contentFactory;

    private String structureTitle;
    private ToolPanelContentPresenter<UIContext> toolPanelPresenter;

    @Inject
    protected HierarchyGridContentPresenterLite(DraggableContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "HierarchyGrid");
    }

    @Override
    public void init(HierarchyGrid content, UIContext context)
    {
        super.init(content, context);

        service.execute(new GetStructuredHierarchyGridLiteAction(content.getStructuredObjectsViewCode()),
                new BasicCallback<SimpleResult<Collection<HierarchyGridLiteResponseItem>>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<Collection<HierarchyGridLiteResponseItem>> simpleResult)
                    {
                        gridPresenter.init(simpleResult.get(), content);
                        structureTitle = simpleResult.get().stream().findFirst().isPresent() ?
                                simpleResult.get().stream().findFirst().get().getStructureTitle() : "";
                        refreshDisplay();
                    }
                });
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();

        getDisplay().addWidget(gridPresenter.getDisplay().asWidget());

        if (!content.isVisible())
        {
            display.getElement().getStyle().setDisplay(Display.NONE);
        }
        customizeColumnAndTableWidthAndScrollOnReady();
    }

    @Override
    protected String getHelpText()
    {
        String helpText = contentTitles.content("HierarchyGrid") + " | " + messages.structuredObjectsView() + ": "
                          + structureTitle + " | " + messages.buildHierarchyFromCurrentObject() + ": "
                          + formatters.yesNoFormatter(content.isBuildHierarchyFromCurrentObject());

        if (!content.isBuildHierarchyFromCurrentObject())
        {
            helpText += " | " + messages.cardObjectFocus() + ": " + messages.cardObjectFocusTitle(content
                    .getFocusOnCardObject());
        }

        return helpText;
    }

    private void customizeColumnAndTableWidthAndScrollOnReady()
    {
        gridPresenter.getRefreshReady().ready(new HasReadyState.ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                customizeColumnAndTableWidthScrollAndColor();
            }
        });
    }

    @Override
    public void onReveal()
    {
        super.onReveal();
        customizeColumnAndTableWidthAndScrollOnReady();
    }

    private void customizeColumnAndTableWidthScrollAndColor()
    {
        JQueryElement rootGridElement = $(getDisplay().asWidget().getElement());
        JQueryElement grids = rootGridElement.find(DOT_K_GRID);
        IntegerRef maxTableWidth = new IntegerRef();
        maxTableWidth.setValue(0);
        grids.eachElement((index, childGrid) ->
        {
            childGrid.getParentElement().getStyle().setBackgroundColor("#ffffff");
            GridData childGridData = $(childGrid).data(KENDO_GRID);
            if (null != childGridData)
            {
                KendoGridUtils.setMinimumColumnsWidthIfAutoWidthLessThanMin(childGridData);
                KendoGridUtils.setTableWidthBySumColumnWidth(childGridData);
            }
        });
        GridData firstGridData = $((Element)grids.get(0)).data(KENDO_GRID);
        if (firstGridData != null)
        {
            KendoGridUtils.customizeScrollNotInternalMode(firstGridData);
        }
    }

    @Override
    protected void initCommands()
    {
        initCommandDisplay(getContext(), TabContentCommandCode.MOVE_TAB_CONTENT, ContentCommandAction.MOVE);
        initCommandDisplay(getContext(), TabContentCommandCode.EDIT_HIERARCHY_DEFAULT_PRS,
                ContentCommandAction.EDIT_HIERARCHY_DEFAULT_PRS);
        initCommandDisplay(getContext(), TabContentCommandCode.EDIT_TOOL_PANEL, ContentCommandAction.EDIT_TOOL_PANEL);
        initCommandDisplay(getContext(), TabContentCommandCode.EDIT_HIERARCHY_OBJECT_FILTER,
                ContentCommandAction.EDIT_HIERARCHY_OBJECT_FILTER);
        initCommandDisplay(getContext(), TabContentCommandCode.EDIT_CONTENT, ContentCommandAction.EDIT);
        initCommandDisplay(getParentContentContext(), TabContentCommandCode.DELETE_CONTENT,
                ContentCommandAction.DELETE);
    }

    @Override
    protected void onBind()
    {
        gridPresenter.bind();
        super.onBind();
        registerHandler(eventBus.addHandler(RefreshContentEvent.getType(), event ->
        {
            // Изменение тулбара списка свойств
            if (event.getContent().getParent() != null && event.getContent().getParent().equals(getContent())
                && toolPanelPresenter != null && toolPanelPresenter.isBound())
            {
                toolPanelPresenter.rebuild();
                toolPanelPresenter.refreshDisplay();
            }
        }));
        bindToolPanel();
    }

    private void bindToolPanel()
    {
        toolPanelPresenter = contentFactory.build(content.getToolPanel(), context);
        getDisplay().bindActionBar(toolPanelPresenter.getDisplay());
        registerChildPresenter(toolPanelPresenter);
    }
}