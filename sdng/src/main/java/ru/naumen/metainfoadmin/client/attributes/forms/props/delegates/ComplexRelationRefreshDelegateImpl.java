package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import static ru.naumen.core.shared.Constants.PARENT_ATTR;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.CODE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.EDITABLE;

import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.collect.Sets;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType;
import ru.naumen.metainfo.shared.elements.ComplexRelationType;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления поля "Сложная форма добавления связи"
 * <AUTHOR>
 * @since 22.09.2015
 */
public class ComplexRelationRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    //@formatter:off
    private static final  Set<String> WITH_COMPLEX_RELATION = 
            Sets.newHashSet(ObjectAttributeType.CODE, 
                    BOLinksAttributeType.CODE, 
                    BackLinkAttributeType.CODE,
                    AggregateAttributeType.CODE,
                    ResponsibleAttributeType.CODE);
    //@formatter:on

    @Inject
    private AttributesMessages messages;
    @Inject
    private SharedSettingsClientService sharedSettingsService;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        String attrEditPrs = context.getPropertyValues().getProperty(AttributeFormPropertyCode.EDIT_PRS);
        Boolean editable = context.getPropertyValues().getProperty(EDITABLE, false);
        initComplexRelationSelectList(property.getValueWidget(), attrType);

        callback.onSuccess(WITH_COMPLEX_RELATION.contains(attrType)
                           && !Presentations.QUICK_SELECTION_FIELD.equals(attrEditPrs)
                           && (editable || AggregateAttributeType.CODE.equals(attrType)
                               || PARENT_ATTR.equals(context.getPropertyValues().getProperty(CODE))));
    }

    private void initComplexRelationSelectList(SingleSelectCellList<?> selectList, String attrType)
    {
        selectList.clear();
        selectList.addItem(messages.complexRelationType(Boolean.FALSE.toString()), Boolean.FALSE.toString());
        selectList.addItem(messages.complexRelationType(ComplexRelationType.FLAT.getCode().toLowerCase()),
                ComplexRelationType.FLAT.getCode());

        if (Constants.LINK_ATTRIBUTE_TYPES.contains(attrType) && sharedSettingsService.isFullTextSearchFormEnabled())
        {
            selectList.addItem(messages.complexRelationType(
                            ComplexRelationType.FLAT_WITH_FULL_TEXT_SEARCH.getCode().toLowerCase()),
                    ComplexRelationType.FLAT_WITH_FULL_TEXT_SEARCH.getCode());
        }

        if (!(attrType.equals(AggregateAttributeType.CODE) || attrType.equals(ResponsibleAttributeType.CODE)))
        {
            selectList.addItem(messages.complexRelationType(ComplexRelationType.HIERARCHY.getCode().toLowerCase()),
                    ComplexRelationType.HIERARCHY.getCode());
        }
    }
}
