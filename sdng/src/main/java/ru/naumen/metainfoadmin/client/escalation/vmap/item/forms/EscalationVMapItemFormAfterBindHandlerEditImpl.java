/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap.VMapItemFormAfterBindHandlerEditImpl;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationVMapItemFormAfterBindHandlerEditImpl
        extends VMapItemFormAfterBindHandlerEditImpl<EscalationValueMapItemFormContext>
{
    @Override
    public void onAfterContainerBind(PropertyContainerContext context)
    {
        super.onAfterContainerBind(context);
        context.getDisplay().display();
    }
}