/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap;

import java.util.Map;

import ru.naumen.metainfoadmin.client.TableDisplay.ColumnInfo;
import ru.naumen.metainfoadmin.client.catalog.columns.CatalogColumnInfoFactory.ColumnCode;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogCommandCode;
import ru.naumen.metainfoadmin.client.catalog.impl.valuemap.VMapContentColumnsProvider;
import ru.naumen.metainfoadmin.client.escalation.vmap.command.EscalationVMapCommandGinModule.EscalationVMapItemCommandCode;

/**
 * <AUTHOR>
 * @since 30.10.2012
 *
 */
public class EscalationVMapContentColumnsProvider extends VMapContentColumnsProvider<EscalationValueMapContext>
{
    @Override
    protected void initImageColumns(Map<String, ColumnInfo> columns)
    {
        addAction(columns, ColumnCode.EDIT, EscalationVMapItemCommandCode.EDIT_ESCALATION_VMAP_ITEM);
        addAction(columns, ColumnCode.COPY, EscalationVMapItemCommandCode.COPY_ESCALATION_VMAP_ITEM);
        addAction(columns, ColumnCode.REMOVE_RESTORE, CatalogCommandCode.REMOVE_CATALOG_ITEM,
                CatalogCommandCode.RESTORE_CATALOG_ITEM);
        addDeleteColumn(columns);
    }
}