package ru.naumen.metainfoadmin.client.eventaction.form;

import static ru.naumen.metainfo.shared.eventaction.ActionType.ChangeTrackingEventAction;
import static ru.naumen.metainfo.shared.eventaction.ActionType.IntegrationEventAction;
import static ru.naumen.metainfo.shared.eventaction.ActionType.NotificationEventAction;
import static ru.naumen.metainfo.shared.eventaction.ActionType.PushEventAction;
import static ru.naumen.metainfo.shared.eventaction.ActionType.PushMobileEventAction;
import static ru.naumen.metainfo.shared.eventaction.ActionType.PushPortalEventAction;
import static ru.naumen.metainfo.shared.eventaction.ActionType.ScriptEventAction;
import static ru.naumen.metainfo.shared.eventaction.EventType.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;

import com.google.common.collect.ImmutableSet;

import jakarta.inject.Singleton;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.EventType;

/**
 * Компонент для построения списка доступных действий.
 * <AUTHOR>
 * @since Mar 29, 2022
 */
@Singleton
public class AvailableActionsProvider
{
    private static final Set<EventType> INTEGRATION_DISABLED_EVENTS = ImmutableSet.of(
            openEditForm,
            userEvent,
            escalation,
            alertActivated,
            alertDeactivated,
            alertChanged,
            ndapMessage,
            onsetTimeOfAttr,
            insertMention,
            arriveMessageOnQueue,
            logout);

    private static final Set<EventType> ONLY_TRACKING_EVENTS = ImmutableSet.of(openEditForm);

    private static final Set<EventType> TRACKING_EVENTS = ImmutableSet.of(
            openEditForm,
            edit,
            changeState,
            changeResponsible,
            addFile,
            addComment,
            editComment);

    private final Map<ActionType, Predicate<EventType>> actionTypeMap = new EnumMap<>(ActionType.class);

    public AvailableActionsProvider()
    {
        actionTypeMap.put(ScriptEventAction, eventType -> !ONLY_TRACKING_EVENTS.contains(eventType));
        actionTypeMap.put(IntegrationEventAction, eventType -> !INTEGRATION_DISABLED_EVENTS.contains(eventType));
        actionTypeMap.put(PushEventAction, eventType -> !ONLY_TRACKING_EVENTS.contains(eventType));
        actionTypeMap.put(PushMobileEventAction, eventType -> !ONLY_TRACKING_EVENTS.contains(eventType));
        actionTypeMap.put(PushPortalEventAction, eventType -> !ONLY_TRACKING_EVENTS.contains(eventType));
        actionTypeMap.put(NotificationEventAction, eventType -> !ONLY_TRACKING_EVENTS.contains(eventType));
        actionTypeMap.put(ChangeTrackingEventAction,
                eventType -> null == eventType || TRACKING_EVENTS.contains(eventType));
    }

    public List<ActionType> getAvailableActions(EventType eventType)
    {
        List<ActionType> result = new ArrayList<>();
        actionTypeMap.forEach((actionType, condition) ->
        {
            if (condition.test(eventType))
            {
                result.add(actionType);
            }
        });
        return result;
    }

    public Set<ActionType> getUnavailableActions(EventType eventType)
    {
        Set<ActionType> result = EnumSet.noneOf(ActionType.class);
        result.addAll(actionTypeMap.keySet());
        getAvailableActions(eventType).forEach(result::remove);
        return result;
    }

    public Set<EventType> getUnavailableEvents(ActionType actionType)
    {
        Set<EventType> result = EnumSet.noneOf(EventType.class);
        Predicate<EventType> eventTypeCondition = actionTypeMap.get(actionType);
        if (null == eventTypeCondition)
        {
            return result;
        }
        Arrays.stream(EventType.values()).filter(eventTypeCondition.negate()).forEach(result::add);
        return result;
    }
}
