package ru.naumen.metainfoadmin.client.adminprofile;

import java.util.Collection;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import jakarta.inject.Singleton;
import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumnFactory;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.adminprofile.command.AddAdminProfileCommand;
import ru.naumen.metainfoadmin.client.adminprofile.command.AdminProfilesInitializer;
import ru.naumen.metainfoadmin.client.adminprofile.command.DeleteAdminProfileCommand;
import ru.naumen.metainfoadmin.client.adminprofile.command.DeleteAdminProfilesCommand;
import ru.naumen.metainfoadmin.client.adminprofile.command.EditAdminProfileCommand;
import ru.naumen.metainfoadmin.client.adminprofile.data.AdminProfilesServiceAsync;
import ru.naumen.metainfoadmin.client.adminprofile.data.AdminProfilesServiceAsyncImpl;
import ru.naumen.metainfoadmin.client.adminprofile.matrix.access.AccessMarkerMatrixDisplay;
import ru.naumen.metainfoadmin.client.adminprofile.matrix.access.AccessMarkerMatrixDisplayImpl;
import ru.naumen.metainfoadmin.client.adminprofile.matrix.access.AccessMarkerMatrixPresenter;
import ru.naumen.metainfoadmin.client.adminprofile.matrix.settings.AdminSettingsMatrixDisplay;
import ru.naumen.metainfoadmin.client.adminprofile.matrix.settings.AdminSettingsMatrixDisplayImpl;
import ru.naumen.metainfoadmin.client.adminprofile.matrix.settings.AdminSettingsMatrixPresenter;

/**
 * Gin-модуль для профилей администрирования в интерфейсе администратора.
 * <AUTHOR>
 * @since 17.01.2024
 */
public class AdminProfilesGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        bind(AdminProfilesResources.class).in(Singleton.class);

        bind(AdminProfilesServiceAsync.class).to(AdminProfilesServiceAsyncImpl.class).in(Singleton.class);

        bind(AdminProfilesInitializer.class).asEagerSingleton();

        bind(AccessMarkerMatrixDisplay.class).to(AccessMarkerMatrixDisplayImpl.class);
        bind(AccessMarkerMatrixPresenter.class);

        bind(AdminSettingsMatrixDisplay.class).to(AdminSettingsMatrixDisplayImpl.class);
        bind(AdminSettingsMatrixPresenter.class);

        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, AddAdminProfileCommand.class)
                .build(new TypeLiteral<CommandProvider<AddAdminProfileCommand, CommandParam<DtObject, DtObject>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditAdminProfileCommand.class)
                .build(new TypeLiteral<CommandProvider<EditAdminProfileCommand, CommandParam<DtObject, DtObject>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, DeleteAdminProfileCommand.class)
                .build(new TypeLiteral<CommandProvider<DeleteAdminProfileCommand, CommandParam<DtObject, Void>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, DeleteAdminProfilesCommand.class)
                .build(new TypeLiteral<CommandProvider<DeleteAdminProfilesCommand, CommandParam<Collection<DtObject>,
                        Void>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .build(new TypeLiteral<LinkToPlaceColumnFactory<DtObject>>()
                {
                }));
    }
}