package ru.naumen.metainfoadmin.client.escalation.schemes.columns;

import com.google.inject.Inject;

import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.columns.ClickableTextColumn;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.core.shared.timer.definition.TimerDefinition;

/**
 * <AUTHOR>
 * @since 23.07.2012
 *
 */
public class TimerColumn extends ClickableTextColumn<DtoContainer<EscalationScheme>>
{
    @Inject
    I18nUtil i18nUtil;

    @Inject
    public TimerColumn(WidgetResources resources)
    {
        super(resources.additional().cursorPointer(), "timer");
    }

    @Override
    public String getValue(DtoContainer<EscalationScheme> object)
    {
        TimerDefinition td = object.get().getTimer();
        return td == null ? null : i18nUtil.getLocalizedTitle(td);
    }
}
