package ru.naumen.metainfoadmin.client.eventaction;

import java.util.HashSet;

import jakarta.inject.Singleton;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Sets;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.gwt.resources.client.ImageResource;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.eventaction.Constants;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfoadmin.client.customforms.CustomFormsGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.columns.EventActionFieldUpdater;
import ru.naumen.metainfoadmin.client.eventaction.EventActionGinjector.ConditionInfoPresenterFactory;
import ru.naumen.metainfoadmin.client.eventaction.command.EventActionCommandGinjector;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormGinModule;
import ru.naumen.metainfoadmin.client.style.templates.preview.engine.PreviewTemplateEngine;
import ru.naumen.metainfoadmin.client.style.templates.preview.engine.SimplePreviewTemplateEngine;

/**
 * <AUTHOR>
 * @since 01.12.2011
 */
public class EventActionGinModule extends AbstractGinModule
{
    static class EventActionAttrsProvider implements Provider<ImmutableList<AttributeFqn>>
    {
        @Override
        public ImmutableList<AttributeFqn> get()
        {
            return ImmutableList.of(
                    Constants.EventAction.Attributes.TITLE,
                    Constants.EventAction.Attributes.LINKED_CLASSES,
                    Constants.EventAction.Attributes.EVENT,
                    Constants.EventAction.Attributes.ACTION,
                    Constants.EventAction.Attributes.JMS_QUEUE,
                    Constants.EventAction.Attributes.ON);
        }
    }

    static class PermittedTypesProvider implements Provider<HashSet<EventType>>
    {
        @Override
        public HashSet<EventType> get()
        {
            return new HashSet<>(); // NOPMD
        }
    }

    static class ProhibitedTypesProvider implements Provider<HashSet<EventType>>
    {
        @Override
        public HashSet<EventType> get()
        {
            return Sets.newHashSet(EventType.escalation);
        }
    }

    public static final String EVENT_ACTION_PERMITTED = "eventActionPermitted";
    public static final String EVENT_ACTION_PROHIBITED = "eventActionProhibited";
    public static final String EVENT_ACTION_ATTRS = "eventActionAttrs";

    @Override
    protected void configure()
    {
        //@formatter:off
        install(new EventActionFormGinModule());
        install(new CustomFormsGinModule());

        bind(new TypeLiteral<EventActionFieldUpdater<String>>(){}).in(Singleton.class);
        bind(new TypeLiteral<EventActionFieldUpdater<SafeHtml>>(){}).in(Singleton.class);
        bind(new TypeLiteral<EventActionFieldUpdater<ImageResource>>(){}).in(Singleton.class);

        bind(EventActionsTabsPresenter.class);
        bind(EventActionsPresenter.class);

        bind(EventActionPresenter.class);
        bind(ScriptEventActionInfoPresenter.class);
        bind(PlannedEventInfoPresenter.class);

        bind(EventActionsPlace.class).in(Singleton.class);
        bind(ActionConditionsPresenter.class);
        bind(EventActionCommandGinjector.class).to(EventActionGinjector.class).in(Singleton.class);
        bind(EventActionFactory.class).to(EventActionFactoryImpl.class).in(Singleton.class);
        bind(ActionConditionsCreatorFactory.class).to(ActionConditionCreatorFactoryImpl.class).in(Singleton.class);
        install(new GinFactoryModuleBuilder().implement(Presenter.class, ActionConditionInfoPresenter.class).build(
                ConditionInfoPresenterFactory.class));

        bind(PreviewTemplateEngine.class).to(SimplePreviewTemplateEngine.class).in(Singleton.class);

        bind(new TypeLiteral<HashSet<EventType>>(){})
            .annotatedWith(Names.named(EVENT_ACTION_PERMITTED))
            .toProvider(PermittedTypesProvider.class);
        bind(new TypeLiteral<HashSet<EventType>>(){})
            .annotatedWith(Names.named(EVENT_ACTION_PROHIBITED))
            .toProvider(ProhibitedTypesProvider.class);
        bind(new TypeLiteral<ImmutableList<AttributeFqn>>(){})
            .annotatedWith(Names.named(EVENT_ACTION_ATTRS))
            .toProvider(EventActionAttrsProvider.class);
    }
}