package ru.naumen.metainfoadmin.client.eventaction.form.creator;

import static ru.naumen.core.client.widgets.properties.PropertiesGinModule.RICH_TEXT_AREA;

import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.script.places.EventActionCategories;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.push.PushPortalEventAction;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;

/**
 * Создание свойств на форме для ДПС типа Пуш на портал
 *
 * <AUTHOR>
 * @since 16.10.2015
 */
public class PushPortalEventActionFormPropertiesCreator
        extends PushEventActionFormPropertiesCreatorBase<PushPortalEventAction>
{
    @Inject
    public PushPortalEventActionFormPropertiesCreator(@Named(RICH_TEXT_AREA) Property<String> message)
    {
        this.message = message;
    }

    @Override
    public void bindProperties(EventActionFormDisplay display, List<ClassFqn> fqns)
    {
        super.bindProperties(display, fqns);
        bindPushMessageProperties();

        //TODO: придумать что-нибудь с дублированием.
        bindPropertiesAfter(display, fqns, EventActionCategories.EVENTACTION_PUSH_CUSTOMIZATION);
        prepareRichTextWidget();
    }

    @Override
    protected Action newEventActionTypeInstance()
    {
        return new PushPortalEventAction();
    }

    @Override
    public void init(@Nullable EventActionWithScript eventAction, Property<SelectItem> event)
    {
        super.init(eventAction, event);
        if (eventAction != null && !(eventAction.getObject().getAction() instanceof PushPortalEventAction))
        {
            throw new IllegalArgumentException("EventAction must be PushEventAction");
        }
        this.eventAction = eventAction;
    }
}