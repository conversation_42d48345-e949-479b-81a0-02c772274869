package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.navigationsettings.AddButtonValue;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.core.shared.navigationsettings.dispatch.EditNavigationMenuItemAction;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.common.objectcommands.form.ObjectFormAfterBindHandler;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.ReferenceHelper;

/**
 * <AUTHOR>
 * @since 30 сент. 2013 г.
 */
public class TopMenuItemFormPresenter<F extends ObjectForm> extends MenuItemFormPresenter<F, MenuItem>
{
    private static final List<String> PROPERTIES = Arrays
            .asList(EditNavigationSettingsFormGinModule.MenuItemPropertyCode.TITLE,
                    EditNavigationSettingsFormGinModule.MenuItemPropertyCode.USE_ATTR_TITLE,
                    EditNavigationSettingsFormGinModule.MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE,
                    EditNavigationSettingsFormGinModule.MenuItemPropertyCode.TYPE,
                    EditNavigationSettingsFormGinModule.MenuItemPropertyCode.PARENT,
                    EditNavigationSettingsFormGinModule.MenuItemPropertyCode.TYPE_OF_CARD,
                    ReferenceCode.ATTRIBUTE_CHAIN,
                    ReferenceCode.OBJECT_CLASS,
                    ReferenceCode.OBJECT_CASES,
                    ReferenceCode.REFERENCE_VALUE,
                    EditNavigationSettingsFormGinModule.MenuItemPropertyCode.ADD_BUTTON_VALUE,
                    EditNavigationSettingsFormGinModule.MenuItemPropertyCode.REFERENCE_TAB_VALUE,
                    EditNavigationSettingsFormGinModule.MenuItemPropertyCode.CUSTOM_LINK_VALUE,
                    EditNavigationSettingsFormGinModule.MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE,
                    EditNavigationSettingsFormGinModule.MenuItemPropertyCode.NEW_TAB_VALUE,
                    ToolFormPropertyCodes.ACTION,
                    ToolFormPropertyCodes.USE_QUICK_ADD_FORM,
                    ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION,
                    ToolFormPropertyCodes.QUICK_ADD_FORM,
                    ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT,
                    ToolFormPropertyCodes.SETTINGS_SET);

    private final ReferenceHelper referenceHelper;

    @Inject
    public TopMenuItemFormPresenter(PropertyFormDisplay display, EventBus eventBus,
            ObjectFormAfterBindHandler<F, MenuItem> afterBindHandler,
            PropertyControllerFactory<MenuItem, ObjectFormEdit> propertyControllerFactory,
            ReferenceHelper referenceHelper)
    {
        super(display, eventBus, afterBindHandler, propertyControllerFactory);
        this.referenceHelper = referenceHelper;
    }

    @Override
    protected EditNavigationMenuItemAction getEditNavigationMenuItemAction(MenuItem menuItem)
    {
        EditNavigationMenuItemAction action = new EditNavigationMenuItemAction();
        action.setNew(isNew);
        action.setItem(menuItem);
        action.setNewPath(getNewPath(propertyValues.getProperty(
                EditNavigationSettingsFormGinModule.MenuItemPropertyCode.PARENT)));
        action.setMenuItemCode(menuItem.getCode());
        action.setPathToMenuItem(isNew ? Lists.newLinkedList() : getMenuItemPaths().get(menuItem.getCode()));
        return action;
    }

    @Override
    protected MenuItem getNewMenuItem()
    {
        return new MenuItem();
    }

    @Override
    protected List<String> getPropertiesList()
    {
        return PROPERTIES;
    }

    protected void fillPropertyValuesByType(SuccessReadyState readyState)
    {
        if (menuItem == null)
        {
            return;
        }
        if (menuItem.getType() == MenuItemType.reference)
        {
            Reference refValue = (Reference)(menuItem).getValue();
            ArrayList<String> refMetaClassTabs = new ArrayList<>();
            ArrayList<String> refContentTabs = new ArrayList<>();
            if (!ObjectUtils.isEmpty(refValue.getTabUUIDs()))
            {
                refMetaClassTabs.addAll(refValue.getTabUUIDs().subList(0, 1));
                if (refValue.getTabUUIDs().size() > 1)
                {
                    refContentTabs.addAll(refValue.getTabUUIDs().subList(1, refValue.getTabUUIDs().size()));
                }
            }
            Reference refMetaClass = new Reference(refValue.getClassFqn(), refMetaClassTabs);
            SimpleDtObject refDTO = new SimpleDtObject(refMetaClass.getCode(), refMetaClass.getTitle());
            refDTO.setProperty(ReferenceCode.TAB_UUIDS, refMetaClassTabs);
            refDTO.setProperty(ReferenceCode.CLASS_FQN, refValue.getClassFqn());
            Reference refContent = new Reference(refValue.getClassFqn(), refContentTabs);
            propertyValues.<DtObject> setProperty(ReferenceCode.REFERENCE_VALUE, refDTO);
            propertyValues.setProperty(MenuItemPropertyCode.REFERENCE_TAB_VALUE, refContent);
            propertyValues.setProperty(MenuItemPropertyCode.NEW_TAB_VALUE, menuItem.isNewTab());
            propertyValues.setProperty(MenuItemPropertyCode.USE_ATTR_TITLE, menuItem.isUseAttributeTitle());
            propertyValues.setProperty(MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE, menuItem.getAttrForUseInTitle());
            propertyValues.setProperty(MenuItemPropertyCode.TYPE_OF_CARD, menuItem.getTypeOfCard());
            referenceHelper.transformAttrReferenceToAttrTreeObject(ReferenceCode.ATTRIBUTE_CHAIN, propertyValues,
                    readyState, menuItem.getAttrChain(), null);
            propertyValues.setProperty(ReferenceCode.OBJECT_CASES, menuItem.getObjectCases());
        }
        if (menuItem.getType() == MenuItemType.customLink)
        {
            propertyValues.setProperty(MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE, menuItem.isCustomLinkSystem());
            propertyValues.setProperty(MenuItemPropertyCode.CUSTOM_LINK_VALUE, (String)menuItem.getValue());
            propertyValues.setProperty(MenuItemPropertyCode.NEW_TAB_VALUE, menuItem.isNewTab());
        }
        if (menuItem.getType() == MenuItemType.customButton)
        {
            UserEventTool userEventTool = (UserEventTool)menuItem.getValue();
            propertyValues.setProperty(ToolFormPropertyCodes.ACTION, userEventTool.getEventUuid());
            propertyValues.setProperty(ToolFormPropertyCodes.USE_QUICK_ADD_FORM, userEventTool.isUseQuickForm());
            propertyValues.setProperty(ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION,
                    Boolean.TRUE.equals(userEventTool.getGoToCardAfterAction()));
            propertyValues.setProperty(ToolFormPropertyCodes.QUICK_ADD_FORM, userEventTool.getQuickForm());
            if (CollectionUtils.isNotEmpty(userEventTool.getAttributesFqnFilledByCurrent()))
            {
                propertyValues.setProperty(ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT,
                        userEventTool.getAttributesFqnFilledByCurrent().get(0));
            }
        }
    }

    protected void fillMenuItemByType()
    {
        switch (menuItem.getType())
        {
            case addButton:
                Collection<DtObject> value = propertyValues.getProperty(
                        EditNavigationSettingsFormGinModule.MenuItemPropertyCode.ADD_BUTTON_VALUE);
                ArrayList<ClassFqn> fqns = Lists.newArrayList(Collections2.transform(value, DtObject.FQN_EXTRACTOR));
                menuItem.setValue(new AddButtonValue(fqns, new ArrayList<>(), ""));
                break;
            case reference:
                DtObject refDTO = propertyValues.getProperty(ReferenceCode.REFERENCE_VALUE);
                Reference refContent = propertyValues.getProperty(
                        EditNavigationSettingsFormGinModule.MenuItemPropertyCode.REFERENCE_TAB_VALUE);
                if (refContent != null)
                {
                    refDTO.<List<String>> getProperty(ReferenceCode.TAB_UUIDS).addAll(refContent.getTabUUIDs());
                }

                ArrayList<String> tabUUIDs = refDTO.<List<String>> getProperty(ReferenceCode.TAB_UUIDS) != null ?
                        new ArrayList<>(refDTO.<List<String>> getProperty(ReferenceCode.TAB_UUIDS)) : null;
                menuItem.setNewTab(Boolean.TRUE.equals(propertyValues.getProperty(MenuItemPropertyCode.NEW_TAB_VALUE)));
                menuItem.setUseAttributeTitle(
                        Boolean.TRUE.equals(propertyValues.getProperty(MenuItemPropertyCode.USE_ATTR_TITLE)));
                setTitle();
                DtObject typeOfCard = propertyValues.getProperty(MenuItemPropertyCode.TYPE_OF_CARD);
                menuItem.setTypeOfCard(typeOfCard != null ? typeOfCard.getUUID() : null);
                menuItem.setAttrChain(ContentTemplatePropertiesTranslator.transformAttrTreeDtoToAttrChain(
                        propertyValues.getProperty(ReferenceCode.ATTRIBUTE_CHAIN)));
                menuItem.setObjectCases(propertyValues.getProperty(ReferenceCode.OBJECT_CASES));
                Reference refMetaClass = new Reference(refDTO.getProperty(ReferenceCode.CLASS_FQN), tabUUIDs);
                List<AttrReference> chain = menuItem.getAttrChain();
                if (chain != null)
                {
                    refMetaClass.setAttrChain(chain);
                }
                menuItem.setValue(refMetaClass);
                break;
            case customLink:
                String customLinkValue = propertyValues.getProperty(MenuItemPropertyCode.CUSTOM_LINK_VALUE);
                menuItem.setCustomLinkSystem(Boolean.TRUE.equals(
                        propertyValues.getProperty(MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE)));
                menuItem.setValue(customLinkValue);
                menuItem.setNewTab(Boolean.TRUE.equals(propertyValues.getProperty(MenuItemPropertyCode.NEW_TAB_VALUE)));
                break;
            case customButton:
                UserEventTool userEventTool = new UserEventTool();
                String eventUuid = propertyValues.getProperty(ToolFormPropertyCodes.ACTION);
                userEventTool.setEventUuid(eventUuid);
                boolean isUseQuickForm =
                        Boolean.TRUE.equals(propertyValues.getProperty(ToolFormPropertyCodes.USE_QUICK_ADD_FORM));
                userEventTool.setUseQuickForm(isUseQuickForm);
                Boolean isGoToCard =
                        Boolean.TRUE.equals(
                                propertyValues.getProperty(ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION));
                userEventTool.setGoToCardAfterAction(isGoToCard);
                String addFormCode = propertyValues.getProperty(ToolFormPropertyCodes.QUICK_ADD_FORM);
                userEventTool.setQuickForm(addFormCode);
                String attributeForFill =
                        propertyValues.getProperty(ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT);
                userEventTool.setAttributesFqnFilledByCurrent(
                        attributeForFill != null ? Collections.singletonList(attributeForFill) : null);
                menuItem.setValue(userEventTool);
                break;
            default:
                break;
        }
    }

    @Override
    protected Map<String, LinkedList<String>> getMenuItemPaths()
    {
        return settings.getTopMenuItemPaths();
    }

    protected String getMenuItemCaption()
    {
        return messages.menuTopElementBy();
    }
}