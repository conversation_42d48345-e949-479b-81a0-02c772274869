package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes;

import java.util.Collection;

import ru.naumen.core.client.tree.selection.HierarchicalMultiSelectionModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

/**
 * Обработчик обновления свойства "Ограничения по типам"
 * <AUTHOR>
 * @since 28.05.2012
 */
public interface PermittedTypesRefreshDelegate<F extends ObjectForm>
        extends
        AttributeFormPropertyDelegateRefresh<F, Collection<DtObject>, PropertyBase<Collection<DtObject>,
                PopupValueCellTree<DtObject, Collection<DtObject>, HierarchicalMultiSelectionModel<DtObject>>>>
{
}
