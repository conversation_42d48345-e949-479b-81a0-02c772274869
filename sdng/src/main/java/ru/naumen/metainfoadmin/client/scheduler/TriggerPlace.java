package ru.naumen.metainfoadmin.client.scheduler;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.scheduler.Trigger;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

/**
 *
 * <AUTHOR>
 */
public class TriggerPlace extends Place
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<TriggerPlace>
    {
        @Override
        public TriggerPlace getPlace(String token)
        {
            return new TriggerPlace(token);
        }

        /**
         * Формируем токен
         */
        @Override
        public String getToken(TriggerPlace place)
        {
            return place == null ? "" : place.getCode();
        }
    }

    /**
     * Префикс
     */
    public static final String PLACE_PREFIX = "trigger";

    private transient DtoContainer<Trigger> trigger = null;

    /**
     * Код правила выполнения задачи планировщика
     */
    private String code = null;

    public TriggerPlace()
    {
    }

    /**
     * Конструктор
     *
     * @param code код правила выполнения задачи планировщика
     */
    public TriggerPlace(String code)
    {
        this.code = code;
    }

    @AssistedInject
    public TriggerPlace(@Assisted DtoContainer<Trigger> trigger)
    {
        this.code = trigger.get().getCode();
        this.trigger = trigger;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null || this.getClass() != obj.getClass())
        {
            return false;
        }
        return ObjectUtils.equals(this.code, ((TriggerPlace)obj).code);
    }

    /**
     * @return код правила выполнения задачи планировщика
     */
    public String getCode()
    {
        return code;
    }

    public DtoContainer<Trigger> getTrigger()
    {
        return trigger;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(code);
    }

    @Override
    public String toString()
    {
        return "TriggerPlace [" + code + "]";
    }
}
