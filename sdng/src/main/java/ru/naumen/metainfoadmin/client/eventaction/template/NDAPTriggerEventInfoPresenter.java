package ru.naumen.metainfoadmin.client.eventaction.template;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.collect.ImmutableMap;
import com.google.gwt.event.shared.EventBus;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.shared.eventaction.Constants.EventAction;
import ru.naumen.metainfo.shared.eventaction.NDAPTriggerEvent;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.eventaction.EventInfoPresenterBase;

/**
 * Презентер для работы с {@link NDAPTriggerEvent}
 * <AUTHOR>
 * @since Mar 24, 2020
 */
public class NDAPTriggerEventInfoPresenter extends EventInfoPresenterBase<NDAPTriggerEvent>
{
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> source;

    @Inject
    protected CommonMessages cmessages;

    @Inject
    protected Processor validation;

    @Inject
    public NDAPTriggerEventInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void refreshDisplay()
    {
        setPropertiesValues();
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(cmessages.source());
        getDisplay().add(source);
        source.setCaption(cmessages.title());
        refreshDisplay();
    }

    private void setPropertiesValues()
    {
        if (!(event instanceof NDAPTriggerEvent))
        {
            source.setValue(cmessages.metric());
        }
        else
        {
            source.setValue(getSourceValue(event.getSource()));
        }
    }

    private String getSourceValue(Set<String> source)
    {
        Map<String, String> eventSources = ImmutableMap.of(EventAction.METRIC, cmessages.metric(),
                EventAction.MODEL, cmessages.model());
        return StringUtilities.join(source.stream()
                .map(eventSources::get).collect(Collectors.toSet()));
    }
}
