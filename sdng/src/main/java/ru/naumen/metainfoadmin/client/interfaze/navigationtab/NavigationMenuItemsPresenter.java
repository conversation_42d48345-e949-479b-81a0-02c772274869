package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import static ru.naumen.commons.shared.utils.StringUtilities.SPACE;
import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;

import java.util.ArrayList;

import com.google.common.collect.Sets;
import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NodeList;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safecss.shared.SafeStyles;
import com.google.gwt.safecss.shared.SafeStylesBuilder;
import com.google.gwt.safecss.shared.SafeStylesUtils;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.Header;
import com.google.gwt.user.cellview.client.RowStyles;
import com.google.gwt.user.cellview.client.TextHeader;
import com.google.gwt.view.client.AbstractDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.inject.name.Named;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.group.HierarchicalListEditorDnDController;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.columns.ClickableSafeHtmlTextCell;
import ru.naumen.core.client.widgets.columns.HasEnabledColumn;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumnFactory;
import ru.naumen.core.client.widgets.columns.LinkToPlaceWithIndentColumn;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.MoveNavigationMenuItemAction;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.CommonUtils.UUIDExtractor;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfoadmin.client.CatalogCellTableResources.CatalogCellTableStyle;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableResources;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableStyle;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.column.MenuItemColumn;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsMenuItemAbstractCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.MenuItemChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.MenuItemChangedHandler;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.NavigationSettingsChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.NavigationSettingsChangedHandler;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Базовый класс списка элементов меню - верхнего и левого
 *
 * <AUTHOR>
 * @since 26.06.2020
 */
public abstract class NavigationMenuItemsPresenter<M extends IMenuItem> extends BasicPresenter<TableDisplay<M>>
        implements MenuItemChangedHandler, NavigationSettingsChangedHandler
{
    protected abstract class ItemsDataProvider extends AbstractDataProvider<M>
    {
        @Override
        protected void onRangeChanged(final HasData<M> display)
        {
            List<M> itemsList = new ArrayList<>();
            if (settings != null)
            {
                reqFillList(getMenuItems(), itemsList);
            }
            display.setRowData(0, itemsList);
            display.setRowCount(itemsList.size(), true);

            Scheduler.get().scheduleDeferred(NavigationMenuItemsPresenter.this::updateDnDControllers);
        }

        protected abstract List<M> getMenuItems();

        private void reqFillList(Iterable<M> items, List<M> result)
        {
            for (M item : items)
            {
                result.add(item);
                reqFillList((Iterable<M>)item.getChildren(), result);
            }
        }
    }

    private class ItemsRowStyles implements RowStyles<M>
    {
        @Override
        public String getStyleNames(M item, int rowIndex)
        {
            CatalogCellTableStyle cellTableStyle = cellTableResources.cellTableStyle();
            if (item instanceof LeftMenuItemSettingsDTO && Boolean.FALSE.equals(
                    ((LeftMenuItemSettingsDTO)item).getProperty(Tag.IS_ELEMENT_ENABLED)))
            {
                return cellTableStyle.itemAttentionRow();
            }
            Integer level = getLevelFunction().apply(item);
            boolean hasParent = level != null && level > 0;
            return item.isEnabled() ? hasParent ? null : cellTableStyle.folderRow()
                    : hasParent ? cellTableStyle.itemRemoved() : cellTableStyle.folderRowRemoved();
        }
    }

    //TODO: Вынести DnD в базовый класс
    // NSDPRD-29647 Рефакторинг DnD в простых таблицах
    // https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$200698643:smrmTask$development
    protected abstract class NavigationMenuListDnDController extends HierarchicalListEditorDnDController
    {
        public NavigationMenuListDnDController()
        {
            super(getDisplay().getTableContainer().getElement());
        }

        @Override
        public void move(final int oldPosition, final int newPosition, ReadyState readyState)
        {
            MoveNavigationMenuItemAction action = getMoveAction();
            M item = getDisplay().getTable().getVisibleItem(getGroupIndexList().get(oldPosition));
            action.setDirection(newPosition - oldPosition);
            action.setMenuItemCode(item.getCode());
            action.setPathToMenuItem(getMenuItemPaths().get(item.getCode()));
            dispatch.execute(action, new BasicCallback<SimpleResult<DtoContainer<NavigationSettings>>>(readyState)
            {
                @Override
                protected void handleSuccess(SimpleResult<DtoContainer<NavigationSettings>> value)
                {
                    refreshCallback.onSuccess(value.get());
                }
            });
        }

        protected abstract Map<String, LinkedList<String>> getMenuItemPaths();

        protected abstract MoveNavigationMenuItemAction getMoveAction();

        @Override
        public boolean canDragStart(@Nullable Element element)
        {
            int index = indexOf(element);
            DataTable<M> table = getDisplay().getTable();
            if (index < 0 || index >= table.getVisibleItemCount())
            {
                return false;
            }
            return hasPermission(permissions, PermissionType.EDIT, table.getVisibleItem(index));
        }
    }

    private final Function<M, SafeStyles> TITLE_STYLES = input ->
    {
        SafeStylesBuilder sb = new SafeStylesBuilder();
        Integer level = getLevelFunction().apply(input);
        if (level != null)
        {
            if (level == 0)
            {
                sb.appendTrustedString("font-weight: 800;");
            }
            sb.append(SafeStylesUtils.forMarginLeft(level * 20, Unit.PX));
        }
        return sb.toSafeStyles();
    };

    protected OnStartCallback<DtoContainer<NavigationSettings>> refreshCallback =
            new SafeOnStartBasicCallback<DtoContainer<NavigationSettings>>(
                    getDisplay())
            {
                @Override
                protected void handleSuccess(@Nullable DtoContainer<NavigationSettings> value)
                {
                    if (value != null)
                    {
                        String menuItemCode = param.getValue() != null ? param.getValue().getCode() : "";
                        eventBus.fireEvent(new MenuItemChangedEvent(value, menuItemCode));
                    }
                }
            };

    @Inject
    protected CommonMessages cmessages;
    @Inject
    ObjectListColumnBuilder tableBuilder;
    @Inject
    private LinkToPlaceColumnFactory<M> titleColumnFactory;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    protected FontIconFactory<M> iconFactory;
    @Inject
    protected WithArrowsCellTableResources cellTableResources;
    @Inject
    @Named(NavigationTabSettingsGinModule.MENU_TYPE_TITLES)
    protected Map<MenuItemType, String> menuTypeTitles;
    @Inject
    private MenuItemColumn<M> itemValueColumn;
    @Inject
    protected PlaceController placeController;
    @Inject
    private ListEditorDnDControllerFactory dndControllerFactory;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private NavigationSettingsResources resources;

    private final Map<String, ListEditorDnDController> dndControllersMap = new HashMap<>();
    private final Map<String, NavigationMenuListDnDController> dndGroupsMap = new HashMap<>();

    private final ToolBarDisplayMediator<DtoContainer<NavigationSettings>> toolBar;

    protected final CommonMessages cmesages;
    protected final NavigationSettingsMessages messages;

    protected DtoContainer<NavigationSettings> settings;
    protected PermissionHolder permissions;

    private NavigationSettingsMenuItemAbstractCommandParam<M> param;

    protected FieldUpdater<M, SafeHtml> htmlFieldUpdater = new FieldUpdater<M, SafeHtml>()
    {
        @Override
        public void update(int index, M item, SafeHtml value)
        {
            placeController.goTo(getNewPlace(item));
        }
    };

    @Inject
    public NavigationMenuItemsPresenter(TableDisplay<M> display, EventBus eventBus,
            CommonMessages cmesages, NavigationSettingsMessages messages)
    {
        super(display, eventBus);
        this.cmesages = cmesages;
        this.messages = messages;
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    public void init(DtoContainer<NavigationSettings> container)
    {
        NavigationSettings newSettings = new NavigationSettings();
        newSettings.copyFrom(container.get());
        DtoContainer<NavigationSettings> newSettingsDto = new DtoContainer<>(newSettings);
        this.settings = newSettingsDto;
        this.permissions = container.getProperty(SettingsSet.ADMIN_PERMISSIONS);
        param = getParam(container, refreshCallback);
    }

    protected void refreshSettings(DtoContainer<NavigationSettings> settings)
    {
        NavigationSettings newSettings = new NavigationSettings();
        newSettings.copyFrom(settings.get());
        DtoContainer<NavigationSettings> newSettingsDto = new DtoContainer<NavigationSettings>(newSettings);
        this.settings = newSettingsDto;
        param.setSettings(newSettingsDto);
    }

    @Override
    public void onMenuItemChanged(MenuItemChangedEvent event)
    {
        PermissionHolder newPermHolder = event.getSettings().getProperty(SettingsSet.ADMIN_PERMISSIONS);
        newPermHolder.fillPermissionHolder(permissions);
        refreshSettings(event.getSettings());
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        toolBar.refresh(settings);
        getDisplay().refresh();
        if (!ifMenuBlockEnabled())
        {
            getDisplay().getCaptionWidget().setStyleName(resources.css().archiveBlockTitle(), true);
            getDisplay().getCaptionWidget().setTitle(messages.visibilityHidden());
        }
        else
        {
            getDisplay().getCaptionWidget().removeStyleName(resources.css().archiveBlockTitle());
        }
    }

    protected void initTable()
    {
        DataTable<M> table = getDisplay().getTable();
        table.clearColumns();
        table.setVisibleRange(0, DefaultCellTable.DEFAULT_PAGESIZE);

        addFrontActionColumns();

        addPropertiesColumns(table);

        HasEnabledColumn<M> enableColumn = new HasEnabledColumn<>(iconFactory);
        enableColumn.setFieldUpdater((index, item, value) -> placeController.goTo(getNewPlace(item)));
        table.addColumn(enableColumn, cmessages.on());
        addBackActionColumns();

        table.setRowStyles(new ItemsRowStyles());
        table.asWidget().ensureDebugId(getTableDebugId());
    }

    protected abstract String getTableDebugId();

    protected void addPropertiesColumns(DataTable<M> table)
    {
        addTitleColumn(table);

        addTypeColumn(table);

        addValueColumn(table);
    }

    protected void addValueColumn(DataTable<M> table)
    {
        itemValueColumn.setFieldUpdater((index, item, value) -> placeController.goTo(getNewPlace(item)));
        table.addColumn(itemValueColumn, cmessages.content());
    }

    protected void addTypeColumn(DataTable<M> table)
    {
        Column<M, SafeHtml> typeColumn = new Column<M, SafeHtml>(new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(M item)
            {
                return new SafeHtmlBuilder().appendEscaped(menuTypeTitles.get(item.getType())).toSafeHtml();
            }
        };
        typeColumn.setFieldUpdater(htmlFieldUpdater);

        table.addColumn(typeColumn, cmessages.elementView());
    }

    protected void addTitleColumn(DataTable<M> table)
    {
        WithArrowsCellTableStyle tableStyle = cellTableResources.cellTableStyle();
        LinkToPlaceWithIndentColumn<M> titleColumn = (LinkToPlaceWithIndentColumn<M>)titleColumnFactory
                .getColumn(this::getNewPlace);
        titleColumn.setStyleFunction(TITLE_STYLES);
        titleColumn.setCellStyleNames(tableStyle.titleColumn() + SPACE + tableStyle.titleCellWithHint());
        titleColumn.setIdProviderFunction(UUIDExtractor.INSTANCE);
        titleColumn.setLevelFunction(getLevelFunction());
        titleColumn.setFieldUpdater((index, item, value) -> placeController.goTo(getNewPlace(item)));
        Header<?> titleHeader = new TextHeader(cmessages.title());
        titleHeader.setHeaderStyleNames(tableStyle.titleColumn());
        titleColumn.setHintTextFunction(getHintTextFunction());
        table.addColumn(titleColumn, titleHeader);
    }

    protected Function<M, Integer> getLevelFunction()
    {
        return item -> getMenuItemPaths().get(item.getCode()).size();
    }

    protected java.util.function.Function<M, String> getHintTextFunction()
    {
        return item -> null;
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(MenuItemChangedEvent.getType(), this));
        registerHandler(eventBus.addHandler(NavigationSettingsChangedEvent.getType(), this));
        addTool(cmessages.addItem(), getAddCommandId());
        getDisplay().setCaption(getDisplayCaption());

        initTable();
        final ItemsDataProvider dataProvider = getDataProvider();
        dataProvider.addDataDisplay(getDisplay().getTable());
        toolBar.bind();
    }

    protected void addActionColumn(String... commands)
    {
        addActionColumn(null, commands);
    }

    protected void addActionColumn(@Nullable Predicate<M> visibilityCondition, String... commands)
    {
        PermissionType permissionType = getCommandPermissionType(commands[0]);
        Predicate<M> permissionCondition = AdminPermissionUtils.createPermissionPredicate(permissionType, permissions);
        Predicate<M> columnCondition = visibilityCondition == null
                ? permissionCondition
                : visibilityCondition.and(permissionCondition);
        tableBuilder.addActionColumn(display, param, columnCondition, commands);
    }

    protected abstract PermissionType getCommandPermissionType(String command);

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private void addTool(String title, String cmd)
    {
        toolBar.add((ButtonPresenter)buttonFactory.create(ButtonCode.ADD, title, cmd, param));
    }

    private void updateDnDControllers()
    {
        NodeList<Element> rows = getDisplay().getTable().asWidget().getElement().getElementsByTagName("tr");
        for (NavigationMenuListDnDController group : dndGroupsMap.values())
        {
            group.getGroupIndexList().clear();
            group.updateElements(rows);
        }

        Map<String, Integer> itemIndexes = new HashMap<>();
        int curIndex = 0;
        for (M item : getDisplay().getTable().getVisibleItems())
        {
            itemIndexes.put(item.getCode(), curIndex++);
        }

        updateDnDGroup(StringUtilities.EMPTY, getMenuItems(), itemIndexes, rows);
        Set<String> parentCodes = Sets.newHashSet(StringUtilities.EMPTY);
        for (M item : getDisplay().getTable().getVisibleItems())
        {
            if (MenuItemType.chapter == item.getType() && !item.getChildren().isEmpty())
            {
                String code = item.getCode();
                parentCodes.add(code);
                List<M> children = (List<M>)item.getChildren();
                updateDnDGroup(code, children, itemIndexes, rows);
            }
        }

        for (String code : new HashSet<>(dndGroupsMap.keySet()))
        {
            if (!parentCodes.contains(code))
            {
                dndGroupsMap.remove(code);
                dndControllersMap.remove(code).destroy();
            }
        }
    }

    private void updateDnDGroup(String code, List<M> children, Map<String, Integer> itemIndexes,
            NodeList<Element> allElements)
    {
        NavigationMenuListDnDController group = dndGroupsMap.get(code);
        if (null == group)
        {
            group = getGroupController();
            group.updateElements(allElements);
            dndGroupsMap.put(code, group);
            dndControllersMap.put(code, dndControllerFactory.create(group));
        }
        for (M child : children)
        {
            group.getGroupIndexList().add(itemIndexes.get(child.getCode()));
        }
        dndControllersMap.get(code).update();
    }

    protected abstract String getAddCommandId();

    protected abstract String getDisplayCaption();

    protected abstract ItemsDataProvider getDataProvider();

    protected abstract NavigationMenuListDnDController getGroupController();

    protected abstract List<M> getMenuItems();

    protected abstract void addFrontActionColumns();

    protected abstract void addBackActionColumns();

    protected abstract NavigationSettingsMenuItemAbstractCommandParam<M> getParam(
            DtoContainer<NavigationSettings> settings,
            OnStartCallback<DtoContainer<NavigationSettings>> refreshCallback);

    protected abstract Map<String, LinkedList<String>> getMenuItemPaths();

    protected abstract boolean ifMenuBlockEnabled();

    protected abstract NavigationMenuItemPlace<M> getNewPlace(M item);
}
