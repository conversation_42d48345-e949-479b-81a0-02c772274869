package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.catalog.item.CatalogItemPlace;

/**
 * Кастомайзер представления значения по умолчанию для отображения элемента справочника. 
 *
 * <AUTHOR>
 * @since 18 сент. 2018 г.
 *
 */
public class CatalogItemDefaultValuePrsCustomizer implements DefaultValuePrsCustomizer
{
    @Override
    public IsWidget createWidget(WidgetContext<Attribute> context)
    {
        Attribute attr = context.getContextObject();
        Object defaultValue = attr.getDefaultValue();
        if (defaultValue instanceof SimpleTreeDtObject)
        {
            return createWidget((SimpleTreeDtObject)defaultValue);
        }
        return new HTML(); // NOPMD NSDPRD-28509 unsafe html
    }

    public static Anchor createWidget(SimpleTreeDtObject value)
    {
        Anchor anchor = new Anchor(value.getProperty(Constants.AbstractBO.TITLE), false,
                AttrWidgetsHelper.createLink(CatalogItemPlace.PLACE_PREFIX,
                        value.getProperty(Constants.AbstractBO.UUID)));
        anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
        return anchor;
    }
}
