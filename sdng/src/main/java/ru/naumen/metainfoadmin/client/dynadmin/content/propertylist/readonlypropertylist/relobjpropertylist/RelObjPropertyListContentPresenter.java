package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.relobjpropertylist;

import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.collect.Collections2;
import com.google.gwt.event.shared.EventBus;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.GetPermittedRelatedTypesResponse;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.Constants.MetaClassProperties;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfoadmin.client.common.content.HasPropertiesContentDisplay;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.RelObjPropertyListContentPresenterBase;

/**
 * Презентер контента "Параметры связанного объекта"
 * <AUTHOR>
 * @since 04.10.2011
 */
public class RelObjPropertyListContentPresenter extends RelObjPropertyListContentPresenterBase<RelObjPropertyList>
{
    @Inject
    private CommonMessages commonMessages;

    private boolean allowEdit;

    @Inject
    public RelObjPropertyListContentPresenter(HasPropertiesContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "RelObjPropertyList");
    }

    @Override
    public void refreshDisplay()
    {
        if (allowEdit != content.isAllowEdit())
        {
            toolPanelPresenter.unbind();
            toolPanelPresenter = contentFactory.build(getContent().getToolPanel(), getContext());
            getDisplay().bindActionBar(toolPanelPresenter.getDisplay());
            allowEdit = content.isAllowEdit();
        }
        super.refreshDisplay();
    }

    @Override
    protected String getCaption()
    {
        return metainfoUtils.getLocalizedValue(getContent().getCaption());
    }

    @Override
    protected Set<String> getDisabledAttributes()
    {
        return context.getContextProperty(MetaClassProperties.DISABLED_ATTRIBUTES);
    }

    @Override
    protected void onBind()
    {
        allowEdit = content.isAllowEdit();
        super.onBind();
    }

    @Override
    protected void setupHelpText()
    {
        final Attribute attribute = getContext().getParentContext().getMetainfo()
                .getAttribute(getContent().getAttrCode());
        final AttributeGroup attributeGroup = getContext().getMetainfo()
                .getAttributeGroup(getContent().getAttributeGroup());
        String helpTextForAllChild = getHelpTextForAllChilds(" | ", true);
        if (attribute != null && attributeGroup != null)
        {
            metainfoService.getPermittedTypes(attribute, new BasicCallback<GetPermittedRelatedTypesResponse>()
            {
                @Override
                protected void handleSuccess(GetPermittedRelatedTypesResponse result)
                {
                    String titles = "";
                    if (result.isNoOneSelected())
                    {
                        titles = commonMessages.noone();
                    }
                    else
                    {
                        titles = StringUtilities
                                .join(Collections2.transform(result.getPermittedTypes(), ObjectUtils.TITLE_EXTRACTOR));
                    }

                    String helpText = messages.relProperty() + ' '
                                      + messages.relPropertyExt(getContext().getMetainfo().getTitle(),
                            attribute.getTitle(),
                            attributeGroup.getTitle(), titles)
                                      + helpTextForAllChild;
                    getDisplay().setHelpText(helpText);
                }
            });
        }
        else
        {
            getDisplay().setHelpText(messages.relProperty() + helpTextForAllChild);
        }
    }
}
