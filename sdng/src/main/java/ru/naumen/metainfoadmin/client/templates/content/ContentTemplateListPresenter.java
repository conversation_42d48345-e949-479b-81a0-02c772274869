package ru.naumen.metainfoadmin.client.templates.content;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TEMPLATES;

import java.util.Set;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.admin.client.advlists.AdminAdvListPresenterBase;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.DisplayHolder;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.templates.content.command.ContentTemplateCommandCode;
import ru.naumen.metainfoadmin.client.templates.content.form.AddContentTemplateFormPresenter;
import ru.naumen.objectlist.client.AddMetainfoObjectHandler;

/**
 * Представление списка шаблонов контентов.
 * <AUTHOR>
 * @since Mar 15, 2021
 */
public class ContentTemplateListPresenter extends AdminAdvListPresenterBase<DtObject>
{
    @Inject
    private Provider<AddContentTemplateFormPresenter> addFormProvider;

    private final AsyncCallback<Void> refreshCallback = new BasicCallback<Void>()
    {
        @Override
        protected void handleSuccess(Void value)
        {
            refreshDisplay();
        }
    };

    @Inject
    public ContentTemplateListPresenter(DisplayHolder display,
            EventBus eventBus,
            CommandFactory commandFactory,
            ContentTemplateAdvlistFactory advlistFactory)
    {
        super(display, eventBus, commandFactory, advlistFactory);
    }

    @Override
    protected AddMetainfoObjectHandler getAddMetainfoObjectHandler()
    {
        return event ->
        {
            AddContentTemplateFormPresenter formPresenter = addFormProvider.get();
            formPresenter.init(new CallbackDecorator<DtObject, Void>(refreshCallback)
            {
                @Override
                protected Void apply(DtObject from)
                {
                    return null;
                }
            });
            formPresenter.bind();
        };
    }

    @Override
    protected Set<String> getActionCommands()
    {
        return ContentTemplateCommandCode.COMMANDS_IN_LIST;
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return TEMPLATES;
    }
}
