/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormCopy;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyValuesProviderWithCatalogItemImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemParentPropertyBindDelegateEditImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.CatalogItemFormContextGinModule;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.CatalogItemFormGinModule;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap.LinkedMetaClassesVCHDelegateImpl;

/**
 * <AUTHOR>
 * @since 30.10.2012
 *
 */
public class EscalationVMapItemFormGinModule extends AbstractGinModule
{
    /**
     * Максимальная длина названий
     */
    protected static final int MAX_TITLE_LENGTH = 255;

    /**
     * Максимальная длина кода
     */
    protected static final int MAX_CODE_LENGTH = 255;

    @Override
    protected void configure()
    {
        //@formatter:off
        bind(new TypeLiteral<LinkedMetaClassesVCHDelegateImpl<EscalationValueMapItemFormContext>>(){})
            .to(EscalationLinkedMetaClassesVCHDelegateImpl.class)
            .in(Singleton.class);
        
        install(CatalogItemFormContextGinModule.create(EscalationValueMapItemFormContext.class)
                .setConstants(                              new TypeLiteral<EscalationVMapItemFormConstants>(){})
                .setCodePropertyValidatorDelegate(          new TypeLiteral<EscalationVMapItemFormCodePropertyValidatorDelegateImpl>(){}));
        //@formatter:on

        bindAddForm();
        bindCopyForm();
        bindEditForm();
    }

    private void bindAddForm()
    {
        //@formatter:off
        install(CatalogItemFormGinModule.create(EscalationValueMapItemFormContext.class, ObjectFormAdd.class)
                .setAfterBindHandler(               new TypeLiteral<EscalationVMapItemFormAfterBindHandlerAddImpl>(){})
                .setPropertControllerFactory(       new TypeLiteral<EscalationVMapItemFormPropertyControllerFactoryImpl<ObjectFormAdd>>(){})
                .setPropertyValuesProvider(         new TypeLiteral<EscalationVMapItemFormPropertyValuesProviderAddImpl>(){})
                .setMapConverter(                   new TypeLiteral<EscalationVMapItemFormPropertyMapConverterImpl<ObjectFormAdd>>(){})
                .setPropertyParametersDescriptorFactory(    new TypeLiteral<EscalationVMapItemFormPropertyParametersDescriptionFactoryImpl<ObjectFormAdd>>(){}));
        //@formatter:on
    }

    private void bindCopyForm()
    {
        //@formatter:off
        install(CatalogItemFormGinModule.create(EscalationValueMapItemFormContext.class, ObjectFormCopy.class)
                .setAfterBindHandler(               new TypeLiteral<EscalationVMapItemFormAfterBindHandlerCopyImpl>(){})
                .setPropertControllerFactory(       new TypeLiteral<EscalationVMapItemFormPropertyControllerFactoryImpl<ObjectFormCopy>>(){})
                .setPropertyValuesProvider(         new TypeLiteral<CatalogItemFormPropertyValuesProviderWithCatalogItemImpl<EscalationValueMapItemFormContext, ObjectFormCopy>>(){})
                .setMapConverter(                   new TypeLiteral<EscalationVMapItemFormPropertyMapConverterImpl<ObjectFormCopy>>(){})
                .setPropertyParametersDescriptorFactory(    new TypeLiteral<EscalationVMapItemFormPropertyParametersDescriptionFactoryImpl<ObjectFormCopy>>(){}));
        //@formatter:on
    }

    private void bindEditForm()
    {
        //@formatter:off
        install(CatalogItemFormGinModule.create(EscalationValueMapItemFormContext.class, ObjectFormEdit.class)
                .setAfterBindHandler(               new TypeLiteral<EscalationVMapItemFormAfterBindHandlerEditImpl>(){})
                .setPropertControllerFactory(       new TypeLiteral<EscalationVMapItemEditFormPropertyControllerFactoryImpl>(){})
                .setPropertyValuesProvider(         new TypeLiteral<CatalogItemFormPropertyValuesProviderWithCatalogItemImpl<EscalationValueMapItemFormContext, ObjectFormEdit>>(){})
                .setMapConverter(                   new TypeLiteral<EscalationVMapItemFormPropertyMapConverterImpl<ObjectFormEdit>>(){})
                .setParentBindDelegate(             new TypeLiteral<CatalogItemParentPropertyBindDelegateEditImpl<EscalationValueMapItemFormContext>>(){})
                .setPropertyParametersDescriptorFactory(    new TypeLiteral<EscalationVMapItemFormPropertyParametersDescriptionFactoryImpl<ObjectFormEdit>>(){}));
        //@formatter:on
    }
}