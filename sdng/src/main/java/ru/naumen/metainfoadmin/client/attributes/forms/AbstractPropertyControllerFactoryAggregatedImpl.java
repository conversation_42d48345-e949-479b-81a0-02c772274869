package ru.naumen.metainfoadmin.client.attributes.forms;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.Map;
import java.util.function.Function;

import com.google.common.base.Preconditions;

import java.util.HashMap;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactoryAggregatedImpl;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.BackTimerAttributeType;
import ru.naumen.metainfo.shared.Constants.CaseListAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType;
import ru.naumen.metainfo.shared.Constants.TimerAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Абстрактная реализация фабрики атрегирующих свойств для форм добавления/редактирования 
 * атрибутв и параметров настраиваемых форм
 *
 * <AUTHOR>
 * @since 29 апр. 2016 г.
 */
public abstract class AbstractPropertyControllerFactoryAggregatedImpl<F extends ObjectForm>
        extends PropertyControllerFactoryAggregatedImpl<Attribute, F>
{
    private final static Map<String, String> typeToProperty = new HashMap<>();

    static
    {
        typeToProperty.put(AggregateAttributeType.CODE, AGGREGATE_VALUE);
        typeToProperty.put(ResponsibleAttributeType.CODE, AGGREGATE_VALUE);
        typeToProperty.put(ObjectAttributeType.CODE, TARGET_CLASS);
        typeToProperty.put(BOLinksAttributeType.CODE, TARGET_CLASS);
        typeToProperty.put(CaseListAttributeType.CODE, TARGET_CLASS);
        typeToProperty.put(CatalogItemAttributeType.CODE, TARGET_CATALOG);
        typeToProperty.put(CatalogItemsAttributeType.CODE, TARGET_CATALOG);
        typeToProperty.put(BackLinkAttributeType.CODE, DIRECT_LINK_TARGET);
        typeToProperty.put(TimerAttributeType.CODE, TARGET_TIMER);
        typeToProperty.put(BackTimerAttributeType.CODE, TARGET_TIMER);
    }

    private final static Function<PropertyContainerContext, String> TARGET_SELECTOR =
            new Function<PropertyContainerContext, String>()
            {
                @edu.umd.cs.findbugs.annotations.SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
                @Override
                public String apply(PropertyContainerContext input)
                {
                    Preconditions.checkNotNull(input);
                    String attrType = input.getPropertyValues().getProperty(ATTR_TYPE);
                    return typeToProperty.getOrDefault(attrType, "");
                }
            };

    public AbstractPropertyControllerFactoryAggregatedImpl(
            @Assisted PropertyControllerFactory<Attribute, F> aggregatedPropertyFactory)
    {
        super(aggregatedPropertyFactory);
    }

    @Override
    protected void build()
    {
        register(TARGET, TARGET_SELECTOR, TARGET_CLASS, TARGET_CATALOG, DIRECT_LINK_TARGET, TARGET_TIMER,
                AGGREGATE_VALUE);
    }
}
