package ru.naumen.metainfoadmin.client.attributes;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.DialogDisplay.DialogWidth;
import ru.naumen.core.client.forms.InfoDialogDisplayImpl;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeInfoPresenterBase;

/**
 * {@link Presenter} окна просмотра информации об атрибуте
 *
 * <AUTHOR>
 * @since 20 июл. 2018 г.
 */
public class AttributeInfoPresenter extends AttributeInfoPresenterBase<AttributeInfoContentPresenter>
{
    @Inject
    public AttributeInfoPresenter(InfoDialogDisplayImpl display, EventBus eventBus,
            AttributeInfoContentPresenter contentPresenter)
    {
        super(display, eventBus);
        this.contentPresenter = contentPresenter;
    }

    @Override
    public String getTitle()
    {
        return attrMessages.attributeParameters();
    }

    public void setAttribute(Attribute attribute)
    {
        contentPresenter.setAttribute(attribute);
    }

    @Override
    protected void onBind()
    {
        // TODO возможно, тут можно обойтись без ReadyState
        ReadyState rs = new ReadyState(this);
        contentPresenter.setContext(context);
        contentPresenter.setReadyState(rs);

        super.onBind();

        getDisplay().setDialogWidth(DialogWidth.W600);
        ensureDebugId("attributeInfoModalForm");
        showDisplay(rs);
    }

    private void showDisplay(ReadyState rs)
    {
        rs.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                getDisplay().display();
            }
        });
    }
}
