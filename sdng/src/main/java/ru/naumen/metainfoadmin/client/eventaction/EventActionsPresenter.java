package ru.naumen.metainfoadmin.client.eventaction;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.EVENT_ACTIONS;

import java.util.HashSet;

import com.google.common.collect.ImmutableList;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.name.Named;

import jakarta.inject.Inject;
import ru.naumen.admin.client.advlists.AdminAdvListPresenterBase;
import ru.naumen.admin.client.permission.AccessMarkerViewPermissionControlled;
import ru.naumen.core.client.DisplayHolder;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.widgets.select.popup.PopupListSelectResources;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.client.eventaction.EventActionsPresenterSettings;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.ui.EventActionList;
import ru.naumen.metainfoadmin.client.eventaction.list.EventActionsAdvlistFactory;
import ru.naumen.metainfoadmin.shared.Constants.EventActionCommandCode;
import ru.naumen.objectlist.client.ListPresenter;

/**
 * Презентер страницы со списком действий по событию
 * <AUTHOR>
 * @since 31.07.2012
 *
 */
public class EventActionsPresenter extends AdminAdvListPresenterBase<EventActionsPlace>
        implements AccessMarkerViewPermissionControlled
{
    private final EventActionsAdvlistFactory eventActionsAdvlistFactory;

    private ListPresenter<EventActionList> listPresenter;

    private final HashSet<EventType> permittedTypes;
    private final HashSet<EventType> prohibitedTypes;
    private final ImmutableList<AttributeFqn> attrs;
    private final EventActionsPresenterSettings settings;

    @Inject
    public EventActionsPresenter(DisplayHolder display,
            EventBus eventBus,
            CommandFactory commandFactory,
            EventActionsAdvlistFactory eventActionsAdvlistFactory,
            @Named(EventActionGinModule.EVENT_ACTION_PERMITTED) HashSet<EventType> permittedTypes,
            @Named(EventActionGinModule.EVENT_ACTION_PROHIBITED) HashSet<EventType> prohibitedTypes,
            @Named(EventActionGinModule.EVENT_ACTION_ATTRS) ImmutableList<AttributeFqn> attrs,
            EventActionsPresenterSettings settings)
    {
        super(display, eventBus, commandFactory, eventActionsAdvlistFactory);
        this.permittedTypes = permittedTypes;
        this.prohibitedTypes = prohibitedTypes;
        this.attrs = attrs;
        this.eventActionsAdvlistFactory = eventActionsAdvlistFactory;
        this.settings = settings;
    }

    @Override
    public void refreshDisplay()
    {
        listPresenter.refreshDisplay();
    }

    @Inject
    protected void init(PopupListSelectResources resources)
    {
        resources.styles().ensureInjected();
    }

    @Override
    protected void onBind()
    {
        listPresenter = eventActionsAdvlistFactory.create(permittedTypes, prohibitedTypes,
                EventActionCommandCode.ADD_EVENT_ACTION, attrs, settings.isWithLinkToCards());
        registerChildPresenter(listPresenter, true);
        getDisplay().add(listPresenter.getDisplay());
    }

    @Override
    protected void afterOnBind()
    {
        refreshDisplay();
    }

    @Override
    protected void onUnbind()
    {
        if (listPresenter != null)
        {
            listPresenter.unbind();
        }
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return EVENT_ACTIONS;
    }
}