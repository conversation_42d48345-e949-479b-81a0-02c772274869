package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.clientinfo;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.factory.DefaultContentFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ClientInfo;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfoadmin.client.dynadmin.BasicUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Фабрика, возвращающая нужный ContentPresenter для контента
 * {@link ru.naumen.metainfo.shared.ui.ClientInfo информация о пользователе на ФОО}
 * <AUTHOR>
 */
public class ClientInfoFactory extends DefaultContentFactory<ClientInfo, UIContext>
{
    @Inject
    Provider<ContentPresenter<ClientInfo, UIContext>> presenterProvider;
    @Inject
    MetainfoServiceAsync metainfoService;

    @Override
    public ContentPresenter<ClientInfo, UIContext> create(Content content, Context context)
    {
        ContentPresenter<ClientInfo, UIContext> presenter = create(content);
        init(presenter, content, context);
        return presenter;
    }

    protected void init(final ContentPresenter<ClientInfo, UIContext> presenter, final Content content,
            final Context context)
    {
        final ClientInfo clientInfo = (ClientInfo)content;

        metainfoService.getMetaClass(Constants.Employee.FQN, new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass value)
            {
                UIContext uiContext = (UIContext)context;
                BasicUIContext propertyContext = new BasicUIContext(context, uiContext.getRootContentInfo(), uiContext
                        .isEditable());
                propertyContext.setMetainfo(value);
                presenter.init(clientInfo, propertyContext);
                presenter.bind();
            }
        });
    }
}
