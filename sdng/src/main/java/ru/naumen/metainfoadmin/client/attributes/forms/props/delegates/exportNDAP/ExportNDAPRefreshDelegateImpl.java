package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.exportNDAP;

import static ru.naumen.core.shared.Constants.NDAPConstants.CENTRAL_SERVER_FQN_ID;
import static ru.naumen.core.shared.Constants.NDAPConstants.CLASS_IDS;
import static ru.naumen.core.shared.Constants.NDAPConstants.SERVER_CLASS_ID;
import static ru.naumen.metainfo.shared.Constants.ATTR_TYPES_WITH_EXPORT_NDAP_ABILITY;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.METAINFO;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.iconsforcontrols.BooleanCheckBoxInfoProperty;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления свойства "Доступен из системы мониторинга"
 * <AUTHOR>
 */
public class ExportNDAPRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxInfoProperty>
{

    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxInfoProperty property,
            AsyncCallback<Boolean> callback)
    {
        //@formatter:off
        callback.onSuccess(
                isSuitableAttrType(context) &&
                isSupportadCase(context) &&
                !isComputable(context));
        //@formatter:on
    }

    private boolean isComputable(PropertyContainerContext context)
    {
        return context.getPropertyValues().getProperty(COMPUTABLE);
    }

    private boolean isSuitableAttrType(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        return ATTR_TYPES_WITH_EXPORT_NDAP_ABILITY.contains(attrType);
    }

    private boolean isSupportadCase(PropertyContainerContext context)
    {
        MetaClass metaClass = context.getContextValues().getProperty(METAINFO);
        ClassFqn fqn = metaClass.getFqn();
        return !SERVER_CLASS_ID.equals(fqn.asString()) && !CENTRAL_SERVER_FQN_ID.equals(fqn.asString())
               && CLASS_IDS.contains(fqn.getId());
    }
}
