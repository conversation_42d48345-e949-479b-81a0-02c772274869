package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.name.Named;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfoadmin.client.customforms.CustomFormGinModule;

/**
 * Фабрика по созданию представления для редактирования "типа" пользовательской формы. 
 *
 * <AUTHOR>
 * @since May 11, 2016
 */
public class AttributeCustomFormTypeWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;

    @Inject
    @Named(CustomFormGinModule.CUSTOM_FORM_NAMES)
    private List<Pair<String, String>> customFormNames;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();

        for (Pair<String, String> formType : customFormNames)
        {
            widget.addItem(new SimpleDtObject(formType.getLeft(), formType.getRight(), ClassFqn.parse("")));
        }
        callback.onSuccess(widget);
    }
}
