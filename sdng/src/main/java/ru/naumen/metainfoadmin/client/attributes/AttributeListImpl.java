package ru.naumen.metainfoadmin.client.attributes;

import static ru.naumen.core.client.menu.LeftNavPanelPresenter.NAV_CONTENT_ID;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.logging.Logger;

import com.google.common.collect.Lists;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.Node;
import com.google.gwt.dom.client.NodeList;
import com.google.gwt.dom.client.Style.Display;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.user.client.DOM;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTMLTable.Cell;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.Panel;
import com.google.gwt.user.client.ui.TabLayoutPanel;
import com.google.gwt.user.client.ui.UIObject;
import com.google.gwt.user.client.ui.Widget;

import jakarta.annotation.Nullable;
import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.ClientUtils;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.widgets.AttributeListCss;
import ru.naumen.core.client.widgets.ExpandCollapseEvent;
import ru.naumen.core.client.widgets.ExpandCollapseEventHandler;
import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.grouplist.GroupListImpl;
import ru.naumen.core.client.widgets.grouplist.TableRowComparator;
import ru.naumen.core.client.widgets.select.popup.PopupListSelectDisplayImpl;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.AddAttributeAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.ClassPresenter;
import ru.naumen.metainfoadmin.client.attributes.columns.AttrCodeCellValueExtractor;

/**
 * Таблица атрибутов метакласса
 * <AUTHOR>
 * @since 18.07.2011
 *
 */
public class AttributeListImpl extends GroupListImpl<Attribute, String> implements AttributeList,
        ExpandCollapseEventHandler
{
    private static class GroupFilter implements Predicate<Attribute>
    {
        private final String group;

        public GroupFilter(String group)
        {
            this.group = group;
        }

        @Override
        public boolean test(Attribute input)
        {
            return ObjectUtils.equals(group, GroupCodes.SYSTEM) && input.isHardcoded()
                   || ObjectUtils.equals(group, GroupCodes.FLEX) && !input.isHardcoded();
        }
    }

    private static final Logger LOG = Logger.getLogger(AttributeListImpl.class.getName());

    private static final String STICKY_HEADER_CONTAINER_DEBUG_ID = "stickyHeaderContainer";

    private static final String CSS_PROP_PADDING_LEFT = "paddingLeft";
    private static final String CSS_PROP_PADDING_RIGHT = "paddingRight";
    private static final String CSS_PROP_UNIT_PX = "px";
    private static final String TBODY_TAG_NAME = "TBODY";

    private static final int HEADER_HEIGHT_PX = 40;
    private static final int FAB_WIDTH = 16;
    private static final int ATTR_LIST_PADDING = 16;
    private static final int LEFT_MENU_WIDTH = Constants.NAV_PANEL_WIDTH + FAB_WIDTH + Constants.NAV_PANEL_GAP;
    private static final int TAB_LAYOUT_PANEL_CONTENT_PADDING = 20;
    // Время, через которое будет добавлен класс подсветки после скрытия строки
    private static final int ADD_HIGHLIGHT_CLASS_TIME = 100;
    // Время отображения подсветки для вновь добавленного атрибута
    private static final int HIGHLIGHT_TIME = 2500;

    private static final String ATTRS_TAB_DEBUG_ID = UIObject.DEBUG_ID_PREFIX + ClassPresenter.getAttributesTabCode();

    private MetainfoUtils metainfoUtils;

    private Context parentContext;

    private AttributeListCustomizer customizer;

    private FlowPanel stickyHeaderContainer;
    private FlowPanel stickyHeaderContainerContainer;

    private int headerOffset = 0;

    private Element scrollableElement = null;

    //Признак необходимости сортировать элементы списка
    private boolean enableSorting = true;

    private int windowScrollLeft = 0;
    private int windowScrollTop = 0;
    private int scrollableElementScrollLeft = 0;

    /**
     * Связь описания колонки с элементом &lt;td&gt; заголовка таблицы, непосредственно связанных с телом таблицы.
     * (то есть, находящихся либо в последнем элементе &lt;tr&gt; заголовка, либо имеющих соответствующий rowspan).
     */
    private Map<ColumnInfo<Attribute>, Element> headerLowerLevelTdElements = new HashMap<>();

    /**
     * Кнопки, свойствами которых управляет AttributeList
     */
    private Map<String, ButtonPresenter<?>> buttonsManagedByAttrList;

    /**
     * Панель, содержащая табы: "Атрибуты", "Группы атрибутов", "Карточка объекта" и т.д.
     */
    private TabLayoutPanel tabLayoutPanel = null;
    private AttributeListCss attrListCss;

    public AttributeListImpl(MetainfoUtils metainfoUtils, AttributeListCustomizer customizer)
    {
        this.metainfoUtils = metainfoUtils;
        this.customizer = customizer;
        attrListCss = AdminWidgetResources.INSTANCE.attributeList();
        attrListCss.ensureInjected();
    }

    @Override
    public void addButton(String code, ButtonToolDisplay display)
    {
        buttons.put(code, updateStyle(display));
    }

    /**
     * Сортировать список. true - список будет сортироваться
     */
    @Override
    public void enableSorting(boolean enableSorting)
    {
        this.enableSorting = enableSorting;
    }

    @Override
    public void refresh(MetaClass metainfo)
    {
        customizer.refresh(metainfo, this);
    }

    @Override
    protected void fillContent()
    {
        if (!customizer.fillContent(elements, this))
        {
            super.fillContent();
        }
    }

    @Override
    protected void fillGroupContent(Collection<Attribute> elementsWithoutAggregates)
    {
        if (!customizer.fillGroupContent(elementsWithoutAggregates, grid, this))
        {
            super.fillGroupContent(elementsWithoutAggregates);
        }
    }

    @Override
    protected void fillHeader()
    {
        header = customizer.createAndFillHeader(columns, this, registrationContainer);
        if (header == null)
        {
            super.fillHeader();
        }
        else
        {
            Element parent = customizer.useStickyHeader() ? getStickyHeaderContainer().getElement() : grid.getElement();
            DOM.insertChild(parent, header, 0);
        }
    }

    @Override
    protected void fillGroupRow(GroupInfo<String> group, int row)
    {
        customizer.fillGroupRow(group, row, buttons, grid, this);
    }

    /**
     * Создаем контекст для виджетов ячеек и передаем в контекст
     * что в этом листе необходимо использовать миниатюры а не оригинальные изображения
     */
    @Override
    protected WidgetContext<Attribute> getContext(Attribute element)
    {
        PresentationContext context = new PresentationContext(element);
        context.setUseThumbnail(true);
        context.setParentContext(parentContext);
        return context;
    }

    @Override
    protected Collection<Attribute> getGroupMembers(String group)
    {
        List<Attribute> attrs = Lists.newArrayList(elements.keySet()
                .stream()
                .filter(new GroupFilter(group))
                .iterator());
        if (enableSorting)
        {
            metainfoUtils.sort(attrs);
        }
        return attrs;
    }

    @Override
    protected void onLoad()
    {
        super.onLoad();
        if (customizer.useStickyHeader())
        {
            getStickyHeaderContainer().addStyleName(attrListCss.headerContainer());
            getStickyHeaderContainer().ensureDebugId(STICKY_HEADER_CONTAINER_DEBUG_ID);
            getStickyHeaderContainerContainer().getElement().insertFirst(getStickyHeaderContainer().getElement());
            gridContainer.getParent().getParent().getElement().insertFirst(
                    getStickyHeaderContainerContainer().getElement());
            gridContainer.addStyleName(attrListCss.gridContainer());

            Element scrollableElement = getScrollableElement();
            Event.sinkEvents(scrollableElement, Event.ONSCROLL);
            Event.setEventListener(scrollableElement, event ->
            {
                handleContainerScrollEvent();
            });

            registrationContainer.registerHandler(Window.addWindowScrollHandler(event ->
            {
                if (isAttributesTabActive() && header != null)
                {
                    if (windowScrollLeft != event.getScrollLeft() &&
                        getStickyHeaderContainer().getStyleName().contains(attrListCss.sticky()))
                    {
                        updateStickyHeaderLeft();
                        windowScrollLeft = event.getScrollLeft();
                    }

                    if (windowScrollTop != event.getScrollTop() &&
                        getStickyHeaderContainer().getStyleName().contains(attrListCss.sticky()) &&
                        headerOffset != 0)
                    {
                        updateStickyHeaderTop();
                        windowScrollTop = event.getScrollTop();
                    }
                }
            }));
        }
    }

    protected void handleContainerScrollEvent()
    {
        if (isAttributesTabActive() && header != null)
        {
            if (headerOffset == 0)
            {
                updateHeaderOffset();
            }
            Element scrollableElement = getScrollableElement();
            int elementScrollTop = scrollableElement.getScrollTop();

            //Скрываем popup'ы при скролле
            if (customizer instanceof ExtendedAttributeListCustomizer)
            {
                for (PopupListSelectDisplayImpl<?, ?> popup : ((ExtendedAttributeListCustomizer)customizer)
                        .getPopups())
                {
                    if (popup.isShowing())
                    {
                        popup.destroy();
                    }
                }
            }

            setHeaderSticky(elementScrollTop > headerOffset);

            if (scrollableElementScrollLeft != scrollableElement.getScrollLeft())
            {
                updateStickyHeaderLeft();
            }
        }
    }

    /**
     * Устанавливает состояние заголовка при необходимости: "прилипший" / скроллится вместе с таблицей
     * @param sticky true - "прилипший", false - скроллится вместе с таблицей
     */
    private void setHeaderSticky(boolean sticky)
    {
        AttributeListCss css = AdminWidgetResources.INSTANCE.attributeList();
        if (sticky && !isHeaderSticky())
        {
            getStickyHeaderContainer().addStyleName(css.sticky());
            getStickyHeaderContainerContainer().addStyleName(css.stickyContainer());
            updateStickyHeaderTop();
            fixAddButton(true);
            showManagedButtons();
            getTabLayoutPanel().addStyleName(css.bottomFadeNone());
            syncWidth();
        }
        else if (!sticky && isHeaderSticky())
        {
            getStickyHeaderContainer().removeStyleName(css.sticky());
            getStickyHeaderContainerContainer().removeStyleName(css.stickyContainer());
            fixAddButton(false);
            hideManagedButtons();
            getTabLayoutPanel().removeStyleName(css.bottomFadeNone());
            syncWidth();
        }
    }

    /**
     * "Прилип" ли заголовок?
     * @return true, если "прилип", false - иначе
     */
    private boolean isHeaderSticky()
    {
        return customizer.useStickyHeader() && header != null &&
               getStickyHeaderContainer().getElement()
                       .hasClassName(AdminWidgetResources.INSTANCE.attributeList().sticky());
    }

    /**
     * Обновляет вертикальное позиционирование для "прилипшего" заголовка с учетом скроллов окна и контейнера
     */
    private void updateStickyHeaderTop()
    {
        try
        {
            if (getStickyHeaderContainer().getStyleName()
                    .contains(AdminWidgetResources.INSTANCE.attributeList().sticky()))
            {
                int top = getScrollableElement().getOffsetTop() - Window.getScrollTop() + HEADER_HEIGHT_PX;
                getStickyHeaderContainer().getElement().getStyle().setTop(top, Unit.PX);
                getStickyHeaderContainerContainer().getElement().getStyle().setTop(0, Unit.PX);
            }
        }
        catch (Exception e)
        {
            LOG.severe(e.getMessage());
        }
    }

    // Возможно, это сейчас не используется
    private void hideManagedButtons()
    {
        if (buttonsManagedByAttrList != null)
        {
            buttonsManagedByAttrList.values().forEach(btn ->
            {
                btn.getDisplay().asWidget().removeStyleName(attrListCss.attrListManagedBtnShow());
                btn.getDisplay().asWidget().addStyleName(attrListCss.attrListManagedBtnHide());
            });
        }
    }

    // Возможно, это сейчас не используется
    private void showManagedButtons()
    {
        if (buttonsManagedByAttrList != null)
        {
            buttonsManagedByAttrList.values().forEach(btn ->
            {
                btn.getDisplay().asWidget().removeStyleName(attrListCss.attrListManagedBtnHide());
                btn.getDisplay().asWidget().addStyleName(attrListCss.attrListManagedBtnShow());
            });
        }
    }

    /**
     * При скроллинге списка атрибутов показать кнопку Добавить фиксированно
     * в нижнем правом углу
     */
    private void fixAddButton(boolean fix)
    {
        ButtonToolDisplay addButton = getAddButton();
        if (addButton == null)
        {
            return;
        }
        addButton.asWidget().setStyleName(attrListCss.fixedButton(), fix);
    }

    @Nullable
    private ButtonToolDisplay getAddButton()
    {
        String addButtonCode = GroupCodes.FLEX + "-" + ButtonCode.ADD;
        // @formatter:on
        return buttons.get(GroupCodes.FLEX)
                .stream()
                .filter(w -> w.asWidget().getElement().getId().equals(ClientUtils.getDebugId(addButtonCode)))
                .findFirst()
                .orElse(null);
        // @formatter:off
    }

    /**
     * Обновляет горизонтальное позиционирование для "прилипшего" заголовка с учетом скроллов окна и контейнера
     */
    private void updateStickyHeaderLeft()
    {
        if (getStickyHeaderContainer().getStyleName().contains(attrListCss.sticky()))
        {
            boolean isLeftPanelHidden = isLeftPanelHidden();
            getStickyHeaderContainer().getElement().getStyle().setLeft(
                    (isLeftPanelHidden ? FAB_WIDTH : LEFT_MENU_WIDTH)
                            + ATTR_LIST_PADDING + TAB_LAYOUT_PANEL_CONTENT_PADDING
                            - Window.getScrollLeft() - scrollableElement.getScrollLeft(), Unit.PX);
            getStickyHeaderContainerContainer().getElement().getStyle().setLeft(
                    (isLeftPanelHidden ? 0 : LEFT_MENU_WIDTH) - Window.getScrollLeft(), Unit.PX);

            scrollableElementScrollLeft = scrollableElement.getScrollLeft();
        }
    }

    /**
     * Синхронизирует ширину колонок тела таблицы и заголовка в случае, если заголовок "прилипающий"
     */
    @Override
    public void syncWidth()
    {
        if (customizer.useStickyHeader() && isAttributesTabActive())
        {
            clearHeaderWidth();
            for (int i = 0; i < columns.size(); i++)
            {
                Element col = grid.getCellFormatter().getElement(0, i);
                //clientWidth содержит ширину элемента внутри границ вместе с padding, но без border, прокрутки и margin
                int width = col.getClientWidth();
                Element td = headerLowerLevelTdElements.get(columns.get(i));
                if (td != null)
                {
                    td.getStyle().setWidth(width - (getStylePropertyValueInPx(td, CSS_PROP_PADDING_LEFT)
                            + getStylePropertyValueInPx(td, CSS_PROP_PADDING_RIGHT)), Unit.PX);
                }
            }
            getStickyHeaderContainer().getElement().getStyle().setWidth(grid.getOffsetWidth(), Unit.PX);
            if (getStickyHeaderContainer().getStyleName().contains(attrListCss.sticky()))
            {
                getStickyHeaderContainerContainer().getElement().getStyle().setWidth(
                        grid.getOffsetWidth(), Unit.PX);
            }
            updateStickyHeaderTop();
            updateStickyHeaderLeft();
        }
    }

    /**
     * Скрыта или нет левая панель?
     * @return true, если левая панель скрыта, false - иначе
     */
    private boolean isLeftPanelHidden()
    {
        return Display.NONE.name()
                .equalsIgnoreCase(ClientUtils.getElementByDebugId(NAV_CONTENT_ID).getStyle().getDisplay());
    }

    /**
     * Возвращает значение свойства CSS, определенное в пикселях
     *
     * @param element, значение css-свойства которого хотим получить
     * @param property название свойства
     * @return численное значение свойства CSS, определенное в пикселях или 0, если значение
     * опередено в других единицах, не определено или произошла ошибка
     */
    private int getStylePropertyValueInPx(Element element, String property)
    {
        int value;
        try
        {
            value = Integer.parseInt(getStyleProperty(element, property).replaceFirst(CSS_PROP_UNIT_PX, ""));
        }
        catch (Exception e)
        {
            value = 0;
        }
        return value;
    }

    public void setLowerLevelTdElements(ColumnInfo<Attribute> columnInfo, Element element)
    {
        headerLowerLevelTdElements.put(columnInfo, element);
    }

    /**
     * Возвращает элемент-контейнер для списка атрибутов (TabLayoutPanelContentContainer), который содержит скролл.
     * @return элемент-контейнер для списка атрибутов со скроллом
     */
    private Element getScrollableElement()
    {
        if (scrollableElement == null)
        {
            scrollableElement = this.getElement().getParentElement();
            while (scrollableElement != null)
            {
                if (scrollableElement.getClassName().contains(TabLayoutPanel.CONTENT_CONTAINER_STYLE))
                {
                    break;
                }
                else
                {
                    scrollableElement = scrollableElement.getParentElement();
                }
            }
        }
        return scrollableElement;
    }

    private int getHeaderOffsetTop(Element targetElement)
    {
        return targetElement.getAbsoluteTop();
    }

    private ButtonToolDisplay updateStyle(ButtonToolDisplay display)
    {
        display.addStyleName(WidgetResources.INSTANCE.buttons().buttonBothSide());
        return display;
    }

    @Override
    public void setParentContext(Context parentContext)
    {
        this.parentContext = parentContext;
    }

    @Override
    public void clearAll()
    {
        super.clear();
        columns.clear();
    }

    @Override
    protected IsWidget generateGroupTitle(GroupInfo<String> group)//NOPMD
    {
        //Сделано для того, чтобы AttributeListCustomizer имел доступ к методу
        return super.generateGroupTitle(group);
    }

    @Override
    protected void fillRow(int row, Attribute element)//NOPMD
    {
        //Сделано для того, чтобы AttributeListCustomizer имел доступ к методу
        super.fillRow(row, element);
    }

    @Override
    protected int insertRow()//NOPMD
    {
        //Сделано для того, чтобы AttributeListCustomizer имел доступ к методу
        return super.insertRow();
    }

    @Override
    protected void insertCell(int beforeRow, int beforeColumn)
    {
        super.insertCell(beforeRow, beforeColumn);
        String hintTitle = columns.get(beforeColumn) == null ? "" : columns.get(beforeColumn).getHintTitle();
        if (!StringUtilities.isEmpty(hintTitle))
        {
            grid.getCellFormatter().getElement(beforeRow, beforeColumn).setTitle(hintTitle);
        }
    }

    @Override
    protected Optional<Attribute> getValueFromCell(Cell cell)
    {
        Element firstTd = grid.getCellFormatter().getElement(cell.getRowIndex(), 0);
        String attrCode = new AttrCodeCellValueExtractor().extractValue(firstTd);
        return elements.entrySet().stream()
                .filter(entry -> entry.getKey().getCode().equals(attrCode))
                .map(Map.Entry::getKey)
                .findFirst();
    }

    @Override
    public void sortTable(TableRowComparator<?> rowComparator)
    {
        customizer.sortTable(rowComparator, this);
    };

    protected Element getTBodyElement()
    {
        Element result = grid.getElement();
        for (int i = 0; i < result.getChildCount(); i++)
        {
            if (TBODY_TAG_NAME.equalsIgnoreCase(result.getChild(i).getNodeName()))
            {
                return (Element)result.getChild(i);
            }
        }
        return result;
    }

    /**
     * Возвращает количество строк (элементов tr) в заголовке таблицы
     * @return
     */
    protected int getHeaderRowsCount()
    {
        return header != null ? header.getChildCount() : 0;
    }

    protected int getRowCount()
    {
        return grid.getRowCount();
    }

    protected Element getRow(int rowIndex)
    {
        return grid.getRowFormatter().getElement(rowIndex);
    }

    public static native String getStyleProperty(Element el, String prop)
    /*-{
        var computedStyle;
        if (document.defaultView && document.defaultView.getComputedStyle)
        { // standard (includes ie9)
            computedStyle = document.defaultView.getComputedStyle(el, null)[prop];
        }
        else if (el.currentStyle)
        { // IE older
            computedStyle = el.currentStyle[prop] || 0;
            // we use 'left' property as a place holder so backup values
            var leftCopy = el.style.left;
            var runtimeLeftCopy = el.runtimeStyle.left;

            // assign to runtimeStyle and get pixel value
            el.runtimeStyle.left = el.currentStyle.left;
            el.style.left = (prop === "fontSize") ? "1em" : computedStyle;
            computedStyle = el.style.pixelLeft + "px";

            // restore values for left
            el.style.left = leftCopy;
            el.runtimeStyle.left = runtimeLeftCopy;
        }
        else
        { // inline style
            computedStyle = el.style[prop];
        }
        return computedStyle;
    }-*/;

    @Override
    public void onExpandCollapse(ExpandCollapseEvent event)
    {
        if (isAttributesTabActive())
        {
            updateHeaderOffset();
            if (event.isExpand())
            {
                syncWidth();
            }
        }
    }

    /**
     * Возвращает FlowPanel (&lt;div/&gt;), в который вложена таблица заголовка
     * @return FlowPanel, содержащий заголовок таблицы атрибутов
     */
    private FlowPanel getStickyHeaderContainer()
    {
        if (stickyHeaderContainer == null)
        {
            stickyHeaderContainer = new FlowPanel();
        }
        return stickyHeaderContainer;
    }

    /**
     * Возвращает FlowPanel (&lt;div/&gt;), в который вложен <b>контейнер</b>,
     * содержащий заголовок таблицы атрибутов.
     * Он нужен для возможности определения области отображения в случае, если заголовок "прилип" и
     * происходит скроллинг области основного контента.
     * Все, что не помещается в эту область, будет обрезано и становится невидимым.
     *
     * @return FlowPanel, в который вложен <b>контейнер</b> заголовока таблицы атрибутов.
     */
    private FlowPanel getStickyHeaderContainerContainer()
    {
        if (stickyHeaderContainerContainer == null)
        {
            stickyHeaderContainerContainer = new FlowPanel();
        }
        return stickyHeaderContainerContainer;
    }

    private boolean isAttributesTabActive()
    {
        if (getTabLayoutPanel() != null && getTabLayoutPanel().getSelectedIndex() >= 0 &&
                getTabLayoutPanel().getTabWidget(getTabLayoutPanel().getSelectedIndex()) != null)
        {
            return ATTRS_TAB_DEBUG_ID.equals(getTabLayoutPanel().getTabWidget(getTabLayoutPanel().getSelectedIndex())
                    .getElement().getId());
        }
        return false;
    }

    private TabLayoutPanel getTabLayoutPanel()
    {
        if (tabLayoutPanel == null)
        {
            Widget parent = getParent();
            while (parent != null)
            {
                if (parent instanceof TabLayoutPanel)
                {
                    tabLayoutPanel = (TabLayoutPanel)parent;
                    break;
                }
                parent = parent.getParent();
            }
        }
        return tabLayoutPanel;
    }

    public void clearHeaderWidth()
    {
        getStickyHeaderContainer().getElement().getStyle().clearWidth();
        getStickyHeaderContainerContainer().getElement().getStyle().clearWidth();
    }

    public IsWidget[] getControlledWidget()
    {
        return customizer.useStickyHeader() ? new IsWidget[] { this, getStickyHeaderContainer() }
                : new IsWidget[] { this };
    }

    private void updateHeaderOffset()
    {
        if (customizer.useStickyHeader() && header != null && header.getAbsoluteTop() > 0)
        {
            headerOffset = getHeaderOffsetTop(header);
        }
        updateStickyHeaderTop();
    }

    @Override
    public void setManagedButtons(Map<String, ButtonPresenter<?>> buttonsManagedByAttrList)
    {
        if (buttonsManagedByAttrList != null)
        {
            this.buttonsManagedByAttrList = buttonsManagedByAttrList;
            buttonsManagedByAttrList.values().forEach(btn ->
            {
                if (btn.getDisplay().asWidget().getParent() == null)
                {
                    ((Panel)this.getParent()).add(btn.getDisplay().asWidget());
                    btn.getDisplay().asWidget().addStyleName(attrListCss.attrListManagedBtnHide());
                }
            });
        }
    }

    /**
     * Получение кода только что добавленного атрибута.
     * В случае, если было выполнено действие добавления атрибута, в parentContext должна быть ссылка на
     * это действие. Это необходимо для возможности подсветки вновь добавленного атрибута при
     * обновлении списка.
     * @return код вновь добавленного атрибута или null, если контент обновляется не в результате
     * действия по добавлению атрибута.
     */
    @SuppressWarnings("unchecked")
    public String getAddedAttributeCode()
    {
        AddAttributeAction action = null;
        if (parentContext.getContextProperty(ClassPresenter.PRM_SOURCE_ACTIONS) instanceof List)
        {
            action = (AddAttributeAction)((List<Action<?>>)parentContext.getContextProperty(
                    ClassPresenter.PRM_SOURCE_ACTIONS)).stream().filter(
                            a -> a instanceof AddAttributeAction).findFirst().orElse(null);
        }
        return action == null ? null : action.getCode();
    }

    /**
     * Удаляет исходное действие из контекста. Наличие в контексте исходного действия обычно указывает на необходимость
     * совершения разовой операции (например, подсветить атрибут в списке при добавлении нового втрибута).
     */
    @SuppressWarnings("unchecked")
    public void removeAddAttributeActionFromContext()
    {
        if (parentContext.getContextProperty(ClassPresenter.PRM_SOURCE_ACTIONS) instanceof List)
        {
            ((List<Action<?>>)parentContext.getContextProperty(
                    ClassPresenter.PRM_SOURCE_ACTIONS)).removeIf(a -> a instanceof AddAttributeAction);
        }
    }

    /**
     * Размещает элемент (строку таблицы атрибутов) на странице так, чтобы он был видимым,
     * скрывает его, а затем плавно переводит в видимый режим.
     * @param attributeCode код элемента
     */
    public void scrollToAttributeAndHighlightIt(String attributeCode)
    {
        AttributeListCss css = AdminWidgetResources.INSTANCE.attributeList();
        Element el = findRowElementByAttributeCode(attributeCode);
        if (el != null)
        {
            el.scrollIntoView();
            el.addClassName(css.attrListHighlightedRowHidden());

            Timer timer = new Timer()
            {
                @Override
                public void run()
                {
                    el.addClassName(css.attrListHighlightedRow());
                }
            };
            timer.schedule(ADD_HIGHLIGHT_CLASS_TIME);

            Timer removeHighlightTimer = new Timer()
            {
                @Override
                public void run()
                {
                    el.removeClassName(css.attrListHighlightedRowHidden());
                    el.removeClassName(css.attrListHighlightedRow());
                }
            };
            removeHighlightTimer.schedule(HIGHLIGHT_TIME);
        }
    }

    /**
     * Ищет {@link Element} соответствующий строке (&lt;tr&gt;) для атрибута с кодом {code}
     *
     * @param code код атрибута
     * @return строку для указанного атрибута либо null, если она не найдена
     */
    private Element findRowElementByAttributeCode(String code)
    {
        if (code != null)
        {
            NodeList<Node> nodes = grid.getElement().getLastChild().getChildNodes();
            for (int i = 0; i < nodes.getLength(); i++)
            {
                if (code.equals(((Element)nodes.getItem(i)).getAttribute(ROW_CODE_ATTRIBUTE)))
                {
                    return (Element)nodes.getItem(i);
                }
            }
        }
        return null;
    }

    @Override
    public void setHeaderVisible(boolean isVisible)
    {
        if (isHeaderSticky())
        {
            if (isVisible)
            {
                header.getStyle().clearDisplay();
            }
            else
            {
                header.getStyle().setDisplay(Display.NONE);
            }
        }
    }
}