package ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.delegates;

import static ru.naumen.metainfo.shared.eventaction.ActionType.PushMobileEventAction;
import static ru.naumen.metainfoadmin.client.escalation.actions.EscalationActionsGinModule.ESCALATION_EVENT_ACTION_PERMITTED;
import static ru.naumen.metainfoadmin.client.escalation.actions.EscalationActionsGinModule.ESCALATION_EVENT_ACTION_PROHIBITED;
import static ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinModule.EscalationSchemeLevelContextValueCode.ESCALATION_SCHEME;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.name.Named;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.clselect.ValueToSelectItemConverter;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.eventaction.ActionTypesProvider;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;

/**
 * <AUTHOR>
 * @since 23.08.2012
 *
 */
public class EscalationSchemeLevelActionBindDelegateImpl extends
        PropertyDelegateBindImpl<Collection<SelectItem>, MultiSelectBoxProperty>
{
    private final AdminMetainfoServiceAsync metainfoService;
    private final I18nUtil i18nUtil;
    private final HashSet<EventType> permittedEventTypes;
    private final HashSet<EventType> prohibitedEventTypes;
    private final ActionTypesProvider actionTypesProvider;

    @Inject
    public EscalationSchemeLevelActionBindDelegateImpl(
            final AdminMetainfoServiceAsync metainfoService,
            final I18nUtil i18nUtil,
            final @Named(ESCALATION_EVENT_ACTION_PERMITTED) HashSet<EventType> permittedEventTypes,
            final @Named(ESCALATION_EVENT_ACTION_PROHIBITED) HashSet<EventType> prohibitedEventTypes,
            final ActionTypesProvider actionTypesProvider)
    {
        this.metainfoService = metainfoService;
        this.i18nUtil = i18nUtil;
        this.permittedEventTypes = permittedEventTypes;
        this.prohibitedEventTypes = prohibitedEventTypes;
        this.actionTypesProvider = actionTypesProvider;
    }

    @Override
    public void bindProperty(final PropertyContainerContext context, final MultiSelectBoxProperty property,
            final AsyncCallback<Void> callback)
    {
        final List<ClassFqn> targetTypes =
                context.getContextValues().<EscalationScheme> getProperty(ESCALATION_SCHEME).getTargetTypes().stream()
                        .map(DtObject.FQN_EXTRACTOR)
                        .collect(Collectors.toList());

        metainfoService.getEventActions(permittedEventTypes, prohibitedEventTypes, 1, targetTypes,
                new BasicCallback<List<EventAction>>()
                {
                    @Override
                    protected void handleSuccess(final List<EventAction> eventActions)
                    {
                        final boolean hasMobileModule =
                                actionTypesProvider.getAvailableActionTypes().contains(PushMobileEventAction);
                        final MultiSelectCellList<String> valueWidget = property.getValueWidget();
                        final List<SelectItem> disabledItems = new ArrayList<>(eventActions.size());
                        for (final EventAction eventAction : eventActions)
                        {
                            final String title = i18nUtil.getLocalizedTitle(eventAction);
                            final String code = eventAction.getCode();
                            if (!hasMobileModule
                                && PushMobileEventAction.equals(eventAction.getAction().getActionType()))
                            {
                                disabledItems.add(ValueToSelectItemConverter.convert(title, code, false, false, 0));
                                continue;
                            }
                            valueWidget.addItem(title, code);
                        }
                        // добавляем неактивные элементы после активных, чтобы они хранились в propertyContainer и
                        // метаинформация не изменилась
                        if (!disabledItems.isEmpty())
                        {
                            disabledItems.forEach(valueWidget::addItem);
                        }
                        callback.onSuccess(null);
                    }
                }
        );
    }
}
