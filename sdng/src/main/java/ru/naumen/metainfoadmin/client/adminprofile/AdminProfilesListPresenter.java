package ru.naumen.metainfoadmin.client.adminprofile;

import java.util.Set;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.admin.client.advlists.AdminAdvListPresenterBase;
import ru.naumen.core.client.DisplayHolder;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.adminprofile.command.AdminProfileCommandCode;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionHandler;

/**
 * Презентер списка с профилями администрирования
 * <AUTHOR>
 * @since 12.02.2023
 */
public class AdminProfilesListPresenter extends AdminAdvListPresenterBase<DtObject> implements ObjectListActionHandler
{
    @Inject
    public AdminProfilesListPresenter(DisplayHolder display,
            EventBus eventBus,
            CommandFactory commandFactory,
            AdminProfilesAdvListFactory advlistFactory)
    {
        super(display, eventBus, commandFactory, advlistFactory);
    }

    @Override
    protected Set<String> getActionCommands()
    {
        return AdminProfileCommandCode.COMMANDS_IN_LIST;
    }
}