package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления свойства "Уровень иерархии связанного объекта".
 * <AUTHOR>
 * @since 11.09.18
 */
public class RelatedObjectHierarchyLevelRefreshDelegateImpl<F extends ObjectForm>
        implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    @Inject
    private AttributesMessages attributesMessages;

    @Override
    public void refreshProperty(PropertyContainerContext context,
            ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        IProperties propertyValues = context.getPropertyValues();

        String typeCode = propertyValues.getProperty(AttributeFormPropertyCode.ATTR_TYPE);

        String savedValue = context.getPropertyValues()
                .<String> getProperty(AttributeFormPropertyCode.RELATED_OBJECT_HIERARCHY_LEVEL);
        String relatedObjectAttribute = context.getPropertyValues()
                .getProperty(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE);

        if (!typeCode.equals(AttributeOfRelatedObjectSettings.CODE))
        {
            callback.onSuccess(false);
            return;
        }

        List<AttributeFqn> attrChain = convertToAttrList(
                context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_CHAIN));
        if (attrChain.isEmpty())
        {
            callback.onSuccess(false);
            return;
        }

        metainfoService.isSingleObjectLinkAttrChain(attrChain, new BasicCallback<Boolean>()
        {
            @Override
            protected void handleSuccess(Boolean isSingleObjectLink)
            {
                if (!isSingleObjectLink)
                {
                    callback.onSuccess(false);
                    return;
                }

                // Необходимо убедиться в том, что в цепочке атрибутов выбран атрибут класса, вложенного в себя
                String relatedFqn = propertyValues.getProperty(AttributeFormPropertyCode.RELATED_OBJECT_METACLASS);
                if (relatedFqn == null)
                {
                    callback.onSuccess(false);
                    return;
                }

                metainfoService.getMetaClass(ClassFqn.parse(relatedFqn), new BasicCallback<MetaClass>()
                {
                    @Override
                    protected void handleSuccess(MetaClass metaClass)
                    {
                        boolean isNestedInSelf = false;
                        if (metaClass.hasAttribute(Constants.PARENT_ATTR))
                        {
                            Attribute attribute = metaClass.getAttribute(Constants.PARENT_ATTR);
                            ClassFqn parentFqn = attribute.getType().<ObjectAttributeType> cast().getRelatedMetaClass()
                                    .fqnOfClass();
                            isNestedInSelf = parentFqn.isSameClass(metaClass.getFqn());
                        }

                        if (isNestedInSelf)
                        {
                            SingleSelectCellList<String> selectList = property.getValueWidget();
                            selectList.clear();
                            if (relatedObjectAttribute == null || !AttributeFqn.parse(relatedObjectAttribute)
                                    .getCode()
                                    .equals(Constants.PARENT_ATTR))
                            {
                                selectList.addItem(attributesMessages.parentHierarchy0(), "0");
                            }
                            selectList.addItem(attributesMessages.parentHierarchy1(), "1");
                            selectList.addItem(attributesMessages.parentHierarchy2(), "2");
                            selectList.addItem(attributesMessages.parentHierarchy3(), "3");
                            for (int i = 4; i <= 10; i++)
                            {
                                String valueStr = Integer.toString(i);
                                selectList.addItem(attributesMessages.parentHierarchyN(valueStr), valueStr);
                            }
                            selectList.addItem(attributesMessages.parentHierarchyTop(), "-1");
                            if (savedValue != null)
                            {
                                property.trySetObjValue(savedValue);
                            }
                            callback.onSuccess(true);
                        }
                        else
                        {
                            callback.onSuccess(false);
                        }
                    }
                });
            }
        });
    }

    /**
     * Преобразует дерево выбора атрубута связи в список атрибутов
     */
    private List<AttributeFqn> convertToAttrList(RelationsAttrTreeObject attr)
    {
        ArrayList<AttributeFqn> attrList = new ArrayList<>();
        while (attr != null)
        {
            Attribute attribute = attr.getAttribute();
            attrList.add(new AttributeFqn(attribute.getMetaClassLite().getFqn(), attribute.getCode()));
            attr = attr.getParent();
        }
        Collections.reverse(attrList);
        return attrList;
    }
}
