package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfoadmin.client.catalog.item.CatalogItemPlace;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInValueMap;

/**
 * Представление для отображения значения места использования "Таблица соответствий" на форме "Используется в
 * настройках" в таблице атрибутов
 * <AUTHOR>
 * @since 3 Jul 18
 */
@Singleton
public class AttributeUsageInValueMapHtmlFactoryImpl extends AttributeHtmlFactoryImpl<AttributeUsageInValueMap>
{
    @Inject
    private Formatters formatters;
    @Inject
    private CommonMessages messages;
    @Inject
    private PlaceHistoryMapper historyMapper;

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInValueMap usage)
    {
        return new SafeHtmlBuilder().append(formatters.formatHyperlinkAsHtml(createLinkToValueMap(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLinkToValueMap(AttributeUsageInValueMap usage)
    {
        CatalogItemPlace catalogItemPlace = new CatalogItemPlace(usage.getCode());
        //@formatter:off
        return new Hyperlink(
                usage.isSource() ? messages.valueMapSourceUsagePlace() + " \"" + usage.getTitle() + "\""
                                 : messages.valueMapTargetUsagePlace() + " \"" + usage.getTitle() + "\"", 
                StringUtilities.getHrefByToken(historyMapper.getToken(catalogItemPlace)));
        //@formatter:on
    }
}