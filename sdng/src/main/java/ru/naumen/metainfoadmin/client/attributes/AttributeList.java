package ru.naumen.metainfoadmin.client.attributes;

import java.util.Map;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.client.widgets.grouplist.GroupList;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Интерфейс таблицы атрибутов метакласса
 * <AUTHOR>
 *
 */
public interface AttributeList extends GroupList<Attribute, String>
{
    interface GroupCodes
    {
        String SYSTEM = "system";

        String FLEX = "flex";
    }

    void addButton(String groupCode, ButtonToolDisplay display);

    /**
     * Обновляет таблицу по данным метакласса
     * @param metainfo
     */
    void refresh(MetaClass metainfo);

    void enableSorting(boolean enableSorting);

    void setParentContext(Context parentContext);

    void clearAll();

    /**
     * Синхронизирует ширину колонок тела таблицы и заголовка в случае, если заголовок "прилипающий" 
     */
    default void syncWidth()
    {
        //По умолчанию, заголовок является частью таблицы и не нуждается в синхронизации ширины колонок.
    }

    /**
     * Устанавливает кнопки, свойствами которых управляет AttributeList в зависимости от
     * текущего состояния (например, наличия/отсутствия "прилипшего" заголовка).
     * При необходимости добавляет их в DOM  
     * @param buttonsManagedByAttrList - кнопки
     */
    default void setManagedButtons(Map<String, ButtonPresenter<?>> buttonsManagedByAttrList)
    {
        //По умолчанию данная функциональность не используется
    }

    /**
     * Установить видимость "прилипающего" заголовка
     * @param isVisible true - заголовок видимый, false - иначе
     */
    default void setHeaderVisible(boolean isVisible)
    {
        //По умолчанию данная функциональность не используется
    }
}
