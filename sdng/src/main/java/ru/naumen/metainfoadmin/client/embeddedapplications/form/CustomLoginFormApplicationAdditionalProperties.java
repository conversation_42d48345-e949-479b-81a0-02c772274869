package ru.naumen.metainfoadmin.client.embeddedapplications.form;

import java.util.List;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.dispatch2.script.GetScriptModulesAction;
import ru.naumen.metainfo.shared.embeddedapplication.CustomLoginFormApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;

/**
 * Дополняет презентер формы приложения свойствами, специфичными для пользовательской формы входа в мобильное приложение
 *
 * <AUTHOR>
 * @since 24.06.2021
 */
public class CustomLoginFormApplicationAdditionalProperties extends InternalApplicationAdditionalProperties
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private ListBoxWithEmptyOptProperty scriptModule;
    @Inject
    private EmbeddedApplicationMessages messages;
    @Inject
    private NotNullValidator<SelectItem> notNullValidator;

    private PropertyRegistration<SelectItem> scriptModulePR;

    @Override
    public void addAdditionalProperties(PropertyDialogDisplay display, PropertyRegistration<?> previousProperty,
            RegistrationContainer registrationContainer)
    {
        scriptModule.setCaption(messages.scriptModule());
        scriptModule.setValidationMarker(true);
        DebugIdBuilder.ensureDebugId(scriptModule, "scriptModule");

        SingleSelectCellList<String> select = scriptModule.getValueWidget();
        select.setHasEmptyOption(false);
        dispatch.execute(new GetScriptModulesAction(), new BasicCallback<SimpleResult<List<SimpleDtObject>>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<List<SimpleDtObject>> response)
            {
                response.get().forEach(scriptModuleDto -> select.addItem(new SelectItem(scriptModuleDto)));
            }
        });

        validation.validate(scriptModule, notNullValidator);
        scriptModulePR = display.addProperty(scriptModule, display.getPropertiesCount());
        super.addAdditionalProperties(display, previousProperty, registrationContainer);
    }

    @Override
    public EmbeddedApplication createApplication()
    {
        CustomLoginFormApplication application = new CustomLoginFormApplication();
        application.setApplicationType(EmbeddedApplicationType.CustomLoginFormApplication);
        return application;
    }

    @Override
    public Boolean getValueOfAdditionalProperties(EmbeddedApplicationAdminSettingsDto embeddedApplication)
    {
        if (embeddedApplication.getEmbeddedApplicationType() == getApplicationType())
        {
            return super.getValueOfAdditionalProperties(embeddedApplication);
        }
        return true;
    }

    @Override
    public void fillAdditionalProperties(EmbeddedApplicationAdminSettingsDto embeddedApplication)
    {
        if (embeddedApplication.getEmbeddedApplicationType() == getApplicationType())
        {
            super.fillAdditionalProperties(embeddedApplication);
        }
    }

    @Override
    public void removeAdditionalProperties()
    {
        if (scriptModulePR != null)
        {
            validation.unvalidate(scriptModule);
            scriptModulePR.unregister();
            scriptModulePR = null;
        }
    }

    @Override
    protected boolean showScript()
    {
        return false;
    }

    @Override
    protected EmbeddedApplicationType getApplicationType()
    {
        return EmbeddedApplicationType.CustomLoginFormApplication;
    }
}
