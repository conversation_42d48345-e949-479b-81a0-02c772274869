package ru.naumen.metainfoadmin.client.attributes.columns;

import ru.naumen.admin.client.widgets.AdminWidgetResources;

/**
 * Получения кода атрибута из уже отрисованной ячейки таблицы.
 *
 * <AUTHOR>
 * @since 1 окт. 2018 г.
 *
 */
public class AttrCodeCellValueExtractor extends ByTagAndClassNameCellValueExtractor
{
    @Override
    String getTagName()
    {
        return TAG_NAME_SPAN;
    }

    @Override
    String getClassName()
    {
        return AdminWidgetResources.INSTANCE.attributeList().codeBadge();
    }
}
