package ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.Window;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataAction;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.interfacesettings.dispatch.SaveThemeAction;
import ru.naumen.core.shared.personalsettings.ThemeClient;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsPlace;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms.InterfaceThemeGinModule.ThemeFormPartPresenterFactory;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.ThemePlace;

/**
 * Базовый презентер форм добавления и редактирования тем
 *
 * <AUTHOR>
 * @since 19.07.16
 */
public abstract class ThemeFormPresenterBase<P extends ThemePlace> extends OkCancelPresenter<PropertyFormDisplay>
{
    static final String STANDART_CHOICE = "standart";
    static final String FROM_FILE_CHOICE = "fromfile";

    @Inject
    protected Processor validation;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private PlaceController placeController;
    @Inject
    private ThemeFormPartPresenterFactory partPresenterFactory;
    @Inject
    private MetainfoUtils metainfoUtils;

    protected P place;
    protected ThemeClient theme;

    protected ThemeFormCommonParametersPresenter commonParameters;
    protected ThemeFormStyleParametersPresenter styleParameters;

    @Inject
    public ThemeFormPresenterBase(PropertyFormDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(P place)
    {
        this.place = place;
    }

    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        dispatch.execute(createAction(), new BasicCallback<GetInterfaceTabDataResponse>()
        {
            @Override
            protected void handleSuccess(GetInterfaceTabDataResponse response)
            {
                placeController.goTo(InterfaceSettingsPlace.INSTANCE);
                if (ObjectUtils.equals(response.getSettings().getThemeAdmin(), commonParameters.getCode().getValue()))
                {
                    Window.Location.reload();
                }
            }
        });
    }

    public void onCancel()
    {
        placeController.goTo(InterfaceSettingsPlace.INSTANCE);
    }

    protected SaveThemeAction createAction()
    {
        List<LocalizedString> localizedTitle = new ArrayList<>();
        metainfoUtils.setLocalizedValue(localizedTitle, commonParameters.getTitle().getValue());
        boolean isLogoStandart = STANDART_CHOICE.equals(getValue(commonParameters.getLogoFileType()));
        boolean isLoginLogoStandart = STANDART_CHOICE.equals(getValue(commonParameters.getLogoLoginWindowFileType()));
        // @formatter:off
        return new SaveThemeAction(
            commonParameters.getCode().getValue(),
            localizedTitle,
            isLogoStandart,
            isLogoStandart ? null : getUploadFileUuid(commonParameters.getLogoFileUpload()),
            isLoginLogoStandart,
            isLoginLogoStandart ? null : getUploadFileUuid(commonParameters.getLogoLoginWindowFileUpload()),
            commonParameters.getEnabledForUsers().getValue(),
            styleParameters == null ? null : styleParameters.getUploadFileUuid());
        // @formatter:on
    }

    /**
     * Получить значение из селекта
     */
    private <T> Object getValue(Property<T> select)
    {
        return SelectListPropertyValueExtractor.getValue(select);
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        dispatch.execute(new GetInterfaceTabDataAction(), new BasicCallback<GetInterfaceTabDataResponse>(readyState)
        {
            @Override
            protected void handleSuccess(GetInterfaceTabDataResponse response)
            {
                theme = getTheme(response);
            }
        });
    }

    protected abstract ThemeClient getTheme(GetInterfaceTabDataResponse context);

    @Override
    protected void onBind()
    {
        super.onBind();

        commonParameters = partPresenterFactory.createCommonParametersBlock(getDisplay(), validation, theme);
        commonParameters.bind();
        if (!theme.isSystem())
        {
            styleParameters = partPresenterFactory.createStyleParametersBlock(getDisplay(), validation, theme);
            styleParameters.bind();
        }
    }

    private static String getUploadFileUuid(Property<Collection<DtObject>> fileUpload)
    {
        if (fileUpload.getValue() == null || fileUpload.getValue().isEmpty())
        {
            return null;
        }
        return fileUpload.getValue().iterator().next().getUUID();
    }
}