package ru.naumen.metainfoadmin.client.eventaction.form.creator.event;

import ru.naumen.core.client.FormPropertiesCreator;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfo.shared.eventaction.Event;

import jakarta.inject.Inject;

/**
 * <AUTHOR>
 * @since 09.02.2012
 *
 */
public abstract class AbstractEventFormPropertiesCreator<T extends Event> extends FormPropertiesCreator implements
        EventFormPropertiesCreator<T>
{
    private int startIndex = 0;

    @Inject
    Processor validation;

    @Override
    public void addProperties(PropertyDialogDisplay display)
    {
        if (null != properties)
        {
            int counter = 0;
            for (Property<?> p : properties)
            {
                register(p, display.addProperty(p, startIndex + counter++));
            }
        }
        afterAddProperties(display);
        display.updateTabOrder();
    }

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {

    }

    @Override
    public int getPropertiesCount()
    {
        return properties.size();
    }

    @Override
    public void setStartIndex(int startIndex)
    {
        this.startIndex = startIndex;
    }
}
