package ru.naumen.metainfoadmin.client.structuredobjectsviews.card;

import static com.google.gwt.place.shared.Place.NOWHERE;
import static ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.MassEditToolPanelBlockPresenter.PANEL_DISABLED;
import static ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.MassEditToolPanelBlockPresenter.USE_SYSTEM_SETTINGS;
import static ru.naumen.metainfoadmin.client.structuredobjectsviews.items.forms.StructuredObjectsViewItemFormMassOperationPresenter.ITEM_CODE;
import static ru.naumen.metainfoadmin.client.structuredobjectsviews.items.forms.StructuredObjectsViewItemFormMassOperationPresenter.NOT_USE_MASS_OPERATION;

import java.util.HashMap;
import java.util.List;
import java.util.function.Function;

import java.util.ArrayList;

import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safecss.shared.SafeStyles;
import com.google.gwt.safecss.shared.SafeStylesBuilder;
import com.google.gwt.safecss.shared.SafeStylesUtils;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.RowStyles;
import com.google.gwt.view.client.AbstractDataProvider;
import com.google.gwt.view.client.HasData;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.columns.LineTextCell;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumnFactory;
import ru.naumen.core.client.widgets.columns.LinkToPlaceWithIndentColumn;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.StructuredObjectsView;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.StructuredObjectsViewItemClient;
import ru.naumen.metainfoadmin.client.CatalogCellTableResources.CatalogCellTableStyle;
import ru.naumen.metainfoadmin.client.SimpleTableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableResources;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsMessages;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.StructuredObjectsViewItemsCommandCode;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.commands.StructuredObjectsViewItemsCommandParam;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Блок "Элементы структуры" на форме настройки массового редактирования
 * <AUTHOR>
 * @since 14.12.2020.
 */
public class StructuredObjectsViewItemsForMassOperationsPresenter extends BasicPresenter<SimpleTableDisplay<StructuredObjectsViewItemClient>>
{
    class ItemsDataProvider extends AbstractDataProvider<StructuredObjectsViewItemClient>
    {
        @Override
        protected void onRangeChanged(final HasData<StructuredObjectsViewItemClient> display)
        {
            List<StructuredObjectsViewItemClient> itemsList = new ArrayList<>();
            List<StructuredObjectsViewItemClient> items = structuredObjectsView.getProperty(
                    StructuredObjectsView.ITEMS);
            if (!items.isEmpty())
            {
                reqFillList(items, itemsList);
            }
            display.setRowData(0, itemsList);
            display.setRowCount(itemsList.size(), true);
        }

        private void reqFillList(Iterable<StructuredObjectsViewItemClient> items,
                List<StructuredObjectsViewItemClient> result)
        {
            items.forEach(item ->
            {
                if (structuredObjectsView.getProperty(PANEL_DISABLED, false))
                {
                    notUseMassOperationMap.put(item.getCode(), true);
                }
                else if (!notUseMassOperationMap.containsKey(item.getCode()))
                {
                    notUseMassOperationMap.put(item.getCode(), false);
                }
                result.add(item);
                reqFillList(item.getChildren(), result);
            });
            structuredObjectsView.setProperty(NOT_USE_MASS_OPERATION_MAP, notUseMassOperationMap);
        }
    }

    class ItemsRowStyles implements RowStyles<StructuredObjectsViewItemClient>
    {
        @Override
        public String getStyleNames(StructuredObjectsViewItemClient item, int rowIndex)
        {
            CatalogCellTableStyle cellTableStyle = cellTableResources.cellTableStyle();
            return item.getLevel() == 0 ? cellTableStyle.folderRow() : null;
        }
    }

    private final Function<StructuredObjectsViewItemClient, SafeStyles> TITLE_STYLES = input ->
    {
        SafeStylesBuilder sb = new SafeStylesBuilder();
        Integer level = input.getLevel();
        if (level == 0)
        {
            sb.appendTrustedString("font-weight: 800;");
        }
        else
        {
            sb.append(SafeStylesUtils.forMarginLeft(level * 20, Unit.PX));
        }
        return sb.toSafeStyles();
    };

    public final static String NOT_USE_MASS_OPERATION_MAP = "notUseMassOperationMap";

    private final ObjectListColumnBuilder tableBuilder;
    private final LinkToPlaceColumnFactory<StructuredObjectsViewItemClient> linkToPlaceColumnFactory;
    private final WithArrowsCellTableResources cellTableResources;
    private final StructuredObjectsViewsMessages structuredObjectsViewsMessages;

    private DtObject structuredObjectsView;
    private StructuredObjectsViewItemsCommandParam param;
    private HashMap<String, Boolean> notUseMassOperationMap;

    private final OnStartCallback<DtObject> refreshCallback = new SafeOnStartBasicCallback<DtObject>(
            getDisplay())
    {
        @Override
        protected void handleSuccess(@Nullable DtObject value)
        {
            if (value != null && value.getProperty(NOT_USE_MASS_OPERATION) != null)
            {
                notUseMassOperationMap.put(value.getProperty(ITEM_CODE), value.getProperty(NOT_USE_MASS_OPERATION));
                refreshDisplay();
            }
        }
    };

    @Inject
    public StructuredObjectsViewItemsForMassOperationsPresenter(
            SimpleTableDisplay<StructuredObjectsViewItemClient> display, EventBus eventBus,
            ObjectListColumnBuilder tableBuilder,
            LinkToPlaceColumnFactory<StructuredObjectsViewItemClient> linkToPlaceColumnFactory,
            WithArrowsCellTableResources cellTableResources,
            StructuredObjectsViewsMessages structuredObjectsViewsMessages)
    {
        super(display, eventBus);
        this.tableBuilder = tableBuilder;
        this.linkToPlaceColumnFactory = linkToPlaceColumnFactory;
        this.cellTableResources = cellTableResources;
        this.structuredObjectsViewsMessages = structuredObjectsViewsMessages;
    }

    public void init(DtObject structuredObjectsView, HashMap<String, Boolean> notUseMassOperationMap)
    {
        this.structuredObjectsView = structuredObjectsView;
        param = new StructuredObjectsViewItemsCommandParam(structuredObjectsView, null, refreshCallback);
        this.notUseMassOperationMap = notUseMassOperationMap;
    }

    @Override
    public void refreshDisplay()
    {
        getDisplay().refresh();
    }

    protected void initTable()
    {
        DataTable<StructuredObjectsViewItemClient> table = getDisplay().getTable();
        table.setVisibleRange(0, DefaultCellTable.DEFAULT_PAGESIZE);

        LinkToPlaceWithIndentColumn<StructuredObjectsViewItemClient> titleColumn =
                (LinkToPlaceWithIndentColumn<StructuredObjectsViewItemClient>)linkToPlaceColumnFactory
                        .getColumn(item -> NOWHERE);
        titleColumn.setStyleFunction(TITLE_STYLES);
        titleColumn.setCellStyleNames(cellTableResources.cellTableStyle().titleColumn());
        titleColumn.setIdProviderFunction(input -> input != null
                ? input.getCode()
                : StringUtilities.EMPTY);
        table.addColumn(titleColumn, structuredObjectsViewsMessages.structuredObjectsViewItems());

        Column<StructuredObjectsViewItemClient, String> codeColumn = new Column<StructuredObjectsViewItemClient,
                String>(
                new LineTextCell())
        {
            @Override
            public String getValue(StructuredObjectsViewItemClient item)
            {
                if (structuredObjectsView.getProperty(PANEL_DISABLED, false))
                {
                    return structuredObjectsViewsMessages.notUsedMassOperation();
                }
                else if (structuredObjectsView.getProperty(USE_SYSTEM_SETTINGS, false))
                {
                    return structuredObjectsViewsMessages.usedMassOperation();
                }

                return (notUseMassOperationMap.containsKey(item.getCode()) && notUseMassOperationMap.get(
                        item.getCode())) ?
                        structuredObjectsViewsMessages.notUsedMassOperation() :
                        structuredObjectsViewsMessages.usedMassOperation();
            }
        };
        table.addColumn(codeColumn, structuredObjectsViewsMessages.massOperation());

        addActionColumn(StructuredObjectsViewItemsCommandCode.EDIT_MAS_OPERATION);

        table.setRowStyles(new ItemsRowStyles());
        table.asWidget().ensureDebugId("structuredObjectsViewItemsTable");
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaption(structuredObjectsViewsMessages.editMassOperation());
        initTable();
        ItemsDataProvider dataProvider = new ItemsDataProvider();
        dataProvider.addDataDisplay(getDisplay().getTable());
    }

    private void addActionColumn(String... commands)
    {
        tableBuilder.addActionColumn(display, param, commands);
    }

    public HashMap<String, Boolean> getNotUseMassOperationMap(boolean useSystemSettings, boolean panelDisabled)
    {
        if (useSystemSettings)
        {
            return new HashMap<>();
        }
        if (panelDisabled)
        {
            notUseMassOperationMap.entrySet().forEach(entry -> entry.setValue(true));
        }
        return notUseMassOperationMap;
    }

    public DtObject getStructuredObjectsView()
    {
        return structuredObjectsView;
    }
}
