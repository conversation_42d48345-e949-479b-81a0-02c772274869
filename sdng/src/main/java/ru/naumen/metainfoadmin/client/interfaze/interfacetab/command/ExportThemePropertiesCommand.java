package ru.naumen.metainfoadmin.client.interfaze.interfacetab.command;

import jakarta.inject.Inject;

import com.google.gwt.core.client.GWT;
import com.google.gwt.user.client.Window;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.personalsettings.ThemeClient;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;

/**
 * <AUTHOR>
 * @since 02 авг. 2016 г.
 *
 */
public class ExportThemePropertiesCommand extends BaseCommandImpl<ThemeClient, InterfaceSettingsContext>
{
    public static final String ID = "exportThemePropetiesCommand";

    @Inject
    public ExportThemePropertiesCommand(@Assisted ThemeCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<ThemeClient, InterfaceSettingsContext> param)
    {
        String url = GWT.getModuleBaseURL().replace(GWT.getModuleName(), "admin") + "themeTemplate?theme="
                     + param.getValue().getCode();
        Window.Location.replace(url);
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof ThemeClient))
        {
            return true;
        }

        ThemeClient item = (ThemeClient)input;
        return !item.isSystem();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EXP;
    }
}