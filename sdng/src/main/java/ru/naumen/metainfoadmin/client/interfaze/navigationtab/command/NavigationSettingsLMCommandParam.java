package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;

/**
 * Параметр команд работы с левым меню
 * <AUTHOR>
 * @since 19.06.2020
 */
public class NavigationSettingsLMCommandParam extends NavigationSettingsMenuItemAbstractCommandParam<LeftMenuItemSettingsDTO>
{
    public NavigationSettingsLMCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, (LeftMenuItemSettingsDTO)null, callback);
    }

    public NavigationSettingsLMCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable ValueSource<LeftMenuItemSettingsDTO> valueSource,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, valueSource, callback);
    }

    public NavigationSettingsLMCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable LeftMenuItemSettingsDTO value,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, value, callback);
    }

    @Override
    @SuppressWarnings("unchecked")
    public NavigationSettingsLMCommandParam cloneIt()
    {
        return new NavigationSettingsLMCommandParam(getSettings(), getValueSource(), getCallback());
    }

    public List<LeftMenuItemSettingsDTO> getSiblings(LeftMenuItemSettingsDTO item)
    {
        return item.getParent() != null
                ? item.getParent().getChildren()
                : getSettings().get().getLeftMenu().getChildren();
    }

    @Override
    public List<LeftMenuItemSettingsDTO> getMenuItems()
    {
        return getSettings().get().getLeftMenu().getChildren();
    }

    @Override
    public Map<String, LinkedList<String>> getMenuItemPaths()
    {
        return getSettings().get().getLeftMenuItemPaths();
    }
}