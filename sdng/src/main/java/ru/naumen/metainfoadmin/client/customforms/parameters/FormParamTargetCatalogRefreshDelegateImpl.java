package ru.naumen.metainfoadmin.client.customforms.parameters;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.TARGET_CATALOG;

import java.util.List;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.catalog.TargetCatalogRefreshDelegateImpl;

/**
 * Реализация {@link AttributeFormPropertyDelegateRefresh} для свойства "Каталог" на формах
 * добавления и редакирования параметров настраиваемых форм
 *
 * <AUTHOR>
 * @since 17 июня 2016 г.
 */
public class FormParamTargetCatalogRefreshDelegateImpl<F extends ParameterForm>
        extends TargetCatalogRefreshDelegateImpl<F>
{
    @Override
    protected String getValue(List<Catalog> catalogs, PropertyContainerContext context)
    {
        String value = context.getPropertyValues().getProperty(TARGET_CATALOG);
        if (value != null)
        {
            return value;
        }
        return catalogs.get(0).getItemMetaClass().getFqn().toString();
    }
}
