package ru.naumen.metainfoadmin.client.templates.content;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TEMPLATES;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminSingleTabAdvlistPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;

/**
 * Представление страницы со списком шаблонов контентов.
 * <AUTHOR>
 * @since Mar 15, 2021
 */
public class ContentTemplatesPresenter extends AdminSingleTabAdvlistPresenterBase<ContentTemplatesPlace, DtObject>
{
    @Inject
    public ContentTemplatesPresenter(AdminTabDisplay display, EventBus eventBus,
            ContentTemplateListPresenter advlistPresenter)
    {
        super(display, eventBus, advlistPresenter);
    }

    @Override
    protected String getTitle()
    {
        return messages.contentTemplates();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return TEMPLATES;
    }
}
