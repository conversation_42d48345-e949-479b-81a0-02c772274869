package ru.naumen.metainfoadmin.client.attributes;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Multimap;
import com.google.gwt.dom.client.Element;
import com.google.gwt.user.client.ui.FlexTable;

import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.grouplist.GroupList.ColumnInfo;
import ru.naumen.core.client.widgets.grouplist.GroupList.GroupInfo;
import ru.naumen.core.client.widgets.grouplist.TableRowComparator;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Разделение логики отрисовки списка атрибутов на карточке класса/типа и ДПС.
 * Попытка разделения логики наследованием не увенчалась успехом. Проблема в следующем: 
 * оба списка в качестве Display используют AttributesDisplay (AttributesDisplayImpl), 
 * который является наследником TitledAttributeListImpl (реализует TitledAttributeList). 
 * AttributeListImpl является свойством TitledAttributeListImpl (связывается UiBinder'ом), однако вся логика 
 * должна быть кастомизирована именно здесь. 
 * Красиво и быстро разделить без дублирования большого объема кода не получилось.
 *
 * <AUTHOR>
 * @since 23 авг. 2018 г.
 *
 */
public interface AttributeListCustomizer
{
    /**
     * Создает и заполняет заголовок таблицы атрибутов
     * @return заголовок таблицы атрибутов либо null - указывает на необходимость 
     * вызова метода суперкласса.
     */
    Element createAndFillHeader(List<ColumnInfo<Attribute>> columns, AttributeListImpl attributeList,
            RegistrationContainer registrationContainer);

    void fillGroupRow(GroupInfo<String> group, int row, Multimap<String, ButtonToolDisplay> buttons,
            FlexTable grid, AttributeListImpl attributeList);

    /**
     * Заполняет группу элементами.
     * @return true в случае успешного заполнения, false - указывает на необходимость 
     * вызова метода суперкласса. 
     */
    boolean fillGroupContent(Collection<Attribute> elementsWithoutAggregates, FlexTable grid,
            AttributeListImpl attributeList);

    /**
     * Заполняет таблицу элементами.
     * @param elements мапа, связывающая атрибут с номером строки, в которой он отображается.
     * @return true в случае успешного заполнения, false - указывает на необходимость 
     * вызова метода суперкласса. 
     */
    boolean fillContent(Map<Attribute, Integer> elements, AttributeListImpl attributeList);

    void refresh(MetaClass metainfo, AttributeListImpl attributeList);

    /**
     * Используется или нет заголовок, "прилипающий" к верхней границе при скроллинге списка атрибутов 
     * @return true - использует, false - нет.
     */
    boolean useStickyHeader();

    /**
     * Сортировка таблицы по значению содержимого ячеек колонки. 
     * @param rowComparator компаратор для сортировки таблицы по значению в ячейках колонки
     */
    default void sortTable(TableRowComparator<?> rowComparator, AttributeListImpl attributeList)
    {
        //по умолчанию сортировка не поддерживается
    }

    ;
}
