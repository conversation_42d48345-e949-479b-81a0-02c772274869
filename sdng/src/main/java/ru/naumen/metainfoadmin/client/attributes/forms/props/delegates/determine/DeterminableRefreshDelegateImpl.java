package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determine;

import static java.lang.Boolean.TRUE;
import static ru.naumen.metainfo.shared.Constants.SUITABLE_AS_VALUEMAP_TARGET_TYPES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.ATTRIBUTE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.METAINFO;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPOSITE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.metainfo.shared.ValueMapUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.GenerationRuleDelegateHelper;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 */
public class DeterminableRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Inject
    ValueMapUtils valueMapUtils;

    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Attribute attr = context.getContextValues().getProperty(ATTRIBUTE);
        boolean systemEditable = attr == null || attr.isSystemEditable();
        boolean computable = TRUE.equals(context.getPropertyValues().getProperty(COMPUTABLE));
        boolean composite = TRUE.equals(context.getPropertyValues().getProperty(COMPOSITE));
        boolean determinable = attr != null ? valueMapUtils.isSuitableAsTargetOnAttrForm(attr)
                : isDeterminable(context);
        boolean suitableClass = !Comment.FQN
                .isSameClass(context.getContextValues().<MetaClass> getProperty(METAINFO).getFqn());
        callback.onSuccess(determinable && !computable && !composite && systemEditable && suitableClass
                           && !GenerationRuleDelegateHelper.hideDependenceField(context));
    }

    protected boolean isDeterminable(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        return SUITABLE_AS_VALUEMAP_TARGET_TYPES.contains(attrType);
    }
}
