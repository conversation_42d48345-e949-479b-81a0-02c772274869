/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.forms;

import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.attr.AvailibleTypesProvider;
import ru.naumen.core.client.events.ApplyFormHandler;
import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.inject.ParameterizedGinModule;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.metainfo.shared.dispatch2.AttributeModificationAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.PermittedTypesPropertyControllerFactory;
import ru.naumen.metainfoadmin.client.attributes.forms.props.PermittedTypesPropertyControllerImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.AvailableRestrictionConditionsProvider;

/**
 * <AUTHOR>
 * @since 01.07.2013
 *
 */
public final class AttributeFormGinModule<F extends ObjectForm> extends ParameterizedGinModule<F>
{
    public static <F extends ObjectForm> AttributeFormGinModule<F> create(Class<F> clazz)
    {
        return new AttributeFormGinModule<F>(clazz);
    }

    private TypeLiteral<? extends AbstractFormPropertyControllerFactorySyncImpl<F>> propertyControllerFactorySync;
    private TypeLiteral<? extends AttributeFormAfterBindHandler<F>> afterBindHandler;
    private TypeLiteral<? extends AttributeFormContextPropertiesSetter<F>> contextPropertiesSetter;
    private TypeLiteral<? extends ApplyFormHandler> applyFormHandler;
    private TypeLiteral<? extends AttributeFormPropertyValuesInitializer<F>> propertyValuesInitializer;
    private Class<? extends AttributeFormMessages<F>> messages;
    private TypeLiteral<? extends AttributeFormConstants<F>> constants;
    private Class<? extends AttributeModificationAction> action;

    private TypeLiteral<? extends AvailibleTypesProvider<F>> attrTypesProvider;
    private TypeLiteral<? extends PropertyControllerFactory<Attribute, F>> controllerFactory;

    private AttributeFormGinModule(Class<F> clazz)
    {
        super(clazz);
        propertyValuesInitializer = Gin.parameterizedTypeLiteral(AttributeFormPropertyValuesInitializerImpl.class,
                clazz);
        controllerFactory = Gin.typeLiteral(AttributeFormPropertyControllerFactoryAggregatedImpl.class, clazz);
    }

    public AttributeFormGinModule<F> setAction(Class<? extends AttributeModificationAction> action)
    {
        this.action = action;
        return this;
    }

    public AttributeFormGinModule<F> setAfterBindHandler(
            TypeLiteral<? extends AttributeFormAfterBindHandler<F>> afterBindHandler)
    {
        this.afterBindHandler = afterBindHandler;
        return this;
    }

    public AttributeFormGinModule<F> setApplyFormHandler(Class<? extends ApplyFormHandler> applyFormHandler)
    {
        this.applyFormHandler = Gin.typeLiteral(applyFormHandler);
        return this;
    }

    public AttributeFormGinModule<F> setApplyFormHandler(TypeLiteral<? extends ApplyFormHandler> applyFormHandler)
    {
        this.applyFormHandler = applyFormHandler;
        return this;
    }

    public AttributeFormGinModule<F> setAttrService(Class<? extends AvailibleTypesProvider<F>> attrTypesProvider)
    {
        this.attrTypesProvider = Gin.typeLiteral(attrTypesProvider);
        return this;
    }

    public AttributeFormGinModule<F> setAttrService(TypeLiteral<? extends AvailibleTypesProvider<F>> attrTypesProvider)
    {
        this.attrTypesProvider = attrTypesProvider;
        return this;
    }

    public AttributeFormGinModule<F> setConstants(Class<? extends AttributeFormConstants<F>> constants)
    {
        this.constants = Gin.typeLiteral(constants);
        return this;
    }

    public AttributeFormGinModule<F> setConstants(TypeLiteral<? extends AttributeFormConstants<F>> constants)
    {
        this.constants = constants;
        return this;
    }

    public AttributeFormGinModule<F> setContextPropertiesSetter(
            TypeLiteral<? extends AttributeFormContextPropertiesSetter<F>> contextPropertiesSetter)
    {
        this.contextPropertiesSetter = contextPropertiesSetter;
        return this;
    }

    public AttributeFormGinModule<F> setControllerFactory(
            Class<? extends PropertyControllerFactory<Attribute, F>> controllerFactory)
    {
        this.controllerFactory = Gin.typeLiteral(controllerFactory);
        return this;
    }

    public AttributeFormGinModule<F> setControllerFactory(
            TypeLiteral<? extends PropertyControllerFactory<Attribute, F>> controllerFactory)
    {
        this.controllerFactory = controllerFactory;
        return this;
    }

    public AttributeFormGinModule<F> setMessages(Class<? extends AttributeFormMessages<F>> messages)
    {
        this.messages = messages;
        return this;

    }

    public AttributeFormGinModule<F> setPropertyControllerFactorySync(
            TypeLiteral<? extends AbstractFormPropertyControllerFactorySyncImpl<F>> propertyControllerFactorySync)
    {
        this.propertyControllerFactorySync = propertyControllerFactorySync;
        return this;
    }

    public AttributeFormGinModule<F> setPropertyValuesInitializer(
            TypeLiteral<? extends AttributeFormPropertyValuesInitializer<F>> propertyValuesInitializer)
    {
        this.propertyValuesInitializer = propertyValuesInitializer;
        return this;
    }

    @Override
    protected void configure()
    {
        //@formatter:off
        if(propertyControllerFactorySync == null)
        {
            propertyControllerFactorySync = Gin.typeLiteral(AttributeFormPropertyControllerFactorySyncImpl.class, clazz);
        }
        
        bind(Gin.typeLiteral(AbstractFormPropertyControllerFactorySyncImpl.class, clazz))
            .to(propertyControllerFactorySync)
            .in(Singleton.class);
        bind(Gin.typeLiteral(AttributeFormAfterBindHandler.class, clazz))
            .to(afterBindHandler).in(Singleton.class);
        install(new GinFactoryModuleBuilder()
            .implement(PropertyController.class, Gin.<PermittedTypesPropertyControllerImpl<F>> typeLiteral(PermittedTypesPropertyControllerImpl.class, clazz))
            .build(Gin.typeLiteral(PermittedTypesPropertyControllerFactory.class, clazz)));
        bind(Gin.typeLiteral(AttributeFormContextPropertiesSetter.class, clazz))
            .to(contextPropertiesSetter).in(Singleton.class);
        install(new GinFactoryModuleBuilder()
            .implement(Gin.typeLiteral(PropertyControllerFactory.class, Gin.type(Attribute.class), clazz), controllerFactory)
            .build(Gin.typeLiteral(AttributeFormAggregatedFactoryFactory.class, clazz)));
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<CallbackPresenter<Attribute, MetaClass>>(){}, Gin.<AttributeFormPresenter<F>> typeLiteral(AttributeFormPresenter.class, clazz))
            .build(Gin.typeLiteral(AttributePresenterFactory.class, clazz)));
        install(new GinFactoryModuleBuilder()
            .implement(ApplyFormHandler.class, applyFormHandler)
            .build(Gin.typeLiteral(AttributeFormApplyHandlerFactory.class, clazz)));
        bind(Gin.typeLiteral(AttributeFormPropertyValuesInitializer.class, clazz))
            .to(propertyValuesInitializer)
            .in(Singleton.class);
        bind(Gin.typeLiteral(AttributeFormMessages.class, clazz))
            .to(messages)
            .in(Singleton.class);
        bind(Gin.typeLiteral(AttributeFormConstants.class, clazz))
            .to(constants)
            .in(Singleton.class);
        install(new GinFactoryModuleBuilder()
            .implement(AttributeModificationAction.class, action)
            .build(Gin.typeLiteral(AttributeActionFactory.class, clazz)));
        bind(Gin.typeLiteral(AvailibleTypesProvider.class, clazz))
            .to(attrTypesProvider)
            .in(Singleton.class);
        bind(AvailableRestrictionConditionsProvider.class).in(Singleton.class);
        //@formatter:on
    }
}