package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.quickaccess;

import java.util.Collection;
import java.util.Map;

import jakarta.inject.Inject;

import java.util.HashMap;

import ru.naumen.core.client.validation.NotEmptyObjectValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.StringLengthValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindTextBoxFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetBindDelegate;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetRefreshDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.QuickAccessTilePropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.LeftMenuItemPresentationBindDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.LeftMenuItemPresentationVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.MenuSettingsAbbreviationDelegateRefreshImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.MenuSettingsAbbreviationVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.MenuSettingsIconBindDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.MenuSettingsIconDelegateRefreshImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.MenuSettingsProfileDelegateRefreshImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.MenuSettingsProfileValueBindDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.MenuSettingsTagsBindDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.MenuSettingsTagsRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;

/**
 * Фабрика контроллеров свойств для форм добавления/редактирования плитки меню быстрого доступа в ИА
 *
 * <AUTHOR>
 * @since 15.07.2020
 */
public class QuickAccessTilePropertyControllerFactoryImpl
        extends PropertyControllerFactorySyncImpl<QuickAccessTileDTO, ObjectFormEdit>
{
    @Inject
    private PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxProperty> listBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxWithEmptyPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, SingleSelectProperty<LeftMenuItemSettingsDTO>> selectMenuItemPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, DtObjectSelectProperty> dtoSelectPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<Collection<SelectItem>, MultiSelectBoxProperty> profileValuePropertyFactory;

    @Inject
    private QuickAccessTileHintDelegateRefreshImpl hintDelegateRefresh;
    @Inject
    private QuickAccessTileAreaBindDelegateImpl areaBindDelegate;
    @Inject
    private QuickAccessTileMenuItemBindDelegateImpl menuItemBindDelegate;
    @Inject
    private QuickAccessTileMenuItemVCHDelegateImpl menuItemVCHDelegate;

    @Inject
    private LeftMenuItemPresentationBindDelegateImpl presentationBindDelegate;
    @Inject
    private LeftMenuItemPresentationVCHDelegateImpl presentationVCHDelegate;

    @Inject
    private MenuSettingsIconBindDelegateImpl iconBindDelegate;
    @Inject
    private MenuSettingsIconDelegateRefreshImpl iconDelegateRefresh;

    @Inject
    private MenuSettingsAbbreviationDelegateRefreshImpl abbreviationDelegateRefresh;
    @Inject
    private MenuSettingsAbbreviationVCHDelegateImpl abbreviationVCHDelegate;

    @Inject
    private MenuSettingsProfileValueBindDelegateImpl profilesBindDelegate;
    @Inject
    private MenuSettingsProfileDelegateRefreshImpl profilesDelegateRefresh;

    @Inject
    private PropertyDelegateBindTextBoxFactory textBoxBindDelegateFactory;

    @Inject
    private PropertyControllerSyncFactoryInj<Collection<SelectItem>, TagsProperty> tagsPropertyFactory;
    @Inject
    private MenuSettingsTagsBindDelegateImpl tagsBindDelegate;
    @Inject
    private MenuSettingsTagsRefreshDelegateImpl tagsRefreshDelegate;

    @Inject
    SettingsSetBindDelegate settingsSetBindDelegate;
    @Inject
    SettingsSetRefreshDelegate settingsSetRefreshDelegate;

    private final Map<Validator<String>, String> hintValidators = new HashMap<>();
    private final Map<Validator<String>, String> abbrevValidators = new HashMap<>();
    private final Map<Validator<SelectItem>, String> iconValueValidators = new HashMap<>();

    @Inject
    public void setUpValidators(NotEmptyValidator notEmptyValidator, StringLengthValidator stringLengthValidator,
            StringLengthValidator stringShortLengthValidator,
            NotEmptyObjectValidator<SelectItem> notEmptyObjectValidator)
    {
        hintValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        hintValidators.put(stringLengthValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        abbrevValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        abbrevValidators.put(stringShortLengthValidator.setMaxLength(2),
                PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        iconValueValidators.put(notEmptyObjectValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }

    @Override
    protected void build()
    {
        PropertyDelegateBind<String, TextBoxProperty> hintBindDelegate = textBoxBindDelegateFactory
                .create(Constants.MAX_TITLE_LENGTH);
        PropertyDelegateBind<String, TextBoxProperty> abbrevBindDelegate = textBoxBindDelegateFactory
                .create(2);
        //@formatter:off
        register(QuickAccessTilePropertyCode.HINT, textBoxPropertyFactory)
            .setBindDelegate(hintBindDelegate)
            .setRefreshDelegate(hintDelegateRefresh)
            .setValidators(hintValidators);
        register(QuickAccessTilePropertyCode.AREA, listBoxPropertyFactory)
            .setBindDelegate(areaBindDelegate);
        register(QuickAccessTilePropertyCode.MENU_ITEM, selectMenuItemPropertyFactory)
                .setVchDelegate(menuItemVCHDelegate)
                .setBindDelegate(menuItemBindDelegate);

        register(MenuSettingsPropertyCode.PRESENTATION, listBoxPropertyFactory)
                .setBindDelegate(presentationBindDelegate).setVchDelegate(presentationVCHDelegate);
        register(MenuSettingsPropertyCode.ABBREVIATION, textBoxPropertyFactory)
                .setBindDelegate(abbrevBindDelegate)
                .setRefreshDelegate(abbreviationDelegateRefresh)
                .setVchDelegate(abbreviationVCHDelegate)
                .setValidators(abbrevValidators);
        register(MenuSettingsPropertyCode.ICON, dtoSelectPropertyFactory)
                .setBindDelegate(iconBindDelegate)
                .setRefreshDelegate(iconDelegateRefresh)
                .setValidators(iconValueValidators);
        register(MenuSettingsPropertyCode.PROFILES, profileValuePropertyFactory)
                .setBindDelegate(profilesBindDelegate)
                .setRefreshDelegate(profilesDelegateRefresh);
        register(MenuSettingsPropertyCode.TAGS, tagsPropertyFactory)
                .setBindDelegate(tagsBindDelegate)
                .setRefreshDelegate(tagsRefreshDelegate);
        register(MenuSettingsPropertyCode.SETTINGS_SET, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(settingsSetBindDelegate)
                .setRefreshDelegate(settingsSetRefreshDelegate);
        //@formatter:on
    }
}
