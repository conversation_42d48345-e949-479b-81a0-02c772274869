package ru.naumen.metainfoadmin.client.embeddedapplications;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.EMBEDDED_APPLICATIONS;

import java.util.Set;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.OnStartRefreshCallback;
import ru.naumen.core.client.common.ResourceCallback;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.client.EmbeddedApplicationAsyncServiceImpl;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;

/**
 * Презентер карточки встроенного приложения
 * <AUTHOR>
 * @since 16.08.2016
 *
 */
public class EmbeddedApplicationPresenter extends AdminTabPresenter<EmbeddedApplicationPlace>
        implements EmbeddedApplicationUpdatedHandler
{
    private final EmbeddedApplicationMessages embeddedApplicationMessages;
    private final CommonMessages commonMessages;
    private final EmbeddedApplicationsPlace embeddedApplicationsPlace;
    private final EmbeddedApplicationInfoPresenter applicationPresenter;
    private final EmbeddedApplicationUsagePointPresenter applicationUsagePointPresenter;
    private final SharedSettingsClientService sharedSettingsService;
    private final EmbeddedApplicationAsyncServiceImpl adminSettingsService;

    protected EmbeddedApplicationAdminSettingsDto application;

    @SuppressWarnings("rawtypes")
    private final OnStartCallback refreshCallback = new OnStartRefreshCallback<>(getDisplay(), this);

    @Inject
    public EmbeddedApplicationPresenter(AdminTabDisplay display,
            EventBus eventBus,
            EmbeddedApplicationMessages embeddedApplicationMessages,
            CommonMessages commonMessages,
            EmbeddedApplicationsPlace embeddedApplicationsPlace,
            EmbeddedApplicationInfoPresenter applicationPresenter,
            EmbeddedApplicationUsagePointPresenter applicationUsagePointPresenter,
            SharedSettingsClientService sharedSettingsService,
            EmbeddedApplicationAsyncServiceImpl adminSettingsService)
    {
        super(display, eventBus);
        this.embeddedApplicationMessages = embeddedApplicationMessages;
        this.commonMessages = commonMessages;
        this.embeddedApplicationsPlace = embeddedApplicationsPlace;
        this.applicationPresenter = applicationPresenter;
        this.applicationUsagePointPresenter = applicationUsagePointPresenter;
        this.sharedSettingsService = sharedSettingsService;
        this.adminSettingsService = adminSettingsService;
        getDisplay().setTabBarVisible(false);
    }

    @Override
    public void onEmbeddedApplicationUpdated(EmbeddedApplicationUpdatedEvent e)
    {
        if (application == null)
        {
            application = e.getEmbeddedApplication();
        }
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        if (application == null) // непроинициалирован
        {
            return;
        }
        getDisplay().setTitle(application.getDisplayTitle());
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        adminSettingsService.getEmbeddedApplicationAdminSettings(getPlace().getCode(),
                new ResourceCallback<EmbeddedApplicationAdminSettingsDto>(commonMessages)
                {
                    @Override
                    public void onSuccess(EmbeddedApplicationAdminSettingsDto resource)
                    {
                        renderApplicationInfo(resource);
                        Set<String> appsAvailableOnModalForm =
                                sharedSettingsService.getEmbeddedApplicationsAvailableOnModalForm();
                        if (appsAvailableOnModalForm.contains(application.getCode()))
                        {
                            renderApplicationUsagePoints();
                        }
                        prevPageLinkPresenter.bind(embeddedApplicationMessages.goToEmbeddedApplications(),
                                embeddedApplicationsPlace, true);
                        refreshDisplay();
                    }
                });

        registerHandler(eventBus.addHandler(EmbeddedApplicationUpdatedEvent.getType(), this));
    }

    @Override
    protected void onUnbind()
    {
        if (applicationPresenter != null)
        {
            applicationPresenter.unbind();
        }
        if (applicationUsagePointPresenter != null)
        {
            applicationUsagePointPresenter.unbind();
        }
    }

    private void renderApplicationInfo(EmbeddedApplicationAdminSettingsDto applicationAdminSettings)
    {
        this.application = applicationAdminSettings;
        applicationPresenter.init(application, refreshCallback);
        addContent(applicationPresenter, "info");
    }

    private void renderApplicationUsagePoints()
    {
        applicationUsagePointPresenter.init(application);
        addContent(applicationUsagePointPresenter, "usagePoints");
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return EMBEDDED_APPLICATIONS;
    }
}
