package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemePlace;

import com.google.gwt.place.shared.PlaceController;
import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 21.08.2012
 *
 */
public class AddEscalationSchemeFormApplyCallback extends EscalationSchemeFormApplyCallback<ObjectFormAdd>
{
    @Inject
    PlaceController placeController;

    @Inject
    public AddEscalationSchemeFormApplyCallback(@Assisted EscalationSchemeForm<ObjectFormAdd> form)
    {
        super(form);
    }

    @Override
    protected void handleSuccess(SimpleResult<DtoContainer<EscalationScheme>> response)
    {
        super.handleSuccess(response);
        placeController.goTo(new EscalationSchemePlace(response.get()));
    }
}
