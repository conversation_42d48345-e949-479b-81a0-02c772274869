package ru.naumen.metainfoadmin.client.fastlink.settings;

import static ru.naumen.commons.shared.utils.StringUtilities.join;

import java.util.List;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.FactoryParam.ValueSource;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSetting;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSettingWithTitles;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.DeleteFastLinkSettingCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.EditFastLinkSettingCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.FastLinkSettingsCommandParam;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.client.tags.formatters.TagPropertyFormatter;

/**
 * Представление информации о настройки ссылок на карточке.
 * <AUTHOR>
 * @since 29.03.18
 */
public class FastLinkSettingInfoPresenter extends BasicPresenter<InfoDisplay>
{
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> alias;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> mentionTypes;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> contextTypes;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> mentionAttribute;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> attributeGroup;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> profiles;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> tags;
    private Property<String> settingsSet;

    @Inject
    private CommonMessages cmessages;
    @Inject
    private TagsMessages tagsMessages;
    @Inject
    private FastLinkSettingsMessages messages;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private PlaceController placeController;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private TagPropertyFormatter tagPropertyFormatter;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    private DtoContainer<FastLinkSetting> fastLinkSetting;
    private OnStartCallback<List<DtoContainer<FastLinkSettingWithTitles>>> refreshCallback;
    private final ToolBarDisplayMediator<DtoContainer<FastLinkSetting>> toolBar;

    private final OnStartCallback<List<DtoContainer<FastLinkSettingWithTitles>>> removeCallback =
            new SafeOnStartBasicCallback<List<DtoContainer<FastLinkSettingWithTitles>>>(
                    getDisplay())
            {
                @Override
                protected void handleSuccess(@Nullable List<DtoContainer<FastLinkSettingWithTitles>> value)
                {
                    if (value != null)
                    {
                        placeController.goTo(FastLinksSettingsListPlace.INSTANCE);
                    }
                }
            };

    private ValueSource<FastLinkSetting> fastLinkSettingValueSource = new ValueSource<FastLinkSetting>()
    {
        @Override
        public FastLinkSetting getValue()
        {
            return getFastLinkSetting();
        }
    };

    @Inject
    public FastLinkSettingInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(display.getToolBar());
    }

    public FastLinkSetting getFastLinkSetting()
    {
        return fastLinkSetting.get();
    }

    public void init(OnStartCallback<List<DtoContainer<FastLinkSettingWithTitles>>> refreshCallback)
    {
        settingsSet = settingsSetOnFormCreator.createFieldOnCard();
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        if (null != fastLinkSetting)
        {
            title.setValue(metainfoUtils.getLocalizedValue(fastLinkSetting.get().getTitle()));
            code.setValue(fastLinkSetting.get().getCode());
            alias.setValue(fastLinkSetting.get().getAlias());

            FastLinkSettingWithTitles fastLinkSettingWithTitles = (FastLinkSettingWithTitles)fastLinkSetting.get();
            mentionTypes.setValue(join((fastLinkSettingWithTitles).getMentionTypesTitles()));
            contextTypes.setValue(join((fastLinkSettingWithTitles).getContextTypesTitles()));
            mentionAttribute.setValue(fastLinkSettingWithTitles.getMentionAttributeTitle());
            attributeGroup.setValue(fastLinkSettingWithTitles.getAttributeGroupTitle());
            profiles.setValue(join((fastLinkSettingWithTitles).getProfilesTitles()));
            tags.setValue(tagPropertyFormatter.formatToAnchors(fastLinkSettingWithTitles.getTags()).asString());
            settingsSetOnFormCreator.setValueOnCardProperty(fastLinkSettingWithTitles.getSettingsSet(), settingsSet);
            toolBar.refresh(fastLinkSetting);
        }
        else
        {
            title.setValue(StringUtilities.EMPTY);
            code.setValue(StringUtilities.EMPTY);
            alias.setValue(StringUtilities.EMPTY);
            mentionTypes.setValue(StringUtilities.EMPTY);
            contextTypes.setValue(StringUtilities.EMPTY);
            mentionAttribute.setValue(StringUtilities.EMPTY);
            attributeGroup.setValue(StringUtilities.EMPTY);
            profiles.setValue(StringUtilities.EMPTY);
            tags.setValue(StringUtilities.EMPTY);
            settingsSetOnFormCreator.setValueOnCardProperty(null, settingsSet);
        }
    }

    public void setFastLinkSetting(DtoContainer<FastLinkSetting> fastLinkSetting)
    {
        this.fastLinkSetting = fastLinkSetting;
        refreshDisplay();
    }

    @SuppressWarnings("unchecked")
    protected ButtonPresenter<DtoContainer<FastLinkSetting>> addTool(String button, String title, String command,
            FastLinkSettingsCommandParam fastLinkSettingsCommandParam)
    {
        ButtonPresenter<DtoContainer<FastLinkSetting>> buttonPresenter =
                (ButtonPresenter<DtoContainer<FastLinkSetting>>)buttonFactory
                        .create(button, title, command, fastLinkSettingsCommandParam);
        toolBar.add(buttonPresenter);
        return buttonPresenter;
    }

    protected void bindProperties()
    {
        title.setCaption(cmessages.title());
        getDisplay().add(title);
        code.setCaption(cmessages.code());
        getDisplay().add(code);
        mentionTypes.setCaption(messages.mentionTypes());
        getDisplay().add(mentionTypes);
        alias.setCaption(messages.alias());
        getDisplay().add(alias);
        contextTypes.setCaption(messages.contextTypes());
        getDisplay().add(contextTypes);
        mentionAttribute.setCaption(messages.mentionAttribute());
        getDisplay().add(mentionAttribute);
        attributeGroup.setCaption(messages.attributeGroup());
        getDisplay().add(attributeGroup);
        profiles.setCaption(messages.profiles());
        getDisplay().add(profiles);
        tags.setCaption(tagsMessages.tags());
        getDisplay().add(tags);
        if (settingsSet != null)
        {
            getDisplay().add(settingsSet);
        }
    }

    protected void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(alias, "alias");
        DebugIdBuilder.ensureDebugId(mentionTypes, "mentionTypes");
        DebugIdBuilder.ensureDebugId(contextTypes, "contextTypes");
        DebugIdBuilder.ensureDebugId(mentionAttribute, "mentionAttribute");
        DebugIdBuilder.ensureDebugId(attributeGroup, "attributeGroup");
        DebugIdBuilder.ensureDebugId(profiles, "profiles");
        DebugIdBuilder.ensureDebugId(tags, "tags");
    }

    protected void initToolBar()
    {
        ButtonPresenter<DtoContainer<FastLinkSetting>> editBtn = addTool(ButtonCode.EDIT,
                cmessages.edit(), EditFastLinkSettingCommand.ID,
                new FastLinkSettingsCommandParam(null, fastLinkSettingValueSource, refreshCallback));
        editBtn.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        ButtonPresenter<DtoContainer<FastLinkSetting>> delBtn = addTool(ButtonCode.DELETE,
                cmessages.delete(), DeleteFastLinkSettingCommand.ID,
                new FastLinkSettingsCommandParam(null, fastLinkSettingValueSource, removeCallback));
        delBtn.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE));
        toolBar.bind();
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(cmessages.properties());
        initToolBar();
        bindProperties();
        ensureDebugIds();
        toolBar.refresh(fastLinkSetting);
    }
}
