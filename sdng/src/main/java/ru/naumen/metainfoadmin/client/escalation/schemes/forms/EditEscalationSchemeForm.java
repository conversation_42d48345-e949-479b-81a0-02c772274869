package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.EscalationSchemeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 24.07.2012
 *
 */
public class EditEscalationSchemeForm extends EscalationSchemeForm<ObjectFormEdit>
{
    @Inject
    public EditEscalationSchemeForm(DefaultPropertyFormDisplayImpl display, EventBus eventBus,
            @Assisted EventBus localEventBus)
    {
        super(display, eventBus, localEventBus);
    }

    public void setScheme(EscalationScheme scheme)
    {
        propertyValues.setProperty(EscalationSchemeFormPropertyCode.CODE, scheme.getCode());
        propertyValues.setProperty(EscalationSchemeFormPropertyCode.TITLE, i18nUtil.getLocalizedTitle(scheme));
        propertyValues.setProperty(EscalationSchemeFormPropertyCode.DESCRIPTION,
                i18nUtil.getLocalizedDescription(scheme));
        propertyValues.setProperty(EscalationSchemeFormPropertyCode.TARGET_OBJECTS, scheme.getTargetTypes());
        propertyValues.setProperty(EscalationSchemeFormPropertyCode.TIMER, scheme.getTimer().getCode());
        propertyValues.setProperty(EscalationSchemeFormPropertyCode.STATE, scheme.getState());
        propertyValues.setProperty(EscalationSchemeFormPropertyCode.SETTINGS_SET, scheme.getSettingsSet());
    }
}
