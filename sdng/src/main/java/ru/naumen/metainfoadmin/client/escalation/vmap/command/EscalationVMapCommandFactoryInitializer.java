/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.command;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogItemCommandParam;
import ru.naumen.metainfoadmin.client.escalation.vmap.command.EscalationVMapCommandGinModule.EscalationVMapItemCommandCode;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationVMapCommandFactoryInitializer
{
    @Inject
    public EscalationVMapCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<AddEscalationVMapItemCommand, CatalogItemCommandParam<DtObject>> addItemProvider,
            CommandProvider<CopyEscalationVMapItemCommand, CatalogItemCommandParam<DtObject>> copyCatalogProvider,
            CommandProvider<EditEscalationVMapItemCommand, CatalogItemCommandParam<DtObject>> editItemProvider)

    {
        // @formatter:off
            factory.register(EscalationVMapItemCommandCode.ADD_ESCALATION_VMAP_ITEM,  addItemProvider);
            factory.register(EscalationVMapItemCommandCode.COPY_ESCALATION_VMAP_ITEM, copyCatalogProvider);
            factory.register(EscalationVMapItemCommandCode.EDIT_ESCALATION_VMAP_ITEM, editItemProvider);
            // @formatter:on
    }
}