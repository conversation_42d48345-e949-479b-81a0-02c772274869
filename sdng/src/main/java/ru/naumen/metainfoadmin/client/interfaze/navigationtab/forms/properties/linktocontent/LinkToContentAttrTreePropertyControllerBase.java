package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import jakarta.inject.Inject;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.view.TreeFactory;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerImpl;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist.attrseltree.RelationAttrsTreeFactoryContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist.attrseltree.SelectAttrTreeGinModule.RelationAttrsTree;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Базовый контроллер для свойств вида "Дерево атрибутов"
 *
 * <AUTHOR>
 * @since 09.12.2020
 */
public abstract class LinkToContentAttrTreePropertyControllerBase extends PropertyControllerImpl<RelationsAttrTreeObject,
        PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<?, RelationsAttrTreeObject, ?>>>
{
    public LinkToContentAttrTreePropertyControllerBase(String code,
            PropertyContainerContext context,
            PropertyParametersDescriptor propertyParams,
            PropertyDelegateDescriptor<RelationsAttrTreeObject, PropertyBase<RelationsAttrTreeObject,
                    PopupValueCellTree<?,
                            RelationsAttrTreeObject, ?>>> propertyDelegates)
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    @Inject
    private TreeFactory<RelationsAttrTreeObject, RelationAttrsTree, RelationAttrsTreeFactoryContext> treeFactory;

    protected void setTreeProperty(RelationAttrsTreeFactoryContext treeContext)
    {

        /* Для всех атрибутов типа "дерево", которые будут переинициализированы внутри метода refresh, нужно сначала
        разбиндить соответствующий атрибут, иначе он зарегистрируется многократно и будут проблемы с валидацией */
        unbind();

        treeFactory.createTree(treeContext, new BasicCallback<HasValueOrThrow<RelationsAttrTreeObject>>()
        {
            @Override
            protected void handleSuccess(HasValueOrThrow<RelationsAttrTreeObject> value)
            {
                PopupValueCellTree<?, RelationsAttrTreeObject, ?> tree = (PopupValueCellTree<?,
                        RelationsAttrTreeObject, ?>)value;
                property = new PropertyBase<>(propertyParams.getCaption(), tree);

                bindProperty();
                setValue();
                LinkToContentAttrTreePropertyControllerBase.super.refresh();
            }
        });
    }
}
