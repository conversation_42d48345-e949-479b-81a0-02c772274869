package ru.naumen.metainfoadmin.client.attributes.forms.edit;

import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.base.Function;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.BatchAction;
import net.customware.gwt.dispatch.shared.BatchAction.OnException;
import net.customware.gwt.dispatch.shared.BatchResult;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.dispatch.FastSelectionChangesWithValue;
import ru.naumen.core.shared.dispatch.FastSelectionDtObjectTreeValue;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.GetHideArchivedSettingsAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesLiteAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesLiteResponse;
import ru.naumen.metainfo.shared.dispatch2.GetSettingsAction;
import ru.naumen.metainfo.shared.dispatch2.script.GetScriptsDtosAction;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeDescription;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.CommonRestriction;
import ru.naumen.metainfo.shared.elements.ComplexRelationType;
import ru.naumen.metainfo.shared.elements.DateTimeRestrictionCondition;
import ru.naumen.metainfo.shared.elements.HasDateTimeRestriction.RestrictionType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.Presentation;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormPropertyValuesInitializerImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.GenerationRuleDelegateHelper;
import ru.naumen.metainfoadmin.client.attributes.forms.typeEmul.AttributeTypeBuilderAggregateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.typeEmul.AttributeTypeFactory;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Инициализация исходных значений свойств для формы редактирования атрибута
 * <AUTHOR>
 * @since 01.06.2012
 */
public class EditAttributeFormPropertyValuesInitializerImpl<T extends ObjectForm>
        extends AttributeFormPropertyValuesInitializerImpl<T>
{
    private static final List<String> SCRIPT_PROPERTIES_CODES = Lists.newArrayList(SCRIPT_CODE,
            SCRIPT_FOR_FILTRATION_CODE, COMPUTABLE_ON_FORM_SCRIPT_CODE, SCRIPT_FOR_DEFAULT_CODE,
            DATE_TIME_RESTRICTION_SCRIPT_CODE, COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT_CODE);
    private static final List<String> SCRIPT_PROPERTIES = Lists.newArrayList(SCRIPT, SCRIPT_FOR_FILTRATION,
            COMPUTABLE_ON_FORM_SCRIPT, SCRIPT_FOR_DEFAULT, DATE_TIME_RESTRICTION_SCRIPT,
            COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT);

    @Inject
    private AttributeTypeFactory attrTypeFactory;
    @Inject
    private AttributeTypeBuilderAggregateImpl aggregateTypeBuilder;
    @Inject
    private AdminMetainfoServiceAsync metainfoService;
    @Inject
    private SharedSettingsClientService sharedSettingsService;

    @Override
    public void init(IProperties contextProps, IProperties propertyValues, Attribute attribute, ReadyState rs)
    {
        super.init(contextProps, propertyValues, attribute, rs);
        MetaClass metainfo = contextProps.getProperty(AttributeFormContextValues.METAINFO);

        AttributeType attributeType = attribute.getType();
        if (Constants.AggregateAttributeType.CODES.contains(attributeType.getCode()))
        {
            if (Constants.CustomForm.FQN.isSameClass(attribute.getFqn().getClassFqn()))
            {
                AggregateAttributeType type = attributeType.cast();
                Collection<ClassFqn> aggrClasses = make(type.getAttributes())
                        .transform(new Function<AttributeDescription, ClassFqn>()
                        {

                            @Override
                            public ClassFqn apply(AttributeDescription desc)
                            {
                                return desc.getReferenceMetaClass();
                            }

                        }).toList();
                propertyValues.setProperty(AGGREGATE_CLASSES, aggrClasses);
            }
            else if (!attributeType.isAttributeOfRelatedObject())
            {
                List<String> aggrAttrs = getAggregateAttrsTitles(metainfo, attribute);
                propertyValues.setProperty(AGGREGATE_ATTRIBUTES, StringUtilities.join(aggrAttrs));
            }
        }
        boolean isDeclaredType = metainfo != null && metainfo.getFqn().equals(attribute.getDeclaredMetaClass());
        boolean isAbstract = metainfo != null && metainfo.isAbstract();
        boolean isMetaClassHardcoded = metainfo != null && metainfo.isHardcoded();
        boolean isCase = metainfo != null && MetainfoUtils.isCase(metainfo);

        boolean isAttributeHardcoded = attribute.isHardcoded();
        boolean isUseGenerationRule = GenerationRuleDelegateHelper.isAlwaysGenaratedAttribute(attribute)
                                      || Boolean.TRUE.equals(attribute.isUseGenerationRule());

        boolean hasInherit = !isAbstract && (isMetaClassHardcoded || isCase)
                             && (isAttributeHardcoded || !isDeclaredType);

        boolean isDateTime = Constants.DATE_TIME_TYPES.contains(attributeType.getCode());

        contextProps.setProperty(AttributeFormContextValues.HAS_INHERIT, hasInherit);
        contextProps.setProperty(AttributeFormContextValues.ATTRIBUTE, attribute);

        propertyValues.setProperty(INHERIT, hasInherit && !attribute.isOverrided());
        propertyValues.setProperty(TITLE, attribute.getTitle());
        propertyValues.setProperty(CODE, attribute.getCode());
        propertyValues.setProperty(ATTR_TYPE, attributeType.isAttributeOfRelatedObject()
                ? AttributeOfRelatedObjectSettings.CODE
                : attributeType.getCode());
        propertyValues.setProperty(COMPUTABLE, attribute.isComputable() || attribute.isSystemComputable());
        propertyValues.setProperty(SYSTEM_COMPUTABLE, attribute.isSystemComputable());
        propertyValues.setProperty(USE_GEN_RULE, isUseGenerationRule);
        propertyValues.setProperty(GEN_RULE, attribute.getGenerationRule());
        propertyValues.setProperty(DETERMINABLE, attribute.isDeterminable());
        propertyValues.setProperty(EDITABLE, attribute.isEditable());
        propertyValues.setProperty(EDITABLE_IN_LISTS, attribute.isEditableInLists());
        propertyValues.setProperty(REQUIRED,
                !attributeType.isAttributeOfRelatedObject() && Boolean.TRUE.equals(attribute.isRequired()));
        propertyValues.setProperty(REQUIRED_IN_INTERFACE,
                !attributeType.isAttributeOfRelatedObject()
                && Boolean.TRUE.equals(attribute.isRequiredInInterface()));
        propertyValues.setProperty(UNIQUE,
                !attributeType.isAttributeOfRelatedObject() && Boolean.TRUE.equals(attribute.isUnique()));
        propertyValues.setProperty(DETERMINER, attribute.getDeterminer());
        propertyValues.setProperty(FILTERED_BY_SCRIPT, attribute.isFilteredByScript());
        propertyValues.setProperty(COMPUTABLE_ON_FORM, attribute.isComputableOnForm());
        propertyValues.setProperty(EXPORT_NDAP, attribute.isExportNDAP());
        propertyValues.setProperty(RELATED_ATTRS_TO_EXPORT, attribute.getRelatedAttrsToExport());
        propertyValues.setProperty(HIDDEN_WHEN_EMPTY, attribute.isHiddenWhenEmpty());
        propertyValues.setProperty(HIDDEN_ATTR_CAPTION, attribute.isHiddenAttrCaption());
        propertyValues.setProperty(HIDDEN_WHEN_NO_POSSIBLE_VALUES, attribute.isHiddenWhenNoPossibleValues());
        propertyValues.setProperty(EDIT_ON_COMPLEX_FORM_ONLY, attribute.isEditOnComplexFormOnly());
        if (attribute.getDefaultValue() instanceof FastSelectionChangesWithValue)
        {
            propertyValues.setProperty(DEFAULT_VALUE,
                    new FastSelectionDtObjectTreeValue(
                            Lists.newArrayList((FastSelectionChangesWithValue)attribute.getDefaultValue()),
                            ru.naumen.core.shared.Constants.TEMP_UUID));
        }
        else
        {
            propertyValues.setProperty(DEFAULT_VALUE, attribute.getDefaultValue());
        }
        propertyValues.setProperty(DEFAULT_BY_SCRIPT, attribute.isDefaultByScript());
        propertyValues.setProperty(SHOW_PRS, attribute.getViewPresentation().getCode());

        Presentation editPresentation = attribute.getEditPresentation();
        propertyValues.setProperty(EDIT_PRS, editPresentation.getCode());
        propertyValues.setProperty(STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE,
                editPresentation.getStructuredObjectsViewForBuildingTreeCode());
        propertyValues.setProperty(SUGGEST_CATALOG,
                editPresentation.getProperty(Presentations.SUGGESTION_CLASSFQN));
        propertyValues.setProperty(SELECT_SORTING,
                editPresentation.getProperty(Presentations.SELECT_SORTING));
        propertyValues.setProperty(DESCRIPTION, attribute.getDescription());
        propertyValues.setProperty(COMPOSITE, attribute.isComposite());
        propertyValues.setProperty(TEMPLATE, attribute.getTemplate());

        initComplexRelationProps(propertyValues, attribute);

        attrTypeFactory.invert(attributeType, propertyValues);

        propertyValues.setProperty(SCRIPT_CODE, attribute.getScript());
        propertyValues.setProperty(SCRIPT_FOR_FILTRATION_CODE, attribute.getScriptForFiltration());
        propertyValues.setProperty(COMPUTABLE_ON_FORM_SCRIPT_CODE, attribute.getComputableOnFormScript());
        propertyValues.setProperty(SCRIPT_FOR_DEFAULT_CODE, attribute.getScriptForDefault());
        propertyValues.setProperty(DATE_TIME_RESTRICTION_TYPE, attribute.getDateTimeRestrictionType() == null
                ? RestrictionType.NO_RESTRICTION.name()
                : attribute.getDateTimeRestrictionType().name());
        Collection<CommonRestriction> dateTimeCommonRestrictions = attribute.getDateTimeCommonRestrictions();
        dateTimeCommonRestrictions = isDateTime && !isAttributeHardcoded
                ? CollectionUtils.isEmpty(dateTimeCommonRestrictions)
                ? Lists.newArrayList(CommonRestriction.values())
                : dateTimeCommonRestrictions
                : Collections.emptyList();
        propertyValues.setProperty(DATE_TIME_COMMON_RESTRICTIONS, dateTimeCommonRestrictions);
        propertyValues.setProperty(DATE_TIME_RESTRICTION_SCRIPT_CODE, attribute.getDateTimeRestrictionScript());
        propertyValues.setProperty(DATE_TIME_RESTRICTION_ATTRIBUTE, attribute.getDateTimeRestrictionAttribute());
        propertyValues.setProperty(DATE_TIME_RESTRICTION_CONDITION, attribute.getDateTimeRestrictionCondition() == null
                ? DateTimeRestrictionCondition.NO.name()
                : attribute.getDateTimeRestrictionCondition().name());

        if (Constants.LINK_ATTRIBUTE_TYPES.contains(attributeType.getCode()))
        {
            ru.naumen.metainfo.shared.elements.ObjectAttributeType objectType = attributeType.cast();
            propertyValues.setProperty(QUICK_ADD_FORM_CODE, objectType.getQuickAddFormCode());
            propertyValues.setProperty(QUICK_EDIT_FORM_CODE, objectType.getQuickEditFormCode());
        }
        if (Constants.IntegerAttributeType.CODE.equals(attributeType.getCode()))
        {
            ru.naumen.metainfo.shared.elements.IntegerAttributeType objectType = attributeType.cast();
            propertyValues.setProperty(HAS_GROUP_SEPARATORS, objectType.isHasGroupSeparator());
        }
        if (Constants.DoubleAttributeType.CODE.equals(attributeType.getCode()))
        {
            ru.naumen.metainfo.shared.elements.DoubleAttributeType objectType = attributeType.cast();
            propertyValues.setProperty(HAS_GROUP_SEPARATORS, objectType.isHasGroupSeparator());
            propertyValues.setProperty(DIGITS_COUNT_RESTRICTION, objectType.getDecimalsCountRestriction());
        }

        if (attributeType.getRelatedObjectMetaClass() != null)
        {
            propertyValues.setProperty(RELATED_OBJECT_METACLASS,
                    attributeType.getRelatedObjectMetaClass().asString());
            propertyValues.setProperty(RELATED_OBJECT_ATTRIBUTE,
                    AttributeFqn.toString(attributeType.getRelatedObjectMetaClass(),
                            attributeType.getRelatedObjectAttribute()));
        }
        else if (Constants.COLLECTION_BOLINKS_TYPES.contains(attributeType.getCode()))
        {
            propertyValues.setProperty(HIDE_ARCHIVED, attribute.isHideArchived());
        }

        if (DateTimeIntervalAttributeType.CODE.equals(attributeType.getCode()))
        {
            Collection<String> value = attributeType.hasProperty(AttributeFormPropertyCode.INTERVAL_AVAILABLE_UNITS)
                    ? attributeType.getProperty(AttributeFormPropertyCode.INTERVAL_AVAILABLE_UNITS)
                    : Arrays.stream(Interval.values()).map(Enum::name).collect(Collectors.toList());
            propertyValues.setProperty(AttributeFormPropertyCode.INTERVAL_AVAILABLE_UNITS, value);
            if (isAttributeHardcoded)
            {
                propertyValues.setProperty(AttributeFormPropertyCode.NEED_STORE_UNITS, true);
            }
            else
            {
                propertyValues.setProperty(AttributeFormPropertyCode.NEED_STORE_UNITS,
                        attributeType.getProperty(NEED_STORE_UNITS));
            }
        }

        if (attributeType.isAttributeOfRelatedObject())
        {
            rs.notReady();
            List<AttributeFqn> attrChain = attributeType.getAttrChain();
            metainfoService.getFullMetaInfo(
                    attrChain.stream().map(AttributeFqn::getClassFqn).collect(Collectors.toList()),
                    new BasicCallback<List<MetaClass>>()
                    {
                        @Override
                        protected void handleSuccess(List<MetaClass> list)
                        {
                            RelationsAttrTreeObject ato = null;
                            StringBuilder chainAsString = new StringBuilder();
                            for (int i = 0; i < attrChain.size(); i++)
                            {
                                AttributeFqn ar = attrChain.get(i);
                                MetaClass mc = Iterables.find(list, MetaClassFilters.equal(ar.getClassFqn()));
                                Attribute attr = mc.getAttribute(ar.getCode());
                                ato = new RelationsAttrTreeObject(ato, attr);
                                chainAsString.append(attr.getTitle());
                                if (i < attrChain.size() - 1)
                                {
                                    chainAsString.append('/');
                                }
                            }
                            propertyValues.setProperty(ATTR_CHAIN_VIEW, chainAsString.toString());
                            propertyValues.setProperty(ATTR_CHAIN, ato);
                            metainfoService.getMetaClass(attributeType.getRelatedObjectMetaClass(),
                                    new BasicCallback<MetaClass>()
                                    {
                                        @Override
                                        protected void handleSuccess(MetaClass value)
                                        {
                                            Attribute attr =
                                                    value.getAttribute(attributeType.getRelatedObjectAttribute());
                                            propertyValues.setProperty(HIDE_ARCHIVED, attr.isHideArchived());
                                            rs.ready();
                                        }

                                        @Override
                                        protected void handleFailure(String msg, @Nullable String details)
                                        {
                                            rs.ready();
                                        }
                                    });
                        }
                    });
        }
        propertyValues.setProperty(RELATED_OBJECT_HIERARCHY_LEVEL,
                Integer.toString(attributeType.getRelatedObjectHierarchyLevel()));
        propertyValues.setProperty(TAGS, attribute.getTags());
        propertyValues.setProperty(SETTINGS_SET, attribute.getSettingsSet());
        propertyValues.setProperty(COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT_CODE, attribute
                .getComputeAnyCatalogElementsScript());
        propertyValues.setProperty(ADVLIST_SEMANTIC_FILTERING, attribute.isAdvlistSemanticFiltering());
    }

    @SuppressWarnings("unchecked")
    @Override
    public void loadRelatedData(final IProperties contextProps, final IProperties propertyValues,
            final BasicCallback<Void> basicCallback)
    {
        final Set<String> scriptsForLoad = getScriptsForLoad(propertyValues);
        final List<ClassFqn> aggregateFqns = propertyValues.getProperty(AGGREGATE_CLASSES);
        MetaClass metainfo = contextProps.getProperty(AttributeFormContextValues.METAINFO);
        ClassFqn fqn = metainfo != null ? metainfo.getFqn() : null;
        final boolean isServiceCallClientAttribute = Association.CLIENT.equals(propertyValues.getProperty(CODE)) &&
                                                     (ServiceCall.FQN.equals(fqn) || ServiceCall.FQN.isClassOf(fqn));

        List<Action<?>> actions = new ArrayList<>();
        actions.add(new GetHideArchivedSettingsAction());

        if (!scriptsForLoad.isEmpty())
        {
            actions.add(new GetScriptsDtosAction(scriptsForLoad));
        }
        if (!ObjectUtils.isEmpty(aggregateFqns))
        {
            actions.add(new GetMetaClassesLiteAction(MetaClassFilters.in(aggregateFqns)));
        }
        if (isServiceCallClientAttribute)
        {
            actions.add(new GetSettingsAction());
        }

        if (actions.isEmpty())
        {
            processScriptLoadResult(propertyValues, Collections.emptySet());
            basicCallback.onSuccess(null);
            return;
        }

        BatchAction action = new BatchAction(OnException.ROLLBACK, actions);
        dispatch.execute(action, new BasicCallback<BatchResult>()
        {
            @Override
            protected void handleSuccess(BatchResult result)
            {
                int index = 0;

                boolean isEnableHideArchivedObjects = ((SimpleResult<Boolean>)result.getResult(index)).get();
                contextProps.setProperty(ENABLE_HIDE_ARCHIVED_OBJECTS, isEnableHideArchivedObjects);
                if (!isEnableHideArchivedObjects)
                {
                    propertyValues.removeProperty(HIDE_ARCHIVED);
                }
                ++index;
                Set<ScriptDto> scriptsDtos = Collections.emptySet();
                if (!scriptsForLoad.isEmpty())
                {
                    scriptsDtos = ((SimpleResult<Set<ScriptDto>>)result.getResult(index)).get();
                    ++index;
                }
                processScriptLoadResult(propertyValues, scriptsDtos);

                if (!ObjectUtils.isEmpty(aggregateFqns))
                {
                    GetMetaClassesLiteResponse metaClasses = (GetMetaClassesLiteResponse)result.getResult(index);
                    propertyValues.setProperty(AGGREGATE_CLASSES, metaClasses.getMetaClasses());
                    ++index;
                }

                if (isServiceCallClientAttribute)
                {
                    SimpleResult<Settings> settings = (SimpleResult<Settings>)result.getResult(index);
                    SCParameters scParameters = settings.get().getScParameters();
                    propertyValues.setProperty(AttributeFormPropertyCode.SC_PARAMETERS, scParameters);
                }

                basicCallback.onSuccess(null);
            }
        });
    }

    private List<String> getAggregateAttrsTitles(MetaClass metaClass, Attribute attribute)
    {
        ArrayList<String> result = Lists.newArrayListWithCapacity(3);

        AggregateAttributeType type = attribute.getType().cast();
        Collection<ClassFqn> aggregateFqns = type.getProperty(Constants.AggregateAttributeType.AGGREGATE_CLASSES, null);
        List<AttributeDescription> attrDescs = type.getAttributes();

        // если агрегируемые атрибуты не заполнены, то попытаемся их вычислить
        if (attrDescs == null && aggregateFqns != null)
        {
            attrDescs = aggregateTypeBuilder.getAttributeDescriptions(attribute.getCode(), aggregateFqns);
        }

        if (attrDescs != null)
        {
            for (AttributeDescription attrDesc : attrDescs)
            {
                result.add(metaClass.getAttribute(attrDesc.getAttribute()).getTitle());
            }
        }

        return result;
    }

    private Set<String> getScriptsForLoad(IProperties propertyValues)
    {
        Set<String> scriptsForLoad = new HashSet<>();
        for (String scriptPropertyCode : SCRIPT_PROPERTIES_CODES)
        {
            if (StringUtilities.isEmpty(propertyValues.getProperty(scriptPropertyCode)))
            {
                continue;
            }
            scriptsForLoad.add(propertyValues.getProperty(scriptPropertyCode));
        }
        return scriptsForLoad;
    }

    private void initComplexRelationProps(IProperties propertyValues, Attribute attribute)
    {
        AttributeType attrType = attribute.getType().cast();
        if (!(attrType instanceof AggregateAttributeType || attrType instanceof ObjectAttributeType))
        {
            return;
        }
        if (attrType.isComplexRelation())
        {
            if (ComplexRelationType.FLAT_WITH_FULL_TEXT_SEARCH.getCode().equals(attrType.getComplexRelationType())
                && !sharedSettingsService.isFullTextSearchFormEnabled())
            {
                propertyValues.setProperty(COMPLEX_RELATION, ComplexRelationType.FLAT.getCode());
            }
            else
            {
                propertyValues.setProperty(COMPLEX_RELATION, attrType.getComplexRelationType());
            }
        }
        else
        {
            propertyValues.setProperty(COMPLEX_RELATION, Boolean.FALSE.toString());
        }
        if (attrType instanceof ObjectAttributeType && attrType.getComplexRelationType() != null
            && attrType.getComplexRelationType().equals(ComplexRelationType.HIERARCHY.getCode()))
        {
            propertyValues.setProperty(COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW, attrType
                    .getComplexStructuredObjectsViewCode());
            return;
        }
        else if (attrType instanceof ObjectAttributeType)
        {
            propertyValues.setProperty(COMPLEX_RELATION_ATTR_GROUP, attrType.getComplexAttrGroupCode());
            Presentation editPresentation = attribute.getEditPresentation();
            if (Presentations.STRUCTURE_BASED_EDIT_PRS.contains(editPresentation.getCode()))
            {
                propertyValues.setProperty(STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE,
                        editPresentation.getStructuredObjectsViewForBuildingTreeCode());
            }
            return;
        }
        Map<ClassFqn, String> attrGroups = attrType.getComplexRelationAttrGroups();
        if (attrGroups == null)
        {
            return;
        }
        propertyValues.setProperty(COMPLEX_EMPLOYEE_ATTR_GROUP, attrGroups.get(Employee.FQN));
        propertyValues.setProperty(COMPLEX_TEAM_ATTR_GROUP, attrGroups.get(Team.FQN));
        propertyValues.setProperty(COMPLEX_OU_ATTR_GROUP, attrGroups.get(OU.FQN));
        propertyValues.setProperty(EDIT_ON_COMPLEX_FORM_ONLY, attribute.isEditOnComplexFormOnly());
    }

    private void processScriptLoadResult(IProperties propertyValues, Set<ScriptDto> scriptsDtos)
    {
        Map<String, ScriptDto> scriptsMap = new HashMap<>();
        for (ScriptDto script : scriptsDtos)
        {
            scriptsMap.put(script.getCode(), script);
        }

        for (int i = 0; i < SCRIPT_PROPERTIES_CODES.size(); i++)
        {
            String scriptCode = propertyValues.getProperty(SCRIPT_PROPERTIES_CODES.get(i));
            if (StringUtilities.isEmpty(scriptCode))
            {
                propertyValues.setProperty(SCRIPT_PROPERTIES.get(i), null);
                continue;
            }
            propertyValues.setProperty(SCRIPT_PROPERTIES.get(i), scriptsMap.get(scriptCode));
        }
    }
}
