package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.editablepropertylist;

import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.base.Preconditions;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextChangedEvent;
import ru.naumen.core.client.content.ContextChangedHandler;
import ru.naumen.metainfo.shared.Constants.MetaClassProperties;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.ui.EditablePropertyList;
import ru.naumen.metainfo.shared.ui.PropertyList;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.common.content.TitledContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.ContentUtils;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.PropertyGridFlowContentDisplay;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.PropertyListContentDisplayMessages;
import ru.naumen.metainfoadmin.client.group.BeforeDeleteGroupEvent;
import ru.naumen.metainfoadmin.client.group.BeforeDeleteGroupHandler;

/**
 * Presenter для {@link PropertyList списка атрибутов}
 * Список атрибутов с возможностью редактирования.
 *
 * <AUTHOR>
 */
// TODO dzevako сделать наследование от AbstractEditablePropsContentPresenter
public class EditablePropertyListContentPresenter
        extends TitledContentPresenter<PropertyGridFlowContentDisplay, EditablePropertyList, UIContext>
        implements BeforeDeleteGroupHandler, ContextChangedHandler<Context>
{
    @Inject
    private AdminDialogMessages dialogMessages;
    @Inject
    private PropertyListContentDisplayMessages messages;
    @Inject
    private ContentUtils contentUtils;

    @Inject
    public EditablePropertyListContentPresenter(PropertyGridFlowContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "EditablePropertyList");
    }

    @Override
    public void onBeforeDeleteGroup(BeforeDeleteGroupEvent event)
    {
        if (getContent().getAttributeGroup().equals(event.getGroup().getCode()))
        {
            String formTitle = UI.Form.EDIT.equals(getContext().getCode())
                    ? dialogMessages.editForm() : dialogMessages.newForm();
            event.cancel(dialogMessages.place(formTitle));
        }
    }

    @Override
    public void onContextChanged(ContextChangedEvent<Context> e)
    {
        if (isRevealed)
        {
            refreshDisplay();
        }
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        refreshTable();
    }

    @Override
    protected String getHelpText()
    {
        AttributeGroup group = getContext().getMetainfo().getAttributeGroup(getContent().getAttributeGroup());
        return messages.formObjProperty() + ' '
               + messages.helpText(group.getTitle());
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        registerHandler(getContext().getEventBus().addHandler(ContextChangedEvent.getType(), this));
        registerHandler(eventBus.addHandler(BeforeDeleteGroupEvent.getType(), this));
    }

    private void refreshTable()
    {
        List<Attribute> editableAttributes = metainfoUtils.getEditableGroupAttributes(getContext().getMetainfo(),
                getContent().getAttributeGroup());

        Map<Attribute, IsWidget> attributes = CollectionUtils.convertToMap(editableAttributes,
                input ->
                {
                    Preconditions.checkNotNull(input);
                    return input;
                },
                input -> null);  // в контентах с редактируемыми атрибутами нет пользовательских кнопок

        Set<String> disabledAttributes = getRootContext().getContextProperty(MetaClassProperties.DISABLED_ATTRIBUTES);
        contentUtils.fillProperties(getDisplay(), attributes, false, true, disabledAttributes);
    }
}
