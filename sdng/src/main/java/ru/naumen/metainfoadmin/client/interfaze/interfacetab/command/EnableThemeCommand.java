package ru.naumen.metainfoadmin.client.interfaze.interfacetab.command;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataResponse;
import ru.naumen.core.shared.interfacesettings.dispatch.SwitchThemeAction;
import ru.naumen.core.shared.personalsettings.ThemeClient;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContextChangedEvent;

/**
 * Команда включения темы (тема становится доступной в качестве темы оператора)
 *
 * <AUTHOR>
 * @since 18.07.16
 */
public class EnableThemeCommand extends BaseCommandImpl<ThemeClient, InterfaceSettingsContext>
{
    public static final String ID = "enableThemeCommand";

    @Inject
    private DispatchAsync dispatch;
    @Inject
    private EventBus eventBus;

    @Inject
    public EnableThemeCommand(@Assisted ThemeCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<ThemeClient, InterfaceSettingsContext> param)
    {
        dispatch.execute(new SwitchThemeAction(param.getValue().getCode(), true),
                new BasicCallback<GetInterfaceTabDataResponse>()
                {
                    @Override
                    protected void handleSuccess(GetInterfaceTabDataResponse response)
                    {
                        eventBus.fireEvent(new InterfaceSettingsContextChangedEvent(response));
                    }
                });
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof ThemeClient))
        {
            return false;
        }

        ThemeClient item = (ThemeClient)input;
        return !item.isEnabled();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.SWITCH_ON;
    }
}
