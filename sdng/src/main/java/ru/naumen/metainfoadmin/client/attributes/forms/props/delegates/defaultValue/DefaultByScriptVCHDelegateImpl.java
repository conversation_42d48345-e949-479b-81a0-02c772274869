package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue;

import static java.util.Arrays.asList;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DEFAULT_VALUE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DEFAULT_VALUE_LABEL;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.SCRIPT_FOR_DEFAULT;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 11.10.2012
 *
 */
public class DefaultByScriptVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getRefreshProcess().startCustomProcess(asList(SCRIPT_FOR_DEFAULT, DEFAULT_VALUE_LABEL, DEFAULT_VALUE));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
        context.getPropertyControllers().get(AttributeFormPropertyCode.DEFAULT_BY_SCRIPT).fireUpdateTabOrderEvent();
    }
}
