package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode.REFERENCE_TAB_VALUE;

import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;
import com.google.inject.Singleton;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.dispatch2.GetNavigationReferencesToTabAction;
import ru.naumen.metainfo.shared.dispatch2.GetNavigationReferencesToTabResponse;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;

/**
 * <AUTHOR>
 * @since 13 февр. 2014 г.
 *
 */
@Singleton
public class ReferenceValueTabPropertyDelegateRefreshImpl implements
        PropertyDelegateRefresh<SelectItem, SingleSelectProperty<Reference>>
{
    @Inject
    private DispatchAsync dispatch;

    @Override
    public void refreshProperty(final PropertyContainerContext context, final SingleSelectProperty<Reference> property,
            final AsyncCallback<Boolean> callback)
    {
        context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE);
        String typeStr = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE);
        MenuItemType type = MenuItemType.valueOf(typeStr);
        boolean isLeftMenuRef = MenuItemType.TYPES_WITH_REFERENCE.contains(type) &&
                                context.getPropertyValues().hasProperty(MenuSettingsPropertyCode.PRESENTATION);
        if (!isLeftMenuRef && !MenuItemType.reference.equals(type))
        {
            context.setProperty(REFERENCE_TAB_VALUE, null);
            callback.onSuccess(false);
            return;
        }
        DtObject referenceDtO = context.getPropertyValues().<DtObject> getProperty(ReferenceCode.REFERENCE_VALUE);
        if (referenceDtO == null)
        {
            context.setProperty(REFERENCE_TAB_VALUE, null);
            callback.onSuccess(false);
            return;
        }
        List<String> tabUUIDs = referenceDtO.getProperty(ReferenceCode.TAB_UUIDS);
        boolean tabSelected = !ObjectUtils.isEmpty(tabUUIDs);
        if (!tabSelected)
        {
            context.setProperty(REFERENCE_TAB_VALUE, null);
            callback.onSuccess(false);
            return;
        }
        ClassFqn classFqn = referenceDtO.getProperty(ReferenceCode.CLASS_FQN);
        dispatch.execute(
                new GetNavigationReferencesToTabAction(classFqn, tabUUIDs.get(0)),
                new BasicCallback<GetNavigationReferencesToTabResponse>()
                {
                    @Override
                    protected void handleSuccess(GetNavigationReferencesToTabResponse response)
                    {
                        property.getValueWidget().clear();
                        for (Reference reference : response.res)
                        {
                            property.getValueWidget().addItem(reference.setLevel(reference.getLevel() - 1));
                        }
                        Reference value = context.getPropertyValues().getProperty(REFERENCE_TAB_VALUE);
                        Reference valueToSet = null == value ? null
                                : new Reference(value.getClassFqn(), value.getTabUUIDs());
                        property.trySetObjValue(valueToSet);
                        callback.onSuccess(!response.res.isEmpty());
                    }
                });

    }
}