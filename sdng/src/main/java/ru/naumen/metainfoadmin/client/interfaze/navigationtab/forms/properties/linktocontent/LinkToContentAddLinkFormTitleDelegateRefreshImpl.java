package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления свойства "Название формы добавления связи" на форме добавления элемента левого меню типа
 * "Ссылка на контент"
 *
 * <AUTHOR>
 * @since 30.10.2020
 */
@Singleton
public class LinkToContentAddLinkFormTitleDelegateRefreshImpl implements PropertyDelegateRefresh<String,
        TextBoxProperty>
{
    @Inject
    protected CommonMessages cmessages;

    @Inject
    protected MetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean isRelObjectListBlockVisible =
                LinkToContentMetaClassPropertiesProcessor.getRelObjectListFragmentVisibility(context);
        if (!isRelObjectListBlockVisible)
        {
            callback.onSuccess(false);
            return;
        }

        RelationsAttrTreeObject selectedAttr = context.getPropertyValues()
                .getProperty(MenuItemLinkToContentCode.ATTR_CHAIN);
        Attribute attribute = null == selectedAttr ? null : selectedAttr.getAttribute();
        boolean visible = null != attribute && !attribute.isComputable();
        property.setEnabled(visible);

        if ((selectedAttr == null) || (attribute == null))
        {
            property.setValue(StringUtilities.EMPTY, true);
            callback.onSuccess(true);
            return;
        }
        final ClassFqn relatedFqn = attribute.getType().<ObjectAttributeType> cast().getRelatedMetaClass();
        metainfoService.getMetaClass(relatedFqn.fqnOfClass(), new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass relatedClass)
            {
                property.setValue(cmessages.defaultAddingLinkFormCaption(relatedClass.getTitle()), true);
            }
        });
    }
}
