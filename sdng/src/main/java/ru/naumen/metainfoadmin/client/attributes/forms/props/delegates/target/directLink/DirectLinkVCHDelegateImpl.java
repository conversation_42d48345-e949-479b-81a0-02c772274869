package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.directLink;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.DirectLinkValueFormatter;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public class DirectLinkVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Inject
    AdminMetainfoServiceAsync metainfoService;
    @Inject
    DirectLinkValueFormatter valueFormatter;

    @Override
    public void onValueChanged(final PropertyContainerContext context)
    {
        String strValue = context.getPropertyValues().getProperty(DIRECT_LINK_TARGET);
        if (StringUtilities.isEmpty(strValue))
        {
            return;
        }
        ClassFqn fqn = valueFormatter.fqnFrom(strValue);
        String directLinkAttrCode = valueFormatter.attrCodeFrom(strValue);
        metainfoService.getMetaClass(fqn, new GetMetaClassCallback(context, directLinkAttrCode)
        {
            @Override
            protected void handleSuccess(MetaClass metaClass)
            {
                super.handleSuccess(metaClass);
                context.getRefreshProcess()
                        .startCustomProcess(Lists.newArrayList(EDIT_PRS, DEFAULT_VALUE, COMPLEX_RELATION_ATTR_GROUP));
                context.getPropertyControllers().get(PERMITTED_TYPES).refresh();
            }
        });
    }
}
