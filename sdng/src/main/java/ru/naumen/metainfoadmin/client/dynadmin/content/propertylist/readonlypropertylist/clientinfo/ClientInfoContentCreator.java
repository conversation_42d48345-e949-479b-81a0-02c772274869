package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.clientinfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ClientInfo;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.SimpleContentCreatorBase;

/**
 * {@link ContentCreator} для отображения параметров добавляемого контента типа "Информация о пользователе"
 *
 * <AUTHOR>
 *
 */
public class ClientInfoContentCreator extends SimpleContentCreatorBase<ClientInfo>
{
    @Inject
    private AdminDialogMessages dialogMessages;
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Inject
    private SingleSelectProperty<AttributeGroup> emplAttributeGroup;
    @Inject
    private SingleSelectProperty<AttributeGroup> ouAttributeGroup;
    @Inject
    private SingleSelectProperty<AttributeGroup> teamAttributeGroup;

    /**
     * Распределяет группы атрибутов сотрудника в emplGroups, а группы атрибутов отдела в ouGroups
     * @param metaClasses
     * @param emplGroups
     * @param ouGroups
     */
    public void distributeGroups(List<MetaClass> metaClasses, List<AttributeGroup> emplGroups,
            List<AttributeGroup> ouGroups, List<AttributeGroup> teamGroups)
    {
        for (MetaClass metaClass : metaClasses)
        {
            ArrayList<AttributeGroup> groups = Lists.newArrayList(metaClass.getAttributeGroups());
            metainfoUtils.sort(groups);
            // @formatter:off
            List<AttributeGroup> group = Employee.FQN.isSameClass(metaClass.getFqn()) ? emplGroups :
                    OU.FQN.isSameClass(metaClass.getFqn()) ? ouGroups : teamGroups;
            // @formatter:on
            group.addAll(groups);
        }
    }

    /**
     * Распределяет по группы атрибутов из переданных метаклассов
     * @param metaClasses
     */
    public void initAttributes(List<MetaClass> metaClasses)
    {
        List<AttributeGroup> emplGroups = new ArrayList<AttributeGroup>();
        List<AttributeGroup> ouGroups = new ArrayList<AttributeGroup>();
        List<AttributeGroup> teamGroups = new ArrayList<AttributeGroup>();
        distributeGroups(metaClasses, emplGroups, ouGroups, teamGroups);

        emplAttributeGroup.getValueWidget().addItems(emplGroups);
        ouAttributeGroup.getValueWidget().addItems(ouGroups);
        teamAttributeGroup.getValueWidget().addItems(teamGroups);
    }

    @Override
    protected void bindPropertiesInner()
    {
        super.bindPropertiesInner();

        addWithMarker(dialogMessages.emplAttributeGroup(), emplAttributeGroup);
        //emplAttributeGroup.getValueWidget().setHasEmptyOption(false);
        addWithMarker(dialogMessages.ouAttributeGroup(), ouAttributeGroup);
        //ouAttributeGroup.getValueWidget().setHasEmptyOption(false);
        addWithMarker(dialogMessages.teamAttributeGroup(), teamAttributeGroup);
        //teamAttributeGroup.getValueWidget().setHasEmptyOption(false);
        metainfoService.getFullMetaInfo(Arrays.asList(Employee.FQN, OU.FQN, Team.FQN),
                new BasicCallback<List<MetaClass>>()
                {
                    @Override
                    protected void handleSuccess(List<MetaClass> value)
                    {
                        initAttributes(value);
                    }
                });
        ensureDebugId();
    }

    protected void ensureDebugId()
    {
        DebugIdBuilder.ensureDebugId(emplAttributeGroup, "emplAttributeGroup");
        DebugIdBuilder.ensureDebugId(ouAttributeGroup, "ouAttributeGroup");
        DebugIdBuilder.ensureDebugId(teamAttributeGroup, "teamAttributeGroup");
    }

    @Override
    protected ClientInfo getContentInner()
    {
        ClientInfo content = contentProvider.get();
        content.setEmplAttributeGroup(emplAttributeGroup.getValue().getCode());
        content.setOuAttributeGroup(ouAttributeGroup.getValue().getCode());
        content.setTeamAttributeGroup(teamAttributeGroup.getValue().getCode());
        content.setAttributeGroup(content.getEmplAttributeGroup());
        return content;
    }

    @Override
    protected String getDefaultCaption()
    {
        return dialogMessages.clientInfo();
    }
}
