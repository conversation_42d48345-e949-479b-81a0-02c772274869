package ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.utils.ObjectUtils;

import com.google.common.base.Preconditions;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * Местоположение хлебной крошки 
 *
 * <AUTHOR>
 * @since 07 июля 2014 г.
 */
public class BreadCrumbPlace extends Place
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<BreadCrumbPlace>
    {
        @Override
        public BreadCrumbPlace getPlace(String token)
        {
            Preconditions.checkNotNull(token, "Bad BreadCrumbPlace");
            return new BreadCrumbPlace(token);
        }

        @Override
        public String getToken(BreadCrumbPlace place)
        {
            return place == null ? StringUtilities.EMPTY : place.getCode();
        }
    }

    public static final String PLACE_PREFIX = "crumb";

    private String code;
    private Crumb item;
    private DtoContainer<NavigationSettings> settings;

    public BreadCrumbPlace(Crumb item, DtoContainer<NavigationSettings> settings)
    {
        this.item = item;
        this.settings = settings;
        this.code = item.getCode();
    }

    public BreadCrumbPlace(String code)
    {
        this.code = code;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null || this.getClass() != obj.getClass())
        {
            return false;
        }
        BreadCrumbPlace other = (BreadCrumbPlace)obj;
        return ObjectUtils.equals(this.code, other.code);
    }

    public String getCode()
    {
        return this.code;
    }

    public Crumb getCrumb()
    {
        return item;
    }

    public DtoContainer<NavigationSettings> getSettings()
    {
        return settings;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(code);
    }
}