package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.MoveNavigationMenuItemAction;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.AddTopMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DeleteTopMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DisableTopMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EditTopMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EnableTopMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.MoveTopMenuItemDownCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.MoveTopMenuItemUpCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsMenuItemAbstractCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsTMCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.NavigationSettingsChangedEvent;

/**
 * Реализация презентера списка элементов верхнего меню
 *
 * <AUTHOR>
 * @since 19.06.2020
 */
public class NavigationTopMenuItemsPresenter extends NavigationMenuItemsPresenter<MenuItem>
{
    class TopItemsDataProvider extends ItemsDataProvider
    {
        @Override
        protected List<MenuItem> getMenuItems()
        {
            return settings.get().getTopMenuItems();
        }
    }

    @Inject
    private final NavigationSettingsMessages messages;

    @Inject
    public NavigationTopMenuItemsPresenter(ScrollableTableDisplayImpl<MenuItem> display, EventBus eventBus,
            CommonMessages cmesages, NavigationSettingsMessages messages)
    {
        super(display, eventBus, cmesages, messages);
        this.messages = messages;
    }

    @Override
    public void onNavigationSettingsChanged(NavigationSettingsChangedEvent event)
    {
        DtoContainer<NavigationSettings> newSettings = event.getSettings();
        if (settings.get().isShowTopMenu() == !newSettings.get().isShowTopMenu())
        {
            refreshSettings(newSettings);
            refreshDisplay();
        }
    }

    protected void addFrontActionColumns()
    {
        addActionColumn(MoveTopMenuItemUpCommand.ID);
        addActionColumn(MoveTopMenuItemDownCommand.ID);
    }

    protected void addBackActionColumns()
    {
        addActionColumn(EnableTopMenuItemCommand.ID, DisableTopMenuItemCommand.ID);
        addActionColumn(EditTopMenuItemCommand.ID);
        addActionColumn(DeleteTopMenuItemCommand.ID);
    }

    @Override
    protected NavigationSettingsMenuItemAbstractCommandParam<MenuItem> getParam(
            DtoContainer<NavigationSettings> settings,
            OnStartCallback<DtoContainer<NavigationSettings>> refreshCallback)
    {
        return new NavigationSettingsTMCommandParam(settings, refreshCallback);
    }

    @Override
    protected Map<String, LinkedList<String>> getMenuItemPaths()
    {
        return settings.get().getTopMenuItemPaths();
    }

    @Override
    protected boolean ifMenuBlockEnabled()
    {
        return settings.get().isShowTopMenu();
    }

    @Override
    protected NavigationMenuItemPlace<MenuItem> getNewPlace(MenuItem item)
    {
        return new NavigationTopMenuItemPlace(item.getCode());
    }

    protected String getAddCommandId()
    {
        return AddTopMenuItemCommand.ID;
    }

    protected String getDisplayCaption()
    {
        return messages.topMenu();
    }

    @Override
    protected NavigationMenuItemsPresenter<MenuItem>.ItemsDataProvider getDataProvider()
    {
        return new TopItemsDataProvider();
    }

    @Override
    protected NavigationMenuListDnDController getGroupController()
    {
        return new NavigationMenuListDnDController()
        {
            @Override
            protected Map<String, LinkedList<String>> getMenuItemPaths()
            {
                return settings.get().getTopMenuItemPaths();
            }

            @Override
            protected MoveNavigationMenuItemAction getMoveAction()
            {
                return new MoveNavigationMenuItemAction();
            }
        };
    }

    @Override
    protected List<MenuItem> getMenuItems()
    {
        return settings.get().getTopMenuItems();
    }

    protected String getTableDebugId()
    {
        return "top-menu-items-table";
    }

    @Override
    protected PermissionType getCommandPermissionType(String command)
    {
        return DeleteTopMenuItemCommand.ID.equals(command) ? PermissionType.DELETE : PermissionType.EDIT;
    }

}
