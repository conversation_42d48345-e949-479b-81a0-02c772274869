package ru.naumen.metainfoadmin.client.scheduler.command;

import jakarta.annotation.Nullable;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 * <AUTHOR>
 * @since 27.08.2011
 */
public class TriggerCommandParam extends CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>>
{
    private SchedulerTask schedulerTask;
    private String schedulerTaskCode;

    public TriggerCommandParam(@Nullable DtoContainer<Trigger> value, AsyncCallback<DtoContainer<Trigger>> callback,
            SchedulerTask schedulerTask)
    {
        super(value, callback);
        this.schedulerTask = schedulerTask;
        this.schedulerTaskCode = schedulerTask.getCode();
    }

    public TriggerCommandParam(DtoContainer<Trigger> value, AsyncCallback<DtoContainer<Trigger>> callback,
            String schedulerTaskCode)
    {
        super(value, callback);
        this.schedulerTaskCode = schedulerTaskCode;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <V extends FactoryParam<DtoContainer<Trigger>, DtoContainer<Trigger>>> V cloneIt()
    {
        if (null == schedulerTask)
        {
            return (V)new TriggerCommandParam(getValue(), getCallback(), getSchedulerTaskCode());
        }
        return (V)new TriggerCommandParam(getValue(), getCallback(), schedulerTask);
    }

    public SchedulerTask getSchedulerTask()
    {
        return schedulerTask;
    }

    public String getSchedulerTaskCode()
    {
        return schedulerTaskCode;
    }
}
