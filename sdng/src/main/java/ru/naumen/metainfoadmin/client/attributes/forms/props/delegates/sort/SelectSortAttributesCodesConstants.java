package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.sort;

import static ru.naumen.core.shared.Constants.AbstractBO.TITLE;
import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_CODE;

import com.google.common.collect.ImmutableList;

/**
 *
 * <AUTHOR>
 * @since 22.05.2013
 *
 */
public class SelectSortAttributesCodesConstants
{
    public static final ImmutableList<String> ATTR_CODES_FOR_SORT = ImmutableList.of(TITLE, ITEM_CODE);
}
