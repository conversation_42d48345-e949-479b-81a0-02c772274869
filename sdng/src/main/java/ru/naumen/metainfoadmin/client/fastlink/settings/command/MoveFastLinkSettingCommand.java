package ru.naumen.metainfoadmin.client.fastlink.settings.command;

import java.util.List;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;

import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSetting;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSettingWithTitles;
import ru.naumen.metainfo.shared.fastlink.settings.dispatch.MoveFastLinkSettingAction;

/**
 * <AUTHOR>
 * @since 01.03.18
 */
public abstract class MoveFastLinkSettingCommand
        extends BaseCommandImpl<FastLinkSetting, List<DtoContainer<FastLinkSettingWithTitles>>>
{
    @Inject
    private DispatchAsync dispatch;

    protected final boolean toUp;

    /**
     * @param param
     * @param toUp направление перемещения. true к началу, false к концу.
     * @throws IllegalArgumentException если значение <b>direction</b> не равно -1 или 1.
     */
    protected MoveFastLinkSettingCommand(FastLinkSettingsCommandParam param, boolean toUp)
    {
        super(param);
        this.toUp = toUp;
    }

    @Override
    public void execute(CommandParam<FastLinkSetting, List<DtoContainer<FastLinkSettingWithTitles>>> param)
    {
        FastLinkSettingsCommandParam p = (FastLinkSettingsCommandParam)param;
        FastLinkSetting item = p.getValue();

        MoveFastLinkSettingAction action = new MoveFastLinkSettingAction(item.getCode(), toUp);
        dispatch.execute(action, new SimpleResultCallbackDecorator<>(p.getCallbackSafe()));
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof FastLinkSetting) || null == param)
        {
            return false;
        }
        FastLinkSetting item = (FastLinkSetting)input;
        FastLinkSettingsCommandParam p = (FastLinkSettingsCommandParam)getParam();
        int idx = p.getSettings().indexOf(item);
        return idx >= 0 && isPossible(idx, p.getSettings().size());
    }

    protected abstract boolean isPossible(int index, int count);
}