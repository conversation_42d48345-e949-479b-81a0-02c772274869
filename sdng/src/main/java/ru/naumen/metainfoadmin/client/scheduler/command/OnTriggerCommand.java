package ru.naumen.metainfoadmin.client.scheduler.command;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 * Команда включения правила выполнения задачи планировщика.
 * <AUTHOR>
 * @since 22.08.2011
 */
public class OnTriggerCommand extends ChangeTriggerStatusCommand
{
    @Inject
    public OnTriggerCommand(@Assisted CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>> param)
    {
        super(param);
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof DtoContainer<?>))
        {
            return false;
        }
        @SuppressWarnings("unchecked")
        DtoContainer<Trigger> trigger = (DtoContainer<Trigger>)input;
        return !trigger.get().isEnabled();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.SWITCH_ON;
    }
}
