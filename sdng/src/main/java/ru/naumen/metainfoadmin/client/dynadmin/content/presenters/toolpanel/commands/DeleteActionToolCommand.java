package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.commands;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.client.HasContextCommandParamUtils;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.ObjectActionsMenuHolder;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.MoveToolEvent;

/**
 * <AUTHOR>
 * @since 01.03.17
 */
public class DeleteActionToolCommand extends BaseCommandImpl<ActionTool, ObjectActionsMenuHolder>
{
    public static final String ID = "deleteActionToolCommand";

    @Inject
    public DeleteActionToolCommand(@Assisted EditObjectActionsMenuCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(final CommandParam<ActionTool, ObjectActionsMenuHolder> param)
    {
        EditObjectActionsMenuCommandParam p = (EditObjectActionsMenuCommandParam)prepareParam(this.param);
        p.getContext().getEventBus().fireEvent(new MoveToolEvent(param.getValue(), false, null, false));
    }

    @Override
    public boolean isPossible(Object input)
    {
        return HasContextCommandParamUtils.hasPermission(param, input, PermissionType.DELETE);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }
}
