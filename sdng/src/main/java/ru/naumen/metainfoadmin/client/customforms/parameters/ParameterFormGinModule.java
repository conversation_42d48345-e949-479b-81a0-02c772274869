package ru.naumen.metainfoadmin.client.customforms.parameters;

import com.google.gwt.inject.client.AbstractGinModule;

import ru.naumen.core.client.attr.presentation.FormParameterAvailableTypesProvider;
import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormGinModule;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormMessages;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormPropertyControllerFactorySelectorImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.edit.EditAttributeFormPropertyValuesInitializerImpl;
import ru.naumen.metainfoadmin.shared.customforms.SaveFormParameterAction;

/**
 *
 * <AUTHOR>
 * @since 23 мая 2016 г.
 */
public class ParameterFormGinModule<F extends ParameterForm> extends AbstractGinModule
{
    public static <F extends ParameterForm> ParameterFormGinModule<F> create(Class<F> form)
    {
        return new ParameterFormGinModule<F>(form);
    }

    private final Class<F> form;

    private Class<? extends AttributeFormMessages<F>> messages;

    private ParameterFormGinModule(Class<F> form)
    {
        this.form = form;
    }

    public ParameterFormGinModule<F> setMessages(Class<? extends AttributeFormMessages<F>> messages)
    {
        this.messages = messages;
        return this;
    }

    @Override
    protected void configure()
    {
        //@formatter:off
        install(PropertyControllerGinModule.create(Attribute.class, form)
                .setPropertyControllerFactory(Gin.<AttributeFormPropertyControllerFactorySelectorImpl<F>>
                        typeLiteral(AttributeFormPropertyControllerFactorySelectorImpl.class, form))
                .setPropertyParametersDescriptorFactory(Gin.<ParameterFormPropertyParametersDescriptorFactoryImpl<F>>
                        typeLiteral(ParameterFormPropertyParametersDescriptorFactoryImpl.class, form)));
        
        install(AttributeFormGinModule.create(form)
                .setPropertyControllerFactorySync(Gin.<ParameterFormPropertyControllerFactorySyncImpl<F>>
                        typeLiteral(ParameterFormPropertyControllerFactorySyncImpl.class, form))
                .setAfterBindHandler(Gin.<FormParameterFormAfterBindHandler<F>>typeLiteral(FormParameterFormAfterBindHandler.class, form))
                .setContextPropertiesSetter(Gin.<ParameterFormContextPropertySetter<F>>typeLiteral(ParameterFormContextPropertySetter.class, form))
                .setApplyFormHandler(Gin.<FormParameterFormApplyHandler<F>>typeLiteral(FormParameterFormApplyHandler.class, form))
                .setPropertyValuesInitializer(Gin.<EditAttributeFormPropertyValuesInitializerImpl<F>>
                        typeLiteral(EditAttributeFormPropertyValuesInitializerImpl.class, form))
                .setMessages(messages)
                .setConstants(Gin.<CustomFormParameterFormConstants<F>>typeLiteral(CustomFormParameterFormConstants.class, form)) 
                .setAction(SaveFormParameterAction.class)  
                .setAttrService(Gin.<FormParameterAvailableTypesProvider<F>>typeLiteral(FormParameterAvailableTypesProvider.class, form))
                .setControllerFactory(Gin.<ParameterFormPropertyControllerFactoryAggregatedImpl<F>>
                        typeLiteral(ParameterFormPropertyControllerFactoryAggregatedImpl.class, form)));
        
        install(new PropertyDelegatesGinModule<>(form));
        //@formatter:on
    }

}
