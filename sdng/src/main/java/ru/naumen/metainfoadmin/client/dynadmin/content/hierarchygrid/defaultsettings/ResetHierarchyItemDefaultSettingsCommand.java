package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.defaultsettings;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.advlist.AdvlistConstants;
import ru.naumen.metainfo.shared.ui.HierarchyGridDefaultViewSettings;
import ru.naumen.metainfo.shared.ui.HierarchyGridDefaultViewSettings.HeaderAppearance;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.AdvlistDefaultPrsMessages;
import ru.naumen.metainfoadmin.shared.dynadmin.HierarchyItemSettingsContext;

/**
 * Команда сброса значения по умолчанию для уровня иерархического списка.
 * <AUTHOR>
 * @since 25.01.2021
 */
public class ResetHierarchyItemDefaultSettingsCommand
        extends BaseCommandImpl<HierarchyItemSettingsContext, HierarchyItemSettingsContext>
{
    private final Dialogs dialogs;
    private final AdvlistDefaultPrsMessages advlistDefaultPrsMessages;

    @Inject
    public ResetHierarchyItemDefaultSettingsCommand(@Assisted HierarchyDefaultSettingsCommandParam param,
            Dialogs dialogs, AdvlistDefaultPrsMessages advlistDefaultPrsMessages)
    {
        super(param);
        this.dialogs = dialogs;
        this.advlistDefaultPrsMessages = advlistDefaultPrsMessages;
    }

    @Override
    public void execute(CommandParam<HierarchyItemSettingsContext, HierarchyItemSettingsContext> param)
    {
        HierarchyItemSettingsContext hierarchyItem = param.getValue();
        dialogs.question(advlistDefaultPrsMessages.resetDialogCaption(),
                advlistDefaultPrsMessages.resetSettingsForItemDialog(hierarchyItem.getTitle()),
                new DialogCallback()
                {
                    @Override
                    protected void onYes(final Dialog widget)
                    {
                        HierarchyGridDefaultViewSettings defaultSettings = new HierarchyGridDefaultViewSettings();
                        defaultSettings.setHeaderAppearance(
                                hierarchyItem.isShowNested() ?
                                        HeaderAppearance.HIDE_NESTED_ONLY :
                                        HeaderAppearance.NOT_HIDE);
                        hierarchyItem.setDefaultSettings(defaultSettings);
                        widget.destroy();
                        param.getCallback().onSuccess(null);
                    }
                });
    }

    @Nullable
    @Override
    public FontIconDisplay<HierarchyItemSettingsContext> getFontIcon()
    {
        FontIconDisplay<HierarchyItemSettingsContext> fontIcon = super.getFontIcon();
        if (null == fontIcon)
        {
            return null;
        }
        fontIcon.setTitle(messages.reset());
        return fontIcon;
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof HierarchyItemSettingsContext))
        {
            return false;
        }
        HierarchyItemSettingsContext context = (HierarchyItemSettingsContext)input;
        HierarchyGridDefaultViewSettings settings = context.getDefaultSettings();
        return settings.getPageSize() != AdvlistConstants.DEFAULT_PAGE_SIZE || settings.getListFilter().hasElements()
               || settings.getListSort().hasElements() || !settings.isInherit() || hasNotDefaultHeaderAppearance(
                context) || hasNotDefaultColumnsWidth(settings);
    }

    private static boolean hasNotDefaultHeaderAppearance(HierarchyItemSettingsContext context)
    {
        HierarchyGridDefaultViewSettings settings = context.getDefaultSettings();
        HeaderAppearance headerAppearance = settings.getHeaderAppearance();
        return (context.isShowNested() && !headerAppearance.equals(HeaderAppearance.defaultValue()))
               || (!context.isShowNested() && !headerAppearance.equals(HeaderAppearance.NOT_HIDE));
    }

    private static boolean hasNotDefaultColumnsWidth(HierarchyGridDefaultViewSettings settings)
    {
        return settings.getColumnList().stream().anyMatch(column -> column.getWidthColumn() != 0);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DEL;
    }
}
