package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInGroup;

/**
 * Представление для отображения значения места использования "Группы атрибутов" на форме "Используется в настройках"
 * в таблице атрибутов
 * <AUTHOR>
 * @since 3 Jul 18
 */
@Singleton
public class AttributeUsageInGroupHtmlFactoryImpl extends AttributeHtmlFactoryImpl<AttributeUsageInGroup>
{
    @Inject
    private Formatters formatters;
    @Inject
    private CommonMessages messages;
    @Inject
    private PlaceHistoryMapper historyMapper;

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInGroup usage)
    {
        return new SafeHtmlBuilder().append(formatters.formatHyperlinkAsHtml(createLinkToAttrGroupTab(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLinkToAttrGroupTab(AttributeUsageInGroup usage)
    {
        ClassFqn declaredMetaClass = usage.getTypes().isEmpty() ? usage.getClazz().getFqn() : usage.getTypes().get(0);

        MetaClassPlace mcPlace = new MetaClassPlace(declaredMetaClass, "Class.Groups");
        mcPlace.put(Constants.ATTRIBUTE_GROUP, usage.getCode());

        //@formatter:off
        return new Hyperlink(
                    messages.attributeGroup() + " \"" + usage.getTitle() + "\"",
                    StringUtilities.getHrefByToken(historyMapper.getToken(mcPlace)));
        //@formatter:on
    }
}