package ru.naumen.metainfoadmin.client.eventaction;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.IFormPropertiesCreator;
import ru.naumen.core.client.IInfoPropertiesCreator;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.eventaction.ActionConditionWithScript;

/**
 * Интерфейс для {@link Presenter} , создающего условие выполнения действия по событию.
 * <p>
 * По сути является частью формы добавления условия действия по событию
 *
 * <AUTHOR>
 *
 */
public interface ActionConditionCreator extends IFormPropertiesCreator,
        IInfoPropertiesCreator<ActionConditionWithScript>
{
    ActionConditionWithScript getCondition();

    IProperties getConditionProperties();
}
