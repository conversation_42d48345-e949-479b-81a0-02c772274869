package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.editablepropertylist;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.shared.ui.EditablePropertyList;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.EditPropertyListContentPresenter;

/**
 * Представление формы редактирования контента "Параметры на форме".
 * <AUTHOR>
 * @since Jul 09, 2019
 */
public class EditEditablePropertyListContentPresenter extends EditPropertyListContentPresenter<EditablePropertyList>
{
    @Inject
    private EditablePropertyListConditionFormCustomizer conditionFormCustomizer;

    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> resetIfHidden;

    @Inject
    public EditEditablePropertyListContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void updateCurrentContent()
    {
        super.updateCurrentContent();
        content.setResetValuesIfHidden(conditionFormCustomizer.isResetIfHidden());
    }

    @Override
    protected void restoreContent(EditablePropertyList oldContent)
    {
        super.restoreContent(oldContent);
        content.setResetValuesIfHidden(oldContent.isResetValuesIfHidden());
    }

    @Override
    protected boolean isContentEquals(EditablePropertyList oldContent)
    {
        return super.isContentEquals(oldContent)
               && eq(oldContent.isResetValuesIfHidden(), content.isResetValuesIfHidden());
    }

    @Override
    protected void bindVisibilityCondition()
    {
        super.bindVisibilityCondition();
        resetIfHidden.setCaption(contentMessages.resetIfHidden());
        resetIfHidden.setDisable();
        DebugIdBuilder.ensureDebugId(resetIfHidden, "resetIfHiddenRO");
        getDisplay().add(resetIfHidden);
        conditionFormCustomizer.setResetIfHidden(content.isResetValuesIfHidden());
        conditionFormCustomizer.setLinkedProperty(resetIfHidden);
        visibilityCondition.getValueWidget().setConditionFormCustomizer(conditionFormCustomizer);
    }
}
