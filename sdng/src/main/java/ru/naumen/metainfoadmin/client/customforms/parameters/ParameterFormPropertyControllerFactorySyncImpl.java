package ru.naumen.metainfoadmin.client.customforms.parameters;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;

import ru.naumen.core.client.validation.DefaultCodeValidator;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetBindDelegate;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetRefreshDelegate;
import ru.naumen.metainfoadmin.client.widgets.properties.ScriptComponentEditProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindTextBoxFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.AbstractFormPropertyControllerFactorySyncImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormValidationCode;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation.ComplexRelationAggrAttrGroupRefreshDelegateFactory;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.digitsrestriction.DigitsCountRestrictionBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.digitsrestriction.DigitsCountRestrictionRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.digitsrestriction.DigitsCountRestrictionVCHDelegateImpl;

/**
 * Реализация {@link PropertyControllerFactorySyncImpl} для форм добавления и редактирования
 * параметров настраиваемых форм
 *
 * <AUTHOR>
 * @since 20 мая 2016 г.
 */
@Singleton
public class ParameterFormPropertyControllerFactorySyncImpl<F extends ParameterForm>
        extends AbstractFormPropertyControllerFactorySyncImpl<F>
{
    @Inject
    PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Boolean, BooleanCheckBoxProperty> booleanPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<SelectItem, ListBoxProperty> listBoxPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxWithEmptyPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<ScriptDto, ScriptComponentEditProperty> scriptEditorPropertyFactory;

    @Inject
    @Named(COMPLEX_RELATION)
    AttributeFormPropertyDelegateVCH<F> complexVCHDelegate;
    @Inject
    @Named(COMPLEX_RELATION)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> complexRefreshDelegate;
    @Inject
    @Named(COMPLEX_RELATION_ATTR_GROUP)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> complexAttrGroupRefreshDelegate;
    @Inject
    @Named(COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> complexStructuredObjectsViewRefreshDelegate;
    @Inject
    @Named(EDIT_ON_COMPLEX_FORM_ONLY)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> editOnComplexFormOnlyRefreshDelegate;

    @Inject
    @Named(QUICK_ADD_FORM_CODE)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> quickAddFormRefreshDelegate;
    @Inject
    @Named(QUICK_EDIT_FORM_CODE)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> quickEditFormRefreshDelegate;

    @Inject
    ComplexRelationAggrAttrGroupRefreshDelegateFactory<F> complexAggrAttrGroupRefreshDelegateFactory;

    @Inject
    PropertyDelegateBindTextBoxFactory textBoxBindDelegateFactory;

    @Inject
    @Named(COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT)
    AttributeFormPropertyDelegateRefresh<F, ScriptDto, ScriptComponentEditProperty> computeAnyCatalogElementsScriptRefreshDelegate;

    @Inject
    @Named(COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT)
    AttributeFormPropertyDelegateBind<F, ScriptDto, ScriptComponentEditProperty> computeAnyCatalogElementsScriptBindDelegate;

    @Inject
    @Named(HAS_GROUP_SEPARATORS)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> hasGroupSeparatorsRefreshDelegate;
    @Inject
    @Named(HAS_GROUP_SEPARATORS)
    AttributeFormPropertyDelegateVCH<F> hasGroupSeparatorsVCHDelegate;

    @Inject
    @Named(STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> structuredObjectsViewForBuildingTreeRefreshDelegate;

    @Inject
    DigitsCountRestrictionRefreshDelegateImpl<F> digitsCountRestrictionRefreshDelegate;
    @Inject
    DigitsCountRestrictionBindDelegateImpl<F> digitsCountRestrictionBindDelegate;
    @Inject
    DigitsCountRestrictionVCHDelegateImpl<F> digitsCountRestrictionVCHDelegate;
    @Inject
    SettingsSetBindDelegate settingsSetBindDelegate;
    @Inject
    SettingsSetRefreshDelegate settingsSetRefreshDelegate;

    @Inject
    public void setUpValidators(DefaultCodeValidator codeValidator)
    {
        codeValidators.put(codeValidator, AttributeFormValidationCode.DEFAULT);
    }

    @Override
    protected void build()
    {
        super.build();
        register(CODE, textBoxPropertyFactory)
                .setBindDelegate(textBoxBindDelegateFactory.create(Constants.MAX_TITLE_LENGTH))
                .setValidators(codeValidators);
        register(COMPLEX_RELATION, listBoxPropertyFactory)
                .setVchDelegate(complexVCHDelegate)
                .setRefreshDelegate(complexRefreshDelegate);
        register(COMPLEX_RELATION_ATTR_GROUP, listBoxPropertyFactory)
                .setRefreshDelegate(complexAttrGroupRefreshDelegate)
                .setValidators(selectListTargetValidators);
        register(COMPLEX_EMPLOYEE_ATTR_GROUP, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(
                        complexAggrAttrGroupRefreshDelegateFactory.create(Employee.FQN, COMPLEX_EMPLOYEE_ATTR_GROUP))
                .setValidators(selectListTargetValidators);
        register(COMPLEX_TEAM_ATTR_GROUP, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(
                        complexAggrAttrGroupRefreshDelegateFactory.create(Team.FQN, COMPLEX_TEAM_ATTR_GROUP))
                .setValidators(selectListTargetValidators);
        register(COMPLEX_OU_ATTR_GROUP, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(complexAggrAttrGroupRefreshDelegateFactory.create(OU.FQN, COMPLEX_OU_ATTR_GROUP))
                .setValidators(selectListTargetValidators);
        register(COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW, listBoxPropertyFactory)
                .setRefreshDelegate(complexStructuredObjectsViewRefreshDelegate)
                .setValidators(selectListTargetValidators);
        register(QUICK_ADD_FORM_CODE, listBoxPropertyFactory)
                .setRefreshDelegate(quickAddFormRefreshDelegate);
        register(QUICK_EDIT_FORM_CODE, listBoxPropertyFactory)
                .setRefreshDelegate(quickEditFormRefreshDelegate);
        register(COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT, scriptEditorPropertyFactory)
                .setBindDelegate(computeAnyCatalogElementsScriptBindDelegate)
                .setRefreshDelegate(computeAnyCatalogElementsScriptRefreshDelegate);
        register(HAS_GROUP_SEPARATORS, booleanPropertyFactory)
                .setRefreshDelegate(hasGroupSeparatorsRefreshDelegate)
                .setVchDelegate(hasGroupSeparatorsVCHDelegate);
        register(EDIT_ON_COMPLEX_FORM_ONLY, booleanPropertyFactory)
                .setRefreshDelegate(editOnComplexFormOnlyRefreshDelegate);
        register(SETTINGS_SET, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(settingsSetBindDelegate)
                .setRefreshDelegate(settingsSetRefreshDelegate);
        register(STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE, listBoxPropertyFactory)
                .setRefreshDelegate(structuredObjectsViewForBuildingTreeRefreshDelegate)
                .setValidators(selectListTargetValidators);
    }
}
