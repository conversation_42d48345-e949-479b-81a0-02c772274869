package ru.naumen.metainfoadmin.client.structuredobjectsviews.card;

import static com.google.gwt.place.shared.Place.NOWHERE;
import static ru.naumen.core.client.adminpermission.AdminPermissionUtils.hasDeletePermission;
import static ru.naumen.core.client.adminpermission.AdminPermissionUtils.hasEditPermission;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;

import java.util.ArrayList;

import com.google.common.collect.Sets;
import com.google.gwt.cell.client.SafeHtmlCell;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NodeList;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safecss.shared.SafeStyles;
import com.google.gwt.safecss.shared.SafeStylesBuilder;
import com.google.gwt.safecss.shared.SafeStylesUtils;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.RowStyles;
import com.google.gwt.view.client.AbstractDataProvider;
import com.google.gwt.view.client.HasData;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.group.HierarchicalListEditorDnDController;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.columns.FontIconColumn;
import ru.naumen.core.client.widgets.columns.HTMLCell;
import ru.naumen.core.client.widgets.columns.LineTextCell;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumnFactory;
import ru.naumen.core.client.widgets.columns.LinkToPlaceWithIndentColumn;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.StructuredObjectsView;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.StructuredObjectsViewItemClient;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.dispatch.ChangeStructuredObjectsViewItemsOrderAction;
import ru.naumen.metainfoadmin.client.CatalogCellTableResources.CatalogCellTableStyle;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableResources;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsMessages;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.StructuredObjectsViewItemsCommandCode;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.commands.StructuredObjectsViewItemsCommandParam;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;
import ru.naumen.objectlist.client.mode.active.extended.advlist.filter.ListFilterConditionTextFactory;

/**
 * Блок "Элементы структуры" на карточке.
 * TODO: рассмотреть возможность заменить механизм DnD на используемый в TreeItemsTablePresenterBase с возможностью
 * TODO: перемещения между уровнями дерева
 * <AUTHOR>
 * @since 26.11.2019
 */
public class StructuredObjectsViewItemsPresenter extends BasicPresenter<TableDisplay<StructuredObjectsViewItemClient>>
{
    class ItemsDataProvider extends AbstractDataProvider<StructuredObjectsViewItemClient>
    {
        @Override
        protected void onRangeChanged(final HasData<StructuredObjectsViewItemClient> display)
        {
            List<StructuredObjectsViewItemClient> itemsList = new ArrayList<>();
            List<StructuredObjectsViewItemClient> items = structuredObjectsView.getProperty(
                    StructuredObjectsView.ITEMS);
            if (!items.isEmpty())
            {
                reqFillList(items, itemsList);
            }
            display.setRowData(0, itemsList);
            display.setRowCount(itemsList.size(), true);

            Scheduler.get().scheduleDeferred(StructuredObjectsViewItemsPresenter.this::updateDnDControllers);
        }

        private void reqFillList(Iterable<StructuredObjectsViewItemClient> items,
                List<StructuredObjectsViewItemClient> result)
        {
            items.forEach(item ->
            {
                result.add(item);
                reqFillList(item.getChildren(), result);
            });
        }
    }

    class ItemsRowStyles implements RowStyles<StructuredObjectsViewItemClient>
    {
        @Override
        public String getStyleNames(StructuredObjectsViewItemClient item, int rowIndex)
        {
            CatalogCellTableStyle cellTableStyle = cellTableResources.cellTableStyle();
            return item.getLevel() == 0 ? cellTableStyle.folderRow() : null;
        }
    }

    private class StructuredObjectsViewItemsListDnDController extends HierarchicalListEditorDnDController
    {
        public StructuredObjectsViewItemsListDnDController()
        {
            super(getDisplay().getTableContainer().getElement());
        }

        @Override
        public void move(final int oldPosition, final int newPosition, ReadyState readyState)
        {
            StructuredObjectsViewItemClient item = getDisplay().getTable().getVisibleItem(getGroupIndexList().get(
                    oldPosition));
            ChangeStructuredObjectsViewItemsOrderAction action = new ChangeStructuredObjectsViewItemsOrderAction(
                    structuredObjectsView.getUUID(), item, newPosition);
            dispatch.execute(action, new BasicCallback<SimpleResult<DtObject>>(readyState)
            {
                @Override
                protected void handleSuccess(SimpleResult<DtObject> value)
                {
                    refreshCallback.onSuccess(value.get());
                }
            });
        }
    }

    private final Function<StructuredObjectsViewItemClient, SafeStyles> TITLE_STYLES = input ->
    {
        SafeStylesBuilder sb = new SafeStylesBuilder();
        Integer level = input.getLevel();
        if (level == 0)
        {
            sb.appendTrustedString("font-weight: 800;");
        }
        else
        {
            sb.append(SafeStylesUtils.forMarginLeft(level * 20, Unit.PX)); //NOSONAR
        }
        return sb.toSafeStyles();
    };

    @Inject
    private CommonMessages cmessages;
    @Inject
    private ObjectListColumnBuilder tableBuilder;
    @Inject
    private LinkToPlaceColumnFactory<StructuredObjectsViewItemClient> linkToPlaceColumnFactory;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private FontIconFactory<StructuredObjectsViewItemClient> iconFactory;
    @Inject
    private WithArrowsCellTableResources cellTableResources;
    @Inject
    private StructuredObjectsViewsMessages structuredObjectsViewsMessages;
    @Inject
    private ListEditorDnDControllerFactory dndControllerFactory;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private Provider<ListFilterConditionTextFactory> textFactoryProvider;

    private final Map<String, ListEditorDnDController> dndControllersMap = new HashMap<>();
    private final Map<String, StructuredObjectsViewItemsListDnDController> dndGroupsMap = new HashMap<>();
    private final ToolBarDisplayMediator<DtObject> toolBar;
    private DtObject structuredObjectsView;
    private StructuredObjectsViewItemsCommandParam param;

    private final OnStartCallback<DtObject> refreshCallback = new SafeOnStartBasicCallback<DtObject>(
            getDisplay())
    {
        @Override
        protected void handleSuccess(@Nullable DtObject value)
        {
            if (value != null)
            {
                structuredObjectsView = value;
                param.setStructuredObjectsView(structuredObjectsView);
            }
            refreshDisplay();
        }
    };

    @Inject
    public StructuredObjectsViewItemsPresenter(TableDisplay<StructuredObjectsViewItemClient> display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    public void init(DtObject structuredObjectsView)
    {
        this.structuredObjectsView = structuredObjectsView;
        param = new StructuredObjectsViewItemsCommandParam(structuredObjectsView, null, refreshCallback);
    }

    @Override
    public void refreshDisplay()
    {
        toolBar.refresh(structuredObjectsView);
        getDisplay().refresh();
    }

    protected void initTable()
    {
        DataTable<StructuredObjectsViewItemClient> table = getDisplay().getTable();
        table.setVisibleRange(0, DefaultCellTable.DEFAULT_PAGESIZE);

        List<PermissionType> structureObjectViewPermissions =
                structuredObjectsView.getProperty(SettingsSet.ADMIN_PERMISSIONS);

        addActionColumn(c -> hasEditPermission(structureObjectViewPermissions),
                StructuredObjectsViewItemsCommandCode.MOVE_ITEM_UP);
        addActionColumn(c -> hasEditPermission(structureObjectViewPermissions),
                StructuredObjectsViewItemsCommandCode.MOVE_ITEM_DOWN);

        LinkToPlaceWithIndentColumn<StructuredObjectsViewItemClient> titleColumn =
                (LinkToPlaceWithIndentColumn<StructuredObjectsViewItemClient>)linkToPlaceColumnFactory
                        .getColumn(item -> NOWHERE);
        titleColumn.setStyleFunction(TITLE_STYLES::apply);
        titleColumn.setCellStyleNames(cellTableResources.cellTableStyle().titleColumn());
        titleColumn.setIdProviderFunction(input -> input != null ? input.getCode()
                : StringUtilities.EMPTY);
        table.addColumn(titleColumn, cmessages.title());

        Column<StructuredObjectsViewItemClient, String> codeColumn = new Column<StructuredObjectsViewItemClient,
                String>(
                new LineTextCell())
        {
            @Override
            public String getValue(StructuredObjectsViewItemClient item)
            {
                return item.getCode();
            }
        };
        table.addColumn(codeColumn, cmessages.code());

        Column<StructuredObjectsViewItemClient, String> classFqnColumn = new Column<StructuredObjectsViewItemClient,
                String>(new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
        {
            @Override
            public String getValue(StructuredObjectsViewItemClient item)
            {
                return item.getClassesHtml();
            }
        };
        table.addColumn(classFqnColumn, cmessages.objectClass());

        Column<StructuredObjectsViewItemClient, String> relAttrFqnColumn = new Column<StructuredObjectsViewItemClient
                , String>(
                new LineTextCell())
        {
            @Override
            public String getValue(StructuredObjectsViewItemClient item)
            {
                return item.getRelAttrTitle();
            }
        };
        table.addColumn(relAttrFqnColumn, cmessages.attributeLink());

        Column<StructuredObjectsViewItemClient, String> attrGroupColumn = new Column<StructuredObjectsViewItemClient,
                String>(
                new LineTextCell())
        {
            @Override
            public String getValue(StructuredObjectsViewItemClient item)
            {
                return item.getAttrGroupTitle();
            }
        };
        table.addColumn(attrGroupColumn, cmessages.attributeGroup());

        FontIconColumn<StructuredObjectsViewItemClient> showNestedColumn =
                new FontIconColumn<StructuredObjectsViewItemClient>()
                {
                    @Override
                    public FontIconDisplay<StructuredObjectsViewItemClient> getValue(
                            StructuredObjectsViewItemClient item)
                    {
                        return Boolean.TRUE.equals(item.isShowNested()) ? iconFactory.create(IconCodes.YES)
                                : iconFactory.create(IconCodes.EMPTY);
                    }
                };
        table.addColumn(showNestedColumn, structuredObjectsViewsMessages.showNested());

        Column<StructuredObjectsViewItemClient, SafeHtml> objectFilterColumn =
                new Column<StructuredObjectsViewItemClient, SafeHtml>(new SafeHtmlCell())
                {
                    @Override
                    public SafeHtml getValue(StructuredObjectsViewItemClient object)
                    {
                        ListFilterConditionTextFactory textFactory = textFactoryProvider.get();
                        textFactory.setAttributes(object.getAvailableAttributes());
                        textFactory.setEmptyIfNoFiltration(true);
                        textFactory.setDisabledByTags(object.getDisabledAttributes());
                        return textFactory.create(object.getObjectFilter());
                    }
                };
        table.addColumn(objectFilterColumn, structuredObjectsViewsMessages.objectFilter());
        FontIconColumn<StructuredObjectsViewItemClient> showNameColumn =
                new FontIconColumn<StructuredObjectsViewItemClient>()
                {
                    @Override
                    public FontIconDisplay<StructuredObjectsViewItemClient> getValue(
                            StructuredObjectsViewItemClient item)
                    {
                        return Boolean.TRUE.equals(item.isShowName()) ? iconFactory.create(IconCodes.YES)
                                : iconFactory.create(IconCodes.EMPTY);
                    }
                };
        table.addColumn(showNameColumn, structuredObjectsViewsMessages.showName());

        addActionColumn(c -> hasEditPermission(structureObjectViewPermissions),
                StructuredObjectsViewItemsCommandCode.EDIT);
        addActionColumn(c -> hasDeletePermission(structureObjectViewPermissions),
                StructuredObjectsViewItemsCommandCode.DELETE);

        table.setRowStyles(new ItemsRowStyles());
        table.asWidget().ensureDebugId("structuredObjectsViewItemsTable");
    }

    @Override
    protected void onBind()
    {
        ButtonPresenter<DtObject> btnAdd = (ButtonPresenter<DtObject>)buttonFactory.create(ButtonCode.ADD,
                cmessages.addItem(), StructuredObjectsViewItemsCommandCode.ADD, param);
        btnAdd.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        toolBar.add(btnAdd);

        getDisplay().setCaption(structuredObjectsViewsMessages.structuredObjectsViewItems());

        initTable();
        ItemsDataProvider dataProvider = new ItemsDataProvider();
        dataProvider.addDataDisplay(getDisplay().getTable());

        toolBar.bind();
        refreshDisplay();
    }

    private void addActionColumn(@Nullable Predicate<StructuredObjectsViewItemClient> visibilityCondition,
            String... commands)
    {
        tableBuilder.addActionColumn(display, param, null, 0, visibilityCondition, commands);
    }

    private void updateDnDControllers()
    {
        NodeList<Element> rows = getDisplay().getTable().asWidget().getElement().getElementsByTagName("tr");
        for (StructuredObjectsViewItemsListDnDController group : dndGroupsMap.values())
        {
            group.getGroupIndexList().clear();
            group.updateElements(rows);
        }

        Map<String, Integer> itemIndexes = new HashMap<>();
        int curIndex = 0;
        for (StructuredObjectsViewItemClient item : getDisplay().getTable().getVisibleItems())
        {
            itemIndexes.put(item.getCode(), curIndex++);
        }

        updateDnDGroup(StringUtilities.EMPTY, structuredObjectsView.getProperty(StructuredObjectsView.ITEMS),
                itemIndexes, rows);
        Set<String> parentCodes = Sets.newHashSet(StringUtilities.EMPTY);
        for (StructuredObjectsViewItemClient item : getDisplay().getTable().getVisibleItems())
        {
            if (!item.getChildren().isEmpty())
            {
                String code = item.getCode();
                parentCodes.add(code);
                List<StructuredObjectsViewItemClient> children = item.getChildren();
                updateDnDGroup(code, children, itemIndexes, rows);
            }
        }

        for (String code : new HashSet<>(dndGroupsMap.keySet()))
        {
            if (!parentCodes.contains(code))
            {
                dndGroupsMap.remove(code);
                dndControllersMap.remove(code).destroy();
            }
        }
    }

    private void updateDnDGroup(String code, List<StructuredObjectsViewItemClient> children,
            Map<String, Integer> itemIndexes,
            NodeList<Element> allElements)
    {
        StructuredObjectsViewItemsListDnDController group = dndGroupsMap.get(code); //NOSONAR
        if (null == group)
        {
            group = new StructuredObjectsViewItemsListDnDController();
            group.updateElements(allElements);
            dndGroupsMap.put(code, group);
            dndControllersMap.put(code, dndControllerFactory.create(group));
        }
        for (StructuredObjectsViewItemClient child : children)
        {
            group.getGroupIndexList().add(itemIndexes.get(child.getCode()));
        }
        dndControllersMap.get(code).update();
    }
}
