package ru.naumen.metainfoadmin.client.eventcleaner.rule.form;

import com.google.inject.Singleton;

import ru.naumen.core.client.validation.MetainfoCodeValidatorBase;
import ru.naumen.metainfo.shared.Constants;

/**
 * Проверяет код правила очистки лога событий.
 * <AUTHOR>
 * @since 03.07.23
 */
@Singleton
public class EventStorageRuleCodeValidator extends MetainfoCodeValidatorBase
{
    public EventStorageRuleCodeValidator()
    {
        super(Constants.MAX_METAINFO_KEY_LENGTH, Constants.CODE_SPECIAL_CHARS,
                Constants.CODE_SPECIAL_CHARS, true);
    }
}
