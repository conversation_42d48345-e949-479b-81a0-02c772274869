package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import java.util.ArrayList;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.EscalationSchemeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 24.07.2012
 *
 */
public class AddEscalationSchemeForm extends EscalationSchemeForm<ObjectFormAdd>
{
    @Inject
    public AddEscalationSchemeForm(DefaultPropertyFormDisplayImpl display, EventBus eventBus,
            @Assisted EventBus localEventBus)
    {
        super(display, eventBus, localEventBus);
    }

    @Override
    protected boolean isNewEscalationScheme()
    {
        return true;
    }

    @Override
    protected void setProperties()
    {
        propertyValues = new MapProperties();
        contextProps = new MapProperties();
        propertyValues.setProperty(EscalationSchemeFormPropertyCode.TARGET_OBJECTS, new ArrayList<>());
        propertyValues.setProperty(EscalationSchemeFormPropertyCode.STATE, EscalationScheme.StateCode.OFF);
        propertyValues.setProperty(EscalationSchemeFormPropertyCode.SETTINGS_SET, null);
    }
}
