package ru.naumen.metainfoadmin.client.customforms;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.USER_INTERFACE;

import com.google.common.collect.ImmutableList;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.name.Named;

import jakarta.inject.Inject;
import ru.naumen.core.client.TabLayoutDisplay;
import ru.naumen.core.client.widgets.select.popup.PopupListSelectResources;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.client.events.MetainfoUpdatedEvent;
import ru.naumen.metainfo.client.ui.CustomFormMessages;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ui.CustomFormList;
import ru.naumen.metainfoadmin.client.AdminTabContainerHasContextPresenter;
import ru.naumen.metainfoadmin.shared.Constants.CustomFormCommandCode;
import ru.naumen.objectlist.client.ListPresenter;

/**
 * Презентер таба со списком настроек специальных форм
 *
 * <AUTHOR>
 * @since 21.04.2016
 */
public class CustomFormsTabPresenter extends AdminTabContainerHasContextPresenter
{
    private final CustomFormsAdvlistFactory advlistFactory;
    private final CustomFormMessages customFormMessages;
    private final ImmutableList<AttributeFqn> attrs;

    protected String addCustomFormCommandCode = CustomFormCommandCode.ADD_CUSTOM_FORM;
    private ListPresenter<CustomFormList> listPresenter;

    @Inject
    public CustomFormsTabPresenter(TabLayoutDisplay display,
            EventBus eventBus,
            CustomFormsAdvlistFactory advlistFactory,
            CustomFormMessages customFormMessages,
            @Named(CustomFormGinModule.OTHER_FORM_ATTRS) ImmutableList<AttributeFqn> attrs)
    {
        super(display, eventBus);
        this.advlistFactory = advlistFactory;
        this.customFormMessages = customFormMessages;
        this.attrs = attrs;
    }

    @Override
    public void refreshDisplay()
    {
        listPresenter.refreshDisplay();
    }

    @Inject
    protected void init(PopupListSelectResources resources)
    {
        resources.styles().ensureInjected();
    }

    @Override
    protected void onBind()
    {
        listPresenter = advlistFactory.create(addCustomFormCommandCode, attrs, getContext());
        addTitledContent(listPresenter, customFormMessages.formTitle(getContext().getMetainfo().getTitle()),
                "customForms");
        refreshDisplay();
        // При любом изменении метаинфы сделать рефреш
        registerHandler(eventBus.addHandler(MetainfoUpdatedEvent.getType(), e -> refreshDisplay()));
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return USER_INTERFACE;
    }
}
