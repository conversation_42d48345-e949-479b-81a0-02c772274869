/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import com.google.inject.Inject;
import com.google.inject.Provider;

import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.factory.ContentFactoryBase;
import ru.naumen.core.client.content.toolbar.ToolPanelContentPresenter;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.Window;

/**
 * <AUTHOR>
 * @since 19 мая 2015 г.
 *
 */
public class ToolPanelContentFactory<R extends Context> extends ContentFactoryBase<ToolPanel, R>
{
    @Inject
    private Provider<EditableToolPanelContentPresenter<R>> editableToolPanelProvider;
    @Inject
    protected Provider<ToolPanelContentPresenter<R>> nonEditableToolPanelProvider;

    @Override
    protected ContentPresenter<ToolPanel, R> create(Content content)
    {
        Content parentContent = content.getParent();
        //Редактирование тулбара через отдельную кнопку сделано 
        //только для тулбара на карточке и тулбаров вкладок
        if (parentContent instanceof Window || parentContent instanceof Tab)
        {
            return editableToolPanelProvider.get();
        }
        else
        {
            return nonEditableToolPanelProvider.get();
        }
    }
}