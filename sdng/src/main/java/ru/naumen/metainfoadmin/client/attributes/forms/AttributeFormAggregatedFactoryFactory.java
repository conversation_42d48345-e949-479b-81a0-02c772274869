/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.forms;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.metainfo.shared.elements.Attribute;

public interface AttributeFormAggregatedFactoryFactory<F extends ObjectForm>
{
    PropertyControllerFactory<Attribute, F> create(PropertyControllerFactory<Attribute, F> factorySelector);
}