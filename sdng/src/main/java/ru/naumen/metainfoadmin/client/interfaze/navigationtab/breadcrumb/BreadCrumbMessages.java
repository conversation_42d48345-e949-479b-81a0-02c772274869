package ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb;

import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;

/**
 * Локализация крошек
 *
 * <AUTHOR>
 * @since 07 июля 2014 г.
 */
@DefaultLocale("ru")
public interface BreadCrumbMessages extends NavigationSettingsMessages
{
    @Description("Текст информирующий о порядке атрибутов в хлебной крошке")
    String attentionText();

    @Description("Назад")
    String back();

    @Description("Заголовок формы")
    String captionOfBreadCrumbForm();

    @Description("Вопрос при удалении хлебной крошки")
    String deleteQuestion(String title);

    @Description("Названия атрибута 'Объекты' хлебной крошки")
    String objects();

    @Description("Название колонки 'Объекты, на которые ссылаются атрибуты связи' хлебной крошки")
    String permittedTypes();

    @Description("Название колонки 'Примерный вид хлебных крошек' хлебной крошки")
    String presentationOfBreadCrumb();

    @Description("Названия атрибута 'Атрибуты связи' хлебной крошки")
    String relationAttributes();

    @Description("Заголовок карточки хлебной крошки")
    String titleOfBreadCrumbCard(String title);
}