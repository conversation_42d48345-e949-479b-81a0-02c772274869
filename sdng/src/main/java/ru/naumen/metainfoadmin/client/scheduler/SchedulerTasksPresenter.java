package ru.naumen.metainfoadmin.client.scheduler;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.SCHEDULER;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfoadmin.client.AdminSingleTabAdvlistPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;

/**
 * Презентер вкладки задач планировщика
 * <AUTHOR>
 * @since 14.11.2017
 */
public class SchedulerTasksPresenter extends AdminSingleTabAdvlistPresenterBase<SchedulerPlace, SchedulerTask>
{
    @Inject
    public SchedulerTasksPresenter(AdminTabDisplay display, EventBus eventBus,
            SchedulerTasksListPresenter advlistPresenter)
    {
        super(display, eventBus, advlistPresenter);
    }

    @Override
    protected String getTitle()
    {
        return messages.schedulerTasks();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return SCHEDULER;
    }
}
