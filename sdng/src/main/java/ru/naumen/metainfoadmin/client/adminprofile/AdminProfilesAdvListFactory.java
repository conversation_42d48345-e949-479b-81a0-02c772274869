package ru.naumen.metainfoadmin.client.adminprofile;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.ADMINISTRATION_PROFILES;
import static ru.naumen.core.shared.permission.PermissionType.CREATE;
import static ru.naumen.core.shared.permission.PermissionType.DELETE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.ArrayList;

import com.google.common.collect.Lists;

import jakarta.inject.Inject;
import ru.naumen.admin.client.permission.AdminPermissionCheckServiceSync;
import ru.naumen.admin.client.permission.AdminPermissionHelper;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.Constants.AdminProfileMetainfo;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.adminprofile.command.AdminProfileCommandCode;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.objectlist.client.extended.advlist.FeatureCodes;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;
import ru.naumen.objectlist.shared.AdvListMassOperationLightActionDesc;
import ru.naumen.objectlist.shared.AdvListMassOperationLightContext;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Фабрика списка для списка профилей администрирования в админке
 * <AUTHOR>
 * @since 17.01.2024
 */
public class AdminProfilesAdvListFactory extends AdminCustomAdvlistFactoryBase
{
    private final AdminProfilesMessages messages;
    private final AdminPermissionHelper adminPermissionHelper;
    private final AdminPermissionCheckServiceSync adminPermissionCheckServiceSync;

    @Inject
    public AdminProfilesAdvListFactory(AdminProfilesMessages messages,
            AdminPermissionHelper adminPermissionHelper,
            AdminPermissionCheckServiceSync adminPermissionCheckServiceSync)
    {
        this.messages = messages;
        this.adminPermissionHelper = adminPermissionHelper;
        this.adminPermissionCheckServiceSync = adminPermissionCheckServiceSync;
    }

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        ArrayList<ExtendedListActionCellContext> actionColumns = new ArrayList<>();
        adminPermissionHelper.runIfPermissionByAccessMarkerGranted(ADMINISTRATION_PROFILES, EDIT,
                () -> actionColumns.add(
                        new ExtendedListActionCellContext(IconCodes.EDIT, AdminProfileCommandCode.EDIT)));
        adminPermissionHelper.runIfPermissionByAccessMarkerGranted(ADMINISTRATION_PROFILES, PermissionType.DELETE,
                () -> actionColumns.add(
                        new ExtendedListActionCellContext(IconCodes.DEL, AdminProfileCommandCode.DELETE)));
        return actionColumns;
    }

    @Override
    protected AdvListMassOperationLightContext createAdvListMassOperationLightContext()
    {
        if (!adminPermissionCheckServiceSync.hasPermission(ADMINISTRATION_PROFILES, DELETE))
        {
            return null;
        }
        return new AdvListMassOperationLightContext(
                new AdvListMassOperationLightActionDesc(AdminProfileCommandCode.DELETE_MASS, messages.deleteMass()));
    }

    @Override
    protected ToolPanel createToolPanel(CustomList content)
    {
        final ToolPanel panel = super.createToolPanel(content);
        adminPermissionHelper.runIfPermissionByAccessMarkerGranted(ADMINISTRATION_PROFILES, CREATE,
                () -> panel.addToolBar(
                        createAddButtonToolBar(AdminProfileCommandCode.ADD, messages.addAdminProfile())));
        return panel;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return AdminProfileMetainfo.FQN;
    }

    @Override
    protected ArrayList<String> createFeatures()
    {
        ArrayList<String> features = Lists.newArrayList(FeatureCodes.FILTER, FeatureCodes.SORT);
        adminPermissionHelper.runIfPermissionByAccessMarkerGranted(ADMINISTRATION_PROFILES, DELETE, () ->
        {
            features.add(FeatureCodes.MASS_OPERATION_LIGHT);
            features.add(FeatureCodes.SELECTION);
        });
        return features;
    }
}
