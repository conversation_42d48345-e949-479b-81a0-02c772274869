package ru.naumen.metainfoadmin.client.customforms.parameters;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPLEX_RELATION;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPLEX_RELATION_ATTR_GROUP;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.EDIT_PRS;

import com.google.common.collect.Lists;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * Реализация {@link AttributeFormPropertyDelegateVCH} для свойства "Редактируемый" на 
 * формах добавления и редактирования параметров настраиваемыз форм 
 *
 * <AUTHOR>
 * @since 13 мая 2016 г.
 */
public class ParameterFormEditableVCHDelegateImpl<F extends ParameterForm>
        implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        String attrEditPrs = context.getPropertyValues().getProperty(EDIT_PRS);
        if (attrEditPrs.equals(Presentations.QUICK_SELECTION_FIELD))
        {
            context.setProperty(COMPLEX_RELATION, Boolean.FALSE.toString());
        }
        context.getRefreshProcess()
                .startCustomProcess(Lists.newArrayList(EDIT_PRS, COMPLEX_RELATION, COMPLEX_RELATION_ATTR_GROUP));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}