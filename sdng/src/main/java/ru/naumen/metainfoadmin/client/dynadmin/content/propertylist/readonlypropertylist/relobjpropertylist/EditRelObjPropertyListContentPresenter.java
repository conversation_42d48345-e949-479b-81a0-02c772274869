package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.relobjpropertylist;

import java.util.ArrayList;
import java.util.List;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfoadmin.client.dynadmin.BasicUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.EditRelObjPropertyListContentPresenterBase;

/**
 * Презентер формы редактирования контента "Параметры связанного объекта"
 *
 * <AUTHOR>
 */
public class EditRelObjPropertyListContentPresenter extends EditRelObjPropertyListContentPresenterBase<RelObjPropertyList>
{
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> attribute;
    @Inject
    private TextBoxProperty relObjClass;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> attributeGroup;
    @Named(PropertiesGinModule.CHECK_BOX)
    @Inject
    private Property<Boolean> allowEdit;

    @Inject
    public EditRelObjPropertyListContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void refreshDisplay()
    {
        refreshDisplay("");
    }

    @Override
    protected String getRelationAttrCode()
    {
        return content.getAttrCode();
    }

    @Override
    protected void bindProfiles()
    {
        profiles = contentUtils.createProfilesProperty(context.getParentContext().getMetainfo().getFqn(), content,
                false);
        DebugIdBuilder.ensureDebugId(profiles, "profiles");
        getDisplay().add(profiles);
        if (sharedSettingsClientService.isModuleInstalled(Modules.PLANNED_VERSION))
        {
            versProfiles = contentUtils.createProfilesProperty(context.getParentContext().getMetainfo().getFqn(),
                    content, true);
            DebugIdBuilder.ensureDebugId(versProfiles, "versProfiles");
            getDisplay().add(versProfiles);
        }
    }

    @Override
    public void updateCurrentContent()
    {
        super.updateCurrentContent();
        content.setAllowEdit(allowEdit.getValue());
        content.setAttributeGroup(SelectListPropertyValueExtractor.getValue(attributeGroup));
        content.setAttrCode(SelectListPropertyValueExtractor.getValue(attribute));
        updateEditTool();
    }

    @Override
    protected void restoreContent(RelObjPropertyList oldContent)
    {
        super.restoreContent(oldContent);
        content.setAllowEdit(oldContent.isAllowEdit());
        content.setAttributeGroup(oldContent.getAttributeGroup());
        content.setAttrCode(oldContent.getAttrCode());
        updateEditTool();
    }

    private void updateEditTool()
    {
        Tool editTool = content.getToolPanel().getTool(ru.naumen.metainfo.shared.ui.Constants.EDIT_PROPERTIES);
        if (null != editTool)
        {
            editTool.setVisible(allowEdit.getValue());
        }
    }

    @Override
    protected void bindPropertiesInner()
    {
        bindAttribute();
        bindRelObjectClass();
        bindAttrGroup();
        bindShowAttrDescription();
        bindAllowEditRelObject();

        List<Attribute> attributes = new ArrayList<>();
        for (Attribute attr : getUiContext().getMetainfo().getAttributes())
        {
            if (Constants.ObjectAttributeType.CODE.equals(attr.getType().getCode()) &&
                !attr.getType().isAttributeOfRelatedObject())
            {
                attributes.add(attr);
            }
        }
        metainfoUtils.sort(attributes);
        for (Attribute attr : attributes)
        {
            attribute.<SingleSelectCellList<?>> getValueWidget().addItem(attr.getTitle(), attr.getCode());
        }
        attribute.trySetObjValue(getRelationAttrCode());
        registerHandler(attribute.addValueChangeHandler(event -> refreshDisplay()));

        refreshDisplay(content.getAttributeGroup());
    }

    private void bindAttribute()
    {
        attribute.setCaption(commonMessages.attribute());
        attribute.setValidationMarker(true);
        DebugIdBuilder.ensureDebugId(attribute, "attribute");
        getDisplay().add(attribute);
    }

    private void bindRelObjectClass()
    {
        relObjClass.setCaption(messages.relObjectClass());
        relObjClass.setDisable();
        DebugIdBuilder.ensureDebugId(relObjClass, "relObjCalss");
        getDisplay().add(relObjClass);
    }

    private void bindAttrGroup()
    {
        attributeGroup.setCaption(commonMessages.attributeGroup());
        attributeGroup.setValidationMarker(true);
        DebugIdBuilder.ensureDebugId(attributeGroup, "attributeGroup");
        getDisplay().add(attributeGroup);
    }

    private void bindAllowEditRelObject()
    {
        allowEdit.setCaption(commonMessages.allowEditRelObj());
        allowEdit.setValue(content.isAllowEdit());
        if (!UI.WINDOW_KEY.equals(context.getCode()))
        {
            allowEdit.setEnabled(false);
        }
        DebugIdBuilder.ensureDebugId(allowEdit, "allowEditRelObj");
        getDisplay().add(allowEdit);
    }

    protected void refreshDisplay(final String defaultGroupCode)
    {
        String attrCode = SelectListPropertyValueExtractor.getValue(attribute);
        ObjectAttributeType type = getAttrType(attrCode);

        metainfoService.getMetaClass(type.getRelatedMetaClass(), new BasicCallback<MetaClass>(getDisplay())
        {
            @Override
            protected void handleSuccess(MetaClass selectedMetaClass)
            {
                attributeGroup.<SingleSelectCellList<?>> getValueWidget().clear();
                relObjClass.setValue(selectedMetaClass.getTitle());
                ArrayList<AttributeGroup> groups = Lists.newArrayList(selectedMetaClass.getAttributeGroups());
                metainfoUtils.sort(groups);
                for (AttributeGroup grp : groups)
                {
                    attributeGroup.<SingleSelectCellList<?>> getValueWidget().addItem(grp.getTitle(), grp.getCode());
                }
                attributeGroup.trySetObjValue(defaultGroupCode);
            }
        });
    }

    @Override
    protected boolean isContentEquals(RelObjPropertyList oldContent)
    {
        return super.isContentEquals(oldContent)
               && oldContent.getAttrCode().equals(content.getAttrCode())
               && oldContent.getAttributeGroup().equals(content.getAttributeGroup())
               && oldContent.isShowAttrDescription() == content.isShowAttrDescription()
               && oldContent.isAllowEdit() == content.isAllowEdit();
    }

    @Override
    protected void updateAfterSave(RelObjPropertyList savedContent)
    {
        super.updateAfterSave(savedContent);

        ObjectAttributeType type = getAttrType(getRelationAttrCode());
        metainfoService.getMetaClass(type.getRelatedMetaClass(), new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass value)
            {
                content.setEnabled(savedContent.isEnabled());
                BasicUIContext newContext = (BasicUIContext)context;
                newContext.setMetainfo(value);
                context.getEventBus().fireEvent(new RefreshContentEvent(content));
                eventBus.fireEvent(new RefreshContentEvent(content.getToolPanel()));
                unbind();
            }
        });
    }

    private ObjectAttributeType getAttrType(String code)
    {
        Attribute objectAttribute = getUiContext().getMetainfo().getAttribute(code);
        return objectAttribute.getType().cast();
    }
}
