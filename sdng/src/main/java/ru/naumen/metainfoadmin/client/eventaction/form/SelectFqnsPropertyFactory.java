package ru.naumen.metainfoadmin.client.eventaction.form;

import java.util.Collection;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.eventaction.EventAction;

/**
 * Фабрика свойства выбора типов объектов для действий по событию и эскалаций
 *
 * <AUTHOR>
 * @since 02 июля 2015 г.
 */
public interface SelectFqnsPropertyFactory
{
    Property<Collection<DtObject>> create(@Nullable EventAction eventAction);
}
