package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.QuickAccessTileFormPresenter;

/**
 * Команда припинивания элемента левого меню
 * <AUTHOR>
 * @since 14.07.2020
 */
public class PinLeftMenuItemCommand extends BaseCommandImpl<LeftMenuItemSettingsDTO, DtoContainer<NavigationSettings>>
{
    public static final String ID = "pinLeftMenuItem";

    private final Provider<QuickAccessTileFormPresenter<ObjectFormAdd>> presenterProvider;

    @Inject
    public PinLeftMenuItemCommand(@Assisted NavigationSettingsLMCommandParam param,
            Provider<QuickAccessTileFormPresenter<ObjectFormAdd>> presenterProvider)
    {
        super(param);
        this.presenterProvider = presenterProvider;
    }

    @Override
    public void execute(CommandParam<LeftMenuItemSettingsDTO, DtoContainer<NavigationSettings>> cparam)
    {
        NavigationSettingsLMCommandParam p = (NavigationSettingsLMCommandParam)prepareParam(cparam);

        QuickAccessTileFormPresenter<ObjectFormAdd> presenter = presenterProvider.get();

        presenter.init(p.getSettings().get(), p.getValue().getCode(), null, p.getCallback());
        presenter.bind();
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof LeftMenuItemSettingsDTO))
        {
            return false;
        }
        LeftMenuItemSettingsDTO item = (LeftMenuItemSettingsDTO)input;
        NavigationSettingsLMCommandParam p = (NavigationSettingsLMCommandParam)prepareParam(param);
        return !UnpinLeftMenuItemCommand.getTileForItem(p, item).isPresent();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.PIN;
    }
}