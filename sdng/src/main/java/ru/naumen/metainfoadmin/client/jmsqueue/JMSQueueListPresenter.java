package ru.naumen.metainfoadmin.client.jmsqueue;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.QUEUES;

import java.util.Set;
import java.util.function.Consumer;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Provider;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.client.advlists.AdminAdvListPresenterBase;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.DisplayHolder;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.client.widgets.AbstractMessageWidget;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.jmsqueue.comands.JMSQueueCommandCode;
import ru.naumen.metainfoadmin.client.jmsqueue.forms.AddJMSQueueFormPresenter;
import ru.naumen.metainfoadmin.client.jmsqueue.service.JMSQueueServiceAsync;
import ru.naumen.objectlist.client.AddMetainfoObjectEvent;
import ru.naumen.objectlist.client.AddMetainfoObjectHandler;

/**
 * Презентер таба "Очереди"
 * <AUTHOR>
 * @since 15.02.2021
 */
public class JMSQueueListPresenter extends AdminAdvListPresenterBase<DtObject>
{
    private class AddJMSQueueFormHandler implements AddMetainfoObjectHandler
    {
        @Override
        public void onAddMetainfoObject(AddMetainfoObjectEvent event)
        {
            getCountJMSUserQueues(response ->
            {
                if (response.get() >= sharedSettingsClientService.getMaxUserJMXQueues())
                {
                    addJMSQueueCallback.onFailure(new Throwable(messages.errorAddingNewJMSQueue()));
                }
                else
                {
                    addJMSQueueCallback.onSuccess(null);
                }
            });
        }
    }

    private final SharedSettingsClientService sharedSettingsClientService;
    private final Provider<AddJMSQueueFormPresenter> formProvider;
    private final JMSQueueServiceAsync jmsQueueServiceAsync;
    private final JMSQueueMessages messages;

    private final AsyncCallback<Void> addJMSQueueCallback = new OnStartBasicCallback<Void>()
    {
        @Override
        protected void handleSuccess(Void value)
        {
            AddJMSQueueFormPresenter presenter = formProvider.get();
            presenter.init(null, refreshCallback);
            presenter.bind();
        }

        @Override
        protected void handleFailure(String msg, @Nullable String details)
        {
            refreshDisplay();
            super.handleFailure(msg, details);
        }
    };

    @Inject
    public JMSQueueListPresenter(DisplayHolder display,
            EventBus eventBus,
            CommandFactory commandFactory,
            JMSQueueAdvlistFactory advlistFactory,
            SharedSettingsClientService sharedSettingsClientService,
            Provider<AddJMSQueueFormPresenter> formProvider,
            JMSQueueServiceAsync jmsQueueServiceAsync,
            JMSQueueMessages messages)
    {
        super(display, eventBus, commandFactory, advlistFactory);
        this.sharedSettingsClientService = sharedSettingsClientService;
        this.formProvider = formProvider;
        this.jmsQueueServiceAsync = jmsQueueServiceAsync;
        this.messages = messages;
    }

    @Override
    protected void afterOnBind()
    {
        super.afterOnBind();
        getCountJMSUserQueues(response ->
        {
            if (response.get() >= sharedSettingsClientService.getMaxUserJMXQueues())
            {
                AbstractMessageWidget attentionWidget = listPresenter.getListComponents().getAttentionWidget();
                attentionWidget.setText(messages.errorAddingNewJMSQueue());
                attentionWidget.setVisible(true);
            }
        });
    }

    /**
     * Запрос количества пользовательских очередей
     * @param consumer действия, которые нужно выполнить над результатом запроса
     */
    private void getCountJMSUserQueues(Consumer<SimpleResult<Integer>> consumer)
    {
        // в случае, если количество пользовательских очередей превысит лимит - не даем больше создавать
        jmsQueueServiceAsync.getCountJMSUserQueues(new BasicCallback<SimpleResult<Integer>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<Integer> response)
            {
                consumer.accept(response);
            }
        });
    }

    @Override
    protected Set<String> getActionCommands()
    {
        return JMSQueueCommandCode.COMMANDS_IN_LIST;
    }

    @Override
    protected AddMetainfoObjectHandler getAddMetainfoObjectHandler()
    {
        return new AddJMSQueueFormHandler();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return QUEUES;
    }
}