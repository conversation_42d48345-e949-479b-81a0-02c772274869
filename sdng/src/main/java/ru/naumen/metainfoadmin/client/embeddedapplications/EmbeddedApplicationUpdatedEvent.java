package ru.naumen.metainfoadmin.client.embeddedapplications;

import com.google.gwt.event.shared.GwtEvent;

import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.script.ScriptDto;

/**
 * <AUTHOR>
 * @since 16.08.2016
 *
 */
public class EmbeddedApplicationUpdatedEvent extends GwtEvent<EmbeddedApplicationUpdatedHandler>
{
    private static final Type<EmbeddedApplicationUpdatedHandler> TYPE = new Type<EmbeddedApplicationUpdatedHandler>();
    private EmbeddedApplicationAdminSettingsDto application;
    private ScriptDto scriptDto;

    public EmbeddedApplicationUpdatedEvent(EmbeddedApplicationAdminSettingsDto application)
    {
        this.application = application;
    }

    public EmbeddedApplicationUpdatedEvent(EmbeddedApplicationAdminSettingsDto application, ScriptDto scriptDto)
    {
        this.application = application;
        this.scriptDto = scriptDto;
    }

    public EmbeddedApplicationAdminSettingsDto getEmbeddedApplication()
    {
        return application;
    }

    public ScriptDto getScriptDto()
    {
        return scriptDto;
    }

    public void setScriptDto(ScriptDto scriptDto)
    {
        this.scriptDto = scriptDto;
    }

    public static Type<EmbeddedApplicationUpdatedHandler> getType()
    {
        return TYPE;
    }

    @Override
    public Type<EmbeddedApplicationUpdatedHandler> getAssociatedType()
    {
        return TYPE;
    }

    @Override
    protected void dispatch(EmbeddedApplicationUpdatedHandler handler)
    {
        handler.onEmbeddedApplicationUpdated(this);
    }
}
