package ru.naumen.metainfoadmin.client.eventaction.form.creator.event;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.function.Predicate;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.utils.TimeIntervalConstants;
import ru.naumen.core.client.validation.HasValidation;
import ru.naumen.core.client.validation.NotEmptyObjectValidator;
import ru.naumen.core.client.validation.ValidateEvent;
import ru.naumen.core.client.validation.ValidationMessages;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.ParseExceptionsUtils;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.DateUtils;
import ru.naumen.core.shared.utils.DateUtils.TimeInterval;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.eventaction.EventActionConstants;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.eventaction.Event;
import ru.naumen.metainfo.shared.eventaction.PlannedEventCategory;
import ru.naumen.metainfo.shared.eventaction.PlannedEventRule;

import com.google.common.collect.Lists;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.HorizontalPanel;

/**
 *
 * <AUTHOR>
 * @since 03.02.2012
 *
 */
public class PlannedEventFormPropertiesCreator extends AbstractEventFormPropertiesCreator<PlannedEventRule>
{

    @SuppressWarnings({ "unchecked", "rawtypes" })
    static class CombinedPropertiesProperty extends PropertyBase<List<Object>, MultiPropertyWidget>
    {
        public CombinedPropertiesProperty(List<Property> properties)
        {
            super(new MultiPropertyWidget(properties));
        }
    }

    @SuppressWarnings("rawtypes")
    static class MultiPropertyWidget extends Composite implements HasValueOrThrow<List<Object>>, HasEnabled
    {
        private final HorizontalPanel panel;

        protected MultiPropertyWidget(List<Property> properties)
        {
            panel = new HorizontalPanel();
            panel.addStyleName(WidgetResources.INSTANCE.form().multiProperty());
            for (Property property : properties)
            {
                panel.add(property.getValueWidget().asWidget());
            }
            initWidget(panel);
        }

        @Override
        public HandlerRegistration addValueChangeHandler(ValueChangeHandler<List<Object>> handler)
        {
            return addHandler(handler, ValueChangeEvent.getType());
        }

        @Override
        public List<Object> getValue()
        {
            return ParseExceptionsUtils.getValueSafe(this);
        }

        @Override
        public List<Object> getValueOrThrow() throws ParseException
        {
            return null;
        }

        @Override
        public boolean isEnabled()
        {
            return true;
        }

        @Override
        public void setEnabled(boolean enabled)
        {

        }

        @Override
        public void setValue(List<Object> value)
        {

        }

        @Override
        public void setValue(List<Object> value, boolean fireEvents)
        {

        }

    }

    class TimeValueValidator implements Validator<Long>
    {
        @Override
        public boolean validate(HasValueOrThrow<Long> hasValue)
        {
            Long value = hasValue.getValue();
            if ((value == null || value < 0) && hasValue instanceof HasValidation)
            {
                ((HasValidation)hasValue).addValidationMessage(validationMessages.negativeValueValidationError());
                return false;
            }
            else
            {
                return true;
            }
        }

        @Override
        public void validateAsync(HasValueOrThrow<Long> hasValue, ValidateEvent event)
        {
            validate(hasValue);
        }
    }

    private static class DateTimeAttributePredicate implements Predicate<Attribute>
    {
        private final HashSet<String> EXCLUDE_CODES = new HashSet<String>(
                Arrays.asList(AbstractBO.CREATION_DATE, AbstractBO.REMOVAL_DATE,
                        ru.naumen.core.shared.Constants.LAST_MODIFIED_DATE));

        @Override
        public boolean test(Attribute attribute)
        {
            String attrTypeCode = attribute.getType().getCode();
            //@formatter:off
            return !EXCLUDE_CODES.contains(attribute.getCode()) &&
                    !attribute.getType().isAttributeOfRelatedObject() && !attribute.isComputable() &&
                   (ObjectUtils.equals(attrTypeCode, Constants.DateAttributeType.CODE)
                   || ObjectUtils.equals(attrTypeCode, Constants.DateTimeAttributeType.CODE) 
                   || ObjectUtils.equals(attrTypeCode, Constants.BackTimerAttributeType.CODE));
            //@formatter:on
        }
    }

    public static final int MAX_TIME_VALUE_LENGTH = 9;

    @Inject
    private NotEmptyObjectValidator<SelectItem> notEmptySelectItemValidator;
    @Inject
    EventActionMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    ValidationMessages validationMessages;
    @Inject
    @Named(PropertiesGinModule.INTEGER)
    Property<Long> timeValue;

    @Inject
    @Named(PropertiesGinModule.LIST_BOX_WITHOUT_SEARCH)
    SelectListProperty<String, SelectItem> measure;

    @Inject
    @Named(PropertiesGinModule.LIST_BOX_WITHOUT_SEARCH)
    SelectListProperty<String, SelectItem> category;

    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    SelectListProperty<String, SelectItem> attributes;

    @Inject
    MetainfoUtils metainfoUtils;
    @Inject
    AdminMetainfoServiceAsync metainfoService;
    @Inject
    EventActionConstants eventActionConstants;

    private List<ClassFqn> fqns;
    private PlannedEventRule plannedEvent;
    private Validator<Long> timeValueValidator;

    private MetaClass metaclass;

    @Inject
    DateUtils dateUtils;

    @Inject
    TimeIntervalConstants timeIntervalConstants;

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {
        addValidation();
    }

    @Override
    public void bindProperties()
    {
        addAttributeProperty();
        addCombinedProperty();

        setPropertiesValues();
        initPropertiesHandlers();

        ensureDebugIds();
    }

    @Override
    public PlannedEventRule getEvent()
    {
        if (!validation.validate())
        {
            return null;
        }
        plannedEvent.setAttribute(SelectListPropertyValueExtractor.getValue(attributes));
        PlannedEventCategory plannedCategory = PlannedEventCategory.valueOf(SelectListPropertyValueExtractor.getValue(
                category));
        plannedEvent.setCategory(plannedCategory);
        if (!plannedCategory.equals(PlannedEventCategory.IN_TIME))
        {
            plannedEvent.setOffsetTime(
                    dateUtils.getOffsetValue(timeValue.getValue(), TimeInterval.valueOf(SelectListPropertyValueExtractor
                            .getValue(measure))));
        }
        else
        {
            plannedEvent.setOffsetTime(0);
        }
        return plannedEvent;
    }

    @Override
    public void init(@Nullable Event event, List<ClassFqn> fqns)
    {
        if (!(event instanceof PlannedEventRule))
        {
            this.plannedEvent = new PlannedEventRule();
            this.plannedEvent.setCategory(PlannedEventCategory.IN_TIME);
        }
        else
        {
            this.plannedEvent = (PlannedEventRule)event;
        }
        this.fqns = fqns;
    }

    @Override
    public void refreshProperties(List<ClassFqn> fqns)
    {
        this.fqns = fqns;
        fillAttributes(fqns);
    }

    @Override
    public void setReadyState(ReadyState rs)
    {
    }

    protected void addAttributeProperty()
    {
        addWithMarker(cmessages.attribute(), attributes);
        fillAttributes(fqns);
        validation.validate(attributes, notEmptySelectItemValidator);
    }

    @SuppressWarnings("rawtypes")
    protected void addCombinedProperty()
    {
        ArrayList<Property> combinedProperties = new ArrayList<Property>();
        initDirection();
        initMeasure();

        combinedProperties.add(category);
        combinedProperties.add(timeValue);
        combinedProperties.add(measure);
        timeValue.setMaxLength(MAX_TIME_VALUE_LENGTH);

        CombinedPropertiesProperty combinedProperty = new CombinedPropertiesProperty(combinedProperties);
        addWithMarker(messages.eventActionTime(), combinedProperty);
    }

    protected void fillAttributes(List<ClassFqn> fqns)
    {
        if (!fqns.isEmpty())
        {
            //Все типы принадлежат к одному классу, обеспечивается проверкой в ru.naumen.metainfoadmin.client
            // .eventaction.form.EventActionFormPresenter.EventValidator.validate
            // (HasValueOrThrow<Collection<DtObject>>, HasValidationCallback)
            //Вызываем сервис поиска общего предка для списка Fqn отмеченных типов/классов и берем атрибуты
            // найденного класса
            metainfoService.getCommonMetaClass((ArrayList<ClassFqn>)fqns, new BasicCallback<MetaClass>()
            {
                @Override
                protected void handleSuccess(MetaClass inMetaclass)
                {
                    metaclass = inMetaclass;
                    List<Attribute> attrs = Lists.newArrayList(
                            metaclass.getAttributes()
                                    .stream()
                                    .filter(new DateTimeAttributePredicate())
                                    .iterator());
                    metainfoUtils.sort(attrs);

                    SingleSelectCellList<?> list = attributes.getValueWidget();
                    list.clear();
                    list.refreshPopupCellList();
                    if (attrs.isEmpty())
                    {
                        list.getPopup().clear();
                        list.setValue(null, false);
                    }
                    for (Attribute attribute : attrs)
                    {
                        list.addItem(attribute.getTitle(), attribute.getCode());
                    }
                    if (list.getItemCount() == 0)
                    {
                        attributes.trySetObjValue(null);
                    }
                    else
                    {
                        String attribute = plannedEvent.getAttribute();
                        if (attribute == null && !attrs.isEmpty())
                        {
                            attributes.trySetObjValue(attrs.iterator().next().getCode());
                        }
                        else
                        {
                            attributes.trySetObjValue(attribute);
                        }
                    }
                }
            });
        }
        else
        {
            SingleSelectCellList<?> list = attributes.getValueWidget();
            list.clear();
            list.refreshPopupCellList();
            attributes.trySetObjValue(null);
        }
    }

    protected void initPropertiesHandlers()
    {
        category.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                onCategoryChange();
            }
        });
    }

    protected void onCategoryChange()
    {
        PlannedEventCategory plannedCategory = PlannedEventCategory.valueOf(SelectListPropertyValueExtractor.getValue(
                category));
        if (plannedCategory.equals(PlannedEventCategory.IN_TIME))
        {
            timeValue.asWidget().addStyleName(WidgetResources.INSTANCE.additional().displayNone());
            measure.asWidget().addStyleName(WidgetResources.INSTANCE.additional().displayNone());
            validation.unvalidate(timeValue);
            timeValueValidator = null;
        }
        else
        {
            timeValue.asWidget().removeStyleName(WidgetResources.INSTANCE.additional().displayNone());
            measure.asWidget().removeStyleName(WidgetResources.INSTANCE.additional().displayNone());
            addValidation();
        }
    }

    private void addValidation()
    {
        if (timeValueValidator == null)
        {
            timeValueValidator = new TimeValueValidator();
            validation.validate(timeValue, new TimeValueValidator());
        }
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(timeValue, "timeValue");
        DebugIdBuilder.ensureDebugId(measure, "measure");
        DebugIdBuilder.ensureDebugId(category, "category");
        DebugIdBuilder.ensureDebugId(attributes, "attributes");
    }

    private void initDirection()
    {
        SingleSelectCellList<?> list = category.getValueWidget();
        for (PlannedEventCategory category : PlannedEventCategory.values())
        {
            list.addItem(eventActionConstants.plannedEventCategory().get(category.name()), category.name());
        }
    }

    private void initMeasure()
    {
        SingleSelectCellList<?> list = measure.getValueWidget();
        for (TimeInterval timeInterval : TimeInterval.values())
        {
            list.addItem(timeIntervalConstants.intervalTitles().get(timeInterval.name()), timeInterval.name());
        }
    }

    private void setPropertiesValues()
    {
        category.trySetObjValue(plannedEvent.getCategory().name());

        long offsetTime = plannedEvent.getOffsetTime();
        TimeInterval timeInterval = dateUtils.getTimeInterval(offsetTime);
        measure.trySetObjValue(timeInterval.name());
        timeValue.setValue(offsetTime / timeInterval.inMilliSeconds());

        onCategoryChange();
    }
}
