package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import static ru.naumen.metainfo.shared.Constants.LinkObjectType.OBJECT_LINKED_TO_CURRENT_USER;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.Relation;
import ru.naumen.metainfo.shared.filters.RelationFilters;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfoadmin.shared.dynadmin.content.objectlist.relobjectlist.PossibleLinkAttributeFilter;

/**
 * Делегат обновления свойства "Объект связи"
 *
 * <AUTHOR>
 * @since 06.11.2020
 */
public class LinkToContentLinkObjectAttrRefreshDelegateImpl implements PropertyDelegateRefresh<SelectItem,
        ListBoxWithEmptyOptProperty>
{
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean isLinkToContentType = LinkToContentMetaClassPropertiesProcessor.isLinkToContentElementType(context);

        String contentTypeStr = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);

        String linkObject = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT);

        if (!isLinkToContentType || ObjectList.class.getSimpleName().equals(contentTypeStr)
            || !OBJECT_LINKED_TO_CURRENT_USER.equals(linkObject))
        {
            callback.onSuccess(false);
            return;
        }

        ClassFqn metaclass = Employee.FQN;
        String linkObjectCase = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT_CASE);

        if (linkObjectCase != null)
        {
            metaclass = ClassFqn.parse(linkObjectCase);
        }

        metainfoService.getMetaClass(metaclass, new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass metaClass)
            {
                property.getValueWidget().clear();
                property.getValueWidget().refreshPopupCellList();

                List<Attribute> possibleLinkAttributes = metaClass.getAttributes()
                        .stream()
                        .filter(new PossibleLinkAttributeFilter(metaClass.getFqn()))
                        .sorted(ITitled.COMPARATOR)
                        .collect(Collectors.toList());

                for (Attribute possibleLinkAttribute : possibleLinkAttributes)
                {
                    property.getValueWidget().addItem(possibleLinkAttribute.getTitle(),
                            AttributeFqn.toString(metaClass.getFqn(), possibleLinkAttribute.getCode()));
                }
                Collection<Relation> parentRelations = metaClass.getOutgoingRelation()
                        .stream()
                        .filter(RelationFilters.isParent().and(RelationFilters.isSelf()))
                        .collect(Collectors.toList());

                if (!parentRelations.isEmpty())
                {
                    property.trySetObjValue(
                            AttributeFqn.toString(metaClass.getFqn(), ru.naumen.core.shared.Constants.PARENT_ATTR));
                }
                else
                {
                    property.setValue(null);
                }

                callback.onSuccess(true);
            }
        });
    }
}
