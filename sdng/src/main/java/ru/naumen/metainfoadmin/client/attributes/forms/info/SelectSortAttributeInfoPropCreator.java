package ru.naumen.metainfoadmin.client.attributes.forms.info;

import java.util.Objects;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.sort.SelectSortConstants;

/**
 * Создает {@link Property} для отображения информации о
 * сортировке списка на модальной форме свойств атрибута
 *
 * <AUTHOR>
 * @since 30 июл. 2018 г.
 */
public class SelectSortAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    private SelectSortConstants constants;

    @Override
    protected void createInt(String code)
    {
        String sortCode = propertyValues.getProperty(code);
        String atrCode = attribute.getType().getCode();
        if (Objects.equals(attribute.getEditPresentation().getCode(), Presentations.STRING_EDIT_WITH_CATALOG_SUGGESTION)
            || atrCode.equals(CatalogItemsAttributeType.CODE)
            || atrCode.equals(CatalogItemAttributeType.CODE))
        {
            createProperty(code, constants.selectSortTypes().get(sortCode));
        }
    }
}
