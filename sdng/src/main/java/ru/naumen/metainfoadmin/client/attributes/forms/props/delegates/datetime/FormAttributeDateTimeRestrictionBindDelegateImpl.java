package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.isDateOrDateTimeAttribute;
import static ru.naumen.metainfo.shared.Constants.CustomForm.FQN;

import java.util.Objects;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.client.customforms.parameters.ParameterForm;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;
import ru.naumen.metainfoadmin.shared.customforms.GetCustomFormAction;

/**
 * <AUTHOR>
 * @since 11 дек. 2018 г.
 *
 */
public class FormAttributeDateTimeRestrictionBindDelegateImpl<F extends ParameterForm> extends
        PropertyDelegateBindImpl<SelectItem, ListBoxProperty> implements
        AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxProperty>
{

    private final DispatchAsync dispatch;

    @Inject
    public FormAttributeDateTimeRestrictionBindDelegateImpl(DispatchAsync dispatch)
    {
        this.dispatch = dispatch;
    }

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxProperty property, AsyncCallback<Void> callback)
    {
        MetaClassLite metaClassLite = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        String attributeCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE);
        String code = metaClassLite.getFqn().getCode();
        if ("form-template".equals(code) || code == null || code.isEmpty() || FQN.getCode().equals(code))
        {
            callback.onSuccess(null);
            return;
        }
        dispatch.execute(new GetCustomFormAction(metaClassLite.getCode()),
                new BasicCallback<SimpleResult<DtoContainer<CustomForm>>>(context.getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtoContainer<CustomForm>> result)
                    {
                        super.handleSuccess(result);
                        bindProperty(context, property, callback, attributeCode, result.get().get());
                    }

                });
    }

    private void bindProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Void> callback, String attributeCode, @Nullable CustomForm customForm)
    {
        if (customForm != null)
        {
            SingleSelectCellList<String> valueWidget = property.getValueWidget();
            valueWidget.clear();
            valueWidget.setHasEmptyOption(true);
            customForm.getAttributes().stream()
                    .filter(attr -> isDateOrDateTimeAttribute(attr) && !Objects.equals(attr.getCode(), attributeCode))
                    .sorted(CommonUtils.ITITLED_COMPARATOR)
                    .forEach(attr -> valueWidget.addItem(attr.getTitle(), attr.getCode()));
            super.bindProperty(context, property, callback);
        }
    }

}
