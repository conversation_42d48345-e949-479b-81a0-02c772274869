package ru.naumen.metainfoadmin.client.customforms;

import static ru.naumen.metainfo.shared.ui.customform.Constants.CustomUserForm.Attributes.ATTRIBUTES_GROUP;
import static ru.naumen.metainfo.shared.ui.customform.Constants.CustomUserForm.Attributes.COMMENT_ON_FORM;
import static ru.naumen.metainfo.shared.ui.customform.Constants.CustomUserForm.Attributes.FORM_TYPE;
import static ru.naumen.metainfo.shared.ui.customform.Constants.CustomUserForm.Attributes.TITLE;
import static ru.naumen.metainfo.shared.ui.customform.Constants.CustomUserForm.Attributes.TRANSITION_CASES;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.common.collect.ImmutableList;

import java.util.ArrayList;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.metainfo.client.ui.CustomFormMessages;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ui.customform.CustomFormType;
import ru.naumen.metainfoadmin.client.customforms.command.CustomFormCommandGinModule;

/**
 * <AUTHOR>
 * @since 21.04.2016
 */
public class CustomFormGinModule extends AbstractGinModule
{
    public static class CustomFormNamesProvider implements Provider<List<Pair<String, String>>>
    {
        @Inject
        private CustomFormMessages messages;

        @Override
        public List<Pair<String, String>> get()
        {
            List<Pair<String, String>> result = new ArrayList<>();
            result.add(Pair.create(CustomFormType.ChangeCaseForm.toString(), messages.changeCaseForm()));
            result.add(Pair.create(CustomFormType.ChangeResponsibleForm.toString(), messages.changeResponsibleForm()));
            result.add(Pair.create(CustomFormType.QuickForm.toString(), messages.quickAddAndEditForm()));
            result.add(Pair.create(CustomFormType.MassEditForm.toString(), messages.massEditForm()));
            return result;
        }
    }

    static class CustomFormAttrsProvider implements Provider<ImmutableList<AttributeFqn>>
    {
        @Override
        public ImmutableList<AttributeFqn> get()
        {
            //@formatter:off
            return ImmutableList.of(
                    FORM_TYPE,
                    TITLE,
                    TRANSITION_CASES,
                    ATTRIBUTES_GROUP,
                    COMMENT_ON_FORM);
            //@formatter:on
        }
    }

    public static final String OTHER_FORM_ATTRS = "customFormAttrs";
    public static final String CUSTOM_FORM_NAMES = "customFormNames";

    @Override
    protected void configure()
    {
        install(new CustomFormCommandGinModule());

        //@formatter:off
        bind(new TypeLiteral<ImmutableList<AttributeFqn>>(){})
            .annotatedWith(Names.named(OTHER_FORM_ATTRS))
            .toProvider(CustomFormAttrsProvider.class);
        bind(new TypeLiteral<List<Pair<String, String>>>(){})
            .annotatedWith(Names.named(CUSTOM_FORM_NAMES))
            .toProvider(CustomFormNamesProvider.class).in(Singleton.class);
        //@formatter:on
    }
}
