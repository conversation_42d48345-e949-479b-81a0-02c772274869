package ru.naumen.metainfoadmin.client.embeddedapplications.form;

import static ru.naumen.metainfo.shared.embeddedapplication.ExternalApplicationUrlDefinition.SCRIPT_DEFINED;
import static ru.naumen.metainfo.shared.embeddedapplication.ExternalApplicationUrlDefinition.SCRIPT_DEFINED_PARAMETERS;
import static ru.naumen.metainfo.shared.embeddedapplication.ExternalApplicationUrlDefinition.SYSTEM_DEFINED;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfo.shared.embeddedapplication.ExternalApplication;
import ru.naumen.metainfo.shared.embeddedapplication.ExternalApplicationUrlDefinition;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;
import ru.naumen.metainfoadmin.client.widgets.script.component.ScriptComponentEditWidget;

/**
 * Дополняет презентер формы приложения свойствами, специфичными для внешнего приложения
 *
 * <AUTHOR>
 * @since 28.10.16
 */
public class ExternalApplicationAdditionalProperties extends ApplicationWithScriptPropertiesProvider
{
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> applicationAddress;
    private PropertyRegistration<String> applicationAddressPR;

    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    private SelectListProperty<String, SelectItem> appUrlDefinition;
    private PropertyRegistration<SelectItem> appUrlDefinitionPR;

    @Inject
    private NotEmptyValidator notEmptyValidator;

    @Inject
    private EmbeddedApplicationMessages messages;

    @Override
    public void addAdditionalProperties(PropertyDialogDisplay display, PropertyRegistration<?> previousProperty,
            RegistrationContainer registrationContainer)
    {
        applicationAddress.setCaption(messages.applicationAddress());
        applicationAddress.setValidationMarker(true);
        applicationAddress.setMaxLength(Constants.StringAttributeType.MAX_LENGTH_DEFAULT);
        DebugIdBuilder.ensureDebugId(applicationAddress, "serverLink");

        appUrlDefinition.setCaption(messages.methodOfUrlDefinition());
        DebugIdBuilder.ensureDebugId(appUrlDefinition, "appUrlDefinition");
        SingleSelectCellList<String> select = appUrlDefinition.getValueWidget();
        select.addItem(messages.parametersAreDefinedBySystemLogic(),
                SYSTEM_DEFINED.name());
        select.addItem(messages.parametersAreDefinedByScript(), SCRIPT_DEFINED_PARAMETERS.name());
        select.addItem(messages.wholeUrlIsDefinedByScript(), SCRIPT_DEFINED
                .name());

        appUrlDefinitionPR = display.addPropertyAfter(appUrlDefinition, previousProperty);
        appUrlDefinition.addValueChangeHandler(handler ->
        {

            ExternalApplicationUrlDefinition method = ExternalApplicationUrlDefinition
                    .valueOf(SelectItemValueExtractor.extract(handler.getValue()));

            switch (method)
            {
                case SYSTEM_DEFINED:
                case SCRIPT_DEFINED_PARAMETERS:
                    addAppAddressProperty(display);
                    break;

                default:
                    removeAppAddressProperty();
                    break;
            }
            handleNewValueForScript(method);
        });

        addAppAddressProperty(display);
        super.addAdditionalProperties(display, previousProperty, registrationContainer);
    }

    @Override
    public EmbeddedApplication createApplication()
    {
        ExternalApplication externalApplication = new ExternalApplication();
        externalApplication.setApplicationType(EmbeddedApplicationType.ExternalApplication);
        return externalApplication;
    }

    @Override
    public void fillAdditionalProperties(EmbeddedApplicationAdminSettingsDto embeddedApplication)
    {
        if (getApplicationType().equals(embeddedApplication.getEmbeddedApplicationType()))
        {
            appUrlDefinition.trySetObjValue(embeddedApplication.getExternalApplicationUrlDefinition().name());
            if (embeddedApplication.getExternalApplicationUrlDefinition()
                == ExternalApplicationUrlDefinition.SCRIPT_DEFINED)
            {
                removeAppAddressProperty();
                script.setValidationMarker(true);
            }
            else
            {
                applicationAddress.setValue(embeddedApplication.getApplicationAddress());
                script.setValidationMarker(false);
            }
            super.fillAdditionalProperties(embeddedApplication);
        }
    }

    @Override
    public Boolean getValueOfAdditionalProperties(EmbeddedApplicationAdminSettingsDto embeddedApplication)
    {
        if (!validation.validate())
        {
            return false;
        }

        if (getApplicationType().equals(embeddedApplication.getEmbeddedApplicationType()))
        {
            embeddedApplication.setApplicationAddress(applicationAddress.getValue());
            embeddedApplication.setExternalApplicationUrlDefinition(ExternalApplicationUrlDefinition.valueOf(
                    SelectListPropertyValueExtractor.getValue(appUrlDefinition)));
            if (embeddedApplication.getExternalApplicationUrlDefinition()
                == ExternalApplicationUrlDefinition.SCRIPT_DEFINED)
            {
                embeddedApplication.setApplicationAddress(null);
            }
            super.getValueOfAdditionalProperties(embeddedApplication);
        }

        return true;
    }

    @Override
    public void removeAdditionalProperties()
    {
        if (applicationAddressPR != null)
        {
            applicationAddressPR.unregister();
            applicationAddressPR = null;
        }
        if (appUrlDefinitionPR != null)
        {
            appUrlDefinitionPR.unregister();
            appUrlDefinitionPR = null;
        }
        super.removeAdditionalProperties();
    }

    private void addAppAddressProperty(PropertyDialogDisplay display)
    {
        if (applicationAddressPR == null)
        {
            applicationAddressPR = display.addPropertyAfter(applicationAddress, appUrlDefinitionPR);
            validation.validate(applicationAddress, notEmptyValidator);
        }
    }

    private void handleNewValueForScript(ExternalApplicationUrlDefinition method)
    {
        ScriptComponentEditWidget widget = script.getValueWidget();
        if (method == SCRIPT_DEFINED)
        {
            script.setValidationMarker(true);
            widget.rebuildTree(true, scriptCategory);
        }
        else
        {
            script.setValidationMarker(false);
            widget.rebuildTree(false, scriptCategory);
        }
    }

    private void removeAppAddressProperty()
    {
        if (applicationAddressPR != null)
        {
            validation.unvalidate(applicationAddress);
            applicationAddressPR.unregister();
            applicationAddressPR = null;
        }
    }

    private EmbeddedApplicationType getApplicationType()
    {
        return EmbeddedApplicationType.ExternalApplication;
    }
}
