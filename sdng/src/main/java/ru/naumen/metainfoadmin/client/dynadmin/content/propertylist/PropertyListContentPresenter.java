package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist;

import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.base.Preconditions;

import java.util.ArrayList;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.ToolPanelContentPresenterBase;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.Constants.MetaClassProperties;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.ui.AttributeToolPanel;
import ru.naumen.metainfo.shared.ui.ClientInfo;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfo.shared.ui.PropertyListBase;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.metainfoadmin.client.common.content.AbstractInfoContentPresenter;
import ru.naumen.metainfoadmin.client.common.content.ActionContentPanelDisplayImpl.ContentCommandAction;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.common.content.HasPropertiesContentDisplay;
import ru.naumen.metainfoadmin.client.common.content.commands.TabContentCommandCode;
import ru.naumen.metainfoadmin.client.dynadmin.ContentUtils;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.PropertyListContentDisplayMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.AttributeToolPanelContentPresenter;
import ru.naumen.metainfoadmin.client.group.BeforeDeleteGroupEvent;
import ru.naumen.metainfoadmin.client.group.BeforeDeleteGroupHandler;

/**
 * Презентер контента "Параметры объекта"
 *
 * <AUTHOR>
 * @since 18.01.2011
 */
public class PropertyListContentPresenter<C extends PropertyListBase>
        extends AbstractInfoContentPresenter<HasPropertiesContentDisplay, C, UIContext>
        implements BeforeDeleteGroupHandler
{
    public interface AttributeToolPanelContentPresenterFactory
    {
        AttributeToolPanelContentPresenter<UIContext> create(Attribute attr);
    }

    @Inject
    protected MetainfoServiceAsync metainfoService;
    @Inject
    protected PropertyListContentDisplayMessages messages;
    @Inject
    protected AdminContentFactory contentFactory;
    @Inject
    private ContentUtils contentUtils;
    @Inject
    private AttributeToolPanelContentPresenterFactory editableToolPanelFactory;

    protected String helpText;
    protected ContentPresenter<ToolPanel, UIContext> toolPanelPresenter;
    private final List<AttributeToolPanelContentPresenter<UIContext>> attributesPanel = new ArrayList<>();

    @Inject
    public PropertyListContentPresenter(HasPropertiesContentDisplay display, EventBus eventBus)
    {
        this(display, eventBus, "PropertyList");
    }

    protected PropertyListContentPresenter(HasPropertiesContentDisplay display, EventBus eventBus, String debugPrefix)
    {
        super(display, eventBus, debugPrefix);
    }

    @Override
    protected void initStyles()
    {
        super.initStyles();
        getDisplay().asWidget().addStyleName(WidgetResources.INSTANCE.contentLayout().propertyListContent());
    }

    @Override
    public void onBeforeDeleteGroup(BeforeDeleteGroupEvent event)
    {
        if (getContent().getAttributeGroup().equals(event.getGroup().getCode()))
        {
            event.cancel(messages.place());
        }
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();

        if (isRevealed) // это условие перенести в вызов метода, чтоб работало для всех контентов
        {
            refreshTable();
            if (toolPanelPresenter != null && toolPanelPresenter.isBound())
            {
                ((ToolPanelContentPresenterBase<?, ?>)toolPanelPresenter).rebuild();
                toolPanelPresenter.refreshDisplay();
            }
        }
    }

    protected String getCaption()
    {
        return metainfoUtils.getLocalizedValue(getContent().getCaption());
    }

    protected Set<String> getDisabledAttributes()
    {
        return getRootContext().getContextProperty(MetaClassProperties.DISABLED_ATTRIBUTES);
    }

    @Override
    protected void initCommands()
    {
        initCommandDisplay(getContext(), TabContentCommandCode.MOVE_TAB_CONTENT, ContentCommandAction.MOVE);
        if (!(content instanceof ClientInfo) && context.getRootContent() instanceof Window)
        {
            initCommandDisplay(getContext(), TabContentCommandCode.EDIT_TOOL_PANEL,
                    ContentCommandAction.EDIT_TOOL_PANEL);
        }
        if (isEditable())
        {
            initCommandDisplay(getContext(), TabContentCommandCode.EDIT_CONTENT, ContentCommandAction.EDIT);
        }
        initCommandDisplay(getParentContentContext(), TabContentCommandCode.DELETE_CONTENT,
                ContentCommandAction.DELETE);
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        registerHandler(eventBus.addHandler(BeforeDeleteGroupEvent.getType(), this));

        //На формах добавления/редактирования toolPanel не нужна
        if (UI.WINDOW_KEY.equals(getContext().getCode()))
        {
            toolPanelPresenter = contentFactory.build(getContent().getToolPanel(), getContext());
            toolPanelPresenter.getDisplay()
                    .asWidget()
                    .addStyleName(WidgetResources.INSTANCE.buttons().contentToolPanel());
            getDisplay().bindActionBar(toolPanelPresenter.getDisplay());
        }
    }

    @Override
    public void onRefresh(RefreshContentEvent event)
    {
        super.onRefresh(event);
        // Изменение тулбара списка свойств
        if (event.getContent().getParent() != null && event.getContent().getParent().equals(getContent())
            && toolPanelPresenter != null && toolPanelPresenter.isBound())
        {
            ((ToolPanelContentPresenterBase<?, ?>)toolPanelPresenter).rebuild();
            toolPanelPresenter.refreshDisplay();
        }
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        if (null != toolPanelPresenter)
        {
            toolPanelPresenter.unbind();
        }

        for (AttributeToolPanelContentPresenter<UIContext> oldPanel : attributesPanel)
        {
            oldPanel.unbind();
        }
    }

    @Override
    protected String getHelpText()
    {
        final AttributeGroup attributeGroup = getContext().getMetainfo()
                .getAttributeGroup(getContent().getAttributeGroup());
        return attributeGroup == null
                ? StringUtilities.EMPTY : messages.objProperty() + ' '
                                          + messages.helpText(attributeGroup.getTitle());
    }

    private void refreshTable()
    {
        for (AttributeToolPanelContentPresenter<UIContext> oldPanel : attributesPanel)
        {
            oldPanel.unbind();
        }
        attributesPanel.clear();

        final AttributeGroup attributeGroup = getContext().getMetainfo()
                .getAttributeGroup(getContent().getAttributeGroup());
        final List<String> attrCodes = new ArrayList<>();

        Map<Attribute, IsWidget> attributes = CollectionUtils.convertToMap(
                getContext().getMetainfo().getGroupAttributes(getContent().getAttributeGroup()),
                input ->
                {
                    Preconditions.checkNotNull(input);
                    attrCodes.add(input.getCode());
                    return input;
                }, input ->
                {
                    Preconditions.checkNotNull(input);

                    AttributeToolPanelContentPresenter<UIContext> toolPanelPresenter = editableToolPanelFactory.create(
                            input);
                    toolPanelPresenter.init(getContent().getAttributeToolPanel(input.getCode()), getContext());
                    toolPanelPresenter.bind();
                    attributesPanel.add(toolPanelPresenter);

                    return toolPanelPresenter.getDisplay();
                });

        if (attributeGroup != null)
        {
            getDisplay().setCaption(getCaption());
            contentUtils.fillProperties(display, attributes, true, false, getDisabledAttributes());

            List<AttributeToolPanel> panelsToRemove = new ArrayList<>();
            for (AttributeToolPanel attributePanel : getContent().getAttributeToolPanels())
            {
                if (!attrCodes.contains(attributePanel.getAttributeCode()))
                {
                    panelsToRemove.add(attributePanel);
                }
            }
            getContent().getAttributeToolPanels().removeAll(panelsToRemove);
            List<LocalizedString> caption = getContent().getCaption();
            metainfoUtils.setLocalizedValue(caption, getCaption());
        }
    }
}
