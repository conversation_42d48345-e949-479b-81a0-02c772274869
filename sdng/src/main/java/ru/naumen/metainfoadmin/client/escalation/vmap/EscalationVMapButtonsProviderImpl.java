package ru.naumen.metainfoadmin.client.escalation.vmap;

import static ru.naumen.core.shared.utils.CommonUtils.assertNotNull;

import java.util.List;

import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfoadmin.client.catalog.buttons.CatalogButtonsProviderImpl;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogItemCommandParam;
import ru.naumen.metainfoadmin.client.escalation.vmap.command.EscalationVMapCommandGinModule.EscalationVMapItemCommandCode;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @since 21.12.2012
 */
@SuppressWarnings("unchecked")
public class EscalationVMapButtonsProviderImpl extends CatalogButtonsProviderImpl<EscalationValueMapContext>
{
    @Override
    public List<ButtonPresenter<DtoContainer<Catalog>>> create(OnStartCallback<DtObject> callback,
            EscalationValueMapContext context)
    {
        DtoContainer<Catalog> catalog = context.getCatalog();
        assertNotNull(catalog, "Catalog in context");
        return Lists.newArrayList(addItemPresenter(callback, catalog));
    }

    @Override
    protected ButtonPresenter<DtoContainer<Catalog>> addItemPresenter(OnStartCallback<DtObject> callback,
            DtoContainer<Catalog> catalog)
    {
        CatalogItemCommandParam<DtObject> itemParam = new CatalogItemCommandParam<DtObject>(null, callback,
                catalog.get(),
                false);
        return (ButtonPresenter<DtoContainer<Catalog>>)buttonFactory.create(ButtonCode.ADD_ELEMENT, messages.addItem(),
                EscalationVMapItemCommandCode.ADD_ESCALATION_VMAP_ITEM, itemParam);
    }

}
