package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.catalog;

import java.util.List;
import java.util.stream.Collectors;

import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.HasFolders;
import ru.naumen.core.shared.Constants.VectorIconsCatalog;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.filters.CatalogFilters;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Обработчик обновления значения свойства "Элемент справочника"
 * <AUTHOR>
 * @since 17.05.2012
 */
public class TargetCatalogRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    private class GetCatalogsCallback extends BasicCallback<List<DtoContainer<Catalog>>>
    {
        private final PropertyContainerContext context;
        private final ListBoxProperty property;
        private final AsyncCallback<Boolean> callback;

        public GetCatalogsCallback(PropertyContainerContext context, ListBoxProperty property,
                AsyncCallback<Boolean> callback)
        {
            this.context = context;
            this.property = property;
            this.callback = callback;
        }

        @Override
        protected void handleSuccess(List<DtoContainer<Catalog>> catalogs)
        {
            List<Catalog> catalogList = catalogs.stream().map(DtoContainer::get).collect(Collectors.toList());
            metainfoUtils.sort(catalogList);

            SingleSelectCellList<String> selectList = property.getValueWidget();
            selectList.clear();
            for (Catalog cat : catalogList)
            {
                selectList.addItem(cat.getTitle(), cat.getItemMetaClass().getFqn().toString());
            }
            String value = getValue(catalogList, context);
            property.trySetObjValue(value);
            context.getPropertyValues().setProperty(AttributeFormPropertyCode.TARGET_CATALOG, value);
            callback.onSuccess(true);
        }

    }

    @Inject
    AdminMetainfoServiceAsync metainfoService;
    @Inject
    MetainfoUtils metainfoUtils;

    @Override
    public void refreshProperty(final PropertyContainerContext context, final ListBoxProperty property,
            final AsyncCallback<Boolean> callback)
    {
        String typeCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        if (!(CatalogItemAttributeType.CODE.equals(typeCode) || CatalogItemsAttributeType.CODE.equals(typeCode)))
        {
            callback.onSuccess(false);
            return;
        }

        if (ObjectUtils.equals(HasFolders.FOLDERS,
                context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE)))
        {
            callback.onSuccess(false);
            return;
        }

        // Временно нужно убрать из выборки шрифтовые и векторные иконки
        Predicate<Catalog> predicate = Predicates.and(CatalogFilters.notHidden(), catalog ->
                !VectorIconsCatalog.CODE.equals(catalog.getCode()));

        metainfoService.getCatalogs(predicate, new GetCatalogsCallback(context, property, callback));
    }

    protected String getValue(List<Catalog> catalogs, PropertyContainerContext context)
    {
        Attribute attribute = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        if (attribute != null)
        {
            return attribute.getType().<ObjectAttributeType> cast().getRelatedMetaClass().toString();
        }
        return catalogs.get(0).getItemMetaClass().getFqn().toString();
    }
}
