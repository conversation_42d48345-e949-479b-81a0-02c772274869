package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.admin.client.PlaceFactory;
import ru.naumen.admin.client.PlaceFactoryRegister;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInMobileApp;

/**
 * Представление для отображения значения места использования "Мобильное приложение" на форме "Используется в
 * настройках" в таблице атрибутов
 * <AUTHOR>
 * @since 3 Jul 18
 */
@Singleton
public class AttributeUsageInMobileAppHtmlFactoryImpl extends AttributeHtmlFactoryImpl<AttributeUsageInMobileApp>
{
    @Inject
    private Formatters formatters;
    @Inject
    private CommonMessages messages;
    @Inject
    private PlaceHistoryMapper historyMapper;
    @Inject
    private PlaceFactoryRegister placeFactoryRegister;

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInMobileApp usage)
    {
        SafeHtmlBuilder builder = new SafeHtmlBuilder();

        switch (usage.getType())
        {
            case LIST_CARD:
                builder.append(formatters.formatHyperlinkAsHtml(createLinkToMobileApp(usage,
                        PlaceFactory.MOBILE_LIST_CARD, messages.mobileListCardUsagePlace())));
                break;
            case OBJECT_CARD:
                builder.append(formatters.formatHyperlinkAsHtml(createLinkToMobileApp(usage,
                        PlaceFactory.MOBILE_OBJECT_CARD, messages.mobileObjectCardUsagePlace())));
                break;
            case ADD_FORM:
                builder.append(formatters.formatHyperlinkAsHtml(createLinkToMobileApp(usage,
                        PlaceFactory.MOBILE_ADD_FORM, messages.mobileAddFormUsagePlace())));
                break;
            case EDIT_FORM:
                builder.append(formatters.formatHyperlinkAsHtml(createLinkToMobileApp(usage,
                        PlaceFactory.MOBILE_EDIT_FORM, messages.mobileEditFormUsagePlace())));
                break;
        }
        return builder.toSafeHtml();
    }

    private Hyperlink createLinkToMobileApp(AttributeUsageInMobileApp usage, String placeFactoryConstant,
            String message)
    {
        //@formatter:off
        return new Hyperlink(
                    message + " \"" + usage.getTitle() + "\"", 
                    StringUtilities.getHrefByToken(historyMapper.getToken(placeFactoryRegister.getPlaceFactory(placeFactoryConstant).create(usage))));
        //@formatter:on
    }
}
