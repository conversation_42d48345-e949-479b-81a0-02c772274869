package ru.naumen.metainfoadmin.client.attributes.forms.usage;

import java.util.HashSet;
import java.util.Map;

import com.google.gwt.safehtml.shared.SafeUri;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ClassTypesAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.UsagePlacesAttributeType;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsageAttr;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType_SnapshotObject;
import ru.naumen.metainfo.shared.elements.Attribute_SnapshotObject;
import ru.naumen.metainfo.shared.elements.Presentation_SnapshotObject;
import ru.naumen.metainfoadmin.client.eventaction.list.EventActionsAdvlistFactory;
import ru.naumen.objectlist.client.metainfo.FakeMetainfoAdvlistProviderBase;

/**
 * Провайдер метаинформации для списка объектов {@link UsageAttr} на форме "Используется в настройках"
 * <AUTHOR>
 * @since 15 Jun 18
 */
public class AttributeUsageMetainfoAdvlistProvider extends FakeMetainfoAdvlistProviderBase
{
    @Override
    protected Map<String, Attribute> createAttributes()
    {
        Map<String, Attribute> attrs = super.createAttributes();

        Attribute usagePlaces = createUsagePlacesAttribute();
        attrs.put(UsageAttr.Attributes.ATTR_USAGE_PLACES.toString(), usagePlaces);
        attrCodes.add(UsageAttr.Attributes.ATTR_USAGE_PLACES.toString());

        Attribute classTypes = createClassTypesAttribute();
        attrs.put(UsageAttr.Attributes.ATTR_CLASS_TYPES.toString(), classTypes);
        attrCodes.add(UsageAttr.Attributes.ATTR_CLASS_TYPES.toString());

        return attrs;
    }

    @Override
    protected SafeUri createTargetUri(DtObject object)
    {
        return null;
    }

    @Override
    protected AttributeFqn getAttrCode()
    {
        return null;
    }

    @Override
    protected AttributeFqn getAttrTitle()
    {
        return null;
    }

    private Attribute createClassTypesAttribute()
    {
        Attribute_SnapshotObject classTypes = new Attribute_SnapshotObject();
        classTypes.__init__fqn(UsageAttr.Attributes.ATTR_CLASS_TYPES);
        classTypes.__init__code(UsageAttr.Attributes.ATTR_CLASS_TYPES.getCode());

        AttributeType_SnapshotObject classTypesType = new AttributeType_SnapshotObject();
        classTypesType.__init__attribute(classTypes);
        classTypesType.__init__code(ClassTypesAttributeType.CODE);
        classTypesType.__init__permittedTypes(new HashSet<>());
        classTypesType.put(FakeMetaClassesConstants.TARGET_URI_CREATOR, getTargetUriCreator());

        classTypes.__init__type(classTypesType);
        classTypes.__init__computable(false);
        classTypes.__init__title(cmessages.classTypesOnAttrShowUsageForm());
        classTypes.__init__metaClass(EventActionsAdvlistFactory.getEventActionMetaClass());
        classTypes.__init__metaClassLite(EventActionsAdvlistFactory.getEventActionMetaclassLite());
        classTypes.__init__filteredByScript(false);
        classTypes.__init__hiddenAttrCaption(false);

        Presentation_SnapshotObject editPresentation = new Presentation_SnapshotObject();
        editPresentation.__init__code(Presentations.UNKNOWN_EDIT);
        classTypes.__init__editPresentation(editPresentation);

        Presentation_SnapshotObject viewPresentation = new Presentation_SnapshotObject();
        viewPresentation.__init__code(Presentations.CLASS_TYPES_VIEW);
        classTypes.__init__viewPresentation(viewPresentation);
        return classTypes;
    }

    private Attribute createUsagePlacesAttribute()
    {
        Attribute_SnapshotObject usagePlaces = new Attribute_SnapshotObject();
        usagePlaces.__init__fqn(UsageAttr.Attributes.ATTR_USAGE_PLACES);
        usagePlaces.__init__code(UsageAttr.Attributes.ATTR_USAGE_PLACES.getCode());

        AttributeType_SnapshotObject usagePlacesType = new AttributeType_SnapshotObject();
        usagePlacesType.__init__attribute(usagePlaces);
        usagePlacesType.__init__code(UsagePlacesAttributeType.CODE);
        usagePlacesType.__init__permittedTypes(new HashSet<>());
        usagePlacesType.put(FakeMetaClassesConstants.TARGET_URI_CREATOR, getTargetUriCreator());

        usagePlaces.__init__type(usagePlacesType);
        usagePlaces.__init__computable(false);
        usagePlaces.__init__title(cmessages.usagePlacesOnAttrForm());
        usagePlaces.__init__metaClass(EventActionsAdvlistFactory.getEventActionMetaClass());
        usagePlaces.__init__metaClassLite(EventActionsAdvlistFactory.getEventActionMetaclassLite());
        usagePlaces.__init__filteredByScript(false);
        usagePlaces.__init__hiddenAttrCaption(false);

        Presentation_SnapshotObject editPresentation = new Presentation_SnapshotObject();
        editPresentation.__init__code(Presentations.UNKNOWN_EDIT);
        usagePlaces.__init__editPresentation(editPresentation);

        Presentation_SnapshotObject viewPresentation = new Presentation_SnapshotObject();
        viewPresentation.__init__code(Presentations.ATTR_USAGE_PLACES_VIEW);
        usagePlaces.__init__viewPresentation(viewPresentation);
        return usagePlaces;
    }

    @Override
    protected ClassFqn getClassFqn()
    {
        return UsageAttr.FQN;
    }
}
