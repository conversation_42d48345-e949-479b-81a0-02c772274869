package ru.naumen.metainfoadmin.client.eventaction;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.EVENT_ACTIONS;

import java.util.List;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.general.StringResult;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.OnStartRefreshCallback;
import ru.naumen.core.client.common.ResourceCallback;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants.NDAPConstants.NDAPTrigger;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.client.events.EventActionUpdatedEvent;
import ru.naumen.metainfo.client.events.EventActionUpdatedHandler;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.Event;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.NDAPTriggerEvent;
import ru.naumen.metainfo.shared.eventaction.PlannedEventRule;
import ru.naumen.metainfo.shared.eventaction.UserEvents;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.customforms.CustomFormParametersPresenter;
import ru.naumen.metainfoadmin.client.escalation.EscalationGinModule.EscalationPlaceTabs;
import ru.naumen.metainfoadmin.client.escalation.EscalationPlace;
import ru.naumen.metainfoadmin.client.escalation.actions.EscalationActionsMessages;
import ru.naumen.metainfoadmin.client.eventaction.EventActionFactory.EventCreator;
import ru.naumen.sec.shared.GetAllowedForUnlicensedActionWarningAction;

/**
 * Презентер карточки Действия по событию
 * <AUTHOR>
 * @since 06.12.2011
 *
 */
public class EventActionPresenter extends AdminTabPresenter<EventActionPlace>
        implements EventActionUpdatedHandler
{
    private static final String INFO_DISPLAY_ID = "eventInfo";

    private static ClassFqn getUserEventFqn(String code)
    {
        return ClassFqn.parse("userEvent", ClassFqn.parse(code).getCode());
    }

    private static boolean isNDAPTriggerEventType(Event event)
    {
        EventType eventType = event.getEventType();
        return eventType.equals(EventType.alertActivated) || eventType.equals(EventType.alertDeactivated)
               || eventType.equals(EventType.alertChanged);
    }

    private static boolean fqnContainsNDAPTriggerOnly(List<ClassFqn> fqns)
    {
        return fqns.stream().filter(fqn -> !fqn.isSameClass(NDAPTrigger.FQN)).findFirst().orElse(null) == null;
    }

    private final AdminMetainfoServiceAsync metainfoService;
    private final EventActionMessages eventActionMessages;
    private final CommonMessages commonMessages;
    private final EventActionFactory eventActionFactory;
    private final EventActionsPlace eventActionsPlace;
    private final EscalationActionsMessages escActionsMessages;
    private final I18nUtil i18nUtil;
    private final DispatchAsync dispatch;

    private EventActionInfoPresenterBase<Action> infoPresenter;
    @SuppressWarnings("rawtypes")
    private EventInfoPresenterBase eventInfoPresenter;
    private CustomFormParametersPresenter paramsPresenter;
    private ActionConditionsPresenter conditionsPresenter;

    @SuppressWarnings("rawtypes")
    private final OnStartCallback refreshCallback = new OnStartRefreshCallback<>(getDisplay(), this);

    protected EventActionWithScript eventAction;

    @Inject
    public EventActionPresenter(AdminTabDisplay display,
            EventBus eventBus,
            AdminMetainfoServiceAsync metainfoService,
            EventActionMessages eventActionMessages,
            CommonMessages commonMessages,
            EventActionFactory eventActionFactory,
            EventActionsPlace eventActionsPlace,
            EscalationActionsMessages escActionsMessages,
            I18nUtil i18nUtil,
            DispatchAsync dispatch)
    {
        super(display, eventBus);
        this.metainfoService = metainfoService;
        this.eventActionMessages = eventActionMessages;
        this.commonMessages = commonMessages;
        this.eventActionFactory = eventActionFactory;
        this.eventActionsPlace = eventActionsPlace;
        this.escActionsMessages = escActionsMessages;
        this.i18nUtil = i18nUtil;
        this.dispatch = dispatch;
        getDisplay().setTabBarVisible(false);
    }

    @SuppressWarnings("unchecked")
    @Override
    public void onEventActionUpdated(EventActionUpdatedEvent e)
    {
        if (eventAction == null)
        {
            eventAction = new EventActionWithScript(e.getEventAction());
        }
        else
        {
            eventAction.setDtObject(e.getEventAction());
        }
        if (e.getScriptDto() != null)
        {
            eventAction.putScript(e.getScriptDto());
        }
        Event event = eventAction.getObject().getEvent();
        List<ClassFqn> fqns = eventAction.getObject().getLinkedClasses();
        if (event != null && eventInfoPresenter != null)
        {
            EventType eventType = event.getEventType();
            if (eventType.equals(eventInfoPresenter.getEventType()))
            {
                eventInfoPresenter.init(event, fqns);
                eventInfoPresenter.refreshDisplay();
            }
            else
            {
                eventInfoPresenter.unbind();
                eventInfoPresenter = null;
                EventCreator<Event> eventCreator = eventActionFactory.getEventCreator(eventType);
                if (eventCreator != null)
                {
                    eventInfoPresenter = eventCreator.infoPresenter();
                    if (eventInfoPresenter != null)
                    {
                        eventInfoPresenter.init(event, eventAction.getObject().getLinkedClasses());
                    }
                }
            }
        }
        if (conditionsPresenter != null)
        {
            conditionsPresenter.init(eventAction);
            conditionsPresenter.refreshDisplay();
        }
    }

    @Override
    public void refreshDisplay()
    {
        if (null == eventAction) // непроинициализирован
        {
            return;
        }

        getDisplay().setTitle(i18nUtil.getLocalizedTitle(eventAction));

        Event event = eventAction.getObject().getEvent();
        if (event instanceof UserEvents && paramsPresenter == null)
        {
            paramsPresenter = eventActionFactory.getUserEventParamsPresenter(eventAction.getObject());
            paramsPresenter.init(eventAction);
            insertContent(paramsPresenter, "params", 1);
        }
        else if (!(event instanceof UserEvents) && paramsPresenter != null)
        {
            paramsPresenter.unbind();
            paramsPresenter = null;
        }
        if (event instanceof NDAPTriggerEvent && eventInfoPresenter == null)
        {
            EventCreator<Event> eventCreator = eventActionFactory.getEventCreator(event.getEventType());
            eventInfoPresenter = eventCreator.infoPresenter();
            List<ClassFqn> fqns = eventAction.getObject().getLinkedClasses();
            eventInfoPresenter.init(event, fqns);
            insertContent(eventInfoPresenter, INFO_DISPLAY_ID, 1);
            eventInfoPresenter.bind();
        }
        else if (event instanceof NDAPTriggerEvent && eventInfoPresenter != null && !eventInfoPresenter.isBound())
        {
            insertContent(eventInfoPresenter, INFO_DISPLAY_ID, 1);
            eventInfoPresenter.bind();
        }
        else if (!(event instanceof NDAPTriggerEvent) && !(event instanceof PlannedEventRule)
                 && eventInfoPresenter != null && !(isNDAPTriggerEventType(event)))
        {
            eventInfoPresenter.unbind();
            eventInfoPresenter = null;
        }
        List<ClassFqn> fqns = eventAction.getObject().getLinkedClasses();
        if (!fqnContainsNDAPTriggerOnly(fqns) && !(event instanceof PlannedEventRule) && eventInfoPresenter != null)
        {
            eventInfoPresenter.unbind();
            eventInfoPresenter = null;
        }

        if (event.getEventType() == EventType.arriveMessageOnQueue && conditionsPresenter != null)
        {
            conditionsPresenter.unbind();
            conditionsPresenter = null;
        }

        showAttention(eventAction.getCode());
    }

    protected void bindChildPresenters()
    {
        addContent(infoPresenter, "info");
        if (paramsPresenter != null)
        {
            addContent(paramsPresenter, "params");
        }
        if (eventInfoPresenter != null)
        {
            addContent(eventInfoPresenter, INFO_DISPLAY_ID);
        }
        if (conditionsPresenter != null)
        {
            addContent(conditionsPresenter, "conditions");
        }
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        metainfoService.getEventAction(getPlace().getCode(), new ResourceCallback<EventActionWithScript>(commonMessages)
        {
            @Override
            public void onSuccess(EventActionWithScript resource)
            {
                haveEventAction(resource);
            }
        });
        registerHandler(eventBus.addHandler(EventActionUpdatedEvent.getType(), this));
    }

    @Override
    protected void onUnbind()
    {
        if (infoPresenter != null)
        {
            infoPresenter.unbind();
        }
        if (eventInfoPresenter != null)
        {
            eventInfoPresenter.unbind();
        }
        if (conditionsPresenter != null)
        {
            conditionsPresenter.unbind();
        }
    }

    private void bindPrevPageLinkText()
    {
        if (EventType.escalation.equals(eventAction.getObject().getEvent().getEventType()))
        {
            prevPageLinkPresenter.bind(escActionsMessages.toEscalationsActions(),
                    new EscalationPlace(EscalationPlaceTabs.ACTIONS), true);
        }
        else
        {
            prevPageLinkPresenter.bind(eventActionMessages.goToEventActions(), eventActionsPlace, true);
        }
    }

    @SuppressWarnings("unchecked")
    private void haveEventAction(EventActionWithScript eventAction)
    {
        this.eventAction = eventAction;

        infoPresenter = eventActionFactory.getInfoPresenter(eventAction.getObject());
        infoPresenter.init(eventAction, refreshCallback);

        Event event = eventAction.getObject().getEvent();
        if (event != null)
        {
            if (EventType.arriveMessageOnQueue != event.getEventType())
            {
                conditionsPresenter = eventActionFactory.getConditionsPresenter(eventAction);
                conditionsPresenter.init(eventAction);
            }

            EventCreator<Event> eventCreator = eventActionFactory.getEventCreator(event.getEventType());
            if (eventCreator != null)
            {
                eventInfoPresenter = eventCreator.infoPresenter();
                if (eventInfoPresenter != null)
                {
                    eventInfoPresenter.init(event, eventAction.getObject().getLinkedClasses());
                }
            }
        }

        if (event instanceof UserEvents)
        {
            paramsPresenter = eventActionFactory.getUserEventParamsPresenter(eventAction.getObject());
            paramsPresenter.init(eventAction);
        }
        bindChildPresenters();
        bindPrevPageLinkText();
        refreshDisplay();
    }

    private void showAttention(String code)
    {
        Event event = eventAction.getObject().getEvent();
        if (!(event instanceof UserEvents))
        {
            return;
        }
        dispatch.execute(new GetAllowedForUnlicensedActionWarningAction(getUserEventFqn(code)),
                new BasicCallback<StringResult>()
                {
                    @Override
                    public void onSuccess(StringResult result)
                    {
                        if (!result.get().isEmpty())
                        {
                            setAttention(commonMessages.attention() + " " + result.get());
                        }
                    }
                });
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return EVENT_ACTIONS;
    }
}
