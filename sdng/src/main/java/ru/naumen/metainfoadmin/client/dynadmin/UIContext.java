package ru.naumen.metainfoadmin.client.dynadmin;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.shared.HasReadyState;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfo.shared.ui.Window;

/**
 * Контекст настройки карточки объекта/формы
 *
 * <AUTHOR>
 *
 */
public interface UIContext extends Context, HasReadyState
{
    /**
     *
     * @return код формы, или код карточки объекта(для карточки объекта код всегда один
     * {@link ru.naumen.metainfo.shared.Constants.UI#WINDOW_KEY})
     */
    String getCode();

    /**
     * @return контент, находящийся в режиме редактирования разметки. Либо null,
     * если не один из контентов не находится в режиме редактирования разметки 
     */
    Content getContentInLayoutEditMode();

    /**
     * @return фейковый DtObject, на карточке которого мы находимся. Может быть null
     */
    @CheckForNull
    DtObject getObject();

    /**
     * @param <T>
     * @return родительский контекст формы (вероятнее всего это контекст метакласса)
     */
    <T extends Context> T getParentContext();

    /**
     * @return корневой контекст. Типовое значение {@link Window} или {@link Form}
     */
    Content getRootContent();

    /**
     * @return возвращает информацию о контенте
     */
    ContentInfo getRootContentInfo();

    /**
     * Возвращает для шаблона контента его код.
     */
    String getTemplateCode();

    /**
     * Возвращает контекст настройки
     */
    @Nullable
    UITemplateContext getUITemplateContext();

    /**
     * Если произошло изменение значения, то посылается событие {@link ChangeContextEditableEvent}
     *
     * @return true если форму/карточку объекта можно редактировать, false -ь карточка находится в режиме предпросмотра
     */
    boolean isEditable();

    /**
     *
     * @param object фейковый DtObject, на карточке которого мы находимся. Может быть null
     */
    void setObject(@Nullable DtObject object);

    void setUITemplateContext(@Nullable UITemplateContext context);
}
