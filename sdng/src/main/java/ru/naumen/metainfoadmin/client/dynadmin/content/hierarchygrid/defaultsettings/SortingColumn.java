package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.defaultsettings;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.cell.client.SafeHtmlCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.safehtml.client.SafeHtmlTemplates;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.cellview.client.Column;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ui.ListSortElement;
import ru.naumen.metainfoadmin.shared.dynadmin.HierarchyItemSettingsContext;
import ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridUtils;
import ru.naumen.objectlist.client.mode.active.extended.advlist.sort.ListSortResources;

/**
 * Колонка, содержащая описание настроенной сортировки по умолчанию.
 * <AUTHOR>
 * @since 12.01.2021
 */
public class SortingColumn extends Column<HierarchyItemSettingsContext, SafeHtml>
{
    private final CommonMessages messages;
    private final ListSortResources resources;

    public interface SortingColumnHtmlTemplates extends SafeHtmlTemplates
    {
        @Template("<span class=\"{0}\">{1}</span>")
        SafeHtml title(String titleStyle, String title);
    }

    private static final SortingColumnHtmlTemplates templates = GWT.create(SortingColumnHtmlTemplates.class);

    @Inject
    public SortingColumn(CommonMessages messages, ListSortResources resources)
    {
        super(new SafeHtmlCell());
        this.messages = messages;
        this.resources = resources;
        resources.style().ensureInjected();
    }

    @Override
    public SafeHtml getValue(HierarchyItemSettingsContext object)
    {
        SafeHtmlBuilder builder = new SafeHtmlBuilder();
        List<ListSortElement> listSort = object.getDefaultSettings().getListSort().getElements();
        List<ListSortElement> itemDefaultSort = object.getItemDefaultSort().getElements();
        List<ListSortElement> sortElements = KendoGridUtils.isSortInherited(object.getDefaultSettings())
                ? itemDefaultSort : listSort;
        List<String> disabledByTags = object.getDisabledAttributes().stream()
                .map(AttributeFqn::toString)
                .collect(Collectors.toList());
        if (sortElements.isEmpty())
        {
            builder.appendEscaped("[")
                    .appendEscaped(messages.empty())
                    .appendEscaped("]");
        }
        else
        {
            boolean isFirst = true;
            for (ListSortElement sortElement : sortElements)
            {
                String title = sortElement.getAttrTitle() + (sortElement.isAscending() ? " ▲" : " ▼");
                if (!isFirst)
                {
                    builder.appendEscaped(" ").appendEscaped(messages.and()).appendEscaped(" ");
                }
                builder.appendEscaped("[");
                if (disabledByTags.contains(sortElement.getAttrCode()))
                {
                    builder.append(templates.title(resources.style().disabledSelectableItem(), title));
                }
                else
                {
                    builder.appendEscaped(title);
                }
                builder.appendEscaped("]");
                isFirst = false;
            }
        }
        return builder.toSafeHtml();
    }
}
