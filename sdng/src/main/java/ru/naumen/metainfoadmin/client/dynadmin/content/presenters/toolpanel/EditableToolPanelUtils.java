package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.grantAllPermissions;
import static ru.naumen.metainfo.shared.ui.Constants.TRANSITION_CHANGE_STATE;

import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextUtils;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.AddFileTool;
import ru.naumen.metainfo.shared.ui.AdvlistPrsSelectTool;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.RefreshObjectListTool;
import ru.naumen.metainfo.shared.ui.SearchBoxTool;
import ru.naumen.metainfo.shared.ui.ShowRemovedTool;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.ToolPanel;

/**
 * <AUTHOR>
 * @since 27 мая 2015 г.
 *
 */
@Singleton
public class EditableToolPanelUtils
{
    private final MetainfoUtils metainfoUtils;
    private final CommonMessages cmessages;
    private final EditableToolPanelMessages messages;
    private final ILocaleInfo localeInfo;

    /**
     * Компаратор для лексикографической сортировки тулов по названию
     */
    public Comparator<Tool> LOCALIZED_TITLE_COMPARATOR = new Comparator<Tool>()
    {
        @Override
        public int compare(Tool arg0, Tool arg1)
        {
            String title1 = getToolTitle(arg0);
            String title2 = getToolTitle(arg1);
            return nativeCompare(title1, title2, localeInfo.getCurrentLang());
        }

        private native int nativeCompare(String arg0, String arg1, String language)
        /*-{
            return arg0.localeCompare(arg1, language);
        }-*/;
    };

    @Inject
    public EditableToolPanelUtils(MetainfoUtils metainfoUtils, CommonMessages cmessages,
            EditableToolPanelMessages messages, ILocaleInfo localeInfo)
    {
        this.metainfoUtils = metainfoUtils;
        this.cmessages = cmessages;
        this.messages = messages;
        this.localeInfo = localeInfo;
    }

    /**
     * Возвращает строку с описанием "действия" тула.
     * Либо захардкоженное название тула (из тулпанели из XXX.window.xml), либо просто захардкоженная строка если у
     * тула нет названия
     */
    public String getToolAction(Tool tool, List<? extends Tool> availableTools)
    {
        if (tool instanceof AdvlistPrsSelectTool)
        {
            return messages.selectView();
        }
        if (tool instanceof SearchBoxTool)
        {
            return cmessages.search();
        }
        if (tool instanceof AddFileTool)
        {
            return cmessages.addFile().toLowerCase();
        }
        if (!(tool instanceof ActionTool))
        {
            throw new IllegalArgumentException("wrong tool type: " + tool);
        }
        if (((ActionTool)tool).getAction().equals(TRANSITION_CHANGE_STATE))
        {
            return messages.fastChangeStateButtons();
        }
        ActionTool actionTool = (ActionTool)tool;
        for (Tool hardcodedTool : availableTools)
        {
            if (!(hardcodedTool instanceof ActionTool))
            {
                continue;
            }
            ActionTool hardcodedActionTool = (ActionTool)hardcodedTool;
            if (ObjectUtils.equals(hardcodedActionTool.getAction(), actionTool.getAction()))
            {
                String result = metainfoUtils.getLocalizedValue(hardcodedActionTool.getCaption());
                if (actionTool.getAction().equals(Constants.OPEN_MASS_SERVICE_CALL_FORM_FOR_MASS_CALL))
                {
                    result += " " + messages.forMassRequest();
                }
                return result;
            }
        }
        throw new IllegalArgumentException("unknown tool - no such tool in hardcoded: " + tool);
    }

    /**
     * Возвращает название тула - либо из его контента, либо просто соотв. захардкоженную строку
     */
    public String getToolTitle(@Nullable Tool tool)
    {
        if (tool instanceof RefreshObjectListTool)
        {
            String titleFromContent = metainfoUtils.getLocalizedValue(tool.getCaption());
            if (!ObjectUtils.isEmpty(titleFromContent))
            {
                return titleFromContent;
            }
            return cmessages.refresh();
        }
        if (tool instanceof ShowRemovedTool)
        {
            String titleFromContent = metainfoUtils.getLocalizedValue(tool.getCaption());
            if (!ObjectUtils.isEmpty(titleFromContent))
            {
                return titleFromContent;
            }
            return cmessages.showRemoved();
        }
        if (tool instanceof ActionTool)
        {
            return metainfoUtils.getLocalizedValue(tool.getCaption());
        }
        if (tool instanceof AdvlistPrsSelectTool)
        {
            return messages.selectView();
        }
        if (tool instanceof SearchBoxTool)
        {
            return cmessages.search();
        }
        if (tool == null)
        {
            return null;
        }
        throw new IllegalArgumentException("wrong tool type");
    }

    public void grantToolsPermissions(Collection<Tool> tools, @Nullable ToolPanel currentToolPanel, Context context)
    {
        Set<Tool> currentTools = currentToolPanel == null || currentToolPanel.isUseSystemSettings()
                ? Collections.emptySet()
                : new HashSet<>(currentToolPanel.getTools());
        Context permissionContext = ContextUtils.getRootPermissionContext(context);
        tools.stream()
                .filter(tool -> !currentTools.contains(tool))
                .forEach(tool -> grantAllPermissions(permissionContext.getPermissions(), tool));
    }

    public void grantToolPermissions(Tool tool, Context context)
    {
        Context permissionContext = ContextUtils.getRootPermissionContext(context);
        grantAllPermissions(permissionContext.getPermissions(), tool);
    }
}
