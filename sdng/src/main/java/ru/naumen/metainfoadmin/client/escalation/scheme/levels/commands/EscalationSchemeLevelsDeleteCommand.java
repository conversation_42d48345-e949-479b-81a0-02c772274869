package ru.naumen.metainfoadmin.client.escalation.scheme.levels.commands;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.PresenterCommandEvent;
import ru.naumen.core.client.mvp.PresenterCommandEvent.PresenterCommandCode;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.metainfo.shared.dispatch2.DeleteEscalationSchemeLevelAction;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemeContext;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.EscalationSchemeLevelsMessages;

/**
 * <AUTHOR>
 * @since 21.08.2012
 *
 */
public class EscalationSchemeLevelsDeleteCommand extends BaseCommandImpl<EscalationSchemeLevel, Void>
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private Dialogs dialogs;
    @Inject
    private CommonMessages messages;
    @Inject
    private EscalationSchemeLevelsMessages levelsMessages;

    @Inject
    public EscalationSchemeLevelsDeleteCommand(@Assisted EscalationSchemeLevelsCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<EscalationSchemeLevel, Void> param)
    {
        EscalationSchemeContext context = ((EscalationSchemeLevelsCommandParam)param).getContext();
        dialogs.question(
                messages.confirmDelete(),
                messages.confirmDeleteQuestion(levelsMessages.escalationLevel(), String.valueOf(param.getValue()
                                                                                                        .getLevel()
                                                                                                + 1)),
                new DialogCallback()
                {
                    @Override
                    protected void onYes(final Dialog widget)
                    {
                        dispatch.execute(
                                new DeleteEscalationSchemeLevelAction(context.getEscalationScheme().get().getCode(),
                                        param.getValue().getLevel()), new BasicCallback<SimpleResult<Integer>>()
                                {
                                    @Override
                                    protected void handleSuccess(SimpleResult<Integer> response)
                                    {
                                        widget.hide();
                                        context.getLocalEventBus().fireEvent(
                                                new PresenterCommandEvent(PresenterCommandCode.REFRESH));
                                    }

                                    @Override
                                    protected void handleFailure(String msg, @Nullable String details)
                                    {
                                        widget.hide();
                                        super.handleFailure(msg, details);
                                    }
                                });
                    }
                });
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }
}