package ru.naumen.metainfoadmin.client.embeddedapplications;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.EMBEDDED_APPLICATIONS;
import static ru.naumen.metainfo.shared.embeddedapplication.Constants.Application.Attributes.APPLICATION_TYPE;
import static ru.naumen.metainfo.shared.embeddedapplication.Constants.Application.Attributes.ON;
import static ru.naumen.metainfo.shared.embeddedapplication.Constants.Application.Attributes.TITLE;

import com.google.common.collect.ImmutableList;
import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.SingleTabContainerPresenterAdapter;
import ru.naumen.metainfoadmin.shared.Constants.EmbeddedAppCommandCode;

/**
 * Презентер страницы со списком встроенных приложений
 * <AUTHOR>
 * @since 07.07.2016
 */
public class EmbeddedApplicationsPresenter extends SingleTabContainerPresenterAdapter<EmbeddedApplicationsPlace>
{
    @Inject
    public EmbeddedApplicationsPresenter(AdminTabDisplay display,
            EventBus eventBus,
            EmbeddedApplicationsAdvlistFactory advlistFactory)
    {
        super(display, eventBus, advlistFactory.create(EmbeddedAppCommandCode.ADD_APPLICATION,
                ImmutableList.of(TITLE, APPLICATION_TYPE, ON)));
    }

    @Override
    protected String getTitle()
    {
        return messages.applications();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return EMBEDDED_APPLICATIONS;
    }
}
