package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.LabelProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления вычислимого свойства "Класс объектов иерархии"
 *
 * <AUTHOR>
 * @since 03.11.2020
 */
public class LinkToContentHierarchyClassRefreshDelegateImpl implements PropertyDelegateRefresh<String,
        LabelProperty>
{
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, LabelProperty property,
            AsyncCallback<Boolean> callback)
    {
        final Boolean showHierarchy = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.SHOW_HIERARCHY);

        boolean isVisible = LinkToContentMetaClassPropertiesProcessor.getRelObjectListFragmentVisibility(context)
                            && Boolean.TRUE.equals(showHierarchy);

        RelationsAttrTreeObject beforeHierarchyAttr = context.getPropertyValues().<RelationsAttrTreeObject> getProperty(
                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR);

        callback.onSuccess(isVisible);

        if (isVisible)
        {
            if (beforeHierarchyAttr == null)
            {
                property.setValue("");
                return;
            }
            Attribute attrBeforeHierarchy = beforeHierarchyAttr.getAttribute();
            ClassFqn hierarchyFqn = attrBeforeHierarchy == null ? null :
                    attrBeforeHierarchy.getType().<ObjectAttributeType> cast().getRelatedMetaClass();

            property.setDisable();

            if (hierarchyFqn != null)
            {
                metainfoService.getMetaClass(hierarchyFqn, new BasicCallback<MetaClass>()
                {
                    @Override
                    protected void handleSuccess(MetaClass metaClass)
                    {
                        property.setValue(metaClass.getTitle());
                    }
                });
            }
        }
    }
}
