package ru.naumen.metainfoadmin.client.attributes.attrusage;

import com.google.inject.ImplementedBy;

import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactory;

/**
 * Интерфейс для получения/регистрации {@link AttributeHtmlFactory} по ключу Class<?>
 *
 * Используется для хранения различных представлений атрибута одного типа в зависимости от типа экземпляра,
 * передаваемого в параметр Object value
 * в {@link AttributeHtmlFactory#create(ru.naumen.core.client.attr.presentation.PresentationContext, Object)}
 * <AUTHOR>
 * @since 28 Jun 18
 */
@ImplementedBy(AttributeDiffTypesHtmlFactoriesImpl.class)
public interface AttributeDiffTypesHtmlFactories
{
    <T> AttributeHtmlFactory<T> getFactory(Class<?> clazz);

    <T> void register(Class<?> clazz, AttributeHtmlFactory<T> htmlFactoryProvider);
}
