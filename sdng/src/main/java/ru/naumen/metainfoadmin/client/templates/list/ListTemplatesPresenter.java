package ru.naumen.metainfoadmin.client.templates.list;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TEMPLATES;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminSingleTabAdvlistPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;

/**
 * Презентер вкладки шаблонов списков
 * <AUTHOR>
 * @since 06.04.2018
 */
public class ListTemplatesPresenter extends AdminSingleTabAdvlistPresenterBase<ListTemplatesPlace, DtObject>
{
    @Inject
    public ListTemplatesPresenter(AdminTabDisplay display, EventBus eventBus,
            ListTemplatesListPresenter advlistPresenter)
    {
        super(display, eventBus, advlistPresenter);
    }

    @Override
    protected String getTitle()
    {
        return messages.listTemplates();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return TEMPLATES;
    }
}
