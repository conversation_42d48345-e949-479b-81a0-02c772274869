package ru.naumen.metainfoadmin.client.jmsqueue;

import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventActionsInQueue.EVENT_ACTIONS_IN_QUEUE;
import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventActionsInQueue.EVENT_ACTIONS_IN_SYSTEM_QUEUE;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

import com.google.inject.Provider;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.ui.toolbar.LocalizedToolFactory;
import ru.naumen.core.shared.ui.toolbar.SimpleActionToolFactory;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClass_SnapshotObject;
import ru.naumen.metainfo.shared.eventaction.Constants.EventAction;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.Constants.Titles;
import ru.naumen.metainfo.shared.ui.EventActionList;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.PagerPosition;
import ru.naumen.metainfo.shared.ui.PagingSettings;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.DefaultContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.EventActionAdvlistUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.client.jmsqueue.comands.JMSQueueCommandCode;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.extended.advlist.AdvListPresentationDisplayImpl;
import ru.naumen.objectlist.client.extended.advlist.AdvListPresenterImpl;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;

/**
 * Фабрика для списка блока "Действия по событиям, обрабатываемые в очереди"
 * <AUTHOR>
 * @since 28.04.2021
 **/
public class JMSQueueEventActionsInQueueListFactory extends AdminCustomAdvlistFactoryBase
{
    private static MetaClass getEventActionMetaClass()
    {
        MetaClass_SnapshotObject metaclass = new MetaClass_SnapshotObject();
        metaclass.__init__fqn(EventAction.FQN);
        return metaclass;
    }

    @Inject
    private Provider<AdvListPresenterImpl<AdvListPresentationDisplayImpl, EventActionList>> eventActionsListPresenterProvider;
    @Inject
    private JMSQueueMessages jmessages;
    @Inject
    private CommonMessages messages;
    private DtObject jmsQueue;

    /**
     * Создать список действий по событию.
     */
    public ListPresenter<EventActionList> create(List<AttributeFqn> attrs, DtObject jmsQueue)
    {
        this.jmsQueue = jmsQueue;
        ListPresenter<EventActionList> eventActionsList = eventActionsListPresenterProvider.get();

        MetaClass eventActionMetaClass = getEventActionMetaClass();
        EventActionList content = createContent(eventActionMetaClass, attrs);

        ObjectListUIContext context = new EventActionAdvlistUIContext(new DefaultContext(eventActionMetaClass), null,
                false, null,
                eventActionsList.getListComponents(), true);

        context.setObject(jmsQueue);

        eventActionsList.init(content, context);

        return eventActionsList;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return EventAction.FQN;
    }

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        ArrayList<ExtendedListActionCellContext> actionColumns = new ArrayList<>();
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.BREAK_LINK,
                JMSQueueCommandCode.BREAK_LINK, cmessages.breakLink(), new Predicate<DtObject>()
        {
            @Override
            public boolean test(@Nullable DtObject input)
            {
                return JMSQueueHelper.isUserJMSQueue(jmsQueue);
            }
        }));
        return actionColumns;
    }

    private EventActionList createContent(MetaClass metaclass, List<AttributeFqn> attrs)
    {
        EventActionList content = new EventActionList();
        content.setClazz(metaclass.getFqn());
        content.setPresentation(PresentationType.ADVLIST.getCode());
        String contentUuid = JMSQueueHelper.isUserJMSQueue(jmsQueue)
                ? EVENT_ACTIONS_IN_QUEUE
                : EVENT_ACTIONS_IN_SYSTEM_QUEUE;
        content.setUuid(contentUuid);

        PagingSettings pagingSettings = PagingSettings.getDefaultSettings();
        content.getDefaultSettings().setPageSize(100);
        pagingSettings.setPosition(PagerPosition.BOTTOM);
        content.setPagingSettings(pagingSettings);

        content.getDisplayedAttrs().addAll(attrs);

        ToolPanel eventActionsPanel = new ToolPanel(content);
        content.setToolPanel(eventActionsPanel);

        ToolBar eventActionsPropertiesToolBar = new ToolBar(eventActionsPanel);
        eventActionsPanel.getToolBars().add(eventActionsPropertiesToolBar);
        Tool eventActionSelectPrsTool = tfInitializer.initFactory(LocalizedToolFactory.getAdminAdvlistToolFactory())
                .create().setToolBar(eventActionsPropertiesToolBar);

        //виды
        Tool saveEventActionsPresentationTool = tfInitializer
                .initFactory(new SimpleActionToolFactory(
                        ru.naumen.metainfoadmin.shared.Constants.EventActionCommandCode.SAVE_ADVLIST_PRS,
                        Titles.SAVE_PRESENTATION, Tool.PresentationType.DEFAULT, messages.savePresentation()))
                .create().setToolBar(eventActionsPropertiesToolBar);
        eventActionsPropertiesToolBar.addTool(eventActionSelectPrsTool).addTool(saveEventActionsPresentationTool);

        ToolBar filterAndSortToolBar = new ToolBar(eventActionsPanel);
        eventActionsPanel.getToolBars().add(filterAndSortToolBar);
        //фильтрация
        Tool filtrationTool = tfInitializer
                .initFactory(new SimpleActionToolFactory(
                        Constants.SHOW_ADVLIST_FILTER, Titles.FILTRATION, Tool.PresentationType.DEFAULT,
                        messages.filtration()))
                .create().setToolBar(filterAndSortToolBar);
        //сортировка
        Tool sortTool = tfInitializer
                .initFactory(
                        new SimpleActionToolFactory(
                                Constants.SHOW_ADVLIST_SORT, Titles.SORT, Tool.PresentationType.DEFAULT,
                                messages.sort()))
                .create().setToolBar(filterAndSortToolBar);
        filterAndSortToolBar.addTool(filtrationTool).addTool(sortTool);

        // добавить связь
        if (JMSQueueHelper.isUserJMSQueue(jmsQueue))
        {
            ToolBar bar2 = new ToolBar(eventActionsPanel);
            Tool addLink = tfInitializer
                    .initFactory(new SimpleActionToolFactory(
                            Constants.LINK_QUEUE, Titles.ADD_RELATION, Tool.PresentationType.DEFAULT,
                            jmessages.addLink()))
                    .create().setToolBar(bar2);
            bar2.addTool(addLink);
            eventActionsPanel.getToolBars().add(bar2);
        }

        return content;
    }
}