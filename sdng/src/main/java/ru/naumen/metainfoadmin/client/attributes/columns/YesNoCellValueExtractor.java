package ru.naumen.metainfoadmin.client.attributes.columns;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NodeList;

import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.grouplist.CellValueExtractor;

/**
 * Получения значения Да/Нет из уже отрисованной ячейки таблицы.
 *
 * <AUTHOR>
 * @since 3 окт. 2018 г.
 *
 */
public class YesNoCellValueExtractor implements CellValueExtractor<String>
{
    private static final List<String> FILTER_CLASS_NAMES = Arrays.asList(
            IconCodes.TICK, IconCodes.DOT);

    @Override
    public String extractValue(Element td)
    {
        String result = null;
        if (td != null)
        {
            NodeList<Element> childs = td.getElementsByTagName("span");
            for (int i = 0; i < childs.getLength(); i++)
            {
                //@formatter:off
                Optional<String> o = Arrays.stream(childs.getItem(i).getClassName()
                        .split(" "))
                        .filter(FILTER_CLASS_NAMES::contains).findFirst();
                //@formatter:on
                if (o.isPresent())
                {
                    result = o.get();
                    break;
                }
            }
        }
        return result;
    }
}
