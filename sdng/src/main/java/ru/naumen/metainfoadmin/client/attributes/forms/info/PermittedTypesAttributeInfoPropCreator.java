package ru.naumen.metainfoadmin.client.attributes.forms.info;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.dispatch.GetPermittedRelatedTypesResponse;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Создает {@link Property} для отображения информации о типах объектов
 * для атрибутов "Ссылка на БО", "Обратная ссылка", "Набор ссылок на БО"
 *
 * <AUTHOR>
 * @since 31 июл. 2018 г.
 */
public class PermittedTypesAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    private final static int CLASSES_LIMIT = 3;

    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    public void create()
    {
        createInt(null);
    }

    @Override
    protected void createInt(@Nullable String code)
    {
        String attrCode = attribute.getType().getCode();
        if (attrCode.equals(BOLinksAttributeType.CODE)
            || attrCode.equals(ObjectAttributeType.CODE)
            || attrCode.equals(BackLinkAttributeType.CODE))
        {
            createPermittedTypes();
        }
    }

    private String buildPropertyValue(GetPermittedRelatedTypesResponse value)
    {
        String etc = value.getPermittedTypes().size() > CLASSES_LIMIT ? " " + cmessages.etc() : StringUtilities.EMPTY;
        return value.getPermittedTypes().stream()
                       .limit(CLASSES_LIMIT)
                       .map(MetaClassLite::getTitle)
                       .collect(Collectors.joining(", ")) + etc;
    }

    private void createPermittedTypes()
    {
        String fqnAsString = attribute.getType().getProperty(ObjectAttributeType.METACLASS_FQN);
        metainfoService.getDescendantClasses(ClassFqn.parse(fqnAsString), true, new BasicCallback<List<MetaClassLite>>(
                rs)
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> childrens)
            {
                metainfoService.getPermittedTypes(attribute, new BasicCallback<GetPermittedRelatedTypesResponse>(rs)
                {
                    @Override
                    protected void handleSuccess(GetPermittedRelatedTypesResponse value)
                    {
                        if (needCreateProperty(value, childrens))
                        {
                            createProperty(AttributeFormPropertyCode.PERMITTED_TYPES, buildPropertyValue(value),
                                    formPropParameter.create(AttributeFormPropertyCode.PERMITTED_TYPES).getCaption());
                        }
                    }
                });
            }
        });
    }

    private boolean needCreateProperty(GetPermittedRelatedTypesResponse value, List<MetaClassLite> childrens)
    {
        return !value.getPermittedTypes().containsAll(childrens) && (!value.getPermittedTypes().isEmpty());
    }
}
