package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DEFAULT_VALUE;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;

/**
 * Делегат изменения значения свойства формы атрибута, который вызывает обновление свойства "Значение по умолчанию"
 * <AUTHOR>
 * @since 17.05.2012
 */
public class RefreshDefaultValueVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getPropertyControllers().get(DEFAULT_VALUE).refresh();
    }
}
