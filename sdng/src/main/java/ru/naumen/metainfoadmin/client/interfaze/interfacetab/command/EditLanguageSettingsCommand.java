package ru.naumen.metainfoadmin.client.interfaze.interfacetab.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms.EditLanguageFormPresenter;

/**
 * Команда для изменения локали и языка истории событий
 *
 * <AUTHOR>
 * @since 18.07.16
 */
public class EditLanguageSettingsCommand extends BaseCommandImpl<InterfaceSettingsContext, InterfaceSettingsContext>
{
    public static final String ID = "EditLanguageSettingsCommand";

    @Inject
    private Provider<EditLanguageFormPresenter> editFormProvider;

    @Inject
    public EditLanguageSettingsCommand(@Assisted CommandParam<InterfaceSettingsContext, InterfaceSettingsContext> param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<InterfaceSettingsContext, InterfaceSettingsContext> param)
    {
        EditLanguageFormPresenter presenter = editFormProvider.get();
        presenter.init(param.getValue());
        presenter.bind();
        presenter.getDisplay().display();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}
