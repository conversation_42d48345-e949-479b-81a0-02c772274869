package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.forms.SimpleEditContentPresenter;

/**
 * {@link Presenter} для редактирования контента типа {@link HierarchyGrid}
 *
 * <AUTHOR>
 * @since 22.08.2019
 */
public class EditHierarchyGridContentPresenter extends SimpleEditContentPresenter<HierarchyGrid>
{
    @Inject
    private HierarchyGridPropertiesPresenter propertiesPresenter;

    @Inject
    public EditHierarchyGridContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(HierarchyGrid content, UIContext context)
    {
        super.init(content, context);
        propertiesPresenter.init(this);
    }

    @Override
    protected void bindPropertiesInner()
    {
        propertiesPresenter.bindProperties(content).forEach(display::add);
        propertiesPresenter.bindHandlers(content);
    }

    @Override
    protected void updateCurrentContent()
    {
        super.updateCurrentContent();
        propertiesPresenter.setContentProperties(content);
    }

    @Override
    protected void restoreContent(HierarchyGrid oldContent)
    {
        super.restoreContent(oldContent);
        content.setBuildHierarchyFromCurrentObject(oldContent.isBuildHierarchyFromCurrentObject());
        content.setFocusOnCardObject(oldContent.getFocusOnCardObject());
        content.setStructuredObjectsViewCode(oldContent.getStructuredObjectsViewCode());
        content.setToolPanel(oldContent.getToolPanel());
    }

    @Override
    protected boolean isContentEquals(HierarchyGrid oldContent)
    {
        return super.isContentEquals(oldContent)
               && eq(oldContent.getStructuredObjectsViewCode(), content.getStructuredObjectsViewCode())
               && eq(oldContent.isBuildHierarchyFromCurrentObject(), content.isBuildHierarchyFromCurrentObject())
               && eq(oldContent.getFocusOnCardObject(), content.getFocusOnCardObject());
    }

    @Override
    protected void updateAfterSave(HierarchyGrid savedContent)
    {
        super.updateAfterSave(savedContent);
        content.setObjectFilters(savedContent.getObjectFilters());
        content.setDefaultSettings(savedContent.getDefaultSettings());
    }

    @Override
    protected void onUnbind()
    {
        propertiesPresenter.unbind();
        super.onUnbind();
    }
}
