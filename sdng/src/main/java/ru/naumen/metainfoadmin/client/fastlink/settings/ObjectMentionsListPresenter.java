package ru.naumen.metainfoadmin.client.fastlink.settings;

import static ru.naumen.commons.shared.utils.StringUtilities.join;
import static ru.naumen.core.client.adminpermission.AdminPermissionUtils.createPermissionPredicate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Sets;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NodeList;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safecss.shared.SafeStylesBuilder;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.Header;
import com.google.gwt.user.cellview.client.TextHeader;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.view.client.AbstractDataProvider;
import com.google.gwt.view.client.HasData;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.group.ListEditorDnDGroupControllerBase;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.columns.ClickableSafeHtmlTextCell;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumnFactory;
import ru.naumen.core.client.widgets.columns.LinkToPlaceWithIndentColumn;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSetting;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSettingWithTitles;
import ru.naumen.metainfo.shared.fastlink.settings.dispatch.MoveFastLinkSettingAction;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableResources;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableStyle;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.AddFastLinkSettingCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.DeleteFastLinkSettingCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.EditFastLinkSettingCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.FastLinkSettingsCommandParam;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.MoveFastLinkSettingDownCommand;
import ru.naumen.metainfoadmin.client.fastlink.settings.command.MoveFastLinkSettingUpCommand;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Презентер списка упоминаний объектов
 * <AUTHOR>
 *
 */
public class ObjectMentionsListPresenter extends BasicPresenter<TableDisplay<FastLinkSetting>>
{
    class ItemsDataProvider extends AbstractDataProvider<FastLinkSetting>
    {
        @Override
        protected void onRangeChanged(final HasData<FastLinkSetting> display)
        {
            List<FastLinkSetting> itemsList = settings;
            display.setRowData(0, itemsList);
            display.setRowCount(itemsList.size(), true);

            Scheduler.get().scheduleDeferred(() ->
            {
                updateDnDControllers();
            });
        }
    }

    public class FastLinkSettingsListDnDController extends ListEditorDnDGroupControllerBase
    {
        private ArrayList<Integer> groupIndexList = new ArrayList<>();
        private NodeList<Element> allElements = null;

        public FastLinkSettingsListDnDController()
        {
            super(getDisplay().getTableContainer().getElement());
        }

        public ArrayList<Integer> getGroupIndexList()
        {
            return groupIndexList;
        }

        @Override
        public Element getNextElement(Element element)
        {
            if (null == allElements || 0 == allElements.getLength())
            {
                return null;
            }
            int index = indexOf(element);
            if (index < 0)
            {
                return null;
            }
            int elementIndex = groupIndexList.get(index);
            int nextIndex = elementIndex + 2;
            if (nextIndex < allElements.getLength())
            {
                return allElements.getItem(nextIndex);
            }
            return null;
        }

        @Override
        public Element getPreviousElement(Element element)
        {
            if (null == allElements || 0 == allElements.getLength())
            {
                return null;
            }
            int index = indexOf(element);
            if (index < 0)
            {
                return null;
            }
            int elementIndex = groupIndexList.get(index);
            if (elementIndex <= 0)
            {
                return allElements.getItem(0);
            }
            else
            {
                return allElements.getItem(elementIndex);
            }
        }

        @Override
        public void move(final int oldPosition, final int newPosition, ReadyState readyState)
        {
            FastLinkSetting item = getTable().getVisibleItem(groupIndexList.get(oldPosition));
            MoveFastLinkSettingAction action = new MoveFastLinkSettingAction(item.getCode(), oldPosition > newPosition);
            dispatch.execute(action,
                    new BasicCallback<SimpleResult<List<DtoContainer<FastLinkSettingWithTitles>>>>(readyState)
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<List<DtoContainer<FastLinkSettingWithTitles>>> value)
                        {
                            refreshCallback.onSuccess(value.get());
                        }
                    });
        }

        public void updateElements(NodeList<Element> elements)
        {
            allElements = elements;
        }

        @Override
        protected List<Element> retrieveChildren()
        {
            List<Element> elements = new ArrayList<>();
            if (null == allElements || 0 == allElements.getLength())
            {
                return elements;
            }
            elements.add(null);
            for (Integer i : groupIndexList)
            {
                elements.add(allElements.getItem(i + 1));
            }
            return elements;
        }

        @Override
        public boolean canDragStart(@Nullable Element element)
        {
            int index = indexOf(element);
            DataTable<FastLinkSetting> table = getDisplay().getTable();
            if (index < 0 || index >= table.getVisibleItemCount())
            {
                return false;
            }
            return AdminPermissionUtils.hasEditPermission(permissions.get(table.getVisibleItem(index).getCode()));
        }
    }

    private DataTable<FastLinkSetting> getTable()
    {
        return getDisplay().getTable();
    }

    protected OnStartCallback<List<DtoContainer<FastLinkSettingWithTitles>>> refreshCallback =
            new SafeOnStartBasicCallback<List<DtoContainer<FastLinkSettingWithTitles>>>(
                    getDisplay())
            {
                @Override
                protected void handleSuccess(@Nullable List<DtoContainer<FastLinkSettingWithTitles>> value)
                {
                    if (value != null)
                    {
                        ObjectMentionsListPresenter.this.settings.clear();
                        ObjectMentionsListPresenter.this.settings.addAll(value.stream().map(DtoContainer::get)
                                .collect(Collectors.toCollection(ArrayList::new)));
                        param.setSettings(settings);
                    }
                    refreshDisplay();
                }
            };

    @Inject
    private FastLinkSettingsMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    private ObjectListColumnBuilder tableBuilder;
    @Inject
    private LinkToPlaceColumnFactory<FastLinkSetting> titleColumnFactory;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private WithArrowsCellTableResources cellTableResources;
    @Inject
    private PlaceController placeController;
    @Inject
    private ListEditorDnDControllerFactory dndControllerFactory;
    @Inject
    private DispatchAsync dispatch;

    private Map<String, ListEditorDnDController> dndControllersMap = new HashMap<>();
    private Map<String, FastLinkSettingsListDnDController> dndGroupsMap = new HashMap<>();

    private ToolBarDisplayMediator<List<FastLinkSetting>> toolBar;

    private List<FastLinkSetting> settings;
    private FastLinkSettingsCommandParam param;
    private Map<String, List<PermissionType>> permissions = new HashMap<>();

    private Boolean isListRightsEnabled;

    @Inject
    public ObjectMentionsListPresenter(TableDisplay<FastLinkSetting> display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
        display.setCollapsible(false);
    }

    public void init(List<DtoContainer<FastLinkSettingWithTitles>> containers, Boolean isListRightsEnabled)
    {
        List<FastLinkSetting> fastLinkSettings = new ArrayList<>();
        for (DtoContainer<FastLinkSettingWithTitles> container : containers)
        {
            permissions.put(container.get().getCode(), container.getProperty(SettingsSet.ADMIN_PERMISSIONS));
            fastLinkSettings.add(container.get());
        }

        this.settings = fastLinkSettings;
        this.isListRightsEnabled = isListRightsEnabled;
        param = new FastLinkSettingsCommandParam(fastLinkSettings, refreshCallback);
    }

    @Override
    public void refreshDisplay()
    {
        toolBar.refresh(settings);
        getDisplay().refresh();
    }

    protected void initTable()
    {
        DataTable<FastLinkSetting> table = getTable();
        table.setVisibleRange(0, DefaultCellTable.DEFAULT_PAGESIZE);

        WithArrowsCellTableStyle tableStyle = cellTableResources.cellTableStyle();

        addActionColumn(MoveFastLinkSettingUpCommand.ID);
        addActionColumn(MoveFastLinkSettingDownCommand.ID);

        addTitleColumn(table, tableStyle);
        addCodeColumn(table);
        addMentionTypesColumn(table);
        addAliasColumn(table);
        addContextTypesColumn(table);
        addMentionAttributeColumn(table);
        addAttributeGroupColumn(table);

        addProfilesColumn(table);

        addActionColumn(EditFastLinkSettingCommand.ID);
        addActionColumn(DeleteFastLinkSettingCommand.ID);

        table.setRowStyles((row, idx) -> null);
        table.asWidget().ensureDebugId("fast-links-settings-table");
    }

    @Override
    protected void onBind()
    {
        addTool(ButtonCode.ADD, cmessages.add(), AddFastLinkSettingCommand.ID);

        getDisplay().setCaption(messages.metaClassesForMentionInRtf());

        if (isListRightsEnabled)
        {
            Label attentionMessage = new Label();
            attentionMessage.addStyleName(WidgetResources.INSTANCE.all().tableDisplayInfo());
            attentionMessage.ensureDebugId("attentionMessage");
            attentionMessage.setText(messages.searchPermissionIsUsed());

            getDisplay().getTableContainer().insert(attentionMessage, 0);
        }

        initTable();

        final ItemsDataProvider dataProvider = new ItemsDataProvider();
        dataProvider.addDataDisplay(getTable());

        toolBar.bind();
    }

    private void addActionColumn(String... commands)
    {
        PermissionType permissionType = DeleteFastLinkSettingCommand.ID.equals(commands[0])
                ? PermissionType.DELETE
                : PermissionType.EDIT;
        tableBuilder.addActionColumn(getDisplay(), param, createPermissionPredicate(permissionType, permissions),
                commands);
    }

    private void addAliasColumn(DataTable<FastLinkSetting> table)
    {
        // Колонка "Символы для показа объектов
        Column<FastLinkSetting, SafeHtml> aliasColumn = new Column<FastLinkSetting, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(FastLinkSetting item)
            {
                return new SafeHtmlBuilder().appendEscaped(item.getAlias()).toSafeHtml();
            }
        };
        aliasColumn.setFieldUpdater((int index, FastLinkSetting item, SafeHtml value) ->
        {
            placeController.goTo(new FastLinkSettingPlace(item.getCode()));
        });
        table.addColumn(aliasColumn, messages.alias());
    }

    private void addAttributeGroupColumn(DataTable<FastLinkSetting> table)
    {
        // Колонка "Группа атрибутов"
        Column<FastLinkSetting, SafeHtml> attributeGroupColumn = new Column<FastLinkSetting, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(FastLinkSetting item)
            {
                return new SafeHtmlBuilder().appendEscaped(((FastLinkSettingWithTitles)item).getAttributeGroupTitle())
                        .toSafeHtml();
            }
        };
        attributeGroupColumn.setFieldUpdater((int index, FastLinkSetting item, SafeHtml value) ->
        {
            placeController.goTo(new FastLinkSettingPlace(item.getCode()));
        });
        table.addColumn(attributeGroupColumn, messages.attributeGroup());
    }

    private void addCodeColumn(DataTable<FastLinkSetting> table)
    {
        // Колонка "Код"
        Column<FastLinkSetting, SafeHtml> codeColumn = new Column<FastLinkSetting, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(FastLinkSetting item)
            {
                return new SafeHtmlBuilder().appendEscaped(item.getCode()).toSafeHtml();
            }
        };
        codeColumn.setFieldUpdater((int index, FastLinkSetting item, SafeHtml value) ->
        {
            placeController.goTo(new FastLinkSettingPlace(item.getCode()));
        });
        table.addColumn(codeColumn, cmessages.code());
    }

    private void addContextTypesColumn(DataTable<FastLinkSetting> table)
    {
        // Колонка "В рамках объектов"
        Column<FastLinkSetting, SafeHtml> contextTypesColumn = new Column<FastLinkSetting, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(FastLinkSetting item)
            {
                String typesTitles = join(((FastLinkSettingWithTitles)item).getContextTypesTitles());
                return new SafeHtmlBuilder().appendEscaped(typesTitles).toSafeHtml();
            }
        };
        contextTypesColumn.setFieldUpdater((int index, FastLinkSetting item, SafeHtml value) ->
        {
            placeController.goTo(new FastLinkSettingPlace(item.getCode()));
        });
        table.addColumn(contextTypesColumn, messages.contextTypes());
    }

    private void addMentionAttributeColumn(DataTable<FastLinkSetting> table)
    {
        // Колонка "Идентификатор для формирования ссылки"
        Column<FastLinkSetting, SafeHtml> mentionAttributeColumn = new Column<FastLinkSetting, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(FastLinkSetting item)
            {
                return new SafeHtmlBuilder().appendEscaped(item.getMentionAttribute()).toSafeHtml();
            }
        };
        mentionAttributeColumn.setFieldUpdater((int index, FastLinkSetting item, SafeHtml value) ->
        {
            placeController.goTo(new FastLinkSettingPlace(item.getCode()));
        });
        table.addColumn(mentionAttributeColumn, messages.mentionAttribute());
    }

    private void addMentionTypesColumn(DataTable<FastLinkSetting> table)
    {
        // Колонка "Объекты для упоминания"
        Column<FastLinkSetting, SafeHtml> mentionTypesColumn = new Column<FastLinkSetting, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(FastLinkSetting item)
            {
                String typesTitles = join(((FastLinkSettingWithTitles)item).getMentionTypesTitles());
                return new SafeHtmlBuilder().appendEscaped(typesTitles).toSafeHtml();
            }
        };
        mentionTypesColumn.setFieldUpdater((int index, FastLinkSetting item, SafeHtml value) ->
        {
            placeController.goTo(new FastLinkSettingPlace(item.getCode()));
        });
        table.addColumn(mentionTypesColumn, messages.mentionTypes());
    }

    private void addProfilesColumn(DataTable<FastLinkSetting> table)
    {
        // Колонка "Профили"
        Column<FastLinkSetting, SafeHtml> profilesColumn = new Column<FastLinkSetting, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(FastLinkSetting item)
            {
                String profilesTitles = join(((FastLinkSettingWithTitles)item).getProfilesTitles());
                return new SafeHtmlBuilder().appendEscaped(profilesTitles).toSafeHtml();
            }
        };
        profilesColumn.setFieldUpdater((int index, FastLinkSetting item, SafeHtml value) ->
        {
            placeController.goTo(new FastLinkSettingPlace(item.getCode()));
        });
        table.addColumn(profilesColumn, messages.profiles());
    }

    private void addTitleColumn(DataTable<FastLinkSetting> table, WithArrowsCellTableStyle tableStyle)
    {
        // Колонка "Название"
        LinkToPlaceWithIndentColumn<FastLinkSetting> titleColumn =
                (LinkToPlaceWithIndentColumn<FastLinkSetting>)titleColumnFactory
                        .getColumn((FastLinkSetting item) ->
                        {
                            return new FastLinkSettingPlace(item.getCode());
                        });

        titleColumn.setStyleFunction((FastLinkSetting input) ->
        {
            SafeStylesBuilder sb = new SafeStylesBuilder();
            sb.appendTrustedString("font-weight: 800;");
            return sb.toSafeStyles();
        });
        titleColumn.setCellStyleNames(tableStyle.titleColumn());
        titleColumn.setIdProviderFunction(FastLinkSetting::getCode);

        titleColumn.setFieldUpdater((int index, FastLinkSetting item, SafeHtml value) ->
        {
            placeController.goTo(new FastLinkSettingPlace(item.getCode()));
        });
        Header<?> titleHeader = new TextHeader(cmessages.title());
        titleHeader.setHeaderStyleNames(tableStyle.titleColumn());
        table.addColumn(titleColumn, titleHeader);
    }

    private void addTool(String btn, String title, String cmd)
    {
        toolBar.add((ButtonPresenter)buttonFactory.create(btn, title, cmd, param));
    }

    private void updateDnDControllers()
    {
        NodeList<Element> rows = getTable().asWidget().getElement().getElementsByTagName("tr");
        for (FastLinkSettingsListDnDController group : dndGroupsMap.values())
        {
            group.getGroupIndexList().clear();
            group.updateElements(rows);
        }

        Map<String, Integer> itemIndexes = new HashMap<>();
        int curIndex = 0;
        for (FastLinkSetting item : getTable().getVisibleItems())
        {
            itemIndexes.put(item.getCode(), curIndex++);
        }

        updateDnDGroup(StringUtilities.EMPTY, settings, itemIndexes, rows);
        Set<String> parentCodes = Sets.<String> newHashSet(StringUtilities.EMPTY);

        for (String code : new HashSet<>(dndGroupsMap.keySet()))
        {
            if (!parentCodes.contains(code))
            {
                dndGroupsMap.remove(code);
                dndControllersMap.remove(code).destroy();
            }
        }
    }

    private void updateDnDGroup(String code, List<FastLinkSetting> children, Map<String, Integer> itemIndexes,
            NodeList<Element> allElements)
    {
        FastLinkSettingsListDnDController group = dndGroupsMap.get(code);
        if (null == group)
        {
            group = new FastLinkSettingsListDnDController();
            group.updateElements(allElements);
            dndGroupsMap.put(code, group);
            dndControllersMap.put(code, dndControllerFactory.create(group));
        }
        for (FastLinkSetting child : children)
        {
            group.getGroupIndexList().add(itemIndexes.get(child.getCode()));
        }
        dndControllersMap.get(code).update();
    }

}
