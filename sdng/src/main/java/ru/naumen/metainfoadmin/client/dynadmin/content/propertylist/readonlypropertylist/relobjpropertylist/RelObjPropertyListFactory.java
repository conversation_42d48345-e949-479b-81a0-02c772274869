package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.relobjpropertylist;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.factory.DefaultContentFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.shared.Constants.MetaClassProperties;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassResponse;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfoadmin.client.dynadmin.BasicUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Фабрика, возвращающая нужный ContentPresenter для контента
 * {@link ru.naumen.metainfo.shared.ui.RelObjPropertyList RelObjPropertyList}
 * <AUTHOR>
 */
public class RelObjPropertyListFactory extends DefaultContentFactory<RelObjPropertyList, UIContext>
{
    @Inject
    DispatchAsync dispatch;

    @Override
    public ContentPresenter<RelObjPropertyList, UIContext> create(Content content, Context context)
    {
        ContentPresenter<RelObjPropertyList, UIContext> presenter = create(content);
        init(presenter, content, context);
        return presenter;
    }

    protected void init(final ContentPresenter<RelObjPropertyList, UIContext> presenter, final Content content,
            final Context context)
    {

        final RelObjPropertyList list = (RelObjPropertyList)content;

        Attribute objectAttribute = context.getMetainfo().getAttribute(list.getAttrCode());
        ObjectAttributeType type = objectAttribute.getType().cast();

        dispatch.execute(new GetMetaClassAction(type.getRelatedMetaClass()), new BasicCallback<GetMetaClassResponse>()
        {
            @Override
            protected void handleSuccess(GetMetaClassResponse value)
            {
                UIContext uiContext = (UIContext)context;
                BasicUIContext propertyContext = new BasicUIContext(context, uiContext.getRootContentInfo(), uiContext
                        .isEditable());
                propertyContext.setMetainfo(value.getMetaClass());
                propertyContext.setContextProperty(MetaClassProperties.DISABLED_ATTRIBUTES,
                        value.getDisabledAttributes());
                presenter.init(list, propertyContext);
                presenter.bind();
            }
        });
    }
}
