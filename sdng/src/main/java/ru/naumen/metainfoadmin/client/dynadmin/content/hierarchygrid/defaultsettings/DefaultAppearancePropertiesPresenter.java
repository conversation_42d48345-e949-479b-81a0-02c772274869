package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.defaultsettings;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.content.property.PropertyGridDisplay;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.IRadioButtonGroup;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.shared.ui.HierarchyGridDefaultViewSettings;
import ru.naumen.metainfo.shared.ui.HierarchyGridDefaultViewSettings.HeaderAppearance;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.AdvlistDefaultPrsMessages;
import ru.naumen.metainfoadmin.shared.dynadmin.HierarchyItemSettingsContext;

/**
 * Представление свойств блока "Внешний вид по умолчанию"
 *
 * <AUTHOR>
 * @since 25.03.2021
 */
public class DefaultAppearancePropertiesPresenter extends BasicPresenter<PropertyGridDisplay>
{
    @Inject
    private AdvlistDefaultPrsMessages messages;

    @Inject
    @Named(PropertiesGinModule.RADIO_GROUP)
    private Property<String> headerAppearanceProperty;

    private HierarchyGridDefaultViewSettings settings;
    private HierarchyItemSettingsContext context;

    @Inject
    public DefaultAppearancePropertiesPresenter(PropertyGridDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(HierarchyGridDefaultViewSettings settings, HierarchyItemSettingsContext context)
    {
        this.settings = settings;
        this.context = context;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        headerAppearanceProperty.setValue(getHeaderAppearanceValue());
    }

    @Override
    protected void onBind()
    {
        headerAppearanceProperty.setCaption(messages.headerAppearancePropertyTitle());
        IRadioButtonGroup radioGroup = headerAppearanceProperty.getValueWidget();
        if (context.isShowNested())
        {
            radioGroup.addItem(HeaderAppearance.HIDE_ALL.name(),
                    messages.headerAppearanceHideAll());
            radioGroup.addItem(HeaderAppearance.HIDE_NESTED_ONLY.name(),
                    messages.headerAppearanceHideNestedOnly());
        }
        else
        {
            radioGroup.addItem(HeaderAppearance.HIDE_ALL.name(),
                    messages.headerAppearanceHide());
        }
        radioGroup.addItem(HeaderAppearance.NOT_HIDE.name(),
                messages.headerAppearanceNotHide());
        radioGroup.setValue(getHeaderAppearanceValue());
        getDisplay().add(headerAppearanceProperty, "headerAppearance");
        headerAppearanceProperty.addValueChangeHandler(
                event -> settings.setHeaderAppearance(HeaderAppearance.valueOf(event.getValue())));
    }

    private String getHeaderAppearanceValue()
    {
        HeaderAppearance headerAppearance = settings.getHeaderAppearance();
        if (!context.isShowNested()
            && headerAppearance == HeaderAppearance.HIDE_NESTED_ONLY)
        {
            return HeaderAppearance.NOT_HIDE.name();
        }
        return headerAppearance.name();
    }
}
