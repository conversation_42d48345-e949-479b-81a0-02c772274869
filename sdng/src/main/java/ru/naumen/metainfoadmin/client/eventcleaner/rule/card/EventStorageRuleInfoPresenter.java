package ru.naumen.metainfoadmin.client.eventcleaner.rule.card;

import static ru.naumen.admin.client.administration.AdministrationPresenter.EVENT_CLEANER_TAB_ID;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.admin.client.administration.AdministrationPlace;
import ru.naumen.admin.client.eventcleaner.EventCleanerSettingsMessages;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.FactoryParam.ValueSource;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertyUtils;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventStorageRule;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.wf.FakeState;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.eventcleaner.EventCleanerJobPresenterSettings;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.commands.EventStorageRuleCommandCode;

/**
 * Презентер свойств для правила хранения логов событий
 * <AUTHOR>
 * @since 15.07.2023
 */
public class EventStorageRuleInfoPresenter extends BasicPresenter<InfoDisplay>
{
    private class EventStorageRuleInfoToggleCommandParam extends CommandParam<DtObject, DtObject>
    {
        public EventStorageRuleInfoToggleCommandParam(AsyncCallback<DtObject> callback)
        {
            super(valueSource, callback);
        }
    }

    private class EventStorageRuleInfoDeleteCommandParam extends CommandParam<List<DtObject>, DtObject>
    {
        public EventStorageRuleInfoDeleteCommandParam(AsyncCallback<DtObject> callback)
        {
            super(valueSourceDelete, callback);
        }
    }

    private class EventStorageRuleInfoEditCommandParam extends CommandParam<DtObject, DtObject>
    {
        public EventStorageRuleInfoEditCommandParam(AsyncCallback<DtObject> callback)
        {
            super(valueSource, callback);
        }
    }

    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> events;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> metaClasses;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> states;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> storageTime;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> enabled;

    @Inject
    private CommonMessages cmessages;
    @Inject
    private EventCleanerSettingsMessages eventCleanerMessages;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private PlaceController placeController;
    @Inject
    protected MetainfoServiceAsync metainfoService;
    @Inject
    private EventCleanerJobPresenterSettings settings;

    private DtObject rule;
    private OnStartCallback<DtObject> refreshCallback;
    private final ToolBarDisplayMediator<DtObject> toolBar;
    private ButtonPresenter<DtObject> toggleButtonPresenter;

    private ValueSource<List<DtObject>> valueSourceDelete = new ValueSource<List<DtObject>>()
    {
        @Override
        public List<DtObject> getValue()
        {
            return Lists.newArrayList(rule);
        }
    };

    private ValueSource<DtObject> valueSource = new ValueSource<DtObject>()
    {
        @Override
        public DtObject getValue()
        {
            return rule;
        }
    };

    private OnStartCallback<DtObject> removeCallback = new SafeOnStartBasicCallback<DtObject>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            placeController.goTo(new AdministrationPlace(EVENT_CLEANER_TAB_ID));
        }
    };

    @Inject
    public EventStorageRuleInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<DtObject>(display.getToolBar());
    }

    public void init(OnStartCallback<DtObject> refreshCallback)
    {
        this.refreshCallback = refreshCallback;
    }

    public void setRule(DtObject rule)
    {
        this.rule = rule;
        refreshDisplay();
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(cmessages.properties());
        initToolBar();
        bindProperties();
        ensureDebugIds();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        title.setValue(rule.getTitle());
        code.setValue(rule.getUUID());
        // @formatter:off
        String eventsValue = ((Collection<DtObject>)rule.getProperty(EventStorageRule.EVENTS_CODE))
                .stream()
                .sorted(ITitled.COMPARATOR)
                .map(ITitled::getTitle)
                .collect(Collectors.joining(", "));
        // @formatter:on
        events.setValue(eventsValue);
        // @formatter:off
        List<ClassFqn> classes = ((Collection<MetaClassLite>)rule.getProperty(EventStorageRule.CLASSES_CODE))
                .stream()
                .map(MetaClassLite::getFqn)
                .collect(Collectors.toList());
        final Set<ClassFqn> fqns = new HashSet<>(classes);
        // @formatter:on
        fqns.addAll(Collections2.transform(classes, ClassFqn.CLASS_EXTRACTOR));
        metainfoService.getMetaClasses(fqns, new BasicCallback<List<MetaClassLite>>(getDisplay())
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> mcList)
            {
                String value = CommonUtils.getGroupedMetaClassTitles(classes,
                        mcList);
                metaClasses.setValue(value);
            }
        });

        prepareStates(rule);
        storageTime.setValue(Long.toString(rule.getProperty(EventStorageRule.STORAGE_TIME_CODE)));
        enabled.setValue(rule.getProperty(EventStorageRule.ENABLED_CODE));
        if (toggleButtonPresenter != null)
        {
            toggleButtonPresenter.setTitle(
                    isRuleEnabled() ? eventCleanerMessages.turnOff() : eventCleanerMessages.turnOn());
        }
        toolBar.refresh(rule);
    }

    private void bindProperties()
    {
        title.setCaption(cmessages.title());
        getDisplay().add(title);
        code.setCaption(cmessages.code());
        getDisplay().add(code);
        events.setCaption(eventCleanerMessages.eventStorageRuleEventTypes());
        getDisplay().add(events);
        metaClasses.setCaption(cmessages.objects());
        getDisplay().add(metaClasses);
        states.setCaption(eventCleanerMessages.eventStorageRuleState());
        getDisplay().add(states);
        PropertyUtils.addDescriptionHint(states, eventCleanerMessages.eventStorageRuleStateHint());
        storageTime.setCaption(eventCleanerMessages.eventStorageRuleStorageTime());
        getDisplay().add(storageTime);
        PropertyUtils.addDescriptionHint(storageTime, eventCleanerMessages.eventStorageRuleStorageTimeHint());
        enabled.setCaption(cmessages.enabled());
        getDisplay().add(enabled);
    }

    @SuppressWarnings("unchecked")
    protected void initToolBar()
    {
        if (settings.isToggleRule())
        {
            toggleButtonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(ButtonCode.SWITCH,
                    isRuleEnabled() ? eventCleanerMessages.turnOff() : eventCleanerMessages.turnOn(),
                    EventStorageRuleCommandCode.TOGGLE,
                    new EventStorageRuleInfoToggleCommandParam(refreshCallback));
            toolBar.add(toggleButtonPresenter);
        }
        if (settings.isEditRule())
        {
            ButtonPresenter<DtObject> buttonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(ButtonCode.EDIT,
                    cmessages.edit(), EventStorageRuleCommandCode.EDIT,
                    new EventStorageRuleInfoEditCommandParam(refreshCallback));
            toolBar.add(buttonPresenter);
        }
        if (settings.isDeleteRule())
        {
            ButtonPresenter<DtObject> buttonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(
                    ButtonCode.DELETE, cmessages.delete(),
                    EventStorageRuleCommandCode.DELETE, new EventStorageRuleInfoDeleteCommandParam(removeCallback));
            toolBar.add(buttonPresenter);
        }
        toolBar.bind();
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(events, "events");
        DebugIdBuilder.ensureDebugId(metaClasses, "metaClasses");
        DebugIdBuilder.ensureDebugId(states, "states");
        DebugIdBuilder.ensureDebugId(storageTime, "storageTime");
        DebugIdBuilder.ensureDebugId(enabled, "enabled");
    }

    private void prepareStates(DtObject rule)
    {
        Map<ClassFqn, List<String>> st = rule.getProperty(EventStorageRule.STATES);
        Collection<ClassFqn> fqns = st.keySet();
        metainfoService.getMetaClasses(fqns,
                new BasicCallback<List<MetaClassLite>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(List<MetaClassLite> mcList)
                    {
                        mcList.sort(ITitled.COMPARATOR);
                        metainfoService.getStates(fqns, new BasicCallback<Map<ClassFqn, ArrayList<FakeState>>>()
                        {
                            @Override
                            protected void handleSuccess(final Map<ClassFqn, ArrayList<FakeState>> fakeStates)
                            {
                                Map<String, List<String>> statesData = new LinkedHashMap<>();
                                for (MetaClassLite mc : mcList)
                                {
                                    List<String> codes = st.get(mc.getFqn());
                                    List<String> statusTitle = codes.stream()
                                            .map(c ->
                                            {
                                                ArrayList<FakeState> fsList = fakeStates.get(mc.getFqn());
                                                if (fsList != null)
                                                {
                                                    return findStateTitle(c, fsList);
                                                }
                                                return null;
                                            })
                                            .filter(Objects::nonNull)
                                            .sorted()
                                            .collect(Collectors.toList());
                                    statesData.put(mc.getTitle(), statusTitle);
                                }
                                states.setValue(prepareMetaClassStatuses(statesData));
                            }
                        });
                    }
                });
    }

    @Nullable
    private static String findStateTitle(String code, List<FakeState> states)
    {
        for (FakeState st : states)
        {
            if (st.getCode().equals(code))
            {
                return st.getTitle();
            }
        }
        return null;
    }

    private static String prepareMetaClassStatuses(Map<String, List<String>> stateData)
    {
        StringBuilder builder = new StringBuilder();
        int counter = 0;
        for (Map.Entry<String, List<String>> entry : stateData.entrySet())
        {
            String classTitle = entry.getKey();
            List<String> statesTitle = entry.getValue();
            counter++;

            if (counter > 1)
            {
                builder.append("; ");
            }

            builder.append('[').append(classTitle).append(']');
            if (!statesTitle.isEmpty())
            {
                builder.append(": ");
                for (int i = 0; i < statesTitle.size(); i++)
                {
                    builder.append(statesTitle.get(i));
                    if (i < statesTitle.size() - 1)
                    {
                        builder.append(", ");
                    }
                }
            }
        }
        return builder.toString();
    }

    private boolean isRuleEnabled()
    {
        return rule != null && Boolean.TRUE.equals(rule.getProperty(EventStorageRule.ENABLED_CODE));
    }
}