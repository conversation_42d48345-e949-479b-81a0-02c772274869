package ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms;

import java.util.HashMap;

import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.core.shared.escalation.EscalationSchemeLevelCondition.EscalationSchemeLevelConditionCode;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinjector.EscalationSchemeLevelFormAggregatedFactoryFactory;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinjector.EscalationSchemeLevelFormFactory;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.delegates.EscalationSchemeLevelFormsDelegatesGinjector;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

/**
 * <AUTHOR>
 * @since 22.08.2012
 *
 */
public class EscalationSchemeLevelFormsGinModule extends AbstractGinModule
{
    public static class EscalationSchemeLevelConditionSelectorProvider implements Provider<HashMap<String, String>>
    {
        @Override
        public HashMap<String, String> get()
        {
            HashMap<String, String> result = new HashMap<>();
            result.put(EscalationSchemeLevelConditionCode.TIME_EXCEEDED, EscalationSchemeLevelPropertyCode.VALUE_DTI);
            result.put(EscalationSchemeLevelConditionCode.TIME_PART_EXCEEDED,
                    EscalationSchemeLevelPropertyCode.VALUE_PERCENT);
            return result;
        }
    }

    public interface EscalationSchemeLevelContextValueCode
    {
        String LEVEL = "level";
        String ESCALATION_SCHEME = "escalationScheme";
    }

    public static class EscalationSchemeLevelPropertyCode
    {
        private EscalationSchemeLevelPropertyCode()
        {
        }

        public static final String CONDITION = "condition";
        public static final String VALUE = "value";
        public static final String VALUE_PERCENT = "valuePercent";
        public static final String VALUE_DTI = "valueDTI";
        public static final String ACTION = "action";
        public static final String EXEC_ACTION = "execAction";
        public static final String SETTINGS_SET = "settingsSet";
    }

    public static final String ESCALATION_SCHEME_LEVEL_CONDITION_SELECTOR = "escalationSchemeLevelConditionSelector";

    @Override
    protected void configure()
    {
        bind(EscalationSchemeLevelFormsDelegatesGinjector.class).to(EscalationSchemeLevelFormsGinjector.class).in(
                Singleton.class);
        configureCommon();
        configureAddForm();
        configureEditForm();
    }

    private void configureAddForm()
    {
        //@formatter:off
        bind(new TypeLiteral<EscalationSchemeLevelFormMessages<ObjectFormAdd>>(){})
            .to(AddEscalationSchemeLevelFormMessages.class).in(Singleton.class);
        
        bind(new TypeLiteral<EscalationSchemeLevelFormPropertyControllerFactorySyncImpl      <ObjectFormAdd>>(){});
        install(new GinFactoryModuleBuilder()
            .implement(AddEscalationSchemeLevelForm.class, AddEscalationSchemeLevelForm.class)
            .build(new TypeLiteral<EscalationSchemeLevelFormFactory<AddEscalationSchemeLevelForm>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<PropertyControllerFactory<EscalationSchemeLevel, ObjectFormAdd>>(){}, new TypeLiteral<EscalationSchemeLevelFormPropertyControllerAggregatedImpl<ObjectFormAdd>>(){})
            .build(new TypeLiteral<EscalationSchemeLevelFormAggregatedFactoryFactory<ObjectFormAdd>>(){}));
        install(PropertyControllerGinModule.create(EscalationSchemeLevel.class, ObjectFormAdd.class)
            .setPropertyControllerFactory(new TypeLiteral<EscalationSchemeLevelFormPropertyControllerSelector<ObjectFormAdd>>(){})
            .setPropertyParametersDescriptorFactory(new TypeLiteral<EscalationSchemeLevelFormPropertyParametersDescriptorFactoryImpl<ObjectFormAdd>>(){}));
        //@formatter:on
    }

    private void configureCommon()
    {
        //@formatter:off       
        install(Gin.bindSingleton(
                new TypeLiteral<EscalationSchemeLevelPropertyContainerAfterBindHandler>(){},         
                     new TypeLiteral<EscalationSchemeLevelPropertyContainerAfterBindHandlerImpl>(){}));
        
        bind(new TypeLiteral<HashMap<String, String>>(){})
            .annotatedWith(Names.named(ESCALATION_SCHEME_LEVEL_CONDITION_SELECTOR))
            .toProvider(EscalationSchemeLevelConditionSelectorProvider.class)
            .in(Singleton.class);
        //@formatter:on
    }

    private void configureEditForm()
    {
        //@formatter:off
        bind(new TypeLiteral<EscalationSchemeLevelFormMessages<ObjectFormEdit>>(){})
            .to(EditEscalationSchemeLevelFormMessages.class).in(Singleton.class);
        bind(new TypeLiteral<EscalationSchemeLevelFormPropertyControllerFactorySyncImpl      <ObjectFormEdit>>(){});
        install(new GinFactoryModuleBuilder()
            .implement(EditEscalationSchemeLevelForm.class, EditEscalationSchemeLevelForm.class)
            .build(new TypeLiteral<EscalationSchemeLevelFormFactory<EditEscalationSchemeLevelForm>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<PropertyControllerFactory<EscalationSchemeLevel, ObjectFormEdit>>(){}, new TypeLiteral<EscalationSchemeLevelFormPropertyControllerAggregatedImpl<ObjectFormEdit>>(){})
            .build(new TypeLiteral<EscalationSchemeLevelFormAggregatedFactoryFactory<ObjectFormEdit>>(){}));
        install(PropertyControllerGinModule.create(EscalationSchemeLevel.class, ObjectFormEdit.class)
                    .setPropertyControllerFactory(new TypeLiteral<EscalationSchemeLevelFormPropertyControllerSelector<ObjectFormEdit>>(){})
                    .setPropertyParametersDescriptorFactory(new TypeLiteral<EscalationSchemeLevelFormPropertyParametersDescriptorFactoryImpl<ObjectFormEdit>>(){}));
        //@formatter:on
    }
}
