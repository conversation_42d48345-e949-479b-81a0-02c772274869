package ru.naumen.metainfoadmin.client.interfaze.interfacetab.command;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.Dialogs.DialogResult;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataResponse;
import ru.naumen.core.shared.interfacesettings.dispatch.DeleteThemeAction;
import ru.naumen.core.shared.personalsettings.ThemeClient;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContextChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.ThemeMessages;

/**
 * <AUTHOR>
 * @since 02 авг. 2016 г.
 *
 */
public class DeleteThemeCommand extends BaseCommandImpl<ThemeClient, InterfaceSettingsContext>
{
    public static final String ID = "deleteThemeCommand";

    @Inject
    private DispatchAsync dispatch;
    @Inject
    private Dialogs dialogs;
    @Inject
    private ThemeMessages messages;
    @Inject
    private EventBus eventBus;
    @Inject
    private CommonMessages cmessages;

    private final InterfaceSettingsContext context;

    @Inject
    public DeleteThemeCommand(@Assisted ThemeCommandParam param)
    {
        super(param);
        this.context = param.getContext();
    }

    @Override
    public void execute(final CommandParam<ThemeClient, InterfaceSettingsContext> param)
    {
        if (ObjectUtils.equals(context.getSettings().getThemeOperator(), param.getValue().getCode()))
        {
            dialogs.error(messages.cantBeDeleted(param.getValue().getTitle()));
            return;
        }
        dialogs.question(cmessages.confirmDelete(),
                cmessages.confirmDeleteQuestion(messages.theme(), param.getValue().getTitle()), new DialogCallback()
                {
                    @Override
                    public void handleSuccess(final DialogResult result)
                    {
                        if (Dialogs.Buttons.YES.equals(result.getButtons()))
                        {
                            dispatch.execute(new DeleteThemeAction(param.getValue().getCode()),
                                    new BasicCallback<GetInterfaceTabDataResponse>()
                                    {
                                        @Override
                                        protected void handleSuccess(GetInterfaceTabDataResponse response)
                                        {
                                            result.getWidget().hide();
                                            eventBus.fireEvent(new InterfaceSettingsContextChangedEvent(response));
                                        }
                                    });
                        }
                        else
                        {
                            result.getWidget().hide();
                        }
                    }
                });

    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof ThemeClient))
        {
            return true;
        }

        ThemeClient item = (ThemeClient)input;
        return !item.isSystem();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DEL;
    }

}
