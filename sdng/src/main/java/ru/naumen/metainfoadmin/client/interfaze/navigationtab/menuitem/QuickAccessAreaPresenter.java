package ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem;

import java.util.Map;
import java.util.Optional;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.name.Named;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.GetNavigationSettingsAction;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelAreaSettingsDTO;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsPlace;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationTabSettingsGinModule;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.QuickAccessAreaPlace;

/**
 * Презентер области панели быстрого доступа
 * <AUTHOR>
 * @since 21.09.2020
 */
public class QuickAccessAreaPresenter extends AdminTabPresenter<QuickAccessAreaPlace>
{
    private final NavigationSettingsMessages messages;
    private final QuickAccessTileAreaAttributesPresenter attributesPresenter;
    private final DispatchAsync dispatch;
    private final Map<String, String> areaTitles;

    private QuickAccessPanelAreaSettingsDTO areaSettingsDTO;

    @Inject
    public QuickAccessAreaPresenter(AdminTabDisplay display, EventBus eventBus, NavigationSettingsMessages messages,
            @Named(NavigationTabSettingsGinModule.QUICK_ACCESS_AREA_TITLES) Map<String, String> areaTitles,
            QuickAccessTileAreaAttributesPresenter attributesPresenter,
            DispatchAsync dispatch)
    {
        super(display, eventBus);
        this.messages = messages;
        this.attributesPresenter = attributesPresenter;
        this.areaTitles = areaTitles;
        this.dispatch = dispatch;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        if (areaSettingsDTO != null)
        {
            getDisplay().setTitle(areaTitles.get(areaSettingsDTO.getCode()));
        }
    }

    @Override
    public void init(QuickAccessAreaPlace place)
    {
        super.init(place);
        if (place.getArea() != null)
        {
            areaSettingsDTO = place.getArea();
        }
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        prevPageLinkPresenter.bind(messages.toTopMenuElements(), new InterfaceSettingsPlace("navigation"));
        if (areaSettingsDTO == null)
        {
            dispatch.execute(new GetNavigationSettingsAction(),
                    new BasicCallback<SimpleResult<DtoContainer<NavigationSettings>>>(getDisplay())
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<DtoContainer<NavigationSettings>> response)
                        {
                            super.handleSuccess(response);
                            Optional<QuickAccessPanelAreaSettingsDTO> areaOpt =
                                    response.get().get()
                                            .getQuickAccessPanelSettings()
                                            .getAreas()
                                            .stream()
                                            .filter(area -> getPlace().getCode().equals(area.getCode()))
                                            .findAny();
                            areaSettingsDTO = areaOpt.orElse(null);
                            initAttributes();
                            refreshDisplay();
                        }
                    });
        }
        initAttributes();
    }

    private void initAttributes()
    {
        attributesPresenter.init(areaSettingsDTO);
        addContent(attributesPresenter, "attrs");
        attributesPresenter.revealDisplay();
    }
}