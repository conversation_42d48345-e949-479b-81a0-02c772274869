package ru.naumen.metainfoadmin.client.interfaze.interfacetab.command;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.ui2.logo.EditUI2LogoSettingsCommand;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.ui2.logo.Ui2LogoCommandParam;

/**
 * Команды действий над интерфейсом системы
 *
 * <AUTHOR>
 * @since 18.07.16
 */
public class InterfaceSettingsCommandFactoryInitializer
{
    //@formatter:off
    @Inject
    public InterfaceSettingsCommandFactoryInitializer(
            CommandFactory factory,
            CommandProvider<EnableThemeCommand, ThemeCommandParam> enableThemeCommandProvider,
            CommandProvider<DisableThemeCommand, ThemeCommandParam> disableThemeCommandProvider,
            CommandProvider<EditThemeCommand, ThemeCommandParam> editThemeCommandProvider,
            CommandProvider<EditDefaultThemeSettingsCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>> editDefaultThemeSettingsCommandProvider,
            CommandProvider<EditLanguageSettingsCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>> editLanguageSettingsCommandProvider,
            CommandProvider<EditTabHeaderSettingsCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>> editTabHeadeSettingsCommandProvider,
            CommandProvider<AddThemeCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>> addThemeCommandProvider,
            CommandProvider<DeleteThemeCommand, ThemeCommandParam> deleteThemeCommandProvider,
            CommandProvider<ExportThemePropertiesCommand, ThemeCommandParam> exportThemePropertiesCommandProvider,
            CommandProvider<EnableCustomLoginPageCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>> enableCustomLoginPageCommand,
            CommandProvider<DisableCustomLoginPageCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>> disableCustomLoginPageCommand,
            CommandProvider<TestCustomLoginPageCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>> previewCustomLoginPageCommand,
            CommandProvider<EditContentSettingsCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>> editContentInternalScrollSettingsCommandProvider,
            CommandProvider<EditThemeBaseSettingsCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>> editThemeBaseSettingsCommandProvider,
            CommandProvider<EditUI2LogoSettingsCommand, CommandParam<Ui2LogoCommandParam, Ui2LogoCommandParam>> editUI2LogoSettingsCommand)
    {
        //@formatter:on
        factory.register(EnableThemeCommand.ID, enableThemeCommandProvider);
        factory.register(DisableThemeCommand.ID, disableThemeCommandProvider);
        factory.register(EditThemeCommand.ID, editThemeCommandProvider);
        factory.register(EditDefaultThemeSettingsCommand.ID, editDefaultThemeSettingsCommandProvider);
        factory.register(EditLanguageSettingsCommand.ID, editLanguageSettingsCommandProvider);
        factory.register(EditTabHeaderSettingsCommand.ID, editTabHeadeSettingsCommandProvider);
        factory.register(AddThemeCommand.ID, addThemeCommandProvider);
        factory.register(DeleteThemeCommand.ID, deleteThemeCommandProvider);
        factory.register(ExportThemePropertiesCommand.ID, exportThemePropertiesCommandProvider);
        factory.register(EnableCustomLoginPageCommand.ID, enableCustomLoginPageCommand);
        factory.register(DisableCustomLoginPageCommand.ID, disableCustomLoginPageCommand);
        factory.register(TestCustomLoginPageCommand.ID, previewCustomLoginPageCommand);
        factory.register(EditContentSettingsCommand.ID, editContentInternalScrollSettingsCommandProvider);
        factory.register(EditThemeBaseSettingsCommand.ID, editThemeBaseSettingsCommandProvider);
        factory.register(EditUI2LogoSettingsCommand.ID, editUI2LogoSettingsCommand);
    }
}
