package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.HAS_DEFAULT_VALUE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPOSITE;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.LabelProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 11.10.2012
 *
 */
public class DefaultValueLabelRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, String, LabelProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, LabelProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean hasDefault = Boolean.TRUE.equals(context.getContextValues().getProperty(HAS_DEFAULT_VALUE));
        boolean composite = Boolean.TRUE.equals(context.getPropertyValues().getProperty(COMPOSITE));

        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        String attrCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE);
        if (SuperUser.FQN.isSameClass(metaClass.getFqn())
            && (SuperUser.LOGIN.equals(attrCode) || SuperUser.PASSWORD.equals(attrCode)))
        {
            callback.onSuccess(false);
            return;
        }

        callback.onSuccess(hasDefault && !composite);
    }
}
