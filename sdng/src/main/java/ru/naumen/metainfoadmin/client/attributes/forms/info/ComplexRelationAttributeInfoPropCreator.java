package ru.naumen.metainfoadmin.client.attributes.forms.info;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.dispatch2.AttributeGroupInfo;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosAction;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosResponse;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeDescription;
import ru.naumen.metainfo.shared.elements.ComplexRelationType;

/**
 * Создает {@link Property} для отображения информации о
 * сложной форме добавления связи на модальной форме свойств атрибута
 *
 * <AUTHOR>
 * @since 30 июл. 2018 г.
 */
public class ComplexRelationAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Override
    protected void createInt(String code)
    {
        if (!propertyValues.hasProperty(code) || Boolean.FALSE.equals(propertyValues.getProperty(code)))
        {
            return;
        }
        String attrCode = attribute.getType().getCode();
        switch (attrCode)
        {
            case BOLinksAttributeType.CODE:
            case ObjectAttributeType.CODE:
            case BackLinkAttributeType.CODE:
                bindPropertyForComplexRelation(code);
                bindAttributeGroupForLinksTypes(code);
                bindPropertyForStructure(code);
                break;
            case ru.naumen.metainfo.shared.Constants.AggregateAttributeType.CODE:
            case ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType.CODE:
                bindPropertyForComplexRelation(code);
                bindAttributeGroupForAggrType(code);
                break;
            default:
                break;
        }
    }

    private void bindAttributeGroupForAggrType(String code)
    {
        boolean isFlatList = ComplexRelationType.FLAT.getCode().equals(propertyValues.getProperty(code));
        if (!isFlatList)
        {
            return;
        }
        AggregateAttributeType type = attribute.getType().cast();
        List<ClassFqn> aggrClassesFqns = type.getAttributes().stream()
                .map(AttributeDescription::getReferenceMetaClass)
                .collect(Collectors.toList());
        GetAttributeGroupInfosAction action = new GetAttributeGroupInfosAction(aggrClassesFqns);
        dispatch.execute(action, new BasicCallback<GetAttributeGroupInfosResponse>(rs)
        {
            @Override
            protected void handleSuccess(GetAttributeGroupInfosResponse response)
            {
                bindPropertyForGroup(OU.FQN, response, COMPLEX_OU_ATTR_GROUP);
                bindPropertyForGroup(Team.FQN, response, COMPLEX_TEAM_ATTR_GROUP);
                bindPropertyForGroup(Employee.FQN, response, COMPLEX_EMPLOYEE_ATTR_GROUP);
            }
        });
    }

    private void bindAttributeGroupForLinksTypes(String code)
    {
        boolean isFlatList = ComplexRelationType.FLAT.getCode().equals(propertyValues.getProperty(code));
        if (!isFlatList)
        {
            return;
        }
        String fqnAsString = attribute.getType().getProperty(ObjectAttributeType.METACLASS_FQN);
        GetAttributeGroupInfosAction action = new GetAttributeGroupInfosAction(Lists.newArrayList(ClassFqn.parse(
                fqnAsString)));
        dispatch.execute(action, new BasicCallback<GetAttributeGroupInfosResponse>(rs)
        {
            @Override
            protected void handleSuccess(GetAttributeGroupInfosResponse response)
            {
                bindPropertyForGroup(ClassFqn.parse(fqnAsString), response, COMPLEX_RELATION_ATTR_GROUP);
            }
        });
    }

    private void bindPropertyForGroup(ClassFqn fqn, GetAttributeGroupInfosResponse response, String propertyCode)
    {
        List<AttributeGroupInfo> groups = (response.getGroupInfos().get(fqn) == null)
                ? java.util.Collections.emptyList()
                : response.getGroupInfos().get(fqn);
        Optional<AttributeGroupInfo> group = groups.stream().filter(value -> value.getCode().equals(
                propertyValues.getProperty(propertyCode))).findAny();
        group.ifPresent((value) -> createProperty(propertyCode, value.getTitle()));
    }

    private void bindPropertyForComplexRelation(String code)
    {
        String complexRelationTypeCode = Objects.requireNonNull(propertyValues.getProperty(code))
                .toString()
                .toLowerCase();
        createProperty(code, messages.complexRelationType(complexRelationTypeCode));
    }

    private void bindPropertyForStructure(String code)
    {
        boolean isHierarchy = ComplexRelationType.HIERARCHY.getCode().equals(propertyValues.getProperty(code));
        if (!isHierarchy)
        {
            return;
        }
        bindStructuredObjectsViewProperty(COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW);
    }
}
