package ru.naumen.metainfoadmin.client.attributes.columns;

import com.google.gwt.dom.client.Element;

import ru.naumen.core.client.widgets.grouplist.CellValueExtractor;

/**
 * {@link CellValueExtractor}, возвращающий весь текст (без html-разметки) в ячейке
 *
 * <AUTHOR>
 * @since 28 сент. 2018 г.
 *
 */
public class InnerTextCellValueExtractor implements CellValueExtractor<String>
{
    @Override
    public String extractValue(Element td)
    {
        return td.getInnerText();
    }
}
