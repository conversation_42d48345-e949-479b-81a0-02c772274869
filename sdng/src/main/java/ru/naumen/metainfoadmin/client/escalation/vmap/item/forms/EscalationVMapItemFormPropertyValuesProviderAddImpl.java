/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap.VMapItemFormPropertyValuesProviderAddImpl;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationVMapItemFormPropertyValuesProviderAddImpl extends
        VMapItemFormPropertyValuesProviderAddImpl<EscalationValueMapItemFormContext>
{
    @Override
    public IProperties create(EscalationValueMapItemFormContext context)
    {
        IProperties result = super.create(context);
        result.removeProperty(ValueMapCatalogItem.TARGET_ATTRS);
        return result;
    }
}
