package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import jakarta.inject.Inject;

import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;

import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 21.08.2012
 *
 */
public class EscalationSchemeFormApplyCallback<F extends ObjectForm> extends
        SafeBasicCallback<SimpleResult<DtoContainer<EscalationScheme>>>
{
    protected final EscalationSchemeForm<F> form;

    @Inject
    public EscalationSchemeFormApplyCallback(@Assisted EscalationSchemeForm<F> form)
    {
        super(form.getDisplay());
        this.form = form;
    }

    @Override
    protected void handleSuccess(SimpleResult<DtoContainer<EscalationScheme>> response)
    {
        form.unbind();
    }
}
