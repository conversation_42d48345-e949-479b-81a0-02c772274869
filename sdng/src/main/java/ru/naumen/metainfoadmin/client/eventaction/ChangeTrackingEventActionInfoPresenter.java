package ru.naumen.metainfoadmin.client.eventaction;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.TextProperty;
import ru.naumen.metainfo.client.eventaction.EventActionConstants;
import ru.naumen.metainfo.shared.eventaction.tracking.ChangeTrackingEventAction;
import ru.naumen.metainfo.shared.eventaction.tracking.TrackingUiAction;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;

/**
 * Представление свойств действия «Отслеживание изменений».
 * <AUTHOR>
 * @since Mar 24, 2022
 */
public class ChangeTrackingEventActionInfoPresenter
        extends EventActionWithRecipientsInfoPresenter<ChangeTrackingEventAction, TextProperty>
{
    private PropertyRegistration<String> messagePR;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> useDefaultMessage;
    private PropertyRegistration<Boolean> useDefaultMessagePR;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> uiAction;
    private PropertyRegistration<String> uiActionPR;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> refreshArea;
    private PropertyRegistration<String> refreshAreaPR;

    @Inject
    private EventActionConstants eventActionConstants;

    @Inject
    public ChangeTrackingEventActionInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void addActionPropertiesAfter()
    {
        useDefaultMessagePR = addProperty(messages.useDefaultMessage(), useDefaultMessage);
        uiActionPR = addProperty(messages.actionInWeb(), uiAction);
        addScript();
    }

    @Override
    public void refreshDisplay()
    {
        refreshMessage();
        refreshRefreshArea();

        super.refreshDisplay();
    }

    /**
     * Обновить свойство "Область обновления"
     */
    private void refreshRefreshArea()
    {
        boolean allowRefresh = null != eventAction && eventAction.getObject().getAction() != null
                               && getAction().getUiAction() != TrackingUiAction.MESSAGE;
        if (allowRefresh && null == refreshAreaPR)
        {
            refreshArea.setCaption(messages.updateArea());
            refreshAreaPR = getDisplay().addPropertyAfter(refreshArea, uiActionPR);
        }
        else if (!allowRefresh && null != refreshAreaPR)
        {
            refreshAreaPR.unregister();
            refreshAreaPR = null;
        }
    }

    /**
     * Обновить свойство "Текст уведомления"
     */
    private void refreshMessage()
    {
        boolean showMessage = null != eventAction && eventAction.getObject().getAction() != null
                              && !getAction().isUseDefaultMessage();
        if (showMessage && null == messagePR)
        {
            message.setCaption(messages.pushContent());
            messagePR = getDisplay().addPropertyAfter(message, useDefaultMessagePR);
        }
        else if (!showMessage && null != messagePR)
        {
            messagePR.unregister();
            messagePR = null;
        }
    }

    @Override
    protected void setPropertiesValues()
    {
        super.setPropertiesValues();

        if (!isEventActionTypeDefined())
        {
            return;
        }

        ChangeTrackingEventAction action = getAction();
        useDefaultMessage.setValue(action.isUseDefaultMessage());
        uiAction.setValue(eventActionConstants.trackingUiActions().get(action.getUiAction().getName()));
        refreshArea.setValue(eventActionConstants.trackingRefreshAreas().get(action.getPageRefreshArea().getName()));
    }

    @Override
    protected void ensureDebugIds()
    {
        super.ensureDebugIds();
        useDefaultMessage.ensureDebugId("useDefaultMessage");
        uiAction.ensureDebugId("uiAction");
        refreshArea.ensureDebugId("refreshArea");
    }
}
