package ru.naumen.metainfoadmin.client.scheduler;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Predicate;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;

import jakarta.inject.Inject;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.columns.HasEnabledColumn;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumn;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.events.SchedulerTaskUpdatedEvent;
import ru.naumen.metainfo.client.events.SchedulerTaskUpdatedHandler;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfoadmin.client.AdminListPresenterBase;
import ru.naumen.metainfoadmin.client.scheduler.command.SchedulerCommandCode;
import ru.naumen.metainfoadmin.client.scheduler.command.TriggerCommandParam;

/**
 * Презентер с таблицей расписания выполнения задачи планировщика
 *
 * oaleksandrova
 */
public class TriggersPresenter extends AdminListPresenterBase<DtoContainer<Trigger>> implements SchedulerTaskUpdatedHandler
{
    @Inject
    private AdminMetainfoServiceAsync metainfoService;
    @Inject
    private Formatters formatters;
    @Inject
    private SchedulerTaskMessages messages;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private LinkToPlaceColumn<DtoContainer<Trigger>> titleColumn;
    @Inject
    public FontIconFactory<DtoContainer<Trigger>> iconFactory;

    private DtoContainer<SchedulerTask> schTask;

    @Inject
    public TriggersPresenter(TriggersDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @SuppressWarnings("rawtypes")
    public void init(DtoContainer<SchedulerTask> schTask, OnStartCallback refreshCallback)
    {
        this.schTask = schTask;
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void onSchedulerTaskUpdated(SchedulerTaskUpdatedEvent e)
    {
        this.schTask = e.getSchedulerTask();
    }

    @Override
    public void refreshDisplay()
    {
        metainfoService.getTriggersTasks(schTask.get().getCode(),
                new BasicCallback<List<DtoContainer<Trigger>>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(List<DtoContainer<Trigger>> containers)
                    {
                        List<DtoContainer<Trigger>> triggers = new ArrayList<>();
                        for (DtoContainer<Trigger> container : containers)
                        {
                            triggers.add(container);
                            permissions.put(container.get().getCode(),
                                    container.getProperty(SettingsSet.ADMIN_PERMISSIONS));
                        }

                        // Здесь нужен стоп процессинг, иначе пропадут иконки в строках
                        getDisplay().stopProcessing();
                        getTable().setRowData(0, triggers);
                        getTable().setRowCount(triggers.size());
                    }
                });
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(SchedulerTaskUpdatedEvent.getType(), this));
        getDisplay().setCaption(messages.triggers());
        super.onBind();
    }

    @Override
    protected List<Object> getRowActions()
    {
        return Lists.newArrayList(
                new String[] { SchedulerCommandCode.ON_TRIGGER, SchedulerCommandCode.OFF_TRIGGER },
                SchedulerCommandCode.EDIT_TRIGGER,
                SchedulerCommandCode.DELETE_TRIGGER);
    }

    @Override
    protected List<ButtonPresenter<?>> getListActions()
    {
        return Lists.newArrayList(buttonFactory.create(
                ButtonCode.ADD, cmessages.add(), SchedulerCommandCode.ADD_TRIGGER, getCommandParam()));
    }

    @Override
    protected CommandParam<DtoContainer<Trigger>, ?> getCommandParam()
    {
        return new TriggerCommandParam(null, refreshCallback, schTask.get());
    }

    @Override
    protected List<Pair<String, Column<DtoContainer<Trigger>, ?>>> getColumns()
    {
        Column<DtoContainer<Trigger>, ?> planDateColumn = new TextColumn<DtoContainer<Trigger>>()
        {
            @Override
            public String getValue(DtoContainer<Trigger> object)
            {
                Date planExecutionDate = object.get().getPlanExecutionDate();
                return null == planExecutionDate ? StringUtilities.EMPTY : formatters.formatDateTime(planExecutionDate);
            }
        };

        Column<DtoContainer<Trigger>, FontIconDisplay<DtoContainer<Trigger>>> statusColumn =
                new HasEnabledColumn<DtoContainer<Trigger>>(
                        iconFactory)
                {
                    @Override
                    protected boolean isEnabled(DtoContainer<Trigger> value)
                    {
                        return super.isEnabled(value) && Boolean.TRUE.equals(schTask.getProperty(
                                Constants.Tag.IS_ELEMENT_ENABLED));
                    }
                };

        return Lists.newArrayList(
                new Pair<>(cmessages.title(), titleColumn),
                new Pair<>(messages.planExecutionDate(), planDateColumn),
                new Pair<>(messages.isOn(), statusColumn));
    }

    @Override
    protected Predicate<DtoContainer<Trigger>> getVisibilityCondition(String command)
    {
        PermissionType permissionType = SchedulerCommandCode.DELETE_TRIGGER.equals(command)
                ? PermissionType.DELETE
                : PermissionType.EDIT;
        return AdminPermissionUtils.createPermissionPredicate(permissionType, permissions);
    }
}
