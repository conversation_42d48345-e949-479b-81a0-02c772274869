package ru.naumen.metainfoadmin.client.structuredobjectsviews.items.forms;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.dispatch.EditStructuredObjectsViewItemAction;

/**
 * Форма редактирования элемента структуры
 * <AUTHOR>
 * @since 24.10.2019
 *
 */
public class EditStructuredObjectsViewItemFormPresenter extends StructuredObjectsViewItemFormPresenterImpl
{
    @Inject
    public EditStructuredObjectsViewItemFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        super.onApply();
        service.execute(new EditStructuredObjectsViewItemAction(structuredObjectsView.getUUID(),
                        structuredObjectsViewItem),
                new BasicCallback<SimpleResult<DtObject>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtObject> value)
                    {
                        unbind();
                        refreshCallback.onSuccess(value.get());
                    }
                });
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(structuredObjectsViewsMessages.editingStructuredObjectsViewItem());
        code.setDisable();
        getDisplay().display();
    }
}
