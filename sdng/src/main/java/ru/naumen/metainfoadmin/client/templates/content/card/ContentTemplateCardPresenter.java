package ru.naumen.metainfoadmin.client.templates.content.card;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TEMPLATES;

import java.util.Objects;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.templates.content.ContentTemplate;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.dynadmin.BasicUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateServiceAsync;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplatesPlace;

/**
 * Представление карточки шаблона контента.
 * <AUTHOR>
 * @since Mar 23, 2021
 */
public class ContentTemplateCardPresenter extends AdminTabPresenter<ContentTemplatePlace>
{
    @Inject
    private ContentTemplateInfoPresenter infoPresenter;
    @Inject
    private ContentTemplateServiceAsync contentTemplateServiceAsync;
    @Inject
    private CommonMessages commonMessages;
    @Inject
    private AdminContentFactory contentFactory;

    private DtObject contentTemplateDto;
    private ContentPresenter<FlowContent, UIContext> contentPresenter;

    private final OnStartCallback<DtObject> refreshCallback = new OnStartBasicCallback<DtObject>()
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            contentTemplateDto = value;
            refreshDisplay();
        }
    };

    @Inject
    public ContentTemplateCardPresenter(AdminTabDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void refreshDisplay()
    {
        refreshContentPreview();
        super.refreshDisplay();
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        contentTemplateServiceAsync.loadContentTemplate(getPlace().getCode(), new BasicCallback<DtObject>(readyState)
        {
            @Override
            protected void handleSuccess(DtObject value)
            {
                contentTemplateDto = value;
            }
        });
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        prevPageLinkPresenter.bind(commonMessages.back(), ContentTemplatesPlace.INSTANCE);

        getDisplay().setTitle(contentTemplateDto.getTitle());
        infoPresenter.init(contentTemplateDto, refreshCallback);
        addContent(infoPresenter, "contentTemplateInfo");

        refreshContentPreview();
        registerHandler(eventBus.addHandler(RefreshContentEvent.getType(), event -> reload()));
    }

    @Override
    protected void onUnbind()
    {
        if (null != contentPresenter)
        {
            contentPresenter.unbind();
        }
        infoPresenter.unbind();
        super.onUnbind();
    }

    private void refreshContentPreview()
    {
        if (null != contentPresenter)
        {
            contentPresenter.unbind();
        }
        ContentTemplate template = contentTemplateDto.getProperty(FakeMetaClassesConstants.ContentTemplate.TEMPLATE);
        FlowContent content = Objects.requireNonNull(template).getTemplate();
        MetaClass contextMetaClass = Objects.requireNonNull(contentTemplateDto.getProperty(
                FakeMetaClassesConstants.ContentTemplate.CONTEXT_METACLASS));
        BasicUIContext parentContext = new BasicUIContext(contextMetaClass, eventBus);
        ContentInfo contentInfo = new ContentInfo(contextMetaClass.getFqn(), UI.CONTENT_TEMPLATE, content);
        BasicUIContext uiContext = new BasicUIContext(parentContext, contentInfo, eventBus, true);
        PermissionHolder permissions = contentTemplateDto.getProperty(
                FakeMetaClassesConstants.ContentTemplate.CONTENT_PERMISSIONS, new PermissionHolder());
        uiContext.setPermissions(permissions);
        uiContext.setTemplateCode(contentTemplateDto.getUUID());
        contentPresenter = contentFactory.build(content, uiContext);
        contentPresenter.getDisplay().asWidget().addStyleName(
                AdminWidgetResources.INSTANCE.contentLayout().editableBlockHover());
        addContent(contentPresenter, "contentTemplatePreview");
    }

    private void reload()
    {
        SuccessReadyState readyState = new SuccessReadyState(this);
        loadData(readyState);
        readyState.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                infoPresenter.setContentTemplateDto(contentTemplateDto);
                refreshDisplay();
            }
        });
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return TEMPLATES;
    }
}
