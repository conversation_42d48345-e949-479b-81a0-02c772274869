package ru.naumen.metainfoadmin.client.templates.list;

import java.util.ArrayList;

import com.google.inject.Singleton;

import jakarta.inject.Inject;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;
import ru.naumen.objectlist.shared.AdvListMassOperationLightContext;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Фабрика списка шаблонов списков
 * <AUTHOR>
 * @since 06.04.2018
 */
@Singleton
public class ListTemplatesAdvlistFactory extends AdminCustomAdvlistFactoryBase
{
    @Inject
    private ListTemplatesMessages messages;

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        ArrayList<ExtendedListActionCellContext> actionColumns = new ArrayList<>();
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.EDIT, ListTemplatesCommandCode.EDIT,
                AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT)));
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.DEL, ListTemplatesCommandCode.DELETE,
                AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE)));
        return actionColumns;
    }

    @Override
    protected AdvListMassOperationLightContext createAdvListMassOperationLightContext()
    {
        return massContextWithDelTool(ListTemplatesCommandCode.DELETE);
    }

    @Override
    protected ToolPanel createToolPanel(CustomList content)
    {
        final ToolPanel panel = super.createToolPanel(content);
        panel.addToolBar(createAddButtonToolBar(messages.addTemplate()));
        return panel;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return FakeMetaClassesConstants.ListTemplate.FQN;
    }
}
