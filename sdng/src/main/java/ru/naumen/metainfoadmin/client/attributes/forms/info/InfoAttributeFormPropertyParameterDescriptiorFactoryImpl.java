package ru.naumen.metainfoadmin.client.attributes.forms.info;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormPropertyParametersDescriptorFactoryImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.edit.EditAttributeFormPropertyParametersDescriptorFactoryImpl;

/**
 * Реализация {@link AttributeFormPropertyParametersDescriptorFactoryImpl} для 
 * модальной формы вывода информации об атрибуте
 *
 * <AUTHOR>
 * @since 30 июл. 2018 г.
 */
public class InfoAttributeFormPropertyParameterDescriptiorFactoryImpl extends
        EditAttributeFormPropertyParametersDescriptorFactoryImpl
{
    @Override
    protected void build()
    {
        super.build();
        //@formatter:off
        registerOrModifyProperty(INHERIT, messages.inheritParams(), false, "inherit", 0, false, true);
        registerOrModifyProperty(USE_GEN_RULE, cmessages.useNameRule(), true, "useGenRule", 13, false, false);
        registerOrModifyProperty(DEFAULT_BY_SCRIPT, messages.defaultValueByScript(), false, "defaultByScript", 55, false, false);
        registerOrModifyProperty(DEFAULT_VALUE, messages.defaultValue(), false, "defaultValue", 56, true, true);
        registerOrModifyProperty(COMPLEX_RELATION_ATTR_GROUP, messages.complexAttrGroup(), true, "complexAttrGroup", 59, true,true);
        registerOrModifyProperty(COMPLEX_EMPLOYEE_ATTR_GROUP, messages.complexFormEmplAttrGroup(), true, "complex-employee-attrGroup", 60, true, true);
        registerOrModifyProperty(COMPLEX_TEAM_ATTR_GROUP, messages.complexFormTeamAttrGroup(), true, "complex-team-attrGroup", 61, true, true);
        registerOrModifyProperty(COMPLEX_OU_ATTR_GROUP, messages.complexFormOuAttrGroup(), true, "complex-ou-attrGroup", 62, true, true);
        registerOrModifyProperty(COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW, messages.structuredObjectsView(), true,"complexStructuredObjectsView", 63, true, true);
        //@formatter:on
    }
}
