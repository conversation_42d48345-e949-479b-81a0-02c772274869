package ru.naumen.metainfoadmin.client.scheduler.forms;

import java.util.Collection;

import com.google.gwt.event.dom.client.BlurEvent; //NOPMD
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.commons.shared.utils.StringUtilities; //NOPMD
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.HasReadyState;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.mailreader.client.InboundMailClientService;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;
import ru.naumen.mailreader.shared.task.ReceiveMailTask;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskMessages;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskTypeFactory;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.SchedulerTaskCreator;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.metainfoadmin.client.tags.property.TagsPropertyFactory;

/**
 *
 * <AUTHOR>
 */
public abstract class SchedulerTaskFormPresenterImpl extends OkCancelPresenter<PropertyDialogDisplay>
{
    @Inject
    protected I18nUtil i18nUtil;
    @Inject
    protected Processor validation;
    @Inject
    private NotNullValidator<SelectItem> notNullValidator;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    protected SchedulerTaskTypeFactory factory;
    @Inject
    protected MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    protected SchedulerTaskMessages messages;
    @Inject
    protected TagsMessages tagsMessages;
    @Inject
    private SecurityHelper security;
    @Inject
    private TransliterationService transliterationService;
    @Inject
    protected CommonMessages cmessages;
    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    @Inject
    protected SelectListProperty<String, SelectItem> type;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT_AREA)
    protected Property<String> description;
    @Inject
    protected TagsPropertyFactory tagsPropertyFactory;
    @Inject
    protected SettingsSetOnFormCreator settingsSetOnFormCreator;
    @Inject
    private InboundMailClientService inboundMailClientService;
    protected TagsProperty tags;
    protected Property<SelectItem> settingSetProperty;
    protected PropertyRegistration<Collection<SelectItem>> tagsPR;

    protected AsyncCallback<DtoContainer<SchedulerTask>> saveCallback;
    protected SchedulerTask schTask;
    protected SchedulerTaskCreator creator;

    public SchedulerTaskFormPresenterImpl(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(SchedulerTask schTask, AsyncCallback<DtoContainer<SchedulerTask>> saveCallback)
    {
        this.schTask = schTask;
        this.saveCallback = saveCallback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        SchedulerTask task = creator.getSchedulerTask();
        if (task == null)
        {
            return;
        }

        //проверка для правила обработки входящей почты
        if (!isNewSchedulerTask() && task instanceof ReceiveMailTask)
        {
            //сперва получаем параметр сервера входящей почты, которые нужно будет сохранить на планировщике
            ReceiveMailTask receiveMailTask = (ReceiveMailTask)task;
            inboundMailClientService.getInboundMailServerConfig(
                    receiveMailTask,
                    new BasicCallback<InboundMailServerConfig>()
                    {
                        @Override
                        public void onSuccess(@Nullable InboundMailServerConfig newMailServerConfig)
                        {
                            //затем получаем старые параметры сервера входящей почты
                            inboundMailClientService.getTaskAndInboundMailServerConfig(
                                    task.getCode(),
                                    new BasicCallback<Pair<ReceiveMailTask, InboundMailServerConfig>>()
                                    {
                                        @Override
                                        public void onSuccess(Pair<ReceiveMailTask, InboundMailServerConfig> result)
                                        {
                                            showDialogMessageBeforeSaved(
                                                    task,
                                                    result.right,
                                                    newMailServerConfig);
                                        }
                                    }
                            );
                        }
                    }
            );
        }
        else
        {
            saved(task);
        }
    }

    @Override
    public void refreshDisplay()
    {
    }

    protected void bindProperties()
    {
        ReadyState readyState = new ReadyState(this);
        tags = tagsPropertyFactory.createProperty(new BasicCallback<>(readyState));
        settingSetProperty = settingsSetOnFormCreator.createSettingSetFormElement(getInitialSettingsSetValue());
        type.setCaption(messages.taskType());
        type.setValidationMarker(true);
        title.setCaption(cmessages.title());
        title.setValidationMarker(true);
        title.setMaxLength(Constants.MAX_METAINFO_TITLE_LENGTH);
        code.setCaption(cmessages.code());
        code.setValidationMarker(true);
        code.setMaxLength(Constants.MAX_METAINFO_KEY_LENGTH);
        description.setCaption(cmessages.description());
        tags.setCaption(tagsMessages.tags());

        getDisplay().add(type);
        getDisplay().add(title);
        getDisplay().add(code);
        getDisplay().add(description);

        readyState.ready(new HasReadyState.ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                initSchedulerTask(schTask);
            }
        });
        validation.validate(title, notEmptyValidator);
        validation.validate(type, notNullValidator);
        ensureDebugIds();
    }

    private String getInitialSettingsSetValue()
    {
        return schTask == null ? null : schTask.getSettingsSet();
    }

    protected void initSchedulerTask(SchedulerTask schTask)
    {
        if (schTask == null)
        {
            registerTagsProperty();
            getDisplay().add(settingSetProperty);
            return;
        }
        title.setValue(i18nUtil.getLocalizedTitle(schTask));
        code.setValue(schTask.getCodeOnForm());
        description.setValue(i18nUtil.getLocalizedDescription(schTask));
        tags.trySetObjValue(schTask.getTags());
        initSchedulerTaskCreator(schTask.getType());
    }

    protected void initSchedulerTaskCreator(String code)
    {
        if (null != creator)
        {
            creator.removeProperties();
        }
        if (null == code)
        {
            title.setValue(null);
            creator = null;
        }
        else
        {
            creator = factory.getCreator(code);
            creator.init(schTask);
            creator.bindProperties();
            creator.addProperties(getDisplay());
            if (null == schTask)
            {
                title.setValue(factory.getTypeTitle(SelectListPropertyValueExtractor.getValue(type)));
            }
        }
        registerTagsProperty();
        getDisplay().add(settingSetProperty);
    }

    protected abstract boolean isNewSchedulerTask();

    @Override
    protected void onBind()
    {
        bindProperties();
        super.onBind();
        registerHandler(type.addValueChangeHandler(event ->
        {
            initSchedulerTaskCreator(SelectListPropertyValueExtractor.getValue(type));
            eventBus.fireEvent(new UpdateTabOrderEvent(true));
        }));
        registerHandler(title.asWidget().addHandler(event ->
        {
            String codeValue = code.getValue();
            if (StringUtilities.isEmpty(codeValue))
            {
                String titleValue = title.getValue();
                String specialSymbols = security.hasVendorProfile()
                        ? Constants.PREFIXED_CODE_SPECIAL_CHARS_FOR_VENDOR
                        : "";
                codeValue = transliterationService.transliterateToCode(titleValue,
                        Constants.MAX_METAINFO_KEY_LENGTH, specialSymbols);
                code.setValue(codeValue);
            }
        }, BlurEvent.getType()));
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(type, "type");
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(description, "description");
        DebugIdBuilder.ensureDebugId(tags, "tags");
    }

    private void registerTagsProperty()
    {
        if (null != tagsPR)
        {
            tagsPR.unregister();
        }
        tagsPR = getDisplay().add(tags);
    }

    /**
     * Показать диалоговое окно с подтверждением сохранения изменений планировщика задач, если изменился сервер
     * входящей почты, а его настройки необходимо подтверждать. Т.к. заполнены не полностью.
     * @param task задача, которую нужно сохранить.
     * @param oldMailServerConfig старая конфигурация сервера входящей почты
     * @param newMailServerConfig новая конфигурация сервера входящей почты.
     */
    private void showDialogMessageBeforeSaved(
            SchedulerTask task,
            @Nullable InboundMailServerConfig oldMailServerConfig,
            @Nullable InboundMailServerConfig newMailServerConfig)
    {
        inboundMailClientService.showDialogMessageBeforeSavingSchedule(
                oldMailServerConfig,
                newMailServerConfig,
                () -> saved(task));
    }

    /**
     * Выполнить сохранение планировщика задачи.
     * @param task планировщик задачи для сохранения.
     */
    private void saved(SchedulerTask task)
    {
        task.setType(SelectListPropertyValueExtractor.getValue(type));
        i18nUtil.updateI18nObjectTitle(task, title.getValue());
        if (isNewSchedulerTask())
        {
            task.setCode(code.getValue());
        }
        i18nUtil.updateI18nObjectDescription(task, description.getValue());
        task.getTags().clear();
        task.getTags().addAll(SelectListPropertyValueExtractor.getCollectionValue(tags));
        task.setSettingsSet(SelectListPropertyValueExtractor.getValue(settingSetProperty));
        metainfoModificationService.saveSchedulerTask(task, isNewSchedulerTask(), tags.getPendingTags(),
                new CallbackDecorator<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>>(saveCallback)
                {
                    @Override
                    protected DtoContainer<SchedulerTask> apply(DtoContainer<SchedulerTask> result)
                    {
                        unbind();
                        return result;
                    }
                });
    }
}
