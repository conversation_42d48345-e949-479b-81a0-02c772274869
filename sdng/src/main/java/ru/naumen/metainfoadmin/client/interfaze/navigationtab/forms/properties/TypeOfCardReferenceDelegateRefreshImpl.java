package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Делегат обновления для виджета {@link MenuItemPropertyCode#TYPE_OF_CARD}
 *
 * <AUTHOR>
 * @since 09.03.2022
 */
public class TypeOfCardReferenceDelegateRefreshImpl extends ReferencePropertyDelegateRefreshBase<SelectItem,
        DtObjectSelectProperty>
{
}