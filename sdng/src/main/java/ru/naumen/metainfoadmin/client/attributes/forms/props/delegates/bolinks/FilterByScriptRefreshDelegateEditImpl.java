package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.bolinks;

import static ru.naumen.core.shared.Constants.PARENT_ATTR;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

/**
 * Отвечает за необходимость показывать галочку "Фильтрация значений при редактировании" на форме редактирования
 * атрибута
 * <AUTHOR>
 * @since 15.08.2012
 *
 */
public class FilterByScriptRefreshDelegateEditImpl extends FilterByScriptRefreshDelegateImpl<ObjectFormEdit>
{
    @Override
    protected boolean isFilteredByScript(PropertyContainerContext context)
    {
        Attribute attr = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        if (attr == null || !CollectionUtils.isEmpty(attr.getSystemFilters()))
        {
            return false;
        }
        boolean notAggregated = attr.isSystemEditable();
        return super.isFilteredByScript(context)
               && (notAggregated
                   || Constants.StateAttributeType.CODE.equals(attr.getType().getCode())
                   || Association.CLIENT.equals(attr.getCode())
                   || PARENT_ATTR.equals(attr.getCode())
                   || (AbstractBO.METACLASS.equals(attr.getCode()) && !ServiceCall.FQN.isSameClass(
                attr.getFqn().getClassFqn()))
               );
    }
}
