/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.separator;

import jakarta.inject.Inject;

import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelMessages;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.dom.client.HasAllDragAndDropHandlers;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FocusPanel;

/**
 * <AUTHOR>
 * @since 28 мая 2015 г.
 *
 */
public class ToolSeparatorInToolsDisplayImpl extends Composite implements ToolSeparatorInToolsDisplay
{
    public interface ToolSeparatorInToolsDisplayImplUiBinder extends
            UiBinder<FocusPanel, ToolSeparatorInToolsDisplayImpl>
    {
    }

    private static ToolSeparatorInToolsDisplayImplUiBinder uiBinder = GWT
            .create(ToolSeparatorInToolsDisplayImplUiBinder.class);

    @UiField
    FocusPanel panel;

    @Inject
    public ToolSeparatorInToolsDisplayImpl(EditableToolPanelMessages messages)
    {
        initWidget(uiBinder.createAndBindUi(this));
        panel.getElement().setInnerText(messages.separator());
    }

    @Override
    public void destroy()
    {
        removeFromParent();
    }

    @Override
    public HasAllDragAndDropHandlers getDndHandler()
    {
        return panel;
    }

    @Override
    public void setDraggable()
    {
        getElement().setDraggable(Element.DRAGGABLE_TRUE);
    }

    @Override
    public void startProcessing()
    {
    }

    @Override
    public void stopProcessing()
    {
    }
}
