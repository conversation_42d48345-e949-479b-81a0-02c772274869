package ru.naumen.metainfoadmin.client.eventaction;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.shared.eventaction.EventAction.TxType;
import ru.naumen.metainfo.shared.eventaction.ScriptEventAction;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;

/**
 * Инфо презентер карточки ДПС типа "Скрипт"
 *
 * <AUTHOR>
 * @since 06.12.2011
 */
public class ScriptEventActionInfoPresenter
        extends ScriptEventActionInfoPresenterBase<ScriptEventAction>
{
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> syncAction;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> isSlow;
    @Inject
    private PossibleTxTypePredicate txTypePredicate;
    @Inject
    private PossibleSlowPredicate slowPredicate;

    @Inject
    public ScriptEventActionInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void addActionProperties()
    {
        super.addActionProperties();

        String eventTypeName = eventAction.getObject().getEvent().getEventType().name();
        if (slowPredicate.test(eventTypeName))
        {
            addProperty(messages.externalInteraction(), isSlow);
        }
        if (txTypePredicate.test(eventTypeName))
        {
            addProperty(messages.performSynchronously(), syncAction);
        }
    }

    @Override
    protected void setPropertiesValues()
    {
        super.setPropertiesValues();

        syncAction.setValue(TxType.CURRENT_TX.equals(eventAction.getObject().getTxType()));
        isSlow.setValue(eventAction.getObject().isSlow());
    }

    @Override
    void ensureDebugIds()
    {
        super.ensureDebugIds();
        DebugIdBuilder.ensureDebugId(syncAction, "txType");
        DebugIdBuilder.ensureDebugId(isSlow, "isSlow");
    }
}
