/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.display;

import java.util.Map;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.display.VMapItemContentColumnsProviderImpl;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.display.VMapRowCommandParam;
import ru.naumen.metainfoadmin.client.escalation.EscalationMessages;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.EscalationVMapItemRowCommandCode;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.EscalationValueMapItemContext;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationVMapItemContentColumnsProviderImpl extends
        VMapItemContentColumnsProviderImpl<EscalationValueMapItemContext>
{
    @Inject
    EscalationSchemesColumn escalationSchemesColumn;
    @Inject
    EscalationMessages messages;

    @Inject
    public EscalationVMapItemContentColumnsProviderImpl(@Assisted EscalationValueMapItemContext context,
            @Assisted CellTable<Map<String, Object>> table)
    {
        super(context, table);
    }

    @Override
    protected void addActionColumns(VMapRowCommandParam<EscalationValueMapItemContext> param)
    {
        addActionColumn(EscalationVMapItemRowCommandCode.EDIT, param, AdminPermissionUtils::hasEditPermission);
        addActionColumn(EscalationVMapItemRowCommandCode.DELETE, param, AdminPermissionUtils::hasDeletePermission);
    }

    @Override
    protected void addDataColumns(String attrsType)
    {
        if (!ValueMapCatalogItem.TARGET_ATTRS.equals(attrsType))
        {
            super.addDataColumns(attrsType);
            return;
        }
        table.addColumn(escalationSchemesColumn, messages.escalationSchemes());
    }
}