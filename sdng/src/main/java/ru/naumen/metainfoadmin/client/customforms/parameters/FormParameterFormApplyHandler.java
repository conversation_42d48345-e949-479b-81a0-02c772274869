package ru.naumen.metainfoadmin.client.customforms.parameters;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.ATTRIBUTE;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextUtils;
import ru.naumen.core.client.content.SafeContextualCallback;
import ru.naumen.core.client.events.ApplyFormEvent;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.DefaultContext;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormApplyHandler;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormApplyHandlerImpl;
import ru.naumen.metainfoadmin.client.customforms.CustomFormContext;
import ru.naumen.metainfoadmin.client.customforms.FormSettingsChangedEvent;
import ru.naumen.metainfoadmin.shared.customforms.SaveFormParameterAction;

/**
 * Реализация {@link AttributeFormApplyHandler} для форм добавления и редактирования 
 * параметров настраиваеных форм
 *
 * <AUTHOR>
 * @since 20 апр. 2016 г.
 */
public class FormParameterFormApplyHandler<F extends ParameterForm> extends AttributeFormApplyHandlerImpl<F>
{
    @Inject
    private DispatchAsync dispatch;

    @Inject
    //@formatter:off
    public FormParameterFormApplyHandler(@Assisted("contextProps") IProperties contextProps,
            @Assisted("propertyValues") IProperties propertyValues,
            @Assisted PropertyContainerPresenter propertyContainer, 
            @Assisted Context context,
            @Assisted Presenter formPresenter, 
            @Assisted AsyncCallback<MetaClass> callback)
    //@formatter:on
    {
        super(contextProps, propertyValues, propertyContainer, context, formPresenter, callback);
    }

    @Override
    public void onApplyForm(ApplyFormEvent event)
    {
        if (!propertyContainer.validate())
        {
            return;
        }
        AttributeType attrType = attrTypeFactory.create(contextProps, propertyValues);

        SaveFormParameterAction action = (SaveFormParameterAction)actionFactory.createAction(contextProps,
                propertyValues, attrType);

        CustomFormContext formContext = ((DefaultContext)context).getParentContext();
        Attribute attribute = contextProps.getProperty(ATTRIBUTE);

        //@formatter:off
        action.setModificationContext(formContext.getModificationContext())
              .setParameterCode(attribute != null ? attribute.getCode() : null);
        //@formatter:on

        dispatch.execute(action, new SafeContextualCallback<SimpleResult<DtoContainer<CustomForm>>>(context)
        {
            @Override
            protected void handleSuccess(SimpleResult<DtoContainer<CustomForm>> result)
            {
                formPresenter.unbind();
                DtoContainer<CustomForm> container = result.get();
                PermissionHolder permissions = container.getProperty(SettingsSet.ADMIN_PERMISSIONS);
                if (permissions != null)
                {
                    permissions.fillPermissionHolder(ContextUtils.getRootPermissionContext(context).getPermissions());
                }
                context.getEventBus().fireEvent(new FormSettingsChangedEvent(container));
            }
        });
    }
}
