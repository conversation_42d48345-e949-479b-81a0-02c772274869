package ru.naumen.metainfoadmin.client.escalation.scheme;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;

/**
 * <AUTHOR>
 * @since 20.08.2012
 *
 */
public class EscalationSchemeContext
{
    private DtoContainer<EscalationScheme> escalationScheme;
    private final EventBus localEventBus;
    private final Display display;

    public EscalationSchemeContext(DtoContainer<EscalationScheme> escalationScheme, EventBus localEventBus,
            Display display)
    {
        this.escalationScheme = escalationScheme;
        this.localEventBus = localEventBus;
        this.display = display;
    }

    public Display getDisplay()
    {
        return display;
    }

    public DtoContainer<EscalationScheme> getEscalationScheme()
    {
        return escalationScheme;
    }

    public EventBus getLocalEventBus()
    {
        return localEventBus;
    }

    public void setEscalationScheme(DtoContainer<EscalationScheme> escalationScheme)
    {
        this.escalationScheme = escalationScheme;
    }

}