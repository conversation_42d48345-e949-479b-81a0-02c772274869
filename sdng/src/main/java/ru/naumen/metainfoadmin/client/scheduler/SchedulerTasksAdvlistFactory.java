package ru.naumen.metainfoadmin.client.scheduler;

import java.util.ArrayList;

import com.google.inject.Singleton;

import jakarta.inject.Inject;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.metainfoadmin.client.scheduler.command.SchedulerCommandCode;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;
import ru.naumen.objectlist.shared.AdvListMassOperationLightContext;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Фабрика списка задач планировщика
 * <AUTHOR>
 * @since 03.10.2017
 */
@Singleton
public class SchedulerTasksAdvlistFactory extends AdminCustomAdvlistFactoryBase
{
    @Inject
    private SchedulerTaskMessages messages;

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        ArrayList<ExtendedListActionCellContext> actionColumns = new ArrayList<>();
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.RUN, SchedulerCommandCode.RUN_SCH_TASK,
                dto -> Boolean.TRUE.equals(dto.getProperty(Constants.Tag.IS_ELEMENT_ENABLED))));
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.EDIT, SchedulerCommandCode.EDIT_SCH_TASK,
                AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT)));
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.DEL, SchedulerCommandCode.DELETE_SCH_TASK,
                AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE)));
        return actionColumns;
    }

    @Override
    protected AdvListMassOperationLightContext createAdvListMassOperationLightContext()
    {
        return massContextWithDelTool(SchedulerCommandCode.DELETE_SCH_TASK);
    }

    @Override
    protected ToolPanel createToolPanel(CustomList content)
    {
        final ToolPanel panel = super.createToolPanel(content);
        panel.addToolBar(createAddButtonToolBar(messages.addTask()));
        return panel;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return FakeMetaClassesConstants.SchedulerTask.FQN;
    }
}
