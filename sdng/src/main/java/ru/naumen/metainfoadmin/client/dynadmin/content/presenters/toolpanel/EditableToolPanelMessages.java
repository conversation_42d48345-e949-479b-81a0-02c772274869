package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import jakarta.inject.Singleton;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * <AUTHOR>
 * @since 01 июня 2015 г.
 *
 */
@DefaultLocale("ru")
@Singleton
public interface EditableToolPanelMessages extends Messages
{
    String actionBarUsage();

    @Description("Способ вызова действия")
    String actionInvocationMethod();

    @Description("Панель действий")
    String actionPanel();

    @Description("Действия")
    String actions();

    @Description("Добавить/Удалить объекты")
    String addDeleteObjButton();

    @Description("Добавление действия")
    String addItem();

    @Description("Добавление элемента")
    String addMenuItem();

    @Description("Добавить элемент")
    String addMenuItemIcon();

    @Description("Добавить на панель действий")
    String addToToolBarContent();

    @Description("Добавить пользовательский элемент")
    String addUserElement();

    String allowInMassOperations();

    @Description("Применяется к")
    String appliedTo();

    String attributeGroupForAddFile();

    String attributeToSaveFile();

    @Description("Добавить действие")
    String addAction();

    @Description("Добавить пользовательский элемент")
    String addLineBreak();

    @Description("Добавление переноса строки")
    String addingLineBreak();

    String bulkOperationsBarUsage();

    @Description("Скопировать настройку из родителя")
    String copyToolPanelFromParent();

    @Description("Текущему объекту")
    String currentObject();

    @Description("Область контента")
    String contentArea();

    @Description("по умолчанию")
    String defaultMassEditForm();

    @Description("Убрать с панели действий")
    String deleteFromToolBarContent();

    @Description("Вы действительно хотите удалить элемент")
    String doYouReallyWantToDeleteElement(String title);

    @Description("Редактирование действия")
    String editItem();

    @Description("Редактирование элемента")
    String editMenuItem();

    @Description("Настройка действий")
    String editToolPanel();

    @Description("Настройка панели действий контента")
    String editToolPanelForContent(String contentTitle);

    @Description("Настройка панели действий карточки объекта")
    String editToolPanelForObjectCard();

    @Description("Настройка панели действий вкладки карточки объекта")
    String editToolPanelForTab(String contentTitle);

    @Description("Завершить настройку панели действий")
    String endEditToolBar();

    @Description("Кнопки быстрой смены статуса")
    String fastChangeStateButtons();

    @Description("Расположить под значением")
    String placeBottom();

    @Description("Расположить справа от значения")
    String placeRight();

    @Description("Показывать всегда")
    String showAlways();

    @Description("Показывать только при наведении")
    String showOnlyOnHover();

    @Description("Настройка панели действий")
    String toolPanelSettings();

    @Description("Верхний блок действий")
    String topActionBlock();

    @Description("Кнопки действий, видимые в шапке карточки объекта")
    String windowActionButtons();

    @Description("(для массового запроса)")
    String forMassRequest();

    @Description("Перейти в карточку добавленного объекта")
    String goToCardAfterCreation();

    @Description("Иконка")
    String iconsForControls();

    @Description("Иконка и подпись")
    String iconWithText();

    @Description("Иконка вызова меню")
    String image();

    @Description("Выбор из всплывающего меню действий")
    String invokeActionList();

    @Description("Нажатие на иконку/кнопку действия в строке списка")
    String invokeIconClick();

    @Description("Нажатие по строке списка")
    String invokeRowClick();

    @Description("Слева")
    String left();

    @Description("Слева и справа")
    String leftAndRight();

    @Description("Ссылка")
    String link();

    @Description("Объектам списка")
    String listObjects();

    @Description("Нижний блок действий")
    String bottomActionBlock();

    @Description("[перенос строки]")
    String lineBreak();

    @Description("Форма массового редактирования")
    String massEditForm();

    @Description("Панель массовых операций (видна при выборе объектов в списке)")
    String massOperationsPanel();

    @Description("Элементы меню")
    String menuItems();

    @Description("Действия в списке объектов")
    String menuOfActionsWithObject();

    String menuOfActionsWithObjectUsage();

    String menuOfMobileCardUsage(String toolHolderCode);

    @Description("Новая кнопка")
    String newButton();

    @Description("Не отображается")
    String notDisplayed();

    @Description("Не использовать панель массовых операций")
    String notUseMassPanel();

    @Description("Расположение")
    String position();

    @Description("Внешний вид")
    String presentation();

    @Description("С фиксацией")
    String pushable();

    @Description("Форма быстрого добавления")
    String quickAddForm();

    @Description("Форма быстрого редактирования")
    String quickEditForm();

    @Description("Связанному объекту")
    String relatedObject();

    @Description("Сбросить до системной настройки")
    String resetToolPanelSettings();

    @Description("После добавления объекта вернуться на список")
    String returnToListAfterAdding();

    @Description("Справа")
    String right();

    @Description("Клик по строке уже переопределен")
    String rowClickAlreadyConfigured(String toolTitle);

    @Description("Выберите вид")
    String selectView();

    @Description("Разделитель групп")
    String separator();

    @Description("Перейти к настройке панели действий")
    String startEditToolBar();

    @Description("Системные действия")
    String systemActions();

    @Description("Системная логика формирования панели действий не используется")
    String systemSettingsNotUsed();

    @Description("Настраивайте панель действий, перетаскивая элементы между областями")
    String toolPanelSetupTip();

    @Description("Передавать геопозицию устройства")
    String transferDeviceGeostation();

    @Description("Используется системная логика формирования панели действий")
    String usedSystemSettings();

    @Description("Использовать системную логику формирования панели действий (пустая панель действий)")
    String useEmptySystemSettings();

    String useMassEditForm();

    @Description("Использовать системную логику формирования панели массовых операций (на панель выводятся кнопки, "
                 + "видимые в шапке карточки объекта списка)")
    String useMassSystemSettings();

    @Description("Использовать форму быстрого добавления")
    String useQuickAddForm();

    @Description("Использовать форму быстрого редактирования")
    String useQuickEditForm();

    @Description("Пользовательские действия")
    String userActions();

    @Description("Использовать системную логику действий с элементами списка")
    String useSystemObjectAction();

    @Description("Использовать системную логику формирования панели действий (отображаются только системные кнопки)")
    String useSystemSettings();

    @Description("Кнопка с иконкой и подписью")
    String withIconAndText();

    @Description("Кнопка с подписью")
    String withoutIcon();

    @Description("Кнопка с иконкой")
    String withoutText();

    @Description("При добавлении заполнить текущим объектом атрибут")
    String attributeFillByCurrentObject();
}