package ru.naumen.metainfoadmin.client.scheduler;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.SCHEDULER;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.ResourceCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;

/**
 * {@link Presenter} Карточки правила задачи планировщика
 *
 * <AUTHOR>
 */
public class TriggerPresenter extends AdminTabPresenter<TriggerPlace>
{
    private final CommonMessages commonMessages;
    private final SchedulerGinjector injector;
    private final SchedulerTaskMessages schedulerTaskMessages;
    private final AdminMetainfoServiceAsync metainfoService;

    private TriggerInfoPresenter infoPresenter;

    @Inject
    public TriggerPresenter(AdminTabDisplay display,
            EventBus eventBus,
            CommonMessages commonMessages,
            SchedulerGinjector injector,
            SchedulerTaskMessages schedulerTaskMessages,
            AdminMetainfoServiceAsync metainfoService)
    {
        super(display, eventBus);
        this.commonMessages = commonMessages;
        this.injector = injector;
        this.schedulerTaskMessages = schedulerTaskMessages;
        this.metainfoService = metainfoService;
        getDisplay().setTabBarVisible(false);
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        if (null != getPlace().getTrigger())
        {
            afterLoadTrigger(getPlace().getTrigger());
        }
        else
        {
            metainfoService.getTrigger(getPlace().getCode(), new ResourceCallback<DtoContainer<Trigger>>(commonMessages)
            {
                @Override
                protected void handleSuccess(DtoContainer<Trigger> trigger)
                {
                    afterLoadTrigger(trigger);
                }
            });
        }
    }

    @Override
    protected void onUnbind()
    {
        if (null != infoPresenter)
        {
            infoPresenter.unbind();
        }
    }

    private void afterLoadTask(final DtoContainer<Trigger> trigger, final DtoContainer<SchedulerTask> schTask)
    {
        infoPresenter = injector.triggerInfoPresenter();
        infoPresenter.init(trigger, schTask);
        addContent(infoPresenter, "info");
        getDisplay().setTitle(trigger.getTitle());
        prevPageLinkPresenter.bind(schedulerTaskMessages.toSchedulerTask(),
                new SchedulerTaskPlace(trigger.get().getSchTaskCode()),
                new SchedulerPlace());
    }

    private void afterLoadTrigger(final DtoContainer<Trigger> trigger)
    {
        metainfoService.getSchedulerTask(trigger.get().getSchTaskCode(),
                new ResourceCallback<DtoContainer<SchedulerTask>>(commonMessages)
                {
                    @Override
                    protected void handleSuccess(DtoContainer<SchedulerTask> schTask)
                    {
                        afterLoadTask(trigger, schTask);
                    }
                });
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return SCHEDULER;
    }
}
