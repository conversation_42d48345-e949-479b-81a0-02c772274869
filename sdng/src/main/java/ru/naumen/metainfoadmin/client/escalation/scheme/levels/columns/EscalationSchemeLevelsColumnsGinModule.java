package ru.naumen.metainfoadmin.client.escalation.scheme.levels.columns;

import java.util.ArrayList;
import java.util.LinkedHashMap;

import jakarta.inject.Inject;

import com.google.common.collect.Maps;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.Header;
import com.google.gwt.user.cellview.client.TextHeader;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.core.shared.escalation.EscalationSchemeLevelCondition.EscalationSchemeLevelConditionCode;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;

/**
 * <AUTHOR>
 * @since 20.08.2012
 *
 */
public class EscalationSchemeLevelsColumnsGinModule extends AbstractGinModule
{

    public static class EscalationSchemeLevelsColumnsProvider implements
            Provider<ArrayList<Pair<? extends Header<?>, ? extends Column<EscalationSchemeLevel, ?>>>>
    {
        @Inject
        EscalationSchemeLevelsActionsColumn actionsColumn;
        @Inject
        EscalationSchemeLevelsConditionColumn conditionColumn;
        @Inject
        EscalationSchemeLevelsExecuteActionColumn executeActionColumn;
        @Inject
        EscalationSchemeLevelsLevelColumn levelColumn;
        @Inject
        EscalationSchemeLevelsValueColumn valueColumn;
        @Inject
        CommonMessages commonMessages;
        @Inject
        EventActionMessages eventActionMessages;
        @Inject
        EscalationSchemeLevelColumnsMessages messages;
        @Inject
        WidgetResources resources;

        @Override
        public ArrayList<Pair<? extends Header<?>, ? extends Column<EscalationSchemeLevel, ?>>> get()
        {
            Header<?> levelHeader = new TextHeader(commonMessages.level());
            Header<?> execActionHeader = new TextHeader(messages.executeActionAfterSchemeChange());

            /*levelHeader.setHeaderStyleNames(resources.all().alignCenter());
            execActionHeader.setHeaderStyleNames(resources.all().alignCenter());*/
            ArrayList<Pair<? extends Header<?>, ? extends Column<EscalationSchemeLevel, ?>>> result =
                    new ArrayList<>();
            result.add(Pair.create(levelHeader, levelColumn));
            result.add(Pair.create(new TextHeader(commonMessages.condition()), conditionColumn));
            result.add(Pair.create(new TextHeader(commonMessages.value()), valueColumn));
            result.add(Pair.create(new TextHeader(eventActionMessages.action()), actionsColumn));
            result.add(Pair.create(execActionHeader, executeActionColumn));
            return result;
        }
    }

    public static class EscalationSchemeLevelsConditionsTextProvider implements Provider<LinkedHashMap<String, String>>
    {
        @Inject
        EscalationSchemeLevelColumnsMessages messages;

        @Override
        public LinkedHashMap<String, String> get()
        {
            LinkedHashMap<String, String> result = Maps.newLinkedHashMap();
            result.put(EscalationSchemeLevelConditionCode.TIME_EXCEEDED, messages.afterTimePassed());
            result.put(EscalationSchemeLevelConditionCode.TIME_PART_EXCEEDED, messages.afterPartOfTimePassed());
            return result;
        }
    }

    public static final String ESCALATION_SCHEME_LEVELS_CONDITIONS = "escalationSchemeLevelsConditions";
    public static final String ESCALATION_SCHEME_LEVELS_COLUMNS = "escalationSchemeLevelsColumns";
    public static final String ESCALATION_SCHEME_LEVELS_ACTIONS = "escalationSchemeLevelsActions";

    @Override
    protected void configure()
    {
        bind(EscalationSchemeLevelsActionsColumn.class);
        bind(EscalationSchemeLevelsConditionColumn.class);
        bind(EscalationSchemeLevelsExecuteActionColumn.class);
        bind(EscalationSchemeLevelsLevelColumn.class);
        bind(EscalationSchemeLevelsValueColumn.class);
        bind(EscalationSchemeLevelColumnsMessages.class).in(Singleton.class);
        bind(EscalationSchemeLevelsActionsSafeHtmlRenderer.class).in(Singleton.class);

        //@formatter:off
        bind(new TypeLiteral<ArrayList<Pair<? extends Header<?>, ? extends Column<EscalationSchemeLevel, ?>>>>(){})
            .annotatedWith(Names.named(ESCALATION_SCHEME_LEVELS_COLUMNS))
            .toProvider(EscalationSchemeLevelsColumnsProvider.class)
            .in(Singleton.class);
        bind(new TypeLiteral<LinkedHashMap<String, String>>(){})
            .annotatedWith(Names.named(ESCALATION_SCHEME_LEVELS_CONDITIONS))
            .toProvider(EscalationSchemeLevelsConditionsTextProvider.class)
            .in(Singleton.class);
        //@formatter:on
    }
}