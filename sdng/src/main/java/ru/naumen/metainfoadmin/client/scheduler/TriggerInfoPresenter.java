package ru.naumen.metainfoadmin.client.scheduler;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.ui.HasEnabled;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.client.events.TriggerUpdatedEvent;
import ru.naumen.metainfo.client.events.TriggerUpdatedHandler;
import ru.naumen.metainfo.shared.scheduler.PeriodicTrigger;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfo.shared.scheduler.Trigger.CalculateStrategies;
import ru.naumen.metainfo.shared.scheduler.Trigger.Periods;
import ru.naumen.metainfoadmin.client.MGinjector;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.scheduler.command.SchedulerCommandCode;
import ru.naumen.metainfoadmin.client.scheduler.command.TriggerCommandParam;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.TriggerCreatorFactory;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * {@link Presenter} для отображения параметров задачи планирощика
 * oaleksandrova
 */
public class TriggerInfoPresenter extends BasicPresenter<InfoDisplay> implements TriggerUpdatedHandler
{
    public enum TriggerInfoPropertyName
    {
        startDate,
        period,
        strategy,
        planDate,
        randomizeDelayOn;
    }

    @Inject
    protected CommonMessages cmessages;
    @Inject
    SchedulerTaskMessages messages;
    @Inject
    MGinjector injector;
    @Inject
    TriggerCreatorFactory factory;
    @Inject
    ButtonFactory buttonFactory;
    @Inject
    PropertiesGinjector properties;
    @Inject
    FontIconFactory<?> iconFactory;
    Map<String, PropertyRegistration<String>> otherPropertyRegistrations;
    Map<String, Property<String>> otherProperties;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    Property<String> on;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Property<String> title;
    Property<String> settingsSet;
    @Inject
    Formatters formatters;
    @SuppressWarnings("rawtypes")
    OnStartCallback refreshCallback;
    @SuppressWarnings("rawtypes")
    OnStartCallback deleteTriggerCallback;
    @Inject
    PlaceController placeController;
    @Inject
    protected SharedSettingsClientService sharedSettings;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    private DtoContainer<Trigger> trigger;
    private DtoContainer<SchedulerTask> schTask;
    private final ToolBarDisplayMediator<DtoContainer<Trigger>> toolBar;

    @Inject
    public TriggerInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        getDisplay().getToolBar().compact();
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    public String getOnPropertyValue(Boolean status)
    {
        return status ? iconFactory.create(IconCodes.YES).asString() : null;
    }

    public void init(DtoContainer<Trigger> trigger, DtoContainer<SchedulerTask> schTask)
    {
        this.trigger = trigger;
        this.schTask = schTask;
    }

    @Override
    public void onTriggerUpdated(TriggerUpdatedEvent e)
    {
        trigger = e.getTrigger();
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        display.clearProperties();
        otherPropertyRegistrations = new HashMap<>();
        otherProperties = new HashMap<>();
        addProperties();
        setCommonPropertiesValues();
        setOtherPropertiesValues();
        toolBar.refresh(trigger);
    }

    @SuppressWarnings("rawtypes")
    @Override
    protected void onBind()
    {
        refreshCallback = new OnStartBasicCallback(getDisplay())
        {
            @Override
            protected void handleSuccess(Object value)
            {
                refreshDisplay();
            }
        };
        deleteTriggerCallback = new OnStartBasicCallback(getDisplay())
        {
            @Override
            protected void handleSuccess(Object value)
            {
                placeController.goTo(new SchedulerTaskPlace(trigger.get().getSchTaskCode()));
            }
        };
        initToolBar();
        registerHandler(eventBus.addHandler(TriggerUpdatedEvent.getType(), this));
        refreshDisplay();
    }

    @Override
    protected void onUnbind()
    {
    }

    private void addDisabledOnTools(TriggerCommandParam param)
    {
        ButtonPresenter<DtoContainer<Trigger>> onButton = (ButtonPresenter<DtoContainer<Trigger>>)buttonFactory.create(
                ButtonCode.SWITCH, cmessages.switchOn(), SchedulerCommandCode.ON_TRIGGER, param);
        ((HasEnabled)onButton.getDisplay()).setEnabled(false);
        ButtonPresenter<DtoContainer<Trigger>> offButton = (ButtonPresenter<DtoContainer<Trigger>>)buttonFactory.create(
                ButtonCode.SWITCH, cmessages.switchOn(), SchedulerCommandCode.OFF_TRIGGER, param);
        ((HasEnabled)offButton.getDisplay()).setEnabled(false);
        toolBar.add(onButton);
        toolBar.add(offButton);
    }

    private void addProperties()
    {
        getDisplay().add(title);
        title.setCaption(cmessages.title());

        switch (trigger.get().getType())
        {
            case PERIODIC:
                Property<String> startDate = initScriptProperty(messages.startDate(), "startDate");
                otherPropertyRegistrations.put(TriggerInfoPropertyName.startDate.name(), getDisplay().add(startDate));
                otherProperties.put(TriggerInfoPropertyName.startDate.name(), startDate);

                Property<String> period = initScriptProperty(messages.period(), "period");
                otherPropertyRegistrations.put(TriggerInfoPropertyName.period.name(), getDisplay().add(period));
                otherProperties.put(TriggerInfoPropertyName.period.name(), period);

                Property<String> strategy = initScriptProperty(messages.calculationStrategy(), "calculationStrategy");
                otherPropertyRegistrations.put(TriggerInfoPropertyName.strategy.name(), getDisplay().add(strategy));
                otherProperties.put(TriggerInfoPropertyName.strategy.name(), strategy);

                break;
            case CONCRETE_DATE:
                break;
            default:
                throw new FxException("Unsupported TriggerType enum value:" + trigger.get().getType());
        }
        Property<String> planExecutionDate = initScriptProperty(messages.planExecutionDate(), "planExecutionDate");
        otherPropertyRegistrations.put(TriggerInfoPropertyName.planDate.name(), getDisplay().add(planExecutionDate));
        otherProperties.put(TriggerInfoPropertyName.planDate.name(), planExecutionDate);

        if (sharedSettings.useRandomizeDelay())
        {
            Property<String> randomizeDelayOn = initScriptProperty(messages.randomizeDelay(), "randomizeDelayOn");
            otherPropertyRegistrations.put(TriggerInfoPropertyName.randomizeDelayOn.name(),
                    getDisplay().add(randomizeDelayOn));
            otherProperties.put(TriggerInfoPropertyName.randomizeDelayOn.name(), randomizeDelayOn);
        }
        getDisplay().add(on);
        on.setCaption(messages.isOn());
        settingsSet = settingsSetOnFormCreator.createFieldOnCard(getDisplay());
        ensureDebugIds();
    }

    @SuppressWarnings("unchecked")
    private ButtonPresenter<DtoContainer<Trigger>> addTool(String btn, String title, String cmd,
            TriggerCommandParam param)
    {
        ButtonPresenter<DtoContainer<Trigger>> buttonPresenter =
                (ButtonPresenter<DtoContainer<Trigger>>)buttonFactory.create(
                        btn, title, cmd, param);
        toolBar.add(buttonPresenter);
        return buttonPresenter;
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(on, "on");
    }

    private Property<String> initScriptProperty(String caption, String id)
    {
        Property<String> scriptProperty = properties.htmlTextProperty();
        scriptProperty.setCaption(caption);
        DebugIdBuilder.ensureDebugId(scriptProperty, id);
        return scriptProperty;
    }

    @SuppressWarnings("unchecked")
    private void initToolBar()
    {
        TriggerCommandParam param = new TriggerCommandParam(trigger, refreshCallback, trigger.get().getSchTaskCode());
        TriggerCommandParam delParam = new TriggerCommandParam(trigger, deleteTriggerCallback,
                trigger.get().getSchTaskCode());

        if (!isSchedulerTaskEnabled())
        {
            addDisabledOnTools(param);
        }
        else
        {
            ButtonPresenter<DtoContainer<Trigger>> switchOn = addTool(ButtonCode.SWITCH, cmessages.switchOn(),
                    SchedulerCommandCode.ON_TRIGGER, param);
            switchOn.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));

            ButtonPresenter<DtoContainer<Trigger>> switchOff = addTool(ButtonCode.SWITCH, cmessages.switchOff(),
                    SchedulerCommandCode.OFF_TRIGGER, param);
            switchOff.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        }
        ButtonPresenter<DtoContainer<Trigger>> editBtn = addTool(ButtonCode.EDIT, cmessages.edit(),
                SchedulerCommandCode.EDIT_TRIGGER, param);
        editBtn.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        ButtonPresenter<DtoContainer<Trigger>> delBtn = addTool(ButtonCode.DELETE, cmessages.delete(),
                SchedulerCommandCode.DELETE_TRIGGER, delParam);
        delBtn.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE));

        toolBar.bind();
    }

    private boolean isSchedulerTaskEnabled()
    {
        return Boolean.TRUE.equals(schTask.getProperty(Constants.Tag.IS_ELEMENT_ENABLED));
    }

    private void setCommonPropertiesValues()
    {
        on.setValue(getOnPropertyValue(trigger.get().isEnabled() && isSchedulerTaskEnabled()));
        title.setValue(trigger.getTitle());
        settingsSetOnFormCreator.setValueOnCardProperty(trigger.get().getSettingsSet(), settingsSet);
    }

    private void setOtherPropertiesValues()
    {
        switch (trigger.get().getType())
        {
            case PERIODIC:
                PeriodicTrigger periodicTrigger = (PeriodicTrigger)trigger.get();
                otherProperties.get(TriggerInfoPropertyName.startDate.name()).setValue(
                        formatters.formatDateTime(periodicTrigger.getStartDate()));
                Periods period = periodicTrigger.getPeriod();
                if (null == period)
                {
                    otherProperties.get(TriggerInfoPropertyName.period.name()).setValue(
                            formatters.formatDateTimeInterval(periodicTrigger.getInterval()));
                }
                else
                {
                    otherProperties.get(TriggerInfoPropertyName.period.name()).setValue(
                            factory.getPeriods().get(period.toString()));
                }
                otherProperties.get(TriggerInfoPropertyName.strategy.name()).setValue(
                        factory.getCalculateStrategies().get(periodicTrigger.getStrategy().toString()));
                setRandomizePeriodicValue(periodicTrigger, period);
                break;
            case CONCRETE_DATE:
                setRandomizeValue();
                break;
            default:
                throw new FxException("Unsupported TriggerType enum value:" + trigger.get().getType());
        }
        Date planExecutionDate = trigger.get().getPlanExecutionDate();
        String formatDateTime = null == planExecutionDate ? StringUtilities.EMPTY : formatters
                .formatDateTime(planExecutionDate);
        otherProperties.get(TriggerInfoPropertyName.planDate.name()).setValue(formatDateTime);
    }

    private void setRandomizePeriodicValue(PeriodicTrigger periodicTrigger, @Nullable Periods period)
    {
        if (!sharedSettings.useRandomizeDelay())
        {
            return;
        }
        if (period != null && CalculateStrategies.FROM_START.toString()
                .equals(periodicTrigger.getStrategy().toString()))
        {
            setRandomizeValue();
        }
        else
        {
            otherProperties.remove(TriggerInfoPropertyName.randomizeDelayOn.name());
            otherPropertyRegistrations.get(TriggerInfoPropertyName.randomizeDelayOn.name()).unregister();
        }
    }

    private void setRandomizeValue()
    {
        if (!sharedSettings.useRandomizeDelay())
        {
            return;
        }
        otherProperties.get(TriggerInfoPropertyName.randomizeDelayOn.name())
                .setValue(getOnPropertyValue(trigger.get().isRandomizeDelayOn()));
    }
}
