package ru.naumen.metainfoadmin.client.eventcleaner.rule;

import java.util.Collection;

import com.google.gwt.cell.client.Cell;
import com.google.gwt.cell.client.HasCell;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.gwt.place.shared.Place;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.cellopt.CellWidgetOptions;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.tree.adapter.HasValueAdapter;
import ru.naumen.core.client.tree.adapter.HierarchicalMultiAdapter;
import ru.naumen.core.client.tree.cell.ContentTreeCell;
import ru.naumen.core.client.tree.cell.ContentTreeCellFactory;
import ru.naumen.core.client.tree.datasource.ITreeDataSource;
import ru.naumen.core.client.tree.datasource.TreeDataSourceFactory;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithoutFolders;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassTreeComparator;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassTreeSelModelGinModule;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassTreeViewModelImpl;
import ru.naumen.core.client.tree.metainfo.HierarchicalHierarchyCallback;
import ru.naumen.core.client.tree.metainfo.HierarchicalMetaClassHierarchyProviderImpl;
import ru.naumen.core.client.tree.metainfo.MetaClassHierarchyProvider;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactoryImpl;
import ru.naumen.core.client.tree.metainfo.helper.MetaClassesSelectionModelFactory;
import ru.naumen.core.client.tree.metainfo.helper.MetaClassesSelectionModelFactoryDefault;
import ru.naumen.core.client.tree.selector.DtoTreeSelectorGinModule;
import ru.naumen.core.client.widgets.columns.PlaceProvider;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.client.widgets.tree.ValueCellTree;
import ru.naumen.core.client.widgets.tree.ValueCellTreeGinjector.ValueCellTreeCheckBoxHasCellFactory;
import ru.naumen.core.client.widgets.tree.ValueCellTreeGinjector.ValueCellTreeFactory;
import ru.naumen.core.client.widgets.tree.cell.ValueTreeDefaultCellFactory;
import ru.naumen.core.client.widgets.tree.cell.ValueTreeDefaultCellFactoryMultiImpl;
import ru.naumen.core.client.widgets.tree.cell.WidgetTreeCellGinModule.WithoutRemoved;
import ru.naumen.core.client.widgets.tree.dto.DtoPopupMultiValueCellTree;
import ru.naumen.core.client.widgets.tree.hascell.ValueCellTreeDisablableCheckBoxHasCell;
import ru.naumen.core.client.widgets.tree.hascell.content.DtoValueCellTreeContentHasCell;
import ru.naumen.core.client.widgets.tree.hascell.content.ValueCellTreeContentHasCell;
import ru.naumen.core.client.widgets.tree.hascell.content.ValueCellTreeContentHasCellFactory;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.eventcleaner.rule.EventStorageRule;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.card.EventStorageRuleCardPresenter;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.card.EventStorageRulePlace;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.commands.DeleteEventStorageRulesCommand;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.commands.DisableEventStorageRulesCommand;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.commands.EditEventStorageRuleCommand;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.commands.EnableEventStorageRulesCommand;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.commands.EventStorageRuleCommandFactoryInitializer;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.commands.ToggleEventStorageRuleCommand;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDto3StateCheckBoxHasCell;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDtoTreeDataSource;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDtoTreeDataSourceFactory;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDtoTreePropertyFactory;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDtoTreePropertyFactoryImpl;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDtoTreeSelectionModel;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDtoTreeSelectionModelFactory;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDtoTreeViewModelFactory;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDtoValueAdapter;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.PopupEventsDtoMultiValueCellTree;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.metaclasstree.MetaClassSelectPropertyFactory;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.metaclasstree.MetaClassSelectionModel;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.metaclasstree.MetaClassTreeComparatorImpl;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.metaclasstree.MetaClassTreeContext;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.metaclasstree.MetaClassTreeFactory;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.metaclasstree.MetaClassTreeSource;

/**
 * Модуль настройки правил хранения логов событий
 * <AUTHOR>
 * @since 09.07.2023
 */
public class EventStorageRuleGinModule extends AbstractGinModule
{
    public static class AllDtObjectProvider implements Provider<DtObject>
    {
        @Inject
        private CommonMessages messages;

        @Override
        public DtObject get()
        {
            SimpleDtObject dto = new SimpleDtObject(CellWidgetOptions.ALL_OPTION, messages.all(), Constants.EMPTY_FQN);
            dto.setProperty(Constants.DtoTree.IS_LEAF, true);
            return dto;
        }
    }

    public static final String SELECT_ALL = "selectAll";
    public static final String METACLASS_TREE = "eventMetaClassTree";

    @Override
    protected void configure()
    {
        bind(EventStorageRuleCardPresenter.class);
        bind(EventStorageRuleCommandFactoryInitializer.class).asEagerSingleton();

        install(new GinFactoryModuleBuilder().implement(Place.class, EventStorageRulePlace.class)
                .build(new TypeLiteral<PlaceProvider<EventStorageRule>>()
                {
                }));

        bind(DtObject.class)
                .annotatedWith(Names.named(SELECT_ALL))
                .toProvider(AllDtObjectProvider.class)
                .in(Singleton.class);

        configureCommands();
        configureEventsTree();
        configureMetaClassTree();
    }

    private void configureCommands()
    {
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, DeleteEventStorageRulesCommand.class)
                .build(new TypeLiteral<CommandProvider<DeleteEventStorageRulesCommand,
                        CommandParam<Collection<DtObject>, Void>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditEventStorageRuleCommand.class)
                .build(new TypeLiteral<CommandProvider<EditEventStorageRuleCommand, CommandParam<DtObject, DtObject>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, ToggleEventStorageRuleCommand.class)
                .build(new TypeLiteral<CommandProvider<ToggleEventStorageRuleCommand, CommandParam<DtObject,
                        DtObject>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EnableEventStorageRulesCommand.class)
                .build(new TypeLiteral<CommandProvider<EnableEventStorageRulesCommand,
                        CommandParam<Collection<DtObject>, Void>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, DisableEventStorageRulesCommand.class)
                .build(new TypeLiteral<CommandProvider<DisableEventStorageRulesCommand,
                        CommandParam<Collection<DtObject>, Void>>>()
                {
                }));
    }

    private void configureEventsTree()
    {
        //@formatter:off
        install(Gin.bind(
                new TypeLiteral<ValueTreeDefaultCellFactory<DtObject, EventsDtoTreeSelectionModel, WithoutFolders, WithoutRemoved>>(){},
                new TypeLiteral<ValueTreeDefaultCellFactoryMultiImpl<DtObject, EventsDtoTreeSelectionModel, WithoutFolders, WithoutRemoved>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(new TypeLiteral<HasCell<DtObject, ?>>(){}, new TypeLiteral<EventsDto3StateCheckBoxHasCell<DtObject,  EventsDtoTreeSelectionModel>>(){})
                .build(new TypeLiteral<ValueCellTreeCheckBoxHasCellFactory<DtObject, EventsDtoTreeSelectionModel>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(
                        new TypeLiteral<ValueCellTreeContentHasCell<DtObject, EventsDtoTreeSelectionModel>>(){},
                        new TypeLiteral<DtoValueCellTreeContentHasCell<EventsDtoTreeSelectionModel, WithoutFolders, WithoutRemoved>>(){})
                .build(new TypeLiteral<ValueCellTreeContentHasCellFactory<DtObject, EventsDtoTreeSelectionModel, WithoutFolders, WithoutRemoved>>(){}));

        install(Gin.install(
                new TypeLiteral<ContentTreeCellFactory<DtObject, EventsDtoTreeSelectionModel, WithoutFolders, WithoutRemoved>>(){},
                new TypeLiteral<Cell<DtObject>>(){},
                new TypeLiteral<ContentTreeCell<DtObject, EventsDtoTreeSelectionModel>>(){}));

        install(Gin.bind(
                new TypeLiteral<EventsDtoTreeSelectionModel>(){},
                null));

        install(Gin.bind(
                new TypeLiteral<PopupValueCellTree<DtObject, Collection<DtObject>, EventsDtoTreeSelectionModel>>(){},
                new TypeLiteral<PopupEventsDtoMultiValueCellTree>(){}));

        install(Gin.install(
                new TypeLiteral<ValueCellTreeFactory<DtObject,Collection<DtObject>, EventsDtoTreeSelectionModel>>(){},
                new TypeLiteral<ValueCellTree<DtObject, Collection<DtObject>, EventsDtoTreeSelectionModel>>(){},
                new TypeLiteral<ValueCellTree<DtObject, Collection<DtObject>, EventsDtoTreeSelectionModel>>(){}));

        install(Gin.bindSingleton(
                new TypeLiteral<HasValueAdapter<DtObject, Collection<DtObject>, EventsDtoTreeSelectionModel>>(){},
                new TypeLiteral<EventsDtoValueAdapter>(){}));

        install(Gin.bindSingleton(
                new TypeLiteral<EventsDtoTreePropertyFactory>(){},
                new TypeLiteral<EventsDtoTreePropertyFactoryImpl>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(EventsDtoTreeSelectionModel.class, EventsDtoTreeSelectionModel.class)
                .build(EventsDtoTreeSelectionModelFactory.class));

        install(new GinFactoryModuleBuilder()
                .implement(EventsDtoTreeDataSource.class, EventsDtoTreeDataSource.class)
                .build(EventsDtoTreeDataSourceFactory.class));

        install(new GinFactoryModuleBuilder()
                .implement(EventsDtoTreeSelectionModel.class, EventsDtoTreeSelectionModel.class)
                .build(EventsDtoTreeViewModelFactory.class));
        //@formatter:on
    }

    private void configureMetaClassTree()
    {
        //@formatter:off
        bind(new TypeLiteral<MetaClassSelectPropertyFactory>(){})
                .annotatedWith(Names.named(METACLASS_TREE)).to(MetaClassTreeFactory.class);

        install(Gin.install(
                new TypeLiteral<TreeDataSourceFactory<DtObject, MetaClassTreeContext>>(){},
                new TypeLiteral<ITreeDataSource<DtObject>>(){},
                new TypeLiteral<MetaClassTreeSource>(){}));

        bind(new TypeLiteral<DtoMetaClassTreeViewModelImpl<MetaClassSelectionModel, MetaClassTreeContext>>(){});

        install(Gin.bind(
                new TypeLiteral<MetaClassHierarchyProvider<MetaClassTreeContext>>(){},
                new TypeLiteral<HierarchicalMetaClassHierarchyProviderImpl<MetaClassTreeContext>>(){}));

        install(Gin.bindSingleton(
                new TypeLiteral<DtoMetaClassTreeComparator<MetaClassTreeContext>>(){},
                new TypeLiteral<MetaClassTreeComparatorImpl>(){}));

        install(DtoTreeSelectorGinModule.multi(new TypeLiteral<MetaClassSelectionModel>(){}));

        install(Gin.bindSingleton(
                new TypeLiteral<DtoMetaClassesTreeFactory<MetaClassSelectionModel, MetaClassTreeContext>>(){},
                new TypeLiteral<DtoMetaClassesTreeFactoryImpl<MetaClassSelectionModel, MetaClassTreeContext>>(){}));

        install(Gin.bindSingleton(
                new TypeLiteral<MetaClassesSelectionModelFactory<MetaClassSelectionModel, MetaClassTreeContext>>(){},
                new TypeLiteral<MetaClassesSelectionModelFactoryDefault<MetaClassSelectionModel, MetaClassTreeContext>>(){}));

        install(Gin.bind(
                new TypeLiteral<ValueTreeDefaultCellFactory<DtObject, MetaClassSelectionModel, WithoutFolders, WithoutRemoved>>(){},
                new TypeLiteral<ValueTreeDefaultCellFactoryMultiImpl<DtObject, MetaClassSelectionModel, WithoutFolders, WithoutRemoved>>(){}));

        install(Gin.install(
                new TypeLiteral<ValueCellTreeContentHasCellFactory<DtObject, MetaClassSelectionModel, WithoutFolders, WithoutRemoved>>(){},
                new TypeLiteral<ValueCellTreeContentHasCell<DtObject, MetaClassSelectionModel>>(){},
                new TypeLiteral<DtoValueCellTreeContentHasCell<MetaClassSelectionModel, WithoutFolders, WithoutRemoved>>(){}));

        install(Gin.install(
                new TypeLiteral<ContentTreeCellFactory<DtObject, MetaClassSelectionModel, WithoutFolders, WithoutRemoved>>(){},
                new TypeLiteral<Cell<DtObject>>(){},
                new TypeLiteral<ContentTreeCell<DtObject, MetaClassSelectionModel>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(new TypeLiteral<HasCell<DtObject, ?>>(){}, new TypeLiteral<ValueCellTreeDisablableCheckBoxHasCell<DtObject, MetaClassSelectionModel>>(){})
                .build(new TypeLiteral<ValueCellTreeCheckBoxHasCellFactory<DtObject, MetaClassSelectionModel>>(){}));

        install(DtoMetaClassTreeSelModelGinModule.create(new TypeLiteral<MetaClassSelectionModel>(){})
                .setHierarchyCallback(new TypeLiteral<HierarchicalHierarchyCallback<MetaClassSelectionModel>>(){}));

        install(Gin.bind(
                new TypeLiteral<PopupValueCellTree<DtObject, Collection<DtObject>, MetaClassSelectionModel>>(){},
                new TypeLiteral<DtoPopupMultiValueCellTree<MetaClassSelectionModel>>(){}));

        install(Gin.install(
                new TypeLiteral<ValueCellTreeFactory<DtObject,Collection<DtObject>, MetaClassSelectionModel>>(){},
                new TypeLiteral<ValueCellTree<DtObject, Collection<DtObject>, MetaClassSelectionModel>>(){},
                new TypeLiteral<ValueCellTree<DtObject, Collection<DtObject>, MetaClassSelectionModel>>(){}));

        install(Gin.bindSingleton(
                new TypeLiteral<HasValueAdapter<DtObject, Collection<DtObject>, MetaClassSelectionModel>>(){},
                new TypeLiteral<HierarchicalMultiAdapter<DtObject, MetaClassSelectionModel>>(){}));
        //@formatter:on
    }
}
