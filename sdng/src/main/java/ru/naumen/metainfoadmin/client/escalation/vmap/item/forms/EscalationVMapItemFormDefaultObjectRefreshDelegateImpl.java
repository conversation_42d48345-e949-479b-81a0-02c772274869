/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;

import com.google.common.collect.Collections2;
import com.google.common.collect.Sets;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;
import com.google.inject.Singleton;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.GetEscalationSchemesAction;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
@Singleton
public class EscalationVMapItemFormDefaultObjectRefreshDelegateImpl implements
        PropertyDelegateRefresh<SelectItem, ListBoxWithEmptyOptProperty>
{
    @Inject
    DispatchAsync dispatch;
    @Inject
    I18nUtil i18nUtil;

    @Override
    public void refreshProperty(final PropertyContainerContext context, final ListBoxWithEmptyOptProperty property,
            final AsyncCallback<Boolean> callback)
    {
        Collection<DtObject> linkedMetaClasses = context.getPropertyValues().getProperty(
                ValueMapCatalogItem.LINKED_CLASSES);
        HashSet<ClassFqn> targetTypes = Sets.newHashSet(Collections2.transform(linkedMetaClasses,
                DtObject.FQN_EXTRACTOR));
        dispatch.execute(new GetEscalationSchemesAction(targetTypes),
                new BasicCallback<SimpleResult<ArrayList<DtoContainer<EscalationScheme>>>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<ArrayList<DtoContainer<EscalationScheme>>> response)
                    {
                        SingleSelectCellList<String> widget = property.getValueWidget();
                        widget.clear();
                        response.get().stream()
                                .map(DtoContainer::get)
                                .forEach(scheme ->
                                        widget.addItem(i18nUtil.getLocalizedTitle(scheme), scheme.getCode()));
                        String defaultValue = context.getPropertyValues()
                                .getProperty(ValueMapCatalogItem.DEFAULT_OBJECT);
                        if (defaultValue != null)
                        {
                            widget.setObjValue(defaultValue);
                        }
                        callback.onSuccess(true);
                    }
                });
    }
}