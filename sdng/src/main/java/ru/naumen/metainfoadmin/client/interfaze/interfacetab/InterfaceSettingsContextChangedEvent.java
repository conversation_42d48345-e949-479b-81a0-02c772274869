package ru.naumen.metainfoadmin.client.interfaze.interfacetab;

import java.util.List;

import ru.naumen.core.shared.dispatch.GetInterfaceTabDataResponse;
import ru.naumen.core.shared.interfacesettings.InterfaceSettings;
import ru.naumen.core.shared.personalsettings.ThemeClient;

import com.google.gwt.event.shared.GwtEvent;

/**
 * <AUTHOR>
 * @since 10 авг. 2016 г.
 *
 */
public class InterfaceSettingsContextChangedEvent extends GwtEvent<InterfaceSettingsContextChangedHandler>
{
    private static Type<InterfaceSettingsContextChangedHandler> TYPE =
            new Type<InterfaceSettingsContextChangedHandler>();

    public static Type<InterfaceSettingsContextChangedHandler> getType()
    {
        return TYPE;
    }

    private final InterfaceSettings settings;
    private final List<ThemeClient> themes;

    public InterfaceSettingsContextChangedEvent(GetInterfaceTabDataResponse response)
    {
        this.settings = response.getSettings();
        this.themes = response.getThemes();
    }

    @Override
    public GwtEvent.Type<InterfaceSettingsContextChangedHandler> getAssociatedType()
    {
        return TYPE;
    }

    public InterfaceSettings getSettings()
    {
        return settings;
    }

    public List<ThemeClient> getThemes()
    {
        return themes;
    }

    @Override
    protected void dispatch(InterfaceSettingsContextChangedHandler handler)
    {
        handler.onInterfaceSettingsContextChanged(this);
    }
}