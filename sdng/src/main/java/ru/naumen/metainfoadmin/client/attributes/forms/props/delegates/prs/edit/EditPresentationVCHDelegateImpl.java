package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.edit;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.Arrays;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * <AUTHOR>
 */
public class EditPresentationVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        String attrEditPrs = context.getPropertyValues().getProperty(EDIT_PRS);
        if (attrEditPrs.equals(Presentations.QUICK_SELECTION_FIELD))
        {
            context.setProperty(COMPLEX_RELATION, Boolean.FALSE.toString());
            context.setProperty(EDIT_ON_COMPLEX_FORM_ONLY, false);
        }
        context.getRefreshProcess().startCustomProcess(
                Arrays.asList(SUGGEST_CATALOG, SELECT_SORTING, INPUTMASK_MODE, INPUTMASK, DEFAULT_VALUE,
                        COMPLEX_RELATION, COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW, COMPLEX_RELATION_ATTR_GROUP,
                        EDIT_ON_COMPLEX_FORM_ONLY, STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}
