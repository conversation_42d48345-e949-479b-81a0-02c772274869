package ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem;

import java.util.ArrayList;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.widgets.properties.container.AttributePropertyDescription;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DeleteTopMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DisableTopMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EditTopMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EnableTopMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsTMCommandParam;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;

/**
 * Презентер списка атрибутов элемента верхнего меню
 * <AUTHOR>
 * @since 16.07.2020
 */
public class NavigationTopMenuItemAttributesPresenter
        extends NavigationMenuItemAttributesPresenter<MenuItem, NavigationMenuItemContext<MenuItem>,
        NavigationSettingsTMCommandParam>
{
    @Inject
    public NavigationTopMenuItemAttributesPresenter(InfoDisplay display, EventBus eventBus,
            @Named(NavigationMenuItemGinModule.NAVIGATION_MENU_ITEM_ATTRIBUTES)
            ArrayList<AttributePropertyDescription<?, NavigationMenuItemContext<MenuItem>>> properties,
            NavigationSettingsMessages messages,
            ButtonFactory buttonFactory, CommonMessages cmessages,
            PlaceController placeController, TagsMessages tagsMessages)
    {
        super(display, eventBus, properties, messages, buttonFactory, cmessages, placeController, tagsMessages);
    }

    protected NavigationSettingsTMCommandParam getParam(AsyncCallback<DtoContainer<NavigationSettings>> refreshCallback)
    {
        return new NavigationSettingsTMCommandParam(context.getSettings(), context.getItem(), refreshCallback);
    }

    protected MenuItem getMenuItem(DtoContainer<NavigationSettings> settings)
    {
        return settings.get().findTopMenuItem(context.getItem().getCode());
    }

    protected String getDeleteCommandId()
    {
        return DeleteTopMenuItemCommand.ID;
    }

    protected String getEditCommandId()
    {
        return EditTopMenuItemCommand.ID;
    }

    protected String getDisableCommandId()
    {
        return DisableTopMenuItemCommand.ID;
    }

    protected String getEnableCommandId()
    {
        return EnableTopMenuItemCommand.ID;
    }
}