package ru.naumen.metainfoadmin.client.embeddedapplications.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfoadmin.client.embeddedapplications.form.EditApplicationFormPresenter;

/**
 * <AUTHOR>
 * @since 07.07.2016
 *
 */
public class EditEmbeddedApplicationCommand extends EmbeddedApplicationPresenterCommandBase
{
    @Inject
    private final Provider<EditApplicationFormPresenter> formProvider;

    @Inject
    // @formatter:off
    public EditEmbeddedApplicationCommand(
            @Assisted CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto> param,
            Provider<EditApplicationFormPresenter> formProvider)
    // @formatter:on
    {
        super(param);
        this.formProvider = formProvider;
    }

    @Override
    public void execute(
            final CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto> value)
    {
        super.execute(new CommandParam<>(param.getValue(),
                param.getCallback()));
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected CallbackPresenter<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto> getPresenter(
            EmbeddedApplicationAdminSettingsDto value)
    {
        EditApplicationFormPresenter presenter = formProvider.get();
        return presenter;
    }
}
