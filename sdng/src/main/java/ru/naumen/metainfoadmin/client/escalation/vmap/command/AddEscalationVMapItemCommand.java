/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.command;

import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.command.AddCatalogItemCommand;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogItemCommandParam;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormFactory;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.forms.EscalationValueMapItemFormContext;

/**
 * Команда добавления элемента таблицы соответствий эскалации.
 *
 * <AUTHOR>
 * @since 30.10.2012
 *
 */
public class AddEscalationVMapItemCommand extends AddCatalogItemCommand
{
    private final CatalogItemFormFactory<EscalationValueMapItemFormContext> formFactory;

    @Inject
    public AddEscalationVMapItemCommand(@Assisted CatalogItemCommandParam<DtObject> param,
            CatalogItemFormFactory<EscalationValueMapItemFormContext> formFactory)
    {
        super(param);
        this.formFactory = formFactory;
    }

    @Override
    protected CallbackPresenter<DtObject, IProperties> getPresenter(DtObject value)
    {
        EscalationValueMapItemFormContext context = formFactory.createContext(getCatalog(), isFolder(value), value);
        return formFactory.createAddForm(context);
    }
}