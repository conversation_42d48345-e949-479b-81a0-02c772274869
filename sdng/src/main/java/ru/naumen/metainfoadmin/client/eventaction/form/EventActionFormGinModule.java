package ru.naumen.metainfoadmin.client.eventaction.form;

import java.util.Collection;
import java.util.HashSet;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.tree.adapter.HasValueAdapter;
import ru.naumen.core.client.tree.adapter.MultiAdapter;
import ru.naumen.core.client.tree.cell.ContentFilteredTreeCell;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactoryGinModule;
import ru.naumen.core.client.tree.dto.factory.DtoTreeViewModelGinModule;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassHierarchicalMetaClassTreeContext;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.tree.selection.HierarchicalMetaClassMultiSelectionModel;
import ru.naumen.core.client.tree.selector.DtoTreeSelectorGinModule;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.client.widgets.tree.ValueCellTreeMultiGinModule;
import ru.naumen.core.client.widgets.tree.cell.WidgetTreeCellGinModule;
import ru.naumen.core.client.widgets.tree.dto.DtoPopupMultiValueCellTree;
import ru.naumen.core.client.widgets.tree.hascell.ValueCellTreeCheckBoxFilteredHasCell;
import ru.naumen.core.client.widgets.tree.hascell.content.DtoValueCellTreeContentHasCell;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfoadmin.client.eventaction.ActionConditionCreator;
import ru.naumen.metainfoadmin.client.eventaction.ActionConditionCreatorFactoryInj;
import ru.naumen.metainfoadmin.client.eventaction.form.attrtree.EventAttributesSelectionModel;
import ru.naumen.metainfoadmin.client.eventaction.form.attrtree.EventAttributesTreeDataSource;
import ru.naumen.metainfoadmin.client.eventaction.form.attrtree.EventAttributesTreeFactoryContext;
import ru.naumen.metainfoadmin.client.eventaction.form.attrtree.EventAttributesTreeViewModelFactoryImpl;
import ru.naumen.metainfoadmin.client.eventaction.form.creator.EmptyEventActionFormPropertiesCreator;
import ru.naumen.metainfoadmin.client.eventaction.form.creator.ScriptActionConditionCreator;
import ru.naumen.metainfoadmin.client.eventaction.form.creator.ScriptEventActionFormPropertiesCreator;
import ru.naumen.metainfoadmin.client.eventaction.form.creator.event.PlannedEventFormPropertiesCreator;
import ru.naumen.metainfoadmin.client.eventaction.form.creator.event.UserEventPropertiesCreator;

/**
 * <AUTHOR>
 * @since 02.08.2012
 */
public class EventActionFormGinModule extends AbstractGinModule
{
    //@formatter:off
    public static class EventAttributesTree extends DtoTreeGinModule.DtoTree {}
    //@formatter:on

    public interface EventAttributesSelectionModelFactory
    {
        EventAttributesSelectionModel create();
    }

    public static class EventActionTreeFactory implements SelectFqnsPropertyFactory
    {
        @Inject
        EventActionMessages messages;
        @Inject
        protected CommonMessages cmessages;
        @Inject
        PopupValueCellTreeFactory<DtObject, Collection<DtObject>, HierarchicalMetaClassMultiSelectionModel<DtObject>> treeFactory;
        @Inject
        DtoMetaClassesTreeFactory<HierarchicalMetaClassMultiSelectionModel<DtObject>,
                DtoMetaClassHierarchicalMetaClassTreeContext> treeModelHelper;

        @Override
        public Property<Collection<DtObject>> create(@Nullable EventAction eventAction)
        {
            return new PropertyBase<>(
                    cmessages.objects(), treeFactory.create(treeModelHelper.createMetaClassTreeViewModel(Container
                    .create(new DtoMetaClassHierarchicalMetaClassTreeContext(AbstractBO.FQN,
                            MetaClassFilters.isNotSystem(),
                            Lists.newArrayList(ru.naumen.core.shared.Constants.File.FQN), new HashSet<>())))));
        }
    }

    public static final String EVENT_ACTION_TREE = "eventActionTree";

    @Override
    protected void configure()
    {
        //@formatter:off
        install(ValueCellTreeMultiGinModule.create(DtObject.class, new TypeLiteral<Collection<DtObject>>(){}, new TypeLiteral<EventAttributesSelectionModel>(){}, DtoTreeGinModule.WithoutFolders.class, WidgetTreeCellGinModule.WithoutRemoved.class)
                .setCheckBoxCell(new TypeLiteral<ValueCellTreeCheckBoxFilteredHasCell<DtObject,  EventAttributesSelectionModel>>(){})
                .setTreeContentCell(new TypeLiteral<DtoValueCellTreeContentHasCell<EventAttributesSelectionModel, DtoTreeGinModule.WithoutFolders, WidgetTreeCellGinModule.WithoutRemoved>>(){})
                .setPopupValueCellTree(new TypeLiteral<DtoPopupMultiValueCellTree< EventAttributesSelectionModel>>(){})
                .setValueCell(new TypeLiteral<ContentFilteredTreeCell<DtObject, EventAttributesSelectionModel, DtoTreeGinModule.WithoutFolders>>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(EventAttributesSelectionModel.class, EventAttributesSelectionModel.class)
                .build(EventAttributesSelectionModelFactory.class));
        install(DtoTreeSelectorGinModule.multi(new TypeLiteral<EventAttributesSelectionModel>(){}));
        install(Gin.bindSingleton(
                new TypeLiteral<HasValueAdapter<DtObject, Collection<DtObject>, EventAttributesSelectionModel>>(){},
                new TypeLiteral<MultiAdapter<DtObject, EventAttributesSelectionModel>>(){}));
        install(DtoTreeViewModelGinModule.create(EventAttributesTree.class)
                .setMultiSelModel(new TypeLiteral<EventAttributesSelectionModel>(){}));
        install(DtoTreeFactoryGinModule.multi(
                        EventAttributesTree.class, DtoTreeGinModule.WithoutFolders.class,
                        EventAttributesTreeFactoryContext.class, new TypeLiteral<EventAttributesSelectionModel>(){})
                .setDtoTreeViewModelFactory(new TypeLiteral<EventAttributesTreeViewModelFactoryImpl>(){})
                .setDataSource(new TypeLiteral<EventAttributesTreeDataSource>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(ActionConditionCreator.class, ScriptActionConditionCreator.class)
                .build(ActionConditionCreatorFactoryInj.class));
        bind(new TypeLiteral<SelectFqnsPropertyFactory>(){})
                .annotatedWith(Names.named(EVENT_ACTION_TREE))
                .to(EventActionTreeFactory.class);
        //@formatter:on
        bind(AddEventActionFormPresenter.class);
        bind(EditEventActionFormPresenter.class);
        bind(EventActionFormDisplay.class).to(EventActionFormDisplayImpl.class);
        bind(ScriptEventActionFormPropertiesCreator.class);
        bind(EmptyEventActionFormPropertiesCreator.class);
        bind(PlannedEventFormPropertiesCreator.class);
        bind(UserEventPropertiesCreator.class);
    }
}
