package ru.naumen.metainfoadmin.client.attributes.forms;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.Collection;
import java.util.Map;

import java.util.HashMap;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.validation.DateTimeRestrictionConditionValidator;
import ru.naumen.core.client.validation.NotEmptyObjectValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.PositiveIntegerBetweenValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.FootedTextBoxProperty;
import ru.naumen.core.client.widgets.properties.IntegerBoxInfoProperty;
import ru.naumen.core.client.widgets.properties.LabelProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.RichTextAreaProperty;
import ru.naumen.metainfoadmin.client.widgets.properties.ScriptComponentEditProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.ValueCellListProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindTextBoxFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySync;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.CommonRestriction;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.props.DefaultValuePropertyControllerFactory;
import ru.naumen.metainfoadmin.client.attributes.forms.props.PermittedTypesPropertyControllerFactory;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.RichTextAreaBindDeletageImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeCommonRestrictionValidator;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.digitsrestriction.DigitsCountRestrictionBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.digitsrestriction.DigitsCountRestrictionRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.digitsrestriction.DigitsCountRestrictionVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskModeBindDeletageImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskModeRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskModeVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskVCHDelegateImpl;

/**
 * Абстрактная реализация {@link PropertyControllerFactorySync} для форм добавления/удаления
 * атрибутов и параметров настраиваемых форм
 *
 * <AUTHOR>
 * @since 20 мая 2016 г.
 */
public abstract class AbstractFormPropertyControllerFactorySyncImpl<F extends ObjectForm>
        extends PropertyControllerFactorySyncImpl<Attribute, F>
{
    @Inject
    PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<SelectItem, ListBoxProperty> listBoxPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Boolean, BooleanCheckBoxProperty> booleanPropertyFactory;
    @Inject
    PropertyDelegateBindTextBoxFactory textBoxBindDelegateFactory;
    @Inject
    PermittedTypesPropertyControllerFactory<F> permittedTypesPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<ScriptDto, ScriptComponentEditProperty> scriptEditorPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<ScriptDto, ScriptComponentEditProperty> dateTimeRestrictionScriptEditorPropertyFactory;
    @Inject
    DefaultValuePropertyControllerFactory<F> defaultValuePropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<String, LabelProperty> labelPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<String, RichTextAreaProperty> richTextAreaPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Collection<MetaClassLite>, ValueCellListProperty<MetaClassLite>> multiCheckBoxesPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Collection<CommonRestriction>, ValueCellListProperty<CommonRestriction>> dateTimeCommonRestrictionsPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<String, FootedTextBoxProperty> footedTextPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Long, IntegerBoxInfoProperty> integerBoxInfoPropertyFactory;

    @Inject
    @Named(ATTR_TYPE)
    AttributeFormPropertyDelegateVCH<F> attrTypeVCHDelegate;
    @Inject
    @Named(REQUIRED)
    AttributeFormPropertyDelegateVCH<F> requiredVCHDelegate;
    @Inject
    @Named(EDITABLE)
    AttributeFormPropertyDelegateVCH<F> editableVCHDelegate;
    @Inject
    @Named(PERMITTED_TYPES)
    AttributeFormPropertyDelegateVCH<F> permittedTypesVCHDelegate;
    @Inject
    @Named(FILTERED_BY_SCRIPT)
    AttributeFormPropertyDelegateVCH<F> filteredByScriptVCHDelegate;
    @Inject
    @Named(COMPUTABLE_ON_FORM)
    AttributeFormPropertyDelegateVCH<F> computableOnFormVCHDelegate;
    @Inject
    @Named(SHOW_PRS)
    AttributeFormPropertyDelegateVCH<F> showPrsVCHDelegate;
    @Inject
    @Named(EDIT_PRS)
    AttributeFormPropertyDelegateVCH<F> editPrsVCHDelegate;
    @Inject
    @Named(SUGGEST_CATALOG)
    AttributeFormPropertyDelegateVCH<F> suggestCatalogVCHDelegate;
    @Inject
    @Named(SELECT_SORTING)
    AttributeFormPropertyDelegateVCH<F> selectSortVCHDelegate;
    @Inject
    @Named(DEFAULT_BY_SCRIPT)
    AttributeFormPropertyDelegateVCH<F> defaultByScriptVCHDelegate;
    @Inject
    @Named(TARGET_CLASS)
    AttributeFormPropertyDelegateVCH<F> targetClassVCHDelegate;
    @Inject
    @Named(TARGET_CATALOG)
    AttributeFormPropertyDelegateVCH<F> targetCatalogVCHDelegate;
    @Inject
    @Named(DIRECT_LINK_TARGET)
    AttributeFormPropertyDelegateVCH<F> directLinkVCHDelegate;
    @Inject
    @Named(AGGREGATE_CLASSES)
    AttributeFormPropertyDelegateVCH<F> aggregateClassesVCHDelegate;
    @Inject
    InputmaskVCHDelegateImpl<F> inputmaskVCHDelegate;
    @Inject
    InputmaskModeVCHDelegateImpl<F> inputmaskModeVCHDelegate;

    @Inject
    @Named(REQUIRED)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> requiredRefreshDelegate;
    @Inject
    @Named(EDITABLE)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> editableRefreshDelegate;
    @Inject
    @Named(FILTERED_BY_SCRIPT)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> filteredByScriptRefreshDelegate;
    @Inject
    @Named(SCRIPT_FOR_FILTRATION)
    AttributeFormPropertyDelegateRefresh<F, ScriptDto, ScriptComponentEditProperty> scriptForFiltrationRefreshDelegate;

    @Inject
    @Named(DATE_TIME_COMMON_RESTRICTIONS)
    AttributeFormPropertyDelegateBind<F, Collection<CommonRestriction>, ValueCellListProperty<CommonRestriction>> dateTimeCommonRestrictionsBindDelegate;
    @Inject
    @Named(DATE_TIME_COMMON_RESTRICTIONS)
    AttributeFormPropertyDelegateRefresh<F, Collection<CommonRestriction>, ValueCellListProperty<CommonRestriction>> dateTimeCommonRestrictionsRefreshDelegate;
    @Inject
    @Named(DATE_TIME_RESTRICTION_SCRIPT)
    AttributeFormPropertyDelegateRefresh<F, ScriptDto, ScriptComponentEditProperty> dateTimeRestrictionScriptRefreshDelegate;
    @Inject
    @Named(DATE_TIME_RESTRICTION_ATTRIBUTE)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> dateTimeRestrictionAttributeRefreshDelegate;
    @Inject
    @Named(DATE_TIME_RESTRICTION_ATTRIBUTE)
    AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxProperty> dateTimeRestrictionAttributeBindDelegate;

    @Inject
    @Named(DATE_TIME_RESTRICTION_CONDITION)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> dateTimeRestrictionConditionRefreshDelegate;
    @Inject
    @Named(DATE_TIME_RESTRICTION_CONDITION)
    AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxProperty> dateTimeRestrictionConditionBindDelegate;

    @Inject
    @Named(DATE_TIME_RESTRICTION_TYPE)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> dateTimeRestrictionTypeRefreshDelegate;
    @Inject
    @Named(DATE_TIME_RESTRICTION_TYPE)
    AttributeFormPropertyDelegateVCH<F> dateTimeRestrictionTypeVCHDelegate;
    @Inject
    @Named(DATE_TIME_RESTRICTION_TYPE)
    AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxProperty> dateTimeRestrictionTypeBindDelegate;
    @Inject
    @Named(COMPUTABLE_ON_FORM_SCRIPT)
    AttributeFormPropertyDelegateBind<F, ScriptDto, ScriptComponentEditProperty> dateTimeRestrictionScriptBindDelegate;
    @Inject
    @Named(COMPUTABLE_ON_FORM)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> computableOnFormRefreshDelegate;
    @Inject
    @Named(HIDDEN_ATTR_CAPTION)
    AttributeFormPropertyDelegateBind<F, Boolean, BooleanCheckBoxProperty> hideCaptionOnFormBindDelegate;
    @Inject
    @Named(HIDDEN_ATTR_CAPTION)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> hideCaptionOnFormRefreshDelegate;
    @Inject
    @Named(COMPUTABLE_ON_FORM_SCRIPT)
    AttributeFormPropertyDelegateRefresh<F, ScriptDto, ScriptComponentEditProperty> valueOnEditScriptRefreshDelegate;
    @Inject
    @Named(SHOW_PRS)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> showPrsRefreshDelegate;
    @Inject
    @Named(EDIT_PRS)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> editPrsRefreshDelegate;
    @Inject
    @Named(SUGGEST_CATALOG)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> suggestCatalogRefreshDelegate;
    @Inject
    @Named(SELECT_SORTING)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> selectSortRefreshDelegate;
    @Inject
    @Named(DEFAULT_VALUE_LABEL)
    AttributeFormPropertyDelegateRefresh<F, String, LabelProperty> defaultValueLabelRefreshDelegate;
    @Inject
    @Named(SCRIPT_FOR_DEFAULT)
    AttributeFormPropertyDelegateRefresh<F, ScriptDto, ScriptComponentEditProperty> scriptForDefaultRefreshDelegate;
    @Inject
    @Named(DEFAULT_BY_SCRIPT)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> defaultByScriptRefreshDelegate;
    @Inject
    @Named(TARGET_CLASS)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> targetClassRefreshDelegate;
    @Inject
    @Named(TARGET_CATALOG)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> targetCatalogRefreshDelegate;
    @Inject
    @Named(DIRECT_LINK_TARGET)
    AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty> directLinkRefreshDelegate;
    @Inject
    InputmaskRefreshDelegateImpl<F> inputmaskRefreshDelegate;
    @Inject
    InputmaskModeRefreshDelegateImpl<F> inputmaskModeRefreshDelegate;
    @Inject
    DigitsCountRestrictionRefreshDelegateImpl<F> digitsCountRestrictionRefreshDelegate;
    @Inject
    DigitsCountRestrictionBindDelegateImpl<F> digitsCountRestrictionBindDelegate;
    @Inject
    DigitsCountRestrictionVCHDelegateImpl<F> digitsCountRestrictionVCHDelegate;

    @Inject
    @Named(AGGREGATE_CLASSES)
    AttributeFormPropertyDelegateBind<F, Collection<MetaClassLite>, ValueCellListProperty<MetaClassLite>> aggregateClassesBindDelegate;
    @Inject
    @Named(ATTR_TYPE)
    AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxProperty> attrTypeBindDelegate;
    @Inject
    @Named(COMPUTABLE_ON_FORM_SCRIPT)
    AttributeFormPropertyDelegateBind<F, ScriptDto, ScriptComponentEditProperty> compOfFormScriptBindDelegate;
    @Inject
    @Named(SCRIPT_FOR_FILTRATION)
    AttributeFormPropertyDelegateBind<F, ScriptDto, ScriptComponentEditProperty> filtrationScriptBindDelegate;
    @Inject
    @Named(SCRIPT_FOR_DEFAULT)
    AttributeFormPropertyDelegateBind<F, ScriptDto, ScriptComponentEditProperty> defaultValueScriptBindDelegate;
    @Inject
    @Named(HIDDEN_WHEN_NO_POSSIBLE_VALUES)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> hiddenWhenNoPossibleValuesRefreshDelegate;
    @Inject
    @Named(EDIT_ON_COMPLEX_FORM_ONLY)
    AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty> editOnComplexFormOnlyRefreshDelegate;

    @Inject
    RichTextAreaBindDeletageImpl<F> richTextAreaBindDelegate;

    @Inject
    InputmaskModeBindDeletageImpl<F> inputmaskModeBindDelegate;
    @Inject
    InputmaskBindDelegateImpl<F> inputmaskBindDelegate;

    protected final Map<Validator<String>, String> titleValidators = new HashMap<>();
    protected final Map<Validator<String>, String> codeValidators = new HashMap<>(); // NOSONAR
    protected final Map<Validator<String>, String> targetValidators = new HashMap<>();
    protected final Map<Validator<SelectItem>, String> selectListTargetValidators = new HashMap<>();
    protected final Map<Validator<Collection<SelectItem>>, String> selectListRelatedAttrsToExportValidators =
            new HashMap<>();
    private final Map<Validator<String>, String> inputmaskValidators = new HashMap<>();
    private final Map<Validator<Long>, String> digitsCountRestrictionValidators = new HashMap<>();
    private final Map<Validator<Collection<CommonRestriction>>, String> commonRestrictionValidators = new HashMap<>();
    private final Map<Validator<SelectItem>, String> attributeRestrictionValidators = new HashMap<>();
    private final Map<Validator<SelectItem>, String> attributeConditionValidators = new HashMap<>();

    @Inject
    public void setUpValidators(NotEmptyValidator notEmptyValidator,
            NotEmptyObjectValidator<SelectItem> notEmptySelectItemValidator,
            DateTimeCommonRestrictionValidator dateTimeCommonRestrictionValidator,
            DateTimeRestrictionConditionValidator notNullAttributeRestrictionValidator,
            DateTimeRestrictionConditionValidator notNullAttributeConditionValidatorsValidator,
            PositiveIntegerBetweenValidator integerBetweenValidator,
            NotEmptyObjectValidator<Collection<SelectItem>> notEmptyCollectionItemValidator)
    {
        integerBetweenValidator
                .setLowerBound(DIGITS_COUNT_RESTRICTION_FIELD_MIN_VALUE)
                .setUpperBound(DIGITS_COUNT_RESTRICTION_FIELD_MAX_VALUE);
        codeValidators.put(notEmptyValidator, AttributeFormValidationCode.DEFAULT);
        titleValidators.put(notEmptyValidator, AttributeFormValidationCode.DEFAULT);
        targetValidators.put(notEmptyValidator, AttributeFormValidationCode.DEFAULT);
        selectListTargetValidators.put(notEmptySelectItemValidator, AttributeFormValidationCode.DEFAULT);
        inputmaskValidators.put(notEmptyValidator, AttributeFormValidationCode.DEFAULT);
        digitsCountRestrictionValidators.put(integerBetweenValidator, AttributeFormValidationCode.DEFAULT);
        commonRestrictionValidators.put(dateTimeCommonRestrictionValidator, AttributeFormValidationCode.DEFAULT);
        attributeRestrictionValidators.put(notNullAttributeRestrictionValidator, AttributeFormValidationCode.DEFAULT);
        attributeConditionValidators.put(notNullAttributeConditionValidatorsValidator,
                AttributeFormValidationCode.DEFAULT);
        selectListRelatedAttrsToExportValidators.put(notEmptyCollectionItemValidator,
                AttributeFormValidationCode.DEFAULT);
    }

    @Override
    protected void build()
    {
        PropertyDelegateBind<String, TextBoxProperty> titleBindDelegate = textBoxBindDelegateFactory
                .create(Constants.MAX_TITLE_LENGTH);
        PropertyDelegateBind<String, TextBoxProperty> codeBindDelegate = textBoxBindDelegateFactory
                .create(Constants.MAX_ID_LENGTH);

        //@formatter:off
        register(TITLE, textBoxPropertyFactory)
                .setBindDelegate(titleBindDelegate)
                .setValidators(titleValidators);
        register(CODE, textBoxPropertyFactory)
                .setBindDelegate(codeBindDelegate)
                .setValidators(codeValidators);
        register(ATTR_TYPE, listBoxPropertyFactory)
                .setVchDelegate(attrTypeVCHDelegate)
                .setBindDelegate(attrTypeBindDelegate);
        register(REQUIRED, booleanPropertyFactory)
                .setVchDelegate(requiredVCHDelegate)
                .setRefreshDelegate(requiredRefreshDelegate);
        register(EDITABLE, booleanPropertyFactory)
                .setVchDelegate(editableVCHDelegate)
                .setRefreshDelegate(editableRefreshDelegate);
        register(PERMITTED_TYPES, permittedTypesPropertyFactory)
                .setVchDelegate(permittedTypesVCHDelegate);
        register(FILTERED_BY_SCRIPT, booleanPropertyFactory)
                .setVchDelegate(filteredByScriptVCHDelegate)
                .setRefreshDelegate(filteredByScriptRefreshDelegate);
        register(SCRIPT_FOR_FILTRATION, scriptEditorPropertyFactory)
                .setBindDelegate(filtrationScriptBindDelegate)
                .setRefreshDelegate(scriptForFiltrationRefreshDelegate);
        register(COMPUTABLE_ON_FORM, booleanPropertyFactory)
                .setRefreshDelegate(computableOnFormRefreshDelegate)
                .setVchDelegate(computableOnFormVCHDelegate);
        register(COMPUTABLE_ON_FORM_SCRIPT, scriptEditorPropertyFactory)
                .setBindDelegate(compOfFormScriptBindDelegate)
                .setRefreshDelegate(valueOnEditScriptRefreshDelegate);
        register(SHOW_PRS, listBoxPropertyFactory)
                .setVchDelegate(showPrsVCHDelegate)
                .setRefreshDelegate(showPrsRefreshDelegate);
        register(EDIT_PRS, listBoxPropertyFactory)
                .setVchDelegate(editPrsVCHDelegate)
                .setRefreshDelegate(editPrsRefreshDelegate);
        register(SUGGEST_CATALOG, listBoxPropertyFactory)
                .setVchDelegate(suggestCatalogVCHDelegate)
                .setRefreshDelegate(suggestCatalogRefreshDelegate)
                .setValidators(selectListTargetValidators);
        register(SELECT_SORTING, listBoxPropertyFactory)
                .setVchDelegate(selectSortVCHDelegate)
                .setRefreshDelegate(selectSortRefreshDelegate);
        register(DEFAULT_VALUE, defaultValuePropertyFactory);
        register(DEFAULT_VALUE_LABEL, labelPropertyFactory)
                .setRefreshDelegate(defaultValueLabelRefreshDelegate);
        register(SCRIPT_FOR_DEFAULT, scriptEditorPropertyFactory)
                .setBindDelegate(defaultValueScriptBindDelegate)
                .setRefreshDelegate(scriptForDefaultRefreshDelegate);
        register(DEFAULT_BY_SCRIPT, booleanPropertyFactory)
                .setVchDelegate(defaultByScriptVCHDelegate)
                .setRefreshDelegate(defaultByScriptRefreshDelegate);
        register(DESCRIPTION, richTextAreaPropertyFactory)
                .setBindDelegate(richTextAreaBindDelegate);
        register(TARGET_CLASS, listBoxPropertyFactory)
                .setVchDelegate(targetClassVCHDelegate)
                .setRefreshDelegate(targetClassRefreshDelegate)
                .setValidators(selectListTargetValidators);
        register(TARGET_CATALOG, listBoxPropertyFactory)
                .setVchDelegate(targetCatalogVCHDelegate)
                .setRefreshDelegate(targetCatalogRefreshDelegate)
                .setValidators(selectListTargetValidators);
        register(DIRECT_LINK_TARGET, listBoxPropertyFactory)
                .setVchDelegate(directLinkVCHDelegate)
                .setRefreshDelegate(directLinkRefreshDelegate)
                .setValidators(selectListTargetValidators);
        register(AGGREGATE_CLASSES, multiCheckBoxesPropertyFactory)
                .setVchDelegate(aggregateClassesVCHDelegate)
                .setBindDelegate(aggregateClassesBindDelegate);
        register(AGGREGATE_ATTRIBUTES, textBoxPropertyFactory);
        register(INPUTMASK, footedTextPropertyFactory)
                .setBindDelegate(inputmaskBindDelegate)
                .setRefreshDelegate(inputmaskRefreshDelegate)
                .setVchDelegate(inputmaskVCHDelegate)
                .setValidators(inputmaskValidators);
        register(DIGITS_COUNT_RESTRICTION, integerBoxInfoPropertyFactory)
            .setBindDelegate(digitsCountRestrictionBindDelegate)
            .setRefreshDelegate(digitsCountRestrictionRefreshDelegate)
            .setVchDelegate(digitsCountRestrictionVCHDelegate)
            .setValidators(digitsCountRestrictionValidators);
        register(INPUTMASK_MODE, listBoxPropertyFactory)
                .setBindDelegate(inputmaskModeBindDelegate)
                .setRefreshDelegate(inputmaskModeRefreshDelegate)
                .setVchDelegate(inputmaskModeVCHDelegate);
        register(HIDDEN_WHEN_NO_POSSIBLE_VALUES, booleanPropertyFactory)
                .setRefreshDelegate(hiddenWhenNoPossibleValuesRefreshDelegate);
        register(EDIT_ON_COMPLEX_FORM_ONLY, booleanPropertyFactory)
                .setRefreshDelegate(editOnComplexFormOnlyRefreshDelegate);
        register(DATE_TIME_COMMON_RESTRICTIONS, dateTimeCommonRestrictionsPropertyFactory)
                .setBindDelegate(dateTimeCommonRestrictionsBindDelegate)
                .setRefreshDelegate(dateTimeCommonRestrictionsRefreshDelegate)
                .setValidators(commonRestrictionValidators);
        register(DATE_TIME_RESTRICTION_TYPE, listBoxPropertyFactory)
                .setVchDelegate(dateTimeRestrictionTypeVCHDelegate)
                .setBindDelegate(dateTimeRestrictionTypeBindDelegate)
                .setRefreshDelegate(dateTimeRestrictionTypeRefreshDelegate);
        register(DATE_TIME_RESTRICTION_SCRIPT, scriptEditorPropertyFactory)
                .setBindDelegate(dateTimeRestrictionScriptBindDelegate)
                .setRefreshDelegate(dateTimeRestrictionScriptRefreshDelegate);
        register(HIDDEN_ATTR_CAPTION, booleanPropertyFactory)
                .setBindDelegate(hideCaptionOnFormBindDelegate)
                .setRefreshDelegate(hideCaptionOnFormRefreshDelegate);
        register(DATE_TIME_RESTRICTION_ATTRIBUTE, listBoxPropertyFactory)
                .setBindDelegate(dateTimeRestrictionAttributeBindDelegate)
                .setRefreshDelegate(dateTimeRestrictionAttributeRefreshDelegate)
                .setValidators(attributeRestrictionValidators);
        register(DATE_TIME_RESTRICTION_CONDITION, listBoxPropertyFactory)
                .setBindDelegate(dateTimeRestrictionConditionBindDelegate)
                .setRefreshDelegate(dateTimeRestrictionConditionRefreshDelegate)
                .setValidators(attributeConditionValidators);
        //@formatter:on
    }
}
