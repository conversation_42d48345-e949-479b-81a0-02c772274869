/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import jakarta.inject.Singleton;

import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListContentDisplay;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListContentDisplayImpl;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelGinModule;

import com.google.gwt.inject.client.AbstractGinModule;

/**
 * <AUTHOR>
 * @since 25.02.2013
 *
 */
public class ContentPresentersGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        install(new EditableToolPanelGinModule());
        //@formatter:off
        bind(PropertyListContentDisplayMessages.class).in(Singleton.class);
        //@formatter:on

        bind(FormContentDisplay.class).to(FormContentDisplayImpl.class);
        bind(LayoutContentDisplay.class).to(LayoutContentDisplayImpl.class);
        bind(ObjectListContentDisplay.class).to(ObjectListContentDisplayImpl.class);
        bind(PropertyGridFlowContentDisplay.class).to(PropertyGridFlowContentDisplayImpl.class);
        bind(TabBarContentDisplay.class).to(TabBarContentDisplayImpl.class);
    }
}