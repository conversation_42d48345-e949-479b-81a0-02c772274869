package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления для виджета {@link MenuItemPropertyCode#ATTR_FOR_USE_IN_TITLE}
 * <AUTHOR>
 * @since 12.03.2022
 */
public class AttrForUseInTitleDelegateRefresh extends ReferencePropertyDelegateRefreshBase<SelectItem,
        DtObjectSelectProperty>
{
    @Inject
    private ReferenceHelper referenceHelper;

    @Override
    public void refreshProperty(PropertyContainerContext context, DtObjectSelectProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean isReference = isReference(context);
        boolean useAttrTitle =
                Boolean.TRUE.equals(context.getPropertyValues().getProperty(MenuItemPropertyCode.USE_ATTR_TITLE));
        boolean isVisible = isReference && useAttrTitle;
        if (!isVisible)
        {
            callback.onSuccess(false);
            context.getPropertyControllers().get(MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE).unbindValidators();
            return;
        }
        DtObject typeOfCard = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE_OF_CARD);
        RelationsAttrTreeObject attrTree = context.getPropertyValues().getProperty(ReferenceCode.ATTRIBUTE_CHAIN);
        ClassFqn fqn = ReferenceHelper.getObjectClassFqnByCardType(typeOfCard, attrTree);
        referenceHelper.fillMenuItemAttrForUseInTitle(fqn, property, callback, context);
    }
}