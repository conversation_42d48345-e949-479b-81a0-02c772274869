package ru.naumen.metainfoadmin.client.scheduler.command;

import java.util.Collection;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 * <AUTHOR>
 * @since 19.08.2011
 *
 */
public class SchedulerCommandGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        //@formatter:off
        bind(SchedulerCommandFactoryInitializer.class).asEagerSingleton();
        
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, AddTriggerCommand.class)
            .build(new TypeLiteral<CommandProvider<AddTriggerCommand, TriggerCommandParam>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, DeleteSchTaskCommand.class)
            .build(new TypeLiteral<CommandProvider<DeleteSchTaskCommand, CommandParam<Collection<DtObject>, Void>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, RunTaskCommand.class)
            .build(new TypeLiteral<CommandProvider<RunTaskCommand, CommandParam<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, DeleteTriggerCommand.class)
            .build(new TypeLiteral<CommandProvider<DeleteTriggerCommand,
                    CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, EditSchTaskCommand.class)
            .build(new TypeLiteral<CommandProvider<EditSchTaskCommand, CommandParam<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, EditTriggerCommand.class)
            .build(new TypeLiteral<CommandProvider<EditTriggerCommand, TriggerCommandParam>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, OffTriggerCommand.class)
            .build(new TypeLiteral<CommandProvider<OffTriggerCommand, CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, OnTriggerCommand.class)
            .build(new TypeLiteral<CommandProvider<OnTriggerCommand, CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>>>>() {}));
        //@formatter:on
    }
}
