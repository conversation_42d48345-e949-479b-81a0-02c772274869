package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue;

import static ru.naumen.metainfo.shared.Constants.NOT_COMPUTABLE_ATTRIBUTE_TYPES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.HAS_DEFAULT_VALUE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPOSITE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 11.10.2012
 *
 */
public class DefaultByScriptRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        String attrCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE);
        if (SuperUser.FQN.isSameClass(metaClass.getFqn())
            && (SuperUser.LOGIN.equals(attrCode) || SuperUser.PASSWORD.equals(attrCode)))
        {
            callback.onSuccess(false);
            return;
        }

        Boolean computable = context.getPropertyValues().getProperty(COMPUTABLE);
        Boolean definableByTemplate = context.getPropertyValues().getProperty(COMPOSITE);
        callback.onSuccess(!computable && !definableByTemplate && isDefaultByScript(context) && isComputable(context));
    }

    protected boolean isComputable(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(ATTR_TYPE);
        boolean computable = !NOT_COMPUTABLE_ATTRIBUTE_TYPES.contains(attrType);
        return computable;
    }

    protected boolean isDefaultByScript(PropertyContainerContext context)
    {
        return Boolean.TRUE.equals(context.getContextValues().getProperty(HAS_DEFAULT_VALUE));
    }
}
