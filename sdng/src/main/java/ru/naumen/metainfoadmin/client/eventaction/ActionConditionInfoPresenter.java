package ru.naumen.metainfoadmin.client.eventaction;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.OnStartRefreshCallback;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.client.eventaction.EventActionConstants;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.eventaction.ActionConditionWithScript;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.eventaction.command.ActionConditionCommandParam;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.shared.Constants.EventActionCommandCode;

/**
 *
 * <AUTHOR>
 *
 */
public class ActionConditionInfoPresenter extends BasicPresenter<InfoDisplay>
{
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Property<String> type;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    Property<Boolean> syncVerification;
    Property<String> settingsSet;
    @Inject
    EventActionMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    EventActionConstants eventActionConstants;
    @Inject
    PlaceController placeController;
    @Inject
    ActionConditionsCreatorFactory factory;
    @Inject
    ButtonFactory buttonFactory;
    @Inject
    SettingsSetOnFormCreator settingsSetOnFormCreator;

    ActionConditionCreator creator;
    private ToolBarDisplayMediator<ActionConditionWithScript> toolBar;
    private ActionConditionWithScript actionCondition;
    private EventAction eventAction;

    protected OnStartRefreshCallback<ActionConditionWithScript> refreshCallback =
            new OnStartRefreshCallback<ActionConditionWithScript>(
                    getDisplay(), this);

    @Inject
    // @formatter:off
    public ActionConditionInfoPresenter(
            @Assisted EventAction eventAction,
            @Assisted ActionConditionWithScript actionCondition,
            InfoDisplay display,
            EventBus eventBus)
    // @formatter:on
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<ActionConditionWithScript>(getDisplay().getToolBar());
        this.actionCondition = actionCondition;
        this.eventAction = eventAction;
    }

    @Override
    public void refreshDisplay()
    {
        type.setValue(eventActionConstants.actionConditionsTypes().get(actionCondition.getObject().getType().name()));
        creator.setInfoPropertiesValues();
        syncVerification.setValue(actionCondition.isSyncVerification());
        settingsSetOnFormCreator.setValueOnCardProperty(actionCondition.getObject().getSettingsSet(), settingsSet);
        toolBar.refresh(actionCondition);
    }

    @SuppressWarnings("unchecked")
    protected ButtonPresenter<ActionConditionWithScript> addTool(String btn, String title, String cmd,
            CommandParam<ActionConditionWithScript, ActionConditionWithScript> param)
    {
        ButtonPresenter<ActionConditionWithScript> buttonPresenter =
                (ButtonPresenter<ActionConditionWithScript>)buttonFactory
                        .create(btn, title, cmd, param);
        toolBar.add(buttonPresenter);
        return buttonPresenter;
    }

    @Override
    protected void onBind()
    {
        getDisplay().hideTitle();
        type.setCaption(messages.conditionType());
        getDisplay().add(type);
        creator = factory.create(actionCondition.getObject().getType(), actionCondition);
        creator.addInfoProperties(getDisplay());
        // подменим чекбокс на картинку
        syncVerification.setCaption(cmessages.syncVerification());
        syncVerification.ensureDebugId("sync-verification");
        getDisplay().setProperty(syncVerification, 2);
        settingsSet = settingsSetOnFormCreator.createFieldOnCard(getDisplay());
        initToolBar();
        refreshDisplay();
    }

    private void initToolBar()
    {
        OnStartCallback<ActionConditionWithScript> delCallback =
                new OnStartBasicCallback<ActionConditionWithScript>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(ActionConditionWithScript value)
                    {
                        placeController.goTo(new EventActionPlace(new EventActionWithScript(eventAction)));
                    }
                };

        ActionConditionCommandParam param = new ActionConditionCommandParam(actionCondition, refreshCallback,
                new EventActionWithScript(eventAction));
        ButtonPresenter<ActionConditionWithScript> editBtn = addTool(ButtonCode.EDIT,
                cmessages.edit(), EventActionCommandCode.EDIT_ACTION_CONDITION, param);
        editBtn.addPossibleFilter(actionConWithScript ->
                AdminPermissionUtils.hasEditPermission(actionConWithScript.getPermissions()));

        ActionConditionCommandParam delParam = new ActionConditionCommandParam(actionCondition,
                delCallback, new EventActionWithScript(eventAction));

        ButtonPresenter<ActionConditionWithScript> delBtn = addTool(ButtonCode.DELETE, cmessages.delete(),
                EventActionCommandCode.DELETE_ACTION_CONDITION, delParam);
        delBtn.addPossibleFilter(actionConWithScript ->
                AdminPermissionUtils.hasDeletePermission(actionConWithScript.getPermissions()));

        toolBar.bind();
    }
}
