package ru.naumen.metainfoadmin.client.scheduler;

import ru.naumen.metainfoadmin.client.scheduler.command.SchedulerCommandGinjector;
import ru.naumen.metainfoadmin.client.scheduler.forms.AddTriggerFormPresenter;
import ru.naumen.metainfoadmin.client.scheduler.forms.EditSchedulerTaskFormPresenter;
import ru.naumen.metainfoadmin.client.scheduler.forms.EditTriggerFormPresenter;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.ConcreteDateTriggerCreator;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.ExecuteScriptCreator;
import ru.naumen.metainfoadmin.client.scheduler.forms.creators.PeriodicTriggerCreator;

import com.google.gwt.inject.client.GinModules;
import com.google.gwt.inject.client.Ginjector;

/**
 *
 * <AUTHOR>
 */
@GinModules(SchedulerGinModule.class)
public interface SchedulerGinjector extends Ginjector, SchedulerCommandGinjector
{
    AddTriggerFormPresenter addTriggerFormPresenter();

    ConcreteDateTriggerCreator concreteDateTriggerCreator();

    EditSchedulerTaskFormPresenter editSchedulerTaskFormPresenter();

    EditTriggerFormPresenter editTriggerFormPresenter();

    ExecuteScriptCreator executeScriptCreator();

    ExecuteScriptTaskInfoPresenter executeScriptTaskInfoPresenter();

    PeriodicTriggerCreator periodicTriggerCreator();

    SchedulerTaskPresenter schedulerTaskPresenter();

    TriggerInfoPresenter triggerInfoPresenter();
}
