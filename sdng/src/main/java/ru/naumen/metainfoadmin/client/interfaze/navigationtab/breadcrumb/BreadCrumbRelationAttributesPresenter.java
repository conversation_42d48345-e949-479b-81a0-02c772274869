package ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb;

import java.util.List;

import jakarta.inject.Inject;

import java.util.ArrayList;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.view.client.AbstractDataProvider;
import com.google.gwt.view.client.HasData;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.group.DataTableDnDGroupController;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.columns.HTMLCell;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.CrumbRelationAttribute;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.SaveBreadCrumbAction;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableResources;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableStyle;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.BreadCrumbRelationAttributeCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.MoveCrumbRelationAttributeDownComand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.MoveCrumbRelationAttributeUpComand;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Презентер отображения на карточке хлебной крошки атрибутов связи
 *
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public class BreadCrumbRelationAttributesPresenter extends BasicPresenter<TableDisplay<CrumbRelationAttribute>>
{
    private class BreadCrumbAttributeListDnDController extends DataTableDnDGroupController
    {
        public BreadCrumbAttributeListDnDController()
        {
            super(getDisplay().getTableContainer().getElement());
        }

        @Override
        public void move(final int oldPosition, final int newPosition, ReadyState readyState)
        {
            Crumb crumb = context.getCrumb();
            List<CrumbRelationAttribute> attributes = crumb.getRelationAttributes();
            attributes.add(newPosition, attributes.remove(oldPosition));
            dispatch.execute(new SaveBreadCrumbAction(crumb, false),
                    new BasicCallback<SimpleResult<DtoContainer<NavigationSettings>>>(readyState)
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<DtoContainer<NavigationSettings>> value)
                        {
                            param.getCallbackSafe().onSuccess(value.get());
                        }
                    });
        }
    }

    private BreadCrumbContext context;
    @Inject
    private BreadCrumbMessages messages;
    @Inject
    private BreadCrumbHelper helper;
    @Inject
    private WithArrowsCellTableResources cellTableResources;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private ObjectListColumnBuilder tableBuilder;

    private BreadCrumbRelationAttributeCommandParam param;
    private ListEditorDnDController dndController;

    @Inject
    public BreadCrumbRelationAttributesPresenter(TableDisplay<CrumbRelationAttribute> display, EventBus eventBus,
            ListEditorDnDControllerFactory dndControllerFactory)
    {
        super(display, eventBus);
        dndController = dndControllerFactory.create(new BreadCrumbAttributeListDnDController());
    }

    public void init(BreadCrumbContext context, OnStartCallback<DtoContainer<NavigationSettings>> refreshCallback)
    {
        this.context = context;
        helper.init(context.getSettings().get().getBreadCrumb());
        param = new BreadCrumbRelationAttributeCommandParam(context.getSettings(), refreshCallback);
        param.setCrumb(context.getCrumb());
    }

    @Override
    public void refreshDisplay()
    {
        param.setCrumb(context.getCrumb());
        param.setSettings(context.getSettings());
        helper.init(context.getSettings().get().getBreadCrumb());
        display.refresh();
    }

    @Override
    protected void onBind()
    {
        initTable();
        getDisplay().setCaptionVisible(false);

        AbstractDataProvider<CrumbRelationAttribute> dataProvider = new AbstractDataProvider<CrumbRelationAttribute>()
        {

            @Override
            protected void onRangeChanged(HasData<CrumbRelationAttribute> display)
            {
                List<CrumbRelationAttribute> itemsList = new ArrayList<>();
                if (context != null)
                {
                    itemsList.addAll(context.getCrumb().getRelationAttributes());
                }
                display.setRowData(0, itemsList);
                display.setRowCount(itemsList.size(), true);
                scheduleDnDUpdate();
            }
        };
        dataProvider.addDataDisplay(getDisplay().getTable());
    }

    private void addActionColumn(String... commands)
    {
        tableBuilder.addActionColumn(display, param, commands);
    }

    private void initTable()
    {
        DataTable<CrumbRelationAttribute> table = getDisplay().getTable();
        table.setVisibleRange(0, DefaultCellTable.DEFAULT_PAGESIZE);

        WithArrowsCellTableStyle tableStyle = cellTableResources.cellTableStyle();
        addActionColumn(MoveCrumbRelationAttributeUpComand.ID);
        addActionColumn(MoveCrumbRelationAttributeDownComand.ID);

        Column<CrumbRelationAttribute, String> textColumn =
                new Column<CrumbRelationAttribute, String>(new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
                {
                    @Override
                    public String getValue(CrumbRelationAttribute item)
                    {
                        return helper.getRelationAttributeTitle(item);
                    }
                };
        textColumn.setCellStyleNames(tableStyle.columnWithLineBreak());
        table.addColumn(textColumn, messages.relationAttributes());
        textColumn = new Column<CrumbRelationAttribute, String>(new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
        {
            @Override
            public String getValue(CrumbRelationAttribute input)
            {
                return helper.getPermittedTypesViewWithLinks(input);
            }
        };
        textColumn.setCellStyleNames(tableStyle.columnWithLineBreak());
        table.addColumn(textColumn, messages.permittedTypes());
        textColumn = new Column<CrumbRelationAttribute, String>(new HTMLCell()) // NOPMD NSDPRD-28509 unsafe html
        {
            @Override
            public String getValue(CrumbRelationAttribute item)
            {
                return helper.generateAllExamples4Rel(context.getCrumb(), item);
            }
        };
        textColumn.setCellStyleNames(tableStyle.columnWithLineBreak());
        table.addColumn(textColumn, messages.presentationOfBreadCrumb());

        table.asWidget().ensureDebugId("bread-crumb-table");
    }

    private void scheduleDnDUpdate()
    {
        Scheduler.get().scheduleDeferred(new ScheduledCommand()
        {
            @Override
            public void execute()
            {
                dndController.update();
            }
        });
    }
}
