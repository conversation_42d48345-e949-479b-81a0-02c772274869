package ru.naumen.metainfoadmin.client.dynadmin.content.embeddedapplications;

import static ru.naumen.dynaform.client.customforms.propertycreator.CustomPropertyListCreator.buildCustomFormContext;

import java.util.HashMap;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.dynaform.client.customforms.propertycreator.CustomPropertyListCreator;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.DeleteEmbeddedApplicationParametersCustomFormAction;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.GetEmbeddedApplicationCustomFormParametersAction;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.GetEmbeddedApplicationCustomFormParametersResponse;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.GetMobileEmbeddedApplicationParametersAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.mobile.contents.MobileEmbeddedApplicationContent;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;

/**
 * Делегат, выполняющий операции, необходимые для формы добавления и редатирования контента
 * типа "Встроенное приложение"
 *
 * <AUTHOR>
 * @since 18 мая 2019 г.
 */
public class EmbeddedApplicationContentDelegate
{
    private final CustomPropertyListCreator customPropertyListCreator;

    private final DispatchAsync dispatch;

    private PropertyDialogDisplay display;

    private Processor validation;

    private String customForm;

    private List<String> scripts;

    private boolean isParametersNotShow = false;

    @Nullable
    private ClassFqn fqn;

    @Nullable
    private String contentCode;

    @Nullable
    private String contentType;

    @Inject
    public EmbeddedApplicationContentDelegate(DispatchAsync dispatch,
            CustomPropertyListCreator customPropertyListCreator)
    {
        this.dispatch = dispatch;
        this.customPropertyListCreator = customPropertyListCreator;
    }

    public void clearProperties()
    {
        customPropertyListCreator.clearProperties();
        deleteEmbeddedApplicationParametersCustomForm();
    }

    public HashMap<Attribute, Object> getAttributeValues()
    {
        return isParametersNotShow ? new HashMap<>() : customPropertyListCreator.getAttributeValues();
    }

    public void init(PropertyDialogDisplay display, Processor validation, @Nullable ClassFqn fqn,
            @Nullable String contentCode, @Nullable String contentType)
    {
        this.display = display;
        this.validation = validation;
        this.fqn = fqn;
        this.contentCode = contentCode;
        this.contentType = contentType;
    }

    public void onEmbeddedApplicationChange(@Nullable String app,
            HasProperties.PropertyRegistration<?> propertyRegistration)
    {
        clearProperties();
        deleteEmbeddedApplicationParametersCustomForm();
        if (app != null)
        {
            dispatch.execute(new GetEmbeddedApplicationCustomFormParametersAction(app,
                            new GetEmbeddedApplicationCustomFormParametersAction.ContentInfo(fqn, contentCode,
                                    contentType)),
                    new BasicCallback<GetEmbeddedApplicationCustomFormParametersResponse>(display)
                    {
                        @Override
                        protected void handleSuccess(GetEmbeddedApplicationCustomFormParametersResponse response)
                        {
                            createAttributeProperties(response, propertyRegistration);
                        }
                    });
        }
    }

    public void onMobileEmbeddedApplicationChange(@Nullable String objectCardCode, @Nullable String contentCode,
            @Nullable String appCode, HasProperties.PropertyRegistration propertyRegistration)
    {
        clearProperties();
        deleteEmbeddedApplicationParametersCustomForm();
        if (appCode != null)
        {
            GetMobileEmbeddedApplicationParametersAction action =
                    new GetMobileEmbeddedApplicationParametersAction(objectCardCode, contentCode, appCode);
            dispatch.execute(action, new BasicCallback<GetEmbeddedApplicationCustomFormParametersResponse>(display)
            {
                @Override
                protected void handleSuccess(GetEmbeddedApplicationCustomFormParametersResponse response)
                {
                    createAttributeProperties(response, propertyRegistration);
                }
            });
        }
    }

    public void setAttributesProperties(Content content)
    {
        if (!(content instanceof EmbeddedApplicationContent || content instanceof MobileEmbeddedApplicationContent)
            || isParametersNotShow)
        {
            return;
        }
        if (content instanceof MobileEmbeddedApplicationContent)
        {
            ((MobileEmbeddedApplicationContent)content).setParametersValues(customPropertyListCreator
                    .getAttributeValues());
        }
        else
        {
            ((EmbeddedApplicationContent)content).setParametersValues(customPropertyListCreator.getAttributeValues());
        }
    }

    public void setPropertiesValues(MapProperties propertiesValues)
    {
        customPropertyListCreator.setPropertiesValues(propertiesValues);
    }

    private void createAttributeProperties(GetEmbeddedApplicationCustomFormParametersResponse response,
            HasProperties.PropertyRegistration propertyRegistration)
    {
        customPropertyListCreator.clearProperties();
        isParametersNotShow = response.isParametersNotShow();
        display.addAttentionMessage(response.getMessage());
        if (!isParametersNotShow)
        {
            this.customForm = response.getForm().getCode();
            this.scripts = response.getScripts();
            customPropertyListCreator.init(buildCustomFormContext(response), display, validation);
            customPropertyListCreator.createProperties(propertyRegistration);
        }
    }

    private void deleteEmbeddedApplicationParametersCustomForm()
    {
        if (customForm != null && scripts != null)
        {
            dispatch.execute(new DeleteEmbeddedApplicationParametersCustomFormAction(customForm, scripts),
                    new BasicCallback<>());
        }
        customForm = null;
        scripts = null;
    }
}
