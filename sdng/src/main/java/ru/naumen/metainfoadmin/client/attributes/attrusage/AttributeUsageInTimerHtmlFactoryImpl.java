package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfoadmin.client.timer.TimerPlace;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInTimer;

/**
 * Представление для отображения значения места использования "Счетчик времени" на форме "Используется в настройках"
 * в таблице атрибутов
 * <AUTHOR>
 * @since 3 Jul 18
 */
@Singleton
public class AttributeUsageInTimerHtmlFactoryImpl extends AttributeHtmlFactoryImpl<AttributeUsageInTimer>
{
    @Inject
    private Formatters formatters;
    @Inject
    private CommonMessages messages;
    @Inject
    private PlaceHistoryMapper historyMapper;

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInTimer usage)
    {
        return new SafeHtmlBuilder().append(formatters.formatHyperlinkAsHtml(createLinkToTimerCard(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLinkToTimerCard(AttributeUsageInTimer usage)
    {
        TimerPlace timerPlace = new TimerPlace(usage.getCode());
        //@formatter:off
        return new Hyperlink(
                    messages.timer() + " \"" + usage.getTitle() + "\"", 
                    StringUtilities.getHrefByToken(historyMapper.getToken(timerPlace)));
        //@formatter:on
    }
}
