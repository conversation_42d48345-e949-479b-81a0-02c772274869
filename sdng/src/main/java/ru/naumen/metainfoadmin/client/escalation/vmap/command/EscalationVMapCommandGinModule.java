/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.command;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogItemCommandParam;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationVMapCommandGinModule extends AbstractGinModule
{
    public interface EscalationVMapItemCommandCode
    {
        String ADD_ESCALATION_VMAP_ITEM = "addEscalationVMapItem";
        String COPY_ESCALATION_VMAP_ITEM = "copyEscalationVMapItem";
        String EDIT_ESCALATION_VMAP_ITEM = "editEscalationVMapItem";
    }

    @Override
    protected void configure()
    {
        bind(EscalationVMapCommandFactoryInitializer.class).asEagerSingleton();
        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, AddEscalationVMapItemCommand.class)
            .build(new TypeLiteral<CommandProvider<AddEscalationVMapItemCommand, CatalogItemCommandParam<DtObject>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, CopyEscalationVMapItemCommand.class)
            .build(new TypeLiteral<CommandProvider<CopyEscalationVMapItemCommand, CatalogItemCommandParam<DtObject>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, EditEscalationVMapItemCommand.class)
            .build(new TypeLiteral<CommandProvider<EditEscalationVMapItemCommand, CatalogItemCommandParam<DtObject>>>() {}));
        //@formatter:on
    }
}