package ru.naumen.metainfoadmin.client.interfaze.interfacetab.command;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.shared.personalsettings.ThemeClient;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.ui2.logo.EditUI2LogoSettingsCommand;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.ui2.logo.Ui2LogoCommandParam;

/**
 * <AUTHOR>
 * @since 18.07.16
 */
public class InterfaceSettingsCommandGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        bind(InterfaceSettingsCommandFactoryInitializer.class).asEagerSingleton();

        //@formatter:off
        install(Gin.install(
                new TypeLiteral<CommandProvider<EnableThemeCommand, ThemeCommandParam>>(){},
                new TypeLiteral<ClosureCommand<ThemeClient>>(){},
                new TypeLiteral<EnableThemeCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<DisableThemeCommand, ThemeCommandParam>>(){},
                new TypeLiteral<ClosureCommand<ThemeClient>>(){},
                new TypeLiteral<DisableThemeCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditThemeCommand, ThemeCommandParam>>(){},
                new TypeLiteral<ClosureCommand<ThemeClient>>(){},
                new TypeLiteral<EditThemeCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditDefaultThemeSettingsCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>>>(){},
                new TypeLiteral<ClosureCommand<InterfaceSettingsContext>>(){},
                new TypeLiteral<EditDefaultThemeSettingsCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditLanguageSettingsCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>>>(){},
                new TypeLiteral<ClosureCommand<InterfaceSettingsContext>>(){},
                new TypeLiteral<EditLanguageSettingsCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditTabHeaderSettingsCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>>>(){},
                new TypeLiteral<ClosureCommand<InterfaceSettingsContext>>(){},
                new TypeLiteral<EditTabHeaderSettingsCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<AddThemeCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>>>(){},
                new TypeLiteral<ClosureCommand<InterfaceSettingsContext>>(){},
                new TypeLiteral<AddThemeCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<DeleteThemeCommand, ThemeCommandParam>>(){},
                new TypeLiteral<ClosureCommand<ThemeClient>>(){},
                new TypeLiteral<DeleteThemeCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<ExportThemePropertiesCommand, ThemeCommandParam>>(){},
                new TypeLiteral<ClosureCommand<ThemeClient>>(){},
                new TypeLiteral<ExportThemePropertiesCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EnableCustomLoginPageCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>>>(){},
                new TypeLiteral<ClosureCommand<InterfaceSettingsContext>>(){},
                new TypeLiteral<EnableCustomLoginPageCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<DisableCustomLoginPageCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>>>(){},
                new TypeLiteral<ClosureCommand<InterfaceSettingsContext>>(){},
                new TypeLiteral<DisableCustomLoginPageCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<TestCustomLoginPageCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>>>(){},
                new TypeLiteral<ClosureCommand<InterfaceSettingsContext>>(){},
                new TypeLiteral<TestCustomLoginPageCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditContentSettingsCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>>>(){},
                new TypeLiteral<ClosureCommand<InterfaceSettingsContext>>(){},
                new TypeLiteral<EditContentSettingsCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditThemeBaseSettingsCommand, CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>>>(){},
                new TypeLiteral<ClosureCommand<InterfaceSettingsContext>>(){},
                new TypeLiteral<EditThemeBaseSettingsCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditUI2LogoSettingsCommand, CommandParam<Ui2LogoCommandParam, Ui2LogoCommandParam>>>(){},
                new TypeLiteral<ClosureCommand<Ui2LogoCommandParam>>(){},
                new TypeLiteral<EditUI2LogoSettingsCommand>(){}));
        //@formatter:on
    }
}
