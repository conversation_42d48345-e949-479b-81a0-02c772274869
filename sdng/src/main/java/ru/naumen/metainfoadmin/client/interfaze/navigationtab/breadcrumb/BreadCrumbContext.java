package ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

/**
 * <AUTHOR>
 * @since 07 июля 2014 г.
 */
public class BreadCrumbContext
{
    private Crumb crumb;
    private DtoContainer<NavigationSettings> settings;

    public BreadCrumbContext(Crumb crumb, DtoContainer<NavigationSettings> settings)
    {
        this.crumb = crumb;
        this.settings = settings;
    }

    public Crumb getCrumb()
    {
        return crumb;
    }

    public DtoContainer<NavigationSettings> getSettings()
    {
        return settings;
    }

    public void setCrumb(Crumb item)
    {
        this.crumb = item;
    }

    public void setSettings(DtoContainer<NavigationSettings> settings)
    {
        this.settings = settings;
    }
}
