package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.client.SafeHtmlTemplates;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.cellview.client.Column;
import com.google.inject.name.Named;

import jakarta.inject.Inject;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.icons.IconsFactory;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.columns.ClickableSafeHtmlTextCell;
import ru.naumen.core.client.widgets.columns.ToggleColumn;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.ResultProfilesDTO.ResultProfilesType;
import ru.naumen.core.shared.navigationsettings.dispatch.MoveLeftMenuItemAction;
import ru.naumen.core.shared.navigationsettings.dispatch.MoveNavigationMenuItemAction;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO.MenuItemFormatting;
import ru.naumen.core.shared.navigationsettings.menu.MenuIconSettingsDTO;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableStyle;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.AddLeftMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DeleteLeftMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DisableLeftMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EditLeftMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EnableLeftMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.MoveLeftMenuItemDownCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.MoveLeftMenuItemUpCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsLMCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsMenuItemAbstractCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.PinLeftMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.UnpinLeftMenuItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.NavigationSettingsChangedEvent;

/**
 * Реализация презентера списка элементов левого меню
 *
 * <AUTHOR>
 * @since 11.06.2020
 */
public class NavigationLeftMenuItemsPresenter extends NavigationMenuItemsPresenter<LeftMenuItemSettingsDTO>
{
    public interface Templates extends SafeHtmlTemplates
    {
        @Template("<span>{0}</span><span class=\"hint fontIcon\" style=\"font-weight:normal\""
                  + " title=\"{1}\"></span>")
        SafeHtml textWithHint(String text, String hintText);
    }

    class LeftItemsDataProvider extends ItemsDataProvider
    {
        @Override
        protected List<LeftMenuItemSettingsDTO> getMenuItems()
        {
            return settings.get().getLeftMenu().getChildren();
        }
    }

    private final Map<ResultProfilesType, String> resultProfileTypeTitles;
    private final Templates template;
    private final IconsFactory iconsFactory;

    @Inject
    public NavigationLeftMenuItemsPresenter(ScrollableTableDisplayImpl<LeftMenuItemSettingsDTO> display,
            EventBus eventBus,
            @Named(NavigationTabSettingsGinModule.RESULT_PROFILE_TYPE_TITLES)
            Map<ResultProfilesType, String> resultProfileTypeTitles,
            CommonMessages cmesages, NavigationSettingsMessages messages,
            Templates template, IconsFactory iconsFactory)
    {
        super(display, eventBus, cmesages, messages);
        this.resultProfileTypeTitles = resultProfileTypeTitles;
        this.template = template;
        this.iconsFactory = iconsFactory;
    }

    protected void addFrontActionColumns()
    {
        addActionColumn(MoveLeftMenuItemUpCommand.ID);
        addActionColumn(MoveLeftMenuItemDownCommand.ID);
    }

    protected void addBackActionColumns()
    {
        addActionColumn(value -> Boolean.TRUE.equals(value.getProperty(Tag.IS_ELEMENT_ENABLED)),
                EnableLeftMenuItemCommand.ID, DisableLeftMenuItemCommand.ID);

        addActionColumn(EditLeftMenuItemCommand.ID);
        addActionColumn(DeleteLeftMenuItemCommand.ID);
        addActionColumn(PinLeftMenuItemCommand.ID, UnpinLeftMenuItemCommand.ID);
    }

    @Override
    protected PermissionType getCommandPermissionType(String command)
    {
        return DeleteLeftMenuItemCommand.ID.equals(command) ? PermissionType.DELETE : PermissionType.EDIT;
    }

    @Override
    protected NavigationMenuItemPlace<LeftMenuItemSettingsDTO> getNewPlace(LeftMenuItemSettingsDTO item)
    {
        return new NavigationLeftMenuItemPlace(item.getCode());
    }

    protected void addPropertiesColumns(DataTable<LeftMenuItemSettingsDTO> table)
    {
        addMarkerColumn(table);
        addTitleColumn(table);
        addTileExistColumn(table);
        addProfilesColumn(table);
        addTypeColumn(table);
        addValueColumn(table);
        addResultProfilesColumn(table);
    }

    private void addTileExistColumn(DataTable<LeftMenuItemSettingsDTO> table)
    {
        ToggleColumn<LeftMenuItemSettingsDTO> tileExists = new ToggleColumn<LeftMenuItemSettingsDTO>(iconFactory)
        {
            @Override
            protected boolean isEnabled(LeftMenuItemSettingsDTO item)
            {
                return settings.get().getQuickAccessPanelSettings()
                        .getAreas()
                        .stream()
                        .anyMatch(area -> area.getTiles()
                                .stream()
                                .anyMatch(tile -> tile.getMenuItemCode().equals(item.getCode())));
            }
        };
        table.addColumn(tileExists, messages.tile());
    }

    protected void addProfilesColumn(DataTable<LeftMenuItemSettingsDTO> table)
    {
        Column<LeftMenuItemSettingsDTO, SafeHtml> profilesColumn = new Column<LeftMenuItemSettingsDTO, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(LeftMenuItemSettingsDTO item)
            {
                String profilesTitles = item.getProfiles().stream().map(ITitled::getTitle)
                        .collect(Collectors.joining(", "));
                if (!StringUtilities.isEmpty(item.getNotAvailableForProfilesMessage()))
                {
                    return template.textWithHint(profilesTitles,
                            String.join(", ", item.getNotAvailableForProfilesMessage()));
                }
                return new SafeHtmlBuilder().appendEscaped(profilesTitles).toSafeHtml();
            }
        };
        WithArrowsCellTableStyle tableStyle = cellTableResources.cellTableStyle();
        profilesColumn.setFieldUpdater(htmlFieldUpdater);
        profilesColumn.setCellStyleNames(tableStyle.titleCellWithHint());
        table.addColumn(profilesColumn, messages.profiles());
    }

    protected void addResultProfilesColumn(DataTable<LeftMenuItemSettingsDTO> table)
    {
        Column<LeftMenuItemSettingsDTO, SafeHtml> profilesColumn = new Column<LeftMenuItemSettingsDTO, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(LeftMenuItemSettingsDTO item)
            {
                if (item.getResultProfiles() == null)
                {
                    return new SafeHtmlBuilder().toSafeHtml();
                }
                String profilesTypeTitle = resultProfileTypeTitles.get(item.getResultProfiles().getType());
                String profilesTitles = item.getResultProfiles().getProfiles().stream().map(ITitled::getTitle)
                        .collect(Collectors.joining(", "));

                String result =
                        (ResultProfilesType.LIST.equals(item.getResultProfiles().getType()) ?
                                profilesTitles : profilesTypeTitle);
                return new SafeHtmlBuilder()
                        .appendEscapedLines(result)
                        .toSafeHtml();
            }
        };
        profilesColumn.setFieldUpdater(htmlFieldUpdater);

        table.addColumn(profilesColumn, messages.resultProfiles());
    }

    protected void addMarkerColumn(DataTable<LeftMenuItemSettingsDTO> table)
    {
        Column<LeftMenuItemSettingsDTO, SafeHtml> markerColumn = new Column<LeftMenuItemSettingsDTO, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(LeftMenuItemSettingsDTO item)
            {
                return getIconOrAbbr(item.getIcon(), iconsFactory);
            }
        };
        markerColumn.setFieldUpdater(htmlFieldUpdater);

        table.addColumn(markerColumn, cmessages.menuPresentation());
    }

    /**
     * Вернуть иконку или аббревиатуру по данным из элемента меню
     */
    public static SafeHtml getIconOrAbbr(MenuIconSettingsDTO icon, IconsFactory iconsFactory)
    {
        switch (icon.getType())
        {
            case ABBREVIATION:
            {
                return new SafeHtmlBuilder().appendEscaped(icon.getAbbreviation()).toSafeHtml();
            }
            case CATALOG_FONT_ICON:
            {
                String iconCode = icon.getCode();
                if (StringUtilities.isEmpty(iconCode))
                {
                    return SafeHtmlUtils.EMPTY_SAFE_HTML;
                }
                return iconsFactory.getIconAsHtml(iconCode);
            }
            default:
            {
                return SafeHtmlUtils.EMPTY_SAFE_HTML;
            }
        }
    }

    @Override
    protected void addTypeColumn(DataTable<LeftMenuItemSettingsDTO> table)
    {
        Column<LeftMenuItemSettingsDTO, SafeHtml> typeColumn = new Column<LeftMenuItemSettingsDTO, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(LeftMenuItemSettingsDTO item)
            {
                if (MenuItemType.chapter.equals(item.getType())
                    && MenuItemFormatting.module.name().equals(item.getUiFormatting()))
                {
                    return new SafeHtmlBuilder()
                            .appendEscaped(
                                    menuTypeTitles.get(item.getType()) + "(" + messages.module().toLowerCase() + ")")
                            .toSafeHtml();
                }
                return new SafeHtmlBuilder().appendEscaped(menuTypeTitles.get(item.getType())).toSafeHtml();
            }
        };
        typeColumn.setFieldUpdater(htmlFieldUpdater);

        table.addColumn(typeColumn, cmessages.elementView());
    }

    @Override
    protected java.util.function.Function<LeftMenuItemSettingsDTO, String> getHintTextFunction()
    {
        return LeftMenuItemSettingsDTO::getItemError;
    }

    @Override
    protected NavigationSettingsMenuItemAbstractCommandParam<LeftMenuItemSettingsDTO> getParam(
            DtoContainer<NavigationSettings> settings,
            OnStartCallback<DtoContainer<NavigationSettings>> refreshCallback)
    {
        return new NavigationSettingsLMCommandParam(settings, refreshCallback);
    }

    @Override
    protected Map<String, LinkedList<String>> getMenuItemPaths()
    {
        return settings.get().getLeftMenuItemPaths();
    }

    @Override
    protected boolean ifMenuBlockEnabled()
    {
        return settings.get().getLeftMenu().isEnabled();
    }

    protected String getAddCommandId()
    {
        return AddLeftMenuItemCommand.ID;
    }

    protected String getDisplayCaption()
    {
        return messages.leftMenu();
    }

    @Override
    protected NavigationMenuItemsPresenter<LeftMenuItemSettingsDTO>.ItemsDataProvider getDataProvider()
    {
        return new LeftItemsDataProvider();
    }

    @Override
    protected NavigationMenuListDnDController getGroupController()
    {
        return new NavigationMenuListDnDController()
        {
            @Override
            protected Map<String, LinkedList<String>> getMenuItemPaths()
            {
                return settings.get().getLeftMenuItemPaths();
            }

            @Override
            protected MoveNavigationMenuItemAction getMoveAction()
            {
                return new MoveLeftMenuItemAction();
            }
        };
    }

    @Override
    protected List<LeftMenuItemSettingsDTO> getMenuItems()
    {
        return settings.get().getLeftMenu().getChildren();
    }

    @Override
    public void onNavigationSettingsChanged(NavigationSettingsChangedEvent event)
    {
        DtoContainer<NavigationSettings> newSettings = event.getSettings();
        if (settings.get().getLeftMenu().isEnabled() == !newSettings.get().getLeftMenu().isEnabled())
        {
            refreshSettings(newSettings);
            refreshDisplay();
        }
    }

    protected String getTableDebugId()
    {
        return "left-menu-items-table";
    }
}
