package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.Collection;

import jakarta.inject.Inject;

import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления для виджета {@link ReferenceCode#OBJECT_CASES}
 * <AUTHOR>
 * @since 12.03.2022
 */
public class ObjectCasesReferenceDelegateRefreshImpl extends ReferencePropertyDelegateRefreshBase<Collection<SelectItem>,
        MultiSelectBoxProperty>
{
    @Inject
    private ReferenceHelper referenceHelper;

    @Override
    public void refreshProperty(PropertyContainerContext context, MultiSelectBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        if (!isReference(context))
        {
            callback.onSuccess(false);
            return;
        }
        DtObject typeOfCard = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE_OF_CARD);
        RelationsAttrTreeObject attrTree = context.getPropertyValues().getProperty(ReferenceCode.ATTRIBUTE_CHAIN);
        property.clearValue();
        property.getValueWidget().clear();
        property.getValueWidget().refreshPopupCellList();
        Predicate<? extends MetaClassLite> filter = attrTree == null
                ? Predicates.alwaysTrue()
                : ReferenceHelper.getAttributeChainPermittedClasses(attrTree.getAttribute());
        ClassFqn fqn = ReferenceHelper.getObjectClassFqnByCardType(typeOfCard, attrTree);
        referenceHelper.fillObjectCasesProperty(fqn, context, property, filter, callback);
    }
}