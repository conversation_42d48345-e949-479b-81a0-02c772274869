package ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsTMCommandParam;

/**
 * Презентер катрочки элемента верхнего меню
 * <AUTHOR>
 * @since 16.07.2020
 */
public class NavigationTopMenuItemPresenter extends NavigationMenuItemPresenter<MenuItem,
        NavigationMenuItemContext<MenuItem>, NavigationSettingsTMCommandParam>
{
    @Inject
    public NavigationTopMenuItemPresenter(AdminTabDisplay display, EventBus eventBus,
            NavigationSettingsMessages messages, DispatchAsync dispatch,
            NavigationTopMenuItemAttributesPresenter attributesPresenter)
    {
        super(display, eventBus, messages, dispatch, attributesPresenter);
    }

    @Override
    protected NavigationMenuItemContext<MenuItem> getContext(MenuItem menuItem,
            DtoContainer<NavigationSettings> settings)
    {
        return new NavigationMenuItemContext<MenuItem>(menuItem, settings);
    }
}