package ru.naumen.metainfoadmin.client.scheduler.forms.creators;

import java.util.Date;
import java.util.Map.Entry;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.core.client.CoreGinjector;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.DateTimeIntervalValidator;
import ru.naumen.core.client.validation.DateTimeValidator;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.validation.SecondsAmountValidator;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.datepicker.HasTimeRoundingMode;
import ru.naumen.core.client.widgets.datepicker.HasTimeRoundingMode.TimeRoundingMode;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.dispatch2.GetCurrentDateAction;
import ru.naumen.metainfo.shared.dispatch2.GetCurrentDateResponse;
import ru.naumen.metainfo.shared.scheduler.PeriodicTrigger;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfo.shared.scheduler.Trigger.CalculateStrategies;
import ru.naumen.metainfo.shared.scheduler.Trigger.Periods;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskMessages;

/**
 * <AUTHOR>
 * @since 31.05.2011
 *
 */
public class PeriodicTriggerCreator extends TriggerCreatorBase implements TriggerCreator
{
    @Inject
    Processor validation;
    @Inject
    private DateTimeIntervalValidator dateTimeIntervalValidator;
    @Inject
    private DateTimeValidator dateTimeValidator;
    @Inject
    private SecondsAmountValidator secondsAmountValidator;
    @Inject
    private NotNullValidator<DateTimeInterval> notNullValidator;
    @Inject
    private NotNullValidator<Date> notNullDateValidator;
    String schTaskCode;
    @Inject
    SchedulerTaskMessages messages;

    @Inject
    TriggerCreatorFactory factory;
    @Inject
    Formatters formatters;

    @Inject
    CoreGinjector injector;
    @Inject
    DispatchAsync service;

    ValidationUnit<DateTimeInterval> intervalValidationUnit;
    ValidationUnit<DateTimeInterval> intervalNotNullVU;

    ValidationUnit<DateTimeInterval> secondsAmountValidationUnit;
    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    @Inject
    SelectListProperty<String, SelectItem> period;
    @Named(PropertiesGinModule.DATE_TIME_INTERVAL)
    @Inject
    Property<DateTimeInterval> interval;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    SelectListProperty<String, SelectItem> calculateStrategy;
    @Named(PropertiesGinModule.DATE_TIME)
    @Inject
    Property<Date> startDate;
    PropertyDialogDisplay dialogDisplay;
    PropertyRegistration<DateTimeInterval> intervalPR;

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {
        this.dialogDisplay = display;

        refreshIntervalField();
        add(validation.validate(startDate, dateTimeValidator));
        add(validation.validate(startDate, notNullDateValidator));
    }

    @Override
    public void bindProperties()
    {
        interval.setCaption(messages.interval());
        interval.setValidationMarker(true);

        add(messages.period(), period);
        add((Property<?>)null);
        add(messages.calculationStrategy(), calculateStrategy);
        addWithMarker(messages.startDate(), startDate);
        ((HasTimeRoundingMode)startDate.getValueWidget()).setTimeRoundingMode(TimeRoundingMode.START_OF_MINUTE);
        period.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                refreshIntervalField();
            }
        });

        calculateStrategy.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                refreshRandomizeDelay();
            }
        });

        for (Entry<String, String> p : factory.getPeriods().entrySet())
        {
            period.<SingleSelectCellList<?>> getValueWidget().addItem(p.getValue(), p.getKey());
        }
        for (Entry<String, String> strategy : factory.getCalculateStrategies().entrySet())
        {
            calculateStrategy.<SingleSelectCellList<?>> getValueWidget().addItem(strategy.getValue(),
                    strategy.getKey());
        }
        if (null != trigger)
        {

            Periods triggerPeriod = ((PeriodicTrigger)trigger).getPeriod();
            if (null == triggerPeriod)
            {
                period.setValue(null);
                interval.setValue(((PeriodicTrigger)trigger).getInterval());
            }
            else
            {
                period.trySetObjValue(triggerPeriod.toString());
                randomizeDelayOn.setValue(sharedSettings.useRandomizeDelay() && trigger.isRandomizeDelayOn());
            }
            startDate.setValue(((PeriodicTrigger)trigger).getStartDate());
            calculateStrategy.trySetObjValue(((PeriodicTrigger)trigger).getStrategy().toString());
        }
        else
        {
            GetCurrentDateAction action = new GetCurrentDateAction();
            getService().execute(action, new BasicCallback<GetCurrentDateResponse>()
            {
                @Override
                protected void handleSuccess(GetCurrentDateResponse value)
                {
                    startDate.setValue(value.getDate());
                }
            });
        }
        ensureDebugIds();
        addRandomizeDelay(messages.randomizeDelay());
    }

    public DispatchAsync getService()
    {
        return service;
    }

    @Override
    public DtoContainer<Trigger> getTrigger()
    {
        if (!validation.validate())
        {
            return null;
        }
        PeriodicTrigger newTrigger = ((PeriodicTrigger)trigger);
        if (null == newTrigger)
        {
            newTrigger = new PeriodicTrigger();
        }
        newTrigger.setStartDate(startDate.getValue());
        newTrigger.setPeriod(null == period.getValue() ? null
                : Periods.valueOf(SelectListPropertyValueExtractor.getValue(period)));
        newTrigger.setInterval(interval.getValue());
        newTrigger.setTitle(getTriggerTitle());
        newTrigger
                .setStrategy(CalculateStrategies.valueOf(SelectListPropertyValueExtractor.getValue(calculateStrategy)));
        newTrigger.setSchTaskCode(schTaskCode);
        newTrigger.setRandomizeDelayOn(sharedSettings.useRandomizeDelay() && randomizeDelayOn.getValue());
        return new DtoContainer<>(newTrigger);
    }

    @Override
    public void init(String schTaskCode, @Nullable DtoContainer<Trigger> trigger)
    {
        this.schTaskCode = schTaskCode;
        this.trigger = trigger == null ? null : (PeriodicTrigger)trigger.get();
    }

    @Override
    public void removeProperties()
    {
        unregValidations();
        super.removeProperties();
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(period, "period");
        DebugIdBuilder.ensureDebugId(interval, "interval");
        DebugIdBuilder.ensureDebugId(calculateStrategy, "calculateStrategy");
        DebugIdBuilder.ensureDebugId(startDate, "startDate");
    }

    private String getTriggerTitle()
    {
        if (period.getValue() == null)
        {
            return messages.periodicRule() + " " + formatters.formatDateTimeInterval(interval.getValue());
        }
        return switch (Periods.valueOf(SelectListPropertyValueExtractor.getValue(period)))
        {
            case DAILY -> messages.periodicRule() + " " + messages.bigDaily();
            case MONTHLY -> messages.periodicRule() + " " + messages.bigMonthly();
            case WEEKLY -> messages.periodicRule() + " " + messages.bigWeekly();
            case YEARLY -> messages.periodicRule() + " " + messages.bigYearly();
            case MILLISECONDS -> throw new IllegalArgumentException("Milliseconds is not supported for view on form.");
        };
    }

    private void refreshIntervalField()
    {
        PropertyRegistration<?> propertyRegistration = propertyRegistrations.get(properties.get(1));
        if (null == period.getValue())
        {
            propertyRegistration.setProperty((Property)interval);
            intervalValidationUnit = validation.validate(interval, dateTimeIntervalValidator);
            intervalNotNullVU = validation.validate(interval, notNullValidator);
            secondsAmountValidationUnit = validation.validate(interval, secondsAmountValidator);
        }
        else
        {
            if (null != propertyRegistration.getProperty())
            {
                propertyRegistration.setProperty(null);
            }
            unregValidations();
        }
        refreshRandomizeDelay();
    }

    private void refreshRandomizeDelay()
    {
        if (!sharedSettings.useRandomizeDelay())
        {
            return;
        }

        PropertyRegistration<?> propertyRegistration = propertyRegistrations.get(randomizeDelayOn);
        if (null != period.getValue() && CalculateStrategies.FROM_START.toString()
                .equals(calculateStrategy.getValue().getUUID()))
        {
            propertyRegistration.setProperty((Property)randomizeDelayOn);
        }
        else
        {
            propertyRegistration.setProperty(null);
            randomizeDelayOn.setValue(false);
        }
    }

    private void unregValidations()
    {
        if (null != intervalValidationUnit)
        {
            intervalValidationUnit.unregister();
            intervalValidationUnit = null;
        }
        if (null != intervalNotNullVU)
        {
            intervalNotNullVU.unregister();
            intervalNotNullVU = null;
        }
        if (null != secondsAmountValidationUnit)
        {
            secondsAmountValidationUnit.unregister();
            secondsAmountValidationUnit = null;
        }
    }
}
