package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.Collection;

import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.tree.metainfo.MetaClassMultiSelectionModelSameClassOnly;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule;

/**
 * Делегат обновления свойства "Класс объектов для добавления" на форме добавления элемента левого меню типа
 * "Кнопка добавления объектов"
 * <AUTHOR>
 * @since 15.12.2021
 */
@Singleton
public class AddButtonLeftMenuPropertyDelegateRefreshImpl
        implements PropertyDelegateRefresh<Collection<DtObject>, PropertyBase<Collection<DtObject>,
        PopupValueCellTree<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly>>>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, PropertyBase<Collection<DtObject>,
                    PopupValueCellTree<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly>> property,
            AsyncCallback<Boolean> callback)
    {
        String typeStr = context.getPropertyValues()
                .getProperty(EditNavigationSettingsFormGinModule.MenuItemPropertyCode.TYPE);
        IMenuItem.MenuItemType type = IMenuItem.MenuItemType.valueOf(typeStr);
        callback.onSuccess(IMenuItem.MenuItemType.addButton.equals(type));
    }
}
