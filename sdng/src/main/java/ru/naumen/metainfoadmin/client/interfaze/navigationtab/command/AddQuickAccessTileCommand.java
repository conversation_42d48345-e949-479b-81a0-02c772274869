package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelElementWrapper;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.QuickAccessTileFormPresenter;

/**
 * Команда добавления плитки быстрого доступа
 *
 * <AUTHOR>
 * @since 17.07.2020
 */
public class AddQuickAccessTileCommand extends BaseCommandImpl<QuickAccessPanelElementWrapper,
        DtoContainer<NavigationSettings>>
{
    public static final String ID = "addQuickTileCommand";

    @Inject
    private
    Provider<QuickAccessTileFormPresenter<ObjectFormAdd>> addQuickTileFormProvider;

    @Inject
    public AddQuickAccessTileCommand(@Assisted QuickAccessPanelTileCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<QuickAccessPanelElementWrapper, DtoContainer<NavigationSettings>> param)
    {
        QuickAccessPanelTileCommandParam p = (QuickAccessPanelTileCommandParam)param;

        QuickAccessTileFormPresenter<ObjectFormAdd> presenter = addQuickTileFormProvider.get();
        QuickAccessTileDTO value = p.getValue() == null ? null : (QuickAccessTileDTO)p.getValue().getWrappable();
        presenter.init(p.getSettings().get(), null, value, p.getCallback());
        presenter.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.ADD;
    }

}