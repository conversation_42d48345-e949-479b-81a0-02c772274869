package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs;

import static ru.naumen.commons.shared.utils.CollectionUtils.map;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.attr.AttrService;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.ValueToSelectItemConverter;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

/**
 * Обший обработчик обновления значения свойства "Представление атрибута"
 * <AUTHOR>
 * @since 17.05.2012
 */
public abstract class PresentationRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    @Inject
    protected PresentationFactories prsFactories;
    @Inject
    protected AttrService attrService;
    @Inject
    protected ValueToSelectItemConverter<String> transformer;

    protected Map<String, Object> getPrsSettings(String type, PropertyContainerContext context)
    {
        if (Constants.LINK_ATTRIBUTE_TYPES.contains(type))
        {
            if ((BOLinksAttributeType.CODE.equals(type) || BackLinkAttributeType.CODE.equals(type))
                && isSelfNested(context))
            {
                return Presentations.SELF_NESTED_LINK;
            }
            return isWithParent(context) ? Presentations.NESTED_LINK : Presentations.FLAT_LINK;
        }
        if (Constants.StringAttributeType.CODE.equals(type))
        {
            MetaClass metaClass = context.getContextValues()
                    .<MetaClass> getProperty(AttributeFormContextValues.METAINFO);
            if (metaClass == null)
            {
                return map(Presentations.SYSTEM_METACLASS, false);
            }
            return map(Presentations.SYSTEM_METACLASS, Constants.SYSTEM_METACLASSES.contains(metaClass.getFqn()));
        }
        return null;
    }

    protected void initPrsSelectList(SingleSelectCellList<?> selectList, String[] prsCodes,
            @Nullable Map<String, Object> settings)
    {
        selectList.clear();
        for (String[] pair : prsFactories.getPresentations(prsCodes, settings))
        {
            selectList.addItem(pair[0], pair[1]);
        }
    }

    private boolean isSelfNested(PropertyContainerContext context)
    {
        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.RELATED_METAINFO);
        if (metaClass == null)
        {
            return false;
        }

        if (!metaClass.hasAttribute(ru.naumen.core.shared.Constants.PARENT_ATTR))
        {
            return false;
        }

        ClassFqn fqn = metaClass.getAttribute(ru.naumen.core.shared.Constants.PARENT_ATTR).getType()
                .<ObjectAttributeType> cast().getRelatedMetaClass();

        if (metaClass.getFqn().isSameClass(fqn))
        {
            return true;
        }

        return false;
    }

    private boolean isWithParent(PropertyContainerContext context)
    {
        MetaClassLite metainfo = context.getContextValues().getProperty(AttributeFormContextValues.RELATED_METAINFO);
        //@formatter:off
        Collection<String> codes = metainfo != null 
                ? CollectionUtils.transform(metainfo.getAttributeFqns(), HasCode.CODE_EXTRACTOR) 
                : Collections.<String> emptyList();
        //@formatter:on

        //для невложенных удалим список выбора
        return codes.contains(ru.naumen.core.shared.Constants.PARENT_ATTR);
    }
}
