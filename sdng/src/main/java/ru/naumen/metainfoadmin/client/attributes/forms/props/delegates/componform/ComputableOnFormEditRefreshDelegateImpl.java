package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.componform;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.ATTRIBUTE;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.HasState;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 *
 * <AUTHOR>
 * @since 16 февр. 2015 г.
 */
public class ComputableOnFormEditRefreshDelegateImpl extends ComputableOnFormRefreshDelegateImpl<ObjectFormEdit>
{
    @Override
    public void refreshProperty(final PropertyContainerContext context, BooleanCheckBoxProperty property,
            final AsyncCallback<Boolean> callback)
    {
        super.refreshProperty(context, property, new BasicCallback<Boolean>()
        {
            @Override
            public void handleSuccess(Boolean value)
            {
                if (!Boolean.TRUE.equals(value))
                {
                    callback.onSuccess(false);
                    return;
                }

                Attribute attribute = context.getContextValues().getProperty(ATTRIBUTE);
                String attrCode = attribute.getCode();
                MetaClassLite metaClass = attribute.getMetaClassLite();

                boolean restrictedSysAttr = AbstractBO.METACLASS.equals(attrCode) || HasState.STATE.equals(attrCode)
                                            || (Employee.FQN.isSameClass(metaClass.getFqn())
                                                && Employee.PASSWORD.equals(attrCode))
                                            || (SuperUser.FQN.isSameClass(metaClass.getFqn())
                                                && (SuperUser.PASSWORD.equals(attrCode) || SuperUser.LOGIN.equals(
                        attrCode)
                                                    || SuperUser.PERMISSION_EXPIRATION_DATE.equals(attrCode)));

                callback.onSuccess(!restrictedSysAttr);
            }
        });
    }
}
