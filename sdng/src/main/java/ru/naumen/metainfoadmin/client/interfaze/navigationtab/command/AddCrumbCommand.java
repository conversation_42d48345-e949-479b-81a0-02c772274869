package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.CrumbFormPresenter;

/**
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public class AddCrumbCommand extends BaseCommandImpl<Crumb, DtoContainer<NavigationSettings>>
{
    public static final String ID = "addCrumbCommand";

    @Inject
    Provider<CrumbFormPresenter<ObjectFormAdd>> addCrumbFormProvider;

    @Inject
    public AddCrumbCommand(@Assisted BreadCrumbCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<Crumb, DtoContainer<NavigationSettings>> param)
    {
        BreadCrumbCommandParam p = (BreadCrumbCommandParam)param;

        CrumbFormPresenter<ObjectFormAdd> presenter = addCrumbFormProvider.get();
        presenter.init(p.getValue(), p.getCallback(), true);
        presenter.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.ADD;
    }
}
