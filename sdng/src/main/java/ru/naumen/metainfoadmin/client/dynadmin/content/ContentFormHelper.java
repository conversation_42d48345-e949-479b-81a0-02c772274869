package ru.naumen.metainfoadmin.client.dynadmin.content;

import static ru.naumen.commons.shared.utils.ComparatorUtils.compose;
import static ru.naumen.commons.shared.utils.ComparatorUtils.ignoreCaseComparator;

import java.util.Arrays;
import java.util.List;
import java.util.Map.Entry;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.base.Function;
import com.google.common.collect.ImmutableList;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.IRadioButtonGroup;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.RadioButtonGroupProperty;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.AttributeGroupInfo;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosAction;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosResponse;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.PagerPosition;
import ru.naumen.metainfo.shared.ui.PagingSettings;
import ru.naumen.metainfo.shared.ui.Position;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.dynadmin.parenttree.ParentTreeFactoryContext;

/**
 * Вспомогательные методы для инициализации элементов форм добавления/редактирования контентов
 * <AUTHOR>
 * @since 04.07.2013
 *
 */
public class ContentFormHelper
{
    private static <A, B> Pair<A, B> pair(A a, B b)
    {
        return new Pair<A, B>(a, b);
    }

    @Inject
    private DispatchAsync dispatch;
    @Inject
    protected AdminDialogMessages messages;
    @Inject
    protected CommonMessages cmessages;

    // список представлений контента (<название, представление>)
    private List<Pair<String, String>> fileListPresentationValues;

    // <Position, Название местоположения контента в текущей локали>
    private List<Pair<Position, String>> positionList;

    /**
     * Формирует поле выбора группы атрибутов
     */
    public void asyncInitGroupList(final Property<SelectItem> attributeGroupList, Iterable<ClassFqn> fqns,
            @Nullable final String value)
    {
        GetAttributeGroupInfosAction action = new GetAttributeGroupInfosAction(fqns);
        dispatch.execute(action, new BasicCallback<GetAttributeGroupInfosResponse>()
        {
            @Override
            protected void handleSuccess(GetAttributeGroupInfosResponse response)
            {
                SingleSelectCellList<String> groupsWidget = attributeGroupList.getValueWidget();
                for (Entry<ClassFqn, ? extends List<AttributeGroupInfo>> entry : response.getGroupInfos().entrySet())
                {
                    for (AttributeGroupInfo groupInfo : entry.getValue())
                    {
                        groupsWidget.addItem(groupInfo.getTitle(), groupInfo.getCode());
                    }
                }
                if (value != null)
                {
                    groupsWidget.setObjValue(value);
                }
            }
        });
        DebugIdBuilder.ensureDebugId(attributeGroupList, "attributeGroup");
        attributeGroupList.setValidationMarker(true);
        attributeGroupList.setCaption(cmessages.attributeGroup());
    }

    /**
     * Фомирует поле выбора представления для контента "Список файлов"
     */
    public void initFileListPresentaion(Property<SelectItem> presentationList)
    {
        presentationList.setValidationMarker(true);
        presentationList.setCaption(cmessages.presentation());
        DebugIdBuilder.ensureDebugId(presentationList, "presentationList");
        SingleSelectCellList<?> presentationField = presentationList.getValueWidget();
        for (Pair<String, String> item : fileListPresentationValues)
        {
            presentationField.addItem(item.getLeft(), item.getRight());
        }
    }

    public void initPagingLocation(RadioButtonGroupProperty property)
    {
        property.setCaption(cmessages.pagingLocationSettings());
        property.setValidationMarker(true);
        DebugIdBuilder.ensureDebugId(property, "pagingPosition");

        IRadioButtonGroup group = property.getValueWidget();
        group.addItem(PagerPosition.TOP.value(), cmessages.aboveList());
        group.addItem(PagerPosition.BOTTOM.value(), cmessages.underList());
        group.addItem(PagerPosition.TOP_AND_BOTTOM.value(), cmessages.aboveAndUnderList());

        group.setValue(PagingSettings.getDefaultSettings().getPosition().value());
    }

    /**
     * Фомирует поле выбора расположения контента
     */
    public void initPositionProperty(Property<SelectItem> position)
    {
        position.setValidationMarker(true);
        DebugIdBuilder.ensureDebugId(position, "position");
        for (Pair<Position, String> p : positionList)
        {
            position.<SingleSelectCellList<?>> getValueWidget().addItem(p.getRight(), p.getLeft().toString());
        }
        position.setCaption(messages.position());
    }

    @Inject
    protected void init()
    {
        //@formatter:off
        fileListPresentationValues = ImmutableList.of(
                pair(cmessages.icons(),PresentationType.DEFAULT.getCode()), 
                pair(cmessages.list(), PresentationType.ADVLIST.getCode())
                );
        List<Pair<Position, String>> positions = Arrays.asList(
                pair(Position.LEFT, messages.toLeft()),
                pair(Position.RIGHT, messages.toRight()), 
                pair(Position.FULL, messages.toFull())
                );
        //@formatter:on
        // Сортировка делается таким образом, так как в разных языках названия расположений контента будут
        // сортироваться по-разному,
        //<Position, Название местоположения контента в текущей локали>
        positions.sort(compose(ignoreCaseComparator(), new Function<Pair<Position, String>, String>()
        {
            @Override
            public String apply(Pair<Position, String> input)
            {
                return input.getRight();
            }
        }));
        positionList = ImmutableList.copyOf(positions);
    }

    /**
     * Создает поле формы - дерево для выбора родителя
     *
     * @param treeContext - контекст, на основе которого создается дерево
     * @param tree - дерево
     * @param defaultParent - родитель по умолчанию, используется в качестве значения по умолчанию
     * @param caption - название поля поля
     * @return Property для формы
     */
    public static Property<DtObject> createTreeProperty(ParentTreeFactoryContext treeContext,
            HasValueOrThrow<DtObject> tree, Content defaultParent, String caption)
    {
        PropertyBase<DtObject, HasValueOrThrow<DtObject>> parent = new PropertyBase<>(tree);
        parent.setValue(treeContext.getDtObjectByContent(defaultParent.getParent()));
        parent.setCaption(caption);
        DebugIdBuilder.ensureDebugId(parent, "parent");
        tree.addValueChangeHandler(event ->
        {
            if (tree instanceof PopupValueCellTree<?, ?, ?>)
            {
                ((PopupValueCellTree<?, ?, ?>)tree).getTree().openTreeToSelectedItem();
            }
        });
        return parent;
    }
}
