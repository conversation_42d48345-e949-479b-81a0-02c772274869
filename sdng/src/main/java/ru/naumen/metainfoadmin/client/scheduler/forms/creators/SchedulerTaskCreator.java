package ru.naumen.metainfoadmin.client.scheduler.forms.creators;

import ru.naumen.core.client.IFormPropertiesCreator;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfoadmin.client.scheduler.forms.SchedulerTaskFormPresenterImpl;

/**
 * Интерфейс для {@link Presenter} , создающего задачу планировщика.
 * <p>
 * По сути является частью {@link SchedulerTaskFormPresenterImpl формы} добавления задачи планировщика.
 *
 * <AUTHOR>
 *
 */
public interface SchedulerTaskCreator extends IFormPropertiesCreator
{
    /**
     * Возвращает задачу планировщика созданную на основе данных введенных пользователем на форме добавления. Если
     * задача планировщика не может
     * быть создана (например, заполнены не все поля), то возвращает null
     *
     * @return созданную задачу планировщика, или возвращает null если задача планировщика не может быть создана
     */
    SchedulerTask getSchedulerTask();

    void init(SchedulerTask schTask);
}
