package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 *
 */
public class InputmaskModeVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        String inputMaskMode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_MODE);
        context.setProperty(AttributeFormPropertyCode.INPUTMASK, getInputMask(context, inputMaskMode));
        context.getPropertyControllers().get(AttributeFormPropertyCode.DEFAULT_VALUE).refresh();
        context.getEventBus().fireEvent(new InputMaskModeChangeEvent(inputMaskMode));
    }

    private static String getInputMask(PropertyContainerContext context, String inputMaskMode)
    {
        switch (inputMaskMode)
        {
            case StringAttributeType.INPUT_MASK_ALIAS:
                return context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_ALIAS);
            case StringAttributeType.INPUT_MASK_DEFINITIONS:
                return context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_DEFINITIONS);
            case StringAttributeType.INPUT_MASK_REGEX:
                return context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_REGEX);
            default:
                return "";
        }
    }
}