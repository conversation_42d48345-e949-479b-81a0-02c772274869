package ru.naumen.metainfoadmin.client.escalation.schemes.columns;

import java.util.ArrayList;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.gwt.place.shared.Place;
import com.google.gwt.user.cellview.client.Column;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumn;
import ru.naumen.core.client.widgets.columns.PlaceProvider;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemePlace;

/**
 * <AUTHOR>
 * @since 23.07.2012
 *
 */
public class EscalationSchemesColumnsGinModule extends AbstractGinModule
{
    public static class ColumnsProvider implements Provider<ArrayList<Pair<String,
            Column<DtoContainer<EscalationScheme>, ?>>>>
    {
        @Inject
        LinkToPlaceColumn<DtoContainer<EscalationScheme>> titleColumn;
        @Inject
        TargetObjectsColumn targetObjectsColumn;
        @Inject
        DescriptionColumn descriptionColumn;
        @Inject
        TimerColumn timerColumn;
        @Inject
        StateColumn stateColumn;
        @Inject
        AdminWidgetResources resources;
        @Inject
        CommonMessages messages;
        @Inject
        AttributesMessages attrMessages;
        @Inject
        I18nUtil i18nUtil;

        @Override
        public ArrayList<Pair<String, Column<DtoContainer<EscalationScheme>, ?>>> get()
        {
            ArrayList<Pair<String, Column<DtoContainer<EscalationScheme>, ?>>> result = new ArrayList<>();
            appendColumn(result, messages.title(), getTitleColumn());
            appendColumn(result, messages.objects(), targetObjectsColumn);
            appendColumn(result, messages.description(), descriptionColumn);
            appendColumn(result, attrMessages.timerDefinition(), timerColumn);
            appendColumn(result, messages.on(), stateColumn);
            stateColumn.setCellStyleNames(resources.tables().tableImageCell() + " " + resources.all().alignCenter());
            return result;
        }

        private void appendColumn(ArrayList<Pair<String, Column<DtoContainer<EscalationScheme>, ?>>> list, String title,
                Column<DtoContainer<EscalationScheme>, ?> column)
        {
            list.add(new Pair<>(title, column));
        }

        private LinkToPlaceColumn<DtoContainer<EscalationScheme>> getTitleColumn()
        {
            titleColumn.setRenderFunction(object -> i18nUtil.getLocalizedTitle(object.get()));
            return titleColumn;
        }
    }

    public static final String COLUMNS = "escalationSchemeColumns";

    @Override
    protected void configure()
    {
        //@formatter:off
        bind(new TypeLiteral<ArrayList<Pair<String, Column<DtoContainer<EscalationScheme>, ?>>>>(){})
            .annotatedWith(Names.named(COLUMNS))
            .toProvider(ColumnsProvider.class);

        install(new GinFactoryModuleBuilder()
            .implement(Place.class, EscalationSchemePlace.class)
            .build(new TypeLiteral<PlaceProvider<DtoContainer<EscalationScheme>>>(){}));
        //@formatter:on
    }
}
