package ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

/**
 * <AUTHOR>
 * @since 04 дек. 2013 г.
 *
 */
public class NavigationMenuItemContext<M extends IMenuItem>
{
    private M item;
    private DtoContainer<NavigationSettings> settings;

    public NavigationMenuItemContext(M item, DtoContainer<NavigationSettings> settings)
    {
        this.item = item;
        this.settings = settings;
    }

    public M getItem()
    {
        return item;
    }

    public DtoContainer<NavigationSettings> getSettings()
    {
        return settings;
    }

    public void setItem(M item)
    {
        this.item = item;
    }

    public void setSettings(DtoContainer<NavigationSettings> settings)
    {
        this.settings = settings;
    }
}