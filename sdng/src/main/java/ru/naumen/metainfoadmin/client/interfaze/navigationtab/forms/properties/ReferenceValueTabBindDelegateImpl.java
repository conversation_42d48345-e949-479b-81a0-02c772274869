/**
 *
 */
package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Singleton;

import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.Reference;

/**
 * <AUTHOR>
 * @since 13 февр. 2014 г.
 *
 */
@Singleton
public class ReferenceValueTabBindDelegateImpl extends
        PropertyDelegateBindImpl<SelectItem, SingleSelectProperty<Reference>>
{

    @Override
    public void bindProperty(PropertyContainerContext context, SingleSelectProperty<Reference> property,
            AsyncCallback<Void> callback)
    {
        property.getValueWidget().setHasEmptyOption(true);
        callback.onSuccess(null);
    }
}