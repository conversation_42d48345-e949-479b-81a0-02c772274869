package ru.naumen.metainfoadmin.client.eventaction.template;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.PropertyDecorator;
import ru.naumen.core.client.widgets.WidgetResources;

/**
 * Декоратор, добавляющий ссылку-переключатель к названию свойства.
 * <AUTHOR>
 * @since Jan 26, 2017
 */
public abstract class ToggleLinkPropertyDecorator<T> extends PropertyDecorator<T>
{
    private boolean state = false;
    private FlowPanel captionWidget;

    private HandlerRegistration clickHandlerRegistration;

    protected Anchor toggleLink;

    @Inject
    protected WidgetResources resources;

    public ToggleLinkPropertyDecorator(Property<T> delegate)
    {
        super(delegate);
    }

    @Override
    public IsWidget getCaptionWidget()
    {
        return captionWidget;
    }

    public boolean getState()
    {
        return state;
    }

    public void setState(boolean state)
    {
        if (this.state != state)
        {
            this.state = state;
            refreshLink();
            onChangeState(state);
        }
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        if (null != clickHandlerRegistration)
        {
            clickHandlerRegistration.removeHandler();
        }
        super.unbind(callback);
    }

    protected abstract String getDisableLinkText();

    protected abstract String getEnableLinkText();

    protected void initWidgets()
    {
        resources.form().ensureInjected();
        resources.all().ensureInjected();

        captionWidget = new FlowPanel();
        captionWidget.addStyleName(resources.form().decoratedProperty());
        captionWidget.add(delegate.getCaptionWidget());
        toggleLink = new Anchor();
        toggleLink.addStyleName(resources.form().propertyDecorator());
        toggleLink.addStyleName(resources.buttons().defaultLink());
        refreshLink();
        captionWidget.add(toggleLink);
        addClickHandler();
    }

    protected boolean isLinkVisible()
    {
        return true;
    }

    protected abstract void onChangeState(boolean state);

    protected void refreshLink()
    {
        toggleLink.setVisible(isLinkVisible());
        toggleLink.setText(state ? getDisableLinkText() : getEnableLinkText());
    }

    private void addClickHandler()
    {
        clickHandlerRegistration = toggleLink.addClickHandler(event -> setState(!state));
    }
}
