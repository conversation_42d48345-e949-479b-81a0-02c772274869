package ru.naumen.metainfoadmin.client.jmsqueue.service;

import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.AddLinkToJMSQueueAction;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.BreakLinkEventAction;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.DeleteJMSQueueAction;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.GetAvailableEventActionsForJMSQueueAction;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.GetAvailableJMSQueuesForEventAction;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.GetCountJMSUserQueuesAction;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.GetEventActionsByJMSQueueAction;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.GetJMSQueueCountMessagesAction;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.GetJMSQueuesForFiltrationAction;
import ru.naumen.metainfo.shared.jmsqueue.dispatch.GetPossibleArrivedMessageToQueueEventAction;

/**
 * Реализация {@link JMSQueueServiceAsync}
 * <AUTHOR>
 * @since 26.04.2021
 **/
public class JMSQueueServiceAsyncImpl implements JMSQueueServiceAsync
{
    private final DispatchAsync dispatch;

    @Inject
    public JMSQueueServiceAsyncImpl(DispatchAsync dispatch)
    {
        this.dispatch = dispatch;
    }

    @Override
    public void addLink(List<String> eventActionCodes, String jmsQueueCode, BasicCallback<SimpleResult<Void>> callback)
    {
        dispatch.execute(new AddLinkToJMSQueueAction(eventActionCodes, jmsQueueCode),
                CallbackDecorator.nullReturn(callback));
    }

    @Override
    public void breakLink(String eventActionCode, AsyncCallback<Void> callback)
    {
        dispatch.execute(new BreakLinkEventAction(Lists.newArrayList(eventActionCode)),
                CallbackDecorator.nullReturn(callback));
    }

    @Override
    public void breakLinks(List<String> eventActionsCodes, AsyncCallback<Void> callback)
    {
        dispatch.execute(new BreakLinkEventAction(eventActionsCodes), CallbackDecorator.nullReturn(callback));
    }

    @Override
    public void getCountMessage(String jmsQueueCode, AsyncCallback<SimpleResult<Integer>> callback)
    {
        dispatch.execute(new GetJMSQueueCountMessagesAction(jmsQueueCode), new BasicCallback<SimpleResult<Integer>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<Integer> value)
            {
                callback.onSuccess(value);
            }
        });
    }

    public void getCountJMSUserQueues(BasicCallback<SimpleResult<Integer>> callback)
    {
        dispatch.execute(new GetCountJMSUserQueuesAction(), callback);
    }

    @Override
    public void getAvailableEventActionsForJMSQueue(String jmsQueueCode,
            BasicCallback<SimpleResult<List<DtObject>>> callback)
    {
        dispatch.execute(new GetAvailableEventActionsForJMSQueueAction(jmsQueueCode), callback);
    }

    @Override
    public void getAvailableJMSQueuesForEventAction(EventAction eventAction,
            BasicCallback<SimpleResult<List<DtObject>>> callback)
    {
        dispatch.execute(new GetAvailableJMSQueuesForEventAction(eventAction), callback);
    }

    @Override
    public void deleteJMSQueue(String code, AsyncCallback<Void> callback)
    {
        dispatch.execute(new DeleteJMSQueueAction(code), CallbackDecorator.nullReturn(callback));
    }

    @Override
    public void getEventActionsByJMSQueue(String jmsQueueCode, BasicCallback<SimpleResult<Object>> callback)
    {
        dispatch.execute(new GetEventActionsByJMSQueueAction(jmsQueueCode), callback);
    }

    @Override
    public void getJMSQueuesForFiltration(BasicCallback<SimpleResult<List<DtObject>>> callback)
    {
        dispatch.execute(new GetJMSQueuesForFiltrationAction(), callback);
    }

    @Override
    public void possibleArrivedMessageToQueueEvent(BasicCallback<SimpleResult<Boolean>> callback)
    {
        dispatch.execute(new GetPossibleArrivedMessageToQueueEventAction(), callback);
    }
}