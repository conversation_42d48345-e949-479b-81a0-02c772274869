package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import static ru.naumen.core.client.widgets.RichTextView.DEFAULT_IFRAME_WIDTH;
import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.USER_INTERFACE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.ArrayList;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.IFrameElement;
import com.google.gwt.dom.client.NodeList;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.SimplePanel;
import com.google.gwt.user.client.ui.Widget;

import jakarta.inject.Inject;
import ru.naumen.admin.client.permission.AdminAccessDeniedHandler;
import ru.naumen.admin.client.permission.AdminPermissionHelper;
import ru.naumen.core.client.layout.Band;
import ru.naumen.core.client.layout.LayoutBuilder;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfoadmin.client.common.content.FlowPresenter;
import ru.naumen.metainfoadmin.client.common.content.LayoutEditorResources;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.LayoutContentPresenter.ResizeColumnCommand;

/**
 * @see LayoutContentDisplay
 *
 * <AUTHOR>
 *
 */
public class LayoutContentDisplayImpl extends Composite implements LayoutContentDisplay, ResizableColumnsOwner
{
    /**
     * Cуммарная ширина отсупов между столбцами с контентами
     */
    private static final int PADDING_BETWEEN_CONTENT_COLUMNS = 30;

    /**
     * Минимальная ширина области в режиме редактирования разметки,
     * при которой на области выводим ширину в процентах
     */
    private static final int MIN_WIDTH_FOR_PERCENTS = 180;

    /**
     * Ширина атрибута типа rtf / iframe в режиме редактирования разметки
     */
    private static final int WIDTH_FOR_RTF = DEFAULT_IFRAME_WIDTH;

    /**
     * Панели для режима редактирования разметки
     */
    private final NauSplitLayoutPanel splitPanel;
    private final SimplePanel leftSplitPanelWrapper;
    private final SimplePanel rightSplitPanelWrapper;
    private SimplePanel layoutEditPanel;

    private boolean isInEditLayoutMode;
    private int leftColumnWidth;
    private boolean isRootLayout;
    /**
     * Выставляется в true фактически только для верхнеуровнего лэйаута,
     * у которого есть тул панель
     */
    private boolean hasLayoutModeOffset;

    @Inject
    private WidgetResources resources;
    @Inject
    private LayoutEditorResources layoutEditorResources;
    @Inject
    private AdminPermissionHelper adminPermissionHelper;

    private final FlowPanel mainVertical = new FlowPanel();
    private ResizeColumnCommand resizeColumnCommand;

    public LayoutContentDisplayImpl()
    {
        initWidget(mainVertical);
        leftSplitPanelWrapper = new SimplePanel(new Label());
        rightSplitPanelWrapper = new SimplePanel(new Label());

        splitPanel = new NauSplitLayoutPanel(this);
        splitPanel.addWest(leftSplitPanelWrapper, 100);
        splitPanel.add(rightSplitPanelWrapper);

        this.isInEditLayoutMode = false;
        this.hasLayoutModeOffset = false;
        leftSplitPanelWrapper.getWidget().setVisible(false);
        rightSplitPanelWrapper.getWidget().setVisible(false);

        DebugIdBuilder.ensureDebugId(leftSplitPanelWrapper.asWidget(), "leftSplitPanel");
        DebugIdBuilder.ensureDebugId(rightSplitPanelWrapper.asWidget(), "rightSplitPanel");
    }

    @Override
    public void drawContent(ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands, int leftColumnWidth,
            boolean isRootLayout)
    {
        clear();
        this.leftColumnWidth = leftColumnWidth;
        this.isRootLayout = isRootLayout;
        draw(bands);

        if (this.isInEditLayoutMode && !moveSplitterBetweenColumns(false))
        {
            splitPanel.getAssociatedSplitter(leftSplitPanelWrapper).setAssociatedWidgetSize(
                    mainVertical.getOffsetWidth() * leftColumnWidth / 100);
            showPercents(leftColumnWidth);
        }
    }

    @Override
    public void enableLayoutMode(boolean enable)
    {
        this.isInEditLayoutMode = enable;
        if (enable)
        {
            leftSplitPanelWrapper.getWidget().setVisible(true);
            rightSplitPanelWrapper.getWidget().setVisible(true);
            layoutEditPanel.addStyleName(layoutEditorResources.style().shownPanel());
            if (!moveSplitterBetweenColumns(false))
            {
                splitPanel.getAssociatedSplitter(leftSplitPanelWrapper).setAssociatedWidgetSize(
                        mainVertical.getOffsetWidth() * this.leftColumnWidth / 100);
                showPercents(this.leftColumnWidth);
            }

            if (this.hasLayoutModeOffset)
            {
                leftSplitPanelWrapper.addStyleName(layoutEditorResources.style().fixedPanelPart());
                rightSplitPanelWrapper.addStyleName(layoutEditorResources.style().fixedPanelPart());
            }
            else
            {
                leftSplitPanelWrapper.removeStyleName(layoutEditorResources.style().fixedPanelPart());
                rightSplitPanelWrapper.removeStyleName(layoutEditorResources.style().fixedPanelPart());
            }

            if (this.hasLayoutModeOffset)
            {
                layoutEditPanel.addStyleName(layoutEditorResources.style().modeOffset());
            }
        }
        else
        {
            leftSplitPanelWrapper.getWidget().setVisible(false);
            rightSplitPanelWrapper.getWidget().setVisible(false);
            layoutEditPanel.removeStyleName(layoutEditorResources.style().shownPanel());
        }
    }

    @Override
    public void setSplitterEnabled(boolean enabled)
    {
        splitPanel.getAssociatedSplitter(leftSplitPanelWrapper).setEnabled(enabled);
    }

    @Override
    public Widget getLayoutEditPanel()
    {
        return this.layoutEditPanel;
    }

    @Override
    public void setLayoutEditPanel(SimplePanel panel)
    {
        layoutEditPanel = panel;
        layoutEditPanel.addStyleName(layoutEditorResources.style().backgroundPanel());
        layoutEditPanel.add(splitPanel);
    }

    @Override
    public void initResizeColumnCommand(ResizeColumnCommand resizeColumnCommand)
    {
        this.resizeColumnCommand = resizeColumnCommand;
    }

    /**
     * Переместить разделитель в позицию между столбцами
     * @param resizeContent признак необходимости подстраивать контенты под положение разделителя
     * @return статус перемещения (false - переместить не удалось так как колонок нет)
     */
    @Override
    public boolean moveSplitterBetweenColumns(boolean resizeContent)
    {
        int biggestLeftColumnElement = -1;
        int biggestRightColumnElement = -1;
        int totalWidth = mainVertical.getOffsetWidth();

        if (totalWidth == 0)
        {
            return false;
        }

        for (int i = 0; i < mainVertical.getWidgetCount(); i++)
        {
            Widget row = mainVertical.getWidget(i);
            if (row instanceof FlowPanel)
            {
                FlowPanel flowPanelRow = (FlowPanel)row;
                if (flowPanelRow.getWidgetCount() == 2)
                {
                    if (flowPanelRow.getWidget(0).getOffsetWidth() > biggestLeftColumnElement
                        && ((FlowPanel)flowPanelRow.getWidget(0)).getWidgetCount() > 0)
                    {
                        biggestLeftColumnElement = flowPanelRow.getWidget(0).getOffsetWidth();
                    }

                    if (flowPanelRow.getWidget(1).getOffsetWidth() > biggestRightColumnElement
                        && ((FlowPanel)flowPanelRow.getWidget(1)).getWidgetCount() > 0)
                    {
                        biggestRightColumnElement = flowPanelRow.getWidget(1).getOffsetWidth();
                    }
                }
            }
        }

        int actualWidth;

        if (biggestLeftColumnElement == -1 && biggestRightColumnElement == -1)
        {
            // Либо контентов нет, либо все расположены по всей ширине
            if (resizeContent)
            {
                this.leftColumnWidth = Math.round(getActualWidthInPercents());
            }
            return false;
        }
        else if (biggestLeftColumnElement == -1 && biggestRightColumnElement != -1)
        {
            // Слева контентов нет, есть только справа            
            actualWidth = totalWidth - biggestRightColumnElement;
        }
        else
        {
            // Есть контены слева (и, возможно, справа)           
            actualWidth = biggestLeftColumnElement;
            if (biggestLeftColumnElement + biggestRightColumnElement - PADDING_BETWEEN_CONTENT_COLUMNS > totalWidth
                && Math.round(getActualWidthInPercents()) > this.leftColumnWidth)
            {
                actualWidth = totalWidth - biggestRightColumnElement - PADDING_BETWEEN_CONTENT_COLUMNS;
            }
        }

        float updatedWidthInPercents = 100.0f * actualWidth / totalWidth;
        splitPanel.getAssociatedSplitter(leftSplitPanelWrapper).setAssociatedWidgetSize(
                actualWidth - splitPanel.getAssociatedSplitter(leftSplitPanelWrapper).getOffsetWidth() / 2);
        if (resizeContent)
        {
            resizeContents(updatedWidthInPercents);
            this.leftColumnWidth = Math.round(updatedWidthInPercents);
        }

        onColumnResize();

        return true;
    }

    @Override
    public void onColumnResize()
    {
        Scheduler.get().scheduleDeferred(new Scheduler.ScheduledCommand()
        {
            @Override
            public void execute()
            {
                showPercents(Math.round(getActualWidthInPercents()));
            }
        });
    }

    @Override
    public void onColumnResizeCompleted()
    {
        adminPermissionHelper.runWithCheckPermissionByAccessMarker(USER_INTERFACE, EDIT, adminAccessDeniedHandler, () ->
        {
            float widthToSet = getActualWidthInPercents();
            forceContentsResize(widthToSet);
            resizeIframes();
            resizeContents(widthToSet);

            // Необходимо дождаться изменения ширины колонок
            Scheduler.get().scheduleDeferred(new Scheduler.ScheduledCommand()
            {
                @Override
                public void execute()
                {
                    moveSplitterBetweenColumns(true);
                    if (resizeColumnCommand != null)
                    {
                        resizeColumnCommand.setLeftColumnWidth(leftColumnWidth);
                        resizeColumnCommand.execute();
                    }
                }
            });
        });
    }

    private final AdminAccessDeniedHandler adminAccessDeniedHandler = permissionType ->
    {
        if (EDIT.equals(permissionType))
        {
            adminPermissionHelper.showAccessMarkerDeniedError();
        }
    };

    @Override
    public void setLayoutModeOffset()
    {
        this.hasLayoutModeOffset = true;
    }

    @Override
    public void setStyleForFirstWidget(Widget w)
    {
        w.addStyleName(resources.contentLayout().markeredContent());
    }

    private void clear()
    {
        while (mainVertical.getWidgetCount() > 0)
        {
            mainVertical.getWidget(0).removeFromParent();
        }
    }

    private void draw(ArrayList<Band<FlowPresenter<FlowContent, UIContext>>> bands)
    {
        layoutEditorResources.style().ensureInjected();
        splitPanel.addStyleName(layoutEditorResources.style().panel());

        leftSplitPanelWrapper.addStyleName(layoutEditorResources.style().panelPart());
        splitPanel.getAssociatedSplitter(leftSplitPanelWrapper).setStyleName(layoutEditorResources.style().dragger());
        rightSplitPanelWrapper.addStyleName(layoutEditorResources.style().panelPart());

        int bandsCount = bands.size();
        for (int i = 0; i < bandsCount; i++)
        {
            Band<FlowPresenter<FlowContent, UIContext>> band = bands.get(i);
            mainVertical.add(LayoutBuilder.createHorizontal(band, this.leftColumnWidth, null,
                    this.isRootLayout, i == 0, i == bandsCount - 1));
        }
    }

    private void forceContentsResize(float widthToSet)
    {
        int expectedLeftColumnWidth = mainVertical.getOffsetWidth() * Math.round(widthToSet) / 100;
        int expectedRightColumnWidth = mainVertical.getOffsetWidth() * Math.round(100 - widthToSet) / 100;
        int maxExpectedColumnWidth = Window.getClientWidth() - mainVertical.getAbsoluteLeft() - MIN_WIDTH_FOR_PERCENTS;

        resizeColumnCommand.preColumnWidthChanging(
                expectedLeftColumnWidth > maxExpectedColumnWidth ? maxExpectedColumnWidth : expectedLeftColumnWidth,
                expectedRightColumnWidth > maxExpectedColumnWidth ? maxExpectedColumnWidth : expectedRightColumnWidth);
    }

    /**
     * @return выставленная с помощью разделителя ширина столбца в процентах 
     */
    private float getActualWidthInPercents()
    {
        // @formatter:off
        float splitterPercents = 100.0f *
               (leftSplitPanelWrapper.getOffsetWidth() + splitPanel.getAssociatedSplitter(leftSplitPanelWrapper).getOffsetWidth()/2) / 
               (splitPanel.getOffsetWidth() - PADDING_BETWEEN_CONTENT_COLUMNS); 
        // @formatter:on

        if (splitterPercents > 99)
        {
            splitterPercents = 99;
        }

        if (splitterPercents < 1)
        {
            splitterPercents = 1;
        }

        return splitterPercents;
    }

    /**
     * Изменяет размер контентов в колонках
     * @param leftColumnWidth ширина левой колонки в процентах
     */
    private void resizeContents(float leftColumnWidth)
    {
        for (int i = 0; i < mainVertical.getWidgetCount(); i++)
        {
            Widget row = mainVertical.getWidget(i);
            if (row instanceof FlowPanel)
            {
                FlowPanel flowPanelRow = (FlowPanel)row;
                if (flowPanelRow.getWidgetCount() == 2)
                {
                    flowPanelRow.getWidget(0).setWidth(Float.toString(leftColumnWidth) + '%');
                    flowPanelRow.getWidget(1).setWidth(Float.toString(100 - leftColumnWidth) + '%');
                }
            }
        }
    }

    /**
     * Изменяет размер ifram'ов в колонках
     */
    private void resizeIframes()
    {
        for (int i = 0; i < mainVertical.getWidgetCount(); i++)
        {
            Widget row = mainVertical.getWidget(i);
            if (row instanceof FlowPanel)
            {
                FlowPanel flowPanelRow = (FlowPanel)row;
                if (flowPanelRow.getWidgetCount() == 2)
                {
                    NodeList<Element> iframesLeft = flowPanelRow.getWidget(0)
                            .getElement()
                            .getElementsByTagName(IFrameElement.TAG);
                    for (int j = 0; j < iframesLeft.getLength(); j++)
                    {
                        iframesLeft.getItem(j).getStyle().setWidth(WIDTH_FOR_RTF, Unit.PX);
                    }

                    NodeList<Element> iframesRight = flowPanelRow.getWidget(1)
                            .getElement()
                            .getElementsByTagName(IFrameElement.TAG);
                    for (int j = 0; j < iframesRight.getLength(); j++)
                    {
                        iframesRight.getItem(j).getStyle().setWidth(WIDTH_FOR_RTF, Unit.PX);
                    }
                }
            }
        }
    }

    /**
     * Обновляет значения отображаемых процентов 
     * @param newWidth ширина столбца в процентах
     */
    private void showPercents(int newWidth)
    {
        int leftWidth = newWidth * splitPanel.getOffsetWidth() / 100;
        int rightWidth = (100 - newWidth) * splitPanel.getOffsetWidth() / 100;

        if (leftWidth > Math.round(0.8 * Window.getClientWidth()))
        {
            leftWidth = (int)Math.round(0.8 * Window.getClientWidth());
        }
        else if (rightWidth > Math.round(0.8 * Window.getClientWidth()))
        {
            rightWidth = (int)Math.round(0.8 * Window.getClientWidth());
        }

        if (leftWidth > MIN_WIDTH_FOR_PERCENTS)
        {
            ((Label)leftSplitPanelWrapper.getWidget()).setText(Integer.toString(newWidth) + '%');
            if (this.hasLayoutModeOffset)
            {
                leftSplitPanelWrapper.getWidget().getElement().getStyle().setMarginLeft(leftWidth / 2 - 80, Unit.PX);
            }
        }
        else
        {
            ((Label)leftSplitPanelWrapper.getWidget()).setText("");
        }

        if ((splitPanel.getOffsetWidth() == 0 || rightWidth > MIN_WIDTH_FOR_PERCENTS)
            &&
            (Window.getClientWidth() - rightSplitPanelWrapper.getAbsoluteLeft() - PADDING_BETWEEN_CONTENT_COLUMNS)
            > MIN_WIDTH_FOR_PERCENTS)
        {
            ((Label)rightSplitPanelWrapper.getWidget()).setText(Integer.toString(100 - newWidth) + '%');
            if (this.hasLayoutModeOffset)
            {
                rightSplitPanelWrapper.getWidget().getElement().getStyle().setMarginLeft(rightWidth / 2 - 80, Unit.PX);
            }
        }
        else
        {
            ((Label)rightSplitPanelWrapper.getWidget()).setText("");
        }
    }
}
