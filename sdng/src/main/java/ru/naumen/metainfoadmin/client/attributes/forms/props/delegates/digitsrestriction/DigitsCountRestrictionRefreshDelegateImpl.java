package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.digitsrestriction;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.IntegerBoxInfoProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.DoubleAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import jakarta.inject.Inject;

/**
 * Делегат обновления свойства "ограничение количества знаков после запятой"
 * @param <F>
 *
 * <AUTHOR>
 * @since 10.07.2019
 */
public class DigitsCountRestrictionRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, Long, IntegerBoxInfoProperty>
{
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, IntegerBoxInfoProperty property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(ATTR_TYPE);
        boolean suitableAttrType = DoubleAttributeType.CODE.equals(attrType);

        if (Constants.AttributeOfRelatedObjectSettings.CODE.equalsIgnoreCase(attrType))
        {
            String relatedObjectMetaClass = context.getPropertyValues()
                    .getProperty(AttributeFormPropertyCode.RELATED_OBJECT_METACLASS);

            String attrFqn =
                    context.getPropertyValues().getProperty(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE);
            if (relatedObjectMetaClass == null || attrFqn == null)
            {
                callback.onSuccess(false);
                return;
            }

            String attrCode = AttributeFqn.parse(attrFqn).getCode();
            metainfoService.getMetaClass(AttributeFqn.parse(attrFqn).getClassFqn(), new BasicCallback<MetaClass>()
            {
                @Override
                protected void handleSuccess(MetaClass metaClass)
                {
                    if (metaClass.hasAttribute(attrCode) && DoubleAttributeType.CODE.equals(
                            metaClass.getAttribute(attrCode).getType().getCode()))
                    {
                        context.getPropertyValues()
                                .setProperty(AttributeFormPropertyCode.DIGITS_COUNT_RESTRICTION, property
                                        .getValue());
                        callback.onSuccess(true);
                    }
                    else
                    {
                        context.getPropertyValues().setProperty(AttributeFormPropertyCode.DIGITS_COUNT_RESTRICTION, "");
                        callback.onSuccess(false);
                    }
                }
            });
        }
        else
        {
            if (!suitableAttrType)
            {
                context.getPropertyValues().setProperty(AttributeFormPropertyCode.DIGITS_COUNT_RESTRICTION, "");
            }
            else
            {
                context.getPropertyValues().setProperty(AttributeFormPropertyCode.DIGITS_COUNT_RESTRICTION, property
                        .getValue());
            }
            callback.onSuccess(suitableAttrType);
        }
    }
}
