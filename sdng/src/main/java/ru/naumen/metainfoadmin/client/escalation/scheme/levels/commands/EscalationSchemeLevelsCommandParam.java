package ru.naumen.metainfoadmin.client.escalation.scheme.levels.commands;

import jakarta.annotation.Nullable;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemeContext;

/**
 * Параметры для команд уровней схемы эскалации
 *
 * <AUTHOR>
 * @since 18.12.2018
 */
public class EscalationSchemeLevelsCommandParam extends CommandParam<EscalationSchemeLevel, Void>
{
    private final EscalationSchemeContext context;

    public EscalationSchemeLevelsCommandParam(@Nullable ValueSource<EscalationSchemeLevel> valueSource,
            @Nullable AsyncCallback<Void> callback,
            EscalationSchemeContext context)
    {
        super(valueSource, callback);
        this.context = context;
    }

    @SuppressWarnings("unchecked")
    @Override
    public EscalationSchemeLevelsCommandParam cloneIt()
    {
        return new EscalationSchemeLevelsCommandParam(getValueSource(), getCallback(), getContext());
    }

    public EscalationSchemeContext getContext()
    {
        return context;
    }
}
