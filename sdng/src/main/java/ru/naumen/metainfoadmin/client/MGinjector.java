package ru.naumen.metainfoadmin.client;

import jakarta.inject.Singleton;

import com.google.gwt.inject.client.GinModules;
import com.google.gwt.inject.client.Ginjector;

import ru.naumen.core.client.CoreGinjector;
import ru.naumen.core.client.content.toolbar.ToolBarContentPresenter;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfoadmin.client.attributes.AttributesGinjector;
import ru.naumen.metainfoadmin.client.attributes.commands.CommandsGinjector;
import ru.naumen.metainfoadmin.client.commands.UICommandGinjector;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.escalation.EscalationGinjector;
import ru.naumen.metainfoadmin.client.eventaction.EventActionGinjector;
import ru.naumen.metainfoadmin.client.forms.AddMetaclassDialogPresenter;
import ru.naumen.metainfoadmin.client.forms.CopyMetaclassDialogPresenter;
import ru.naumen.metainfoadmin.client.fts.FtsAdminGinjector;
import ru.naumen.metainfoadmin.client.group.GroupGinjector;
import ru.naumen.metainfoadmin.client.group.command.AttrGroupsCommandsGinjector;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerGinjector;
import ru.naumen.metainfoadmin.client.sec.SecGinjector;
import ru.naumen.metainfoadmin.client.wf.statesetting.StateSettingGinjector;

/**
 * {@link Ginjector} "построителя" настраиваемого пользовательского интерфейса
 * при отображении в интерфейсе настройки групп атрибутов
 *
 * @see google gin
 * <AUTHOR>
 * @since 27.07.2010
 *
 */
@GinModules(MGinModule.class)
public interface MGinjector extends CoreGinjector, CommandsGinjector, SecGinjector, SchedulerGinjector, GroupGinjector,
        AttrGroupsCommandsGinjector, EventActionGinjector, StateSettingGinjector, AttributesGinjector,
        UICommandGinjector, FtsAdminGinjector, EscalationGinjector
{
    interface AddMetaClassCommandFactory
    {
        AddMetaClassCommandImpl create(ClassFqn classFqn);
    }

    @Singleton
    AddMetaClassCommandFactory addMetaClassCommandFactory();

    AddMetaclassDialogPresenter addMetaclassDialogPresenter();

    CopyMetaclassDialogPresenter copyMetaclassDialogPresenter();

    SectionItem sectionDisplayItem();

    ToolBarContentPresenter<UIContext> toolBarContentPresenter();
}
