package ru.naumen.metainfoadmin.client.attributes;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Multimap;
import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.Element;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlowPanel;

import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.grouplist.GroupList.ColumnInfo;
import ru.naumen.core.client.widgets.grouplist.GroupList.GroupInfo;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.AttributeList.GroupCodes;

/**
 * Реализация логики отрисовки списка атрибутов на карточке ДПС (блок "Параметры")
 *
 * <AUTHOR>
 * @since 23 авг. 2018 г.
 *
 */
public class StandardAttributeListCustomizer implements AttributeListCustomizer
{
    private static final AttributesMessages attrMessages = GWT.create(AttributesMessages.class);

    @Override
    public Element createAndFillHeader(List<ColumnInfo<Attribute>> columns, AttributeListImpl attributeList,
            RegistrationContainer registrationContainer)
    {
        return null;
    }

    @Override
    public void fillGroupRow(GroupInfo<String> group, int row, Multimap<String, ButtonToolDisplay> buttons,
            FlexTable grid, AttributeListImpl attributeList)
    {
        FlowPanel panel = new FlowPanel();
        panel.add(attributeList.generateGroupTitle(group));
        for (ButtonToolDisplay button : buttons.get(group.getGroup()))
        {
            panel.add(button);
        }
        grid.setWidget(row, 0, panel);
    }

    @Override
    public boolean fillGroupContent(Collection<Attribute> elementsWithoutAggregates, FlexTable grid,
            AttributeListImpl attributeList)
    {
        return false;
    }

    @Override
    public boolean fillContent(Map<Attribute, Integer> elements, AttributeListImpl attributeList)
    {
        return false;
    }

    @Override
    public void refresh(MetaClass metainfo, AttributeListImpl attributeList)
    {
        attributeList.clear();
        attributeList.addGroup(GroupCodes.SYSTEM, attrMessages.systemAttributes());
        attributeList.addGroup(GroupCodes.FLEX, attrMessages.userAttributes());
        attributeList.addElements(metainfo.getAttributes());
        attributeList.refresh();
    }

    @Override
    public boolean useStickyHeader()
    {
        return false;
    }
}
