/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
@Singleton
public class EscalationVMapItemFormTargetAttrsBindDelegateImpl extends
        PropertyDelegateBindImpl<String, TextBoxProperty>
{
    @Inject
    EscalationVMapItemFormMessages messages;

    @Override
    public void bindProperty(PropertyContainerContext context, TextBoxProperty property, AsyncCallback<Void> callback)
    {
        context.getPropertyValues().setProperty(ValueMapCatalogItem.TARGET_ATTRS, messages.escalationScheme());
        context.setDisabled(ValueMapCatalogItem.TARGET_ATTRS);
        callback.onSuccess(null);
    }
}