package ru.naumen.metainfoadmin.client.dynadmin;

import ru.naumen.core.client.content.AbstractContext;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.shared.dto.DtObject;

import com.google.gwt.event.shared.EventBus;

/**
 * Контекст строчки отображаемой в адвлисте. Каждая строка соответствует DtObject'у.
 *
 * <AUTHOR>
 * @since Feb 25, 2015
 */
public class ListDtObjectContext extends AbstractContext
{
    private DtObject object;

    public ListDtObjectContext(Context context, EventBus eventBus, DtObject object)
    {
        super(context, null, eventBus, null);
        this.object = object;
    }

    public DtObject getObject()
    {
        return object;
    }

    public void setObject(DtObject object)
    {
        this.object = object;
    }
}
