package ru.naumen.metainfoadmin.client.eventaction.list;

import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.Attributes.ACTION;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.Attributes.EVENT;
import static ru.naumen.metainfoadmin.client.eventaction.EventActionsTabsPresenter.EVENT_ACTIONS_TAB_ID;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import com.google.inject.Provider;
import com.google.inject.Singleton;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.NotFilter;
import ru.naumen.core.shared.filters.OrFilter;
import ru.naumen.core.shared.filters.SimpleFilter;
import ru.naumen.core.shared.ui.toolbar.LocalizedToolFactory;
import ru.naumen.core.shared.ui.toolbar.SimpleActionToolFactory;
import ru.naumen.core.shared.ui.toolbar.ToolFactoryInitializer;
import ru.naumen.metainfo.client.eventaction.ActionTypesProvider;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.client.eventaction.EventActionsPresenterSettings;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.MetaClassLite_SnapshotObject;
import ru.naumen.metainfo.shared.elements.MetaClass_SnapshotObject;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.Constants.Titles;
import ru.naumen.metainfo.shared.ui.EventActionList;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.PagerPosition;
import ru.naumen.metainfo.shared.ui.PagingSettings;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.DefaultContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.EventActionAdvlistUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.extended.advlist.AdvListPresentationDisplayImpl;
import ru.naumen.objectlist.client.extended.advlist.AdvListPresenterImpl;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;

/**
 * Фабрика списка действия по событию
 *
 * <AUTHOR>
 * @since Jan 16, 2015
 *
 */
@Singleton
public class EventActionsAdvlistFactory extends AdminCustomAdvlistFactoryBase
{
    public static MetaClass getEventActionMetaClass()
    {
        MetaClass_SnapshotObject metaclass = new MetaClass_SnapshotObject();
        metaclass.__init__fqn(ru.naumen.metainfo.shared.eventaction.Constants.EventAction.FQN);
        return metaclass;
    }

    public static MetaClassLite getEventActionMetaclassLite()
    {
        MetaClassLite_SnapshotObject metaclass = new MetaClassLite_SnapshotObject();
        metaclass.__init__fqn(ru.naumen.metainfo.shared.eventaction.Constants.EventAction.FQN);
        return metaclass;
    }

    @Inject
    private CommonMessages messages;
    @Inject
    private EventActionMessages emessages;
    @Inject
    private EventActionsPresenterSettings settings;
    @Inject
    private ToolFactoryInitializer tfInitializer;
    @Inject
    private Provider<AdvListPresenterImpl<AdvListPresentationDisplayImpl, EventActionList>> eventActionsListPresenterProvider;
    @Inject
    private ActionTypesProvider actionTypesProvider;

    /**
     * Создать список действий по событию.
     *
     * @param permittedTypes - разрешенные типы действий
     * @param prohibitedTypes - запрещенные типы действий
     * @param addEventActionCommandCode код команды добавления действия по событию
     * @param withLinks true, если колонка с названием должна быть ссылкой на карточку
     * @return
     */
    public ListPresenter<EventActionList> create(Set<EventType> permittedTypes, Set<EventType> prohibitedTypes,
            String addEventActionCommandCode, List<AttributeFqn> attrs, boolean withLinks)
    {
        ListPresenter<EventActionList> eventActionsList = eventActionsListPresenterProvider.get();

        MetaClass eventActionMetaClass = getEventActionMetaClass();
        EventActionList content = createContent(eventActionMetaClass, addEventActionCommandCode, attrs);
        content.getFilters().addAll(createAdditionalFilters(permittedTypes, prohibitedTypes));

        ObjectListUIContext context = new EventActionAdvlistUIContext(new DefaultContext(eventActionMetaClass), null,
                false, null, eventActionsList.getListComponents(), withLinks);
        eventActionsList.init(content, context);

        return eventActionsList;
    }

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        return null;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return ru.naumen.metainfo.shared.eventaction.Constants.EventAction.FQN;
    }

    protected void setUuid(EventActionList content)
    {
        content.setUuid(EVENT_ACTIONS_TAB_ID);
    }

    private IObjectFilter createActionFilter(ActionType actionType)
    {
        return new SimpleFilter<DtObject>(ACTION.toString(), new SimpleDtObject(actionType.name(), null));
    }

    private List<IObjectFilter> createAdditionalFilters(Set<EventType> permittedTypes, Set<EventType> prohibitedTypes)
    {
        List<IObjectFilter> filters = new ArrayList<>();
        List<IObjectFilter> permittedEventTypesFilters = new ArrayList<>();
        for (EventType permittedType : permittedTypes)
        {
            permittedEventTypesFilters.add(createEventFilter(permittedType));
        }
        if (!permittedEventTypesFilters.isEmpty())
        {
            filters.add(new OrFilter(permittedEventTypesFilters));
        }
        for (EventType prohibitedType : prohibitedTypes)
        {
            filters.add(new NotFilter(createEventFilter(prohibitedType)));
        }
        List<IObjectFilter> permittedActionTypesFilters = new ArrayList<>();
        for (ActionType actionType : settings.getActionTypes())
        {
            permittedActionTypesFilters.add(createActionFilter(actionType));
        }
        if (!permittedActionTypesFilters.isEmpty())
        {
            filters.add(new OrFilter(permittedActionTypesFilters));
        }
        //Исключаем выборку пушей "Уведомление в МК", когда у клиента отсутствует модуль МК.
        if (!actionTypesProvider.getAvailableActionTypes().contains(ActionType.PushMobileEventAction))
        {
            filters.add(new NotFilter(createActionFilter(ActionType.PushMobileEventAction)));
        }
        return filters;
    }

    private EventActionList createContent(MetaClass metaclass, String addEventActionCommandCode,
            List<AttributeFqn> attrs)
    {
        EventActionList content = new EventActionList();
        content.setClazz(metaclass.getFqn());
        content.setPresentation(PresentationType.ADVLIST.getCode());
        setUuid(content);

        PagingSettings pagingSettings = PagingSettings.getDefaultSettings();
        content.getDefaultSettings().setPageSize(100);
        pagingSettings.setPosition(PagerPosition.BOTTOM);
        content.setPagingSettings(pagingSettings);

        content.getDisplayedAttrs().addAll(attrs);

        ToolPanel eventActionsPanel = new ToolPanel(content);
        content.setToolPanel(eventActionsPanel);

        ToolBar eventActionsPropertiesToolBar = new ToolBar(eventActionsPanel);
        eventActionsPanel.getToolBars().add(eventActionsPropertiesToolBar);
        Tool eventActionSelectPrsTool = tfInitializer.initFactory(LocalizedToolFactory.getAdminAdvlistToolFactory())
                .create().setToolBar(eventActionsPropertiesToolBar);

        //SAVE PRESENTATION
        Tool saveEventActionsPresentationTool = tfInitializer
                .initFactory(new SimpleActionToolFactory(
                        ru.naumen.metainfoadmin.shared.Constants.EventActionCommandCode.SAVE_ADVLIST_PRS,
                        Titles.SAVE_PRESENTATION, Tool.PresentationType.DEFAULT, messages.savePresentation()))
                .create().setToolBar(eventActionsPropertiesToolBar);
        eventActionsPropertiesToolBar.addTool(eventActionSelectPrsTool).addTool(saveEventActionsPresentationTool);

        ToolBar filterAndSortToolBar = new ToolBar(eventActionsPanel);
        eventActionsPanel.getToolBars().add(filterAndSortToolBar);
        //FILTER
        Tool filtrationTool = tfInitializer
                .initFactory(new SimpleActionToolFactory(
                        Constants.SHOW_ADVLIST_FILTER, Titles.FILTRATION, Tool.PresentationType.DEFAULT,
                        messages.filtration()))
                .create()
                .setToolBar(filterAndSortToolBar);
        //SORT
        Tool sortTool = tfInitializer
                .initFactory(new SimpleActionToolFactory(
                        Constants.SHOW_ADVLIST_SORT, Titles.SORT, Tool.PresentationType.DEFAULT, messages.sort()))
                .create().setToolBar(filterAndSortToolBar);
        filterAndSortToolBar.addTool(filtrationTool).addTool(sortTool);

        if (settings.isWithAdd())
        {
            ToolBar bar2 = new ToolBar(eventActionsPanel);
            Tool addEventActionTool = tfInitializer
                    .initFactory(new SimpleActionToolFactory(
                            addEventActionCommandCode, addEventActionCommandCode, Tool.PresentationType.DEFAULT,
                            emessages.add()))
                    .create().setToolBar(bar2);
            bar2.addTool(addEventActionTool);
            eventActionsPanel.getToolBars().add(bar2);
        }

        return content;
    }

    private IObjectFilter createEventFilter(EventType eventType)
    {
        return new SimpleFilter<DtObject>(EVENT.toString(), new SimpleDtObject(eventType.name(), null));
    }
}