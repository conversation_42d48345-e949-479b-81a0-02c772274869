package ru.naumen.metainfoadmin.client.dynadmin.parenttree;

import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithFolders;
import ru.naumen.core.client.tree.dto.view.DtoTreeFilteredViewModelFactoryImpl;
import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.dynadmin.parenttree.ParentTreeGinModule.ContentParentTree;

/**
 * Фабрика моделей дерева элементов для контентов
 *
 * <AUTHOR>
 * @since 10.08.2021
 */
public class ContentParentViewModelFactoryImpl extends DtoTreeFilteredViewModelFactoryImpl
        <ContentParentTree, WithFolders, FilteredSingleSelectionModel<DtObject>, ContentParentTreeFactoryContext>
{
}