/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.separator;

import jakarta.annotation.Nullable;

import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelGinModule;

/**
 * <AUTHOR>
 * @since 26 мая 2015 г.
 *
 */
public class ToolSeparator extends ActionTool
{
    private static final long serialVersionUID = 2587307436183297722L;

    public ToolSeparator()
    {
        setAction(EditableToolPanelGinModule.SEPARATOR);
        setUuid(EditableToolPanelGinModule.SEPARATOR);
    }

    public ToolSeparator(@Nullable ToolBar toolBar)
    {
        super(toolBar);
        setAction(EditableToolPanelGinModule.SEPARATOR);
        setUuid(EditableToolPanelGinModule.SEPARATOR);
    }

    @Override
    public ToolSeparator newInstance()
    {
        return new ToolSeparator();
    }
}