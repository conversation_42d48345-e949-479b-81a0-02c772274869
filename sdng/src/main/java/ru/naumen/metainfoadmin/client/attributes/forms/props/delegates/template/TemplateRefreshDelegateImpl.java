package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.template;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPOSITE;

import ru.naumen.core.client.widgets.properties.FootedTextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 *
 * <AUTHOR>
 *
 * @param <F>
 */
public class TemplateRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, String, FootedTextBoxProperty>
{

    @Override
    public void refreshProperty(PropertyContainerContext context, FootedTextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Boolean definableByTemplate = context.getPropertyValues().getProperty(COMPOSITE);
        callback.onSuccess(definableByTemplate);
    }
}
