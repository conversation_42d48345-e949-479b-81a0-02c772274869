package ru.naumen.metainfoadmin.client.attributes.columns;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.inject.Provider;
import com.google.inject.name.Named;

import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.grouplist.ButtonColumnFactory;
import ru.naumen.core.client.widgets.grouplist.WidgetCreator;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.HasEditable;
import ru.naumen.metainfo.shared.elements.HasEditableInLists;
import ru.naumen.metainfo.shared.elements.HasRequired;
import ru.naumen.metainfo.shared.elements.HasRequiredInInterface;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttrBooleanColumnFactory;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttrTitleCodeTypeColumnFactory;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode;

/**
 * Фабрика свойств колонок для списка атрибутов на карточке метакласса.
 *
 * <AUTHOR>
 * @since 23 авг. 2018 г.
 *
 */
@Singleton
public class ExtAttributeListColumnFactoryImpl extends AttributeListColumnFactoryImpl
{
    @Inject
    @Named(AttributeColumnCode.TITLE_CODE_TYPE)
    AttrTitleCodeTypeColumnFactory attrTitleCodeTypeColumnFactory;
    @Inject
    AttrBooleanColumnFactory booleanColumnFactory;
    @Inject
    ButtonColumnFactory<Attribute> buttonColumnFactory;
    @Inject
    @Named(AttributeListColumnGinModule.DEFAULT_VALUE_EXT)
    Provider<WidgetCreator<Attribute>> defaultColumnProvider;
    @Inject
    @Named(AttributeColumnCode.LINKED_TO)
    Provider<WidgetCreator<Attribute>> attrLinkedToColumnProvider;
    @Inject
    @Named(AttributeColumnCode.DETERMINED_BY)
    Provider<WidgetCreator<Attribute>> attrDeterminedByColumnProvider;
    @Inject
    @Named(AttributeColumnCode.FILTERING_ON_EDIT)
    Provider<WidgetCreator<Attribute>> attrFilteringOnEditColumnProvider;
    @Inject
    @Named(AttributeColumnCode.CALCULATING_ON_EDIT)
    Provider<WidgetCreator<Attribute>> attrCalculatingOnEditColumnProvider;
    @Inject
    AttributesMessages messages;

    @Inject
    public ExtAttributeListColumnFactoryImpl()
    {
        super();
    }

    @Override
    public WidgetCreator<Attribute> get(String code, RegistrationContainer registrationContainer)
    {
        switch (code) //NOPMD
        {
            case AttributeColumnCode.TITLE_CODE_TYPE:
                return attrTitleCodeTypeColumnFactory.create(code, registrationContainer);
            case AttributeColumnCode.EDITABLE:
                return booleanColumnFactory.create(code, HasEditable.EDITABLE_PREDICATE, messages.editable());
            case AttributeColumnCode.EDITABLE_IN_LISTS:
                return booleanColumnFactory.create(code, HasEditableInLists.EDITABLE_IN_LISTS_PREDICATE, messages
                        .editableInLists());
            case AttributeColumnCode.REQUIRED:
                return booleanColumnFactory.create(code, HasRequired.REQUIRED_PREDICATE, messages.required());
            case AttributeColumnCode.REQUIRED_IN_INTERFACE:
                return booleanColumnFactory.create(code, HasRequiredInInterface.REQUIRED_IN_INTERFACE_PREDICATE,
                        messages
                                .requiredInInterface());
            case AttributeColumnCode.LINKED_TO:
                return attrLinkedToColumnProvider.get();
            case AttributeColumnCode.DETERMINED_BY:
                return attrDeterminedByColumnProvider.get();
            case AttributeColumnCode.FILTERING_ON_EDIT:
                return attrFilteringOnEditColumnProvider.get();
            case AttributeColumnCode.CALCULATING_ON_EDIT:
                return attrCalculatingOnEditColumnProvider.get();
            case AttributeColumnCode.DEFAULT_VALUE:
                return defaultColumnProvider.get();
            case AttributeColumnCode.HIDE_EMPTY:
                return booleanColumnFactory.create(code, attr -> Boolean.TRUE.equals(attr.isHiddenWhenEmpty()), messages
                        .hideWhenEmpty());
            case AttributeColumnCode.HIDE_NO_VALUE_TO_SELECT:
                return booleanColumnFactory.create(code,
                        attr -> Boolean.TRUE.equals(attr.isHiddenWhenNoPossibleValues()),
                        messages.hideWhenNoPossibleValues());
        }
        throw new IllegalArgumentException("Attribute list column with code " + code + " is not registered!");
    }
}
