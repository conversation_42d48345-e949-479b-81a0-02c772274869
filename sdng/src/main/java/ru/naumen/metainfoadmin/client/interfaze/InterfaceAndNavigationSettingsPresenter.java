package ru.naumen.metainfoadmin.client.interfaze;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.INTERFACE_AND_NAVIGATION;

import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;

import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminMultiTabPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceTabSettingsPresenter;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationTabSettingsPresenter;

/**
 * {@link Presenter} окна настроек интерфейса.
 *
 * <AUTHOR>
 * @since 11.02.2013
 */
public class InterfaceAndNavigationSettingsPresenter extends AdminMultiTabPresenterBase<InterfaceSettingsPlace>
{
    private final NavigationTabSettingsPresenter navigationTab;
    private final InterfaceTabSettingsPresenter interfaceTab;
    private final InterfaceSettingsPresenterSettings config;

    @Inject
    public InterfaceAndNavigationSettingsPresenter(AdminTabDisplay display,
            EventBus eventBus,
            NavigationTabSettingsPresenter navigationTab,
            InterfaceTabSettingsPresenter interfaceTab,
            InterfaceSettingsPresenterSettings config)
    {
        super(display, eventBus);
        this.navigationTab = navigationTab;
        this.interfaceTab = interfaceTab;
        this.config = config;
    }

    @Override
    protected void initTabs(AsyncCallback<Void> callback)
    {
        addTab(messages.interfaze(), interfaceTab, "settings");

        if (config.isWithNavigation())
        {
            addTab(messages.navigation(), navigationTab, "navigation");
        }

        callback.onSuccess(null);
    }

    @Override
    protected String getTitle()
    {
        return config.isWithNavigation() ? messages.interfaceAndNavigation() : messages.interfaze();
    }

    @Override
    protected InterfaceSettingsPlace getTabbedPlace(SelectionEvent<Integer> event, String tab)
    {
        return new InterfaceSettingsPlace(tab);
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return INTERFACE_AND_NAVIGATION;
    }
}