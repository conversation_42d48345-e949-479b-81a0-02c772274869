package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import static com.google.common.collect.Iterables.filter;

import java.util.List;

import jakarta.inject.Inject;

import com.google.common.base.Predicate;
import com.google.common.collect.Ordering;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Provider;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * Фабрика представление атрибута "Объекты" Действия по событию.
 *
 * <AUTHOR>
 * @since Feb 19, 2015
 */
public class AttributeEventActionLinkedClassesWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    public class NotSystemClassPredicate implements Predicate<MetaClassLite>
    {
        @Override
        public boolean apply(MetaClassLite input)
        {
            return input.getFqn().isClass() && !input.isAbstract() && !input.isHidden() && input.isHasCases()
                   && !input.isFolder();
        }
    }

    @Inject
    private MetainfoServiceAsync metainfoService;
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(final PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        final SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();
        metainfoService.getMetaClasses(new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> value)
            {
                addItems(widget, value);
                callback.onSuccess(widget);
            }
        });
    }

    protected void addItems(SingleSelectCellList<DtObject> widget, List<MetaClassLite> value)
    {
        for (MetaClassLite metaClass : Ordering.from(CommonUtils.METACLASSLITE_COMPARATOR)
                .sortedCopy(filter(value, new NotSystemClassPredicate())))
        {
            widget.addItem(new SimpleDtObject(metaClass.getFqn().asString(), metaClass.getTitle(), metaClass.getFqn()));
        }
    }
}
