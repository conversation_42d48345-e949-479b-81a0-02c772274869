package ru.naumen.metainfoadmin.client.templates.list.form;

import java.util.Collection;
import java.util.Objects;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.MetainfoKeyCodeValidator;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.templates.list.dispatch.AddListTemplateAction;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfoadmin.client.templates.list.card.ListTemplatePlace;

/**
 * Форма добавления шаблона списка
 * <AUTHOR>
 * @since 06.04.2018
 *
 */
public class AddListTemplateFormPresenter extends ListTemplateFormPresenterImpl
{
    @Inject
    private PlaceController placeController;
    @Inject
    private MetainfoKeyCodeValidator schedulerCodeValidator;

    private ObjectListBase list;
    private String formCode;
    private ClassFqn owner;

    @Inject
    public AddListTemplateFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(ClassFqn owner, ObjectListBase list, String formCode)
    {
        this.owner = owner;
        this.list = list;
        this.formCode = formCode;
        listTemplate = new SimpleDtObject();
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }

        service.execute(
                new AddListTemplateAction(title.getValue(), code.getValue(),
                        Objects.requireNonNull(SelectListPropertyValueExtractor.getValue(classList)),
                        Lists.newArrayList(SelectListPropertyValueExtractor.getCollectionValue(caseList)), list, owner,
                        formCode, SelectListPropertyValueExtractor.getValue(settingsSet)),
                new BasicCallback<SimpleResult<DtObject>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtObject> value)
                    {
                        unbind();
                        placeController.goTo(new ListTemplatePlace(code.getValue()));
                    }
                });
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.addingTemplate());
        getDisplay().setFixed(false);
        getDisplay().display();
    }

    @Override
    protected String getInitialSettingsSetValue()
    {
        return null;
    }

    @Override
    Collection<String> getCaseValue()
    {
        if (list != null)
        {
            return CollectionUtils.transform(list.getCase(), ClassFqn.TO_STRING_CONVERTER,
                    Lists.newArrayListWithCapacity(list.getCase().size()));
        }
        return null;
    }

    @Override
    String getClassValue()
    {
        if (list != null)
        {
            return list.getFqnOfClass().getId();
        }
        return null;
    }

    @Override
    protected void addCodeValidator()
    {
        validation.validate(code, notEmptyValidator);
        validation.validate(code, schedulerCodeValidator);
    }
}
