/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.toolbar.command;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.display.VMapRowCommandParam;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.toolbar.commands.VMapAddRowCommand;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.toolbar.commands.VMapDeleteRowCommand;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.toolbar.commands.VMapEditRowCommand;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.EscalationVMapItemRowCommandCode;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.EscalationValueMapItemContext;

/**
 * Инициализация команд для строки таблицы соотв. в схеме эскалаций
 * <AUTHOR>
 * @since 01.11.2012
 *
 */
public class EscalationVMapItemCommandFactoryInitializer
{
    @Inject
    public EscalationVMapItemCommandFactoryInitializer(
            CommandFactory factory,
            CommandProvider<VMapAddRowCommand<EscalationValueMapItemContext>,
                    CommandParam<EscalationValueMapItemContext, Void>> addCommandProvider,
            CommandProvider<VMapEditRowCommand<EscalationValueMapItemContext>,
                    VMapRowCommandParam<EscalationValueMapItemContext>> editRowCommandProvider,
            CommandProvider<VMapDeleteRowCommand<EscalationValueMapItemContext>,
                    VMapRowCommandParam<EscalationValueMapItemContext>> delRowCommandProvider)
    {
        factory.register(EscalationVMapItemRowCommandCode.ADD, addCommandProvider);
        factory.register(EscalationVMapItemRowCommandCode.EDIT, editRowCommandProvider);
        factory.register(EscalationVMapItemRowCommandCode.DELETE, delRowCommandProvider);
    }
}