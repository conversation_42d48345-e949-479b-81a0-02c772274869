package ru.naumen.metainfoadmin.client;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.*;
import static ru.naumen.core.shared.utils.CommonUtils.METAINFO;
import static ru.naumen.core.shared.utils.CommonUtils.assertNotNull;
import static ru.naumen.metainfo.shared.ui.Constants.HIGHLIGHT_KEY;
import static ru.naumen.metainfo.shared.ui.Constants.TAB_PARAM_KEY;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;

import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.inject.client.AsyncProvider;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.activity.PlaceParametersChangeEvent;
import ru.naumen.core.client.activity.PlaceParametersChangeHandler;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.ResourceCallback;
import ru.naumen.core.client.events.PageReadyEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractStateResponsibleEvent;
import ru.naumen.core.shared.Constants.AbstractSystemObject;
import ru.naumen.core.shared.Constants.AdminLogRecord;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.Event;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.Constants.GeoHistoryRecord;
import ru.naumen.core.shared.Constants.LinkToVersion;
import ru.naumen.core.shared.Constants.Mail;
import ru.naumen.core.shared.Constants.MailLogRecord;
import ru.naumen.core.shared.Constants.Push;
import ru.naumen.core.shared.Constants.PushMobile;
import ru.naumen.core.shared.Constants.PushPortal;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.core.shared.utils.CIUtils;
import ru.naumen.metainfo.client.CatalogPlace;
import ru.naumen.metainfo.client.MetaClassPermissionsUpdatedEvent;
import ru.naumen.metainfo.client.MetaClassPermissionsUpdatedHandler;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.client.events.DeleteMetaClassEvent;
import ru.naumen.metainfo.client.events.DeleteMetaClassEventHandler;
import ru.naumen.metainfo.client.events.MetainfoUpdatedEvent;
import ru.naumen.metainfo.client.events.MetainfoUpdatedHandler;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.MetaClassProperties;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.QuotaSnapshot;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassResponse;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.DisabledAttributesChangedEvent;
import ru.naumen.metainfoadmin.client.attributes.DisabledAttributesChangedHandler;
import ru.naumen.metainfoadmin.client.attributes.GeneralInfoClassPresenter;
import ru.naumen.metainfoadmin.client.attributes.MetaClassQuotaUpdatedEvent;
import ru.naumen.metainfoadmin.client.attributes.MetaClassQuotaUpdatedHandler;
import ru.naumen.metainfoadmin.client.attributes.MetaClassTagsChangedEvent;
import ru.naumen.metainfoadmin.client.attributes.MetaClassTagsChangedHandler;
import ru.naumen.metainfoadmin.client.customforms.CustomFormsTabPresenter;
import ru.naumen.metainfoadmin.client.fts.FtsPresenter;
import ru.naumen.metainfoadmin.client.group.AttributeGroupsPresenter;
import ru.naumen.metainfoadmin.client.sec.DomainAccessPresenter;
import ru.naumen.metainfoadmin.client.wf.WfPresenter;
import ru.naumen.metainfoadmin.client.wf.responsibility.ResponsibilityTransferPresenter;

/**
 * {@link Presenter} настройки метаинформации конкретного {@link MetaClass
 * метакласса}
 *
 * <AUTHOR>
 */
public class ClassPresenter extends AdminMultiTabPresenterBase<MetaClassPlace>
        implements MetainfoUpdatedHandler, DeleteMetaClassEventHandler,
        PlaceParametersChangeHandler, DisabledAttributesChangedHandler, MetaClassTagsChangedHandler,
        MetaClassQuotaUpdatedHandler, MetaClassPermissionsUpdatedHandler
{
    /**
     * Имя параметра контекста, в котором хранится список действий, инициировавших обновление контекста.
     * Данный список не является обязательным и включает в себя лишь те действия, необходимость
     * наличия которых в контексте чем-то обусловлена.
     */
    public static final String PRM_SOURCE_ACTIONS = "SOURCE_ACTIONS";

    /**
     * Код для вкладки "Атрибуты"
     */
    private static final String ATTR_TAB_CODE = "Attributes";

    /**
     * Код для вкладки "Группы атрибутов"
     */
    private static final String GROUPS_TAB_CODE = "Groups";

    /**
     * Код для вкладки "Права доступа"
     */
    private static final String PERMISSION_SETTINGS_TAB_CODE = "PermissionSettings";

    /**
     * Код для вкладки "Жизненный цикл"
     */
    private static final String WORKFLOW_TAB_CODE = "wf";

    /**
     * Код для вкладки "Поиск"
     */
    private static final String FTS_TAB_CODE = "fts";

    /**
     * Код для вкладки "Передача ответственности"
     */
    public static final String RESPONSIBILITY_TRANSFER_TAB_CODE = "ResponsibilityTransfer";

    private final ClassPresenterMessages classMessages;
    private final CommonMessages commonMessages;
    private final AsyncProvider<GeneralInfoClassPresenter> generalInfoClassPresenterProvider;
    private final AsyncProvider<AttributeGroupsPresenter> attributeGroupsPresenterProvider;
    private final AsyncProvider<UIPresenter> uiPresenterProvider;
    private final AsyncProvider<CustomFormsTabPresenter> customFormsProvider;
    private final AsyncProvider<TabContainerWithTabbedContent<DomainAccessPresenter>> domainAccessPresenterProvider;
    private final AsyncProvider<TabContainerWithTabbedContent<WfPresenter>> wfPresenterProvider;
    private final AsyncProvider<TabContainerWithTabbedContent<ResponsibilityTransferPresenter>> responsibilityTransferPresenterProvider;
    private final AsyncProvider<FtsPresenter> ftsPresenterProvider;
    private final DispatchAsync dispatch;

    private DefaultContext context;

    @Inject
    public ClassPresenter(AdminTabDisplay display,
            EventBus eventBus,
            ClassPresenterMessages classMessages,
            CommonMessages commonMessages,
            AsyncProvider<GeneralInfoClassPresenter> generalInfoClassPresenterProvider,
            AsyncProvider<AttributeGroupsPresenter> attributeGroupsPresenterProvider,
            AsyncProvider<UIPresenter> uiPresenterProvider,
            AsyncProvider<CustomFormsTabPresenter> customFormsProvider,
            AsyncProvider<TabContainerWithTabbedContent<DomainAccessPresenter>> domainAccessPresenterProvider,
            AsyncProvider<TabContainerWithTabbedContent<WfPresenter>> wfPresenterProvider,
            AsyncProvider<TabContainerWithTabbedContent<ResponsibilityTransferPresenter>> responsibilityTransferPresenterProvider,
            AsyncProvider<FtsPresenter> ftsPresenterProvider,
            DispatchAsync dispatch)
    {
        super(display, eventBus);
        this.classMessages = classMessages;
        this.commonMessages = commonMessages;
        this.generalInfoClassPresenterProvider = generalInfoClassPresenterProvider;
        this.attributeGroupsPresenterProvider = attributeGroupsPresenterProvider;
        this.uiPresenterProvider = uiPresenterProvider;
        this.customFormsProvider = customFormsProvider;
        this.domainAccessPresenterProvider = domainAccessPresenterProvider;
        this.wfPresenterProvider = wfPresenterProvider;
        this.responsibilityTransferPresenterProvider = responsibilityTransferPresenterProvider;
        this.ftsPresenterProvider = ftsPresenterProvider;
        this.dispatch = dispatch;
    }

    @Override
    public void onDisabledAttributesChanged(DisabledAttributesChangedEvent event)
    {
        if (event.isSuitableForContext(context))
        {
            context.setContextProperty(MetaClassProperties.DISABLED_ATTRIBUTES, event.getDisabledAttributes());
        }
    }

    @Override
    public void onMetaClassDeleted(DeleteMetaClassEvent e)
    {
        MetaClass metainfo = context.getMetainfo();
        assertNotNull(metainfo, METAINFO);
        if (metainfo.getFqn().equals(e.getClassFqn()))
        {
            placeController.goTo(new MetaClassPlace(e.getParentFqn()));
        }
    }

    @Override
    public void onMetaClassQuotaUpdated(MetaClassQuotaUpdatedEvent event)
    {
        if (event.isSuitableForContext(context))
        {
            context.setContextProperty(MetaClassProperties.QUOTING_PROPERTIES, event.getQuota());
        }
    }

    @Override
    public void onMetaClassTagsChanged(MetaClassTagsChangedEvent event)
    {
        if (event.isSuitableForContext(context))
        {
            context.setContextProperty(Tag.ELEMENT_TAGS, event.getTags());
        }
    }

    @Override
    public void onMetainfoUpdated(MetainfoUpdatedEvent e)
    {
        MetaClass metainfo = context.getMetainfo();
        assertNotNull(metainfo, METAINFO);
        if (e.getAction() != null)
        {
            List<Action<?>> actions = context.getContextProperty(PRM_SOURCE_ACTIONS) != null ? context
                    .getContextProperty(PRM_SOURCE_ACTIONS) : new ArrayList<Action<?>>();
            actions.add(e.getAction());
            context.setContextProperty(PRM_SOURCE_ACTIONS, actions);
        }
        if (metainfo.getFqn().equals(e.getMetainfo().getFqn()))
        {
            context.setMetainfo(e.getMetainfo());
        }

        MetaClass newMetainfo = e.getMetainfo();

        boolean isRefreshTabs = metainfo.isHasResponsible() && metainfo
                                                                       .isResponsibilityTransferTableEnabled()
                                                               != newMetainfo.isResponsibilityTransferTableEnabled();
        isRefreshTabs = isRefreshTabs || metainfo.isHasWorkflow() != newMetainfo.isHasWorkflow();
        isRefreshTabs = isRefreshTabs
                        || metainfo.hasAttribute(Constants.PARENT_ATTR) != newMetainfo.hasAttribute(
                Constants.PARENT_ATTR);

        if (metainfo.hasAttribute(Constants.Association.CLIENT)
            && newMetainfo.hasAttribute(Constants.Association.CLIENT))
        {
            isRefreshTabs = isRefreshTabs || !Objects.equals(metainfo.getAttribute(Association.CLIENT)
                    .isRequired(), newMetainfo.getAttribute(Association.CLIENT).isRequired());
        }

        if (isRefreshTabs)
        {
            refreshTabs();
        }
        if (metainfo.getFqn().equals(e.getMetainfo().getFqn()))
        {
            refreshDisplay();
        }
    }

    /**
     * Метод срабатывает, когда мы гуляем по вкладкам таббара
     */
    @Override
    public void onPlaceParametersChanged(PlaceParametersChangeEvent event)
    {
        init((MetaClassPlace)event.getPlace());
        selectTabFromPlace();
    }

    @Override
    public void refreshDisplay()
    {
        MetaClass metainfo = context.getMetainfo();
        assertNotNull(metainfo, METAINFO);
        String title = metainfo.getTitle();
        getDisplay().setTitle(title);
        eventBus.fireEvent(new PageReadyEvent(title));
        super.refreshDisplay();
    }

    @Override
    protected void initTabs(final AsyncCallback<Void> callback)
    {
        dispatch.execute(new GetMetaClassAction(getPlace().getFqn()),
                new ResourceCallback<GetMetaClassResponse>(commonMessages)
                {
                    @Override
                    public void onSuccess(GetMetaClassResponse response)
                    {
                        onMetaClassReady(callback, response.getMetaClass(), response.getDisabledAttributes(),
                                response.getTags(), response.getQuota(), response.getAdminPermissions());
                    }
                });
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        registerHandler(eventBus.addHandler(MetainfoUpdatedEvent.getType(), this));
        registerHandler(eventBus.addHandler(DeleteMetaClassEvent.getType(), this));
        registerHandler(eventBus.addHandler(PlaceParametersChangeEvent.getType(), this));
        registerHandler(eventBus.addHandler(DisabledAttributesChangedEvent.TYPE, this));
        registerHandler(eventBus.addHandler(MetaClassTagsChangedEvent.TYPE, this));
        registerHandler(eventBus.addHandler(MetaClassQuotaUpdatedEvent.TYPE, this));
        registerHandler(eventBus.addHandler(MetaClassPermissionsUpdatedEvent.TYPE, this));
    }

    protected void onMetaClassReady(final AsyncCallback<Void> callback, MetaClass metaClass,
            Set<String> disabledAttributes, List<DtObject> tags, @Nullable QuotaSnapshot quota,
            PermissionHolder permissions)
    {
        ClassFqn currentFqn = getPlace().getFqn();
        boolean isFileClass = File.FQN.isSameClass(currentFqn);
        boolean isEventClass = Event.FQN.isSameClass(currentFqn);
        boolean isCommentClass = Comment.FQN.isSameClass(currentFqn);
        boolean isMailClass = Mail.FQN.isSameClass(currentFqn);
        boolean isMailLogRecordClass = MailLogRecord.FQN.isSameClass(currentFqn);
        boolean isAdminLogRecordClass = AdminLogRecord.FQN.isSameClass(currentFqn);
        boolean isPushClass = Push.FQN.isSameClass(currentFqn);
        boolean isPushMobileClass = PushMobile.FQN.isSameClass(currentFqn);
        boolean isPushPortalClass = PushPortal.FQN.isSameClass(currentFqn);
        boolean isRootClass = Root.FQN.isSameClass(currentFqn);
        boolean isSuperUserClass = SuperUser.FQN.isSameClass(currentFqn);
        boolean isGeoHistoryClass = GeoHistoryRecord.FQN.isSameClass(currentFqn);
        boolean isLinkToVersion = LinkToVersion.FQN.isSameClass(currentFqn);

        //редирект на справочник в случае возникновения обращения к справочнику как к метаклассу
        ClassFqn fqn = metaClass.getFqn();
        if (metaClass.getParent() != null
            && CatalogItem.CLASS_ID.equals(metaClass.getParent().getId())
            || CIUtils.isIconsCatalogFqn(fqn))
        {
            placeController.goTo(new CatalogPlace(CIUtils.getCatalogCode(fqn)));
            return;
        }

        context = new DefaultContext(metaClass);
        eventBus.fireEvent(new DisabledAttributesChangedEvent(metaClass.getFqn(), disabledAttributes));
        eventBus.fireEvent(new MetaClassTagsChangedEvent(metaClass.getFqn(), tags));
        eventBus.fireEvent(new MetaClassQuotaUpdatedEvent(metaClass.getFqn(), quota));
        eventBus.fireEvent(new MetaClassPermissionsUpdatedEvent(metaClass.getFqn(), permissions));
        MetaClass metainfo = context.getMetainfo();
        assertNotNull(metainfo, METAINFO);
        ClassFqn parent = metainfo.getParent();

        getDisplay().setTitle(metaClass.getTitle());

        boolean isStateResponsibleEvent = AbstractStateResponsibleEvent.FQN.equals(metaClass.getFqn())
                                          || AbstractStateResponsibleEvent.FQN.equals(parent);

        /*
        Костыль: Проверка прав по маркеру доступа перед добавлением вкладки необходима для скрытия
        вкладки на странице метакласса.
        Это необходимо делать из-за того, что на момент добавления вкладки недоступен ее презентер,
        т. к. он обернут в провайдер.
         */
        if (!metainfo.isAbstract() || isStateResponsibleEvent)
        {
            addTab(classMessages.attributes(), generalInfoClassPresenterProvider, ATTR_TAB_CODE, false);
            if (adminPermissionCheckServiceSync.hasViewPermission(USER_INTERFACE)
                && !(isSuperUserClass || isGeoHistoryClass || isLinkToVersion))
            {
                addTab(classMessages.attributeGroups(), attributeGroupsPresenterProvider, GROUPS_TAB_CODE, false);
            }

            if (adminPermissionCheckServiceSync.hasViewPermission(USER_INTERFACE)
                && !AbstractSystemObject.FQN.equals(parent) && !AbstractStateResponsibleEvent.FQN.equals(parent))
            {
                addForm(UI.WINDOW_KEY, classMessages.dynadmin(), true);
                if (!metainfo.isSingleton())
                {
                    addForm(UI.Form.NEW, classMessages.newEntryForm(), false);
                }
                addForm(UI.Form.EDIT, classMessages.editForm(), false);
                if (!isRootClass)
                {
                    addTab(classMessages.otherForms(), customFormsProvider, UI.Form.CUSTOM, false);
                }
            }
        }

        if (adminPermissionCheckServiceSync.hasViewPermission(ACCESS_RIGHTS)
            && !(isFileClass || isEventClass || isCommentClass || isMailClass || isMailLogRecordClass
                 || isAdminLogRecordClass || isPushClass || isPushMobileClass || isStateResponsibleEvent
                 || isSuperUserClass || isGeoHistoryClass || isLinkToVersion || isPushPortalClass))
        {
            addTab(classMessages.settingPermissions(), domainAccessPresenterProvider, PERMISSION_SETTINGS_TAB_CODE,
                    true);
        }
        if (adminPermissionCheckServiceSync.hasViewPermission(WORKFLOW) && metainfo.isHasWorkflow())
        {
            addTab(classMessages.workflow(), wfPresenterProvider, WORKFLOW_TAB_CODE, true);
        }
        if (metainfo.isHasResponsible() && metainfo.isResponsibilityTransferTableEnabled())
        {
            addTab(classMessages.responsibilityTransfer(), responsibilityTransferPresenterProvider,
                    RESPONSIBILITY_TRANSFER_TAB_CODE, true);
        }
        if (adminPermissionCheckServiceSync.hasViewPermission(SEARCH_SETTINGS)
            && MetainfoUtils.hasSearch(metainfo)
            && !(isEventClass || isFileClass || isMailClass || isMailLogRecordClass || isAdminLogRecordClass
                 || isPushClass || isPushMobileClass || isStateResponsibleEvent || isSuperUserClass
                 || isGeoHistoryClass || isLinkToVersion || isPushPortalClass))
        {
            addTab(classMessages.fts(), ftsPresenterProvider, FTS_TAB_CODE, false);
        }

        getDisplay().setOneTabShowed(true);

        callback.onSuccess(null);
    }

    void refreshTabs()
    {
        onUnbind();
        onMetaClassReady(new BasicCallback<>(), context.getMetainfo(),
                context.getContextProperty(MetaClassProperties.DISABLED_ATTRIBUTES),
                context.getContextProperty(Tag.ELEMENT_TAGS),
                context.getContextProperty(MetaClassProperties.QUOTING_PROPERTIES),
                context.getPermissions() == null ? new PermissionHolder() : context.getPermissions());
    }

    private void addForm(final String formCode, final String tabName, boolean excludeInternalScrollOnTab)
    {
        addTab(tabName, uiPresenterProvider, createInitFormFunc(formCode), formCode, excludeInternalScrollOnTab);
    }

    private void addTab(String msg, AsyncProvider provider, String code, boolean excludeInternalScrollOnTab)
    {
        addTab(msg, provider, createInitTabFunc(), code, excludeInternalScrollOnTab);
    }

    private void addTab(String msg, AsyncProvider provider, Function<Presenter, Presenter> func,
            String code, boolean excludeInternalScrollOnTab)
    {
        String tabCode = getClassTabCode(code);
        addTab(msg, provider, func, tabCode);
        if (excludeInternalScrollOnTab)
        {
            excludeTabForInternalScrolling(tabCode);
        }
    }

    private static String getClassTabCode(String code)
    {
        return "Class." + code;
    }

    public static String getAttributesTabCode()
    {
        return getClassTabCode(ATTR_TAB_CODE);
    }

    private Function<Presenter, Presenter> createInitFormFunc(final String formCode)
    {
        return input ->
        {
            ((UIPresenter)input).init(context, formCode);
            return input;
        };
    }

    private Function<Presenter, Presenter> createInitTabFunc()
    {
        return input ->
        {
            ((HasContextPresenter)input).init(context);
            return input;
        };
    }

    @Override
    protected String getTitle()
    {
        return StringUtilities.EMPTY;
    }

    @Override
    protected MetaClassPlace getTabbedPlace(SelectionEvent<Integer> event, String tab)
    {
        MetaClassPlace clone = getPlace().cloneIt();
        clone.setTab(tabs.get(event.getSelectedItem()));
        clone.remove(TAB_PARAM_KEY);
        clone.remove(HIGHLIGHT_KEY);
        return clone;
    }

    @Override
    protected void handleNoTabs()
    {
        MetaClass metaClass = context.getMetainfo();

        /*
         Если метакласс абстрактный и у него нет отображаемых вкладок — отображаем только его заголовок.
         В остальных случаях — необходимо уничтожать презентер и выводить ошибку об отсутствии прав
         */
        if (metaClass != null && Boolean.FALSE.equals(metaClass.isAbstract()))
        {
            super.handleNoTabs();
        }
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return DATABASE_MANAGEMENT;
    }

    public void onMetaClassPermissionsUpdated(MetaClassPermissionsUpdatedEvent event)
    {
        if (event.isSuitableForContext(context))
        {
            context.setPermissions(event.getPermissions());
        }
    }
}
