package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.common.collect.Iterables;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfoadmin.client.AdminCachedMetainfoService;

/**
 * Для атрибутов "Атрибут связанного объекта", отображаются 
 * атрибут связи  и атрибут связанного класса. 
 * Формат: "Атрибут связи %цепочка атрибутов связи%. %атрибут связанного класса%" 
 *
 * <AUTHOR>
 * @since 26.09.18
 *
 */
public class AttrLinkedToAttributeOfRelatedObject implements AttrTypeColumnWidget
{
    private final AdminCachedMetainfoService metainfoService;

    @Inject
    public AttrLinkedToAttributeOfRelatedObject(AdminCachedMetainfoService metainfoService)
    {
        this.metainfoService = metainfoService;
    }

    @Override
    public IsWidget createWidget(Attribute attr)
    {
        final FlowPanel result = new FlowPanel();
        List<ClassFqn> requiredClasses = attr.getType()
                .getAttrChain()
                .stream()
                .map(AttributeFqn::getClassFqn)
                .collect(Collectors.toList());
        requiredClasses.add(attr.getType().getRelatedObjectMetaClass());
        metainfoService.getFullMetaInfo(
                requiredClasses,
                new BasicCallback<List<MetaClass>>()
                {
                    @Override
                    protected void handleSuccess(List<MetaClass> metaClasses)
                    {
                        StringBuilder chainAsString = new StringBuilder();
                        for (int i = 0; i < attr.getType().getAttrChain().size(); i++)
                        {
                            AttributeFqn ar = attr.getType().getAttrChain().get(i);
                            MetaClass mc = Iterables.find(metaClasses, MetaClassFilters.equal(ar.getClassFqn()));
                            Attribute attr = mc.getAttribute(ar.getCode());
                            chainAsString.append(attr.getTitle());
                            chainAsString.append('/');
                        }

                        MetaClass relObjMc = Iterables.find(metaClasses,
                                MetaClassFilters.equal(attr.getType().getRelatedObjectMetaClass()));
                        Attribute relAttr = relObjMc.getAttribute(attr.getType().getRelatedObjectAttribute());
                        chainAsString.append(relAttr.getTitle());
                        result.add(new InlineLabel(chainAsString.toString()));
                    }
                });

        return result;
    }

    @Override
    public List<String> listAllowedAttrTypes()
    {
        return null;
    }
}