package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.inject.Singleton;

import ru.naumen.common.shared.utils.attrusage.AttributeUsagePlace;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;

/**
 * Основное представление для отображения различных мест использования атрибутов на форме "Используется в настройках"
 * в таблице атрибутов
 * <AUTHOR>
 * @since 3 Jul 18
 */
@Singleton
public class AttributeUsagePlaceHtmlFactory extends AttributeHtmlFactoryImpl<AttributeUsagePlace>
{
    @Inject
    private AttributeDiffTypesHtmlFactories htmlFactories;

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsagePlace value)
    {
        return htmlFactories.getFactory(value.getClass()).create(context, value);
    }
}