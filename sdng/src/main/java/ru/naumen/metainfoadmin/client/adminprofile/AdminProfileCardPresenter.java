package ru.naumen.metainfoadmin.client.adminprofile;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.ADMINISTRATION_PROFILES;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.CoreGinjector.PrevPageLinkPresenterFactory;
import ru.naumen.core.client.PrevPageLinkPresenter;
import ru.naumen.core.client.activity.PrevLinkContainer;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.Dialogs.Buttons;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.client.events.AdminProfileUpdatedEvent;
import ru.naumen.metainfo.client.events.AdminProfileUpdatedHandler;
import ru.naumen.metainfoadmin.client.AdminSingleTabPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.adminprofile.data.AdminProfilesServiceAsync;
import ru.naumen.metainfoadmin.client.adminprofile.matrix.access.AccessMarkerMatrixPresenter;
import ru.naumen.metainfoadmin.client.adminprofile.matrix.settings.AdminSettingsMatrixPresenter;

/**
 * Презентер карточки профиля администрирования
 * <AUTHOR>
 * @since 24.01.2024
 */
public class AdminProfileCardPresenter extends AdminSingleTabPresenterBase<AdminProfilePlace> implements AdminProfileUpdatedHandler
{
    private final AdminProfilesServiceAsync adminProfileService;
    private final AdminProfileInfoPresenter infoPresenter;
    private final AccessMarkerMatrixPresenter accessMarkerMatrixPresenter;
    private final AdminSettingsMatrixPresenter adminSettingsMatrixPresenter;
    private final Dialogs dialogs;
    private final CommonMessages commonMessages;
    private final AdminProfilesMessages adminProfilesMessages;
    private final PrevLinkContainer prevLinkContainer;
    private final PrevPageLinkPresenterFactory prevPageLinkPresenterFactory;
    private final SharedSettingsClientService sharedSettingsClientService;

    private DtObject adminProfile;

    private final OnStartCallback<DtObject> refreshCallback = new SafeOnStartBasicCallback<>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            infoPresenter.setAdminProfile(value);
            accessMarkerMatrixPresenter.setAdminProfile(value);
            adminSettingsMatrixPresenter.setAdminProfile(value);
            refreshDisplay();
        }
    };

    @Inject
    public AdminProfileCardPresenter(AdminTabDisplay display,
            EventBus eventBus,
            AdminProfilesServiceAsync adminProfileService,
            AdminProfileInfoPresenter infoPresenter,
            AccessMarkerMatrixPresenter accessMarkerMatrixPresenter,
            AdminSettingsMatrixPresenter adminSettingsMatrixPresenter,
            Dialogs dialogs,
            CommonMessages commonMessages,
            AdminProfilesMessages adminProfilesMessages,
            PrevLinkContainer prevLinkContainer,
            PrevPageLinkPresenterFactory prevPageLinkPresenterFactory,
            SharedSettingsClientService sharedSettingsClientService)
    {
        super(display, eventBus);
        this.adminProfileService = adminProfileService;
        this.infoPresenter = infoPresenter;
        this.accessMarkerMatrixPresenter = accessMarkerMatrixPresenter;
        this.adminSettingsMatrixPresenter = adminSettingsMatrixPresenter;
        this.dialogs = dialogs;
        this.commonMessages = commonMessages;
        this.adminProfilesMessages = adminProfilesMessages;
        this.prevLinkContainer = prevLinkContainer;
        this.prevPageLinkPresenterFactory = prevPageLinkPresenterFactory;
        this.sharedSettingsClientService = sharedSettingsClientService;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        infoPresenter.refreshDisplay();
        accessMarkerMatrixPresenter.refreshDisplay();
        adminSettingsMatrixPresenter.refreshDisplay();
    }

    protected void afterAdminProfileLoaded(DtObject adminProfileDto)
    {
        Place previousPlace = prevLinkContainer.getPreviousPlace();
        PrevPageLinkPresenter prevPageLinkPresenter = prevPageLinkPresenterFactory.create(display.getPrevPageLink());
        prevPageLinkPresenter.ensureDebugId("prevPageLink");
        String backLinkCaption = previousPlace == null || previousPlace instanceof AdminProfilesPlace
                ? adminProfilesMessages.backToProfiles()
                : commonMessages.back();
        prevPageLinkPresenter.bind(backLinkCaption, AdminProfilesPlace.INSTANCE);

        getDisplay().setTitle(adminProfileDto.getTitle());

        this.adminProfile = adminProfileDto;
        infoPresenter.setAdminProfile(adminProfileDto);
        accessMarkerMatrixPresenter.setAdminProfile(adminProfileDto);
        adminSettingsMatrixPresenter.setAdminProfile(adminProfileDto);
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        if (adminProfile == null)
        {
            return;
        }

        addContent(infoPresenter, "info");
        addContent(accessMarkerMatrixPresenter, "accessMarkerMatrix");
        if (sharedSettingsClientService.isSettingsSetEnabled())
        {
            addContent(adminSettingsMatrixPresenter, "settingsMatrix");
        }

        super.onBindAfterCheckPermission();
        infoPresenter.init(refreshCallback);
        registerHandler(eventBus.addHandler(AdminProfileUpdatedEvent.getType(), this));
    }

    @Override
    protected String getTitle()
    {
        return StringUtilities.EMPTY;
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        adminProfileService.getAdminProfile(getPlace().getAdminProfileCode(), new BasicCallback<>(readyState)
        {
            @Override
            protected void handleSuccess(@Nullable DtObject value)
            {
                if (value == null)
                {
                    dialogs.error(commonMessages.resourceNotFoundUserMessage(), Buttons.OK);
                }
                else
                {
                    afterAdminProfileLoaded(value);
                }
            }
        });
    }

    @Override
    protected void onUnbind()
    {
        infoPresenter.unbind();
        accessMarkerMatrixPresenter.unbind();
        adminSettingsMatrixPresenter.unbind();
        super.onUnbind();
    }

    @Override
    public void onAdminProfileUpdated(AdminProfileUpdatedEvent event)
    {
        getDisplay().setTitle(event.getAdminProfile().getTitle());
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return ADMINISTRATION_PROFILES;
    }
}