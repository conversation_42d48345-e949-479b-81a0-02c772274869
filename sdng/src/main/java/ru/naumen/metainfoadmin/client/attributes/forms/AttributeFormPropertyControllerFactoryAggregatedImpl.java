package ru.naumen.metainfoadmin.client.attributes.forms;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.ATTRIBUTE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.AGGREGATE_ATTRIBUTES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.AGGREGATE_CLASSES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.AGGREGATE_VALUE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;

import java.util.function.Function;

import jakarta.inject.Inject;

import com.google.common.base.Preconditions;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Фабрика агрегирующих свойств на форме атрибута - которые инкапсулируют в себе несколько различных виджетов и 
 * показывают какой-то из них в зависимости от определенного условия
 * <AUTHOR>
 * @since 15.05.2012
 */
public class AttributeFormPropertyControllerFactoryAggregatedImpl<F extends ObjectForm>
        extends AbstractPropertyControllerFactoryAggregatedImpl<F>
{
    private final static Function<PropertyContainerContext, String> AGGREGATE_VALUE_SELECTOR =
            new Function<PropertyContainerContext, String>()
            {
                @edu.umd.cs.findbugs.annotations.SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
                @Override
                public String apply(PropertyContainerContext input)
                {
                    Preconditions.checkNotNull(input);
                    if (input.getPropertyValues().<Boolean> getProperty(COMPUTABLE))
                    {
                        return "";
                    }
                    if (input.getContextValues().getProperty(ATTRIBUTE) == null)
                    {
                        return AGGREGATE_CLASSES;
                    }
                    else
                    {
                        return AGGREGATE_ATTRIBUTES;
                    }
                }
            };

    @Inject
    public AttributeFormPropertyControllerFactoryAggregatedImpl(
            @Assisted PropertyControllerFactory<Attribute, F> aggregatedPropertyFactory)
    {
        super(aggregatedPropertyFactory);
    }

    @Override
    protected void build()
    {
        super.build();
        register(AGGREGATE_VALUE, AGGREGATE_VALUE_SELECTOR, AGGREGATE_CLASSES, AGGREGATE_ATTRIBUTES);
    }
}
