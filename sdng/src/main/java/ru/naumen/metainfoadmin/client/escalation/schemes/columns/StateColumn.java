package ru.naumen.metainfoadmin.client.escalation.schemes.columns;

import com.google.inject.Inject;

import ru.naumen.core.client.widgets.columns.FontIconCell;
import ru.naumen.core.client.widgets.columns.ToggleColumn;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;

/**
 * <AUTHOR>
 * @since 24.07.2012
 *
 */
public class StateColumn extends ToggleColumn<DtoContainer<EscalationScheme>>
{
    @Inject
    public StateColumn(FontIconCell<DtoContainer<EscalationScheme>> cell)
    {
        super(cell);
    }

    @Override
    protected boolean isEnabled(DtoContainer<EscalationScheme> value)
    {
        return EscalationScheme.StateCode.ON.equals(value.get().getState());
    }
}
