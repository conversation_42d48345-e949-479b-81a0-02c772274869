package ru.naumen.metainfoadmin.client.attributes.forms.info;

import java.util.List;
import java.util.stream.Collectors;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;

/**
 * Создает {@link Property} для отображения информации о едницах
 * измерения, доступных при редактировании на модальной форме свойств атрибута  
 *
 * <AUTHOR>
 * @since 1 авг. 2018 г.
 */
public class IntervalAvailableAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Override
    protected void createInt(String code)
    {
        List<String> intervals = propertyValues.getProperty(code);
        if (intervals == null || intervals.isEmpty())
        {
            return;
        }
        createInt(code, intervals);
    }

    private void createInt(String code, List<String> intervals)
    {
        String propertyValue = intervals.stream()
                .map(this::getIntervalTitle)
                .collect(Collectors.joining(", "));

        createProperty(code, propertyValue);
    }

    private String getIntervalTitle(String interval)
    {
        switch (Interval.valueOf(interval))
        {
            case DAY:
                return cmessages.days();
            case HOUR:
                return cmessages.hour();
            case MINUTE:
                return cmessages.minute();
            case SECOND:
                return cmessages.second();
            case WEEK:
                return cmessages.week();
            default:
                return "";
        }
    }
}
