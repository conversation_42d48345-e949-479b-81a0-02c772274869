package ru.naumen.metainfoadmin.client.dynadmin.content.selectclient;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Provider;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.SelectClient;
import ru.naumen.metainfoadmin.client.dynadmin.content.AbstractEditablePropsContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.PropertyGridFlowContentDisplay;

/**
 * Presenter для контента {@link SelectClient} в интерфейсе администратора
 *
 * <AUTHOR>
 */
public class SelectClientContentPresenter
        extends AbstractEditablePropsContentPresenter<SelectClient>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<String>> selectListBuilderProvider;

    private Property<String> contactsProperty;

    @Inject
    public SelectClientContentPresenter(PropertyGridFlowContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "SelectClient");
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();

        Attribute attribute = context.getMetainfo().getAttribute(Association.CLIENT);
        String style = attribute.isHiddenAttrCaption() ?
                WidgetResources.INSTANCE.form().hiddenAttrCaption() : StringUtilities.EMPTY;
        contactsProperty.getCaptionWidget().asWidget().setStyleName(style);
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        SingleSelectCellList<String> selectList = selectListBuilderProvider.get().setHasSearch(true)
                .setCellGlobalPaddingLeft(0).withEmptyOption()
                .build();
        contactsProperty = propertyCreator.create(messages.client(), selectList);

        Attribute clientAttribute = context.getMetainfo().getAttribute(Constants.Association.CLIENT);
        if (clientAttribute.isRequired() || clientAttribute.isRequiredInInterface())
        {
            contactsProperty.setValidationMarker(true);
        }
        getDisplay().add(contactsProperty);
    }

    @Override
    protected String getHelpText()
    {
        return messages.selectClient();
    }
}
