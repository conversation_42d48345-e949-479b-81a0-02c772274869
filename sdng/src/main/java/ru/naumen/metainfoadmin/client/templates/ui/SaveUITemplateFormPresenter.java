package ru.naumen.metainfoadmin.client.templates.ui;

import java.util.Objects;

import com.google.gwt.event.dom.client.BlurEvent; //NOPMD
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.validation.MetainfoKeyCodeValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.IRadioButtonGroup;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.metainfo.client.ui.template.UITemplateMetainfoServiceAsync;
import ru.naumen.metainfo.client.ui.template.UITemplateResult;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.templates.ui.UITemplate;
import ru.naumen.metainfo.shared.templates.ui.UITemplateDto;
import ru.naumen.metainfoadmin.client.dynadmin.ContentUtils;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UITemplateContext;

/**
 * Представление формы сохранения шаблона карточки.
 * <AUTHOR>
 * @since Jul 28, 2021
 */
public class SaveUITemplateFormPresenter extends OkCancelPresenter<PropertyDialogDisplay>
{
    public enum SaveMode
    {
        CREATE_NEW("createNew"),
        SAVE_CURRENT("saveCurrent");

        private final String code;

        SaveMode(String code)
        {
            this.code = code;
        }

        public String getCode()
        {
            return code;
        }
    }

    @Inject
    @Named(PropertiesGinModule.RADIO_GROUP)
    private Property<String> saveMode;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> readOnlyCode;
    private PropertyRegistration<String> codePR;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> makeDefault;

    @Inject
    private Processor validation;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    private MetainfoKeyCodeValidator metainfoKeyCodeValidator;
    @Inject
    private CommonMessages commonMessages;
    @Inject
    private UITemplateMessages messages;
    @Inject
    private I18nUtil i18nUtil;
    @Inject
    private UITemplateMetainfoServiceAsync templateMetainfoService;
    @Inject
    private TransliterationService transliterationService;
    @Inject
    private SecurityHelper securityHelper;

    private UIContext context;
    private AsyncCallback<UITemplateResult> saveCallback;

    private ValidationUnit<?> codeValidationUnit;

    @Inject
    public SaveUITemplateFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(UIContext context, AsyncCallback<UITemplateResult> saveCallback)
    {
        this.context = context;
        this.saveCallback = saveCallback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        UITemplateContext templateContext = Objects.requireNonNull(context.getUITemplateContext());
        UITemplate template = templateContext.getTemplate().clone();
        i18nUtil.updateI18nObjectTitle(template, title.getValue());
        boolean asNew = SaveMode.CREATE_NEW.getCode().equals(saveMode.getValue());
        if (asNew)
        {
            template.setCode(code.getValue());
        }
        templateMetainfoService.saveTemplate(template, context.getMetainfo().getFqn(), asNew,
                Boolean.TRUE.equals(makeDefault.getValue()),
                new BasicCallback<UITemplateResult>(context.getReadyState(), getDisplay())
                {
                    @Override
                    protected void handleSuccess(UITemplateResult value)
                    {
                        saveCallback.onSuccess(value);
                        unbind();
                    }
                }.setErrorMessageHandler(getDisplay()));
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        setCaption(messages.saveTemplateDialog());
        bindProperties();
        ensureDebugIds();
        setPropertyValues();
        bindHandlers();
        getDisplay().display();
    }

    private void bindHandlers()
    {
        registerHandler(saveMode.addValueChangeHandler(
                event -> replaceCodeProperty(SaveMode.SAVE_CURRENT.getCode().equals(event.getValue()))));
        registerHandler(title.addValueChangeHandler(event -> fillCode()));
        registerHandler(title.getValueWidget().asWidget().addDomHandler(event -> fillCode(), BlurEvent.getType()));
    }

    private void bindProperties()
    {
        saveMode.setCaption(StringUtilities.EMPTY);
        IRadioButtonGroup saveModeWidget = saveMode.getValueWidget();
        saveModeWidget.addItem(SaveMode.SAVE_CURRENT.getCode(), messages.editExistingTemplate());
        saveModeWidget.addItem(SaveMode.CREATE_NEW.getCode(), messages.createNewTemplate());
        getDisplay().add(saveMode);

        title.setCaption(commonMessages.title());
        title.setMaxLength(ContentUtils.MAX_TITLE_LENGTH);
        title.setValidationMarker(true);
        validation.validate(title, notEmptyValidator);
        getDisplay().add(title);

        code.setCaption(commonMessages.code());
        code.setMaxLength(ContentUtils.MAX_CODE_LENGTH);
        code.setValidationMarker(true);
        validation.validate(code, notEmptyValidator);
        readOnlyCode.setCaption(commonMessages.code());
        readOnlyCode.setDisable();

        UITemplateContext templateContext = Objects.requireNonNull(context.getUITemplateContext());
        codePR = getDisplay().add(templateContext.isNewTemplate() ? code : readOnlyCode);
        if (templateContext.isNewTemplate())
        {
            codeValidationUnit = validation.validate(code, metainfoKeyCodeValidator);
        }

        makeDefault.setCaption(messages.setAsDefault());
        getDisplay().add(makeDefault);
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(saveMode, "saveMode");
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(makeDefault, "makeDefault");
    }

    private void fillCode()
    {
        if (SaveMode.CREATE_NEW.getCode().equals(saveMode.getValue())
            && !StringUtilities.isEmpty(title.getValue())
            && StringUtilities.isEmpty(code.getValue()))
        {
            String specialChars = securityHelper.hasVendorProfile()
                    ? Constants.CODE_SPECIAL_CHARS_FOR_VENDOR
                    : Constants.CODE_SPECIAL_CHARS;
            code.setValue(transliterationService.transliterateToCode(title.getValue(), ContentUtils.MAX_CODE_LENGTH,
                    specialChars));
        }
    }

    private void replaceCodeProperty(boolean readOnly)
    {
        if (null == codePR)
        {
            return;
        }
        if (readOnly && code == codePR.getProperty() || !readOnly && readOnlyCode == codePR.getProperty())
        {
            if (null != codeValidationUnit)
            {
                codeValidationUnit.unregister();
                codeValidationUnit = null;
            }
            Property<String> property = readOnly ? readOnlyCode : code;
            PropertyRegistration<String> newCodePR = getDisplay().addPropertyAfter(property, codePR);
            codePR.unregister();
            codePR = newCodePR;
            if (!readOnly)
            {
                codeValidationUnit = validation.validate(property, metainfoKeyCodeValidator);
            }
            eventBus.fireEvent(new UpdateTabOrderEvent(false));
        }
    }

    private void setPropertyValues()
    {
        UITemplateContext templateContext = Objects.requireNonNull(context.getUITemplateContext());
        if (!templateContext.isNewTemplate())
        {
            saveMode.setValue(SaveMode.SAVE_CURRENT.getCode());
            title.setValue(i18nUtil.getLocalizedTitle(templateContext.getTemplate()));
            code.setValue(templateContext.getTemplate().getCode());
            readOnlyCode.setValue(templateContext.getTemplate().getCode());
            UITemplateDto templateDto = templateContext.getCurrentTemplateDto();
            makeDefault.setValue(null != templateDto && templateDto.isDefault());
        }
        else
        {
            saveMode.setValue(SaveMode.CREATE_NEW.getCode());
            saveMode.setEnabled(false);
        }
    }
}
