package ru.naumen.metainfoadmin.client.escalation.schemes.columns;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.user.cellview.client.Column;
import com.google.inject.Inject;

import ru.naumen.core.client.widgets.columns.ClickableSafeHtmlTextCell;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;

/**
 * <AUTHOR>
 * @since 23.07.2012
 *
 */
public class TargetObjectsColumn extends Column<DtoContainer<EscalationScheme>, SafeHtml>
{
    @Inject
    Formatters formatters;

    @Inject
    public TargetObjectsColumn(ClickableSafeHtmlTextCell cell)
    {
        super(cell);
        cell.ensureDebugId("targetObject");
    }

    @Override
    public SafeHtml getValue(DtoContainer<EscalationScheme> object)
    {
        return formatters.linkToEscalationObjects(object.get());
    }
}
