package ru.naumen.metainfoadmin.client.structuredobjectsviews.commands;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.FxExceptions;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.structuredobjectsviews.dispatch.DeleteStructuredObjectsViewAction;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsMessages;

/**
 * Команда удаления структуры
 * <AUTHOR>
 * @since 21.10.2019
 */
public class DelStructuredObjectsViewsCommand extends ObjectCommandImpl<Collection<DtObject>, Void>
{
    @Inject
    private CommonMessages cmessages;
    @Inject
    private StructuredObjectsViewsMessages structuredObjectsViewsMessages;
    @Inject
    private DispatchAsync dispatch;

    @Inject
    public DelStructuredObjectsViewsCommand(@Assisted CommandParam<Collection<DtObject>, Void> param, Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(Collection<DtObject> values)
    {
        if (values.size() == 1)
        {
            return structuredObjectsViewsMessages.structuredObjectsViewConfirmDelete(values.iterator().next()
                    .getTitle());
        }
        return structuredObjectsViewsMessages.structuredObjectsViewConfirmMassDelete();
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

    @Override
    protected void onDialogSuccess(CommandParam<Collection<DtObject>, Void> param)
    {
        List<String> codes = param.getValue().stream().map(IUUIDIdentifiable::getUUID).collect(Collectors.toList());
        dispatch.execute(new DeleteStructuredObjectsViewAction(codes),
                new BasicCallback<SimpleResult<HashMap<String, String>>>()
                {
                    @Override
                    protected void handleFailure(String msg, @Nullable String details)
                    {
                        param.getCallback().onFailure(new FxExceptions(msg));
                    }

                    @Override
                    protected void handleSuccess(SimpleResult<HashMap<String, String>> result)
                    {
                        if (!result.get().isEmpty())
                        {
                            String message = result.get().entrySet().stream().map(
                                            entry -> structuredObjectsViewsMessages.structuredObjectsViewNotDelete(
                                                    entry.getKey()) + "<br>" + entry.getValue())
                                    .collect(Collectors.joining("<br>"));
                            dialogs.info(message);
                        }
                        if (result.get().size() != codes.size())
                        {
                            param.getCallback().onSuccess(null);
                        }
                    }
                });
    }
}