package ru.naumen.metainfoadmin.client.attributes.columns;

import static ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode.*;
import static ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.*;

import java.util.Map;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.HashMap;

import com.google.inject.name.Named;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.AttributeListCss;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.grouplist.CellValueExtractor;
import ru.naumen.core.client.widgets.grouplist.GroupList.ColumnInfo;
import ru.naumen.core.client.widgets.grouplist.TableRowComparator;
import ru.naumen.core.client.widgets.grouplist.WidgetCreator;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.AttributesPresenter;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;

/**
 * Фабрика колонок для списка атрибутов на карточке метакласса.
 *
 * <AUTHOR>
 * @since 23 авг. 2018 г.
 *
 */
@Singleton
public class ExtAttributeListColumnInfoFactoryImpl implements AttributeListColumnInfoFactory
{
    private final AttributeListColumnFactory columnFactory;

    private Map<String, ColumnInfo<Attribute>> columnInfos;

    private AttributesMessages messages;

    private CommonMessages cmessages;

    private boolean isCompact = false;

    @Inject
    private CommandFactory commandFactory;

    @Inject
    public ExtAttributeListColumnInfoFactoryImpl(AttributesMessages messages, CommonMessages cmessages,
            @Named(AttributesListType.EXTENDED) AttributeListColumnFactory columnFactory)
    {
        this.columnFactory = columnFactory;
        this.messages = messages;
        this.cmessages = cmessages;
    }

    @Override
    public ColumnInfo<Attribute> create(String code, RegistrationContainer registrationContainer)
    {
        WidgetCreator<Attribute> column = columnFactory.get(code, registrationContainer);
        if (null == column)
        {
            return null;
        }
        return buildColumnInfo(code, column);
    }

    @Override
    public ColumnInfo<Attribute> createCommandColumn(String code, AttributeCommandParam param, String cmd)
    {
        return createCommandColumn(code, commandFactory.create(cmd, param), param.getContext());
    }

    @Override
    public ColumnInfo<Attribute> createCommandColumn(String code, BaseCommand<?, ?> command, Context context)
    {
        ensureInited();
        WidgetCreator<Attribute> column = columnFactory.getButtonColumn(code, command, context);
        if (null == column)
        {
            return null;
        }
        return buildColumnInfo(code, column);
    }

    @Override
    public void reInit(boolean isCompact)
    {
        this.isCompact = isCompact;
        init();
    }

    private void addColumnInfo(String columnCode, ColumnInfo<Attribute> columnInfo)
    {
        if (!(isCompact && AttributesPresenter.DOES_NOT_SHOW_IN_COMPACT_MODE.contains(columnCode)))
        {
            columnInfos.put(columnCode, columnInfo);
        }
    }

    private ColumnInfo<Attribute> buildColumnInfo(String code, WidgetCreator<Attribute> column)
    {
        ensureInited();
        ColumnInfo<Attribute> source = columnInfos.get(code);
        if (null == source)
        {
            return null;
        }
        ColumnInfo<Attribute> result = new ColumnInfo<>(source);
        result.setColumn(column);
        return result;
    }

    private ColumnSorter<String> createStringColumnSorter(String id, CellValueExtractor<String> extractor,
            String control)
    {
        return new ColumnSorter<>(id, new TableRowComparator<>(extractor, columnInfos.size()), control);
    }

    private ColumnSorter<String> createStringInnerTextColumnSorter(String id)
    {
        return createStringColumnSorter(id, new InnerTextCellValueExtractor(), null);
    }

    private void ensureInited()
    {
        if (!isInited())
        {
            init();
        }
    }

    private void init()
    {
        columnInfos = new HashMap<>();
        AttributeListCss style = AdminWidgetResources.INSTANCE.attributeList();

        ColumnInfo<Attribute> attrValueConsColumn = new ColumnInfo<>(messages.attributeValue(),
                style.attributeValue(), "auto;", EDITABLE, null);
        ColumnInfo<Attribute> attrHideConsColumn = new ColumnInfo<>(messages.hides(),
                style.hideAttr(), "auto;", EDITABLE, null);

        addColumnInfo(TITLE_CODE_TYPE, new ColumnInfo<Attribute>(cmessages.attribute(),
                AdminWidgetResources.INSTANCE.tables().tableElemsTDLeft() + " " +
                style.colTitleCodeType(), "", TITLE_CODE_TYPE, null,
                createStringColumnSorter(COL_SORTER_TITLE,
                        new AttrTitleCellValueExtractor(), messages.sortByTitle()),
                createStringColumnSorter(COL_SORTER_CODE,
                        new AttrCodeCellValueExtractor(), messages.sortByCode()),
                createStringColumnSorter(COL_SORTER_TYPE,
                        new AttrValueTypeCellValueExtractor(), messages.sortByValueType()),
                new ColumnSorter<>(COL_SORTER_SYSTEM_FIRST,
                        new TableRowComparator<>(new AttrTypeCellValueExtractor(true), columnInfos.size(),
                                true, false), messages.sortSystemFirst()),
                new ColumnSorter<>(COL_SORTER_USER_FIRST,
                        new TableRowComparator<>(new AttrTypeCellValueExtractor(false), columnInfos.size(),
                                true, false), messages.sortUserFirst())));
        addColumnInfo(EDITABLE, new ColumnInfo<Attribute>("",
                style.editUnderline(), "", EDITABLE, null, messages.editable()));
        addColumnInfo(EDITABLE_IN_LISTS, new ColumnInfo<Attribute>("",
                style.editInLists(), "", EDITABLE_IN_LISTS, null, messages
                .editableInLists()));
        addColumnInfo(REQUIRED, new ColumnInfo<Attribute>("",
                style.require(), "", REQUIRED, null, messages.required()));
        addColumnInfo(REQUIRED_IN_INTERFACE, new ColumnInfo<Attribute>("",
                style.requireInInterface(), "", REQUIRED_IN_INTERFACE, null,
                messages.requiredInInterface()));
        addColumnInfo(LINKED_TO, new ColumnInfo<Attribute>(messages.linkedTo(),
                style.linkedTo(), "", LINKED_TO, null,
                createStringInnerTextColumnSorter(LINKED_TO)));
        addColumnInfo(DETERMINED_BY, new ColumnInfo<>(messages.determinedBy(),
                style.determinedBy(), "", DETERMINED_BY, null,
                null, attrValueConsColumn, isCompact ? 2 : 4, createStringInnerTextColumnSorter(DETERMINED_BY)));
        addColumnInfo(FILTERING_ON_EDIT, new ColumnInfo<>(messages.filteringOnEdit(),
                style.filteringOnEdit(), "", FILTERING_ON_EDIT, null,
                null, attrValueConsColumn, isCompact ? 2 : 4, createStringInnerTextColumnSorter(FILTERING_ON_EDIT)));
        addColumnInfo(CALCULATING_ON_EDIT, new ColumnInfo<>(messages.calculatingOnEdit(),
                style.calculatingOnEdit(), "", CALCULATING_ON_EDIT, null,
                null, attrValueConsColumn, isCompact ? 2 : 4, createStringInnerTextColumnSorter(CALCULATING_ON_EDIT)));
        addColumnInfo(DEFAULT_VALUE, new ColumnInfo<>(cmessages.defaultValue(),
                style.byDefault(), "", DEFAULT_VALUE, null,
                null, attrValueConsColumn, isCompact ? 2 : 4, createStringInnerTextColumnSorter(DEFAULT_VALUE)));
        addColumnInfo(HIDE_EMPTY, new ColumnInfo<>("",
                style.hideWhenDisp(), "", HIDE_EMPTY, null,
                messages.hideWhenEmpty(), attrHideConsColumn, isCompact ? 1 : 2));
        addColumnInfo(HIDE_NO_VALUE_TO_SELECT, new ColumnInfo<>("",
                style.hideWhenEdit(), "", HIDE_NO_VALUE_TO_SELECT, null,
                messages.hideWhenNoPossibleValues(), attrHideConsColumn, isCompact ? 1 : 2));

        addColumnInfo(EDIT_BUTTON, new ColumnInfo<Attribute>("", WidgetResources.INSTANCE.root().tableElemsTDIcons(),
                "12px", EDIT_BUTTON, null));
        addColumnInfo(DELETE_BUTTON, new ColumnInfo<Attribute>("", WidgetResources.INSTANCE.root().tableElemsTDIcons(),
                "12px", DELETE_BUTTON, null));

        addColumnInfo(MOVE_UP_BUTTON, new ColumnInfo<Attribute>("", "", "12px", MOVE_UP_BUTTON, null));
        addColumnInfo(MOVE_DOWN_BUTTON, new ColumnInfo<Attribute>("", "", "12px", MOVE_DOWN_BUTTON, null));
    }

    private boolean isInited()
    {
        return columnInfos != null && !columnInfos.isEmpty();
    }
}
