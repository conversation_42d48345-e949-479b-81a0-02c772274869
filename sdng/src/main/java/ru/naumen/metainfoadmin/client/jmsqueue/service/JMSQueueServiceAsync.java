package ru.naumen.metainfoadmin.client.jmsqueue.service;

import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.eventaction.EventAction;

/**
 * Инкапсуляция работы с {@link DispatchAsync} для операций очередей JMS
 * <AUTHOR>
 * @since 26.04.2021
 *
 **/
public interface JMSQueueServiceAsync
{
    /**
     * Добавление связи между выбранными ДПС и очередью
     * @param eventActionCodes коды выбранных дпс
     * @param jmsQueueCode очередь
     * @param callback коллбэк, который выполнит логику после запроса
     */
    void addLink(List<String> eventActionCodes, String jmsQueueCode, BasicCallback<SimpleResult<Void>> callback);

    /**
     * Разорвать связь между ДПС и очередью
     * @param eventActionCode код ДПС, у которого надо разорвать связь с очередью
     * @param callback коллбэк, который выполнит логику после запроса
     */
    void breakLink(String eventActionCode, AsyncCallback<Void> callback);

    /**
     * Разорвать связи между ДПС и очередью
     * @param eventActionsCodes коды ДПС, у которых нужно разорвать связь
     * @param callback коллбэк, который выполнит логику после запроса
     */
    void breakLinks(List<String> eventActionsCodes, AsyncCallback<Void> callback);

    /**
     * Запрос количества сообщений в очереди на текущий момент
     * @param jmsQueueCode код очереди
     * @param callback коллбэк, который выполнит логику после запроса
     */
    void getCountMessage(String jmsQueueCode, AsyncCallback<SimpleResult<Integer>> callback);

    /**
     * Запрос количества пользовательских очередей
     * @param callback коллбэк, который выполнит логику после запроса
     */
    void getCountJMSUserQueues(BasicCallback<SimpleResult<Integer>> callback);

    /**\
     * Запрос ДПС, доступных для этой очереди
     * @param jmsQueueCode код очереди
     * @param callback коллбэк, который выполнит логику после запроса
     */
    void getAvailableEventActionsForJMSQueue(String jmsQueueCode, BasicCallback<SimpleResult<List<DtObject>>> callback);

    /**
     * Запрос очередей, доступных для этого типа действий
     * @param eventAction ДПС для которого требуется найти доступные очереди
     * @param callback коллбэк, который выполнит логику после запроса
     */
    void getAvailableJMSQueuesForEventAction(EventAction eventAction,
            BasicCallback<SimpleResult<List<DtObject>>> callback);

    /**
     * Удаление очереди
     * @param code код очереди
     * @param callback коллбэк, который выполнит логику после запроса
     */
    void deleteJMSQueue(String code, AsyncCallback<Void> callback);

    /**
     * Запрос ДПС, связанных с текущей очередью
     * @param jmsQueueCode код очереди
     * @param callback коллбэк, который выполнит логику после запроса
     */
    void getEventActionsByJMSQueue(String jmsQueueCode, BasicCallback<SimpleResult<Object>> callback);

    /**
     * Запрос очередей для фильтрации в списке ДПС
     * @param callback коллбэк, который выполнит логику после запроса
     */
    void getJMSQueuesForFiltration(BasicCallback<SimpleResult<List<DtObject>>> callback);

    /**
     * Запрос доступности типа события "Поступления события в очередь" в списке событий
     * @param callback коллбэк, который выполнит логику после запроса
     */
    void possibleArrivedMessageToQueueEvent(BasicCallback<SimpleResult<Boolean>> callback);
}