package ru.naumen.metainfoadmin.client.embeddedapplications.form;

import java.util.List;

import com.google.gwt.event.shared.EventBus;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.EmbeddedApplicationAsyncServiceImpl;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.GetMessageBeforeEditEmbeddedApplicationEditAction;

/**
 * <AUTHOR>
 * @since 07.07.2016
 */
public class EditApplicationFormPresenter extends AbstractEmbeddedApplicationFormPresenter
{
    @Inject
    private DispatchAsync dispatch;

    @Inject
    private EmbeddedApplicationAsyncServiceImpl adminSettingsService;

    @Inject
    public EditApplicationFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void bindProperties()
    {
        super.bindProperties();
        code.setDisable();
        applicationType.setDisable();
    }

    @Override
    protected EmbeddedApplicationAdminSettingsDto getApplication()
    {
        return application;
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaptionText(messages.applicationEditing());
        super.onBind();
    }

    @Override
    protected void saveApplication(ReadyState readyState)
    {
        adminSettingsService.saveEmbeddedApplication(application, false, true, null, false,
                new CallbackDecorator<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto>(
                        readyState,
                        saveCallback)
                {
                });
    }

    @Override
    protected void getApplyConfirmationMessage(EmbeddedApplicationAdminSettingsDto app,
            BasicCallback<List<String>> callback)
    {
        GetMessageBeforeEditEmbeddedApplicationEditAction action =
                new GetMessageBeforeEditEmbeddedApplicationEditAction(app);
        dispatch.execute(action, new BasicCallback<SimpleResult<List<String>>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<List<String>> response)
            {
                callback.onSuccess(response.get());
            }

            @Override
            protected void handleFailure(String msg, @Nullable String details)
            {
                //do nothing - no object found
            }
        });
    }
}
