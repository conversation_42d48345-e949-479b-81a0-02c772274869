package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import static ru.naumen.metainfo.shared.Constants.LinkObjectType.CURRENT_USER;
import static ru.naumen.metainfo.shared.Constants.LinkObjectType.OBJECT_LINKED_TO_CURRENT_USER;
import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isClass;
import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isNotHidden;
import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isNotSystem;
import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isPossible;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.Relation;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления свойства "Класс объектов списка"
 *
 * <AUTHOR>
 * @since 30.10.2020
 */
@Singleton
public class LinkToContentMetaClassRefreshDelegateImpl implements PropertyDelegateRefresh<SelectItem, ListBoxProperty>
{
    @Inject
    protected MetainfoServiceAsync metainfoService;
    @Inject
    protected MetainfoUtils metainfoUtils;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String contentTypeStr = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);

        boolean isVisible = LinkToContentMetaClassPropertiesProcessor.isLinkToContentElementType(context)
                            && !RelObjectList.class.getSimpleName().equals(contentTypeStr);

        if (!isVisible)
        {
            callback.onSuccess(isVisible);
            context.getPropertyControllers().get(MenuItemLinkToContentCode.OBJECT_CLASS).unbindValidators();
            return;
        }

        final SingleSelectCellList<String> widget = property.getValueWidget();

        String metaClass = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.OBJECT_CLASS);

        if (ObjectList.class.getSimpleName().equals(contentTypeStr))
        {
            context.getPropertyValues().setProperty(MenuItemLinkToContentCode.OBJECT_CLASS, null);
            metainfoService.getMetaClasses(MetaClassFilters.and(isClass(), isNotSystem(), isPossible(), isNotHidden()),
                    new BasicCallback<List<MetaClassLite>>()
                    {
                        @Override
                        protected void handleSuccess(List<MetaClassLite> classesList)
                        {
                            widget.clear();
                            widget.refreshPopupCellList();
                            property.clearValue();
                            classesList.sort(CommonUtils.METACLASSLITE_COMPARATOR);
                            for (MetaClassLite clz : classesList)
                            {
                                if (!Root.FQN.isSameClass(clz.getFqn()))
                                {
                                    widget.addItem(clz.getTitle(),
                                            clz.getFqn().getId());
                                }
                            }
                            context.getPropertyValues().setProperty(MenuItemLinkToContentCode.OBJECT_CLASS,
                                    StringUtilities.isNotEmpty(metaClass) ? metaClass :
                                            SelectListPropertyValueExtractor.getValue(property));

                            /* Из-за асинхронного получения значения нужно ещё раз инициировать обновление полей,
                                 связанных с метаклассом */
                            context.getRefreshProcess().startCustomProcess(Lists.newArrayList(
                                    MenuItemLinkToContentCode.OBJECT_CASES,
                                    MenuItemLinkToContentCode.ATTRIBUTE_GROUP,
                                    MenuItemLinkToContentCode.LIST_TEMPLATE));
                            context.getPropertyControllers()
                                    .get(context.getRefreshProcess().getNextOperation())
                                    .refresh();
                        }
                    });
            callback.onSuccess(true);
        }
        else if (HierarchyGrid.class.getSimpleName().equals(contentTypeStr))
        {
            callback.onSuccess(false);
        }
        else
        {
            String linkObject = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT);

            if (linkObject == null)
            {
                callback.onSuccess(false);
            }
            else
            {
                ClassFqn metaclass;

                if (CURRENT_USER.equals(linkObject))
                {
                    metaclass = Employee.FQN;
                }
                else if (OBJECT_LINKED_TO_CURRENT_USER.equals(linkObject))
                {
                    RelationsAttrTreeObject linkObjectAttr =
                            context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT_ATTR);

                    if (linkObjectAttr == null || linkObjectAttr.getAttribute() == null)
                    {
                        callback.onSuccess(false);
                        return;
                    }

                    metaclass = linkObjectAttr.getAttribute()
                            .getType().<ObjectAttributeType> cast().getRelatedMetaClass();
                }
                else
                {
                    metaclass = ClassFqn.parse(linkObject);
                }

                metainfoService.getMetaClass(metaclass, new BasicCallback<MetaClass>()
                {
                    @Override
                    protected void handleSuccess(MetaClass value)
                    {
                        widget.clear();
                        widget.refreshPopupCellList();
                        property.clearValue();

                        Collection<Relation> relations = metainfoUtils.getIncomingParentRelations(
                                value.getIncomingRelations(), value.getFqn());
                        List<ClassFqn> fqns = new ArrayList<ClassFqn>();
                        for (Relation rel : relations)
                        {
                            fqns.add(rel.getLeft());
                        }
                        metainfoService.getMetaClasses(fqns, new BasicCallback<List<MetaClassLite>>()
                        {
                            @Override
                            protected void handleSuccess(List<MetaClassLite> classesList)
                            {
                                List<MetaClassLite> classes = Lists.newArrayList(classesList);
                                classes.sort(CommonUtils.METACLASSLITE_COMPARATOR);
                                for (MetaClassLite clz : metainfoUtils.getPossible(classes, true))
                                {
                                    String classId = clz.getFqn().getId();
                                    property.<SingleSelectCellList<?>> getValueWidget().addItem(clz.getTitle(),
                                            classId);
                                }

                                context.getPropertyValues().setProperty(MenuItemLinkToContentCode.OBJECT_CLASS,
                                        StringUtilities.isNotEmpty(metaClass) ? metaClass :
                                                SelectListPropertyValueExtractor.getValue(property));

                                /* Из-за асинхронного получения значения нужно ещё раз инициировать обновление полей,
                                 связанных с метаклассом */
                                context.getRefreshProcess().startCustomProcess(Lists.newArrayList(
                                        MenuItemLinkToContentCode.OBJECT_CASES,
                                        MenuItemLinkToContentCode.ATTRIBUTE_GROUP,
                                        MenuItemLinkToContentCode.LIST_TEMPLATE));
                                context.getPropertyControllers()
                                        .get(context.getRefreshProcess().getNextOperation())
                                        .refresh();
                            }
                        });
                    }
                });
                callback.onSuccess(true);
            }
        }
    }
}