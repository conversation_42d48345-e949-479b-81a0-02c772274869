package ru.naumen.metainfoadmin.client.escalation.scheme.attributes;

import java.util.ArrayList;
import java.util.HashMap;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Named;
import com.google.inject.name.Names;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.container.AttributePropertyDescription;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfo.client.CachedMetainfoServiceAsync;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.sets.formatters.SettingsSetPropertyFormatter;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * <AUTHOR>
 * @since 20.08.2012
 *
 */
public class EscalationSchemeAttributesGinModule extends AbstractGinModule
{
    public static class EscalationSchemeAttributesPropertyProvider
            implements Provider<ArrayList<AttributePropertyDescription<?, EscalationScheme>>>
    {
        private final CommonMessages commonMessages;
        private final AttributesMessages attributesMessages;
        private final AdminDialogMessages adminDialogMessages;
        private final Formatters formatters;
        private final I18nUtil i18nUtil;
        private final SettingsSetOnFormCreator settingsSetOnFormCreator;
        private final CachedMetainfoServiceAsync cachedMetainfoServiceAsync;
        private final SettingsSetPropertyFormatter settingsSetPropertyFormatter;

        private final Property<String> title;
        private final Property<String> code;
        private final Property<String> description;
        private final Property<String> objects;
        private final Property<String> timer;
        private final Property<Boolean> enabled;

        @Inject
        public EscalationSchemeAttributesPropertyProvider(CommonMessages commonMessages,
                AttributesMessages attributesMessages,
                AdminDialogMessages adminDialogMessages,
                Formatters formatters,
                I18nUtil i18nUtil,
                SettingsSetOnFormCreator settingsSetOnFormCreator,
                CachedMetainfoServiceAsync cachedMetainfoServiceAsync,
                SettingsSetPropertyFormatter settingsSetPropertyFormatter,
                @Named(PropertiesGinModule.TEXT) Property<String> title,
                @Named(PropertiesGinModule.TEXT) Property<String> code,
                @Named(PropertiesGinModule.TEXT) Property<String> description,
                @Named(PropertiesGinModule.TEXT) Property<String> objects,
                @Named(PropertiesGinModule.TEXT) Property<String> timer,
                @Named(PropertiesGinModule.BOOLEAN_IMAGE) Property<Boolean> enabled)
        {
            this.commonMessages = commonMessages;
            this.attributesMessages = attributesMessages;
            this.adminDialogMessages = adminDialogMessages;
            this.formatters = formatters;
            this.i18nUtil = i18nUtil;
            this.settingsSetOnFormCreator = settingsSetOnFormCreator;
            this.cachedMetainfoServiceAsync = cachedMetainfoServiceAsync;
            this.settingsSetPropertyFormatter = settingsSetPropertyFormatter;
            this.title = title;
            this.code = code;
            this.description = description;
            this.objects = objects;
            this.timer = timer;
            this.enabled = enabled;
        }

        @Override
        public ArrayList<AttributePropertyDescription<?, EscalationScheme>> get()
        {
            ArrayList<AttributePropertyDescription<?, EscalationScheme>> result = new ArrayList<>();
            result.add(new AttributePropertyDescription<String, EscalationScheme>(commonMessages.title(), title,
                    "title", i18nUtil::getLocalizedTitle));
            result.add(new AttributePropertyDescription<String, EscalationScheme>(commonMessages.code(), code, "code",
                    EscalationScheme::getCode));
            result.add(new AttributePropertyDescription<String, EscalationScheme>(commonMessages.description(),
                    description, "description", i18nUtil::getLocalizedDescription));
            result.add(new AttributePropertyDescription<String, EscalationScheme>(commonMessages.objects(), objects,
                    "objects", formatters::escalationObjects));
            result.add(new AttributePropertyDescription<String, EscalationScheme>(attributesMessages.timerDefinition(),
                    timer, "timer", scheme -> i18nUtil.getLocalizedTitle(scheme.getTimer())));
            result.add(new AttributePropertyDescription<Boolean, EscalationScheme>(commonMessages.on(), enabled,
                    "enabled", scheme -> EscalationScheme.StateCode.ON.equals(scheme.getState())));
            if (settingsSetOnFormCreator.isDisplayedOnCards())
            {
                result.add(new AttributePropertyDescription<>(adminDialogMessages.settingsSet(),
                        settingsSetOnFormCreator.createFieldOnCard(), "settingsSet",
                        input ->
                        {
                            if (input == null)
                            {
                                return "";
                            }
                            DtObject settingsSet = cachedMetainfoServiceAsync.getSettingsSet(input.getSettingsSet());
                            if (settingsSet == null)
                            {
                                return "";
                            }
                            return settingsSetPropertyFormatter.format(
                                    settingsSet.getUUID(), settingsSet.getTitle()).asString();
                        }));
            }
            return result;
        }
    }

    public static class EscalationSchemeStateCaptionProvider implements Provider<HashMap<String, String>>
    {
        @Inject
        CommonMessages messages;

        @Override
        public HashMap<String, String> get()
        {
            HashMap<String, String> result = new HashMap<>();
            result.put(EscalationScheme.StateCode.ON, messages.switchOff());
            result.put(EscalationScheme.StateCode.OFF, messages.switchOn());
            return result;
        }
    }

    public static final String ESCALATION_SCHEME_ATTRIBUTES = "escalationSchemeAttributes";
    public static final String ESCALATION_SCHEME_STATE_CAPTION = "escalationSchemeStateCaption";

    @Override
    protected void configure()
    {
        bind(EscalationSchemeAttributesMessages.class).in(Singleton.class);
        //@formatter:off
        bind(new TypeLiteral<ArrayList<AttributePropertyDescription<?, EscalationScheme>>>(){})
            .annotatedWith(Names.named(ESCALATION_SCHEME_ATTRIBUTES))
            .toProvider(EscalationSchemeAttributesPropertyProvider.class);
        bind(new TypeLiteral<HashMap<String, String>>(){})
            .annotatedWith(Names.named(ESCALATION_SCHEME_STATE_CAPTION))
            .toProvider(EscalationSchemeStateCaptionProvider.class)
            .in(Singleton.class);
        //@formatter:on
    }
}