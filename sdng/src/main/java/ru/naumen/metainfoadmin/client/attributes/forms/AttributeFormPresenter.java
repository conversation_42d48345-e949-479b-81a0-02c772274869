package ru.naumen.metainfoadmin.client.attributes.forms;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.METAINFO;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import java.util.HashMap;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.events.ApplyFormEvent;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.mvp.DisplaySynchronization;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.ProcessDescriptor;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector.PropertyContainerPresenterFactory;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.HasReadyState.SynchronizationCallbackRegistration;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.CommonRestriction;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.DefaultContext;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Базовый класс для презентеров добавления и редактирования атрибутов
 * Роль класса: подготавка данных для PropertyContainerPresenter, его инициализация и
 * инициализация обработчика нажатия на кнопку ОК (создается при помощи applyHandlerFactory)
 * Тип T extends AttributeForm характеризует тип формы - AttributeFormAdd или AttributeFormEdit.
 * Он проходит насквозь по всем классам, которые у которых есть функциональные различия в зависимости от типа формы.
 *
 *
 * <AUTHOR>
 *
 */
public class AttributeFormPresenter<F extends ObjectForm> extends OkCancelPresenter<PropertyFormDisplay>
        implements CallbackPresenter<Attribute, MetaClass>
{
    @Inject
    private PropertyContainerPresenterFactory containerFactory;
    //Класс, который получает управление после завершения биндинга всех свойств
    @Inject
    private AttributeFormAfterBindHandler<F> afterBindHandler;
    //Компонент PropertyContainerPresenter - фабрика свойств
    @Inject
    private PropertyControllerFactory<Attribute, F> propertyFactory;
    //Обработчик нажатия на кнопку ОК. Отличается в зависимости от типа формы
    @Inject
    private AttributeFormApplyHandlerFactory<F> applyHandlerFactory;
    //Класс, отвечающий за исходную инициализацию значений свойств. Отличается в зависимости от типа формы.
    @Inject
    private AttributeFormPropertyValuesInitializer<F> propertyValuesInitializer;
    //Строковые константы для формы в целом. Например, название формы. Отличается в зависимости от типа формы.
    @Inject
    private AttributeFormMessages<F> messages;

    //Компонент PropertyContainerPresenter - класс для управления процессом биндинга
    private final ProcessDescriptor BINDING_PROCESS;
    //Компонент PropertyContainerPresenter - класс для управления процессом обновления дисплея
    private final ProcessDescriptor REFRESH_PROCESS;

    //Класс, инкапсулирующий в себе операции создания, биндинга и отображений свойств, а также их взаимодействие
    private PropertyContainerPresenter propertyContainer; //NOPMD
    //Компонент PropertyContainerPresenter - набор некоторых исходных вспомогательных значений
    private final IProperties contextProps = new MapProperties();
    //Компонент PropertyContainerPresenter - набор исходных значений свойств
    private final IProperties propertyValues = new MapProperties();
    //Компонент PropertyContainerPresenter - мапа <Код обработчика валидации, объект-обработчик валидации>.
    //Чтобы иметь возможность отдельно проводить валидацию свойств или набора свойств
    private final Map<String, Processor> validationProcessors = new HashMap<>();
    //Callback, которому передается управление по закрытию формы. Скорее всего, стоит заменить на событие через EventBus
    private AsyncCallback<MetaClass> callback = new BasicCallback<>();
    private final Context context;
    //Компонент PropertyContainerPresenter - список кодов свойств на форме.
    private final List<String> properties;
    private final SynchronizationCallbackRegistration scr;

    @Inject
    public AttributeFormPresenter(@Assisted Context context, PropertyFormDisplay display, EventBus eventBus,
            AttributeFormConstants<F> constants)
    {
        super(display, eventBus);
        this.context = new DefaultContext(context, context.getMetainfo(), context.getEventBus(), display);
        this.properties = Arrays.asList(constants.properties());
        scr = context.getReadyState().registerSynchronization(new DisplaySynchronization(this, display));

        contextProps.setProperty(METAINFO, context.getMetainfo());
        List<String> processProperties = Lists.newArrayList(properties);
        BINDING_PROCESS = new ProcessDescriptor(processProperties);
        REFRESH_PROCESS = new ProcessDescriptor(processProperties);
        propertyValues.setProperty(AttributeFormPropertyCode.COMPUTABLE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.COMPOSITE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.DETERMINABLE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.EDITABLE, true);
        propertyValues.setProperty(AttributeFormPropertyCode.HIDE_ARCHIVED, true);
        propertyValues.setProperty(AttributeFormPropertyCode.COMPLEX_RELATION, Boolean.FALSE.toString());
        propertyValues.setProperty(AttributeFormPropertyCode.EDIT_ON_COMPLEX_FORM_ONLY, false);
        propertyValues.setProperty(AttributeFormPropertyCode.EDITABLE_IN_LISTS, false);
        propertyValues.setProperty(AttributeFormPropertyCode.REQUIRED, false);
        propertyValues.setProperty(AttributeFormPropertyCode.REQUIRED_IN_INTERFACE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.UNIQUE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.FILTERED_BY_SCRIPT, false);
        propertyValues.setProperty(AttributeFormPropertyCode.DEFAULT_BY_SCRIPT, false);
        propertyValues.setProperty(AttributeFormPropertyCode.USE_GEN_RULE, false);
        propertyValues.setProperty(AttributeFormPropertyCode.EXPORT_NDAP, false);
        propertyValues.setProperty(AttributeFormPropertyCode.RELATED_ATTRS_TO_EXPORT, null);
        propertyValues.setProperty(AttributeFormPropertyCode.HIDDEN_WHEN_EMPTY, false);
        propertyValues.setProperty(AttributeFormPropertyCode.HIDDEN_WHEN_NO_POSSIBLE_VALUES, false);
        propertyValues.setProperty(AttributeFormPropertyCode.ADVLIST_SEMANTIC_FILTERING, false);
        propertyValues.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_SCRIPT, null);
        propertyValues.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_TYPE, null);
        propertyValues.setProperty(AttributeFormPropertyCode.DATE_TIME_COMMON_RESTRICTIONS,
                Lists.newArrayList(CommonRestriction.values()));
        propertyValues.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_ATTRIBUTE, null);
        propertyValues.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_CONDITION, null);
        propertyValues.setProperty(AttributeFormPropertyCode.COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT, null);
        propertyValues.setProperty(AttributeFormPropertyCode.HIDDEN_ATTR_CAPTION, false);
    }

    @Override
    public void init(Attribute attribute, AsyncCallback<MetaClass> callback)
    {
        this.callback = callback;
        propertyValuesInitializer.init(contextProps, propertyValues, attribute, display.getReadyState());
    }

    @Override
    public void refreshDisplay()
    {
    }

    @Inject
    public void setUpValidation(Processor defaultProcessor, Processor permTypesProcessor)
    {
        validationProcessors.put(AttributeFormValidationCode.DEFAULT, defaultProcessor);
        validationProcessors.put(AttributeFormValidationCode.PERM_TYPES, permTypesProcessor);
    }

    @Override
    protected void onBind()
    {
        propertyValuesInitializer.loadRelatedData(contextProps, propertyValues, new BasicCallback<Void>(display)
        {
            @Override
            protected void handleSuccess(Void value)
            {
                getDisplay().setCaptionText(messages.title());
                propertyContainer = containerFactory.create(properties, display, propertyFactory, BINDING_PROCESS,
                        REFRESH_PROCESS, contextProps, propertyValues, afterBindHandler, validationProcessors);
                registerHandler(eventBus.addHandler(ApplyFormEvent.getType(), applyHandlerFactory.create(contextProps,
                        propertyValues, propertyContainer, context, AttributeFormPresenter.this, callback)));
                registerHandler(propertyContainer.addUpdateTabOrderHandler(AttributeFormPresenter.this));
                propertyContainer.bind();

                AttributeFormPresenter.super.onBind();
            }
        });
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        scr.unregister();
    }
}