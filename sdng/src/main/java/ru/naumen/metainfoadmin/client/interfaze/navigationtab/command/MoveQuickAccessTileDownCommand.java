package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.SaveNavigationSettingsAction;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelAreaSettingsDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelElementWrapper;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;

/**
 * Команда смещения плитки быстрого доступа вниз
 *
 * <AUTHOR>
 * @since 22.07.2020
 */
public class MoveQuickAccessTileDownCommand extends BaseCommandImpl<QuickAccessPanelElementWrapper,
        DtoContainer<NavigationSettings>>
{
    public static final String ID = "moveQuickAccessTileDownCommand";

    private final DispatchAsync dispatch;

    @Inject
    public MoveQuickAccessTileDownCommand(@Assisted QuickAccessPanelTileCommandParam param,
            DispatchAsync dispatch)
    {
        super(param);
        this.dispatch = dispatch;
    }

    @Override
    public void execute(CommandParam<QuickAccessPanelElementWrapper, DtoContainer<NavigationSettings>> param)
    {
        QuickAccessPanelTileCommandParam p = (QuickAccessPanelTileCommandParam)param;
        QuickAccessPanelElementWrapper wrapper = p.getValue();
        NavigationSettings settings = p.getSettings().get();
        Optional<QuickAccessPanelAreaSettingsDTO> area = settings.getQuickAccessPanelSettings().getAreas()
                .stream().filter(a -> a.getTiles().contains(wrapper.getWrappable())).findFirst();
        if (area.isPresent())
        {
            int index = area.map(a -> a.getTiles().indexOf(wrapper.getWrappable())).get();
            area.get().getTiles().remove(index);
            area.get().getTiles().add(index + 1, (QuickAccessTileDTO)wrapper.getWrappable());
        }

        SaveNavigationSettingsAction action = new SaveNavigationSettingsAction(settings);
        dispatch.execute(action, new SimpleResultCallbackDecorator<>(p.getCallbackSafe()));
    }

    @Override
    public boolean isPossible(Object input)
    {
        QuickAccessPanelTileCommandParam p = (QuickAccessPanelTileCommandParam)param;
        Map<QuickAccessPanelAreaSettingsDTO, Integer> indexes = getQuickAccessTileIndex(input, p);
        if (indexes.isEmpty())
        {
            return false;
        }
        QuickAccessPanelAreaSettingsDTO area = indexes.keySet().iterator().next();
        int index = indexes.get(area);
        return index >= 0 && isPossible(index, area.getTiles().size());
    }

    protected static Map<QuickAccessPanelAreaSettingsDTO, Integer> getQuickAccessTileIndex(Object input,
            QuickAccessPanelTileCommandParam p)
    {
        Map<QuickAccessPanelAreaSettingsDTO, Integer> res = new HashMap<>();
        if (!(input instanceof QuickAccessPanelElementWrapper) && !(((QuickAccessPanelElementWrapper)input)
                .getWrappable() instanceof QuickAccessTileDTO) || null == p)
        {
            return res;
        }
        QuickAccessPanelElementWrapper item = (QuickAccessPanelElementWrapper)input;

        NavigationSettings settings = p.getSettings().get();
        Optional<QuickAccessPanelAreaSettingsDTO> area = settings.getQuickAccessPanelSettings().getAreas()
                .stream().filter(a -> a.getTiles().contains(item.getWrappable())).findFirst();

        area.ifPresent(a -> res.put(a, a.getTiles().indexOf(item.getWrappable())));

        return res;
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DOWN;
    }

    protected boolean isPossible(int index, int count)
    {
        return index < count - 1;
    }
}