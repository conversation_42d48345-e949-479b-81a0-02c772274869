package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

/**
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public abstract class NavigationSettingsMenuItemAbstractCommandParam<T extends IMenuItem>
        extends NavigationSettingsAbstractCommandParam<T>
{
    protected NavigationSettingsMenuItemAbstractCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable ValueSource<T> valueSource, @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, valueSource, callback);
    }

    protected NavigationSettingsMenuItemAbstractCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable T value, @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, value, callback);
    }

    public abstract List<T> getSiblings(T item);

    public abstract List<T> getMenuItems();

    public abstract Map<String, LinkedList<String>> getMenuItemPaths();
}
