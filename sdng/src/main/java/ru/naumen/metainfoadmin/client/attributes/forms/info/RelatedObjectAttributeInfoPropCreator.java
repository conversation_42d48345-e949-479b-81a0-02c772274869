package ru.naumen.metainfoadmin.client.attributes.forms.info;

import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.IHyperlink;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Создает {@link Property} для отображения информации о атрибуте связанного объекта
 *
 * <AUTHOR>
 * @since 14.09.18
 */
public class RelatedObjectAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    protected void createInt(String code)
    {
        if (!attribute.getType().isAttributeOfRelatedObject())
        {
            return;
        }

        metainfoService.getMetaClass(attribute.getType().getRelatedObjectMetaClass(), new BasicCallback<MetaClass>(rs)
        {
            @Override
            protected void handleSuccess(MetaClass metaClass)
            {
                String attrCode = attribute.getType().getRelatedObjectAttribute();
                Attribute relatedObjectAttribute = metaClass.getAttribute(attrCode);
                createProperty(code, createHyperLink(metaClass.getTitle(), relatedObjectAttribute
                        .getTitle(), attribute.getType().getRelatedObjectMetaClass().asString()));
            }
        });
    }

    private String createHyperLink(String classTitle, String attrTitle, String code)
    {
        String url = "#" + MetaClassPlace.PLACE_PREFIX + ":" + code;
        IHyperlink link = new Hyperlink(classTitle + "/" + attrTitle, url);
        return link.toString();
    }
}
