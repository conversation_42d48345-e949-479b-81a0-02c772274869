package ru.naumen.metainfoadmin.client.attributes.forms.props;

import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.core.client.jsinterop.inputmask.AliasNames.INTEGER_WITH_GROUPS_ALIAS;
import static ru.naumen.core.shared.FqnExtractorFunction.FQN_EXTRACTOR;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.HAS_DEFAULT_VALUE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.HAS_ONLY_COMPUTABLE_DEFAULT_VALUE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.METAINFO;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPOSITE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DEFAULT_BY_SCRIPT;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.safehtml.client.SafeHtmlTemplates;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.HasText;
import com.google.gwt.user.client.ui.Widget;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.attr.presentation.PresentationFactoryEdit;
import ru.naumen.core.client.attr.presentation.PresentationFactoryView;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.InputMaskUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.jsinterop.inputmask.Alias;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.dto.impl.fastselection.FastSelectionDtoValueCellTree;
import ru.naumen.core.client.validation.InputMaskValidator;
import ru.naumen.core.client.validation.NumberInputMaskValidator;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.validation.ValidationMessages;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.DateTimeIntervalWidget;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.MaskedNumberInputBox;
import ru.naumen.core.client.widgets.NauCheckBox;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.ValueToSelectItemConverter;
import ru.naumen.core.client.widgets.mask.InputMaskOptions;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerImpl;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.core.shared.CoreConstants.AttributeTypeProperties;
import ru.naumen.core.shared.HasReadyState;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.dispatch.FastSelectionDtObjectTreeValue;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.CaseListAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.Constants.StringAttributeType.InputMaskMode;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.DoubleAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormValidationCode;
import ru.naumen.metainfoadmin.client.attributes.forms.typeEmul.AttributeTypeFactory;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 21.05.2012
 */
public class DefaultValuePropertyControllerImpl extends PropertyControllerImpl<Object, Property<Object>>
{
    /**
     * Шаблон html отображения атрибута интервал времени
     * <AUTHOR>
     * @since 28.11.2023
     */
    public interface Template extends SafeHtmlTemplates
    {
        @Template("<span style='display:inline-block; min-width:{1}px;'>{0}</span><span>{2}</span>")
        SafeHtml dateTimePropertyCaption(String value, String minWidth, String unit);
    }

    Template template = GWT.create(Template.class);

    private class DefaultValuePropertyRefreshCallback extends DefaultRefreshCallback
    {
        @Override
        protected void handleSuccess(Boolean value)
        {
            if (Boolean.TRUE.equals(value))
            {
                addProperty();
            }
            else
            {
                removeProperty();
            }
            trySetPropertyValue();
            bindProperty();
            continuePropertyRefresh();
        }
    }

    private static final Logger LOG = Logger.getLogger(DefaultValuePropertyControllerImpl.class.getName());
    @Inject
    private PresentationFactories prsFactories;
    @Inject
    protected AttributeTypeFactory attrTypeFactory;
    @Inject
    private PropertyCreator propertyCreator;
    @Inject
    private AttributesMessages messages;
    @Inject
    private ValidationMessages validationMessages;
    @Inject
    private CommonMessages cmessages;

    private boolean isFirstLoading = true;
    private boolean isEmptyValue = false;

    @Inject
    public DefaultValuePropertyControllerImpl(@Assisted String code, @Assisted PropertyContainerContext context,
            @Assisted PropertyParametersDescriptor propertyParams,
            @Assisted PropertyDelegateDescriptor<Object, Property<Object>> propertyDelegates)
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    @Override
    public boolean getValue()
    {
        if (property == null)
        {
            return false;
        }
        initValidation();
        Object oldValue = context.getPropertyValues().getProperty(code);
        if (property.getValueWidget() instanceof FastSelectionDtoValueCellTree)
        {
            context.getPropertyValues()
                    .setProperty(code,
                            new FastSelectionDtObjectTreeValue(property.<FastSelectionDtoValueCellTree> getValueWidget()
                                    .getTreeModel().getSelectionModel().getSelectionChangeHistory(),
                                    Constants.TEMP_UUID));
        }
        else
        {
            context.getPropertyValues().setProperty(code, SelectListPropertyValueExtractor.getValue(property));
        }
        Object newValue = context.getPropertyValues().getProperty(code);
        return !ObjectUtils.equals(oldValue, newValue);
    }

    @Override
    public void refresh()
    {
        for (ValidationUnit<Object> vu : validationUnits)
        {
            vu.unregister();
        }
        final AttributeType attrType = getAttrType();

        Boolean defaultByScript = context.getPropertyValues().getProperty(DEFAULT_BY_SCRIPT);
        Boolean hasDefault = context.getContextValues().getProperty(HAS_DEFAULT_VALUE);
        Boolean computable = context.getPropertyValues().getProperty(COMPUTABLE);
        Boolean definableByTemplate = context.getPropertyValues().getProperty(COMPOSITE);
        Boolean hasOnlyComputableDefaultValue = Boolean.TRUE.equals(context.getContextValues().getProperty(
                HAS_ONLY_COMPUTABLE_DEFAULT_VALUE));
        updateNumericAttrType(attrType);

        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        String attrCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE);
        Boolean skipForClass = SuperUser.FQN.isSameClass(metaClass.getFqn())
                               && (SuperUser.LOGIN.equals(attrCode) || SuperUser.PASSWORD.equals(attrCode));

        boolean isSkip = Boolean.FALSE.equals(hasDefault) || defaultByScript && !computable || definableByTemplate
                         || skipForClass || hasOnlyComputableDefaultValue;
        if (isSkip)
        {
            new DefaultRefreshCallback().onSuccess(false);
            return;
        }

        final String editPrsCode = context.getPropertyValues()
                .getProperty(AttributeFormPropertyCode.EDIT_PRS);
        final PresentationFactoryEdit<Object> eprsFactory = prsFactories.getEditPresentationFactory(editPrsCode);
        final String viewPrsCode = context.getPropertyValues()
                .getProperty(AttributeFormPropertyCode.SHOW_PRS);
        final PresentationFactoryView<Object> showPrsFactory = prsFactories.getViewPresentationFactory(viewPrsCode);

        eprsFactory.createWidget(createPresentationContext(attrType), new BasicCallback<HasValueOrThrow<Object>>()
        {
            @Override
            protected void handleSuccess(HasValueOrThrow<Object> widget)
            {
                refreshIntervalListIfDateTimeIntervalWidget(widget);
                clearHandlerRegistrations();
                property = propertyCreator.create(cmessages.defaultValue(), widget);
                property.setEnabled(context.isEnabled(code));
                setValidator(eprsFactory, attrType);

                //костыль для checkbox: его отображаем как свойство - название не сверху, а сбоку
                if (widget.getClass().equals(NauCheckBox.class))
                {
                    String yes = showPrsFactory.getTitle().split("/")[0];
                    ((HasText)widget).setText(messages.checkBoxDefValue(yes));
                    property.asWidget().addStyleName(WidgetResources.INSTANCE.form().checkBoxes());
                    property.getCaptionWidget().asWidget()
                            .addStyleName(WidgetResources.INSTANCE.additional().displayNone());
                }
                new DefaultValuePropertyRefreshCallback()
                        .onSuccess(context.getContextValues().<Boolean> getProperty(HAS_DEFAULT_VALUE));
            }
        });
    }

    @Override
    protected void bindProperty()
    {
        super.bindProperty();
        if (property.asWidget() instanceof DateTimeIntervalWidget)
        {
            Scheduler.get().scheduleDeferred(this::bindDateTimeIntervalProperty);
        }
    }

    // TODO костыль, переделать на нормальную верстку
    private void bindDateTimeIntervalProperty()
    {
        Widget elem1 = (Widget)((DateTimeIntervalWidget)property.asWidget()).getFirstFocusElement();
        Widget elem2 = ((DateTimeIntervalWidget)property.asWidget()).getIntervalList();
        String minWidth = Integer.toString(elem1.getOffsetWidth() + (int)(elem2.getOffsetWidth() * .03));
        property.setCaption(template.dateTimePropertyCaption(cmessages.value(), minWidth, cmessages.unit()));

        property.setValidationMarker(true);
        if (context.getPropertyValues().getProperty(AttributeFormPropertyCode.DEFAULT_VALUE) == null
            && !isEmptyValue)
        {
            property.addValidationMessage(validationMessages.intervalUnitValueValidationError());
            property.addValidationTitle(validationMessages.intervalUnitValueValidationError());
        }
    }

    @Override
    protected void doBind(AsyncCallback<Void> callback)
    {
        callback.onSuccess(null);
    }

    protected Attribute getAttributeForPrsContext()
    {
        return context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
    }

    protected AttributeType getAttrType()
    {
        Attribute attr = getAttributeForPrsContext();
        if (attr != null)
        {
            return attr.getType();
        }
        return attrTypeFactory.create(context.getContextValues(), context.getPropertyValues());
    }

    private PresentationContext createPresentationContext(AttributeType attrType)
    {
        //TEMP_UUID - чтобы корректно отрабатывал виджет выбора ответственного на форме добавления атрибута
        MetaClass metaClass = context.getContextValues().<MetaClass> getProperty(METAINFO);
        DtObject dtObject = new SimpleDtObject(Constants.TEMP_UUID, "", metaClass != null ? metaClass.getFqn() : null);

        MapProperties prsProps = new MapProperties();
        prsProps.setProperty(Presentations.SELECT_SORTING,
                context.getPropertyValues().getProperty(AttributeFormPropertyCode.SELECT_SORTING));
        prsProps.put(Presentations.SUGGESTION_CLASSFQN,
                context.getPropertyValues().getProperty(AttributeFormPropertyCode.SUGGEST_CATALOG));

        String editPrsCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.EDIT_PRS);
        Attribute attr = getAttributeForPrsContext();
        PresentationContext pc = new PresentationContext(attr, attrType, dtObject);
        pc.setPresentation(prsProps).setPresentationCode(editPrsCode);
        pc.setPermittedTypeFqns(make(context.getPropertyValues().<List<IHasMetaInfo>> getProperty(
                AttributeFormPropertyCode.PERMITTED_TYPES, new ArrayList<>())).map(FQN_EXTRACTOR)
                .toSet());
        pc.setFilterEnabled(false);
        Object prevDefaultValue = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DEFAULT_VALUE);
        if (prevDefaultValue instanceof FastSelectionDtObjectTreeValue)
        {
            pc.setInitialValue(prevDefaultValue);
        }
        if (!ObjectUtils
                .isEmpty(context.getPropertyValues().<String> getProperty(AttributeFormPropertyCode.INPUTMASK_MODE)))
        {
            pc.setInputMaskMode(InputMaskMode.getByCode(
                    context.getPropertyValues().<String> getProperty(AttributeFormPropertyCode.INPUTMASK_MODE)));
            pc.setInputMask(getInputMask());
        }
        return pc;
    }

    /**
     * Возвращает типы класса в либо null
     *
     * @param value
     * @param properties
     * @return
     */
    private Object getAllCasesOrNull(Iterable<?> value, IProperties properties)
    {
        if (value == null)
        {
            return null;
        }

        String targetClass = properties.getProperty(AttributeFormPropertyCode.TARGET_CLASS);

        Iterator<?> i = value.iterator();
        while (i.hasNext())
        {
            ClassFqn fqn = (ClassFqn)i.next();
            if (!fqn.isSameClass(ClassFqn.parse(targetClass)))
            {
                return null;
            }
        }

        return value;
    }

    /**
     * Возвращает все элементы справочника, которые были до этого выбраны, либо null
     */
    private Object getAllCatalogItemsOrNull(Iterable<?> value, IProperties properties)
    {
        String catalogCode = properties.getProperty(AttributeFormPropertyCode.TARGET_CATALOG);
        if (null == catalogCode)
        {
            return null;
        }
        Iterator<?> it = value.iterator();
        while (it.hasNext())
        {
            Object obj = it.next();
            if (obj instanceof TreeDtObject)
            {
                obj = ((TreeDtObject)obj).getAdaptee();
            }
            if (!(obj instanceof IHasMetaInfo) || !isItemOfSelectedCatalog((IHasMetaInfo)obj, catalogCode))
            {
                return null;
            }
        }
        return value;
    }

    /**
     * Возвращает все элементы, если каждый из них удовлетворяет ограничениям по типам,
     * либо null
     */
    private Object getAllObjectsOrNull(Iterable<?> value, Collection<DtObject> permittedTypes)
    {
        Iterator<?> it = value.iterator();
        while (it.hasNext())
        {
            if (null == getValueOrNull(it.next(), permittedTypes))
            {
                return null;
            }
        }
        return value;
    }

    private Object getCatalogItemOrNull(Object value, IProperties properties)
    {
        String catalogCode = properties.getProperty(AttributeFormPropertyCode.TARGET_CATALOG);
        if (!(value instanceof IHasMetaInfo) || !isItemOfSelectedCatalog((IHasMetaInfo)value, catalogCode))
        {
            return null;
        }
        return value;
    }

    private String getInputMask()
    {
        if (StringAttributeType.INPUT_MASK_ALIAS
                .equals(context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_MODE)))
        {
            return context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_ALIAS);
        }
        if (StringAttributeType.INPUT_MASK_DEFINITIONS
                .equals(context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_MODE)))
        {
            return context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_DEFINITIONS);
        }
        if (StringAttributeType.INPUT_MASK_REGEX
                .equals(context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_MODE)))
        {
            return context.getPropertyValues().getProperty(AttributeFormPropertyCode.INPUTMASK_REGEX);
        }
        return "";
    }

    /**
     * Метод получает значение, ограниченное по актуальным типам
     * @return либо value, если оно удовлетворяет ограничениям
     * (работает для классов и их типов первого уровня вложенности), либо null
     */
    private Object getPermittedValue(Object value, IProperties properties)
    {
        Collection<DtObject> permittedTypes = properties.getProperty(AttributeFormPropertyCode.PERMITTED_TYPES);
        String attrType = properties.getProperty(AttributeFormPropertyCode.ATTR_TYPE);

        if (value instanceof Iterable)
        {
            if (CatalogItemsAttributeType.CODE.equals(attrType))
            {
                return getAllCatalogItemsOrNull((Iterable<?>)value, properties);
            }
            else if (CaseListAttributeType.CODE.equals(attrType))
            {
                return getAllCasesOrNull((Iterable<?>)value, properties);
            }
            return getAllObjectsOrNull((Iterable<?>)value, permittedTypes);
        }
        else if (CatalogItemAttributeType.CODE.equals(attrType))
        {
            return getCatalogItemOrNull(value, properties);
        }
        return getValueOrNull(value, permittedTypes);
    }

    /**
     * Возвращает входное значение, если оно удовлетворяет ограничениям по типам, либо null
     */
    private Object getValueOrNull(Object value, Collection<DtObject> permittedTypes)
    {
        if (!(value instanceof IHasMetaInfo) || ObjectUtils.isEmpty(permittedTypes))
        {
            return value;
        }
        ClassFqn fqn1 = ((IHasMetaInfo)value).getMetaClass();
        for (DtObject type : permittedTypes)
        {
            ClassFqn fqn2 = type.getMetaClass();
            if (fqn2.isCase() && fqn2.equals(fqn1) || fqn1.isCaseOf(fqn2))
            {
                return value;
            }
        }
        return null;
    }

    private void initValidation()
    {
        property.initValidation();
        isEmptyValue = property.getValue() == null;
    }

    /**
     * Проверяет, относится ли элемент к выбранному справочнику
     */
    private boolean isItemOfSelectedCatalog(IHasMetaInfo obj, String catalogCode)
    {
        return obj.getMetaClass() != null && obj.getMetaClass().toString().equalsIgnoreCase(catalogCode);
    }

    private void refreshIntervalListIfDateTimeIntervalWidget(HasValueOrThrow<Object> widget)
    {
        if (widget.asWidget() instanceof DateTimeIntervalWidget)
        {
            Collection<String> intervalAvailableUnits = new ArrayList<>();
            if (context.getPropertyValues().<Boolean> getProperty(AttributeFormPropertyCode.DETERMINABLE)
                || context.getPropertyValues().<Boolean> getProperty(AttributeFormPropertyCode.COMPUTABLE)
                || null == context.getPropertyValues()
                    .getProperty(AttributeFormPropertyCode.INTERVAL_AVAILABLE_UNITS))
            {
                for (Interval interval : Interval.values())
                {
                    intervalAvailableUnits.add(interval.name());
                }
            }
            else
            {
                intervalAvailableUnits.addAll(
                        context.getPropertyValues().getProperty(AttributeFormPropertyCode.INTERVAL_AVAILABLE_UNITS));
            }

            DateTimeIntervalWidget dtiWidget = (DateTimeIntervalWidget)widget.asWidget();
            dtiWidget.refillIntervalList(intervalAvailableUnits, null, true);
            SingleSelectCellList<Interval> dtiWidgetIntervalList =
                    dtiWidget.getIntervalList();
            if (dtiWidgetIntervalList.getValue() == null || intervalAvailableUnits.isEmpty()
                || context.getPropertyValues().getProperty(AttributeFormPropertyCode.DEFAULT_VALUE) == null)
            {
                if (isFirstLoading)
                {
                    // При первой прорисовке виджета нужно заполнить значение секундами
                    context.getPropertyValues().setProperty(AttributeFormPropertyCode.DEFAULT_VALUE,
                            new DateTimeInterval(Interval.SECOND));
                    dtiWidgetIntervalList.setValue(dtiWidget.getIntervalList().getItem(0));
                    dtiWidget.setValue(new DateTimeInterval(Interval.SECOND));
                }
                else
                {
                    // при дальнейшей активности пользователя на форме - не менять самостоятельно null и [не указано]
                    // на какие-либо допустимые значения
                    context.getPropertyValues().setProperty(AttributeFormPropertyCode.DEFAULT_VALUE, null);
                    dtiWidgetIntervalList.setValue(dtiWidgetIntervalList.getEmptyOption());
                    dtiWidget.setValue(null);
                }
            }
            isFirstLoading = false;
            dtiWidget.setIsDefaultValueWidget(true);
        }
    }

    private void setValidator(PresentationFactoryEdit<Object> eprsFactory, AttributeType attrType)
    {
        Validator<Object> validator = eprsFactory.getValidator(attrType);
        if ((Validator)validator instanceof InputMaskValidator) //NOPMD
        {
            ((InputMaskValidator)(Validator)validator)
                    .setMaskOptions(InputMaskOptions.create(
                            InputMaskMode.getByCode(context.getPropertyValues().<String> getProperty(
                                    AttributeFormPropertyCode.INPUTMASK_MODE)),
                            context.getPropertyValues().<String> getProperty(AttributeFormPropertyCode.INPUTMASK)))
                    .setMessage(messages.inputmaskAttributeValidationMessage());
        }
        else if (validator instanceof NumberInputMaskValidator)
        {
            InputMaskOptions options = new InputMaskOptions();
            options.setRightAlign(false);

            property.<MaskedNumberInputBox<?>> getValueWidget().getWidgetReadyState().ready(
                    new HasReadyState.ReadyCallback(this)
                    {
                        @Override
                        public void onReady()
                        {
                            Alias alias = null;
                            if (IntegerAttributeType.CODE.equals(attrType.getCode()))
                            {
                                options.setAlias(INTEGER_WITH_GROUPS_ALIAS).setRightAlign(false);
                            }
                            else if (ru.naumen.metainfo.shared.Constants.DoubleAttributeType.CODE.equals(attrType
                                    .getCode()))
                            {
                                DoubleAttributeType doubleAttrType = attrType.<DoubleAttributeType> cast();
                                boolean hasGroupSeparators = doubleAttrType.isHasGroupSeparator();
                                Long digitsCountValue = doubleAttrType.getDecimalsCountRestriction();
                                if (hasGroupSeparators || digitsCountValue != null)
                                {
                                    int decimalsCount = digitsCountValue == null ? 0 : digitsCountValue.intValue();
                                    alias = InputMaskUtils.createAlias(hasGroupSeparators, decimalsCount > 0,
                                            decimalsCount);
                                }
                                if (alias != null)
                                {
                                    if (!InputMaskUtils.checkAlias(alias.getAliasName()))
                                    {
                                        InputMaskUtils.extendAliases(alias.getAliasName(), alias);
                                    }
                                    options.setAlias(alias.getAliasName());
                                    options.setHasMask(true);
                                }
                            }
                        }

                    });

            ((NumberInputMaskValidator)validator)
                    .setMaskOptions(options)
                    .setMessage(messages.inputmaskAttributeValidationMessage());
        }
        validationUnits
                .add(context.getValidation().get(AttributeFormValidationCode.DEFAULT).validate(property, validator));
    }

    private void trySetPropertyValue()
    {
        IProperties properties = context.getPropertyValues();
        Object prevDefaultValue = properties.getProperty(AttributeFormPropertyCode.DEFAULT_VALUE);
        Object checkedValue = getPermittedValue(prevDefaultValue, properties);
        try
        {
            if (!(checkedValue instanceof FastSelectionDtObjectTreeValue))
            {
                if (property.getValueWidget() instanceof SingleSelectCellList)
                {
                    checkedValue = ValueToSelectItemConverter.convert(checkedValue);
                }
                else if (property.getValueWidget() instanceof MultiSelectCellList)
                {
                    checkedValue = ValueToSelectItemConverter.convert((Collection)checkedValue);
                }
                property.setValue(checkedValue);
            }
        }
        catch (Exception e)
        {
            if (LOG.isLoggable(Level.FINE))
            {
                LOG.log(Level.FINE, e.getMessage(), e);
            }
        }
        if (!(property.getValueWidget() instanceof FastSelectionDtoValueCellTree))
        {
            context.getPropertyValues().setProperty(AttributeFormPropertyCode.DEFAULT_VALUE,
                    SelectListPropertyValueExtractor.getValue(property));
        }
        else if (checkedValue instanceof Collection && ((Collection)checkedValue).isEmpty())
        {
            context.getPropertyValues()
                    .setProperty(AttributeFormPropertyCode.DEFAULT_VALUE,
                            new FastSelectionDtObjectTreeValue(property.<FastSelectionDtoValueCellTree> getValueWidget()
                                    .getTreeModel().getSelectionModel().getSelectionChangeHistory(),
                                    Constants.TEMP_UUID));
        }
    }

    /**
     * Обновление содержимого типа атрибута, соответствующего числовым значениям
     * @param attrType тип атрибута
     */
    private void updateNumericAttrType(final AttributeType attrType)
    {
        boolean isNumericType = ru.naumen.metainfo.shared.Constants.NUMBER_ATTRIBUTE_TYPES.contains(context
                .getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE));
        if (!isNumericType)
        {
            return;
        }
        Boolean hasGroupSeparators = context.getPropertyValues().getProperty(
                AttributeFormPropertyCode.HAS_GROUP_SEPARATORS, false);
        Object digitsCountValue = context.getPropertyValues().getProperty(
                AttributeFormPropertyCode.DIGITS_COUNT_RESTRICTION, null);
        attrType.setProperty(IntegerAttributeType.HAS_GROUP_SEPARATOR, hasGroupSeparators);

        if (ru.naumen.metainfo.shared.Constants.DoubleAttributeType.CODE.equals(context
                .getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE)))
        {
            // здесь для обновления содержимого типа атрибута используются значения из контекста, поэтому вытаскиваем
            // их напрямую из property
            Long decimalsCountRestriction = null;
            if (digitsCountValue instanceof String && !StringUtilities.isEmpty(((String)digitsCountValue)))
            {
                decimalsCountRestriction = Long.valueOf((String)digitsCountValue);
            }
            else if (digitsCountValue instanceof Long)
            {
                decimalsCountRestriction = (Long)digitsCountValue;
            }

            attrType.setProperty(AttributeTypeProperties.PROP_DECIMALS_COUNT, decimalsCountRestriction);
        }
    }
}
