package ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms;

import java.util.Collection;
import java.util.Map;

import jakarta.inject.Inject;

import java.util.HashMap;

import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.core.client.validation.DateTimeIntervalConditionValueValidator;
import ru.naumen.core.client.validation.IntegerPercentValidator;
import ru.naumen.core.client.validation.NotEmptyObjectValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.DateTimeIntervalProperty;
import ru.naumen.core.client.widgets.properties.IntegerBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinModule.EscalationSchemeLevelPropertyCode;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.delegates.EscalationSchemeLevelActionBindDelegateImpl;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.delegates.EscalationSchemeLevelConditionBindDelegateImpl;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.delegates.EscalationSchemeLevelConditionVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetBindDelegate;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetRefreshDelegate;

/**
 * <AUTHOR>
 * @since 22.08.2012
 *
 */
public class EscalationSchemeLevelFormPropertyControllerFactorySyncImpl<F extends ObjectForm> extends
        PropertyControllerFactorySyncImpl<EscalationSchemeLevel, F>
{
    @Inject
    PropertyControllerSyncFactoryInj<SelectItem, ListBoxProperty> listBoxPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxWithEmptyPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Long, IntegerBoxProperty> integerBoxPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<DateTimeInterval, DateTimeIntervalProperty> dtiPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Collection<SelectItem>, MultiSelectBoxProperty> multiSelectListPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Boolean, BooleanCheckBoxProperty> booleanPropertyFactory;

    @Inject
    EscalationSchemeLevelActionBindDelegateImpl actionBindDelegate;
    @Inject
    SettingsSetBindDelegate settingsSetBindDelegate;
    @Inject
    SettingsSetRefreshDelegate settingsSetRefreshDelegate;
    @Inject
    EscalationSchemeLevelConditionBindDelegateImpl conditionBindDelegate;
    @Inject
    EscalationSchemeLevelConditionVCHDelegateImpl conditionVCHDelegate;

    private final Map<Validator<SelectItem>, String> conditionValidators = new HashMap<>();
    private final Map<Validator<Long>, String> valuePercentValidators = new HashMap<>();
    private final Map<Validator<DateTimeInterval>, String> valueDTIValidators = new HashMap<>();

    @Inject
    public void setUpValidators(IntegerPercentValidator valuePercentValidator,
            DateTimeIntervalConditionValueValidator valueValidator, NotEmptyValidator notEmptyValidator,
            NotEmptyObjectValidator<SelectItem> notEmptySelectItemValidator)
    {
        conditionValidators.put(notEmptySelectItemValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        valuePercentValidators.put(valuePercentValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        valueDTIValidators.put(valueValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }

    @Override
    protected void build()
    {
        //@formatter:off
        register(EscalationSchemeLevelPropertyCode.CONDITION,listBoxPropertyFactory)
            .setVchDelegate(conditionVCHDelegate)
            .setBindDelegate(conditionBindDelegate)
            .setValidators(conditionValidators);
        register(EscalationSchemeLevelPropertyCode.VALUE_PERCENT,integerBoxPropertyFactory)
            .setValidators(valuePercentValidators);
        register(EscalationSchemeLevelPropertyCode.VALUE_DTI,dtiPropertyFactory)
            .setValidators(valueDTIValidators);
        register(EscalationSchemeLevelPropertyCode.ACTION,multiSelectListPropertyFactory)
            .setBindDelegate(actionBindDelegate);
        register(EscalationSchemeLevelPropertyCode.EXEC_ACTION,booleanPropertyFactory);
        register(EscalationSchemeLevelPropertyCode.SETTINGS_SET, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(settingsSetRefreshDelegate)
                .setBindDelegate(settingsSetBindDelegate);
        //@formatter:on
    }
}
