package ru.naumen.metainfoadmin.client.templates.list.card;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TEMPLATES;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.activity.PrevLinkContainer;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.ResourceCallback;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.shared.templates.list.dispatch.GetListTemplateAction;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMessages;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesPlace;

/**
 * Представление карточки шаблона списка в интерфейсе администратора.
 * <AUTHOR>
 * @since 19.04.2018
 */
public class ListTemplatePresenter extends AdminTabPresenter<ListTemplatePlace>
{
    private final DispatchAsync dispatch;
    private final ListTemplateInfoPresenter infoPresenter;
    private final ListTemplateParametersPresenter parametersPresenter;
    private final ListTemplateSettingPresenter settingPresenter;
    private final ListTemplateUsagePresenter usagePresenter;
    private final CommonMessages commonMessages;
    private final ListTemplatesMessages listTemplatesMessages;
    private final PrevLinkContainer prevLinkContainer;

    private String code = null;

    private final OnStartCallback<DtObject> refreshCallback = new SafeOnStartBasicCallback<DtObject>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            infoPresenter.setTemplate(value);
            parametersPresenter.setTemplate(value);
            settingPresenter.setTemplate(value);
        }
    };

    @Inject
    public ListTemplatePresenter(AdminTabDisplay display,
            EventBus eventBus,
            DispatchAsync dispatch,
            ListTemplateInfoPresenter infoPresenter,
            ListTemplateParametersPresenter parametersPresenter,
            ListTemplateSettingPresenter settingPresenter,
            ListTemplateUsagePresenter usagePresenter,
            CommonMessages commonMessages,
            ListTemplatesMessages listTemplatesMessages,
            PrevLinkContainer prevLinkContainer)
    {
        super(display, eventBus);
        this.dispatch = dispatch;
        this.infoPresenter = infoPresenter;
        this.parametersPresenter = parametersPresenter;
        this.settingPresenter = settingPresenter;
        this.usagePresenter = usagePresenter;
        this.commonMessages = commonMessages;
        this.listTemplatesMessages = listTemplatesMessages;
        this.prevLinkContainer = prevLinkContainer;
    }

    @Override
    public void init(ListTemplatePlace place)
    {
        code = place.getCode();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        infoPresenter.refreshDisplay();
        parametersPresenter.refreshDisplay();
        settingPresenter.refreshDisplay();
        usagePresenter.refreshDisplay();
    }

    protected void afterTemplateLoaded(DtObject templateDto)
    {
        Place previousPlace = prevLinkContainer.getPreviousPlace();
        String backLinkCaption = null == previousPlace || previousPlace instanceof ListTemplatesPlace
                ? listTemplatesMessages.backToTemplates()
                : commonMessages.back();
        prevPageLinkPresenter.bind(backLinkCaption, ListTemplatesPlace.INSTANCE);

        getDisplay().setTitle(templateDto.getTitle());

        infoPresenter.setTemplate(templateDto);
        addContent(infoPresenter, "info");

        parametersPresenter.setTemplate(templateDto);
        addContent(parametersPresenter, "params");

        settingPresenter.setTemplate(templateDto);
        addContent(settingPresenter, "setting");

        usagePresenter.setTemplate(templateDto);
        addContent(usagePresenter, "usage");
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        infoPresenter.init(refreshCallback);
        parametersPresenter.init(refreshCallback);

        dispatch.execute(new GetListTemplateAction(code), new ResourceCallback<SimpleResult<DtObject>>(commonMessages)
        {
            @Override
            protected void handleSuccess(SimpleResult<DtObject> value)
            {
                afterTemplateLoaded(value.get());
            }
        });
    }

    @Override
    protected void onUnbind()
    {
        infoPresenter.unbind();
        parametersPresenter.unbind();
        settingPresenter.unbind();
        usagePresenter.unbind();
        super.onUnbind();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return TEMPLATES;
    }
}
