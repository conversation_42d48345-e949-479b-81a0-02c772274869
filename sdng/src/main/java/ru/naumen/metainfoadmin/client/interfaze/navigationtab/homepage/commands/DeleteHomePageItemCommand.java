package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.HomePageCommandParam;
import ru.naumen.metainfoadmin.shared.homepage.DeleteHomePageAction;

/**
 * Команда удаления элемента домашней страницы
 *
 * <AUTHOR>
 * @since 09.01.2023
 */
public class DeleteHomePageItemCommand extends ObjectCommandImpl<HomePageDtObject, DtoContainer<NavigationSettings>>
{
    private final CommonMessages cmessages;
    private final DispatchAsync dispatch;

    @Inject
    public DeleteHomePageItemCommand(
            @Assisted HomePageCommandParam param,
            Dialogs dialogs,
            CommonMessages cmessages,
            DispatchAsync dispatch)
    {
        super(param, dialogs);
        this.cmessages = cmessages;
        this.dispatch = dispatch;
    }

    @Override
    protected String getDialogMessage(HomePageDtObject value)
    {
        return cmessages.deleteHomePage(value.getTitle());
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    protected void onDialogSuccess(CommandParam<HomePageDtObject, DtoContainer<NavigationSettings>> param)
    {
        dispatch.execute(
                new DeleteHomePageAction(param.getValue()),
                new BasicCallback<SimpleResult<DtoContainer<NavigationSettings>>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtoContainer<NavigationSettings>> homePageSettings)
                    {
                        param.getCallback().onSuccess(homePageSettings.get());
                    }
                });
    }
}