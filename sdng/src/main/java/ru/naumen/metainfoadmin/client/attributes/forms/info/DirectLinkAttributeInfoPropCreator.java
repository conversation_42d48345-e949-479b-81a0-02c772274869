package ru.naumen.metainfoadmin.client.attributes.forms.info;

import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.IHyperlink;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Создает {@link Property} для отображения информации о прямой ссылке 
 * на модальной форме свойств атрибута  
 *
 * <AUTHOR>
 * @since 1 авг. 2018 г.
 */
public class DirectLinkAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    protected void createInt(String code)
    {
        String fqnAsString = attribute.getType().getProperty(ObjectAttributeType.METACLASS_FQN);
        metainfoService.getMetaClass(ClassFqn.parse(fqnAsString), new BasicCallback<MetaClass>(rs)
        {
            @Override
            protected void handleSuccess(MetaClass metaClass)
            {
                String attrCode = attribute.getType().getProperty(BackLinkAttributeType.BACK_ATTR_CODE);
                createProperty(code, createHyperLink(metaClass.getTitle(), metaClass.getAttribute(attrCode)
                        .getTitle(), fqnAsString));
            }
        });
    }

    private String createHyperLink(String classTitle, String attrTitle, String code)
    {
        String url = "#" + MetaClassPlace.PLACE_PREFIX + ":" + code;
        IHyperlink link = new Hyperlink(classTitle + "/" + attrTitle, url);
        return link.toString();
    }
}
