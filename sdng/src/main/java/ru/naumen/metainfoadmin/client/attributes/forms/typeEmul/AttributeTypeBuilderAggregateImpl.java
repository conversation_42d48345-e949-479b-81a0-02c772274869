package ru.naumen.metainfoadmin.client.attributes.forms.typeEmul;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.inject.Singleton;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeDescription;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.AttrWidgetsHelper;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 21.05.2012
 */
@Singleton
public class AttributeTypeBuilderAggregateImpl implements AttributeTypeBuilder
{
    @Override
    public void build(AttributeType type, IProperties propertyValues)
    {
        Collection<MetaClassLite> aggregateClasses = propertyValues
                .getProperty(AttributeFormPropertyCode.AGGREGATE_CLASSES);
        if (aggregateClasses == null)
        {
            //При редактировании атрибута
            return;
        }
        Collection<ClassFqn> aggregateClassesFqn = Collections2.transform(aggregateClasses,
                MetaClassLite.FQN_EXTRACTOR);
        List<AttributeDescription> attrDescs = getAttributeDescriptions("", aggregateClassesFqn);
        type.setProperty(AggregateAttributeType.PREPARED_ATTRIBUTES, attrDescs);
        type.setProperty(AggregateAttributeType.AGGREGATE_CLASSES, Lists.newArrayList(aggregateClassesFqn));

        AttrWidgetsHelper.setPermittedTypes(type, propertyValues);
    }

    public List<AttributeDescription> getAttributeDescriptions(String code, Collection<ClassFqn> aggregateClasses)
    {
        List<String> employeeParents = getEmployeeParents(aggregateClasses);
        if (employeeParents.isEmpty())
        {
            return null;
        }
        List<AttributeDescription> attributes = new ArrayList<>();
        attributes.add(new AttributeDescription(code + AggregateAttributeType.EMPLOYEE_POSTFIX, employeeParents,
                Employee.FQN));
        if (aggregateClasses.contains(OU.FQN))
        {
            attributes.add(new AttributeDescription(code + AggregateAttributeType.OU_POSTFIX,
                    new ArrayList<>(), OU.FQN));
        }
        if (aggregateClasses.contains(Team.FQN))
        {
            attributes.add(new AttributeDescription(code + AggregateAttributeType.TEAM_POSTFIX,
                    new ArrayList<>(), Team.FQN));
        }
        return attributes;
    }

    @Override
    public void invert(AttributeType type, IProperties propertyValues)
    {
    }

    private List<String> getEmployeeParents(Collection<ClassFqn> aggregateClasses)
    {
        List<String> emplParents = new ArrayList<>();
        if (aggregateClasses.contains(OU.FQN))
        {
            emplParents.add(Constants.PARENT_ATTR);
        }
        if (aggregateClasses.contains(Team.FQN))
        {
            emplParents.add(Constants.Employee.TEAMS);
        }
        return emplParents;
    }
}
