package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import java.util.Optional;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.SwitchQuickAccessTileAction;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelAreaSettingsDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelElementWrapper;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;

/**
 * Команда выключения плитки быстрого доступа
 *
 * <AUTHOR>
 * @since 17.07.2020
 */
public class DisableQuickAccessTileCommand extends BaseCommandImpl<QuickAccessPanelElementWrapper,
        DtoContainer<NavigationSettings>>
{
    public static final String ID = "disableQuickTileCommand";

    @Inject
    private DispatchAsync dispatch;

    @Inject
    public DisableQuickAccessTileCommand(@Assisted QuickAccessPanelTileCommandParam param)
    {
        super(param);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.SWITCH_OFF;
    }

    @Override
    public void execute(CommandParam<QuickAccessPanelElementWrapper, DtoContainer<NavigationSettings>> cparam)
    {
        QuickAccessPanelTileCommandParam p = (QuickAccessPanelTileCommandParam)prepareParam(cparam);
        QuickAccessTileDTO value = (QuickAccessTileDTO)p.getValue().getWrappable();

        Optional<QuickAccessPanelAreaSettingsDTO> areaOptional = p.getSettings().get().getQuickAccessPanelSettings()
                .getAreas()
                .stream().filter(area -> area.getTiles().contains(value)).findAny();

        SwitchQuickAccessTileAction action = new SwitchQuickAccessTileAction();
        action.setEnabled(false);
        action.setTile(value);
        action.setAreaCode(areaOptional.map(QuickAccessPanelAreaSettingsDTO::getCode)
                .orElse(Constants.QuickAccessPanelSettings.ADMIN_AREA_CODE));
        dispatch.execute(action, new SimpleResultCallbackDecorator<>(param.getCallback()));
    }

    @Override
    public boolean isPossible(Object input)
    {
        return isTileEnabled(input);
    }

    protected static boolean isTileEnabled(Object input)
    {
        if (!(input instanceof QuickAccessPanelElementWrapper))
        {
            return false;
        }
        QuickAccessPanelElementWrapper item = (QuickAccessPanelElementWrapper)input;
        if (!(item.getWrappable() instanceof QuickAccessTileDTO))
        {
            return false;
        }
        return item.isEnabled();
    }
}