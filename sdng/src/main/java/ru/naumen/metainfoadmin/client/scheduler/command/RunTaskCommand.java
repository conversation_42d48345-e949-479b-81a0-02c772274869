package ru.naumen.metainfoadmin.client.scheduler.command;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.mailreader.client.InboundMailClientService;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;
import ru.naumen.mailreader.shared.task.ReceiveMailTask;
import ru.naumen.metainfo.shared.dispatch2.scheduler.RunTaskAction;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskMessages;

/**
 * Команда "Выполнить сейчас" для задач планровщика
 * <AUTHOR>
 */
public class RunTaskCommand extends ObjectCommandImpl<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>>
{
    @Inject
    private I18nUtil i18nUtil;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    protected SchedulerTaskMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    private InboundMailClientService inboundMailClientService;

    /** Сообщение для диалога, которое имеет более высокий приоритет, чем то сообщение, что сформируется в
     * {@link RunTaskCommand#getDialogMessage}
     */
    private String generatedMessage = null;

    @Inject
    public RunTaskCommand(@Assisted CommandParam<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>> param,
            Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(DtoContainer<SchedulerTask> schTask)
    {
        if (generatedMessage != null)
        {
            return generatedMessage;
        }

        return hasSwichedOnTriggers(schTask.get())
                ? messages.confirmRunQuestion(i18nUtil.getLocalizedTitle(schTask.get()))
                : messages.confirmRunQuestion2(i18nUtil.getLocalizedTitle(schTask.get()));
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmRun();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.RUN;
    }

    @Override
    protected void onDialogSuccess(final CommandParam<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>> param)
    {
        final String taskCode = param.getValue().get().getCode();
        dispatch.execute(new RunTaskAction(taskCode),
                new SimpleResultCallbackDecorator<>(new BasicCallback<DtoContainer<SchedulerTask>>()
                {
                    @Override
                    protected void handleSuccess(DtoContainer<SchedulerTask> value)
                    {
                        param.getCallback().onSuccess(value);
                    }

                    @Override
                    public void onFailure(Throwable t)
                    {
                        // Просто пробросим ошибку на карточку задачи SchedulerTaskInfoPresenterBase, для дальнейшей
                        // обработки и разблокировки кнопки "Выполнить сейчас" в логике колбека по обработке ошибки
                        param.getCallback().onFailure(t);
                    }
                }));
    }

    @Override
    protected void question(CommandParam<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>> preparedParam,
            DialogCallback callback)
    {
        //отдельно проверить задачу обработки входящей почты
        SchedulerTask schedulerTask = preparedParam.getValue().get();
        if (schedulerTask instanceof ReceiveMailTask receiveMailTask)
        {
            inboundMailClientService.getTaskAndInboundMailServerConfig(
                    receiveMailTask.getCode(),
                    new BasicCallback<Pair<ReceiveMailTask, InboundMailServerConfig>>()
                    {
                        @Override
                        public void onSuccess(
                                Pair<ReceiveMailTask, InboundMailServerConfig> taskAndInboundMailServerConfig)
                        {
                            getDialogMessageForReceiveMailTask(
                                    taskAndInboundMailServerConfig.right,
                                    preparedParam,
                                    callback);
                        }
                    }
            );
        }
        else
        {
            super.question(preparedParam, callback);
        }
    }

    /**
     * Сформировать диалоговое сообщение для планировщика задач по обработке входящей почты.
     * @param config параметры сервера входящей почты из планировщика задач.
     * @param preparedParam параметры команды.
     * @param callback обратный вызов для диалогового окна.
     */
    private void getDialogMessageForReceiveMailTask(
            InboundMailServerConfig config,
            CommandParam<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>> preparedParam,
            DialogCallback callback)
    {
        generatedMessage = inboundMailClientService.getDialogMessageForReceiveMailTask(config);
        super.question(preparedParam, callback);
    }

    /**
     * Признак того, что у задачи планировщика есть включенные правила
     */
    private boolean hasSwichedOnTriggers(SchedulerTask schTask)
    {
        for (Trigger t : schTask.getTrigger())
        {
            if (t.isEnabled())
            {
                return true;
            }
        }
        return false;
    }
}