package ru.naumen.metainfoadmin.client.templates.list.form;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.RadioButtonGroupProperty;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ListTemplate;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.AttributeGroupInfo;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosAction;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosResponse;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.templates.list.dispatch.EditListListTemplateAction;
import ru.naumen.metainfo.shared.ui.AdvlistSettingsDefault;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.PagerPosition;
import ru.naumen.metainfo.shared.ui.Position;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentFormHelper;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.EditObjectListBaseContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseContentCreator;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMessages;

/**
 * Форма редактирования параметров шаблона списка
 * {@link EditObjectListBaseContentPresenter} и {@link ObjectListBaseContentCreator}  частично совпадает
 * TODO: aborisov провести рефакторинг. 
 * <AUTHOR>
 * @since 23.04.2018
 *
 */
public class EditParametersListTemplateFormPresenter extends OkCancelPresenter<PropertyDialogDisplay>
        implements CallbackPresenter<DtObject, DtObject>
{
    @Inject
    private CommonMessages cmessages;

    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private ContentFormHelper helper;
    @Inject
    private ListTemplatesMessages messages;
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> attributeGroupList;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> presentationList;
    private PropertyRegistration<SelectItem> prPresentationList;
    @Inject
    private RadioButtonGroupProperty pagingPosition;
    private PropertyRegistration<String> prPagingPosition;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> position;

    private AsyncCallback<DtObject> refreshCallback;

    private DtObject listTemplate;
    private ObjectListBase list;
    /*
     * Название, код и коды атрибутов для групп атрибутов доступных типов и класса. Загружаются при заполнении caseList
     */
    private Map<ClassFqn, ? extends List<AttributeGroupInfo>> attrGroups = new HashMap<>();

    /**
     * Доступные типы. Заполняются при выборе класса.
     */
    private List<ClassFqn> cases = new ArrayList<>();

    @Inject
    public EditParametersListTemplateFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(DtObject listTemplate, AsyncCallback<DtObject> refreshCallback)
    {
        this.listTemplate = listTemplate;
        this.refreshCallback = refreshCallback;
        list = listTemplate.getProperty(FakeMetaClassesConstants.ListTemplate.TEMPLATE);
    }

    @Override
    public void onApply()
    {
        if (!ObjectUtils.equals(SelectListPropertyValueExtractor.getValue(attributeGroupList), list
                .getAttributeGroup()))
        {
            list.setDefaultSettings(new AdvlistSettingsDefault());
        }
        list.setAttributeGroup(SelectListPropertyValueExtractor.getValue(attributeGroupList));
        list.setPresentation(SelectListPropertyValueExtractor.getValue(presentationList));
        if (PresentationType.ADVLIST.getCode().equals(list.getPresentation()))
        {
            list.getPagingSettings().setPosition(PagerPosition.fromValue(pagingPosition.getValue()));
        }
        else
        {
            list.setPagingSettings(null);
        }
        list.setPosition(Position.valueOf(SelectListPropertyValueExtractor.getValue(position)));

        dispatch.execute(new EditListListTemplateAction(listTemplate.getUUID(), list),
                new BasicCallback<SimpleResult<DtObject>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtObject> value)
                    {
                        unbind();
                        refreshCallback.onSuccess(value.get());
                    }
                });
    }

    @Override
    public void refreshDisplay()
    {
    }

    protected void bindProperties()
    {
        attributeGroupList.setCaption(cmessages.attributeGroup());
        getDisplay().add(attributeGroupList);

        presentationList.setCaption(cmessages.presentation());
        prPresentationList = getDisplay().add(presentationList);

        helper.initPagingLocation(pagingPosition);
        pagingPosition.setValidationMarker(false);

        helper.initPositionProperty(position);
        position.setValidationMarker(false);
        position.trySetObjValue(list.getPosition().name());
        getDisplay().add(position);

        ensureDebugIds();
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.editingParametersTemplate());
        bindProperties();

        initAttrGroupList();
        initPresentationList();
        refreshPagingLocation(list.getPresentation());
        display.display();
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(attributeGroupList, "attributeGroup");
        DebugIdBuilder.ensureDebugId(presentationList, "presentationList");
    }

    private void fillAttrGroupList(Map<String, String> visibleGroups)
    {
        List<String> codes = visibleGroups.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByValue())
                .map(Entry::getKey)
                .collect(Collectors.toList());
        for (String code : codes)
        {
            attributeGroupList.<SingleSelectCellList<?>> getValueWidget().addItem(visibleGroups.get(code), code);
        }
        attributeGroupList.trySetObjValue(list.getAttributeGroup());
    }

    private void getAttributeGroups(Collection<ClassFqn> fqns)
    {
        GetAttributeGroupInfosAction action = new GetAttributeGroupInfosAction(fqns);
        dispatch.execute(action, new BasicCallback<GetAttributeGroupInfosResponse>()
        {
            @Override
            protected void handleSuccess(GetAttributeGroupInfosResponse response)
            {
                attrGroups = response.getGroupInfos();
                Collection<MetaClassLite> classValueCol = listTemplate.getProperty(ListTemplate.CLASS);
                showAttributeGroups(classValueCol.iterator().next().getFqn(),
                        listTemplate.getProperty(FakeMetaClassesConstants.ListTemplate.CASE));
            }
        });
    }

    private Collection<ClassFqn> getContentFqnList(ClassFqn classFqn, Collection<ClassFqn> casesFqn)
    {
        if (casesFqn.isEmpty())
        {
            return classFqn.isClass() ? Lists.newArrayList(classFqn) : cases;
        }
        return casesFqn;
    }

    private void initAttrGroupList()
    {
        Collection<MetaClassLite> classValueCol = listTemplate.getProperty(ListTemplate.CLASS);
        ClassFqn fqn = classValueCol.iterator().next().getFqn();
        metainfoService.getDescendantClasses(fqn, fqn.isCase(), new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> casesList)
            {
                metainfoUtils.sort(casesList);
                cases = new ArrayList<>();
                for (MetaClassLite clz : metainfoUtils.getPossible(casesList, true))
                {
                    cases.add(clz.getFqn());
                }
                Set<ClassFqn> fqns = Sets.newHashSet(Collections2.transform(casesList, MetaClassLite.FQN_EXTRACTOR));
                fqns.add(fqn.fqnOfClass());
                getAttributeGroups(fqns);
            }
        });
    }

    private void initPresentationList()
    {
        SingleSelectCellList<?> presentationField = presentationList.getValueWidget();
        presentationField.addItem(cmessages.objectListDefault(), PresentationType.DEFAULT.getCode());
        presentationField.addItem(cmessages.objectListAdvlist(), PresentationType.ADVLIST.getCode());
        presentationList.trySetObjValue(list.getPresentation());

        presentationList.addValueChangeHandler(
                event -> refreshPagingLocation(SelectItemValueExtractor.extract(event.getValue())));
    }

    /**
     * Обновление свойств, зависящих от списка представлений
     */
    private void refreshPagingLocation(String value)
    {
        if (PresentationType.ADVLIST.getCode().equals(value) && prPagingPosition == null)
        {
            if (list.getPagingSettings() != null)
            {
                pagingPosition.setValue(list.getPagingSettings().getPosition().value());
            }

            prPagingPosition = display.addPropertyAfter(pagingPosition, prPresentationList);
        }
        else if (PresentationType.DEFAULT.getCode().equals(value) && prPagingPosition != null)
        {
            prPagingPosition.unregister();
            prPagingPosition = null;
        }
    }

    private void showAttributeGroups(ClassFqn classFqn, Collection<ClassFqn> casesFqn)
    {
        attributeGroupList.<SingleSelectCellList<?>> getValueWidget().clear();
        if (attrGroups.isEmpty())
        {
            return;
        }
        Collection<ClassFqn> fqns = getContentFqnList(classFqn, casesFqn);
        Map<String, String> visibleGroups = ObjectListBaseContentCreator.getVisibleAttrGroups(attrGroups, fqns, false);
        fillAttrGroupList(visibleGroups);
    }
}
