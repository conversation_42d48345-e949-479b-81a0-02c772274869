package ru.naumen.metainfoadmin.client.dynadmin;

import jakarta.inject.Singleton;

import com.google.inject.TypeLiteral;

import ru.naumen.core.client.content.ContextDecoratorCreator;
import ru.naumen.core.client.content.factory.ContentFactory;
import ru.naumen.core.client.mvp.AbstractPresenterModule;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.common.content.ContentGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.content.DynadminContentsGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.content.factory.AdminContentFactoryImpl;
import ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.HierarchyGridGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListAdminGinModule;

/**
 * Модуль конфигурации настройки внешнего вида карточки объекта в интерфейсе настройки (технолога)
 *
 * <AUTHOR>
 * @since 28.07.2010
 *
 */
public class DynadminGinModule extends AbstractPresenterModule
{
    /**
     * Упорядочиваем строки по алфавиту
     */
    @Override
    protected void configure()
    {
        install(new ContentGinModule());
        install(new DynadminContentsGinModule());
        install(new ObjectListAdminGinModule());
        install(new HierarchyGridGinModule());

        //@formatter:off
        bind(new TypeLiteral<ContentFactory<UIContext>>(){}).to(AdminContentFactory.class).in(Singleton.class);
        bind(AdminContentFactory.class).to(AdminContentFactoryImpl.class).in(Singleton.class);
        bind(ContextDecoratorCreator.class).to(AdminContextDecoratorCreator.class).in(Singleton.class);
        //@formatter:on
    }
}
