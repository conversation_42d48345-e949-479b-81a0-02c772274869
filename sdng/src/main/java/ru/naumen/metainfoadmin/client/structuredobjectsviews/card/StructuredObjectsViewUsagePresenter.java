package ru.naumen.metainfoadmin.client.structuredobjectsviews.card;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.shared.SafeHtml;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.StructuredObjectsView;
import ru.naumen.metainfo.shared.structuredobjectsviews.usage.ComplexStructuredObjectsViewInAttributeCustomFormUsagePoint;
import ru.naumen.metainfo.shared.structuredobjectsviews.usage.ComplexStructuredObjectsViewInAttributeUsagePoint;
import ru.naumen.metainfo.shared.structuredobjectsviews.usage.ContentStructuredObjectsViewUsagePoint;
import ru.naumen.metainfo.shared.structuredobjectsviews.usage.ViewForEditingAttributeInCustomFormStructuredObjectsViewUsagePoint;
import ru.naumen.metainfo.shared.structuredobjectsviews.usage.ViewForEditingAttributeStructuredObjectsViewUsagePoint;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.formatters.StructuredObjectsViewUsagePointFormatterRegistry;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.client.tags.formatters.UsagePointFormatter;

/**
 * Блок "Места использования" на карточке структуры.
 * <AUTHOR>
 * @since 21.01.2020
 */
public class StructuredObjectsViewUsagePresenter extends BasicPresenter<InfoDisplay>
{
    private final StructuredObjectsViewUsagePointFormatterRegistry formatters;
    private final TagsMessages messages;
    private final CommonMessages cmessages;
    private final Property<String> attributes;
    private final Property<String> attributesInCustomForm;
    private final Property<String> contents;
    private final Property<String> viewForEditingAttributes;
    private final Property<String> viewForEditingParametersOfUserEventActions;

    private DtObject structuredObjectsView;
    private final Map<String, Property<String>> properties = new HashMap<>();

    @Inject
    public StructuredObjectsViewUsagePresenter(InfoDisplay display,
            EventBus eventBus,
            StructuredObjectsViewUsagePointFormatterRegistry formatters,
            TagsMessages messages,
            CommonMessages cmessages,
            @Named(PropertiesGinModule.HTML_TEXT) Property<String> attributes,
            @Named(PropertiesGinModule.HTML_TEXT) Property<String> attributesInCustomForm,
            @Named(PropertiesGinModule.HTML_TEXT) Property<String> contents,
            @Named(PropertiesGinModule.HTML_TEXT) Property<String> viewForEditingAttributes,
            @Named(PropertiesGinModule.HTML_TEXT) Property<String> viewForEditingParametersOfUserEventActions)
    {
        super(display, eventBus);
        this.formatters = formatters;
        this.messages = messages;
        this.cmessages = cmessages;
        this.attributes = attributes;
        this.attributesInCustomForm = attributesInCustomForm;
        this.contents = contents;
        this.viewForEditingAttributes = viewForEditingAttributes;
        this.viewForEditingParametersOfUserEventActions = viewForEditingParametersOfUserEventActions;
    }

    public void setStructuredObjectsView(DtObject structuredObjectsView)
    {
        this.structuredObjectsView = structuredObjectsView;
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        if (null != structuredObjectsView)
        {
            updateValues();
        }
        else
        {
            clearValues();
        }
    }

    protected void bindProperties()
    {
        attributes.setCaption(cmessages.complexRelationInAttributes());
        getDisplay().add(attributes);
        attributesInCustomForm.setCaption(cmessages.complexRelationInParameters());
        getDisplay().add(attributesInCustomForm);
        contents.setCaption(messages.contents());
        getDisplay().add(contents);
        viewForEditingAttributes.setCaption(cmessages.viewForEditingAttributes());
        getDisplay().add(viewForEditingAttributes);
        viewForEditingParametersOfUserEventActions.setCaption(cmessages.viewForEditingAttributesInCustomForm());
        getDisplay().add(viewForEditingParametersOfUserEventActions);
    }

    protected void clearValues()
    {
        for (Property<String> property : properties.values())
        {
            property.setValue(StringUtilities.EMPTY);
        }
    }

    protected void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(contents, "contents");
        DebugIdBuilder.ensureDebugId(attributes, "attributes");
        DebugIdBuilder.ensureDebugId(attributesInCustomForm, "attributesInCustomForm");
        DebugIdBuilder.ensureDebugId(viewForEditingAttributes, "viewForEditingAttributes");
        DebugIdBuilder.ensureDebugId(viewForEditingParametersOfUserEventActions,
                "viewForEditingParametersOfUserEventActions");
    }

    @Inject
    protected void initProperties()
    {
        properties.put(ContentStructuredObjectsViewUsagePoint.class.getSimpleName(), contents);
        properties.put(ComplexStructuredObjectsViewInAttributeUsagePoint.class.getSimpleName(), attributes);
        properties.put(ComplexStructuredObjectsViewInAttributeCustomFormUsagePoint.class.getSimpleName(),
                attributesInCustomForm);
        properties.put(ViewForEditingAttributeStructuredObjectsViewUsagePoint.class.getSimpleName(),
                viewForEditingAttributes);
        properties.put(ViewForEditingAttributeInCustomFormStructuredObjectsViewUsagePoint.class.getSimpleName(),
                viewForEditingParametersOfUserEventActions);
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaption(cmessages.usagePlaces());
        bindProperties();
        ensureDebugIds();
    }

    protected void updateValues()
    {
        Map<String, List<SimpleDtObject>> usagePointsMap = structuredObjectsView.getProperty(
                StructuredObjectsView.USAGE_POINTS);
        for (Map.Entry<String, Property<String>> entry : properties.entrySet())
        {
            List<SimpleDtObject> usagePoints = usagePointsMap.get(entry.getKey());
            UsagePointFormatter formatter = formatters.getFormatter(entry.getKey());
            if (formatter != null && CollectionUtils.isNotEmpty(usagePoints))
            {
                List<String> formattedLines = usagePoints.stream()
                        .map(formatter::format)
                        .map(SafeHtml::asString)
                        .collect(Collectors.toList());
                entry.getValue().setValue(StringUtilities.join(formattedLines, "<br>\n"));
            }
            else
            {
                entry.getValue().setValue(StringUtilities.EMPTY);
            }
        }
    }
}
