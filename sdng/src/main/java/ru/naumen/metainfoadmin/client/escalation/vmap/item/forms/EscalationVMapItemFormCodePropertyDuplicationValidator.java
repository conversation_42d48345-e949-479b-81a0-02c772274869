package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import ru.naumen.core.client.validation.AttributeDuplicationValidator;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Валидатор кода таблиц соответствий эскалации
 *
 * <AUTHOR>
 * @since 27.02.2017
 */
public class EscalationVMapItemFormCodePropertyDuplicationValidator<T> extends AttributeDuplicationValidator<T>
{
    public EscalationVMapItemFormCodePropertyDuplicationValidator<T> init(ClassFqn fqn, String message)
    {
        this.classFqn = fqn;
        this.message = message;
        return this;
    }

    @Override
    protected DtoCriteria createCriteria(HasValueOrThrow<T> hasValue)
    {
        DtoCriteria criteria = new DtoCriteria(getClassFqn());
        IObjectFilter escalationTypeFilter = Filters.eq(ValueMapCatalogItem.TYPE, ValueMapCatalogItem.ESCALATION_TYPE);
        return criteria
                .addFilters(Filters.and(Filters.eq(CatalogItem.ITEM_CODE, hasValue.getValue()), escalationTypeFilter));
    }
}
