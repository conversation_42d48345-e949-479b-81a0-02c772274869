package ru.naumen.metainfoadmin.client.eventaction.form.creator;

import static com.google.gwt.safehtml.shared.SafeHtmlUtils.*;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.FormPropertiesCreator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.widgets.script.component.ScriptComponentEditWidget;
import ru.naumen.core.shared.script.places.EventActionCategories;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.eventaction.ActionConditionType;
import ru.naumen.metainfo.shared.eventaction.ActionConditionWithScript;
import ru.naumen.metainfo.shared.eventaction.ScriptActionCondition;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.eventaction.ActionConditionCreator;

import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 *
 */
public class ScriptActionConditionCreator extends FormPropertiesCreator implements ActionConditionCreator
{
    @Inject
    Processor validation;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    @Named(PropertiesGinModule.SCRIPT_COMPONENT_EDIT)
    private Property<ScriptDto> script;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    Property<String> name;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    public Property<Boolean> syncVerification;
    @Inject
    @Named(PropertiesGinModule.SCRIPT_COMPONENT_VIEW)
    private Property<ScriptDto> scriptText;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Property<String> nameText;
    Property<SelectItem> settingsSet;
    @Inject
    EventActionMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    private AdminDialogMessages adminDialogMessages;
    @CheckForNull
    ActionConditionWithScript condition;
    @Inject
    SettingsSetOnFormCreator settingsSetOnFormCreator;

    @Inject
    public ScriptActionConditionCreator(@Nullable @Assisted ActionConditionWithScript condition)
    {
        this.condition = condition;
    }

    @Override
    public void addInfoProperties(InfoDisplay display)
    {
        nameText.setCaption(cmessages.title());
        nameText.ensureDebugId("nameText");
        display.add(nameText);

        syncVerification.setCaption(cmessages.syncVerification());
        syncVerification.ensureDebugId("sync-verification");
        display.add(syncVerification);

        scriptText.setCaption(cmessages.script());
        scriptText.ensureDebugId("script-value");
        display.add(scriptText);
    }

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {
        add(validation.validate(name, notEmptyValidator));
        ScriptComponentEditWidget scriptWidget = script.getValueWidget();
        scriptWidget.enableValidation();
        scriptWidget.setParentDisplay(display);

        syncVerification.addValueChangeHandler(event -> display.addAttentionMessage(
                event.getValue() ? messages.syncVerificationHint() : EMPTY_SAFE_HTML));
    }

    @Override
    public void bindProperties()
    {
        addWithMarker(cmessages.title(), name);
        addWithMarker(cmessages.syncVerification(), syncVerification);
        addWithMarker(cmessages.script(), script);

        name.setMaxLength(Constants.StringAttributeType.MAX_LENGTH_DEFAULT);
        name.ensureDebugId("name");

        syncVerification.ensureDebugId("sync-verification");
        script.ensureDebugId("edit-script");

        ScriptComponentEditWidget scriptWidget = script.getValueWidget();
        scriptWidget.init(true, EventActionCategories.EVENTACTION_CONDITION, null);
        scriptWidget.initValidation(validation);
        String settingsSetValue = condition == null ? null : condition.getObject().getSettingsSet();
        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(settingsSetValue);
        if (settingsSet != null)
        {
            add(adminDialogMessages.settingsSet(), settingsSet);
        }

        if (null != condition)
        {
            String scriptCode = ((ScriptActionCondition)condition.getObject()).getScript();
            ScriptDto scriptDto = ScriptDtoFactory.createNeedLoad(scriptCode);
            if (!condition.getScripts().isEmpty())
            {
                scriptDto = condition.getScript(scriptCode);
            }
            script.setValue(scriptDto);
            name.setValue(condition.getTitle());
            syncVerification.setValue(condition.isSyncVerification());
        }
    }

    @Override
    public ActionConditionWithScript getCondition()
    {
        ActionConditionWithScript c = condition == null ? new ActionConditionWithScript(new ScriptActionCondition())
                : condition;
        c.getObject().setType(ActionConditionType.SCRIPT);
        c.putScript(script.getValue());
        c.getObject().setTitle(name.getValue());
        c.getObject().setSyncVerification(syncVerification.getValue());
        c.getObject().setSettingsSet(SelectListPropertyValueExtractor.getValue(settingsSet));
        ((ScriptActionCondition)c.getObject()).setScript(script.getValue().getCode());
        return c;
    }

    @Override
    public MapProperties getConditionProperties()
    {
        if (!validation.validate())
        {
            return null;
        }
        MapProperties props = new MapProperties();
        props.setProperty("script-value", script.getValue());
        props.setProperty("name", name.getValue());
        props.setProperty("sync-verification", syncVerification.getValue());
        props.setProperty("settingsSet", SelectListPropertyValueExtractor.getValue(settingsSet));
        return props;
    }

    @Override
    public void init(ActionConditionWithScript obj)
    {
        this.condition = obj;
    }

    @Override
    public void setInfoPropertiesValues()
    {
        if (null != condition)
        {
            scriptText.setValue(condition.getScript(((ScriptActionCondition)condition.getObject()).getScript()));
            nameText.setValue((condition).getTitle());
            syncVerification.setValue(condition.isSyncVerification());
        }
    }
}
