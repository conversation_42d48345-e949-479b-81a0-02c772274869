package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.AttributeGroupInfo;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosAction;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosResponse;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseContentCreator;

/**
 * Делегат обновления свойства "Группа атрибутов" на форме добавления элемента левого меню типа "Ссылка на контент"
 *
 * <AUTHOR>
 * @since 20.10.2020
 */
@Singleton
public class LinkToContentAttributeGroupRefreshDelegateImpl extends LinkToContentSelectRefreshDelegateImpl
{
    @Inject
    DispatchAsync dispatch;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean isLinkToContentType =
                LinkToContentMetaClassPropertiesProcessor.isLinkToContentElementType(context);

        String metaClassStr = LinkToContentMetaClassPropertiesProcessor.getMetaClassString(context.getPropertyValues());
        ClassFqn fqn = StringUtilities.isNotEmpty(metaClassStr) ? ClassFqn.parse(metaClassStr) : null;

        Collection<String> metaCasesStr = context.getPropertyValues()
                .getProperty(MenuItemLinkToContentCode.OBJECT_CASES);

        if (!isLinkToContentType || (fqn == null))
        {
            callback.onSuccess(false);
            return;
        }

        List<ClassFqn> fqns = getContentFqnList(fqn, metaCasesStr);

        property.getValueWidget().clear();
        property.getValueWidget().refreshPopupCellList();

        String attrGroup = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.ATTRIBUTE_GROUP);

        property.clearValue();
        property.getValueWidget().clear();

        GetAttributeGroupInfosAction action = new GetAttributeGroupInfosAction(fqns);
        dispatch.execute(action, new BasicCallback<GetAttributeGroupInfosResponse>()
        {
            @Override
            protected void handleSuccess(GetAttributeGroupInfosResponse response)
            {
                Map<ClassFqn, ? extends List<AttributeGroupInfo>> attrGroups = response.getGroupInfos();
                if (attrGroups.isEmpty())
                {
                    return;
                }

                Map<String, String> visibleGroups = ObjectListBaseContentCreator.getVisibleAttrGroups(attrGroups,
                        fqns, false);
                List<String> codes = visibleGroups.entrySet()
                        .stream()
                        .sorted(Map.Entry.comparingByValue())
                        .map(Entry::getKey)
                        .collect(Collectors.toList());

                Set<String> possibleGroups = new HashSet<>();
                for (String code : codes)
                {
                    property.<SingleSelectCellList<?>> getValueWidget().addItem(visibleGroups.get(code), code);
                    possibleGroups.add(code);
                }
                if (StringUtilities.isNotEmpty(attrGroup) && possibleGroups.contains(attrGroup))
                {
                    property.trySetObjValue(attrGroup);
                    context.getPropertyValues().setProperty(MenuItemLinkToContentCode.ATTRIBUTE_GROUP, attrGroup);
                }
                else
                {
                    String firstItem = codes.get(0);
                    property.trySetObjValue(firstItem);
                    context.getPropertyValues().setProperty(MenuItemLinkToContentCode.ATTRIBUTE_GROUP, firstItem);
                }
                callback.onSuccess(true);
            }
        });
    }

    private static List<ClassFqn> getContentFqnList(ClassFqn classFqn, @Nullable Collection<String> caseIds)
    {
        if (caseIds == null || caseIds.isEmpty())
        {
            return classFqn.isClass() ? Lists.newArrayList(classFqn) : new ArrayList<>();
        }
        return CollectionUtils.transform(caseIds, ClassFqn.FROM_STRING_CONVERTER,
                Lists.newArrayListWithCapacity(caseIds.size()));
    }
}
