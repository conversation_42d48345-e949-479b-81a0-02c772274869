package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import jakarta.inject.Inject;

import java.util.HashMap;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.metainfo.shared.elements.HasDateTimeRestriction.RestrictionType;

/**
 * Предоставляет типы ограничений даты/времени доступные для выбора на форме добавления/редактирования атрибутов
 * <AUTHOR>
 * @since 4 окт. 2018 г.
 *
 */
public class AvailableRestrictionTypesProviderImpl<F extends ObjectForm> implements AvailableRestrictionTypesProvider<F>
{
    //<код типа атрибута, название типа атрибута>
    private Map<String, String> types = new HashMap<>();

    @Inject
    public AvailableRestrictionTypesProviderImpl(DateTimeRestrictionMessages messages)
    {
        types.put(RestrictionType.NO_RESTRICTION.name(), messages.defaultTitle());
        types.put(RestrictionType.RESTRICTION_BY_SCRIPT.name(), messages.byScriptTitle());
        types.put(RestrictionType.ATTRIBUTE_RESTRICTION.name(), messages.byAttributeTitle());
    }

    @Override
    public List<Entry<String, String>> getTypes()
    {
        return CollectionUtils.asRevertedMapSortedEntries(types);
    }

    @Override
    public String getTypeTitle(String code)
    {
        return types.get(code);
    }

}
