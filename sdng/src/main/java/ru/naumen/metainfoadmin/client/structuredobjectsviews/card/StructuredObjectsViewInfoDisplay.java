package ru.naumen.metainfoadmin.client.structuredobjectsviews.card;

import com.google.gwt.user.cellview.client.CellTable;

import ru.naumen.core.client.table.builder.WithDebugIdCellTableBuilder;
import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay;

/**
 * Дисплей таблицы на карточке иерархической структуры
 * <AUTHOR>
 * @since 14.10.2021
 */
public class StructuredObjectsViewInfoDisplay<T extends HasCode> extends TableWithArrowsDisplay<T>
{
    public StructuredObjectsViewInfoDisplay()
    {
        super();
        wrapTableInScrollableContainer();
        CellTable<T> cellTable = (CellTable<T>)getTable();
        cellTable.setTableBuilder(new WithDebugIdCellTableBuilder<>(cellTable, HasCode.PROVIDES_KEY));
    }
}
