package ru.naumen.metainfoadmin.client.customforms.parameters;

import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.core.shared.dto.DtObject.CREATE_FROM_METACLASSLITE;
import static ru.naumen.metainfoadmin.client.attributes.forms.props.PermittedTypesPropertyController.PERMITTED_TYPES_CLASS_FQN_EXTRACTOR;
import static ru.naumen.metainfoadmin.client.customforms.parameters.ParameterFormContextValues.PERMITTED_TYPES_FQNS;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.collect.Sets;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.selection.HierarchicalMultiSelectionModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes.PermittedTypesRefreshDelegate;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Реализация {@link PermittedTypesRefreshDelegate} для разрешенных типов на форме 
 * редактирования и добавления параметров настраиваемых форм
 *
 * <AUTHOR>
 * @since 27 апр. 2016 г.
 */
public class PermittedTypesRefreshDelegateParameterFormImpl<F extends ParameterForm>
        implements PermittedTypesRefreshDelegate<F>
{
    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    @Inject
    CommonMessages messages;

    @Override
    public void refreshProperty(final PropertyContainerContext context,
            PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                    HierarchicalMultiSelectionModel<DtObject>>> property,
            final AsyncCallback<Boolean> callback)
    {
        // Достаем из контекста исходные разрещенные типы объектов
        Set<ClassFqn> fqns = context.getContextValues().getProperty(PERMITTED_TYPES_FQNS);
        final boolean isNoOne = !CollectionUtils.isEmpty(fqns) && Constants.NOONE.equals(fqns.iterator().next());
        if (CollectionUtils.isEmpty(fqns) || isNoOne)
        {
            // Если исходные типы были затерты сменой класса объектов или типа параметра
            // получаем их из текущего выбранного класса
            fqns = Sets.newHashSet(PERMITTED_TYPES_CLASS_FQN_EXTRACTOR.apply(context));
        }

        metainfoService.getMetaClasses(fqns, new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> metaClasses)
            {
                List<DtObject> dtos = new ArrayList<DtObject>();
                if (isNoOne)
                {
                    dtos.add(new SimpleDtObject("", messages.noone(), Constants.NOONE));
                }
                else
                {
                    dtos.addAll(make(metaClasses).transform(CREATE_FROM_METACLASSLITE).toList());
                }
                context.setProperty(AttributeFormPropertyCode.PERMITTED_TYPES, dtos);
                callback.onSuccess(true);
            }
        });
    }
}
