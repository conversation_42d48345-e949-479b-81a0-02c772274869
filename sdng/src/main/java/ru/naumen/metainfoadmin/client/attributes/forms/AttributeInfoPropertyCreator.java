package ru.naumen.metainfoadmin.client.attributes.forms;

import java.util.Map;

import com.google.inject.ImplementedBy;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Интерфейс для создания {@link Property} отображения параметров атрибутов
 * на модальную форму просмотра атрибутов
 *
 * <AUTHOR>
 * @since 26 июл. 2018 г.
 */
@ImplementedBy(AttributeInfoPropertyCreatorImpl.class)
public interface AttributeInfoPropertyCreator
{
    /**
     * Возвращает коллекцию  созданных свойств
     */
    Map<Integer, Property<?>> createProperties(ReadyState rs);

    /**
     * Устанавливает значение атрибута
     */
    void setAttribute(Attribute attribute);

    /**
     * Инициализирует свойствами
     */
    void setPropertyValues(IProperties propertyValues);

}
