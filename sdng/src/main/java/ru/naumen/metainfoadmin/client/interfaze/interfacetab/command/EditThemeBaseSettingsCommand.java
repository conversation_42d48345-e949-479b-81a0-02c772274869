package ru.naumen.metainfoadmin.client.interfaze.interfacetab.command;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms.EditThemesBaseSettingsFormPresenter;

/**
 * Команда редактирование базовых настроек интерфейса
 *
 * <AUTHOR>
 * @since 11.07.22
 */
public class EditThemeBaseSettingsCommand extends BaseCommandImpl<InterfaceSettingsContext, InterfaceSettingsContext>
{
    public static final String ID = "EditThemeBaseSettingsCommand";

    @Inject
    private Provider<EditThemesBaseSettingsFormPresenter> editFormProvider;

    @Inject
    public EditThemeBaseSettingsCommand(
            @Assisted CommandParam<InterfaceSettingsContext, InterfaceSettingsContext> param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<InterfaceSettingsContext, InterfaceSettingsContext> param)
    {
        EditThemesBaseSettingsFormPresenter presenter = editFormProvider.get();
        presenter.init(param.getValue());
        presenter.bind();
        presenter.getDisplay().display();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}