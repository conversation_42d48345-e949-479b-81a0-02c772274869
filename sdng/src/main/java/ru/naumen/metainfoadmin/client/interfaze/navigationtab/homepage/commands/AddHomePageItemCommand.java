package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.HomePageCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.add.AddHomePageItemFormPresenter;

/**
 * Команда добавления элемента домашней страницы
 *
 * <AUTHOR>
 * @since 09.01.2023
 */
public class AddHomePageItemCommand extends BaseCommandImpl<HomePageDtObject, DtoContainer<NavigationSettings>>
{
    private final Provider<AddHomePageItemFormPresenter> addHomePageFormPresenter;

    @Inject
    public AddHomePageItemCommand(@Assisted HomePageCommandParam param,
            Provider<AddHomePageItemFormPresenter> addHomePageFormPresenter)
    {
        super(param);
        this.addHomePageFormPresenter = addHomePageFormPresenter;
    }

    @Override
    public void execute(CommandParam<HomePageDtObject, DtoContainer<NavigationSettings>> param)
    {
        AddHomePageItemFormPresenter formPresenter = addHomePageFormPresenter.get();
        formPresenter.init(((HomePageCommandParam)param).getSettings().get(), param.getValue(), param.getCallback());
        formPresenter.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.ADD;
    }
}
