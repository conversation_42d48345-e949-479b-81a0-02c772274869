package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;

/**
 * Делегат изменения значения свойства "Шаблон карточки" элемента домашней страницы
 * {@link HomePage#REFERENCE_UI_TEMPLATE}
 * <AUTHOR>
 * @since 15.01.2023
 */
public class HomePageReferenceTemplateDelegateVCH implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        String templateCode = context.getPropertyValues().getProperty(HomePage.REFERENCE_UI_TEMPLATE);
        DtObject referenceDto = context.getPropertyValues().getProperty(ReferenceCode.REFERENCE_VALUE);
        if (null != referenceDto)
        {
            referenceDto.setProperty(ReferenceCode.UI_TEMPLATE, templateCode);
        }

        Reference reference = context.getPropertyValues().getProperty(HomePage.REFERENCE_TAB_VALUE);
        if (null != reference)
        {
            reference.setTemplateCode(templateCode);
        }
    }
}
