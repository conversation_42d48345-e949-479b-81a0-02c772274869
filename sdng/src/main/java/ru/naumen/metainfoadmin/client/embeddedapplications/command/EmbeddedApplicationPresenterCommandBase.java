package ru.naumen.metainfoadmin.client.embeddedapplications.command;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;

/**
 * <AUTHOR>
 * @since 07.07.2016
 *
 */
public abstract class EmbeddedApplicationPresenterCommandBase extends
        PresenterCommandImpl<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto,
                EmbeddedApplicationAdminSettingsDto>
{
    protected EmbeddedApplicationPresenterCommandBase(CommandParam<EmbeddedApplicationAdminSettingsDto,
            EmbeddedApplicationAdminSettingsDto> param)
    {
        super(param);
    }

    @Override
    public void onExecute(EmbeddedApplicationAdminSettingsDto result,
            CallbackDecorator<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto> callback)
    {
        callback.onSuccess(result);
    }
}
