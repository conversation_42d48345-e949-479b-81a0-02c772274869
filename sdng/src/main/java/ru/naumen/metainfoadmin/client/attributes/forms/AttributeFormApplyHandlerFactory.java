/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.forms;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.events.ApplyFormHandler;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.metainfo.shared.elements.MetaClass;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

public interface AttributeFormApplyHandlerFactory<F extends ObjectForm>
{
    //@formatter:off
    ApplyFormHandler create(@Assisted("contextProps") IProperties contextProps,
        @Assisted("propertyValues") IProperties propertyValues,
        PropertyContainerPresenter propertyContainer, Context context,
        Presenter formPresenter, AsyncCallback<MetaClass> callback);
    //@formatter:on
}