package ru.naumen.metainfoadmin.client.attributes.forms.info;

import jakarta.annotation.Nullable;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Создает {@link Property} для отображения общей информации
 * на модальной форме свойств атрибута
 *
 * <AUTHOR>
 * @since 3 авг. 2018 г.
 */
public class CommonAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    private String value;

    public void create(String code, String value)
    {
        this.value = value;
        createInt(code);
    }

    @Override
    protected void createInt(String code)
    {
        if (StringUtilities.isEmptyTrim(value))
        {
            return;
        }
        createProperty(code, value, getCaption(code));
    }

    @Nullable
    private String getCaption(String code)
    {
        return IntegerAttributeType.CODE.equals(attribute.getType().getCode()) && code.equals(
                AttributeFormPropertyCode.USE_GEN_RULE) ? cmessages.useNumberRule() : null;
    }
}
