package ru.naumen.metainfoadmin.client.customforms.form;

import java.util.Collection;
import java.util.Set;

import jakarta.inject.Inject;

import ru.naumen.core.client.tree.metainfo.DtoFilteredMetaClassHierarchicalTreeContext;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.tree.selection.HierarchicalMetaClassMultiSelectionModel;
import ru.naumen.core.client.tree.view.ITreeViewModel;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.client.widgets.tree.dto.DtoPopupMultiValueCellTree;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * Фабрика деревьев быстрого выбора метаклассов на форме добавления/редактирования пользовательской формы.
 * Позволяет выбирать все подтипы при выборе класса. Дерево строится от основного класса.
 * <AUTHOR>
 * @since Feb 26, 2018
 */
public class FastSelectionMetaClassTreeFactory
{
    @Inject
    private DtoMetaClassesTreeFactory<HierarchicalMetaClassMultiSelectionModel<DtObject>,
            DtoFilteredMetaClassHierarchicalTreeContext> treeModelFactory;
    @Inject
    private PopupValueCellTreeFactory<DtObject, Collection<DtObject>,
            HierarchicalMetaClassMultiSelectionModel<DtObject>> treeFactory;

    public DtoPopupMultiValueCellTree<HierarchicalMetaClassMultiSelectionModel<DtObject>> createTree(
            Collection<MetaClassLite> cases, Set<ClassFqn> notSelectable)
    {
        ITreeViewModel<DtObject, HierarchicalMetaClassMultiSelectionModel<DtObject>> treeModel = createTreeViewModel(
                cases, notSelectable);
        return (DtoPopupMultiValueCellTree<HierarchicalMetaClassMultiSelectionModel<DtObject>>)treeFactory
                .create(treeModel);
    }

    private ITreeViewModel<DtObject, HierarchicalMetaClassMultiSelectionModel<DtObject>> createTreeViewModel(
            Collection<MetaClassLite> cases, Set<ClassFqn> notSelectable)
    {
        DtoFilteredMetaClassHierarchicalTreeContext treeContext = new DtoFilteredMetaClassHierarchicalTreeContext(
                AbstractBO.FQN.fqnOfClass(), cases, notSelectable);
        return treeModelFactory.createMetaClassTreeViewModel(Container.create(treeContext));
    }
}
