package ru.naumen.metainfoadmin.client.embeddedapplications.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationWithScript;

/**
 * Отвечает за перевод состояния выключателя в "включено" (on), для визуального представления
 * <AUTHOR>
 * @since 07.07.2016
 */
public class EnableEmbeddedApplicationCommand extends ToggleEmbeddedApplicationCommand
{
    @Inject
    public EnableEmbeddedApplicationCommand(
            @Assisted CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto> param)
    {
        super(param);
    }

    @Override
    public boolean isPossible(Object input)
    {
        return !((EmbeddedApplicationWithScript)input).getObject().isOn();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.SWITCH_ON;
    }

}
