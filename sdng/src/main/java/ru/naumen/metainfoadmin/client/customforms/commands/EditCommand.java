package ru.naumen.metainfoadmin.client.customforms.commands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormPresenter;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributePresenterFactory;
import ru.naumen.metainfoadmin.client.customforms.CustomFormContext;
import ru.naumen.metainfoadmin.client.customforms.parameters.ParameterFormAdd;
import ru.naumen.metainfoadmin.client.customforms.parameters.ParameterFormEdit;

/**
 * Команда редактирования параметра настраиваемой формы
 *
 * <AUTHOR>
 * @since 26 апр. 2016 г.
 */
public class EditCommand extends BaseCommandImpl<Attribute, Void>
{
    @Inject
    private AttributePresenterFactory<ParameterFormEdit> presenterFactory;

    private CustomFormContext context;

    @Inject
    public EditCommand(@Assisted AttributeCommandParam param)
    {
        super(param);
        context = (CustomFormContext)param.getContext();
    }

    @Override
    public void execute(CommandParam<Attribute, Void> param)
    {
        AttributeFormPresenter<ParameterFormAdd> presenter = (AttributeFormPresenter<ParameterFormAdd>)presenterFactory
                .create(context);
        Attribute attr = param.getValue();
        presenter.init(attr, new BasicCallback<MetaClass>());
        presenter.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

}
