package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import java.util.List;
import java.util.Map.Entry;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;

/**
 * <AUTHOR>
 * @since 8 февр. 2019 г.
 *
 */
public interface AvailableRestrictionTypesProvider<F extends ObjectForm>
{

    /**
     * Возвращает типы, доступные для выбора
     *
     * @return пара [код типа, название типа]
     */
    List<Entry<String, String>> getTypes();

    /**
     * Возвращает название атрибута по его коду
     */
    String getTypeTitle(String code);

}