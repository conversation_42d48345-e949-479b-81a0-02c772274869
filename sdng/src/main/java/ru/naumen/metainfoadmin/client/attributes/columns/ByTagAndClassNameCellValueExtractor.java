package ru.naumen.metainfoadmin.client.attributes.columns;

import java.util.Arrays;

import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NodeList;

import ru.naumen.core.client.widgets.grouplist.CellValueExtractor;

/**
 * Получение строкового значения из уже отрисованной ячейки таблицы на основании имени тега и класса.
 *
 * <AUTHOR>
 * @since 3 окт. 2018 г.
 *
 */
public abstract class ByTagAndClassNameCellValueExtractor implements CellValueExtractor<String>
{
    protected static final String TAG_NAME_SPAN = "span";
    protected static final String TAG_NAME_DIV = "div";

    @Override
    public String extractValue(Element td)
    {
        String result = null;
        if (td != null)
        {
            NodeList<Element> childs = td.getElementsByTagName(getTagName());
            for (int i = 0; i < childs.getLength(); i++)
            {
                if (Arrays.stream(childs.getItem(i).getClassName().split(" ")).filter(cl -> getClassName().equals(cl))
                        .findFirst().isPresent())
                {
                    result = childs.getItem(i).getInnerText();
                    break;
                }
            }
        }
        return result;
    }

    /**
     * @return имя тега, в котором ищем значение
     */
    abstract String getTagName();

    /**
     * @return имя класса в теге {@link getTagName()}, в котором ищем значение
     */
    abstract String getClassName();

}
