package ru.naumen.metainfoadmin.client.attributes.forms.info;

import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.IHyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.timer.definition.TimerDefinitionWithScript;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfoadmin.client.timer.TimerPlace;

/**
 * Создает {@link Property} для отображения информации о
 * счетчике времени на модальной форме свойств атрибута 
 *
 * <AUTHOR>
 * @since 3 авг. 2018 г.
 */
public class TimerDefinitionInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    @Inject
    private MetainfoUtils metainfoUtils;

    @Override
    protected void createInt(String code)
    {
        String timerCode = propertyValues.getProperty(code);
        if (StringUtilities.isEmptyTrim(timerCode))
        {
            return;
        }
        createInt(code, timerCode);
    }

    private String createHyperLink(String code, String title)
    {
        String url = "#" + TimerPlace.PLACE_PREFIX + ":" + code;
        IHyperlink link = new Hyperlink(title + " (" + code + ")",
                url);
        return link.toString();
    }

    private void createInt(String code, String timerCode)
    {
        metainfoService.getTimerDefinition(timerCode, new BasicCallback<TimerDefinitionWithScript>(rs)
        {
            @Override
            protected void handleSuccess(TimerDefinitionWithScript timer)
            {
                createProperty(code, createHyperLink(timerCode, metainfoUtils.getLocalizedValue(timer.getTitle())));
            }
        });
    }

}
