package ru.naumen.metainfoadmin.client.eventaction.form.creator;

import static ru.naumen.core.shared.Constants.FROALA_OPTION_USE_CLASSES;

import java.util.Collection;
import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.selection.FilteredMultiSelectionModel;
import ru.naumen.core.client.widgets.FroalaRichTextWidget;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.script.places.EventActionCategories;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.mailsender.client.MailSenderMetainfoService;
import ru.naumen.mailsender.shared.MailSenderConstants;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.elements.mail.MailUtils;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;
import ru.naumen.metainfo.shared.elements.mail.SendMailParameters;
import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.NotificationEventAction;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;
import ru.naumen.metainfoadmin.client.eventaction.template.TemplateEditPropertiesController;

/**
 * Создание свойств на форме для ДПС типа Почтовое оповещение
 *
 * <AUTHOR>
 * @since 13.01.2012
 */
public class NotificationEventActionFormPropertiesCreator extends
        EventActionWithRecipientsFormPropertiesCreator<NotificationEventAction>
{
    @Inject
    @Named(PropertiesGinModule.LABEL)
    private Property<String> from;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> to;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> emails;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> subject;
    @Inject
    private TemplateEditPropertiesController templateProperties;
    @Inject
    private MailSenderMetainfoService mailSenderMetainfoService;

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {
        subject.setValidationMarker(true);
        message.setValidationMarker(true);

        add(validation.validate(subject, notEmptyValidator));
        add(validation.validate(message, textRtfNotEmptyValidator));
    }

    @Override
    public void bindProperties(EventActionFormDisplay display, List<ClassFqn> fqns)
    {
        message = templateProperties.getMessageProperty();
        super.bindProperties(display, fqns);
        add(messages.from(), from);
        from.setDisable();
        add(messages.to(), to);
        to.setDisable();
        add(messages.emails(), emails);
        add(messages.subject(), subject);
        add(messages.template(), templateProperties.getTemplateProperty());
        add(messages.notificationContent(), message);
        bindHtmlFormatProperty();

        emails.setMaxLength(StringAttributeType.MAX_LENGTH_DEFAULT);
        to.getCaptionWidget().asWidget().addStyleName(formStyle.formEmailToLabel());
        emails.getCaptionWidget().asWidget().addStyleName(formStyle.formEmailEmailsLabel());
        emails.getValueWidget().asWidget().addStyleName(formStyle.formEmailEmailsInput());
        subject.getCaptionWidget().asWidget().addStyleName(formStyle.formEmailSubjectLabel());
        excludeAuthor.getValueWidget().asWidget().addStyleName(formStyle.formEmailExcludeAuthorInput());
        excludeAuthor.getCaptionWidget().asWidget().addStyleName(formStyle.formEmailExcludeAuthorLabel());

        templateProperties.initProperties(display);

        bindPropertiesAfter(display, fqns, EventActionCategories.EVENTACTION_NOTIFICATION_CUSTOMIZATION);

        prepareRichTextWidget();
    }

    @Override
    protected void setActionProperties(NotificationEventAction action)
    {
        super.setActionProperties(action);

        action.setEmails(emails.getValue());
        metainfoUtils.setLocalizedValue(action.getSubject(), subject.getValue());
        templateProperties.getPropertiesValues(action);
    }

    @Override
    protected Action newEventActionTypeInstance()
    {
        return new NotificationEventAction();
    }

    @Override
    public void init(@Nullable EventActionWithScript eventAction, Property<SelectItem> event)
    {
        super.init(eventAction, event);
        if (eventAction != null && !(eventAction.getObject().getAction() instanceof NotificationEventAction))
        {
            throw new UnsupportedOperationException("EventAction must be NotificationEventAction");
        }
        this.eventAction = eventAction;
    }

    @Override
    protected Property<Collection<DtObject>> createRecipients(
            final PopupValueCellTree<DtObject, Collection<DtObject>, FilteredMultiSelectionModel<DtObject>> tree)
    {
        Property<Collection<DtObject>> rec = new PropertyBase<>(
                messages.employees(), tree);
        rec.getCaptionWidget().asWidget().addStyleName(formStyle.formEmailEmployeesLabel());
        rec.getValueWidget().asWidget().addStyleName(formStyle.formEmailEmployeesInput());
        DebugIdBuilder.ensureDebugId(rec, "recipients");
        return rec;
    }

    @Override
    protected void ensureDebugIds()
    {
        super.ensureDebugIds();
        templateProperties.ensureDebugIds();
        DebugIdBuilder.ensureDebugId(from, "from");
        DebugIdBuilder.ensureDebugId(to, "to");
        DebugIdBuilder.ensureDebugId(emails, "emails");
        DebugIdBuilder.ensureDebugId(subject, "subject");
    }

    @Override
    protected int getRightSideRecipientsPropertyIndex()
    {
        return 3;
    }

    @Override
    protected void setPropertiesValuesAsync(ReadyState readyState)
    {
        super.setPropertiesValuesAsync(readyState);
        mailSenderMetainfoService.getOutgoingMailServerConfig(MailSenderConstants.DEFAULT_CONFIG_CODE,
                new BasicCallback<OutgoingMailServerConfig>(readyState)
                {
                    @Override
                    protected void handleSuccess(OutgoingMailServerConfig responce)
                    {
                        SendMailParameters sendMailParameters = responce.getSendMailParameters();
                        if (sendMailParameters != null)
                        {
                            from.setValue(MailUtils.getMailSender(sendMailParameters));
                        }
                    }
                });
        //@formatter:on

        templateProperties.setPropetiesValues(isEventActionExists() ? getAction(eventAction) : null, readyState);
    }

    @Override
    protected void setPropertiesValuesForExisted(NotificationEventAction action)
    {
        super.setPropertiesValuesForExisted(action);
        emails.setValue(action.getEmails());
        subject.setValue(metainfoUtils.getLocalizedValue(action.getSubject()));
    }

    @Override
    protected void prepareRichTextWidget()
    {
        super.prepareRichTextWidget();
        if (message.getValueWidget() instanceof FroalaRichTextWidget)
        {
            ((FroalaRichTextWidget)message.getValueWidget()).setOption(FROALA_OPTION_USE_CLASSES,
                    isUseClassesNotification());
        }
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        templateProperties.unbind(callback);
        super.unbind(callback);
    }

    private native boolean isUseClassesNotification()
    /*-{
        return $wnd.useClassesNotification;
    }-*/;
}
