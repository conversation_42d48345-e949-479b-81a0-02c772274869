package ru.naumen.metainfoadmin.client.attributes.forms.edit;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.TimerAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormAfterBindHandlerImpl;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Действия по окончании биндинга формы редактирования атрибутов - вычисляются условия, по которым некоторые из
 * свойств могут быть отключены
 * <AUTHOR>
 * @since 17.05.2012
 */
public class EditAttributeFormAfterBindHandlerImpl extends AttributeFormAfterBindHandlerImpl<ObjectFormEdit>
{
    @Override
    public void onAfterContainerBind(PropertyContainerContext context)
    {
        Attribute attribute = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        SCParameters scParameters = context.getPropertyValues().getProperty(AttributeFormPropertyCode.SC_PARAMETERS,
                null);

        String attrType = attribute.getType().getCode();
        if (attribute.getType().isAttributeOfRelatedObject())
        {
            attrType = AttributeOfRelatedObjectSettings.CODE;
        }

        boolean isSystemAttribute = attribute.isHardcoded();
        boolean isFileClass = File.FQN.isSameClass(metaClass.getFqn());
        boolean isCommentClass = Comment.FQN.isSameClass(metaClass.getFqn());
        boolean isDescriptionAttr = ObjectUtils.equals(attribute.getCode(), File.DESCRIPTION);
        boolean isCommentAuthorAliasAttr = isSystemAttribute && Employee.FQN.isSameClass(metaClass.getFqn())
                                           && ObjectUtils.equals(attribute.getCode(), Employee.COMMENT_AUTHOR_ALIAS);

        boolean isSystemRequired = attribute.isSystemRequired();
        boolean isSystemEditable = attribute.isSystemEditable();
        boolean isSystemUnique = attribute.isSystemUnique();
        boolean isParentAttribute = Constants.PARENT_ATTR.equals(attribute.getCode());
        boolean isServiceCallClientAttribute = Association.CLIENT.equals(attribute.getCode())
                                               && (ServiceCall.FQN.equals(metaClass.getFqn())
                                                   || ServiceCall.FQN.isClassOf(metaClass.getFqn()));
        boolean isBackLink = BackLinkAttributeType.CODE.equals(attrType);
        boolean isEditableType = !ru.naumen.metainfo.shared.Constants.NOT_EDITABLE_ATTRIBUTE_TYPES.contains(attrType);
        boolean isUniqueType = ru.naumen.metainfo.shared.Constants.UNIQUE_ATTRIBUTE_TYPES.contains(attrType);
        boolean isEditableInListsType = ru.naumen.metainfo.shared.Constants.EDITABLE_IN_LISTS_ATTRIBUTE_TYPES
                .contains(attrType);
        boolean isEditableOnlyInListsType = ru.naumen.metainfo.shared.Constants.EDITABLE_ONLY_IN_LISTS_ATTRIBUTE_TYPES
                .contains(attrType);

        boolean alwaysEditable = ru.naumen.metainfo.shared.Constants.ALWAYS_EDITABLE_ATTRIBUTE_TYPES.contains(attrType);
        boolean isEditableEnabled = isSystemEditable && !isParentAttribute && isEditableType
                                    && !isServiceCallClientAttribute && !alwaysEditable;
        if ((isFileClass || isCommentClass) && !isDescriptionAttr && isSystemAttribute)
        {
            isEditableEnabled = false;
        }

        boolean isEditableInListsEnabled = (isEditableEnabled || isEditableOnlyInListsType
                                                                 && CommonUtils.isNotSystemClass(metaClass))
                                           && isEditableInListsType;

        boolean isRequiredDisabled = !isSystemEditable || isSystemRequired || !isEditableType || isBackLink
                                     || isParentAttribute || ((isFileClass || isCommentClass) && !isDescriptionAttr
                                                              && isSystemAttribute)
                                     || isCommentAuthorAliasAttr;
        boolean isUniqueDisabled = !isSystemEditable || isSystemUnique || !isUniqueType || isParentAttribute
                                   || ((isFileClass || isCommentClass) && !isDescriptionAttr && isSystemAttribute)
                                   || isCommentAuthorAliasAttr;
        boolean disableTimer = isTargetTimerDisabled(attribute);

        if (isServiceCallClientAttribute)
        {
            isRequiredDisabled = isRequiredDisabled || !scParameters.isClientRequiredEditable();
        }

        disable(context, CODE, true);
        disable(context, ATTR_TYPE, true);
        disable(context, TARGET_CLASS, true);
        disable(context, TARGET_CATALOG, true);
        disable(context, DIRECT_LINK_TARGET, true);
        disable(context, TARGET_TIMER, disableTimer);
        disable(context, COMPUTABLE, true);
        disable(context, EDITABLE, !isEditableEnabled);
        disable(context, EDITABLE_IN_LISTS, !isEditableInListsEnabled);
        disable(context, REQUIRED, isRequiredDisabled);
        disable(context, REQUIRED_IN_INTERFACE, isRequiredDisabled);
        disable(context, UNIQUE, isUniqueDisabled);
        disable(context, AGGREGATE_ATTRIBUTES, true);

        context.getPropertyControllers().get(INHERIT).fireValueChangeEvent();
        super.onAfterContainerBind(context);
    }

    private void disable(PropertyContainerContext context, String property, boolean disable)
    {
        if (disable)
        {
            context.setDisabled(property);
        }
    }

    private boolean isTargetTimerDisabled(Attribute attribute)
    {
        if (!ru.naumen.metainfo.shared.Constants.TIMER_ATTRIBUTE_TYPES.contains(attribute.getType().getCode()))
        {
            return true;
        }
        return !StringUtilities.isEmpty(attribute.getType().<TimerAttributeType> cast().getTimerDefinitionCode());
    }
}
