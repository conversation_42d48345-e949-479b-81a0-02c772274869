package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Базовый делегат обновления для виджетов на формах "Верхнее меню" и "Левое меню"
 *
 * <AUTHOR>
 * @since 09.03.2022
 */
public class ReferencePropertyDelegateRefreshBase<T, P extends Property<T>> implements PropertyDelegateRefresh<T, P>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, P property, AsyncCallback<Boolean> callback)
    {
        boolean isReference = isReference(context);
        callback.onSuccess(isReference);
        if (!isReference)
        {
            context.getPropertyControllers().get(ReferenceCode.OBJECT_CLASS).unbindValidators();
            context.getPropertyControllers().get(ReferenceCode.ATTRIBUTE_CHAIN).unbindValidators();
            context.getPropertyControllers().get(ReferenceCode.OBJECT_CASES).unbindValidators();
            context.getPropertyControllers().get(MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE).unbindValidators();
            context.getPropertyControllers().get(MenuItemPropertyCode.TYPE_OF_CARD).unbindValidators();
            context.getPropertyControllers().get(MenuItemPropertyCode.USE_ATTR_TITLE).unbindValidators();
        }
    }

    public static boolean isReference(PropertyContainerContext context)
    {
        String typeStr = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE);
        MenuItemType type = MenuItemType.valueOf(typeStr);
        return MenuItemType.reference.equals(type);
    }
}
