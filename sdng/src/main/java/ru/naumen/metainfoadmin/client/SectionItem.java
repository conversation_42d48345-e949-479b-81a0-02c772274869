package ru.naumen.metainfoadmin.client;

import com.google.gwt.place.shared.Place;
import com.google.gwt.user.client.ui.IsWidget;

/**
 * Элемент содержимого секции, предназначенный для вывода наименования,
 * являющегося ссылкой и описания. При клике на наименовании, осуществляется
 * переход к {@link Place}, указанному в {@link #setPlace(Place)}
 *
 * <AUTHOR>
 */
public interface SectionItem extends IsWidget
{
    /**
     * Описание элемента
     *
     * @param description
     */
    void setDescription(String description);

    /**
     * Целевой {@link Place}, при клике на наименовании
     *
     * @param place
     */
    void setPlace(Place place);

    /**
     * Установка наименования элемента
     *
     * @param title
     *            значение наименования
     */
    void setTitle(String title);
}
