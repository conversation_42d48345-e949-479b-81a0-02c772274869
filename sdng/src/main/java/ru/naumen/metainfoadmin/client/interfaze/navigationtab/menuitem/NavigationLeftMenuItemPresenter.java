package ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsLMCommandParam;

/**
 * Презентер катрочки элемента левого меню
 * <AUTHOR>
 * @since 16.07.2020
 */
public class NavigationLeftMenuItemPresenter
        extends NavigationMenuItemPresenter<LeftMenuItemSettingsDTO, NavigationLeftMenuItemContext,
        NavigationSettingsLMCommandParam>
{
    @Inject
    public NavigationLeftMenuItemPresenter(AdminTabDisplay display, EventBus eventBus,
            NavigationSettingsMessages messages, DispatchAsync dispatch,
            NavigationLeftMenuItemAttributesPresenter attributesPresenter)
    {
        super(display, eventBus, messages, dispatch, attributesPresenter);
    }

    @Override
    protected NavigationLeftMenuItemContext getContext(LeftMenuItemSettingsDTO menuItem,
            DtoContainer<NavigationSettings> settings)
    {
        return new NavigationLeftMenuItemContext(menuItem, settings);
    }
}