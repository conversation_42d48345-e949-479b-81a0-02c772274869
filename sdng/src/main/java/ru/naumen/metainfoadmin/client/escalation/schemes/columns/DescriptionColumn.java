package ru.naumen.metainfoadmin.client.escalation.schemes.columns;

import com.google.gwt.user.cellview.client.Column;
import com.google.inject.Inject;

import ru.naumen.core.client.widgets.columns.TextCellWithId;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;

/**
 * <AUTHOR>
 * @since 23.07.2012
 *
 */
public class DescriptionColumn extends Column<DtoContainer<EscalationScheme>, String>
{
    private final I18nUtil i18nUtil;

    @Inject
    public DescriptionColumn(TextCellWithId cell, I18nUtil i18nUtil)
    {
        super(cell);
        cell.ensureDebugId("description");
        this.i18nUtil = i18nUtil;
    }

    @Override
    public String getValue(DtoContainer<EscalationScheme> object)
    {
        return i18nUtil.getLocalizedDescription(object.get());
    }
}
