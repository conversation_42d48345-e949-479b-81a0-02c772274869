package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import jakarta.annotation.Nullable;

import com.google.gwt.event.shared.GwtEvent;

import ru.naumen.metainfo.shared.ui.Tool;

/**
 * Событие о перемещении тула
 * <AUTHOR>
 * @since 29 мая 2015 г.
 *
 */
public class MoveToolEvent extends GwtEvent<MoveToolHandler>
{
    private static final Type<MoveToolHandler> TYPE = new Type<>();

    public static Type<MoveToolHandler> getType()
    {
        return TYPE;
    }

    private final Tool draggedTool;
    private final boolean moveToContent;
    private final Tool tool;
    private boolean toTheLeft;

    /**
     * @param draggedTool контент тула, который перемещается
     * @param moveToContent он перемещается в панель контента тулпанели (верхняя панель) или в панель доступных тулов
     *                     (нижняя панель)
     * @param tool тул среди всех тулов {@link ru.naumen.metainfo.shared.ui.ToolPanel#getTools}, после которого его
     *             надо вставить на новом месте; null - вставить в конец тулпанели
     * @param toTheLeft вставить слева от тула tool
     */
    public MoveToolEvent(Tool draggedTool, boolean moveToContent, @Nullable Tool tool, boolean toTheLeft)
    {
        this.draggedTool = draggedTool;
        this.moveToContent = moveToContent;
        this.tool = tool;
        this.toTheLeft = toTheLeft;
    }

    @Override
    public GwtEvent.Type<MoveToolHandler> getAssociatedType()
    {
        return TYPE;
    }

    public Tool getDraggedTool()
    {
        return draggedTool;
    }

    public Tool getTool()
    {
        return tool;
    }

    /**
     * @return тул перемещается из панели доступных тулов (нижняя панель) или из панели контента тулпанели (верхняя
     * панель)
     */
    public boolean isMovedFromTools()
    {
        return draggedTool.getParent().getParent().getUuid().startsWith(EditableToolPanelGinModule.TOOLS_PREFIX);
    }

    /**
     * @return тул перемещается в панель контента тулпанели (верхняя панель) или в панель доступных тулов (нижняя
     * панель)
     */
    public boolean isMoveToContent()
    {
        return moveToContent;
    }

    /**
     * @return является ли перемещаемый тул разделителем групп
     */
    public boolean isSeparator()
    {
        return EditableToolPanelGinModule.SEPARATOR.equals(draggedTool.getUuid());
    }

    public boolean isToTheLeft()
    {
        return toTheLeft;
    }

    public void setToTheLeft(boolean toTheLeft)
    {
        this.toTheLeft = toTheLeft;
    }

    @Override
    protected void dispatch(MoveToolHandler handler)
    {
        handler.onMoveTool(this);
    }
}
