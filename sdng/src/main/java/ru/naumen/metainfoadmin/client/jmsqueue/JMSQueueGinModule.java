package ru.naumen.metainfoadmin.client.jmsqueue;

import jakarta.inject.Singleton;

import com.google.common.collect.ImmutableList;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.gwt.place.shared.Place;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.widgets.columns.PlaceProvider;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.eventaction.Constants.EventAction;
import ru.naumen.metainfo.shared.jmsqueue.JMSQueue;
import ru.naumen.metainfoadmin.client.jmsqueue.card.JMSQueuePlace;
import ru.naumen.metainfoadmin.client.jmsqueue.card.JMSQueueCardPresenter;
import ru.naumen.metainfoadmin.client.jmsqueue.comands.AddLinkJMSQueueCommand;
import ru.naumen.metainfoadmin.client.jmsqueue.comands.BreakLinkJMSQueueCommand;
import ru.naumen.metainfoadmin.client.jmsqueue.comands.CountMessagesJMSQueueCommand;
import ru.naumen.metainfoadmin.client.jmsqueue.comands.DeleteJMSQueueCommand;
import ru.naumen.metainfoadmin.client.jmsqueue.comands.EditJMSQueueCommand;
import ru.naumen.metainfoadmin.client.jmsqueue.service.JMSQueueServiceAsync;
import ru.naumen.metainfoadmin.client.jmsqueue.service.JMSQueueServiceAsyncImpl;

/**
 * Инициализатор классов для работы с JMS очередями на стороне клиента
 * <AUTHOR>
 * @since 16.02.2021
 **/
public class JMSQueueGinModule extends AbstractGinModule
{
    static class EventActionsInQueueAttrsProvider implements Provider<ImmutableList<AttributeFqn>>
    {
        @Override
        public ImmutableList<AttributeFqn> get()
        {
            return ImmutableList.of(
                    EventAction.Attributes.TITLE,
                    EventAction.Attributes.LINKED_CLASSES,
                    EventAction.Attributes.EVENT);
        }
    }

    public static final String EVENT_ACTIONS_IN_QUEUE_ATTRS = "eventActionsInQueueAttrs";

    @Override
    protected void configure()
    {
        bind(JMSQueueServiceAsync.class)
                .to(JMSQueueServiceAsyncImpl.class)
                .in(Singleton.class);

        bind(JMSQueueListPresenter.class);
        bind(JMSQueueCardPresenter.class);

        bind(JMSQueueCommandFactoryInitializer.class).asEagerSingleton();

        install(new GinFactoryModuleBuilder()
                .implement(Place.class, JMSQueuePlace.class)
                .build(new TypeLiteral<PlaceProvider<JMSQueue>>()
                {
                }));

        bind(new TypeLiteral<ImmutableList<AttributeFqn>>()
        {
        })
                .annotatedWith(Names.named(EVENT_ACTIONS_IN_QUEUE_ATTRS))
                .toProvider(EventActionsInQueueAttrsProvider.class);

        configureCommands();
    }

    private void configureCommands()
    {
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditJMSQueueCommand.class)
                .build(new TypeLiteral<CommandProvider<EditJMSQueueCommand, CommandParam<DtObject, DtObject>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, DeleteJMSQueueCommand.class)
                .build(new TypeLiteral<CommandProvider<DeleteJMSQueueCommand, CommandParam<DtObject, Void>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, CountMessagesJMSQueueCommand.class)
                .build(new TypeLiteral<CommandProvider<CountMessagesJMSQueueCommand,
                        CommandParam<DtObject, SimpleResult<Integer>>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, AddLinkJMSQueueCommand.class)
                .build(new TypeLiteral<CommandProvider<AddLinkJMSQueueCommand, CommandParam<DtObject, Void>>>()
                {
                }));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, BreakLinkJMSQueueCommand.class)
                .build(new TypeLiteral<CommandProvider<BreakLinkJMSQueueCommand, CommandParam<DtObject, Void>>>()
                {
                }));
    }
}