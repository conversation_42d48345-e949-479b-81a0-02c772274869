package ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms;

import java.util.ArrayList;
import java.util.Objects;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.i18n.client.LocaleInfo;
import com.google.gwt.user.client.Window;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.personalsettings.locales.Locale;
import ru.naumen.core.client.personalsettings.locales.LocaleSettingsGinModule.LocaleProvider;
import ru.naumen.core.client.personalsettings.locales.LocaleSettingsMessages;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataResponse;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.interfacesettings.InterfaceSettings;
import ru.naumen.core.shared.interfacesettings.dispatch.EditLanguageAction;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsGinModule;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContextChangedEvent;

/**
 * Форма редактирования локали и языка истории событий
 *
 * <AUTHOR>
 * @since 19.07.16
 */
public class EditLanguageFormPresenter extends OkCancelPresenter<PropertyFormDisplay>
{
    private enum SetLangForUsers
    {
        forUsersWithDefaultSettings, forAllUsers
    }

    @Inject
    private LocaleSettingsMessages messages;
    @Inject
    private ListBoxProperty interfaceLanguage;
    private PropertyRegistration<SelectItem> interfaceLanguagePropRegistration;
    @Inject
    private ListBoxProperty setSelectedLanguage;
    private PropertyRegistration<SelectItem> setSelectedLanguagePropRegistration;
    @Inject
    private ListBoxProperty interfaceConstantsLanguage;
    private PropertyRegistration<SelectItem> interfaceConstantsLanguagePropRegistration;
    @Inject
    private ListBoxProperty historyLanguage;
    private PropertyRegistration<SelectItem> historyLanguagePropRegistration;
    @Inject
    @Named(InterfaceSettingsGinModule.EVENT_LOCALES)
    private ArrayList<Locale> locales;
    @Inject
    private LocaleProvider localeProvider;
    @Inject
    private DispatchAsync dispatch;

    private InterfaceSettingsContext context;

    @Inject
    public EditLanguageFormPresenter(PropertyFormDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(InterfaceSettingsContext context)
    {
        this.context = context;
    }

    @Override
    public void onApply()
    {
        boolean isForAllUsers = SelectListPropertyValueExtractor.getValue(setSelectedLanguage).equals(
                SetLangForUsers.forAllUsers.name());
        dispatch.execute(new EditLanguageAction(SelectListPropertyValueExtractor.getValue(interfaceLanguage),
                        SelectListPropertyValueExtractor.getValue(historyLanguage), getInterfaceConstantsLanguage(),
                        isForAllUsers),
                new BasicCallback<GetInterfaceTabDataResponse>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(GetInterfaceTabDataResponse response)
                    {
                        unbind();
                        InterfaceSettings responseSettings = response.getSettings();
                        InterfaceSettings contextSettings = context.getSettings();
                        if (responseSettings.getLocale().equals(contextSettings.getLocale()) && responseSettings
                                .getInterfaceConstantsLang().equals(contextSettings.getInterfaceConstantsLang()))
                        {
                            eventBus.fireEvent(new InterfaceSettingsContextChangedEvent(response));
                        }
                        else
                        {
                            Window.Location.reload();
                        }
                    }
                });
    }

    @Override
    protected void onBind()
    {
        setCaption(messages.languageFormEdit());
        bindProperties();
        fillProperties();
        super.onBind();
    }

    private void bindInterfaceConstantsLanguage()
    {
        interfaceConstantsLanguage.asWidget().ensureDebugId("interfaceConstantsLanguage");
        interfaceConstantsLanguage.setCaption(messages.textConstantsInterfaceLanguage());
        locales.forEach(locale -> interfaceConstantsLanguage.getValueWidget().addItem(locale.getTitle(), locale
                .getCode()));
    }

    private void bindProperties()
    {
        interfaceLanguage.asWidget().ensureDebugId("interfaceLanguage");
        interfaceLanguage.setCaption(messages.title());
        interfaceLanguagePropRegistration = getDisplay().add(interfaceLanguage);
        registerHandler(interfaceLanguage.addValueChangeHandler(this::languageChangeHandler));

        bindSetSelectedLanguage();
        bindInterfaceConstantsLanguage();

        historyLanguage.asWidget().ensureDebugId("historyLanguage");
        historyLanguage.setCaption(messages.eventLanguage());
        historyLanguagePropRegistration = getDisplay().add(historyLanguage);

        for (Locale locale : localeProvider.get())
        {
            interfaceLanguage.getValueWidget().addItem(locale.getTitle(), locale.getCode());
        }

        for (Locale locale : locales)
        {
            historyLanguage.getValueWidget().addItem(locale.getTitle(), locale.getCode());
        }
    }

    private void bindSetSelectedLanguage()
    {
        setSelectedLanguage.asWidget().ensureDebugId("setSelectedLanguage");
        setSelectedLanguage.setCaption(messages.setSelectedLanguage());
        setSelectedLanguage.getValueWidget().addItem(messages.forAllUsers(), SetLangForUsers.forAllUsers.name());
        setSelectedLanguage.getValueWidget().addItem(messages.forUsersWithDefaultSettings(),
                SetLangForUsers.forUsersWithDefaultSettings.name());
    }

    private void fillProperties()
    {
        interfaceLanguage.trySetObjValue(StringUtilities.isEmpty(context.getSettings().getLocale()) ? LocaleInfo
                .getCurrentLocale().getLocaleName() : context.getSettings().getLocale());
        updateInterfaceConstantsLanguagePropRegistration();
        historyLanguage.trySetObjValue(StringUtilities.isEmpty(context.getSettings().getEventLocale())
                ? LocaleInfo.getCurrentLocale().getLocaleName()
                : context.getSettings().getEventLocale());
    }

    private String getInterfaceConstantsLanguage()
    {
        String interfaceConstantsLang = SelectListPropertyValueExtractor.getValue(interfaceConstantsLanguage);
        return null == interfaceConstantsLang ? ILocaleInfo.DEFAULT_LANG : interfaceConstantsLang;
    }

    @SuppressWarnings("unused")
    private void languageChangeHandler(ValueChangeEvent<SelectItem> event)
    {
        updateSetSelectedLanguagePropRegistration();
        updateInterfaceConstantsLanguagePropRegistration();
    }

    private void updateInterfaceConstantsLanguagePropRegistration()
    {
        String selectedLocale = SelectListPropertyValueExtractor.getValue(interfaceLanguage);
        if (Objects.equals(selectedLocale, ILocaleInfo.CLIENT_LANG))
        {
            if (null == interfaceConstantsLanguagePropRegistration)
            {
                historyLanguagePropRegistration.unregister();
                interfaceConstantsLanguagePropRegistration = getDisplay().addPropertyAfter(interfaceConstantsLanguage,
                        setSelectedLanguagePropRegistration == null ? interfaceLanguagePropRegistration
                                : setSelectedLanguagePropRegistration);
                historyLanguagePropRegistration = getDisplay().addPropertyAfter(historyLanguage,
                        interfaceConstantsLanguagePropRegistration);
                interfaceConstantsLanguage.trySetObjValue(context.getSettings().getInterfaceConstantsLang());
            }
        }
        else if (interfaceConstantsLanguagePropRegistration != null)
        {
            interfaceConstantsLanguagePropRegistration.unregister();
            interfaceConstantsLanguagePropRegistration = null;
        }
    }

    private void updateSetSelectedLanguagePropRegistration()
    {
        String selectedLocale = SelectListPropertyValueExtractor.getValue(interfaceLanguage);
        if (Objects.equals(selectedLocale, context.getSettings().getLocale()))
        {
            if (null != setSelectedLanguagePropRegistration)
            {
                setSelectedLanguagePropRegistration.unregister();
                setSelectedLanguagePropRegistration = null;
            }
        }
        else if (null == setSelectedLanguagePropRegistration)
        {
            historyLanguagePropRegistration.unregister();
            setSelectedLanguagePropRegistration = getDisplay().addPropertyAfter(setSelectedLanguage,
                    interfaceLanguagePropRegistration);
            historyLanguagePropRegistration = getDisplay().addPropertyAfter(historyLanguage,
                    setSelectedLanguagePropRegistration);
        }
    }
}
