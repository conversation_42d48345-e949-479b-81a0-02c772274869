package ru.naumen.metainfoadmin.client.attributes.columns;

import java.util.function.Predicate;

import jakarta.inject.Inject;

import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.client.widgets.grouplist.AbstractBaseColumn;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Генерация представления логического свойства в списке атрибутов в виде картинок: "галочка" = да, "точка" = нет.  
 *
 * <AUTHOR>
 * @since 7 авг. 2018 г.
 *
 */
public class AttrBooleanColumn extends AbstractBaseColumn<Attribute>
{
    @Inject
    private FontIconFactory<?> iconFactory;

    private final Predicate<? super Attribute> applicablePredicate;

    private String hint;

    @Inject
    public AttrBooleanColumn(@Assisted("debugId") String debugId,
            @Assisted Predicate<? super Attribute> applicablePredicate,
            @Assisted("hint") String hint)
    {
        super(debugId);
        this.applicablePredicate = applicablePredicate;
        this.hint = hint;
    }

    @Override
    public IsWidget createWidget(WidgetContext<Attribute> context)
    {
        String iconCode = applicablePredicate.test(context.getContextObject())
                ? IconCodes.TICK
                : IconCodes.DOT;
        FontIconDisplay<?> icon = iconFactory.create(iconCode);
        icon.asWidget().unsinkEvents(Event.ONCLICK | Event.ONMOUSEUP);
        if (hint != null)
        {
            icon.setTitle(hint);
        }
        return icon;
    }
}
