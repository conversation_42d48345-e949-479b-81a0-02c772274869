package ru.naumen.metainfoadmin.client.eventaction.form.attrtree;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.tree.datasource.AbstractAsyncTreeDataSource;
import ru.naumen.core.client.tree.dto.searcher.TreeSearcher;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.eventaction.form.AbstractEventActionFormPresenter;

import jakarta.inject.Inject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Источник данных для деревьев атрибутов на формах создания/редактирования ДПС
 * Иерархия формируется в {@link AbstractEventActionFormPresenter} в зависимости от параметров ДПС
 * Источник данных извлекает готовую иерархию из  {@link EventAttributesTreeFactoryContext#getHierarchy}
 *
 * <AUTHOR>
 * @since 23.03.2022
 */
public class EventAttributesTreeDataSource extends AbstractAsyncTreeDataSource<DtObject,
        EventAttributesTreeFactoryContext>
{
    private final EventAttributesTreeSearcher searcher;

    @Inject
    public EventAttributesTreeDataSource(@Assisted EventAttributesTreeFactoryContext context,
            EventAttributesTreeSearcher searcher)
    {
        super(context);
        this.searcher = searcher;
        this.searcher.setTreeDataSource(this);
    }

    @Override
    public TreeSearcher<DtObject> getSearcher()
    {
        return searcher;
    }

    @Override
    protected void asyncGetChildren(DtObject parent, AsyncCallback<List<DtObject>> callback)
    {
        List<DtObject> children = getChildrenFromHierarchy(parent);
        callback.onSuccess(children);
    }

    private List<DtObject> getChildrenFromHierarchy(DtObject parent)
    {
        if (0 != searcher.getFoundNumber())
        {
            List<DtObject> children = searcher.getSearchedChilds(parent);
            if (!children.isEmpty())
            {
                return children;
            }
        }
        return getChildrenWithoutSearch(parent);
    }

    protected List<DtObject> getChildrenWithoutSearch(DtObject parent)
    {
        Map<DtObject, List<DtObject>> hierarchy = context.getHierarchy();
        return hierarchy.containsKey(parent) ? hierarchy.get(parent) : new ArrayList<>();
    }
}
