package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.template;

import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.TemplateValidatorFactory;
import ru.naumen.core.client.widgets.properties.FootedTextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dispatch.GetAttrTemplateHelpAction;
import ru.naumen.core.shared.dispatch.GetNamingHelpResponse;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule.GenRuleBindDelegateImpl;

/**
 *
 * <AUTHOR>
 *
 * @param <F>
 */
public class TemplateBindDelegateImpl<F extends ObjectForm> extends GenRuleBindDelegateImpl<F>
{
    private static final int MAX_LENGTH = 256;
    @Inject
    private TemplateValidatorFactory templateValidatorFactory;

    @Inject
    private AttributesMessages amessages;

    @Override
    public void bindProperty(final PropertyContainerContext context, FootedTextBoxProperty property,
            AsyncCallback<Void> callback)
    {
        property.getValueWidget().setMaxLength(MAX_LENGTH);
        super.bindProperty(context, property, callback);
    }

    @Override
    protected void ensureDebugId(FootedTextBoxProperty property)
    {
        property.ensureDebugId("template");
    }

    @Override
    protected String getCaption(PropertyContainerContext context)
    {
        return amessages.template();
    }

    @Override
    protected void onNamingHelp(ClassFqn fqn, String attrTypeCode)
    {
        service.execute(new GetAttrTemplateHelpAction(fqn), new BasicCallback<GetNamingHelpResponse>()
        {
            @Override
            public void handleSuccess(GetNamingHelpResponse response)
            {
                SafeHtmlBuilder sb = new SafeHtmlBuilder();
                List<String> lines = response.getUnits();
                sb.append(SafeHtmlUtils.fromSafeConstant(lines.get(0)));
                appendList(sb, lines, 1, 3).append(SafeHtmlUtils.fromSafeConstant(lines.get(3)));
                appendList(sb, lines, 4, 11);

                dialog.info(messages.help(), sb.toSafeHtml().asString());
            }
        });
    }

    @Override
    protected void setValidationOn(FootedTextBoxProperty property, PropertyContainerContext context)
    {
        validation.validate(property, templateValidatorFactory.create(context));
    }

    private SafeHtmlBuilder appendList(SafeHtmlBuilder sb, List<String> lines, int from, int to)
    {
        sb.append(SafeHtmlUtils.fromSafeConstant("<ul style=\"list-style: outside\">"));
        for (int i = from; i < to; ++i)
        {
            sb.append(SafeHtmlUtils.fromSafeConstant("<li>")).append(SafeHtmlUtils.fromSafeConstant(lines.get(i)))
                    .append(SafeHtmlUtils.fromSafeConstant("</li>"));
        }
        return sb.append(SafeHtmlUtils.fromSafeConstant("</ul>"));
    }
}
