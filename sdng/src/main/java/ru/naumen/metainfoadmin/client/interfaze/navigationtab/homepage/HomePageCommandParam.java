package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsAbstractCommandParam;

/**
 * Командный параметр домашней страницы, позволяет выполнять действие над элементом домашней страницы
 *
 * <AUTHOR>
 * @since 09.01.2023
 */
public class HomePageCommandParam extends NavigationSettingsAbstractCommandParam<HomePageDtObject>
{
    public HomePageCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable FactoryParam.ValueSource<HomePageDtObject> valueSource,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, valueSource, callback);
    }

    public HomePageCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable HomePageDtObject value,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, value, callback);
    }

    public HomePageCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, (HomePageDtObject)null, callback);
    }

    /**
     * Метод переинициализации значений параметра команды.
     * @param value новое значение
     * @param settings настройки навигации
     */
    public void update(@Nullable HomePageDtObject value, DtoContainer<NavigationSettings> settings)
    {
        setValue(value);
        setSettings(settings);
    }

    @Override
    public <V extends FactoryParam<HomePageDtObject, DtoContainer<NavigationSettings>>> V cloneIt()
    {
        return (V)new HomePageCommandParam(getSettings(), getValueSource(), getCallback());
    }
}
