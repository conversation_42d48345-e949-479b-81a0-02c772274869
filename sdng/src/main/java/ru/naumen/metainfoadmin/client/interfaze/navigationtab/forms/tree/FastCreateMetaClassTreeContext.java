package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.tree;

import com.google.common.base.Predicate;

import ru.naumen.core.client.tree.metainfo.DtoMetaClassTreeContext;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * Контекст для конфигурирования дерева метаклассов 
 *
 * <AUTHOR>
 * @since 01.04.2013
 */
public class FastCreateMetaClassTreeContext extends DtoMetaClassTreeContext
{
    public FastCreateMetaClassTreeContext(ClassFqn rootFqn, Predicate<? extends MetaClassLite> hierarchyFilter)
    {
        super(rootFqn, hierarchyFilter);
    }
}
