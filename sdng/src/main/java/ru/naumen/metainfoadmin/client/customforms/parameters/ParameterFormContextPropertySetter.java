package ru.naumen.metainfoadmin.client.customforms.parameters;

import static ru.naumen.metainfo.shared.Constants.*;
import static ru.naumen.metainfoadmin.client.customforms.parameters.ParameterFormContextValues.PERMITTED_TYPES_FQNS;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.ATTRIBUTE;

import com.google.common.collect.Lists;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormContextPropertiesSetter;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Реализация {@link AttributeFormContextPropertiesSetter} для форм добавления и редактирования 
 * параметров настраиваемых форм
 *
 * <AUTHOR>
 * @since 20 апр. 2016 г.
 */
public class ParameterFormContextPropertySetter<F extends ParameterForm>
        implements AttributeFormContextPropertiesSetter<F>
{

    @Override
    public void setContextProperties(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        Boolean computable = context.getPropertyValues().<Boolean> getProperty(AttributeFormPropertyCode.COMPUTABLE);

        IProperties contextValues = context.getContextValues();
        boolean editable = !Constants.NOT_EDITABLE_ATTRIBUTE_TYPES.contains(attrType);
        boolean filteredByScript = Constants.FILTERED_BY_SCRIPT_ATTRIBUTE_TYPES.contains(attrType);
        contextValues.setProperty(AttributeFormPropertyCode.EDITABLE, editable && !computable);
        contextValues.setProperty(AttributeFormPropertyCode.FILTERED_BY_SCRIPT, filteredByScript && !computable);
        contextValues.setProperty(AttributeFormPropertyCode.EDITABLE_IN_LISTS, false);

        context.setEnabled(AttributeFormPropertyCode.REQUIRED, !NOT_REQUIRED_ATTRIBUTE_TYPES.contains(attrType));
        context.setEnabled(AttributeFormPropertyCode.EDITABLE, !NOT_EDITABLE_ATTRIBUTE_TYPES.contains(attrType));
        context.setEnabled(AttributeFormPropertyCode.DEFAULT_VALUE, true);

        boolean hasDefaultValue = !NO_DEFAULT_VALUE_ATTRIBUTE_TYPES.contains(attrType);
        boolean hasOnlyComputableDefaultValue = HAS_ONLY_COMPUTABLE_DEFAULT_VALUE_ATTRIBUTE_TYPES.contains(attrType);
        contextValues.setProperty(AttributeFormContextValues.HAS_DEFAULT_VALUE, hasDefaultValue);
        contextValues.setProperty(AttributeFormContextValues.HAS_ONLY_COMPUTABLE_DEFAULT_VALUE,
                hasOnlyComputableDefaultValue);

        boolean hasComputeAnyCatalogElementsScript = COMPUTABLE_ANY_CATALOG_ELEMENTS_SCRIPT_ATTRIBUTE_TYPES.contains(
                attrType);
        contextValues.setProperty(AttributeFormContextValues.HAS_COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT,
                hasComputeAnyCatalogElementsScript);

        Attribute attribute = contextValues.getProperty(ATTRIBUTE);
        if (attribute != null)
        {
            contextValues.setProperty(PERMITTED_TYPES_FQNS, attribute.getType().getPermittedTypes());
        }
        if (editable && attribute == null)
        {
            context.setProperty(AttributeFormPropertyCode.REQUIRED, false);
        }

        if (attrType.equals(AggregateAttributeType.CODE))
        {
            context.setProperty(AttributeFormPropertyCode.PERMITTED_TYPES,
                    Lists.newArrayList(DtObject.CREATE_FROM_FQN.apply(Employee.FQN),
                            DtObject.CREATE_FROM_FQN.apply(Team.FQN), DtObject.CREATE_FROM_FQN.apply(OU.FQN)));
        }

        if (!hasDefaultValue)
        {
            context.getPropertyControllers().get(AttributeFormPropertyCode.DEFAULT_VALUE).removeProperty();
        }
    }
}
