package ru.naumen.metainfoadmin.client.eventaction;

import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.metainfo.client.eventaction.EventActionsPresenterSettings;
import ru.naumen.metainfoadmin.client.AdminMultiTabPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.jmsqueue.JMSQueueListPresenter;

/**
 * Презентер таба "Действия по событиями" с двумя табами "Действия по событиям" и "Очереди"
 * <AUTHOR>
 * @since 16.02.2021
 **/
public class EventActionsTabsPresenter extends AdminMultiTabPresenterBase<EventActionsPlace>
{
    public static final String EVENT_ACTIONS_TAB_ID = "eventActions";
    public static final String JMS_QUEUES_TAB_ID = "jmsQueues";

    private final EventActionsPresenter eventActionsPresenter;
    private final JMSQueueListPresenter jmsQueueItemsPresenter;
    private final EventActionsPresenterSettings eventActionsPresenterSettings;

    @Inject
    public EventActionsTabsPresenter(AdminTabDisplay display,
            EventBus eventBus,
            EventActionsPresenter eventActionsPresenter,
            JMSQueueListPresenter jmsQueueItemsPresenter,
            EventActionsPresenterSettings eventActionsPresenterSettings)
    {
        super(display, eventBus);
        this.eventActionsPresenter = eventActionsPresenter;
        this.jmsQueueItemsPresenter = jmsQueueItemsPresenter;
        this.eventActionsPresenterSettings = eventActionsPresenterSettings;
    }

    @Override
    protected EventActionsPlace getTabbedPlace(SelectionEvent<Integer> event, String defaultSelectedTab)
    {
        return new EventActionsPlace(defaultSelectedTab);
    }

    @Override
    protected String getTitle()
    {
        return messages.eventActions();
    }

    @Override
    protected void initTabs(AsyncCallback<Void> callback)
    {
        addTabWithContent(messages.eventActions(), EVENT_ACTIONS_TAB_ID, eventActionsPresenter);
        if (eventActionsPresenterSettings.isVisibleJMSQueuesTab())
        {
            addTabWithContent(messages.jmsQueues(), JMS_QUEUES_TAB_ID, jmsQueueItemsPresenter);
        }

        getDisplay().setOneTabShowed(true);

        callback.onSuccess(null);
    }

    @Override
    protected void onUnbind()
    {
        eventActionsPresenter.unbind();
        jmsQueueItemsPresenter.unbind();
    }
}