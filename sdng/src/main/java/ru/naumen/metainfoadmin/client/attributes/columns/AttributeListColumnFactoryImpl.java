/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.columns;

import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import java.util.function.Predicate;

import com.google.inject.Provider;
import com.google.inject.name.Named;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.HTMLWidget;
import ru.naumen.core.client.widgets.grouplist.ButtonColumnFactory;
import ru.naumen.core.client.widgets.grouplist.TextColumnFactory;
import ru.naumen.core.client.widgets.grouplist.WidgetCreator;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfo.shared.Constants.CustomForm;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.HasEditable;
import ru.naumen.metainfo.shared.elements.HasRequired;
import ru.naumen.metainfo.shared.elements.HasUnique;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttrBooleanColumnFactory;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode;

/**
 * Фабрика колонок для списка атрибутов на карточке метакласса
 *
 * <AUTHOR>
 *
 */
@Singleton
public class AttributeListColumnFactoryImpl implements AttributeListColumnFactory
{
    /**
     * Предикат отображающий или скрывающий кнопку с действием, полагаясь 
     * на доступность действия
     *
     * <AUTHOR>
     * @since 13 мая 2016 г.
     */
    private static class CommandPossiblePredicate implements Predicate<Attribute>
    {
        private final Context context;
        private final BaseCommand<Attribute, Void> command;

        public CommandPossiblePredicate(Context context, BaseCommand<Attribute, Void> command)
        {
            this.context = context;
            this.command = command;
        }

        @Override
        public boolean test(Attribute attr)
        {
            return command.isPossible(attr) && hasPermission(context, PermissionType.EDIT, attr);
        }
    }

    private static class DeleteButtonColumnPredicate implements Predicate<Attribute>
    {
        private final Context context;
        private final BaseCommand<Attribute, Void> command;

        public DeleteButtonColumnPredicate(Context context, BaseCommand<Attribute, Void> command)
        {
            this.context = context;
            this.command = command;
        }

        @Override
        public boolean test(Attribute attr)
        {
            if (attr == null)
            {
                return false;
            }
            return hasPermission(context, PermissionType.DELETE, attr)
                   && !attr.isHardcoded() && context.getMetainfo().getFqn().equals(attr.getDeclaredMetaClass())
                   && !isUserParentAttr(attr) && command.isPossible(attr);
        }

        /**
         * @param attr
         * @return признак родительского атрибута в позьзовательском классе (его удалять нельзя)
         */
        private boolean isUserParentAttr(Attribute attr)
        {
            return !context.getMetainfo().isHardcoded() && !CustomForm.FQN.isSameClass(context.getMetainfo().getFqn())
                   && Constants.PARENT_ATTR.equalsIgnoreCase(attr.getCode());
        }
    }

    private static class EditButtonColumnPredicate implements Predicate<Attribute>
    {
        private final Context context;

        public EditButtonColumnPredicate(Context context)
        {
            this.context = context;
        }

        @Override
        public boolean test(@Nullable Attribute attr)
        {
            return attr != null && hasPermission(context, PermissionType.EDIT, attr);
        }
    }

    @Inject
    AttrBooleanColumnFactory booleanColumnFactory;
    @Inject
    TextColumnFactory<Attribute, HTMLWidget> textColumnFactory;
    @Inject
    ButtonColumnFactory<Attribute> buttonColumnFactory;
    @Inject
    @Named(AttributeColumnCode.DEFAULT_VALUE)
    Provider<WidgetCreator<Attribute>> defaultColumnProvider;

    @Inject
    public AttributeListColumnFactoryImpl()
    {
    }

    @Override
    public WidgetCreator<Attribute> get(String code, RegistrationContainer registrationContainer)
    {
        if (code.equals(AttributeColumnCode.CODE))
        {
            return textColumnFactory.create(code, HasCode.CODE_EXTRACTOR, null);
        }
        if (code.equals(AttributeColumnCode.DEFAULT_VALUE))
        {
            return defaultColumnProvider.get();
        }
        if (code.equals(AttributeColumnCode.EDITABLE))
        {
            return booleanColumnFactory.create(code, HasEditable.EDITABLE_PREDICATE, null);
        }
        if (code.equals(AttributeColumnCode.REQUIRED))
        {
            return booleanColumnFactory.create(code, HasRequired.REQUIRED_PREDICATE, null);
        }
        if (code.equals(AttributeColumnCode.TITLE))
        {
            return textColumnFactory.create(code, ITitled.TITLE_EXTRACTOR, null);
        }
        if (code.equals(AttributeColumnCode.TYPE))
        {
            return textColumnFactory.create(code, (attr) ->
            {
                if (attr == null || attr.getType() == null)
                {
                    return null;
                }
                if (attr.getType().isAttributeOfRelatedObject())
                {
                    return AttributeOfRelatedObjectSettings.CODE;
                }
                return attr.getType().getCode();
            }, null);
        }
        if (code.equals(AttributeColumnCode.UNIQUE))
        {
            return booleanColumnFactory.create(code, HasUnique.UNIQUE_PREDICATE, null);
        }
        throw new IllegalArgumentException("Attribute list column with code " + code + " is not registered!");
    }

    @SuppressWarnings("unchecked")
    @Override
    public WidgetCreator<Attribute> getButtonColumn(String code, BaseCommand<?, ?> command, Context context)
    {
        if (AttributeColumnCode.DELETE_BUTTON.equals(code))
        {
            return buttonColumnFactory.create(code, (BaseCommand<Attribute, Void>)command, IconCodes.DELETE,
                    new DeleteButtonColumnPredicate(context, (BaseCommand<Attribute, Void>)command));
        }
        if (AttributeColumnCode.EDIT_BUTTON.equals(code))
        {
            return buttonColumnFactory.create(code, (BaseCommand<Attribute, Void>)command, IconCodes.EDIT,
                    new EditButtonColumnPredicate(context));
        }
        if (AttributeColumnCode.SHOW_USAGE_BUTTON.equals(code))
        {
            return buttonColumnFactory.create(code, (BaseCommand<Attribute, Void>)command, IconCodes.USE_IN_SETTINGS,
                    attribute -> true);
        }
        if (AttributeColumnCode.MOVE_UP_BUTTON.equals(code))
        {
            return buttonColumnFactory.create(code, (BaseCommand<Attribute, Void>)command, IconCodes.UP,
                    new CommandPossiblePredicate(context, (BaseCommand<Attribute, Void>)command));
        }
        if (AttributeColumnCode.MOVE_DOWN_BUTTON.equals(code))
        {
            return buttonColumnFactory.create(code, (BaseCommand<Attribute, Void>)command, IconCodes.DOWN,
                    new CommandPossiblePredicate(context, (BaseCommand<Attribute, Void>)command));
        }
        throw new IllegalArgumentException("Attribute list column with code " + code + " is not registered!");
    }
}
