package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления булевского свойства, который скрывает или показывает его в зависимости от значения свойства
 * "Вычислимый"
 * <AUTHOR>
 * @since 05.07.2012
 *
 */
public class BooleanComputableDependentRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Boolean computable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        Boolean determinable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);
        Boolean definableByTemplate = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPOSITE);
        boolean alwaysEditable = Constants.ALWAYS_EDITABLE_ATTRIBUTE_TYPES.contains(
                context.getPropertyValues().<String> getProperty(AttributeFormPropertyCode.ATTR_TYPE));

        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        String attrCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE);
        if (SuperUser.FQN.isSameClass(metaClass.getFqn())
            && (SuperUser.PERMISSION_EXPIRATION_DATE.equals(attrCode) || SuperUser.PASSWORD.equals(attrCode)))
        {
            callback.onSuccess(false);
            return;
        }

        callback.onSuccess(!alwaysEditable && !definableByTemplate && !computable && !determinable
                           && !GenerationRuleDelegateHelper.hideDependenceField(context));
    }
}
