package ru.naumen.metainfoadmin.client.fastlink.settings.forms;

import static ru.naumen.metainfo.shared.Constants.GLOBAL_DOMAIN_CODE;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityProfilesAction;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityProfilesResponse;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSettingUtils;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsGinModule.FastLinkSettingFormPropertyCode;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 01.03.18
 *
 */
public class ProfileRefreshDelegateImpl
        implements AttributeFormPropertyDelegateRefresh<ObjectForm, Collection<SelectItem>, MultiSelectBoxProperty>
{
    @Inject
    private DispatchAsync dispatch;

    @Inject
    private MetainfoUtils metainfoUtils;

    @Inject
    protected AdminMetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, MultiSelectBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Collection<DtObject> mentionTypes = context.getPropertyValues()
                .<Collection<DtObject>> getProperty(FastLinkSettingFormPropertyCode.MENTION_TYPES);
        Collection<String> selectedProfiles = context.getPropertyValues()
                .<Collection<String>> getProperty(FastLinkSettingFormPropertyCode.PROFILES);

        if (mentionTypes != null && !mentionTypes.isEmpty())
        {
            ClassFqn firstFqn = DtObject.FQN_EXTRACTOR.apply(mentionTypes.iterator().next());
            getProfiles(context, property, callback, mentionTypes, selectedProfiles, firstFqn);
        }
        else
        {
            getProfiles(context, property, callback, selectedProfiles);
        }
    }

    private void fillProfileProperty(PropertyContainerContext context,
            MultiSelectBoxProperty property, Collection<String> selectedProfiles, List<Profile> profiles)
    {
        property.getValueWidget().clear();
        profiles = profiles.stream()
                .filter(p -> p.getRoles().stream()
                        .allMatch(r -> GLOBAL_DOMAIN_CODE.equals(r.getDomainCode())))
                .collect(Collectors.toList());
        metainfoUtils.sort(profiles);
        profiles.forEach(p -> property.getValueWidget().addItem(p.getTitle(), p.getCode()));

        Collection<String> valueToSet = profiles.stream()
                .filter(p -> selectedProfiles.contains(p.getCode())).map(Profile::getCode)
                .collect(Collectors.toList());
        property.trySetObjValue(valueToSet);
        context.getPropertyValues().setProperty(FastLinkSettingFormPropertyCode.PROFILES,
                valueToSet);
    }

    /**
     * Заполенние поля профили, если заполнено поле mentionTypes
     */
    private void getProfiles(PropertyContainerContext context, MultiSelectBoxProperty property,
            AsyncCallback<Boolean> callback, Collection<DtObject> mentionTypes, Collection<String> selectedProfiles,
            ClassFqn firstFqn)
    {
        metainfoService.getDescendantClasses(firstFqn.fqnOfClass(), true, new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> metaClasses)
            {
                ClassFqn commonParentFqn = FastLinkSettingUtils.getLeastCommonParent(metaClasses,
                        mentionTypes.stream().map(DtObject::getMetaClass).collect(Collectors.toList()));
                dispatch.execute(new GetSecurityProfilesAction(commonParentFqn, false),
                        new BasicCallback<GetSecurityProfilesResponse>()
                        {
                            @Override
                            protected void handleSuccess(GetSecurityProfilesResponse value)
                            {
                                List<Profile> profiles = value.get();
                                fillProfileProperty(context, property, selectedProfiles, profiles);
                                callback.onSuccess(true);
                            }

                        });
            }
        });
    }

    /**
     * Заполенние поля профили, если не заполнено поле mentionTypes
     */
    private void getProfiles(PropertyContainerContext context, MultiSelectBoxProperty property,
            AsyncCallback<Boolean> callback, Collection<String> selectedProfiles)
    {
        metainfoService.getSecurityProfiles(new BasicCallback<List<Profile>>()
        {
            @Override
            protected void handleFailure(Throwable t)
            {
                callback.onFailure(t);
            }

            @Override
            protected void handleSuccess(List<Profile> profiles)
            {
                fillProfileProperty(context, property, selectedProfiles, profiles);
                callback.onSuccess(true);
            }
        });
    }
}
