package ru.naumen.metainfoadmin.client;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.content.AbstractContext;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ErrorAndAttentionMessageHandler;
import ru.naumen.metainfo.shared.elements.MetaClass;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.SimpleEventBus;

/**
 * Реализация по умолчанию для {@link Context}
 *
 * <AUTHOR>
 *
 */
public class DefaultContext extends AbstractContext
{
    public DefaultContext(Context parentContext, MetaClass metaClass)
    {
        super(parentContext, metaClass, new SimpleEventBus(), parentContext.getErrorAndAttentionMsgHandler());
    }

    public DefaultContext(Context parentContext, MetaClass metaClass, EventBus eventBus,
            ErrorAndAttentionMessageHandler errorAndAttentionMsgHandler)
    {
        super(parentContext, metaClass, eventBus, errorAndAttentionMsgHandler);
    }

    public DefaultContext(@Nullable MetaClass metaClass)
    {
        super(metaClass, new SimpleEventBus(), null);
    }

    public DefaultContext(MetaClass metaClass, ErrorAndAttentionMessageHandler errorMessageHandler)
    {
        super(metaClass, new SimpleEventBus(), errorMessageHandler);
    }
}
