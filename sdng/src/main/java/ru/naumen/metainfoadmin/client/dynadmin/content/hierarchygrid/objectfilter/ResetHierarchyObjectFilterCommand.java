package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.objectfilter;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfoadmin.shared.dynadmin.HierarchyItemSettingsContext;

/**
 * Команда сброса ограничения содержимого для уровня иерархического списка.
 * <AUTHOR>
 * @since Jan 17, 2020
 */
public class ResetHierarchyObjectFilterCommand
        extends BaseCommandImpl<HierarchyItemSettingsContext, HierarchyItemSettingsContext>
{
    private final MetainfoModificationServiceAsync metainfoServiceAsync;

    @Inject
    public ResetHierarchyObjectFilterCommand(@Assisted HierarchyObjectFilterCommandParam param,
            MetainfoModificationServiceAsync metainfoServiceAsync)
    {
        super(param);
        this.metainfoServiceAsync = metainfoServiceAsync;
    }

    @Override
    public void execute(CommandParam<HierarchyItemSettingsContext, HierarchyItemSettingsContext> param)
    {
        HierarchyObjectFilterCommandParam commandParam = (HierarchyObjectFilterCommandParam)getParam();
        String itemCode = param.getValue().getCode();
        ListFilter currentFilter = param.getValue().getObjectFilter();
        if (null != commandParam.getContent().getObjectFilters().remove(itemCode))
        {
            metainfoServiceAsync.saveUI(
                    commandParam.getContext().getMetainfo().getFqn(),
                    commandParam.getContext().getRootContent(),
                    null,
                    commandParam.getContext().getCode(),
                    commandParam.getContent(),
                    false, new BasicCallback<Void>()
                    {
                        @Override
                        protected void handleFailure(Throwable t)
                        {
                            if (!currentFilter.getElements().isEmpty())
                            {
                                commandParam.getContent().getObjectFilters().put(itemCode, currentFilter);
                            }
                            else
                            {
                                commandParam.getContent().getObjectFilters().remove(itemCode);
                            }
                            super.handleFailure(t);
                        }

                        @Override
                        protected void handleSuccess(Void value)
                        {
                            param.getValue().setObjectFilter(null);
                            param.getCallback().onSuccess(param.getValue());
                        }
                    });
        }
    }

    @Nullable
    @Override
    public FontIconDisplay<HierarchyItemSettingsContext> getFontIcon()
    {
        FontIconDisplay<HierarchyItemSettingsContext> fontIcon = super.getFontIcon();
        if (null == fontIcon)
        {
            return null;
        }
        fontIcon.setTitle(messages.reset());
        return fontIcon;
    }

    @Override
    public boolean isPossible(Object input)
    {
        return input instanceof HierarchyItemSettingsContext
               && !((HierarchyItemSettingsContext)input).getObjectFilter().getElements().isEmpty();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DEL;
    }
}
