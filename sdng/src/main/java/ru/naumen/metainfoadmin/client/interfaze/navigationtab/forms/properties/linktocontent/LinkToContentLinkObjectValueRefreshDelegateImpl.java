package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import static ru.naumen.metainfo.shared.Constants.LinkObjectType.CURRENT_USER;

import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.shared.ui.ObjectList;

/**
 * Делегат обновления свойства "Объект связи"
 *
 * <AUTHOR>
 * @since 20.10.2020
 */
@Singleton
public class LinkToContentLinkObjectValueRefreshDelegateImpl implements PropertyDelegateRefresh<SelectItem,
        ListBoxWithEmptyOptProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean isLinkToContentType = LinkToContentMetaClassPropertiesProcessor.isLinkToContentElementType(context);
        boolean isLinkToList = LinkToContentMetaClassPropertiesProcessor.isLinkToListElementType(context);
        property.setValidationMarker(isLinkToList);

        String contentTypeStr = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);

        boolean isVisible = isLinkToContentType
                            && !ObjectList.class.getSimpleName().equals(contentTypeStr);

        if (isVisible)
        {
            if (context.getPropertyValues().getProperty(MenuItemLinkToContentCode.LINK_OBJECT) == null)
            {
                context.getPropertyValues().setProperty(MenuItemLinkToContentCode.LINK_OBJECT, CURRENT_USER);
                property.trySetObjValue(CURRENT_USER);
            }
        }
        else
        {
            context.getPropertyControllers().get(MenuItemLinkToContentCode.LINK_OBJECT).unbindValidators();
        }

        callback.onSuccess(isVisible);
    }
}
