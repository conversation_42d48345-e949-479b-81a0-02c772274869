package ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.INTERFACE_AND_NAVIGATION;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.activity.PrevLinkContainer;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.GetNavigationMenuItemAction;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsPlace;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationMenuItemPlace;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsMenuItemAbstractCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.MenuItemChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.MenuItemChangedHandler;

/**
 * <AUTHOR>
 * @since 03 дек. 2013 г.
 *
 */
public abstract class NavigationMenuItemPresenter<M extends IMenuItem,
        C extends NavigationMenuItemContext<M>,
        P extends NavigationSettingsMenuItemAbstractCommandParam<M>>
        extends AdminTabPresenter<NavigationMenuItemPlace<M>>
        implements MenuItemChangedHandler
{
    @Inject
    private PrevLinkContainer prevLinkContainer;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private I18nUtil i18nUtil;

    private final NavigationSettingsMessages navigationSettingsMessages;
    private final DispatchAsync dispatch;
    private final NavigationMenuItemAttributesPresenter<M, C, P> attributesPresenter;

    private C context;

    protected NavigationMenuItemPresenter(AdminTabDisplay display, EventBus eventBus,
            NavigationSettingsMessages navigationSettingsMessages, DispatchAsync dispatch,
            NavigationMenuItemAttributesPresenter<M, C, P> attributesPresenter)
    {
        super(display, eventBus);
        this.navigationSettingsMessages = navigationSettingsMessages;
        this.dispatch = dispatch;
        this.attributesPresenter = attributesPresenter;
    }

    @Override
    public void init(NavigationMenuItemPlace<M> place)
    {
        super.init(place);
        if (place.getMenuItem() != null)
        {
            context = getContext(place.getMenuItem(), place.getSettings());
        }
    }

    protected abstract C getContext(M menuItem, DtoContainer<NavigationSettings> settings);

    @Override
    public void onMenuItemChanged(MenuItemChangedEvent event)
    {
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        if (context != null)
        {
            getDisplay().setTitle(
                    navigationSettingsMessages.menuElementWithTitle(i18nUtil.getLocalizedTitle(context.getItem())));
        }
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        registerHandler(eventBus.addHandler(MenuItemChangedEvent.getType(), this));
        Place previousPlace = prevLinkContainer.getPreviousPlace();
        String backLinkCaption = null == previousPlace || previousPlace instanceof InterfaceSettingsPlace
                ? navigationSettingsMessages.toTopMenuElements()
                : cmessages.back();
        prevPageLinkPresenter.bind(backLinkCaption, new InterfaceSettingsPlace("navigation"));
        if (context == null)
        {
            dispatch.execute(new GetNavigationMenuItemAction(getPlace().getCode()),
                    new BasicCallback<SimpleResult<DtoContainer<NavigationSettings>>>(getDisplay())
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<DtoContainer<NavigationSettings>> response)
                        {
                            super.handleSuccess(response);
                            context = getContext((M)response.get().get().findMenuItem(getPlace().getCode()),
                                    response.get());
                            hasContext();
                            refreshDisplay();
                        }
                    });
        }
        else
        {
            hasContext();
        }
    }

    private void hasContext()
    {
        attributesPresenter.init(context);
        addContent(attributesPresenter, "attrs");
        attributesPresenter.revealDisplay();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return INTERFACE_AND_NAVIGATION;
    }
}