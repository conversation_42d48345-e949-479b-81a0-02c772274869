package ru.naumen.metainfoadmin.client.dynadmin;

import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.google.common.base.Functions;
import com.google.common.base.Preconditions;
import com.google.common.collect.Collections2;
import com.google.common.collect.Maps;
import com.google.gwt.user.client.rpc.IsSerializable;
import com.google.gwt.user.client.ui.HasHTML;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.inject.Provider;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.BatchAction;
import net.customware.gwt.dispatch.shared.BatchAction.OnException;
import net.customware.gwt.dispatch.shared.BatchResult;
import ru.naumen.admin.client.permission.AdminPermissionCheckServiceSync;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesFullTitleAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesResponse;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityProfilesAction;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityProfilesResponse;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.SelectContacts;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.ExamplePresentationFactory;

@Singleton
public class ContentUtils
{
    /**
     * Максимальная длина названий контентов
     */
    public static final int MAX_TITLE_LENGTH = 255;
    /**
     * Максимальная длина кода для контента
     */
    public static final int MAX_CODE_LENGTH = 255;

    private static final class AttributesForContactsFilter implements com.google.common.base.Predicate<Attribute>,
            IsSerializable
    {
        private final ClassFqn declaredCls;

        public AttributesForContactsFilter(ClassFqn declaredCls)
        {
            this.declaredCls = declaredCls;
        }

        @SuppressWarnings("deprecation")
        @edu.umd.cs.findbugs.annotations.SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
        @Override
        public boolean apply(Attribute input)
        {
            Preconditions.checkNotNull(input);
            boolean attrDeclaredInMetaclass = input.getDeclaredMetaClass().equals(declaredCls);
            boolean attrIsTeamMembers = input.getCode().equals(Constants.Team.MEMBERS);
            boolean attTypeIsLink = ru.naumen.metainfo.shared.Constants.LINK_ATTRIBUTE_TYPES.contains(input.getType()
                    .getCode());

            if (attrDeclaredInMetaclass && !attrIsTeamMembers && attTypeIsLink)
            {
                ClassFqn relFqn = input.getType().<ObjectAttributeType> cast().getRelatedMetaClass();
                return relFqn.isSameClass(Constants.Employee.FQN);
            }
            return false;
        }
    }

    private static class MetaclassAttributesExtractor implements Function<MetaClass, Collection<Attribute>>
    {
        @Override
        public Collection<Attribute> apply(MetaClass input)
        {
            return Collections2.filter(input.getAttributes(), new AttributesForContactsFilter(input.getFqn()));
        }
    }

    @Inject
    private ExamplePresentationFactory presentationFactory;
    @Inject
    private PropertyCreator propertyCreator;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private AdminPermissionCheckServiceSync adminPermissionCheckServiceSync;

    private AdminDialogMessages messages;
    private DispatchAsync dispatch;
    private Provider<SelectListProperty<Collection<String>, Collection<SelectItem>>> profilesProvider;
    private Provider<SelectListProperty<Collection<String>, Collection<SelectItem>>> contactAttributesProvider;

    @SuppressWarnings("unchecked")
    public SelectListProperty<Collection<String>, Collection<SelectItem>> createContactAttributesProperty(
            @Nullable final SelectContacts content)
    {
        final SelectListProperty<Collection<String>, Collection<SelectItem>> property = contactAttributesProvider
                .get();
        property.setCaption(messages.additionalContacts());
        Predicate<MetaClassLite> metaclassFilter = MetaClassFilters.or(MetaClassFilters.isSomeClass(Constants.OU.FQN),
                MetaClassFilters.isSomeClass(Constants.Team.FQN));

        dispatch.execute(new BatchAction(OnException.ROLLBACK, new GetMetaClassesAction(metaclassFilter),
                new GetMetaClassesFullTitleAction(metaclassFilter)), new BasicCallback<BatchResult>()
        {
            @Override
            protected void handleSuccess(BatchResult value)
            {
                ArrayList<MetaClass> metaclasses = value.getResult(0, GetMetaClassesResponse.class).get();
                HashMap<ClassFqn, String> titles = (HashMap<ClassFqn, String>)value.getResult(1,
                                SimpleResult.class)
                        .get();
                List<Attribute> attrs = CollectionUtils.convolve(metaclasses,
                        new MetaclassAttributesExtractor());
                metainfoUtils.sort(attrs);

                for (Attribute attr : attrs)
                {
                    MetaClassLite attrMetaClass = attr.getMetaClassLite();
                    String itemCode = attr.getCode() + "-" + attrMetaClass.getFqn().toString();
                    property.<MultiSelectCellList<String>> getValueWidget().addItem(
                            getAttrFullTitle(titles, attrMetaClass, attr), itemCode);
                }
                if (null != content)
                {
                    property.trySetObjValue(Collections2.transform(content
                            .getAttributes(), Functions
                            .toStringFunction()));
                }
            }
        });
        return property;
    }

    /**
     * Создает свойство выбора профилей пользователей для которых будет отображаться контент для формы
     * добавления контента.
     */
    public Property<Collection<SelectItem>> createProfilesProperty(@Nullable ClassFqn fqn, boolean isVersioning,
            @Nullable ReadyState readyState)
    {
        return createProfilesProperty(fqn, null, isVersioning, readyState, null);
    }

    /**
     * Создает свойство выбора профилей пользователей для которых будет отображаться контент для формы
     * добавления контента.
     */
    public Property<Collection<SelectItem>> createProfilesProperty(@Nullable ClassFqn fqn, boolean isVersioning)
    {
        return createProfilesProperty(fqn, null, isVersioning, null, null);
    }

    public Property<Collection<SelectItem>> createProfilesProperty(@Nullable ClassFqn fqn,
            @Nullable final Content content, boolean isVersioning, @Nullable Predicate<Profile> profileFilter)
    {
        return createProfilesProperty(fqn, content, isVersioning, null, profileFilter);
    }

    /**
     * Создает свойство выбора профилей пользователей для которых будет отображаться контент для формы
     * добавления контента.
     */
    public Property<Collection<SelectItem>> createProfilesProperty(@Nullable ClassFqn fqn,
            @Nullable final Content content, boolean isVersioning)
    {
        return createProfilesProperty(fqn, content, isVersioning, null, null);
    }

    /**
     * Создает свойство выбора профилей пользователей для которых будет отображаться контент для формы
     * редактирования контента .
     *
     * @param fqn класса
     * @param content если null - то создается для формы добавления
     * @param isVersioning профиль для версионирования
     * @param readyState ReadyState
     * @return проперти
     */
    public SelectListProperty<Collection<String>, Collection<SelectItem>> createProfilesProperty(@Nullable ClassFqn fqn,
            @Nullable final Content content, boolean isVersioning, @Nullable ReadyState readyState,
            @Nullable Predicate<Profile> profileFilter)
    {
        if (null != readyState)
        {
            readyState.notReady();
        }
        final SelectListProperty<Collection<String>, Collection<SelectItem>> property = profilesProvider.get();
        property.setCaption(isVersioning ? messages.contentVersProfiles() : messages.contentProfiles());

        dispatch.execute(new GetSecurityProfilesAction(fqn, isVersioning),
                new BasicCallback<GetSecurityProfilesResponse>()
                {
                    @Override
                    protected void handleSuccess(GetSecurityProfilesResponse value)
                    {
                        List<Profile> list = null == profileFilter ? value.get()
                                : value.get().stream().filter(profileFilter).collect(Collectors.toList());
                        metainfoUtils.sort(list);
                        MultiSelectCellList<String> widget = property.getValueWidget();
                        for (Profile p : list)
                        {
                            widget.addItem(p.getTitle(), p.getCode());
                        }
                        if (null != content)
                        {
                            property.trySetObjValue(isVersioning ? content.getVersProfiles() : content.getProfiles());
                        }
                        if (null != readyState)
                        {
                            readyState.ready();
                        }
                    }
                });

        return property;
    }

    public void fillProperties(HasProperties hasProperties, Map<Attribute, IsWidget> attributes, boolean view,
            boolean requredMarker, @Nullable Set<String> disabledAttributes)
    {
        hasProperties.clearProperties();
        for (Entry<Attribute, IsWidget> attrEntry : attributes.entrySet())
        {
            Attribute attr = attrEntry.getKey();
            String viewPresentationCode = attr.getViewPresentation().getCode();
            String presentationCode = view ? viewPresentationCode : attr.getEditPresentation().getCode();
            PresentationContext presentationContext = new PresentationContext(attr);
            presentationContext.setPresentationCode(presentationCode);
            IsWidget widget = presentationFactory.createExampleWidget(presentationContext);
            final boolean isWide = view
                                   && ru.naumen.metainfo.shared.Constants.Presentations.WIDE_ATTR_CODES.contains(
                    viewPresentationCode);
            Property<?> property = propertyCreator.create(attr.getTitle(), widget, isWide);
            property.setToolPanelWidget(attrEntry.getValue());
            property.setValidationMarker(requredMarker && (attr.isRequired() || attr.isRequiredInInterface()));
            if (!view && Presentations.BOOL_CHECKBOX.equals(attr.getEditPresentation().getCode()))
            {
                property.getValueWidget().asWidget().setStyleName(WidgetResources.INSTANCE.all().warningItem(),
                        null != disabledAttributes && disabledAttributes.contains(attr.getCode()));
                ((HasHTML)property.getValueWidget()).setText(attr.getTitle());
            }
            else
            {
                property.getCaptionWidget().asWidget().setStyleName(WidgetResources.INSTANCE.all().warningItem(),
                        null != disabledAttributes && disabledAttributes.contains(attr.getCode()));
            }

            if (attr.isHiddenAttrCaption())
            {
                property.getCaptionWidget().asWidget()
                        .setStyleName(WidgetResources.INSTANCE.form().hiddenAttrCaption());
            }
            DebugIdBuilder.ensureDebugId(property, attr.getCode());
            hasProperties.add(property);
        }
    }

    /**
     * Получает коллекцию значений атрибутов по умолчанию
     * @param attrs коллекция атрибутов
     * @return Карта<Атрибут, свойства>
     */
    public Map<Attribute, Property<?>> getExampleAttributeValues(List<Attribute> attrs)
    {
        Map<Attribute, Property<?>> properties = Maps.newLinkedHashMap();
        for (Attribute attr : attrs)
        {
            String viewPresentationCode = attr.getViewPresentation().getCode();
            PresentationContext presentationContext = new PresentationContext(attr);
            presentationContext.setPresentationCode(viewPresentationCode);
            IsWidget widget = presentationFactory.createExampleWidget(presentationContext);
            final boolean isWide =
                    ru.naumen.metainfo.shared.Constants.Presentations.WIDE_ATTR_CODES.contains(viewPresentationCode);
            Property<?> property = propertyCreator.create(attr.getTitle(), widget, isWide);
            DebugIdBuilder.ensureDebugId(property, attr.getCode());
            properties.put(attr, property);
        }
        return properties;
    }

    public boolean hasEditPermission(Object content, UIContext context)
    {
        return content instanceof Content && hasPermission(context, PermissionType.EDIT, (Content)content);
    }

    /**
     * Проверяет, находится ли контент (либо один из прямых потомков) в режиме редактирования верстки
     * @param contentToCheck контент для проверки
     * @param context контекст проверки
     * @return true если контент в режиме редактирования верстки
     */
    public boolean isContentInEditMode(Object contentToCheck, UIContext context)
    {
        if (contentToCheck instanceof Form
            && ((Form)contentToCheck).getLayout().equals(context.getContentInLayoutEditMode()))
        {
            return true;
        }

        if (contentToCheck instanceof TabBar)
        {
            for (Tab tab : ((TabBar)contentToCheck).getTab())
            {
                if (tab.getLayout().equals(context.getContentInLayoutEditMode()))
                {
                    return true;
                }
            }
        }

        return false;
    }

    @Inject
    public void setContactAttributesProvider(
            @Named(PropertiesGinModule.MULTI_SELECT_BOX)
            Provider<SelectListProperty<Collection<String>, Collection<SelectItem>>> contactAttributesProvider)
    {
        this.contactAttributesProvider = contactAttributesProvider;
    }

    @Inject
    public void setDispatch(DispatchAsync dispatch)
    {
        this.dispatch = dispatch;
    }

    @Inject
    public void setMessages(AdminDialogMessages messages)
    {
        this.messages = messages;
    }

    @Inject
    public void setProfilesProvider(
            @Named(PropertiesGinModule.MULTI_SELECT_BOX)
            Provider<SelectListProperty<Collection<String>, Collection<SelectItem>>> profilesProvider)
    {
        this.profilesProvider = profilesProvider;
    }

    public void updateObjectListPresentation(RelationsAttrTreeObject selectedAttribute,
            PropertyRegistration<SelectItem> presentationListPR, Property<String> formCaption)
    {
        Attribute attribute = selectedAttribute == null ? null : selectedAttribute.getAttribute();
        boolean computable = null != attribute && Boolean.TRUE.equals(attribute.isComputable());
        presentationListPR.setEnabled(!computable);
        formCaption.setEnabled(!computable);
        if (computable)
        {
            Property<SelectItem> property = presentationListPR.getProperty();
            if (property != null)
            {
                ((ListBoxProperty)property).trySetObjValue(PresentationType.DEFAULT.getCode());
            }
        }
    }

    private String getAttrFullTitle(HashMap<ClassFqn, String> titles, MetaClassLite cls, Attribute attr)
    {
        return attr.getTitle() + " (" + titles.get(cls.getFqn()) + ")";
    }
}
