package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.Constants.AbstractCasedBO;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

import com.google.gwt.user.client.rpc.AsyncCallback;

public class UseGenerationRuleBindDelegateImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<Boolean, BooleanCheckBoxProperty> implements
        AttributeFormPropertyDelegateBind<F, Boolean, BooleanCheckBoxProperty>
{
    @Inject
    CommonMessages cmessages;

    @Override
    public void bindProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Void> callback)
    {
        Attribute attr = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        property.setCaption(getTitle(attr));
        property.ensureDebugId("useGenRule");
        super.bindProperty(context, property, callback);
    }

    private String getTitle(Attribute attr)
    {
        if (AbstractCasedBO.NUMBER.equals(attr.getCode()))
        {
            return cmessages.useNumberRule();
        }
        return cmessages.useNameRule();
    }
}
