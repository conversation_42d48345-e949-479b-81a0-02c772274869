package ru.naumen.metainfoadmin.client.eventaction.form.creator;

import static ru.naumen.core.client.widgets.id.DebugIdBuilder.ensureDebugId;
import static ru.naumen.core.shared.Constants.PARENT_ATTR;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.ConcurrentCallbacks;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.common.ObjectService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicNestedCallbacks;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.NotificationRecipient;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithoutFolders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactory;
import ru.naumen.core.client.tree.dto.impl.recipient.DtoTreeFactoryNotificationRecipientContext;
import ru.naumen.core.client.tree.selection.FilteredMultiSelectionModel;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.validation.TextRtfNotEmptyValidator;
import ru.naumen.core.client.widgets.FormCss;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.RichTextWidgetBase;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.DtoTree;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.elements.sec.Role.Type;
import ru.naumen.metainfo.shared.eventaction.EventActionWithRecipients;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.eventaction.PossibleSkipIfUserHasActiveSessionPredicate;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;
import ru.naumen.metainfoadmin.client.widgets.script.component.ScriptComponentEditWidget;

/**
 * Общий инициализатор свойств для настройки событий с получателями 
 * (уведомления, оповещения)
 * <p>Отображаются на форме добавления/редактирования</p>
 *
 * <AUTHOR>
 * @param <T>
 * @since 20.10.2015
 */
public abstract class EventActionWithRecipientsFormPropertiesCreator<T extends EventActionWithRecipients>
        extends AbstractEventActionFormPropertiesCreator<T>
{

    protected static final FormCss formStyle = WidgetResources.INSTANCE.form();

    protected static final Function<DtObject, String> UUID_EXTRACTOR = input ->
    {
        Preconditions.checkNotNull(input);
        return input.getUUID();
    };

    protected String event = null;

    @Inject
    protected NotEmptyValidator notEmptyValidator;
    @Inject
    protected TextRtfNotEmptyValidator textRtfNotEmptyValidator;
    @Inject
    private NotEmptyCollectionValidator<Collection<DtObject>> notEmptyCollectionValidator;

    @Inject
    protected AdminMetainfoServiceAsync metainfoService;
    @Inject
    private DtoTreeFactory<Collection<DtObject>, NotificationRecipient, WithoutFolders,
            DtoTreeFactoryNotificationRecipientContext> treeFactory;
    @Inject
    private ObjectService objectService;
    @Inject
    protected MetainfoUtils metainfoUtils;

    @Inject
    @Named(PropertiesGinModule.SCRIPT_COMPONENT_EDIT)
    private Property<ScriptDto> script;

    protected Property<Collection<DtObject>> recipients;

    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> excludeAuthor;

    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> html;

    @Inject
    private PossibleSkipIfUserHasActiveSessionPredicate skipIfUserHasActiveSessionPredicate;

    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> skipIfUserHasActiveSession;

    // Текст, отправляемый получателям
    protected Property<String> message;

    private ValidationUnit<Collection<DtObject>> recipientsValidation;
    private HandlerRegistration domHandlerRegistration;

    protected EventActionFormDisplay formDisplay;

    @Override
    public void init(@Nullable EventActionWithScript eventAction, Property<SelectItem> eventProperty)
    {
        super.init(eventAction, eventProperty);
        skipIfUserHasActiveSession.setCaption(messages.skipIfUserHasActiveSession());
        skipIfUserHasActiveSession.setDescription(" ");
        skipIfUserHasActiveSession.getDescriptionWidget().asWidget().removeStyleName("display");
        skipIfUserHasActiveSession.getDescriptionWidget().asWidget().setVisible(false);
    }

    @Override
    protected void setActionProperties(T action)
    {
        action.setScript(script.getValue().getCode());
        eventAction.putScript(script.getValue());
        Collection<DtObject> roles = selectRoles(recipients.getValue());
        Collection<DtObject> recipientsValues = CollectionUtils.subtract(recipients.getValue(), roles);
        action.setRecipients(CollectionUtils.asArrayList(CollectionUtils.transform(recipientsValues, UUID_EXTRACTOR)));
        action.setRoles(CollectionUtils.asArrayList(CollectionUtils.transform(roles, UUID_EXTRACTOR)));
        action.setExcludeAuthor(excludeAuthor.getValue());
        metainfoUtils.setLocalizedValue(action.getMessage(), message.getValue());
        action.setHtml(html.getValue());
        eventAction.getObject().setSkipIfUserHasActiveSession(skipIfUserHasActiveSession.getValue());
    }

    @Override
    public void refreshProperties(final EventActionFormDisplay display, final List<ClassFqn> fqns,
            @Nullable String event)
    {
        remove(skipIfUserHasActiveSession);
        if (recipients != null)
        {
            updateRecipientsProperty(display, fqns, event);
        }
        display.getReadyState().onReady(this::refreshSkipIfUserHasActiveSession);
    }

    @Override
    public void bindProperties(EventActionFormDisplay display, List<ClassFqn> fqns)
    {
        formDisplay = display;
        super.bindProperties(display, fqns);
    }

    protected void bindPropertiesAfter(EventActionFormDisplay display, List<ClassFqn> fqns,
            ScriptCategory scriptCategory)
    {
        add(cmessages.script(), script);

        ScriptComponentEditWidget scriptWidget = script.getValueWidget();
        scriptWidget.init(false, scriptCategory, display);
        scriptWidget.initValidation(validation);
        scriptWidget.enableValidation();

        if (eventAction != null && event == null)
        {
            event = eventAction.getObject().getEvent().getEventType().name();
        }

        insertRecipientsProperty(display, fqns, event);
        setPropertiesValues(display);
        display.getReadyState().onReady(this::refreshSkipIfUserHasActiveSession);
        ensureDebugIds();
    }

    protected Property<Collection<DtObject>> createRecipients(
            final PopupValueCellTree<DtObject, Collection<DtObject>, FilteredMultiSelectionModel<DtObject>> tree)
    {
        Property<Collection<DtObject>> rec = new PropertyBase<>(messages.to(), tree);
        rec.setValidationMarker(true);
        if (recipientsValidation != null)
        {
            unregister(recipientsValidation);
        }
        recipientsValidation = validation.validate(rec, notEmptyCollectionValidator);
        add(recipientsValidation);
        ensureDebugId(rec, "recipients");
        return rec;
    }

    protected void ensureDebugIds()
    {
        ensureDebugId(skipIfUserHasActiveSession, "skipIfUserHasActiveSession");
        ensureDebugId(script, "edit-script");
        ensureDebugId(excludeAuthor, "excludeAuthor");
        ensureDebugId(message, "message");
        ensureDebugId(html, "html");
    }

    protected List<String> getRecipients()
    {
        return eventAction == null ? Collections.emptyList() : getAction(eventAction).getRecipients();
    }

    // Положение для свойства получатели в правой панели диалога
    protected int getRightSideRecipientsPropertyIndex()
    {
        return 1;
    }

    protected List<String> getRoles()
    {
        return eventAction != null ? getAction(eventAction).getRoles() : Collections.emptyList();
    }

    @SuppressWarnings("unchecked")
    private void insertRecipientsProperty(final EventActionFormDisplay display, final List<ClassFqn> fqns,
            String event)
    {
        DtoTreeFactoryNotificationRecipientContext context = getContext(fqns, event);

        treeFactory.createTree(context, new BasicCallback<HasValueOrThrow<Collection<DtObject>>>(display)
        {
            @Override
            protected void handleSuccess(HasValueOrThrow<Collection<DtObject>> value)
            {
                PopupValueCellTree<DtObject, Collection<DtObject>, FilteredMultiSelectionModel<DtObject>> tree =
                        (PopupValueCellTree<DtObject, Collection<DtObject>, FilteredMultiSelectionModel<DtObject>>)value;
                insertRecipientsProperty(display, fqns, tree);
            }
        });
    }

    private static Collection<DtObject> selectRoles(Collection<DtObject> recipients)
    {
        return CollectionUtils.select(recipients, input -> input.getMetainfo().equals(DtoTree.ROLE_FQN));
    }

    protected void setPropertiesValues(EventActionFormDisplay display)
    {
        display.startProcessing();
        final ReadyState readyState = new ReadyState(this);

        setPropertiesValuesAsync(readyState);

        if (isEventActionExists())
        {
            final T action = getAction(eventAction);
            setPropertiesValuesForExisted(action);
        }
        else
        {
            setPropertiesValuesForNew();
        }

        readyState.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                display.stopProcessing();
            }
        });
    }

    /**
     * Установить свойства асинхронно с заморозкой дисплея
     */
    protected void setPropertiesValuesAsync(ReadyState readyState)
    {
    }

    /**
     * Установить значения на форме для нового ДПС
     */
    protected void setPropertiesValuesForNew()
    {
        excludeAuthor.setValue(Boolean.TRUE);
        html.setValue(true);
    }

    /**
     * Установить значения на форме для существующего ДПС (при редактировании)
     */
    protected void setPropertiesValuesForExisted(T action)
    {
        if (eventAction.getObject() != null)
        {
            skipIfUserHasActiveSession.setValue(eventAction.getObject().getSkipIfUserHasActiveSession());
        }
        script.setValue(eventAction.getScript(action.getScript()));
        excludeAuthor.setValue(action.isExcludeAuthor());
        message.setValue(metainfoUtils.getLocalizedValue(action.getMessage()));
        html.setValue(action.isHtml());
    }

    boolean isEventActionExists()
    {
        return null != eventAction && eventAction.getObject().getAction() != null;
    }

    private void updateRecipientsProperty(final EventActionFormDisplay display, final List<ClassFqn> fqns,
            @Nullable String event)
    {

        DtoTreeFactoryNotificationRecipientContext context = getContext(fqns, event);

        treeFactory.createTree(context, new BasicCallback<HasValueOrThrow<Collection<DtObject>>>(display)
        {
            @Override
            protected void handleSuccess(HasValueOrThrow<Collection<DtObject>> value)
            {
                PopupValueCellTree<DtObject, Collection<DtObject>, FilteredMultiSelectionModel<DtObject>> tree =
                        (PopupValueCellTree<DtObject, Collection<DtObject>, FilteredMultiSelectionModel<DtObject>>)value;
                updateRecipientsProperty(display, fqns, tree);
            }
        });
    }

    private DtoTreeFactoryNotificationRecipientContext getContext(final List<ClassFqn> fqns, @Nullable String event)
    {
        DtoTreeFactoryNotificationRecipientContext context = new DtoTreeFactoryNotificationRecipientContext();
        context.setRoleFqns(fqns == null ? Collections.emptyList() : fqns);
        context.setFqns(Lists.newArrayList(OU.FQN, Employee.FQN, Team.FQN, DtoTree.ROLE_FQN));
        if (event != null)
        {
            this.event = event;
        }
        context.setEventType(this.event);
        context.setTreeType(DtoTree.NOTIFICATION_RECIPIENTS_TREE);

        return context;
    }

    private static Set<String> getSecDomains(final List<ClassFqn> fqns)
    {
        Set<String> secDomains = Sets.newHashSet(CollectionUtils.transform(fqns, ClassFqn.TO_STRING_CONVERTER));
        secDomains.addAll(CollectionUtils.transform(fqns, ClassFqn.ID_EXTRACTOR));
        return secDomains;
    }

    @SuppressWarnings("unchecked")
    private void insertRecipientsProperty(final EventActionFormDisplay display, final List<ClassFqn> fqns,
            final PopupValueCellTree<DtObject, Collection<DtObject>, FilteredMultiSelectionModel<DtObject>> tree)
    {

        ConcurrentCallbacks callbacks = new BasicNestedCallbacks(display)
        {
            @Override
            public void handleSuccess(Object[] results)
            {
                // employees, OUs, teams
                List<DtObject> recipientObjects = new ArrayList<>((List<DtObject>)results[0]);

                Set<String> secDomains = getSecDomains(fqns);

                for (DtoContainer<Role> containerRole : (List<DtoContainer<Role>>)results[1])
                {
                    Role role = containerRole.get();
                    if ((secDomains.contains(role.getDomainCode())
                         || Constants.GLOBAL_DOMAIN_CODE.equals(role.getDomainCode())
                         || AbstractBO.CLASS_ID.equals(role.getDomainCode()))
                        && (role.getType() != Type.SCRIPT || role.hasScriptedOwners()))
                    {
                        recipientObjects.add(new SimpleDtObject(role.getCode(), role.getTitle(), DtoTree.ROLE_FQN));
                    }
                }

                recipients = createRecipients(tree);
                recipients.setValue(recipientObjects);

                int index = getRightSideRecipientsPropertyIndex() + display.getLeftSideProperiesCount();
                addProperty(display, recipients, index);
                excludeAuthor.setCaption(messages.excludeAuthor());
                addProperty(display, excludeAuthor, index + 1);

                display.updateTabOrder();
            }
        };
        DtoProperties properties = new DtoProperties().add(
                AbstractBO.UUID, AbstractBO.TITLE, AbstractBO.METACLASS, PARENT_ATTR);
        objectService.getObjects(CollectionUtils.asArrayList(getRecipients()), properties, false, callbacks.next());
        metainfoService.getSecurityRoles(getRoles(), callbacks.next());
        callbacks.commit();
    }

    private void updateRecipientsProperty(final EventActionFormDisplay display, final List<ClassFqn> fqns,
            final PopupValueCellTree<DtObject, Collection<DtObject>, FilteredMultiSelectionModel<DtObject>> tree)
    {
        final Collection<DtObject> oldValue = recipients.getValue();

        ArrayList<String> roleCodes = new ArrayList<>();

        for (DtObject obj : oldValue)
        {
            if (ObjectUtils.equals(obj.getMetaClass().toString(), "role"))
            {
                roleCodes.add(obj.getUUID());
            }
        }

        metainfoService.getSecurityRoles(roleCodes, new BasicCallback<List<DtoContainer<Role>>>(display)
        {
            @Override
            public void handleSuccess(List<DtoContainer<Role>> result)
            {
                Set<String> secDomains = getSecDomains(fqns);

                ArrayList<DtObject> employeesAndRoles = new ArrayList<>();

                for (DtoContainer<Role> role : result)
                {
                    if ((secDomains.contains(role.get().getDomainCode())
                         || Constants.GLOBAL_DOMAIN_CODE.equals(role.get().getDomainCode()) || AbstractBO.CLASS_ID
                                 .equals(role.get().getDomainCode()))
                        && (role.get().getType() != Type.SCRIPT || role.get().hasScriptedOwners()))
                    {
                        employeesAndRoles.add(
                                new SimpleDtObject(role.get().getCode(), role.getTitle(), DtoTree.ROLE_FQN));
                    }
                }

                for (DtObject obj : oldValue)
                {
                    if (!ObjectUtils.equals(obj.getMetaClass().toString(), "role"))
                    {
                        employeesAndRoles.add(obj);
                    }
                }

                Property<Collection<DtObject>> temp = createRecipients(tree);

                temp.setValue(employeesAndRoles);

                recipients = temp;

                if (properties.size() > getRightSideRecipientsPropertyIndex())
                {
                    setProperty(display, recipients,
                            getRightSideRecipientsPropertyIndex() + display.getLeftSideProperiesCount());
                }
            }
        });
    }

    protected void bindHtmlFormatProperty()
    {
        add(messages.inHtmlFormat(), html);
    }

    protected void prepareRichTextWidget()
    {
        if (message.getValueWidget() instanceof RichTextWidgetBase)
        {
            ((RichTextWidgetBase<?>)message.getValueWidget()).enableTools(html.getValue());
            ((RichTextWidgetBase<?>)message.getValueWidget()).setAreaHeight(100);

            domHandlerRegistration = html.getValueWidget().asWidget().addDomHandler(
                    event -> ((RichTextWidgetBase<?>)message.getValueWidget()).enableTools(html.getValue()), ClickEvent
                            .getType());
        }
    }

    protected void disableHtmlProperty()
    {
        html.setValue(false);
        html.setEnabled(false);
        html.setVisible(false);
    }

    @Override
    public void removeProperties()
    {
        super.removeProperties();
        ((ScriptComponentEditWidget)script.getValueWidget()).disableValidation();
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        skipIfUserHasActiveSession.unbind(callback);
        html.unbind(callback);
        script.unbind(callback);
        if (domHandlerRegistration != null)
        {
            domHandlerRegistration.removeHandler();
        }
        removeProperties();
        super.unbind(callback);
    }

    private void refreshSkipIfUserHasActiveSession()
    {
        remove(skipIfUserHasActiveSession);
        if (isSkipIfUserHasActiveSessionPossible())
        {
            addPropertyAfter(formDisplay, skipIfUserHasActiveSession, propertiesBlockProperty);
        }
        else
        {
            skipIfUserHasActiveSession.setValue(null);
        }
    }

    private boolean isSkipIfUserHasActiveSessionPossible()
    {
        return skipIfUserHasActiveSessionPredicate.test(SelectListPropertyValueExtractor.getValue(eventProperty));
    }
}