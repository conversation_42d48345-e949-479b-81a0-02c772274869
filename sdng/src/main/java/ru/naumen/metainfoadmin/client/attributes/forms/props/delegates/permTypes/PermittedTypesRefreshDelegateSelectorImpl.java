package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.core.client.tree.selection.HierarchicalMultiSelectionModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes.PermittedTypesDelegatesGinModule.PermittedTypeRefreshDelegateCode;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Класс, реализующий логику выбора стратегии обновления значения свойства "Ограничение по типам" в зависимости от
 * типа ссылки
 * <AUTHOR>
 * @since 28.05.2012
 */
public class PermittedTypesRefreshDelegateSelectorImpl<F extends ObjectForm> implements
        PermittedTypesRefreshDelegate<F>
{
    @Inject
    @Named(PermittedTypeRefreshDelegateCode.BACK_LINK)
    PermittedTypesRefreshDelegate<F> backLinkDelegate;
    @Inject
    @Named(PermittedTypeRefreshDelegateCode.DIRECT_LINK)
    PermittedTypesRefreshDelegate<F> directLinkDelegate;

    @Override
    public void refreshProperty(
            PropertyContainerContext context,
            PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                    HierarchicalMultiSelectionModel<DtObject>>> property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        if (ObjectAttributeType.CODE.equals(attrType) || BOLinksAttributeType.CODE.equals(attrType))
        {
            directLinkDelegate.refreshProperty(context, property, callback);
        }
        else if (BackLinkAttributeType.CODE.equals(attrType))
        {
            backLinkDelegate.refreshProperty(context, property, callback);
        }
        else
        {
            callback.onSuccess(false);
        }
    }

}
