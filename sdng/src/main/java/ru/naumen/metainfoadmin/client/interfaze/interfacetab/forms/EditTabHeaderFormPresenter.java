package ru.naumen.metainfoadmin.client.interfaze.interfacetab.forms;

import static ru.naumen.core.shared.Constants.ImageFileExtension.FAVICON_SUPPORTED_TYPES;

import java.util.Collection;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.Window;
import com.google.inject.Provider;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.personalsettings.PersonalSettingsMessages;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.StringLengthValidator;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.FileUploadProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dispatch.ValidateFaviconAction;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.interfacesettings.FaviconSettings;
import ru.naumen.core.shared.interfacesettings.InterfaceSettings;
import ru.naumen.core.shared.interfacesettings.TabTitleSettings;
import ru.naumen.core.shared.interfacesettings.dispatch.EditTabHeaderAction;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfoadmin.client.FileUploadCompleteHandler;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.tabheader.TabHeaderSettingsMessages;

/**
 * Форма редактирования настроек вкладки
 *
 * <AUTHOR>
 * @since 19.07.16
 */
public class EditTabHeaderFormPresenter extends OkCancelPresenter<PropertyFormDisplay> implements
        ValueChangeHandler<SelectItem>
{
    private static final String STANDART_CHOICE = "standart";
    private static final String FROM_FILE_CHOICE = "fromfile";
    private static final int TAB_TITLE_MAX_LENGTH = 255;

    @Inject
    private TabHeaderSettingsMessages messages;
    @Inject
    private PersonalSettingsMessages settingsMessages;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private ListBoxProperty faviconFileType;
    private PropertyRegistration<SelectItem> faviconFileTypePR;
    @Inject
    private FileUploadProperty faviconFileUpload;
    private PropertyRegistration<Collection<DtObject>> faviconFileUploadPR;
    @Inject
    private ListBoxProperty tabTitleType;
    private PropertyRegistration<SelectItem> tabTitleTypePR;
    @Inject
    private TextBoxProperty tabTitle;
    private PropertyRegistration<String> tabTitlePR;
    @Inject
    private Processor validation;
    @Inject
    private Provider<StringLengthValidator> lengthValidationProvider;
    @Inject
    private Provider<NotEmptyFileValidator> fileValidationProvider;
    @Inject
    private MetainfoUtils metainfoUtils;

    private InterfaceSettingsContext context;

    @Inject
    public EditTabHeaderFormPresenter(PropertyFormDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(InterfaceSettingsContext context)
    {
        this.context = context;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }

        FaviconSettings faviconSettings = new FaviconSettings();
        boolean isFaviconStandart = STANDART_CHOICE.equals(SelectListPropertyValueExtractor.getValue(faviconFileType));
        boolean isTitleStandart = STANDART_CHOICE.equals(SelectListPropertyValueExtractor.getValue(tabTitleType));
        faviconSettings.setFaviconStandard(isFaviconStandart);
        faviconSettings.setFaviconUuid(isFaviconStandart ? null : getUploadFileUuid());
        TabTitleSettings tabTitleSettings = new TabTitleSettings(context.getSettings().getTabTitleSettings());
        tabTitleSettings.setTabTitleStandard(isTitleStandart);
        if (isTitleStandart)
        {
            tabTitleSettings.getLocalizedTabTitle().clear();
        }
        else
        {
            metainfoUtils.setLocalizedValue(tabTitleSettings.getLocalizedTabTitle(),
                    tabTitle.getValue());
        }

        dispatch.execute(new EditTabHeaderAction(faviconSettings, tabTitleSettings),
                new BasicCallback<SimpleResult<InterfaceSettings>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<InterfaceSettings> response)
                    {
                        Window.Location.reload();
                    }
                });
    }

    @Override
    public void onValueChange(ValueChangeEvent<SelectItem> event)
    {
        if (STANDART_CHOICE.equalsIgnoreCase(SelectListPropertyValueExtractor.getValue(faviconFileType))
            && faviconFileUpload.asWidget().isVisible())
        {
            faviconFileUpload.getCaptionWidget().asWidget().setVisible(false);
            faviconFileUpload.asWidget().setVisible(false);

            if (null != faviconFileUploadPR)
            {
                validation.unvalidate(faviconFileUpload);
                faviconFileUploadPR.unregister();
                faviconFileUploadPR = null;
            }
        }

        if (FROM_FILE_CHOICE.equalsIgnoreCase(SelectListPropertyValueExtractor.getValue(faviconFileType))
            && !faviconFileUpload.asWidget().isVisible())
        {
            faviconFileUpload.getCaptionWidget().asWidget().setVisible(true);
            faviconFileUpload.asWidget().setVisible(true);
            faviconFileUpload.getValueWidget().setValidFileExtension(FAVICON_SUPPORTED_TYPES);
            faviconFileUploadPR = getDisplay().addPropertyAfter(faviconFileUpload, faviconFileTypePR);
            validation.validate(faviconFileUpload,
                    fileValidationProvider.get().setErrorMessage(settingsMessages.incorrectFaviconState()));
        }
        if (STANDART_CHOICE.equals(SelectListPropertyValueExtractor.getValue(tabTitleType))
            && tabTitle.asWidget().isVisible())
        {
            tabTitle.getCaptionWidget().asWidget().setVisible(false);
            tabTitle.asWidget().setVisible(false);

            if (null != tabTitlePR)
            {
                validation.unvalidate(tabTitle);
                tabTitlePR.unregister();
                tabTitlePR = null;
            }
        }

        if (FROM_FILE_CHOICE.equals(SelectListPropertyValueExtractor.getValue(tabTitleType))
            && !tabTitle.asWidget().isVisible())
        {
            tabTitle.getCaptionWidget().asWidget().setVisible(true);
            tabTitle.asWidget().setVisible(true);
            tabTitlePR = getDisplay().addPropertyAfter(tabTitle, tabTitleTypePR);
            validation.validate(tabTitle, lengthValidationProvider.get().setMaxLength(TAB_TITLE_MAX_LENGTH));
        }
    }

    protected void bindProperties()
    {
        faviconFileType.asWidget().ensureDebugId("faviconFileType");
        faviconFileType.setCaption(messages.faviconSettingsTitle());

        faviconFileType.getValueWidget().addItem(messages.standardFavicon(), STANDART_CHOICE);
        faviconFileType.getValueWidget().addItem(messages.customFavicon(), FROM_FILE_CHOICE);
        registerHandler(faviconFileType.addValueChangeHandler(this));
        faviconFileTypePR = getDisplay().add(faviconFileType);

        if (faviconFileUpload instanceof PropertyBase)
        {
            faviconFileUpload.setValueFormatter(ITitled.TITLE_JOINER);
        }
        faviconFileUpload.setCaption(cmessages.file());
        faviconFileUpload.setValidationMarker(true);
        faviconFileUpload.getCaptionWidget().asWidget().setVisible(false);
        faviconFileUpload.asWidget().setVisible(false);
        registerHandler(faviconFileUpload.getValueWidget().addUploadCompleteHandler(
                new FileUploadCompleteHandler(getDisplay(), faviconFileUpload, ValidateFaviconAction::new, dispatch)));
        DebugIdBuilder.ensureDebugId(faviconFileUpload, "faviconFileUpload");

        tabTitleType.asWidget().ensureDebugId("tabTitleType");
        tabTitleType.setCaption(messages.tabTitleSettingsTitle());

        tabTitleType.getValueWidget().addItem(messages.standardTabTitle(), STANDART_CHOICE);
        tabTitleType.getValueWidget().addItem(messages.customTabTitle(), FROM_FILE_CHOICE);
        registerHandler(tabTitleType.addValueChangeHandler(this));
        tabTitleTypePR = getDisplay().addProperty(tabTitleType, 4);

        tabTitle.setCaption(cmessages.title());
        tabTitle.getCaptionWidget().asWidget().setVisible(false);
        tabTitle.asWidget().setVisible(false);
        DebugIdBuilder.ensureDebugId(tabTitle, "tabTitle");
    }

    @Override
    protected void onBind()
    {
        setCaption(messages.tabTitleEditForm());
        bindProperties();
        fillProperties();
        super.onBind();
    }

    private void fillProperties()
    {
        TabTitleSettings tabTitleSettings = context.getSettings().getTabTitleSettings();
        FaviconSettings faviconSettings = context.getSettings().getFaviconSettings();

        if (tabTitleSettings == null || tabTitleSettings.isTabTitleStandard())
        {
            tabTitleType.trySetObjValue(STANDART_CHOICE);
        }
        else
        {
            tabTitleType.trySetObjValue(FROM_FILE_CHOICE, true);
            tabTitle.setValue(metainfoUtils.getLocalizedValue(tabTitleSettings.getLocalizedTabTitle()));
        }

        if (faviconSettings == null || faviconSettings.isFaviconStandard())
        {
            faviconFileType.trySetObjValue(STANDART_CHOICE);
        }
        else
        {
            faviconFileType.trySetObjValue(FROM_FILE_CHOICE, true);
            faviconFileUpload.setValue(Lists.<DtObject> newArrayList(new SimpleDtObject(faviconSettings
                    .getFaviconUuid(), "Favicon", null)));
        }
    }

    private String getUploadFileUuid()
    {
        if (faviconFileUpload.getValue() == null || faviconFileUpload.getValue().isEmpty())
        {
            return null;
        }

        return faviconFileUpload.getValue().iterator().next().getUUID();
    }
}
