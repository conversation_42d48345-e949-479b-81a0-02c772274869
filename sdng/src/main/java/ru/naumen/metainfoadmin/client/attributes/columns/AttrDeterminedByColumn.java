package ru.naumen.metainfoadmin.client.attributes.columns;

import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.isRestrictedByAttributeCondition;
import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.isRestrictedByScript;
import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TITLE_SEPARATOR;

import java.util.List;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.IsWidget;

import jakarta.inject.Inject;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.common.ObjectService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.client.widgets.grouplist.AbstractBaseColumn;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.DateTimeRestrictionCondition;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.AttrWidgetsHelper;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.AvailableRestrictionConditionsProvider;
import ru.naumen.metainfoadmin.client.catalog.item.CatalogItemPlace;
import ru.naumen.metainfoadmin.client.script.ScriptPlace;
import ru.naumen.metainfoadmin.client.widgets.script.component.ScriptComponentService;

/**
 * Седьмая колонка "Значение атрибута / Определяется с помощью"
 * В колонке отображаются параметры, которые относятся к определению значения атрибута системой. 
 * Если ни один из этих параметров не выставлен в значение "да" или его нет, то ячейка пустая.
 *
 * <AUTHOR>
 * @since 27 июл. 2018 г.
 *
 */
public class AttrDeterminedByColumn extends AbstractBaseColumn<Attribute>
{
    private final ScriptComponentService scriptComponentService;
    private final ObjectService objectService;
    private final AttributesMessages messages;
    private final MetainfoServiceAsync metainfoService;
    private final AvailableRestrictionConditionsProvider conditionsProvider;

    @Inject
    public AttrDeterminedByColumn(ScriptComponentService scriptComponentService, ObjectService objectService,
            MetainfoServiceAsync metainfoService, AttributesMessages messages,
            AvailableRestrictionConditionsProvider conditionsProvider)
    {
        super(AttributeColumnCode.DETERMINED_BY);
        this.scriptComponentService = scriptComponentService;
        this.objectService = objectService;
        this.metainfoService = metainfoService;
        this.messages = messages;
        this.conditionsProvider = conditionsProvider;
    }

    @Override
    public IsWidget createWidget(WidgetContext<Attribute> context)
    {
        final FlowPanel result = new FlowPanel();
        Attribute attr = context.getContextObject();
        if (Boolean.TRUE.equals(attr.isComputable()) && attr.getScript() != null)
        {
            scriptComponentService.getScript(attr.getScript(), new BasicCallback<ScriptDto>()
            {
                @Override
                public void handleSuccess(ScriptDto script)
                {
                    result.add(new InlineLabel(messages.calculatingByScript() + TITLE_SEPARATOR));
                    Anchor anchor = new Anchor(script.getTitle(), false,
                            AttrWidgetsHelper.createLink(ScriptPlace.PLACE_PREFIX, script.getCode()));
                    result.add(anchor);
                    anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
                }
            });
        }
        else if (Boolean.TRUE.equals(attr.isDeterminable()) && attr.getDeterminer() != null)
        {
            result.add(new InlineLabel(messages.determinableByCorrespondanceTable() + TITLE_SEPARATOR));
            if (attr.getDeterminer() != null)
            {
                DtoCriteria criteria = new DtoCriteria(ClassFqn.parse(ValueMapCatalogItem.CLASS_ID))
                        .setProperties(CatalogItem.ITEM_TITLE, CatalogItem.ITEM_CODE)
                        .addFilters(Filters.eq(CatalogItem.ITEM_IS_REMOVED, false),
                                Filters.eq(CatalogItem.ITEM_IS_FOLDER, false),
                                Filters.eq(CatalogItem.ITEM_CODE, attr.getDeterminer()));
                objectService.getObjects(criteria, new BasicCallback<List<DtObject>>()
                {
                    @Override
                    public void handleSuccess(List<DtObject> list)
                    {
                        if (!list.isEmpty())
                        {
                            Anchor anchor = new Anchor(list.get(0).getTitle(), false,
                                    AttrWidgetsHelper.createLink(CatalogItemPlace.PLACE_PREFIX,
                                            list.get(0).getUUID()));
                            result.add(anchor);
                            anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
                        }
                    }
                });
            }
        }
        else if (Boolean.TRUE.equals(attr.isUseGenerationRule()))
        {
            result.add(new InlineLabel(Constants.StringAttributeType.CODE.equals(attr.getType().getCode())
                    ? messages.determinableByNameRules()
                    : messages.determinableByNumbersFormationRule()));
        }
        else if (Boolean.TRUE.equals(attr.isComposite()))
        {
            result.add(new InlineLabel(messages.compositeValue()));
        }
        else if (isRestrictedByAttributeCondition(attr))
        {
            metainfoService.getMetaClass(context.getContextObject().getMetaClassLite().getFqn(),
                    new BasicCallback<MetaClass>()
                    {
                        @Override
                        protected void handleSuccess(MetaClass metaClass)
                        {
                            super.handleSuccess(metaClass);
                            Attribute mcAttribute = metaClass.getAttribute(attr.getDateTimeRestrictionAttribute());
                            DateTimeRestrictionCondition condition = attr.getDateTimeRestrictionCondition();
                            String title = condition == null
                                    ? conditionsProvider.getTitle(DateTimeRestrictionCondition.NO.name())
                                    : conditionsProvider.getTitle(condition.name());
                            result.add(new InlineLabel(messages.dateTimeAttributeRestriction(title, mcAttribute
                                    .getTitle())));
                        }
                    });
        }
        else if (isRestrictedByScript(attr))
        {
            scriptComponentService.getScript(attr.getDateTimeRestrictionScript(), new BasicCallback<ScriptDto>()
            {
                @Override
                public void handleSuccess(ScriptDto script)
                {
                    result.add(new InlineLabel(messages.dateTimeScriptRestriction() + TITLE_SEPARATOR));
                    Anchor anchor = new Anchor(script.getTitle(), false,
                            AttrWidgetsHelper.createLink(ScriptPlace.PLACE_PREFIX, script.getCode()));
                    result.add(anchor);
                    anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
                }
            });
        }
        return result;
    }
}