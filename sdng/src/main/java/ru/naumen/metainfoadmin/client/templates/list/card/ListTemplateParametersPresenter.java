package ru.naumen.metainfoadmin.client.templates.list.card;

import java.util.function.Predicate;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.FactoryParam.ValueSource;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ListTemplate;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.PagerPosition;
import ru.naumen.metainfo.shared.ui.Position;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesCommandCode;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMessages;

/**
 * Блок "Параметры списка" на карточке.
 * <AUTHOR>
 * @since 20.04.2018
 */
public class ListTemplateParametersPresenter extends BasicPresenter<InfoDisplay>
{
    private class ListTemplateParametersCommandParam extends CommandParam<DtObject, DtObject>
    {
        public ListTemplateParametersCommandParam(AsyncCallback<DtObject> callback)
        {
            super(valueSource, callback);
        }
    }

    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> attributeGroup;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> presentation;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> pagingPosition;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> position;

    @Inject
    private CommonMessages cmessages;
    @Inject
    private ListTemplatesMessages messages;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private AdminDialogMessages dialogMessages;

    private DtObject template;
    private OnStartCallback<DtObject> refreshCallback;
    private ToolBarDisplayMediator<DtObject> toolBar;

    private ValueSource<DtObject> valueSource = new ValueSource<DtObject>()
    {
        @Override
        public DtObject getValue()
        {
            return template;
        }

        ;
    };

    @Inject
    public ListTemplateParametersPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        display.asWidget().ensureDebugId("parameters");
        toolBar = new ToolBarDisplayMediator<>(display.getToolBar());
    }

    public void init(OnStartCallback<DtObject> refreshCallback)
    {
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();

        ObjectListBase list = template.getProperty(ListTemplate.TEMPLATE);
        attributeGroup.setValue(template.getProperty(ListTemplate.ATTR_GROUP_TITLE));

        presentation.setValue(list.getPresentation().equals(PresentationType.DEFAULT.getCode())
                ? cmessages.objectListDefault() : cmessages.objectListAdvlist());

        if (PresentationType.ADVLIST.getCode().equals(list.getPresentation()))
        {
            PagerPosition pagingPos = list.getPagingSettings().getPosition();
            String pagingPosTitle = cmessages.underList();
            if (pagingPos.equals(PagerPosition.TOP))
            {
                pagingPosTitle = cmessages.aboveList();
            }
            else if (pagingPos.equals(PagerPosition.TOP_AND_BOTTOM))
            {
                pagingPosTitle = cmessages.aboveAndUnderList();
            }
            pagingPosition.setValue(pagingPosTitle);
        }
        else
        {
            pagingPosition.setValue(null);
        }

        Position pos = list.getPosition();
        String pagingPosTitle = dialogMessages.toFull();
        if (pos.equals(Position.LEFT))
        {
            pagingPosTitle = dialogMessages.toLeft();
        }
        else if (pos.equals(Position.RIGHT))
        {
            pagingPosTitle = dialogMessages.toRight();
        }
        position.setValue(pagingPosTitle);

        toolBar.refresh(template);
    }

    public void setTemplate(DtObject template)
    {
        this.template = template;
        refreshDisplay();
    }

    @SuppressWarnings("unchecked")
    protected ButtonPresenter<DtObject> addTool(String button, String title, String command,
            CommandParam<DtObject, DtObject> param, Predicate<DtObject> possibleFilter)
    {
        ButtonPresenter<DtObject> buttonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(button, title,
                command, param);
        buttonPresenter.addPossibleFilter(possibleFilter);
        toolBar.add(buttonPresenter);
        return buttonPresenter;
    }

    protected void bindProperties()
    {
        attributeGroup.setCaption(cmessages.attributeGroup());
        getDisplay().add(attributeGroup);
        presentation.setCaption(cmessages.presentation());
        getDisplay().add(presentation);
        pagingPosition.setCaption(cmessages.pagingLocationSettings());
        getDisplay().add(pagingPosition);
        position.setCaption(dialogMessages.position());
        getDisplay().add(position);
    }

    protected void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(attributeGroup, "attributeGroup");
        DebugIdBuilder.ensureDebugId(presentation, "presentation");
        DebugIdBuilder.ensureDebugId(pagingPosition, "pagingPosition");
        DebugIdBuilder.ensureDebugId(position, "position");
    }

    protected void initToolBar()
    {
        addTool(ButtonCode.EDIT, cmessages.edit(), ListTemplatesCommandCode.EDIT_PARAMETERS,
                new ListTemplateParametersCommandParam(refreshCallback),
                AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE));
        toolBar.bind();
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(messages.parametersList());
        initToolBar();
        bindProperties();
        ensureDebugIds();
        refreshDisplay();
    }
}
