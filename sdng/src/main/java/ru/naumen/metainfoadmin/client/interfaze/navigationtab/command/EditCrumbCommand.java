package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.CrumbFormPresenter;

/**
 * Команда редактирования хлебной крошки
 *
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public class EditCrumbCommand extends BaseCommandImpl<Crumb, DtoContainer<NavigationSettings>>
{
    public static final String ID = EditCrumbCommand.class.getSimpleName();

    @Inject
    Provider<CrumbFormPresenter<ObjectFormEdit>> editCrumbFormProvider;

    @Inject
    public EditCrumbCommand(@Assisted BreadCrumbCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<Crumb, DtoContainer<NavigationSettings>> param)
    {
        BreadCrumbCommandParam p = (BreadCrumbCommandParam)param;

        CrumbFormPresenter<ObjectFormEdit> presenter = editCrumbFormProvider.get();
        presenter.init(p.getValue(), p.getCallback(), false);
        presenter.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}