package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hidearchived;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

/**
 * Делегат, определяющий видимость свойства "Скрывать архивные объекты"
 *
 * <AUTHOR>
 * @since 20.02.2021
 */
@Singleton
public class HideArchivedRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        if (context.getPropertyValues().hasProperty(RELATED_OBJECT_ATTRIBUTE))
        {

            String relatedObjectAttributeCode = context.getPropertyValues().getProperty(RELATED_OBJECT_ATTRIBUTE);
            ClassFqn classFqn = ClassFqn.parse(context.getPropertyValues().getProperty(RELATED_OBJECT_METACLASS));
            ClassFqn attrClassFqn = AttributeFqn.getClassFqn(relatedObjectAttributeCode);
            metainfoService.getMetaClass(attrClassFqn.isClassOf(classFqn) ? classFqn : attrClassFqn,
                    new BasicCallback<MetaClass>()
                    {
                        @Override
                        protected void handleSuccess(MetaClass value)
                        {
                            Attribute attribute = value.getAttribute(AttributeFqn.getCode(relatedObjectAttributeCode));
                            context.setProperty(HIDE_ARCHIVED, attribute.isHideArchived());
                            callback.onSuccess(Boolean.TRUE.equals(
                                    context.getContextValues().getProperty(ENABLE_HIDE_ARCHIVED_OBJECTS))
                                               && Constants.COLLECTION_BOLINKS_TYPES.contains(
                                    attribute.getType().getCode()));
                        }
                    });
        }
        else
        {
            Boolean isEnabled =
                    Constants.COLLECTION_BOLINKS_TYPES.contains(context.getPropertyValues().getProperty(ATTR_TYPE))
                    && Boolean.TRUE.equals(context.getContextValues().getProperty(ENABLE_HIDE_ARCHIVED_OBJECTS))
                    && !Boolean.TRUE.equals(context.getPropertyValues().getProperty(COMPUTABLE));
            callback.onSuccess(isEnabled);
        }
    }
}
