package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import jakarta.inject.Inject;

import java.util.HashMap;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.metainfo.shared.elements.DateTimeRestrictionCondition;

/**
 * Предоставляет условия ограничений по атрибутам
 * <AUTHOR>
 * @since 12 дек. 2018 г.
 *
 */
public class AvailableRestrictionConditionsProvider
{
    //<код типа атрибута, название типа атрибута>
    private Map<String, String> types = new HashMap<>();

    @Inject
    public AvailableRestrictionConditionsProvider(DateTimeRestrictionMessages messages)
    {
        types.put(DateTimeRestrictionCondition.GTE.name(), messages.gteTitle());
        types.put(DateTimeRestrictionCondition.GT.name(), messages.gtTitle());
        types.put(DateTimeRestrictionCondition.LT.name(), messages.ltTitle());
        types.put(DateTimeRestrictionCondition.LTE.name(), messages.lteTitle());
    }

    /**
     * Возвращает название атрибута по его коду
     */
    public String getTitle(String code)
    {
        return types.get(code);
    }

    /**
     * Возвращает типы, доступные для выбора
     *
     * @return пара [код типа, название типа]
     */
    public List<Entry<String, String>> getTypes()
    {
        return CollectionUtils.asRevertedMapSortedEntries(types);
    }

}
