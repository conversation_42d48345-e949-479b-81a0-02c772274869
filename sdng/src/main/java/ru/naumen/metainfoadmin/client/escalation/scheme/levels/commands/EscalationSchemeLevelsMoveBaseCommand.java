package ru.naumen.metainfoadmin.client.escalation.scheme.levels.commands;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.PresenterCommandEvent;
import ru.naumen.core.client.mvp.PresenterCommandEvent.PresenterCommandCode;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.metainfo.shared.dispatch2.MoveEscalationSchemeLevelAction;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemeContext;

/**
 * Базовай класс для команд перемещения вверх и вниз
 *
 * <AUTHOR>
 * @since 21.12.2018
 *
 */
public abstract class EscalationSchemeLevelsMoveBaseCommand extends BaseCommandImpl<EscalationSchemeLevel, Void>
{
    @Inject
    private DispatchAsync dispatch;

    public EscalationSchemeLevelsMoveBaseCommand(EscalationSchemeLevelsCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<EscalationSchemeLevel, Void> param)
    {
        EscalationSchemeContext context = ((EscalationSchemeLevelsCommandParam)param).getContext();
        dispatch.execute(new MoveEscalationSchemeLevelAction(context.getEscalationScheme().get().getCode(),
                param.getValue().getLevel(), getMoveDirection()), new BasicCallback<SimpleResult<Void>>(context
                .getDisplay())
        {
            @Override
            protected void handleSuccess(SimpleResult<Void> response)
            {
                context.getLocalEventBus().fireEvent(new PresenterCommandEvent(PresenterCommandCode.REFRESH));
            }
        });
    }

    public abstract int getMoveDirection();
}
