package ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes;

import com.google.gwt.safehtml.shared.SafeUri;
import com.google.inject.Singleton;

import jakarta.inject.Inject;
import ru.naumen.admin.client.theme.SchemeLoginLogoResources;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.theme.SchemeLogoResources;
import ru.naumen.core.client.theme.ThemeLogos;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsResources;

/**
 * Сервис логотипов в админке
 *
 * <AUTHOR>
 * @since 08 авг. 2016 г.
 */
@Singleton
public class ThemeLogosAdmin extends ThemeLogos
{
    private final SchemeLoginLogoResources loginLogos;
    private final InterfaceSettingsResources resources;

    @Inject
    public ThemeLogosAdmin(SchemeLogoResources logos, SchemeLoginLogoResources loginLogos,
            InterfaceSettingsResources resources, SharedSettingsClientService sharedSettingsClientService)
    {
        super(logos, sharedSettingsClientService);
        this.loginLogos = loginLogos;
        this.resources = resources;
    }

    /**
     * Получить стантдартный логотип для формы логина для указанной темы
     */
    public SafeUri getStandartLoginFormLogo(String theme)
    {
        if (sharedSettingsClientService.isPlatformTypeSMP())
        {
            return loginLogos.colorSchema().getSafeUri();
        }
        else
        {
            switch (theme)
            {
                case THEME_BLUE:
                    return loginLogos.blue().getSafeUri();
                case THEME_SITE:
                    return loginLogos.site().getSafeUri();
                case THEME_SCHEME_1:
                    return loginLogos.schema1().getSafeUri();
                case THEME_SCHEME_2:
                    return loginLogos.schema2().getSafeUri();
                case THEME_SCHEME_3:
                    return loginLogos.schema3().getSafeUri();
                case THEME_SCHEME_4:
                    return loginLogos.schema4().getSafeUri();
                case THEME_SCHEME_5:
                    return loginLogos.schema5().getSafeUri();
                case THEME_SCHEME_6:
                    return loginLogos.schema6().getSafeUri();
                case THEME_SCHEME_7:
                    return loginLogos.schema7().getSafeUri();
                default:
                    break;
            }
        }

        return loginLogos.blue().getSafeUri();
    }

    public String getThemeImageStyle(String theme)
    {
        switch (theme)
        {
            case THEME_BLUE:
                return resources.css().tableImageBlue();
            case THEME_SITE:
                return resources.css().tableImageSite();
            case THEME_SCHEME_1:
                return resources.css().tableImageScheme1();
            case THEME_SCHEME_2:
                return resources.css().tableImageScheme2();
            case THEME_SCHEME_3:
                return resources.css().tableImageScheme3();
            case THEME_SCHEME_4:
                return resources.css().tableImageScheme4();
            case THEME_SCHEME_5:
                return resources.css().tableImageScheme5();
            case THEME_SCHEME_6:
                return resources.css().tableImageScheme6();
            case THEME_SCHEME_7:
                return resources.css().tableImageScheme7();
            default:
                break;
        }

        return resources.css().tableImageBlue();
    }
}