/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item;

import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.ValueMapItemContext;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationValueMapItemContext extends ValueMapItemContext
{
    @Inject
    public EscalationValueMapItemContext(@Assisted DtoContainer<Catalog> catalog, @Assisted DtObject catalogItem,
            @Assisted Display display, @Assisted EventBus eventBus)
    {
        super(catalog, catalogItem, display, eventBus);
    }
}