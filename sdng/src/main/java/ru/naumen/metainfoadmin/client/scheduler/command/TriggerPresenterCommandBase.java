package ru.naumen.metainfoadmin.client.scheduler.command;

import jakarta.inject.Inject;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfoadmin.client.scheduler.forms.TriggerFormPresenter;

/**
 * <AUTHOR>
 * @since 22.08.2011
 *
 */
public abstract class TriggerPresenterCommandBase extends PresenterCommandImpl<DtoContainer<Trigger>,
        DtoContainer<Trigger>, DtoContainer<Trigger>>
        implements TriggerCommand
{
    private String schTaskCode;

    @Inject
    protected MetainfoModificationServiceAsync metainfoModificationService;

    public TriggerPresenterCommandBase(TriggerCommandParam param)
    {
        super(param);
        this.schTaskCode = param.getSchedulerTaskCode();
    }

    @Override
    public void init(String schTaskCode)
    {
        this.schTaskCode = schTaskCode;
    }

    @Override
    public void onExecute(DtoContainer<Trigger> result,
            CallbackDecorator<DtoContainer<Trigger>, DtoContainer<Trigger>> callback)
    {
        metainfoModificationService.saveTrigger(result, callback);
    }

    @Override
    protected CallbackPresenter<DtoContainer<Trigger>, DtoContainer<Trigger>> getPresenter(DtoContainer<Trigger> value)
    {
        TriggerFormPresenter result = getTriggerFormPresenter();
        result.setSchTaskCode(schTaskCode);
        return result;
    }

    protected abstract TriggerFormPresenter getTriggerFormPresenter();

}
