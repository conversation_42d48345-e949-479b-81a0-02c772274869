package ru.naumen.metainfoadmin.client.eventaction;

import java.util.EnumMap;
import java.util.Map;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.Event;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.IntegrationEventAction;
import ru.naumen.metainfo.shared.eventaction.NDAPTriggerEvent;
import ru.naumen.metainfo.shared.eventaction.NotificationEventAction;
import ru.naumen.metainfo.shared.eventaction.PlannedEventRule;
import ru.naumen.metainfo.shared.eventaction.ScriptEventAction;
import ru.naumen.metainfo.shared.eventaction.UserEvents;
import ru.naumen.metainfo.shared.eventaction.push.PushEventAction;
import ru.naumen.metainfo.shared.eventaction.push.PushPortalEventAction;
import ru.naumen.metainfo.shared.eventaction.tracking.ChangeTrackingEventAction;
import ru.naumen.metainfoadmin.client.customforms.CustomFormParametersPresenter;
import ru.naumen.metainfoadmin.client.customforms.CustomFormParametersPresenterFactory;
import ru.naumen.metainfoadmin.client.eventaction.form.creator.EventActionFormPropertiesCreator;
import ru.naumen.metainfoadmin.client.eventaction.form.creator.event.EventFormPropertiesCreator;
import ru.naumen.metainfoadmin.shared.customforms.EventActionModificationContext;

/**
 * <AUTHOR>
 * @since 02.12.2011
 *
 */
@Singleton
public class EventActionFactoryImpl implements EventActionFactory
{
    @Inject
    private EventActionGinjector injector;
    @Inject
    private CustomFormParametersPresenterFactory paramsPresenterProvider;

    private final Map<ActionType, EventActionCreator<?>> eventActionCreators = new EnumMap<>(ActionType.class);
    private final Map<EventType, EventCreator<?>> eventCreators = new EnumMap<>(EventType.class);

    @Inject
    public EventActionFactoryImpl(final EventActionGinjector injector)
    {
        initializeEventCreators(injector);
        initializeEventActionCreators(injector);
    }

    @Override
    public ActionConditionsPresenter getConditionsPresenter(EventActionWithScript eventAction)
    {
        return injector.conditionsPresenter();
    }

    @Override
    public Map<ActionType, EventActionCreator<?>> getEventActionCreators()
    {
        return eventActionCreators;
    }

    @Override
    public EventActionPresenter getEventActionPresenter()
    {
        return injector.eventActionPresenter();
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T extends Event> EventCreator<T> getEventCreator(@Nullable EventType event)
    {
        return (EventCreator<T>)eventCreators.get(event);
    }

    @Override
    public EventActionFormPropertiesCreator getFormPropertiesCreator(@Nullable ActionType action)
    {
        if (action == null)
        {
            return injector.emptyEventActionFormPropertiesCreator();
        }
        return eventActionCreators.get(action).formProperties();
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T extends Action> EventActionInfoPresenterBase<T> getInfoPresenter(EventAction eventAction)
    {
        return ((EventActionCreator<T>)eventActionCreators.get(eventAction.getAction().getActionType()))
                .infoPresenter();
    }

    @Override
    public CustomFormParametersPresenter getUserEventParamsPresenter(EventAction eventAction)
    {
        final String formCode = ((UserEvents)eventAction.getEvent()).getFormCode();
        final EventActionModificationContext context = new EventActionModificationContext(formCode, eventAction
                .getCode());
        return paramsPresenterProvider.create(context);
    }

    private void initializeEventCreators(EventActionGinjector injector)
    {
        eventCreators.put(EventType.onsetTimeOfAttr, new EventCreator<PlannedEventRule>()
        {
            @Override
            public EventFormPropertiesCreator<PlannedEventRule> getFormPropertiesCreator(EventType action)
            {
                return injector.plannedEventFormPropertiesCreator();
            }

            @Override
            public EventInfoPresenterBase<PlannedEventRule> infoPresenter()
            {
                return injector.plannedEventInfoPresenter();
            }
        });
        eventCreators.put(EventType.userEvent, new EventCreator<UserEvents>()
        {
            @Override
            public EventFormPropertiesCreator<UserEvents> getFormPropertiesCreator(EventType action)
            {
                return injector.userEventPropertiesCreator();
            }

            @Override
            public EventInfoPresenterBase<UserEvents> infoPresenter()
            {
                return null;
            }
        });
        eventCreators.put(EventType.alertActivated, new EventCreator<NDAPTriggerEvent>()
        {
            @Override
            public EventFormPropertiesCreator<NDAPTriggerEvent> getFormPropertiesCreator(EventType action)
            {
                return injector.ndapTriggerEventFormPropertiesCreator();
            }

            @Override
            public EventInfoPresenterBase<NDAPTriggerEvent> infoPresenter()
            {
                return injector.ndapTriggerEventInfoPresenter();
            }
        });
        eventCreators.put(EventType.alertDeactivated, new EventCreator<NDAPTriggerEvent>()
        {
            @Override
            public EventFormPropertiesCreator<NDAPTriggerEvent> getFormPropertiesCreator(EventType action)
            {
                return injector.ndapTriggerEventFormPropertiesCreator();
            }

            @Override
            public EventInfoPresenterBase<NDAPTriggerEvent> infoPresenter()
            {
                return injector.ndapTriggerEventInfoPresenter();
            }
        });
        eventCreators.put(EventType.alertChanged, new EventCreator<NDAPTriggerEvent>()
        {
            @Override
            public EventFormPropertiesCreator<NDAPTriggerEvent> getFormPropertiesCreator(EventType action)
            {
                return injector.ndapTriggerEventFormPropertiesCreator();
            }

            @Override
            public EventInfoPresenterBase<NDAPTriggerEvent> infoPresenter()
            {
                return injector.ndapTriggerEventInfoPresenter();
            }
        });
    }

    private void initializeEventActionCreators(EventActionGinjector injector)
    {
        registerEventActionCreator(ActionType.ScriptEventAction, new EventActionCreator<ScriptEventAction>()
        {
            @Override
            public EventActionFormPropertiesCreator formProperties()
            {
                return injector.scriptEventActionFormPropertiesCreator();
            }

            @Override
            public EventActionInfoPresenterBase<ScriptEventAction> infoPresenter()
            {
                return injector.scriptEventActionInfoPresenter();
            }
        });
        registerEventActionCreator(ActionType.NotificationEventAction, new EventActionCreator<NotificationEventAction>()
        {
            @Override
            public EventActionFormPropertiesCreator formProperties()
            {
                return injector.notificationEventActionFormPropertiesCreator();
            }

            @Override
            public EventActionInfoPresenterBase<NotificationEventAction> infoPresenter()
            {
                return injector.notificationEventActionInfoPresenter();
            }
        });

        registerEventActionCreator(ActionType.IntegrationEventAction, new EventActionCreator<IntegrationEventAction>()
        {
            @Override
            public EventActionFormPropertiesCreator formProperties()
            {
                return injector.integrationEventActionFormPropertiesCreator();
            }

            @Override
            public EventActionInfoPresenterBase<IntegrationEventAction> infoPresenter()
            {
                return injector.integrationEventActionInfoPresenter();
            }
        });

        registerEventActionCreator(ActionType.PushEventAction, new EventActionCreator<PushEventAction>()
        {
            @Override
            public EventActionFormPropertiesCreator formProperties()
            {
                return injector.pushEventActionFormPropertiesCreator();
            }

            @Override
            public EventActionInfoPresenterBase<PushEventAction> infoPresenter()
            {
                return injector.pushEventActionInfoPresenter();
            }

        });

        registerEventActionCreator(ActionType.PushPortalEventAction, new EventActionCreator<PushPortalEventAction>()
        {
            @Override
            public EventActionFormPropertiesCreator formProperties()
            {
                return injector.pushPortalEventActionFormPropertiesCreator();
            }

            @Override
            public EventActionInfoPresenterBase<PushPortalEventAction> infoPresenter()
            {
                return injector.pushPortalEventActionInfoPresenter();
            }
        });

        registerEventActionCreator(ActionType.ChangeTrackingEventAction,
                new EventActionCreator<ChangeTrackingEventAction>()
                {
                    @Override
                    public EventActionFormPropertiesCreator formProperties()
                    {
                        return injector.changeTrackingEventActionFormPropertiesCreator();
                    }

                    @Override
                    public EventActionInfoPresenterBase<ChangeTrackingEventAction> infoPresenter()
                    {
                        return injector.changeTrackingEventActionInfoPresenter();
                    }
                });
    }

    public void registerEventActionCreator(ActionType action, EventActionCreator<?> creator)
    {
        this.eventActionCreators.put(action, creator);
    }
}
