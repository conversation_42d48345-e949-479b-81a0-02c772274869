package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;

/**
 * Делегат обновления поля на форме.
 *
 * <AUTHOR>
 * @since 31.05.2012
 */
public interface AttributeFormPropertyDelegateRefresh<F extends ObjectForm, T, P extends Property<T>> extends
        PropertyDelegateRefresh<T, P>
{
}
