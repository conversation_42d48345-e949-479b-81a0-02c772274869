/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.display;

import java.util.Collection;
import java.util.Map;

import ru.naumen.core.client.widgets.columns.SafeHtmlCell;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.escalation.EscalationScheme;

import com.google.gwt.user.cellview.client.Column;
import com.google.inject.Inject;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 01.11.2012
 *
 */
@Singleton
public class EscalationSchemesColumn extends Column<Map<String, Object>, Collection<EscalationScheme>>
{
    @Inject
    public EscalationSchemesColumn(EscalationSchemesSafeHtmlRenderer renderer)
    {
        super(new SafeHtmlCell<Collection<EscalationScheme>>(renderer));
    }

    @Override
    public Collection<EscalationScheme> getValue(Map<String, Object> object)
    {
        return (Collection<EscalationScheme>)object.get(ValueMapCatalogItem.ESCALATION_TARGET_DATA);
    }
}