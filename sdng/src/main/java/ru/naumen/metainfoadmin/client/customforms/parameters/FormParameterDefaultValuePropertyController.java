package ru.naumen.metainfoadmin.client.customforms.parameters;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.DefaultValuePropertyControllerImpl;

/**
 *
 * <AUTHOR>
 * @since 16 июня 2016 г.
 */
public class FormParameterDefaultValuePropertyController extends DefaultValuePropertyControllerImpl
{
    @Inject
    public FormParameterDefaultValuePropertyController(@Assisted String code,
            @Assisted PropertyContainerContext context, @Assisted PropertyParametersDescriptor propertyParams,
            @Assisted PropertyDelegateDescriptor<Object, Property<Object>> propertyDelegates)
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    @Override
    protected Attribute getAttributeForPrsContext()
    {
        // Никогда не передаем параметр в контекст представления
        // т.к. тип атрибута и другие параметры могут быть изменены на форме
        return null;
    }

    @Override
    protected AttributeType getAttrType()
    {
        //        Attribute attr = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        //        String ctxAttrType = context.getPropertyValues().getProperty(ATTR_TYPE);
        //        if (attr != null && (ctxAttrType == null || ctxAttrType.equals(attr.getType().getCode())))
        //        {
        //            // Если тип параметра не был отредактирован, возвращаем переданный тип
        //            return attr.getType();
        //        }
        return attrTypeFactory.create(context.getContextValues(), context.getPropertyValues());
    }
}
