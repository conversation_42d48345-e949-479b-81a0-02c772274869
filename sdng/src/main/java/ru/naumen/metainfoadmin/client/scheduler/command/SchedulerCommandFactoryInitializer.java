package ru.naumen.metainfoadmin.client.scheduler.command;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 * <AUTHOR>
 * @since 19.08.2011
 *
 */
@Singleton
public class SchedulerCommandFactoryInitializer
{
    @Inject
    public SchedulerCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<AddTriggerCommand, TriggerCommandParam> addTrigerProvider,
            CommandProvider<DeleteSchTaskCommand, CommandParam<Collection<DtObject>, Void>> delTaskProvider,
            CommandProvider<DeleteTriggerCommand, CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>>> delTriggerProvider,
            CommandProvider<EditSchTaskCommand, CommandParam<DtoContainer<SchedulerTask>,
                    DtoContainer<SchedulerTask>>> editTaskProvider,
            CommandProvider<EditTriggerCommand, TriggerCommandParam> editTriggerProvider,
            CommandProvider<OffTriggerCommand, CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>>> offTrigerProvider,
            CommandProvider<OnTriggerCommand, CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>>> onTrigerProvider,
            CommandProvider<RunTaskCommand, CommandParam<DtoContainer<SchedulerTask>, DtoContainer<SchedulerTask>>> onRunTaskNowProvider)
    {
        // @formatter:off;
        factory.register(SchedulerCommandCode.ADD_TRIGGER,     addTrigerProvider);
        factory.register(SchedulerCommandCode.DELETE_SCH_TASK, delTaskProvider);
        factory.register(SchedulerCommandCode.DELETE_TRIGGER,  delTriggerProvider);       
        factory.register(SchedulerCommandCode.EDIT_SCH_TASK,   editTaskProvider);
        factory.register(SchedulerCommandCode.EDIT_TRIGGER,    editTriggerProvider);        
        factory.register(SchedulerCommandCode.OFF_TRIGGER,     offTrigerProvider);
        factory.register(SchedulerCommandCode.ON_TRIGGER,      onTrigerProvider);
        factory.register(SchedulerCommandCode.RUN_SCH_TASK,    onRunTaskNowProvider);
        // @formatter:on
    }
}
