package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.componform;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE_ON_FORM_SCRIPT;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

import com.google.common.collect.Lists;

/**
 *
 * <AUTHOR>
 *
 * @param <F>
 */
public class ComputableOnFormVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{

    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getRefreshProcess().startCustomProcess(Lists.newArrayList(COMPUTABLE_ON_FORM_SCRIPT));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}
