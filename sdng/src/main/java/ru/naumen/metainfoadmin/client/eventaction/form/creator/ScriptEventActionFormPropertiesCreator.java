package ru.naumen.metainfoadmin.client.eventaction.form.creator;

import java.util.List;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.BooleanUtils;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.script.places.EventActionCategories;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.EventAction.TxType;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.ScriptEventAction;
import ru.naumen.metainfoadmin.client.eventaction.PossibleSkipIfUserHasActiveSessionPredicate;
import ru.naumen.metainfoadmin.client.eventaction.PossibleSlowPredicate;
import ru.naumen.metainfoadmin.client.eventaction.PossibleTxTypePredicate;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;

/**
 * Функционал создания свойств ДПС типа "Скрипт" на форме
 * <AUTHOR>
 * @since 02.12.2011
 *
 */
public class ScriptEventActionFormPropertiesCreator
        extends ScriptEventActionFormPropertiesCreatorBase<ScriptEventAction>
{
    /**
     * Эвент, который просто сообщает об изменении поля
     */
    private static class ChangeEvent extends ValueChangeEvent<Boolean>
    {
        public ChangeEvent()
        {
            super(false);
        }
    }

    private static final ChangeEvent SLOW_CHANGE = new ChangeEvent();

    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> txType;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> isSlow;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> skipIfUserHasActiveSession;
    @Inject
    private PossibleTxTypePredicate txTypePredicate;
    @Inject
    private PossibleSlowPredicate slowPredicate;
    @Inject
    private PossibleSkipIfUserHasActiveSessionPredicate skipIfUserHasActiveSessionPredicate;

    private ValueChangeHandler<Boolean> addingValueChangeHandler;

    @Override
    public void bindProperties(EventActionFormDisplay display, List<ClassFqn> fqns)
    {
        super.bindProperties(display, fqns);

        bindTxType();
        bindSlow();
        if (isSkipIfUserHasActiveSessionPossible())
        {
            bindSkipIfUserHasActiveSession();
        }
        bindScript();

        display.setRightSideStyle(WidgetResources.INSTANCE.form().formEventactionScript(), true);
    }

    @Override
    protected EventActionCategories getScriptHelpCategory()
    {
        return EventActionCategories.EVENTACTION_SCRIPT;
    }

    private void bindTxType()
    {
        initTxTypeEnabled(isTxTypePossible());
        add(txType);
        if (null != eventAction)
        {
            txType.setValue(TxType.CURRENT_TX.equals(eventAction.getObject().getTxType()));
        }
        txType.addValueChangeHandler(event ->
        {
            formDisplay.addAttentionMessage(
                    event.getValue() ? messages.singleTransactionHint() : messages.asyncActionsHint());
            addingValueChangeHandler.onValueChange(event);
        });
    }

    private void bindSlow()
    {
        isSlow.setEnabled(isSlowPossible());
        add(isSlow);
        if (null != eventAction)
        {
            isSlow.setValue(eventAction.getObject().isSlow());
        }
        isSlow.addValueChangeHandler(event ->
        {
            if (isTxNewEventAction())
            {
                addingValueChangeHandler.onValueChange(SLOW_CHANGE);
            }
        });
    }

    private void bindSkipIfUserHasActiveSession()
    {
        if (null != eventAction)
        {
            skipIfUserHasActiveSession.setValue(eventAction.getObject().getSkipIfUserHasActiveSession());
        }
        if (isSkipIfUserHasActiveSessionPossible())
        {
            add(skipIfUserHasActiveSession);
        }
    }

    @Override
    protected void setActionProperties(ScriptEventAction action)
    {
        super.setActionProperties(action);

        boolean atCurrentTx = isTxTypePossible() && Boolean.TRUE.equals(txType.getValue());
        eventAction.getObject().setTxType(atCurrentTx ? TxType.CURRENT_TX : TxType.NEW_TX);
        eventAction.getObject().setSlow(Boolean.TRUE.equals(isSlow.getValue()));
        eventAction.getObject().setSkipIfUserHasActiveSession(
                BooleanUtils.isTrue(skipIfUserHasActiveSession.getValue()));
    }

    @Override
    protected Action newEventActionTypeInstance()
    {
        return new ScriptEventAction();
    }

    @Override
    public void init(@Nullable EventActionWithScript eventAction, Property<SelectItem> event)
    {
        super.init(eventAction, event);
        if (eventAction != null && !(eventAction.getObject().getAction() instanceof ScriptEventAction))
        {
            throw new UnsupportedOperationException("EventAction must be ScriptEventAction");
        }
        this.eventAction = eventAction;

        isSlow.setCaption(messages.externalInteraction());
        DebugIdBuilder.ensureDebugId(isSlow, "isSlow");

        txType.setCaption(messages.performSynchronously());
        DebugIdBuilder.ensureDebugId(txType, "txType");

        skipIfUserHasActiveSession.setCaption(messages.skipIfUserHasActiveSession());
        DebugIdBuilder.ensureDebugId(skipIfUserHasActiveSession, "skipIfUserHasActiveSession");
    }

    @Override
    public void refreshProperties(EventActionFormDisplay display, List<ClassFqn> fqns, @Nullable String event)
    {
        refreshTxType();
        refreshSlow();
        refreshSkipIfUserHasActiveSession();
        formDisplay.clearAttentionMessage();
    }

    private void refreshTxType()
    {
        final boolean txTypePossible = isTxTypePossible();
        initTxTypeEnabled(txTypePossible);
        if (!txTypePossible)
        {
            txType.setValue(false);
        }
    }

    private void initTxTypeEnabled(boolean txTypePossible)
    {
        PropertyRegistration<Boolean> propertyRegistration = getPropertyRegistration(txType);
        if (propertyRegistration != null)
        {
            propertyRegistration.setEnabled(txTypePossible);
        }
        txType.setEnabled(txTypePossible);
    }

    private void refreshSlow()
    {
        final boolean slowPossible = isSlowPossible();
        isSlow.setEnabled(slowPossible);
        if (!slowPossible)
        {
            isSlow.setValue(false);
        }
    }

    private void refreshSkipIfUserHasActiveSession()
    {
        remove(skipIfUserHasActiveSession);
        if (isSkipIfUserHasActiveSessionPossible())
        {
            addPropertyAfter(formDisplay, skipIfUserHasActiveSession, isSlow);
        }
        else
        {
            skipIfUserHasActiveSession.setValue(null);
        }
    }

    @Override
    public EventActionFormPropertiesCreator addValueChangeHandler(ValueChangeHandler<Boolean> handler)
    {
        addingValueChangeHandler = handler;
        return super.addValueChangeHandler(handler);
    }

    @Override
    public boolean isSlowEventAction()
    {
        return isSlow.getValue();
    }

    @Override
    public boolean isTxNewEventAction()
    {
        return !txType.getValue();
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        eventProperty.unbind(callback);
        super.unbind(callback);
    }

    private boolean isTxTypePossible()
    {
        return txTypePredicate.test(SelectListPropertyValueExtractor.getValue(eventProperty));
    }

    private boolean isSlowPossible()
    {
        return slowPredicate.test(SelectListPropertyValueExtractor.getValue(eventProperty));
    }

    private boolean isSkipIfUserHasActiveSessionPossible()
    {
        return skipIfUserHasActiveSessionPredicate.test(SelectListPropertyValueExtractor.getValue(eventProperty));
    }
}