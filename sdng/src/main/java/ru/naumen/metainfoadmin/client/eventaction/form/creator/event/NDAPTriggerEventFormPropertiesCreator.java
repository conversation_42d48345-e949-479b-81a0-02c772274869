package ru.naumen.metainfoadmin.client.eventaction.form.creator.event;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.collect.ImmutableSet;

import java.util.ArrayList;

import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.NotEmptyObjectValidator;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.shared.Constants.NDAPConstants.NDAPNoDataTrigger;
import ru.naumen.core.shared.Constants.NDAPConstants.NDAPTrigger;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.eventaction.Constants.EventAction;
import ru.naumen.metainfo.shared.eventaction.Event;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.NDAPTriggerEvent;

/**
 * Создатель формы со свойствами для {@link NDAPTriggerEvent}
 * <AUTHOR>
 * @since Mar 24, 2020
 */
public class NDAPTriggerEventFormPropertiesCreator extends AbstractEventFormPropertiesCreator<NDAPTriggerEvent>
{
    private NDAPTriggerEvent event;
    private List<ClassFqn> selectedFqns = new ArrayList<>();
    private List<ClassFqn> noDataTriggerTreeFqns = new ArrayList<>();
    private ReadyState readyState;
    private Set<String> fromInitSource;

    @Inject
    private NotEmptyObjectValidator<SelectItem> notEmptySelectItemValidator;
    @Inject
    private MultiSelectBoxProperty source;
    @Inject
    private MetainfoServiceAsync metainfoService;
    @Inject
    private CommonMessages cmessages;

    @Override
    public void bindProperties()
    {
        addWithMarker(cmessages.source(), source);
    }

    @Override
    public NDAPTriggerEvent getEvent()
    {
        if (fqnsContainsNDAPTriggerOnly())
        {
            event.setSource(SelectListPropertyValueExtractor.getValue(source));
            if (!validation.validate())
            {
                return null;
            }
        }
        return event;
    }

    @Override
    public void init(@Nullable Event event, List<ClassFqn> fqn)
    {
        if (event instanceof NDAPTriggerEvent)
        {
            this.event = (NDAPTriggerEvent)event;
            fromInitSource = ((NDAPTriggerEvent)event).getSource();
        }
        else
        {
            this.event = new NDAPTriggerEvent();
            if ((event != null) && isTriggerEvent(event))
            {
                fromInitSource = ImmutableSet.of(EventAction.METRIC);
            }
        }

        validation.validate((HasValueOrThrow)source, notEmptySelectItemValidator);

        metainfoService.getDescendantClasses(NDAPNoDataTrigger.FQN, true,
                new BasicCallback<List<MetaClassLite>>(readyState)
                {
                    @Override
                    public void handleSuccess(List<MetaClassLite> result)
                    {
                        noDataTriggerTreeFqns = result.stream().map(MetaClassLite::getFqn).collect(Collectors.toList());
                    }
                });

        readyState.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                refreshProperties(fqn);
            }
        });
    }

    @Override
    public void refreshProperties(List<ClassFqn> fqns)
    {
        this.selectedFqns = fqns;

        if (fqns.isEmpty() || !fqnsContainsNDAPTriggerOnly())
        {
            source.getValueWidget().clear();
            source.trySetObjValue(new HashSet<>());
        }
        else
        {
            // сохранение предыдущего состояния необходимо перед clear() списка
            Set<String> selected = source.getValue() == null ? new HashSet<>() :
                    source.getValue()
                            .stream()
                            .map(SelectItemValueExtractor::<String>extract)
                            .collect(Collectors.toSet());

            if (fromInitSource != null)
            {
                selected = fromInitSource;
                fromInitSource = null;
            }

            source.getValueWidget().clear();
            source.getValueWidget().addItem(cmessages.metric(), EventAction.METRIC);

            if (fqnsContainsNoDataTriggerOnly(fqns))
            {
                source.trySetObjValue(ImmutableSet.of(EventAction.METRIC));
            }
            else
            {
                source.getValueWidget().addItem(cmessages.model(), EventAction.MODEL);
                source.trySetObjValue(selected);
            }
        }
    }

    @Override
    public void setReadyState(ReadyState rs)
    {
        readyState = rs;
    }

    private boolean isTriggerEvent(Event event)
    {
        EventType eventType = event.getEventType();
        return (eventType.equals(EventType.alertActivated)
                || (eventType.equals(EventType.alertDeactivated))
                || (eventType.equals(EventType.alertChanged)));
    }

    private boolean fqnsContainsNDAPTriggerOnly()
    {
        return selectedFqns.stream().filter(fqn -> !fqn.isSameClass(NDAPTrigger.FQN)).findFirst().orElse(null) == null;
    }

    private boolean fqnsContainsNoDataTriggerOnly(List<ClassFqn> fqns)
    {
        return noDataTriggerTreeFqns.containsAll(fqns);
    }
}
