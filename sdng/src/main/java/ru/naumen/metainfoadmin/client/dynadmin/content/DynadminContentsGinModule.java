package ru.naumen.metainfoadmin.client.dynadmin.content;

import jakarta.inject.Singleton;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.content.toolbar.ActionToolFactory;
import ru.naumen.core.client.content.toolbar.ToolBarContentPresenter;
import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.client.widgets.select2.SelectListGinModule;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.AddDeleteFromObjectListTool;
import ru.naumen.metainfo.shared.ui.AddFileTool;
import ru.naumen.metainfo.shared.ui.AddFromObjectListTool;
import ru.naumen.metainfo.shared.ui.AdvlistPrsSelectTool;
import ru.naumen.metainfo.shared.ui.ChangeStateTool;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfo.shared.ui.ClientInfo;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.CopyLinkToListTool;
import ru.naumen.metainfo.shared.ui.DeleteFromObjectListTool;
import ru.naumen.metainfo.shared.ui.EditFromObjectListTool;
import ru.naumen.metainfo.shared.ui.EditTool;
import ru.naumen.metainfo.shared.ui.EditablePropertyList;
import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;
import ru.naumen.metainfo.shared.ui.EventList;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.MassEditFromObjectListTool;
import ru.naumen.metainfo.shared.ui.MassProblems;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.PropertyList;
import ru.naumen.metainfo.shared.ui.RefreshObjectListTool;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.ReportContent;
import ru.naumen.metainfo.shared.ui.ReportsList;
import ru.naumen.metainfo.shared.ui.SearchBoxTool;
import ru.naumen.metainfo.shared.ui.SelectCase;
import ru.naumen.metainfo.shared.ui.SelectClient;
import ru.naumen.metainfo.shared.ui.SelectContacts;
import ru.naumen.metainfo.shared.ui.SelectParent;
import ru.naumen.metainfo.shared.ui.SelectScCase;
import ru.naumen.metainfo.shared.ui.ShowRemovedTool;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.metainfo.shared.ui.UserHistoryList;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.metainfo.shared.ui.WorkflowContent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.SelectCaseContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.TabBarContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.ChildObjectListContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.CreateOrEditServiceCallContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.CreateServiceCallContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.HierarchyGridContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.NotWindowContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.RelObjPropertyListContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.RelObjectListContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.SelectCaseContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.SelectParentContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.TrueContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.WindowAndCreateContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.WindowContextPredicate;
import ru.naumen.metainfoadmin.client.dynadmin.content.embeddedapplications.EditEmbeddedApplicationContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.embeddedapplications.EmbeddedApplicationContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.embeddedapplications.EmbeddedApplicationContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.forms.EditSelectCaseContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.forms.EditSelectParentContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.forms.EditTabBarContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.EditHierarchyGridContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.HierarchyGridContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.HierarchyGridContentPresenterLite;
import ru.naumen.metainfoadmin.client.dynadmin.content.massproblems.EditMassProblemsContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.massproblems.MassProblemsContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.massproblems.MassProblemsContentGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.content.massproblems.MassProblemsContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListContentGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.childobjectlist.ChildObjectListContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.childobjectlist.EditChildObjectListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.commentlist.CommentListContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.commentlist.EditCommentListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventlist.EditEventListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventlist.EventListContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filelist.EditFileListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filelist.FileListContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.objectlist.EditObjectListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.objectlist.ObjectsListContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist.EditRelObjListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist.RelObjListContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.userhistorylist.EditUserHistoryListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.userhistorylist.UserHistoryListContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.ContentPresentersGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.FormContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.LayoutContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.SelectCaseContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.TabBarContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.WindowContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.ToolPanelContentFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.AttributeToolPanelContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.separator.ToolSeparator;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.separator.ToolSeparatorFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.EditPropertyListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.EditPropertyListContentPresenterBase;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.PropertyListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.PropertyListContentPresenter.AttributeToolPanelContentPresenterFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.editablepropertylist.EditEditablePropertyListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.editablepropertylist.EditablePropertyListContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.editablepropertylist.EditablePropertyListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.PropertyListContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.clientinfo.ClientInfoContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.clientinfo.ClientInfoContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.clientinfo.ClientInfoFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.clientinfo.EditClientInfoContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.relobjpropertylist.EditRelObjPropertyListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.relobjpropertylist.RelObjPropertyListContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.relobjpropertylist.RelObjPropertyListContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.relobjpropertylist.RelObjPropertyListFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.selectclient.SelectClientContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.selectclient.SelectClientContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.selectcontacts.EditSelectContactsContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.selectcontacts.SelectContactsContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.selectcontacts.SelectContactsContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.selectparent.SelectParentContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.selectparent.SelectParentContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.selectsccase.SelectScCaseContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.selectsccase.SelectScCaseContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.AddDeleteFromObjectListToolFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.AddFileToolFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.AddFromObjectListToolFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.AdvlistPrsSelectToolAdminPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.EditFromObjectListToolFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.EditToolFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.MassEditFromObjectListToolFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.UserEventAdminToolFactory;
import ru.naumen.metainfoadmin.client.wf.graph.content.EditWorkflowContentAdminPresenter;
import ru.naumen.metainfoadmin.client.wf.graph.content.WorkflowContentAdminCreator;
import ru.naumen.metainfoadmin.client.wf.graph.content.WorkflowContentAdminPresenter;
import ru.naumen.objectlist.client.mode.passive.SearchBoxToolPresenterPassive;
import ru.naumen.objectlist.shared.CustomList;
import ru.naumen.objectlist.shared.RelationFormList;
import ru.naumen.objectlist.shared.ReportInstanceList;
import ru.naumen.reports.client.metainfoadmin.content.EditReportContentPresenter;
import ru.naumen.reports.client.metainfoadmin.content.ReportContentCreator;
import ru.naumen.reports.client.metainfoadmin.content.ReportContentPresenter;
import ru.naumen.reports.client.metainfoadmin.list.EditReportsListContentPresenter;
import ru.naumen.reports.client.metainfoadmin.list.ReportsListCreator;
import ru.naumen.reports.client.metainfoadmin.list.ReportsListPresenter;

/**
 * <AUTHOR>
 * @since 27.02.2013
 *
 */
public class DynadminContentsGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        install(new ContentPresentersGinModule());
        install(new ObjectListContentGinModule());
        install(new MassProblemsContentGinModule());

        //@formatter:off
        //ActionTool
        install(DynadminContentGinModule.create(ActionTool.class)
                .setFactory(new TypeLiteral<ActionToolFactory<ActionTool, UIContext>>(){}));
        //ActionTool        
        install(DynadminContentGinModule.create(AdvlistPrsSelectTool.class)               
                .setPresenter(AdvlistPrsSelectToolAdminPresenter.class));        
        install(DynadminContentGinModule.create(ShowRemovedTool.class)
                .setFactory(new TypeLiteral<ActionToolFactory<ShowRemovedTool, UIContext>>(){}));
        install(DynadminContentGinModule.create(RefreshObjectListTool.class)
                .setFactory(new TypeLiteral<ActionToolFactory<RefreshObjectListTool, UIContext>>(){}));
        install(DynadminContentGinModule.create(AddFromObjectListTool.class)
                .setFactory(new TypeLiteral<AddFromObjectListToolFactory<UIContext>>(){}));
        install(DynadminContentGinModule.create(EditFromObjectListTool.class)
                .setFactory(new TypeLiteral<EditFromObjectListToolFactory<UIContext>>(){}));
        install(DynadminContentGinModule.create(MassEditFromObjectListTool.class)
                .setFactory(new TypeLiteral<MassEditFromObjectListToolFactory<UIContext>>(){}));
        install(DynadminContentGinModule.create(AddDeleteFromObjectListTool.class)
                .setFactory(new TypeLiteral<AddDeleteFromObjectListToolFactory<UIContext>>(){}));
        install(DynadminContentGinModule.create(DeleteFromObjectListTool.class)
                .setFactory(new TypeLiteral<ActionToolFactory<DeleteFromObjectListTool, UIContext>>(){}));
        install(DynadminContentGinModule.create(ChangeStateTool.class)
                .setFactory(new TypeLiteral<ActionToolFactory<ChangeStateTool, UIContext>>(){}));
        install(DynadminContentGinModule.create(UserEventTool.class)
                .setFactory(new TypeLiteral<UserEventAdminToolFactory<UIContext>>(){}));
        install(DynadminContentGinModule.create(EditTool.class)
                .setFactory(new TypeLiteral<EditToolFactory<UIContext>>(){}));
        install(DynadminContentGinModule.create(AddFileTool.class)
                .setFactory(new TypeLiteral<AddFileToolFactory<UIContext>>(){}));
        install(DynadminContentGinModule.create(CopyLinkToListTool.class)
                .setFactory(new TypeLiteral<ActionToolFactory<CopyLinkToListTool, UIContext>>(){}));
        install(DynadminContentGinModule.create(SearchBoxTool.class)
                .setPresenter(new TypeLiteral<SearchBoxToolPresenterPassive<UIContext>>(){}));

        //ChildObjectList
        install(DynadminFlowContentGinModule.createFlow(ChildObjectList.class)
                .setContentCreator(ChildObjectListContentCreator.class)
                .setContentCreatorPredicate(ChildObjectListContextPredicate.class)
                .setEditContentPresenter(EditChildObjectListContentPresenter.class)
                .setPresenter(new TypeLiteral<ObjectListBaseContentPresenter<ChildObjectList>>(){}));
        //ClientInfo
        install(DynadminFlowContentGinModule.createFlow(ClientInfo.class)
                .setContentCreator(ClientInfoContentCreator.class)
                .setContentCreatorPredicate(CreateServiceCallContextPredicate.class)
                .setEditContentPresenter(EditClientInfoContentPresenter.class)
                .setFactory(ClientInfoFactory.class)
                .setPresenter(ClientInfoContentPresenter.class));
        //CommentList
        install(DynadminFlowContentGinModule.createFlow(CommentList.class)
                .setContentCreator(CommentListContentCreator.class)
                .setContentCreatorPredicate(WindowContextPredicate.class)
                .setEditContentPresenter(EditCommentListContentPresenter.class)
                .setPresenter(new TypeLiteral<ObjectListBaseContentPresenter<CommentList>>(){}));
        //CustomList
        install(DynadminContentGinModule.create(CustomList.class)
                .setPresenter(new TypeLiteral<ObjectListBaseContentPresenter<CustomList>>(){}));
        //RelationList
        install(DynadminContentGinModule.create(RelationFormList.class)
                .setPresenter(new TypeLiteral<ObjectListBaseContentPresenter<RelationFormList>>(){}));
        //ReportInstanceList
        install(DynadminContentGinModule.create(ReportInstanceList.class)
                .setPresenter(new TypeLiteral<ObjectListBaseContentPresenter<ReportInstanceList>>(){}));
        //EditablePropertyList
        install(DynadminFlowContentGinModule.createFlow(EditablePropertyList.class)
                .setContentCreator(new TypeLiteral<EditablePropertyListContentCreator>(){})
                .setContentCreatorPredicate(NotWindowContextPredicate.class)
                .setEditContentPresenter(new TypeLiteral<EditEditablePropertyListContentPresenter>(){})
                .setPresenter(EditablePropertyListContentPresenter.class));
        //EventList
        install(DynadminFlowContentGinModule.createFlow(EventList.class)
                .setContentCreator(EventListContentCreator.class)
                .setContentCreatorPredicate(WindowContextPredicate.class)
                .setEditContentPresenter(EditEventListContentPresenter.class)
                .setPresenter(new TypeLiteral<ObjectListBaseContentPresenter<EventList>>(){}));
        //UserHistoryList
        install(DynadminFlowContentGinModule.createFlow(UserHistoryList.class)
                .setContentCreator(UserHistoryListContentCreator.class)
                .setContentCreatorPredicate(UserHistoryListContentCreator.class)
                .setEditContentPresenter(EditUserHistoryListContentPresenter.class)
                .setPresenter(new TypeLiteral<ObjectListBaseContentPresenter<UserHistoryList>>(){}));
        //FileList
        install(DynadminFlowContentGinModule.createFlow(FileList.class)
                .setContentCreator(FileListContentCreator.class)
                .setContentCreatorPredicate(WindowAndCreateContextPredicate.class)
                .setEditContentPresenter(new TypeLiteral<EditFileListContentPresenter>(){})
                .setPresenter(new TypeLiteral<ObjectListBaseContentPresenter<FileList>>(){}));
        //Form
        install(DynadminContentGinModule.create(Form.class)
                .setPresenter(FormContentPresenter.class));
        //Layout
        install(DynadminContentGinModule.create(Layout.class)
                .setPresenter(LayoutContentPresenter.class));
        //MassProblems
        install(DynadminFlowContentGinModule.createFlow(MassProblems.class)
                .setContentCreator(MassProblemsContentCreator.class)
                .setContentCreatorPredicate(CreateServiceCallContextPredicate.class)
                .setEditContentPresenter(EditMassProblemsContentPresenter.class)
                .setPresenter(MassProblemsContentPresenter.class));
        //ObjectList
        install(DynadminFlowContentGinModule.createFlow(ObjectList.class)
                .setContentCreator(ObjectsListContentCreator.class)
                .setContentCreatorPredicate(TrueContextPredicate.class)
                .setEditContentPresenter(EditObjectListContentPresenter.class)
                .setPresenter(new TypeLiteral<ObjectListBaseContentPresenter<ObjectList>>(){}));
        //PropertyList
        install(DynadminFlowContentGinModule.createFlow(PropertyList.class)
                .setContentCreator(PropertyListContentCreator.class)
                .setEditContentPresenter(new TypeLiteral<EditPropertyListContentPresenter<PropertyList>>(){})
                .setPresenter(new TypeLiteral<PropertyListContentPresenter<PropertyList>>(){}));
        //RelObjectList
        install(DynadminFlowContentGinModule.createFlow(RelObjectList.class)
                .setContentCreator(RelObjListContentCreator.class)
                .setContentCreatorPredicate(RelObjectListContextPredicate.class)
                .setEditContentPresenter(EditRelObjListContentPresenter.class)
                .setPresenter(new TypeLiteral<ObjectListBaseContentPresenter<RelObjectList>>(){}));
        //RelObjPropertyList
        install(DynadminFlowContentGinModule.createFlow(RelObjPropertyList.class)
                .setContentCreator(RelObjPropertyListContentCreator.class)
                .setContentCreatorPredicate(RelObjPropertyListContextPredicate.class)
                .setEditContentPresenter(EditRelObjPropertyListContentPresenter.class)
                .setFactory(RelObjPropertyListFactory.class)
                .setPresenter(RelObjPropertyListContentPresenter.class));
        //ReportContent
        install(DynadminFlowContentGinModule.createFlow(ReportContent.class)
                .setContentCreator(ReportContentCreator.class)
                .setContentCreatorPredicate(WindowContextPredicate.class)
                .setEditContentPresenter(EditReportContentPresenter.class)
                .setPresenter(ReportContentPresenter.class));
        //ReportsList
        install(DynadminFlowContentGinModule.createFlow(ReportsList.class)
                .setContentCreator(ReportsListCreator.class)
                .setContentCreatorPredicate(WindowContextPredicate.class)
                .setEditContentPresenter(EditReportsListContentPresenter.class)
                .setPresenter(ReportsListPresenter.class));
        //SelectCase
        install(DynadminFlowContentGinModule.createFlow(SelectCase.class)
                .setContentCreator(SelectCaseContentCreator.class)
                .setContentCreatorPredicate(SelectCaseContextPredicate.class)
                .setEditContentPresenter(EditSelectCaseContentPresenter.class)
                .setPresenter(SelectCaseContentPresenter.class));
        //SelectClient
        install(DynadminFlowContentGinModule.createFlow(SelectClient.class)
                .setContentCreator(SelectClientContentCreator.class)
                .setContentCreatorPredicate(CreateServiceCallContextPredicate.class)
                .setEditContentPresenter(new TypeLiteral<EditPropertyListContentPresenterBase<SelectClient>>(){})
                .setPresenter(SelectClientContentPresenter.class));
        //SelectContacts
        install(DynadminFlowContentGinModule.createFlow(SelectContacts.class)
                .setContentCreator(SelectContactsContentCreator.class)
                .setContentCreatorPredicate(CreateOrEditServiceCallContextPredicate.class)
                .setEditContentPresenter(EditSelectContactsContentPresenter.class)
                .setPresenter(SelectContactsContentPresenter.class));
        //SelectParent
        install(DynadminFlowContentGinModule.createFlow(SelectParent.class)
                .setContentCreator(SelectParentContentCreator.class)
                .setContentCreatorPredicate(SelectParentContextPredicate.class)
                .setEditContentPresenter(EditSelectParentContentPresenter.class)
                .setPresenter(SelectParentContentPresenter.class));
        //SelectScCase
        install(DynadminFlowContentGinModule.createFlow(SelectScCase.class)
                .setContentCreator(SelectScCaseContentCreator.class)
                .setContentCreatorPredicate(CreateServiceCallContextPredicate.class)
                .setEditContentPresenter(new TypeLiteral<EditPropertyListContentPresenterBase<SelectScCase>>(){})
                .setPresenter(SelectScCaseContentPresenter.class));
        //TabBar
        install(DynadminFlowContentGinModule.createFlow(TabBar.class)
                .setContentCreator(TabBarContentCreator.class)
                .setEditContentPresenter(EditTabBarContentPresenter.class)
                .setPresenter(TabBarContentPresenter.class));
        //ToolBar
        install(DynadminContentGinModule.create(ToolBar.class)
                .setPresenter(new TypeLiteral<ToolBarContentPresenter<UIContext>>(){}));
        //ToolPanel
        install(DynadminContentGinModule.create(ToolPanel.class)
                .setFactory(new TypeLiteral<ToolPanelContentFactory<UIContext>>(){}));
        //ToolSeparator
        install(DynadminContentGinModule.create(ToolSeparator.class)
                .setFactory(new TypeLiteral<ToolSeparatorFactory<UIContext>>(){}));
        //Window
        install(DynadminContentGinModule.create(Window.class)
                .setPresenter(WindowContentPresenter.class));
        //WorkflowContentAdmin
        install(DynadminFlowContentGinModule.createFlow(WorkflowContent.class)
                .setContentCreator(WorkflowContentAdminCreator.class)
                .setContentCreatorPredicate(WorkflowContentAdminCreator.class)
                .setEditContentPresenter(EditWorkflowContentAdminPresenter.class)
                .setPresenter(WorkflowContentAdminPresenter.class));
        //EmbeddedApplication
        install(DynadminFlowContentGinModule.createFlow(EmbeddedApplicationContent.class)
                .setContentCreator(EmbeddedApplicationContentCreator.class)             
                .setEditContentPresenter(EditEmbeddedApplicationContentPresenter.class)
                .setPresenter(EmbeddedApplicationContentPresenter.class));
        //HierarchyGrid
        install(DynadminFlowContentGinModule.createFlow(HierarchyGrid.class)
                .setContentCreatorPredicate(HierarchyGridContextPredicate.class)
                .setContentCreator(HierarchyGridContentCreator.class)
                .setEditContentPresenter(EditHierarchyGridContentPresenter.class)
                .setPresenter(HierarchyGridContentPresenterLite.class));

        install(SelectListGinModule.create(AttributeGroup.class));
        bind(new TypeLiteral<SingleSelectProperty<AttributeGroup>>(){});
        //@formatter:on
        bind(ContentFormHelper.class).in(Singleton.class);

        install(new GinFactoryModuleBuilder()
                .implement(new TypeLiteral<AttributeToolPanelContentPresenter<UIContext>>()
                           {
                           },
                        new TypeLiteral<AttributeToolPanelContentPresenter<UIContext>>()
                        {
                        })
                .build(new TypeLiteral<AttributeToolPanelContentPresenterFactory>()
                {
                }));
    }
}
