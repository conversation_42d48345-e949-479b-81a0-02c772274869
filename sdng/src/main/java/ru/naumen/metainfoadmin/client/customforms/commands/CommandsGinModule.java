package ru.naumen.metainfoadmin.client.customforms.commands;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;

/**
 *
 * <AUTHOR>
 * @since 26 апр. 2016 г.
 */
public class CommandsGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        bind(CustomFormsCommandFactoryInitializer.class).asEagerSingleton();

        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, DeleteCommand.class)
            .build(new TypeLiteral<CommandProvider<DeleteCommand, AttributeCommandParam>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, EditCommand.class)
            .build(new TypeLiteral<CommandProvider<EditCommand, AttributeCommandParam>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, MoveUpCommand.class)
            .build(new TypeLiteral<CommandProvider<MoveUpCommand, AttributeCommandParam>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, MoveDownCommand.class)
            .build(new TypeLiteral<CommandProvider<MoveDownCommand, AttributeCommandParam>>(){}));
        //@formatter:on
    }
}
