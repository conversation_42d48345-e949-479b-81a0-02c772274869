package ru.naumen.metainfoadmin.client.eventcleaner.rule.card;

import static ru.naumen.admin.client.administration.AdministrationPresenter.EVENT_CLEANER_TAB_ID;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.admin.client.administration.AdministrationPlace;
import ru.naumen.admin.client.eventcleaner.EventCleanerSettingsMessages;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.ResourceCallback;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.eventcleaner.rule.dispatch.GetEventStorageRuleAction;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;

/**
 * Представление карточки правила очистки лога событий
 * <AUTHOR>
 * @since 09.07.2023
 */
public class EventStorageRuleCardPresenter extends AdminTabPresenter<EventStorageRulePlace>
{
    private OnStartCallback<DtObject> refreshCallback = new SafeOnStartBasicCallback<DtObject>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            infoPresenter.setRule(value);
            getDisplay().setTitle(value.getTitle());
        }
    };

    @Inject
    private EventStorageRuleInfoPresenter infoPresenter;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private EventCleanerSettingsMessages cleanerSettingsMessages;
    @Inject
    private DispatchAsync dispatch;

    private String code = null;

    @Inject
    public EventStorageRuleCardPresenter(AdminTabDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(EventStorageRulePlace place)
    {
        super.init(place);
        code = place.getCode();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        infoPresenter.refreshDisplay();
    }

    private void afterTemplateLoaded(DtObject ruleDto)
    {
        prevPageLinkPresenter.bind(cleanerSettingsMessages.toTheRuleList(),
                new AdministrationPlace(EVENT_CLEANER_TAB_ID), true);
        getDisplay().setTitle(ruleDto.getTitle());

        infoPresenter.setRule(ruleDto);
        addContent(infoPresenter, "info");
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();
        infoPresenter.init(refreshCallback);

        dispatch.execute(new GetEventStorageRuleAction(code), new ResourceCallback<SimpleResult<DtObject>>(cmessages)
        {
            @Override
            protected void handleSuccess(SimpleResult<DtObject> value)
            {
                afterTemplateLoaded(value.get());
            }
        });
    }

    @Override
    protected void onUnbind()
    {
        infoPresenter.unbind();
        super.onUnbind();
    }
}
