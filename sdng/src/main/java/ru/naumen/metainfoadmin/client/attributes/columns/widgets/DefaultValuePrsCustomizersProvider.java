package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Provider;

/**
 * Провайдер кастомайзеров создания виджетов для отображения в колонке "Значение по умолчанию"
 * в списке атрибутов
 *
 * <AUTHOR>
 * @since 18 сент. 2018 г.
 *
 */
public class DefaultValuePrsCustomizersProvider implements Provider<List<DefaultValuePrsCustomizer>>
{
    @Override
    public List<DefaultValuePrsCustomizer> get()
    {
        return Arrays.asList();
    }
}
