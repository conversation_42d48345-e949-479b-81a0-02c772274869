/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.view;

import java.util.Collection;
import java.util.List;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationUtils;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;

import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.inject.Inject;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 31 янв. 2014 г.
 *
 */
@Singleton
public class ExampleWidgetFactoryCaseList extends ExampleWidgetFactoryView<Collection<ClassFqn>>
{
    @Inject
    private PresentationUtils presentationUtils;

    @Override
    public IsWidget createExampleWidget(PresentationContext context)
    {
        Attribute attribute = context.getAttribute();
        CommonUtils.assertAttrNotNull(attribute);
        List<DtObject> bOs = presentationUtils.getBOs(attribute.getExample(), "");
        return new HTML(presentationUtils.getEmptyLink(bOs)); // NOPMD NSDPRD-28509 unsafe html
    }
}