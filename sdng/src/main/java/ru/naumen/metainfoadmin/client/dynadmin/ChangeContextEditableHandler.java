package ru.naumen.metainfoadmin.client.dynadmin;

import com.google.gwt.event.shared.EventHandler;

/**
 * Обработчик {@link ChangeContextEditableEvent события} изменения 
 * {@link UIContext#isEditable() признака возможности редактирования} формы/карточки объекта
 *
 * <AUTHOR>
 *
 */
public interface ChangeContextEditableHandler extends EventHandler
{
    void onChangeEditable(ChangeContextEditableEvent e);
}
