/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.display;

import java.util.Collection;

import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.escalation.EscalationScheme;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.text.shared.SafeHtmlRenderer;
import com.google.inject.Inject;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 01.11.2012
 *
 */
@Singleton
public class EscalationSchemesSafeHtmlRenderer implements SafeHtmlRenderer<Collection<EscalationScheme>>
{
    @Inject
    Formatters formatters;
    @Inject
    I18nUtil i18nUtil;

    @Override
    public SafeHtml render(Collection<EscalationScheme> object)
    {
        return SafeHtmlUtils.fromString(
                formatters.formatLocalizedWithEllipsis(object, 3, i18nUtil.getTitleExtractor()));
    }

    @Override
    public void render(Collection<EscalationScheme> object, SafeHtmlBuilder builder)
    {
        builder.append(render(object));
    }
}