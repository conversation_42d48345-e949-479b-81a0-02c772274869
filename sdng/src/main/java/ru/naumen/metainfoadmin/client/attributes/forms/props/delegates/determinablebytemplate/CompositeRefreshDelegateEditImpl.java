package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determinablebytemplate;

import jakarta.inject.Inject;

import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.metainfo.shared.dispatch2.CheckIfAttributeCanBeCompositeAction;
import ru.naumen.metainfo.shared.dispatch2.CheckIfAttributeCanBeCompositeResponse;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат проверяет может ли редактируемый атрибут быть составным. В случае если нет
 * свойство "Составной" делается неактивным и на форме появляется желтая плашка с 
 * пояснением почему атрибут не может быть составным.
 *
 * <AUTHOR>
 *
 */
public class CompositeRefreshDelegateEditImpl extends CompositeRefreshDelegateImpl<ObjectFormEdit>
{
    @Inject
    private DispatchAsync dispatch;

    @Override
    public void refreshProperty(final PropertyContainerContext context, final BooleanCheckBoxProperty property,
            final AsyncCallback<Boolean> callback)
    {
        super.refreshProperty(context, property, new BasicCallback<Boolean>()
        {
            @Override
            public void handleSuccess(Boolean result)
            {
                if (!Boolean.TRUE.equals(result))
                {
                    callback.onSuccess(result);
                    return;
                }

                boolean isComposite = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPOSITE);
                if (isComposite)
                {
                    callback.onSuccess(result);
                    return;
                }

                MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
                String attrCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE);

                if (Employee.FQN.isSameClass(metaClass.getFqn()) && AbstractBO.TITLE.equals(attrCode))
                {
                    callback.onSuccess(false);
                    return;
                }

                if (SuperUser.FQN.isSameClass(metaClass.getFqn())
                    && (SuperUser.PASSWORD.equals(attrCode) || SuperUser.LOGIN.equals(attrCode)))
                {
                    callback.onSuccess(false);
                    return;
                }

                dispatch.execute(new CheckIfAttributeCanBeCompositeAction(metaClass.getFqn(), attrCode),
                        new BasicCallback<CheckIfAttributeCanBeCompositeResponse>()
                        {
                            @Override
                            public void handleSuccess(CheckIfAttributeCanBeCompositeResponse response)
                            {

                                if (!response.canBeComposite())
                                {
                                    context.getDisplay().addAttentionMessage(
                                            SafeHtmlUtils.fromSafeConstant(response.getErrorMessage()));
                                    property.getValueWidget().setEnabled(false);
                                }

                                callback.onSuccess(true);
                            }
                        });
            }
        });
    }
}
