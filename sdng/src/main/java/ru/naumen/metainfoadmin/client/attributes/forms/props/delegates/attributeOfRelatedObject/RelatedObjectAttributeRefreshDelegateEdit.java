package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления свойства "Атрибут".
 * <AUTHOR>
 * @since 08.08.18
 */
public class RelatedObjectAttributeRefreshDelegateEdit
        implements AttributeFormPropertyDelegateRefresh<ObjectFormEdit, SelectItem, ListBoxProperty>
{
    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        IProperties propertyValues = context.getPropertyValues();
        String typeCode = propertyValues.getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        if (!typeCode.equals(AttributeOfRelatedObjectSettings.CODE))
        {
            callback.onSuccess(false);
            return;
        }

        String objectAttribute = context.getPropertyValues()
                .getProperty(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE);
        ClassFqn metaclass = ClassFqn
                .parse(context.getPropertyValues().getProperty(AttributeFormPropertyCode.RELATED_OBJECT_METACLASS));

        metainfoService.getMetaClass(metaclass, new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass value)
            {
                String attrCode = AttributeFqn.parse(objectAttribute).getCode();
                if (value.hasAttribute(attrCode))
                {
                    property.getValueWidget().addItem(
                            value.getTitle() + "/" + value.getAttribute(attrCode).getTitle(), objectAttribute);
                    property.getValueWidget().setObjValue(objectAttribute);
                }

                context.setPropertyEnabled(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE, false);
                context.setDisabled(AttributeFormPropertyCode.HIDE_ARCHIVED);
                property.setDisable();

                callback.onSuccess(true);
            }
        });
    }
}
