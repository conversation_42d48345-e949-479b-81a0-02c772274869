package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Создан для возможности кастомизации отображения значений по умолчанию
 * в списке атрибутов
 *
 * <AUTHOR>
 * @since 18 сент. 2018 г.
 *
 */
public interface DefaultValuePrsCustomizer
{
    IsWidget createWidget(WidgetContext<Attribute> context);
}
