package ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb;

import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.name.Named;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplayImpl;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsPlace;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.BreadCrumbCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DeleteCrumbCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EditCrumbCommand;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * Презентер карточки хлебной крошки
 *
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public class BreadCrumbPropertiesPresenter extends BasicPresenter<InfoDisplayImpl>
{
    private final AsyncCallback<DtoContainer<NavigationSettings>> deleteCallback =
            new BasicCallback<DtoContainer<NavigationSettings>>()
            {
                @Override
                protected void handleSuccess(@Nullable DtoContainer<NavigationSettings> settings)
                {
                    if (settings != null)
                    {
                        unbind();
                        placeController.goTo(new InterfaceSettingsPlace("navigation"));
                    }
                }
            };
    @Inject
    private BreadCrumbHelper helper;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> title;
    private Property<String> settingsSet;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;
    @Inject
    private BreadCrumbMessages messages;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private PlaceController placeController;
    @Inject
    private BreadCrumbRelationAttributesPresenter relationAttributesPresenter;

    private BreadCrumbContext context;
    private BreadCrumbCommandParam refreshParam;
    private BreadCrumbCommandParam deleteParam;
    private ToolBarDisplayMediator<Crumb> toolBar;

    @Inject
    public BreadCrumbPropertiesPresenter(InfoDisplayImpl display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(BreadCrumbContext context, OnStartCallback<DtoContainer<NavigationSettings>> refreshCallback)
    {
        this.context = context;
        deleteParam = new BreadCrumbCommandParam(context.getSettings(), context.getCrumb(), deleteCallback);
        refreshParam = new BreadCrumbCommandParam(context.getSettings(), context.getCrumb(), refreshCallback);

        relationAttributesPresenter.init(context, refreshCallback);
    }

    @Override
    public void refreshDisplay()
    {
        if (context.getCrumb().getRelationAttributes().size() > 1)
        {
            getDisplay().getAttention().setText(messages.attentionText());
        }

        refreshParam.setSettings(context.getSettings());
        deleteParam.setSettings(context.getSettings());
        refreshParam.setValue(context.getCrumb());
        deleteParam.setValue(context.getCrumb());
        title.setValue(helper.getFullCrumbTitleWithLinks(context.getCrumb()));
        settingsSetOnFormCreator.setValueOnCardProperty(context.getCrumb().getSettingsSet(), settingsSet);
        super.refreshDisplay();
        toolBar.refresh(context.getCrumb());
    }

    @Override
    @SuppressWarnings({ "unchecked", "rawtypes" })
    protected void onBind()
    {
        getDisplay().setTitle(cmessages.properties());

        toolBar = new ToolBarDisplayMediator(getDisplay().getToolBar());
        ButtonPresenter<Crumb> editBtn = (ButtonPresenter<Crumb>)buttonFactory.create(ButtonCode.EDIT, cmessages.edit(),
                EditCrumbCommand.ID, refreshParam);
        editBtn.addPossibleFilter(crumb -> hasPermission(context.getSettings(), PermissionType.EDIT, crumb));
        toolBar.add(editBtn);
        ButtonPresenter<Crumb> delBtn = (ButtonPresenter<Crumb>)buttonFactory.create(ButtonCode.DELETE,
                cmessages.delete(), DeleteCrumbCommand.ID, deleteParam);
        delBtn.addPossibleFilter(crumb -> hasPermission(context.getSettings(), PermissionType.DELETE, crumb));
        toolBar.add(delBtn);
        toolBar.bind();

        title.setCaption(messages.objects());
        getDisplay().addControlledWidget(title);

        settingsSet = settingsSetOnFormCreator.createFieldOnCard();
        if (settingsSet != null)
        {
            getDisplay().addControlledWidget(settingsSet);
        }

        registerChildPresenter(relationAttributesPresenter);

        getDisplay().addControlledWidget(relationAttributesPresenter.getDisplay());
        relationAttributesPresenter.revealDisplay();
        refreshDisplay();
    }
}
