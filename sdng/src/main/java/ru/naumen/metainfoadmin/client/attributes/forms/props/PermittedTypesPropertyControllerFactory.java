/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.forms.props;

import java.util.Collection;

import ru.naumen.core.client.tree.selection.HierarchicalMultiSelectionModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;

public interface PermittedTypesPropertyControllerFactory<F extends ObjectForm>
        extends
        PropertyControllerSyncFactoryInj<Collection<DtObject>, PropertyBase<Collection<DtObject>,
                PopupValueCellTree<DtObject, Collection<DtObject>, HierarchicalMultiSelectionModel<DtObject>>>>
{

}