package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EditNavigationSettingsCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.NavigationSettingsChangedEvent;

/**
 * <AUTHOR>
 * @since 20 сент. 2013 г.
 */
public class NavigationSettingsInfoPresenter extends BasicPresenter<InfoDisplay>
{
    final class EditCommandParam extends CommandParam<DtoContainer<NavigationSettings>,
            DtoContainer<NavigationSettings>>
    {
        public EditCommandParam(AsyncCallback<DtoContainer<NavigationSettings>> callback)
        {
            super(new ValueSource<DtoContainer<NavigationSettings>>()
            {
                @Override
                public DtoContainer<NavigationSettings> getValue()
                {
                    return settings;
                }
            }, callback);
        }
    }

    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> showTopMenu;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> showLeftMenu;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> showBreadCrumb;

    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> showHomePage;

    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> showAdminArea;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> showUserArea;
    //    Системная область будет реализовываться на втором этапе работ по левому меню
    //    @Inject
    //    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    //    Property<Boolean> showSystemArea;

    @Inject
    private NavigationSettingsMessages messages;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private ButtonFactory buttonFactory;

    private final NavigationSettingsResources navigationSettingsResources;

    private final ToolBarDisplayMediator<DtoContainer<NavigationSettings>> toolBar;
    private DtoContainer<NavigationSettings> settings;
    private CommandParam<DtoContainer<NavigationSettings>, DtoContainer<NavigationSettings>> editParam;

    private final OnStartCallback<DtoContainer<NavigationSettings>> refreshCallback =
            new SafeOnStartBasicCallback<DtoContainer<NavigationSettings>>()
            {
                @Override
                protected void handleSuccess(DtoContainer<NavigationSettings> value)
                {
                    settings = value;
                    refreshDisplay();
                    eventBus.fireEvent(new NavigationSettingsChangedEvent(value));
                }
            };

    @Inject
    public NavigationSettingsInfoPresenter(InfoDisplay display, EventBus eventBus,
            NavigationSettingsResources navigationSettingsResources)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(display.getToolBar());
        this.navigationSettingsResources = navigationSettingsResources;
        this.navigationSettingsResources.css().ensureInjected();
    }

    public void init(DtoContainer<NavigationSettings> settings)
    {
        this.settings = settings;
        editParam = new EditCommandParam(refreshCallback);
    }

    @Override
    public void refreshDisplay()
    {
        refreshProperties();
        toolBar.refresh(settings);
    }

    @SuppressWarnings("unchecked")
    protected void bindToolBar()
    {
        toolBar.add((ButtonPresenter<DtoContainer<NavigationSettings>>)buttonFactory.create(ButtonCode.EDIT,
                cmessages.edit(),
                EditNavigationSettingsCommand.ID, editParam));
        toolBar.bind();
    }

    @Override
    protected void onBind()
    {
        display.setTitle(cmessages.visibility());
        bindToolBar();
        bindProperties();
    }

    private void bindProperties()
    {
        showTopMenu.setCaption(messages.showTopMenu());
        getDisplay().add(showTopMenu);
        showTopMenu.ensureDebugId("showTopMenu");

        showLeftMenu.setCaption(messages.showLeftMenu());
        getDisplay().add(showLeftMenu);
        showLeftMenu.ensureDebugId("showLeftMenu");

        showAdminArea.setCaption(messages.showAdminArea());
        getDisplay().add(showAdminArea);
        showAdminArea.ensureDebugId("showAdminArea");
        showAdminArea.getCaptionWidget().asWidget()
                .setStyleName(navigationSettingsResources.css().fastAccessPanelSetting());

        showUserArea.setCaption(messages.showUserArea());
        getDisplay().add(showUserArea);
        showUserArea.ensureDebugId("showUserArea");
        showUserArea.getCaptionWidget().asWidget()
                .setStyleName(navigationSettingsResources.css().fastAccessPanelSetting());

        //    Системная область будет реализовываться на втором этапе работ по левому меню
        //        showSystemArea.setCaption(messages.showSystemArea());
        //        getDisplay().add(showSystemArea);
        //        showSystemArea.ensureDebugId("showSystemArea");
        //        showSystemArea.getCaptionWidget().asWidget()
        //                .setStyleName(navigationSettingsResources.css().fastAccessPanelSetting());

        showBreadCrumb.setCaption(messages.showBreadCrumb());
        getDisplay().add(showBreadCrumb);
        showBreadCrumb.ensureDebugId("showBreadCrumb");

        showHomePage.setCaption(messages.showHomePage());
        getDisplay().add(showHomePage);
        showHomePage.ensureDebugId("showHomePage");
    }

    private void refreshProperties()
    {
        showTopMenu.setValue(Boolean.TRUE.equals(settings.get().isShowTopMenu()));
        showLeftMenu.setValue(Boolean.TRUE.equals(settings.get().getLeftMenu().isEnabled()));
        showAdminArea.setValue(Boolean.TRUE.equals(settings.get().getQuickAccessPanelSettings().isShowAdminArea()));
        showUserArea.setValue(Boolean.TRUE.equals(settings.get().getQuickAccessPanelSettings().isShowUserArea()));
        //    Системная область будет реализовываться на втором этапе работ по левому меню
        //        showSystemArea.setValue(Boolean.TRUE.equals(settings.getAccessPanelSettings().isShowSystemArea()));
        showBreadCrumb.setValue(Boolean.TRUE.equals(settings.get().isShowBreadCrumb()));
        showHomePage.setValue(Boolean.TRUE.equals(settings.get().isShowHomePage()));
    }
}
