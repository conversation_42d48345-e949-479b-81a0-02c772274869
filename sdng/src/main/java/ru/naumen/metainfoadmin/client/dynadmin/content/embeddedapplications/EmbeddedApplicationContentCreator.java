package ru.naumen.metainfoadmin.client.dynadmin.content.embeddedapplications;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import java.util.ArrayList;

import net.customware.gwt.dispatch.client.DispatchAsync;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.select.selmodel.SelectListCellGinModule;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.GetEmbeddedApplicationsAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.SimpleContentCreatorBase;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;

/**
 * {@link Presenter} для отображения параметров добавляемого контента типа "Встроенное приложение"
 *
 * <AUTHOR>
 * @since 22.08.2016
 */
public class EmbeddedApplicationContentCreator extends SimpleContentCreatorBase<EmbeddedApplicationContent>
{
    @Inject
    private NotNullValidator<SelectItem> notNullValidator;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private I18nUtil i18nUtil;
    @Inject
    private EmbeddedApplicationMessages messages;
    @Inject
    @Named(SelectListCellGinModule.NOT_SELECTED_CSS)
    private String notSelectedCssClass;
    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    @Inject
    private SelectListProperty<String, SelectItem> embeddedApplication;

    @Override
    protected void bindPropertiesInner()
    {
        super.bindPropertiesInner();
        embeddedApplication.setCaption(messages.applicationProperty());
        embeddedApplication.setValidationMarker(true);
        dispatch.execute(new GetEmbeddedApplicationsAction(),
                new BasicCallback<SimpleScriptedResult<List<EmbeddedApplication>>>()
                {
                    @Override
                    public void handleSuccess(SimpleScriptedResult<List<EmbeddedApplication>> response)
                    {
                        List<EmbeddedApplication> sorted = new ArrayList<>();
                        sorted.addAll(response.get());
                        sorted.sort(i18nUtil.getCaseInsensitiveTitleComparator());

                        for (EmbeddedApplication application : sorted)
                        {
                            ((SingleSelectCellList<String>)embeddedApplication.getValueWidget()).addItemWithStyle(
                                    metainfoUtils.getLocalizedValue(application.getTitle()), application.getCode(),
                                    application.isOn() ? null : notSelectedCssClass);
                        }

                        embeddedApplication.setValue(null);
                    }
                });
        embeddedApplication.addValueChangeHandler(event -> onEmbeddedApplicationChange());
        properties.add(embeddedApplication);
        DebugIdBuilder.ensureDebugId(embeddedApplication, "embeddedApplication");
        addValidation(embeddedApplication, notNullValidator);
    }

    @Override
    protected EmbeddedApplicationContent getContentInner()
    {
        EmbeddedApplicationContent content = contentProvider.get();
        content.setApplication(SelectListPropertyValueExtractor.getValue(embeddedApplication));
        return content;
    }

    private void onEmbeddedApplicationChange()
    {
        String application = SelectListPropertyValueExtractor.getValue(embeddedApplication);
        if (null != application)
        {
            RefreshEmbeddedApplicationEvent event = new RefreshEmbeddedApplicationEvent(application,
                    propertyRegistrations.get(embeddedApplication));
            context.getEventBus().fireEvent(event);
        }
    }
}
