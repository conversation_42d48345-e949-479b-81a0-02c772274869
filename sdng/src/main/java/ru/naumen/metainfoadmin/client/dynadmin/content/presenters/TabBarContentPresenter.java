package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import static com.google.common.base.Predicates.compose;
import static com.google.common.base.Predicates.in;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.logging.Logger;

import com.google.common.base.Functions;
import com.google.common.base.Splitter;
import com.google.common.collect.Iterables;
import com.google.common.collect.Sets;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.inject.client.AsyncProvider;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HasText;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.SimplePanel;
import com.google.gwt.user.client.ui.Widget;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.CoreGinjector.TabProxyPresenterFactory;
import ru.naumen.core.client.activity.AbstractPlace;
import ru.naumen.core.client.activity.PlaceParametersChangeEvent;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextChangedEvent;
import ru.naumen.core.client.content.ContextChangedHandler;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.mvp.TabProxyPresenter;
import ru.naumen.core.client.tabbar.TabBarPresenterLogic;
import ru.naumen.core.client.tabbar.TabHierarchyUtils;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.ui.ITab;
import ru.naumen.core.shared.ui.UIComponent;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.metainfoadmin.client.common.content.AbstractFlowContentPresenter;
import ru.naumen.metainfoadmin.client.common.content.ActionContentPanelDisplayImpl.ContentCommandAction;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.common.content.commands.TabContentCommandCode;
import ru.naumen.metainfoadmin.client.common.content.commands.TabContentCommandParam;
import ru.naumen.metainfoadmin.client.dynadmin.BasicUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeLayoutModeEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeUITemplateModeEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ColumnWidthChangedEvent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UITemplateContext;
import ru.naumen.metainfoadmin.client.templates.ui.UITemplateMessages;
import ru.naumen.metainfoadmin.client.templates.ui.UITemplateSelectListPresenter;

/**
 * {@link Presenter} настройки {@link TabBar панели вкладок}
 *
 * @see TabBarContentDisplay
 *
 * <AUTHOR>
 * @since 10.08.2010
 *
 */
@SuppressWarnings({ "rawtypes", "unchecked" })
//TODO NSDPRD-12460 dzevako выделить отдельный презентер для вкладок карточки
public class TabBarContentPresenter extends AbstractFlowContentPresenter<TabBarContentDisplay, TabBar, UIContext>
{
    private static final Logger LOG = Logger.getLogger(TabBarContentPresenter.class.getName());

    class EditObjectCaptionClickHandler implements ClickHandler
    {
        @Override
        public void onClick(ClickEvent event)
        {
            editWindowCaptionPresenter.init(getContext());
            editWindowCaptionPresenter.bind();
        }
    }

    @Inject
    ru.naumen.metainfoadmin.client.common.content.TabContentDisplayMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    private UITemplateMessages templateMessages;
    @Inject
    private Provider<UITemplateSelectListPresenter> selectUITemplatePresenterProvider;
    @Inject
    private WidgetResources widgetResources;
    @Inject
    MetainfoModificationServiceAsync metainfoModificationService;
    Presenter actBarPresenter;
    @Inject
    ButtonFactory buttonFactory;
    @Inject
    private TabProxyPresenterFactory tabProxyPresenterFactory;

    @Inject
    AdminContentFactory contentFactory;
    @Inject
    EditWindowCaptionPresenter editWindowCaptionPresenter;
    @Inject
    protected PlaceController placeController;

    @Inject
    MetainfoServiceAsync metainfoService;

    boolean initialized;
    ContentPresenter<ToolPanel, UIContext> toolPanelPresenter;

    // <tab, соответсвующий табу презентер>
    HashMap<Tab, Presenter> contents = new HashMap<>();

    List<Presenter> layoutPresenters = new ArrayList<>();

    @Inject
    Dialogs dialog;
    int currentTab = -1;

    private final ToolBarDisplayMediator<TabBar> toolBar;

    protected OnStartCallback<Void> refreshCallback = new OnStartBasicCallback<Void>(getDisplay())
    {
        @Override
        protected void handleSuccess(Void value)
        {
            refreshDisplay();
        }
    };

    private final BasicCallback<List<Tab>> editTabsCallback = new BasicCallback<List<Tab>>(getDisplay())
    {
        @Override
        protected void handleSuccess(final List<Tab> tabs)
        {
            if (tabs == null)
            {
                return;
            }

            clearTabs();
            if (CollectionUtils.isNotEmpty(tabs))
            {
                buildTabs();
                revealDisplay();
            }
            else
            {
                getDisplay().destroy();
            }
        }
    };

    @Inject
    public TabBarContentPresenter(TabBarContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "TabBar");
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    @Override
    protected void initStyles()
    {
        super.initStyles();
        getDisplay().asWidget().addStyleName(WidgetResources.INSTANCE.contentLayout().infoContent());
    }

    public void bindActionBar(Presenter actBarPresenter)
    {
        this.actBarPresenter = actBarPresenter;
        getDisplay().drawActionBar(actBarPresenter.getDisplay());
    }

    /**
     * Перемещает вкладку на одну позицию вправо или влево
     *
     * @param tab
     *            вкладка
     * @param toLeft
     *            флаг: при значении true вкладка перемещается влево, при значении false - вправо
     */
    public void moveTab(Tab tab, boolean toLeft)
    {
        int oldIndex = tabs().indexOf(tab);
        int newIndex = oldIndex + (toLeft ? -1 : +1);

        if (oldIndex < 0 || newIndex < 0 || newIndex >= tabs().size())
        {
            return;
        }

        Tab otherTab = tabs().get(newIndex);
        tabs().set(newIndex, tab);
        tabs().set(oldIndex, otherTab);

        getDisplay().moveTab(oldIndex, toLeft);

        saveUI(null);
    }

    @Override
    public void onHide()
    {
        int selectedTab = getDisplay().getSelectedTab();
        if (isTabIndexValid(selectedTab))
        {
            Presenter presenter = contents.get(tabs().get(selectedTab));
            if (presenter != null)
            {
                presenter.hideDisplay();
            }
        }
    }

    @Override
    public void onReveal()
    {
        revealSelectedTab();
    }

    /**
     * Показать текущую вкладку
     * и, при необходимости, добавить атрибутику внутреннего скроллинга
     */
    public void revealSelectedTab()
    {
        int selectedTab = getDisplay().getSelectedTab();
        if (isTabIndexValid(selectedTab))
        {
            ITab tab = tabs().get(selectedTab);
            Presenter tabPresenter = contents.get(tab);
            if (tabPresenter != null)
            {
                TabBarPresenterLogic.provideInternalScrollToTab(tab, tabPresenter.getDisplay().asWidget().getParent());
                tabPresenter.revealDisplay();
            }
        }
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        getDisplay().setCaptionText(""); // У TabBar нет заголовка. надо понять нужен ли он
    }

    /**
     * Удаляет заданную вкладку
     *
     * @param tab
     */
    public void removeTab(final Tab tab)
    {
        dialog.question("",
                cmessages.confirmDeleteQuestion(cmessages.tab(), metainfoUtils.getLocalizedValue(tab.getCaption())),
                cmessages.deleteTabMessage(), new DialogCallback()
                {
                    @Override
                    protected void onYes(Dialog dialog)
                    {
                        dialog.hide();
                        int index = tabs().indexOf(tab);
                        if (index < 0 || tabs().size() < 2)
                        {
                            return;
                        }

                        display.removeTab(index);
                        tabs().remove(index);
                        contents.get(tab).unbind();
                        contents.remove(tab);

                        index = index < tabs().size() ? index : tabs().size() - 1;
                        display.selectTab(index);
                        revealDisplay();
                        saveUI(null);
                    }
                });
    }

    protected TabProxyPresenter buildTab(final Tab tab)
    {
        Layout layout = tab.getLayout();
        layout.setWindowLayout(getContent().isHasHead());
        final SimplePanel layoutEditPanel = new SimplePanel();
        AsyncProvider<Presenter> asyncProvider = callback ->
        {
            final LayoutContentPresenter layoutPresenter = contentFactory.build(layout, getContext());
            layoutPresenter.getDisplay().setLayoutEditPanel(layoutEditPanel);
            layoutPresenters.add(layoutPresenter);
            callback.onSuccess(layoutPresenter);
            if (toolPanelPresenter != null && toolPanelPresenter.getDisplay() != null)
            {
                // Если в TabBar есть тулпанель, то режим разметки должен рисоваться под ней
                layoutPresenter.setLayoutModeOffset();
            }
        };
        TabProxyPresenter tabProxyPresenter = tabProxyPresenterFactory.create(asyncProvider,
                Functions.<Presenter> identity(), getContext().getReadyState());
        tabProxyPresenter.bind();
        contents.put(tab, tabProxyPresenter);
        if (tab.isHasToolPanel())
        {
            toolPanelPresenter = contentFactory.build(tab.getToolPanel(), getContext());
            toolPanelPresenter.bind();
            toolPanelPresenter.getDisplay()
                    .asWidget()
                    .addStyleName(WidgetResources.INSTANCE.all().toolPanelIsInsideTab());
            display.insertTab(tabProxyPresenter.getDisplay(), toolPanelPresenter.getDisplay(), createTabCaption(tab),
                    layoutEditPanel, insertingIndex());
        }
        else
        {
            display.insertTab(tabProxyPresenter.getDisplay(), createTabCaption(tab), layoutEditPanel, insertingIndex());
        }

        return tabProxyPresenter;
    }

    protected void buildTabs()
    {
        for (int i = tabs().size() - 1; i >= 0; i--)
        {
            Tab tab = tabs().get(i);
            buildTab(tab);
            getDisplay().selectTab(0);
        }
    }

    protected IsWidget createTabCaption(Tab tab)
    {
        HasText tabCaption = getDisplay().createTabCaption(tab);
        String caption = metainfoUtils.getLocalizedValue(tab.getCaption());
        if (tab.isShowObjectCount())
        {
            caption += " (0)";
        }
        tabCaption.setText(caption);
        IsWidget captionWidget = (IsWidget)tabCaption;
        UITemplateContext templateContext = context.getUITemplateContext();
        if (null != templateContext && templateContext.getReducedContents().contains(content.getUuid()))
        {
            captionWidget.asWidget().addStyleName(widgetResources.all().disabled());
        }
        return captionWidget;
    }

    protected Set<String> extractTabParam(AbstractPlace place)
    {
        String tabUUIDs = place != null ? place.getString(Constants.TAB_PARAM_KEY) : null;
        if (!StringUtilities.isEmpty(tabUUIDs))
        {
            return Sets.newLinkedHashSet(Splitter.on(Constants.TAB_DELIMITER).split(tabUUIDs));
        }
        return Sets.newLinkedHashSet();
    }

    @Override
    protected void initCommands()
    {
        initCommandDisplay(getContext(), TabContentCommandCode.MOVE_TAB_CONTENT, ContentCommandAction.MOVE);
        if (isEditable())
        {
            initCommandDisplay(getContext(), TabContentCommandCode.EDIT_CONTENT, ContentCommandAction.EDIT);
        }
        if (!(getContent().getParent() instanceof Window))
        {
            initCommandDisplay(getParentContentContext(), TabContentCommandCode.DELETE_CONTENT,
                    ContentCommandAction.DELETE);
        }

    }

    @Override
    protected boolean isEditable()
    {
        return false;
    }

    @Override
    protected void onBind()
    {
        getDisplay().setHeaderVisible(false);
        setVisibleObjectCaptionEditIcon();
        addEditObjectCardCaptionHandler();
        if (tabs().isEmpty())
        {
            Tab tab = new Tab();
            tab.setParent(getContent());
            metainfoUtils.setCaption(tab.getCaption(), cmessages.newTab());
            Layout layout = new Layout();
            layout.setFirstTab(true);
            tab.setLayout(layout);
            tabs().add(tab);
        }
        else
        {
            Tab firstTab = tabs().get(0);
            firstTab.getLayout().setFirstTab(true);
        }

        currentTab = 0;
        buildTabs();
        initDisplay();
        initToolBar();
        refreshDisplay();
        super.onBind();
        if (!getContent().isHasHead())
        {
            initEditTabsActionButton();
        }

        getDisplay().addSelectionHandler(event ->
        {
            if (isTabIndexValid(currentTab) && contents.containsKey(getCurrentTab()))
            {
                contents.get(getCurrentTab()).hideDisplay();
            }

            currentTab = event.getSelectedItem();
            if (isTabIndexValid(currentTab) && contents.containsKey(getCurrentTab()))
            {
                revealSelectedTab();
            }
            if (actBarPresenter != null)
            {
                getDisplay().drawActionBar(actBarPresenter.getDisplay());
            }

            if (getContext().getContentInLayoutEditMode() != null
                && !getContext().getContentInLayoutEditMode().equals(getCurrentTab().getLayout()))
            {
                getContext().getEventBus()
                        .fireEvent(new ChangeLayoutModeEvent(getCurrentTab().getLayout(), true));
            }
            updateOpacity();
        });

        registerHandler(getContext().getEventBus().addHandler(ContextChangedEvent.getType(),
                (ContextChangedHandler<Context>)e ->
                {
                    if (isRevealed)
                    {
                        contents.get(tabs().get(getDisplay().getSelectedTab())).refreshDisplay();
                    }
                }));

        registerHandler(getContext().getEventBus().addHandler(ChangeLayoutModeEvent.getType(),
                e ->
                {
                    if (getContext() instanceof BasicUIContext)
                    {
                        ((BasicUIContext)getContext())
                                .setContentInLayoutEditMode(e.isLayoutModeEnabled() ? e.getLayout() : null);

                        Scheduler.get().scheduleDeferred(() -> toolBar.refresh(getContent()));
                    }
                }));

        registerHandler(getContext().getEventBus().addHandler(ChangeUITemplateModeEvent.TYPE,
                event -> Scheduler.get().scheduleDeferred(() -> toolBar.refresh(getContent()))));

        getDisplay().asWidget().addAttachHandler(event ->
        {
            toolBar.refresh(getContent());
            adjustToolBarPosition();
        });

        registerHandler(com.google.gwt.user.client.Window.addResizeHandler(
                event -> Scheduler.get().scheduleDeferred(this::adjustToolBarPosition)));

        registerHandler(getContext().getEventBus().addHandler(ColumnWidthChangedEvent.getType(),
                e -> adjustToolBarPosition()));

        selectTab();

        registerHandler(getDisplay().addSelectionHandler(event ->
        {
            int selectedIndex = event.getSelectedItem();
            if (!isBound())
            {
                return;
            }
            ITab selectedTab = tabs().get(selectedIndex);
            String tabId = selectedTab.getUuid();
            AbstractPlace place = (AbstractPlace)placeController.getWhere();
            Set<String> tabs = extractTabParam(place);
            if (tabs.contains(tabId) || 0 == selectedIndex && tabs.isEmpty())
            {
                return;
            }
            AbstractPlace newPlace = place.cloneIt();
            newPlace.put(Constants.TAB_PARAM_KEY, getNewTabParamKey(tabs, tabId));
            placeController.goTo(newPlace);
        }));
        registerHandler(eventBus.addHandler(PlaceParametersChangeEvent.getType(), (event) ->
        {
            if (isBound())
            {
                selectTab();
            }
        }));
        registerHandler(context.getEventBus().addHandler(RefreshContentEvent.getType(), event ->
        {
            if (event.isSuitable(getContent()) && CollectionUtils.isNotEmpty(tabs()))
            {
                int tabIndex = currentTab;
                clearTabs();
                buildTabs();
                if (isTabIndexValid(tabIndex))
                {
                    getDisplay().selectTab(tabIndex);
                    currentTab = tabIndex;
                }
                revealDisplay();
            }
        }));

        if (!getContent().isHasHead())
        {
            getDisplay().asWidget().addStyleName(WidgetResources.INSTANCE.contentLayout().tabPanelContent());
        }
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        toolBar.unbind();
        for (Presenter presenter : contents.values())
        {
            presenter.unbind();
        }
        if (null != toolPanelPresenter)
        {
            toolPanelPresenter.unbind();
        }
    }

    @Override
    protected void updateOpacity()
    {
        if (currentTab >= tabs().size())
        {
            currentTab = 0;
        }
        if (tabs().isEmpty())
        {
            unbind();
            return;
        }
        Tab currentTab = getCurrentTab();
        if (null == currentTab)
        {
            return;
        }
        getDisplay().setOpaque(currentTab.isEnabled());
    }

    private void addEditObjectCardCaptionHandler()
    {
        FlowPanel captionPanel = getDisplay().getCaptionPanel();
        if (captionPanel.getWidgetCount() == 2)
        {
            Widget anchor = captionPanel.getWidget(1);
            if (anchor.isVisible())
            {
                anchor.addHandler(new EditObjectCaptionClickHandler(), ClickEvent.getType());
            }
        }
    }

    /**
     * Убирает названия кнопок в тулбаре в случае, если панель вкладок достаточно узкая
     */
    private void adjustToolBarPosition()
    {
        if (getContent().isHasHead())
        {
            return;
        }
        Scheduler.get().scheduleDeferred(
                () -> toolBar.changeButtonsCompactMode(getDisplay().asWidget().getOffsetWidth() > 0
                                                       && getDisplay().asWidget().getOffsetWidth() < 420));
    }

    private void clearTabs()
    {
        currentTab = -1;
        contents.values().forEach(Presenter::unbind);
        contents.clear();
        layoutPresenters.forEach(Presenter::unbind);
        layoutPresenters.clear();
        initialized = false;
        TabBarContentDisplay display = getDisplay();
        if (display != null)
        {
            try
            {
                display.clearTabPanel();
            }
            catch (Exception ex)
            {
                LOG.info("Error clean tabpanel");
            }
        }
    }

    private Tab getCurrentTab()
    {
        return tabs().get(currentTab);
    }

    private TabContentCommandParam<FlowContent, List<Tab>> getEditTabsCommandParam()
    {
        return new TabContentCommandParam<>(getContent(), editTabsCallback, getContext(),
                getContent().getParent(), this::getCurrentTab);
    }

    private String getNewTabParamKey(Set<String> curTabs, String newTabId)
    {
        UIComponent commonParent = TabHierarchyUtils.getCommonParent(getContent(), curTabs);
        TabHierarchyUtils.removeOldTabParams(curTabs, getContent());
        return StringUtilities.join(TabHierarchyUtils.getTabHierarchy(curTabs, newTabId, getContent(), commonParent),
                Constants.TAB_DELIMITER);
    }

    private void initDisplay()
    {
        display.init();
        initialized = true;
        display.selectTab(0);
        display.updateStyles(getContent().isHasHead());
    }

    //добавление кнопки редактирования списка вкладок на панели действий с контентом (вверху справа)
    private void initEditTabsActionButton()
    {
        if (null == context.getUITemplateContext())
        {
            BaseCommand<FlowContent, Void> editTabListCommand = (BaseCommand<FlowContent, Void>)commandFactory
                    .create(TabContentCommandCode.EDIT_TAB_LIST_CONTENT, getEditTabsCommandParam());
            getDisplay().addToolDisplay(editTabListCommand.getFontIcon(), ContentCommandAction.EDIT_TABS);
        }
        else
        {
            BaseCommand<FlowContent, Void> manageTabsCommand = (BaseCommand<FlowContent, Void>)commandFactory
                    .create(TabContentCommandCode.MANAGE_TEMPLATE_TABS, getEditTabsCommandParam());
            getDisplay().addToolDisplay(manageTabsCommand.getFontIcon(), ContentCommandAction.MANAGE_TABS);
        }

        if (permissionChecker.hasPermission(content, context, TabContentCommandCode.EDIT_TAB_LIST_CONTENT))
        {
            initCommandDisplay(getContext(), TabContentCommandCode.EDIT_CONTENT, ContentCommandAction.EDIT);
        }
    }

    private void initToolBar()
    {
        BasicCallback callback = new BasicCallback<Void>(getDisplay())
        {
            @Override
            protected void handleSuccess(Void value)
            {
                contents.get(getCurrentTab()).refreshDisplay();
            }
        };
        TabContentCommandParam<FlowContent, Void> param = new TabContentCommandParam(null, callback, getContext(),
                getContent(), this::getCurrentTab);

        toolBar.add((ButtonPresenter<TabBar>)buttonFactory.create(ButtonCode.ADD, messages.addContent(),
                TabContentCommandCode.ADD_CONTENT, param));
        if (getContent().isHasHead())
        {
            UITemplateSelectListPresenter selectPresenter = selectUITemplatePresenterProvider.get();
            selectPresenter.init(getContext());
            toolBar.add(selectPresenter);
            ButtonPresenter<TabBar> saveButton = (ButtonPresenter<TabBar>)buttonFactory.create(ButtonCode.SAVE_PRS,
                    StringUtilities.EMPTY, TabContentCommandCode.SAVE_UI_TEMPLATE, param);
            saveButton.setHint(templateMessages.saveTemplate());
            toolBar.add(saveButton);
            toolBar.add((ButtonPresenter<TabBar>)buttonFactory.create(ButtonCode.DELETE, cmessages.delete(),
                    TabContentCommandCode.REMOVE_UI_TEMPLATE, param));
            //добавление кнопки редактирования списка вкладок на панели инструментов (большие кнопки вверху слева)
            toolBar.add((ButtonPresenter<TabBar>)buttonFactory.create(ButtonCode.EDIT, cmessages.editTabs(),
                    TabContentCommandCode.EDIT_TAB_LIST_CONTENT, getEditTabsCommandParam()));
            toolBar.add((ButtonPresenter<TabBar>)buttonFactory.create(ButtonCode.EDIT, templateMessages.manageTabs(),
                    TabContentCommandCode.MANAGE_TEMPLATE_TABS, getEditTabsCommandParam()));
        }

        toolBar.add((ButtonPresenter<TabBar>)buttonFactory.create(ButtonCode.ENABLE_LAYOUT_MODE, cmessages.layoutMode(),
                TabContentCommandCode.ENABLE_LAYOUT_MODE, param));
        toolBar.add((ButtonPresenter<TabBar>)buttonFactory.create(ButtonCode.ENABLE_LAYOUT_MODE,
                cmessages.exitFromLayoutMode(), TabContentCommandCode.DISABLE_LAYOUT_MODE, param));

        if (getContent().isHasHead())
        {
            toolBar.add((ButtonPresenter<TabBar>)buttonFactory.create(ButtonCode.DEFAULT,
                    templateMessages.startTemplateMode(), TabContentCommandCode.START_TEMPLATE_MODE, param));
            toolBar.add((ButtonPresenter<TabBar>)buttonFactory.create(ButtonCode.DEFAULT,
                    templateMessages.stopTemplateMode(), TabContentCommandCode.STOP_TEMPLATE_MODE, param));
        }

        toolBar.bind();
    }

    //возвращает позицию, в которую должна быть помещена добавляемая вкладка
    private int insertingIndex()
    {
        return initialized ? tabs().size() - 1 : 0;
    }

    private void saveUI(@Nullable AsyncCallback<Void> callback)
    {
        metainfoModificationService.saveUI(getContext().getMetainfo().getFqn(), getContext().getRootContent(), null,
                getContext().getCode(), null, false, callback == null ? new BasicCallback<Void>() : callback);
    }

    private void selectTab()
    {
        Place place = placeController.getWhere();
        if (place instanceof AbstractPlace)
        {
            Set<String> contentTabs = extractTabParam((AbstractPlace)place);
            int index = Iterables.indexOf(tabs(), compose(in(contentTabs), UIComponent.UUID_EXTRACTOR));
            if (index >= 0 && getDisplay().getSelectedTab() != index)
            {
                getDisplay().selectTab(index);
            }
        }
    }

    @Override
    protected void refreshEditable()
    {
        super.refreshEditable();
        setVisibleObjectCaptionEditIcon();
    }

    private void setVisibleObjectCaptionEditIcon()
    {
        boolean visible = permissionChecker.hasPermission(content, context, TabContentCommandCode.EDIT_CONTENT)
                          && (commonUtils.uiNotInherit(getContext()) || null != context.getUITemplateContext());
        getDisplay().getCaptionPanel().getWidget(1).setVisible(visible);
    }

    private List<Tab> tabs()
    {
        return getContent().getTab();
    }

    private boolean isTabIndexValid(int tabIndex)
    {
        return -1 < tabIndex && tabIndex < tabs().size();
    }
}
