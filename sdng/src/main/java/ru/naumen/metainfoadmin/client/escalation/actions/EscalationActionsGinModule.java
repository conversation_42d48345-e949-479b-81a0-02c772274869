package ru.naumen.metainfoadmin.client.escalation.actions;

import static ru.naumen.core.shared.Constants.AbstractBO.FQN;

import java.util.Collection;
import java.util.HashSet;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Sets;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassSameClassOnlyTreeContext;
import ru.naumen.core.client.tree.metainfo.MetaClassMultiSelectionModelSameClassOnly;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.eventaction.Constants;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfoadmin.client.eventaction.form.SelectFqnsPropertyFactory;

/**
 * <AUTHOR>
 * @since 23.07.2012
 *
 */
public class EscalationActionsGinModule extends AbstractGinModule
{
    static class EscalationEventActionAttrsProvider implements Provider<ImmutableList<AttributeFqn>>
    {
        @Override
        public ImmutableList<AttributeFqn> get()
        {
            return ImmutableList.of(
                    Constants.EventAction.Attributes.TITLE,
                    Constants.EventAction.Attributes.LINKED_CLASSES,
                    Constants.EventAction.Attributes.DESCRIPTION,
                    Constants.EventAction.Attributes.ACTION,
                    Constants.EventAction.Attributes.JMS_QUEUE,
                    Constants.EventAction.Attributes.ON);
        }
    }

    static class EscalationEventActionTreeFactory implements SelectFqnsPropertyFactory
    {
        @Inject
        private CommonMessages cmessages;
        @Inject
        private PopupValueCellTreeFactory<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly> treeFactory;
        @Inject
        private DtoMetaClassesTreeFactory<MetaClassMultiSelectionModelSameClassOnly,
                DtoMetaClassSameClassOnlyTreeContext> treeModelHelper;

        @Override
        public Property<Collection<DtObject>> create(@Nullable EventAction eventAction)
        {
            return new PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                    MetaClassMultiSelectionModelSameClassOnly>>(
                    cmessages.objects(), treeFactory.create(treeModelHelper.createMetaClassTreeViewModel(Container
                    .create(new DtoMetaClassSameClassOnlyTreeContext(FQN, MetaClassFilters.isNotSystem())))));
        }
    }

    static class EscalationPermittedTypesProvider implements Provider<HashSet<EventType>>
    {
        @Override
        public HashSet<EventType> get()
        {
            return Sets.newHashSet(EventType.escalation);
        }
    }

    static class EscalationProhibitedTypesProvider implements Provider<HashSet<EventType>>
    {
        @Override
        public HashSet<EventType> get()
        {
            return new HashSet<>(); // NOPMD
        }
    }

    public static final String ESCALATION_EVENT_ACTION_TREE = "escalationEventActionTree";
    public static final String ESCALATION_EVENT_ACTION_ATTRS = "escalationEventActionAttrs";
    public static final String ESCALATION_EVENT_ACTION_PERMITTED = "escalationEventActionPermitted";
    public static final String ESCALATION_EVENT_ACTION_PROHIBITED = "escalationEventActionProhibited";
    public static final String ADD_ESCALATION_EVENT_ACTION = ButtonCode.ADD_ESCALATION_EVENT_ACTION;
    public static final String EDIT_ESCALATION_EVENT_ACTION = "editEscalationEventAction";

    @Override
    protected void configure()
    {
        bind(EscalationActionsPresenter.class);
        bind(AddEscalationEventActionFormPresenter.class);
        bind(EscalationEventActionCommandFactoryInitializer.class).asEagerSingleton();
        bind(EscalationActionsMessages.class).in(Singleton.class);
        //@formatter:off
        bind(new TypeLiteral<SelectFqnsPropertyFactory>(){})
            .annotatedWith(Names.named(ESCALATION_EVENT_ACTION_TREE))
            .to(EscalationEventActionTreeFactory.class);
        bind(new TypeLiteral<HashSet<EventType>>(){})
            .annotatedWith(Names.named(ESCALATION_EVENT_ACTION_PERMITTED))
            .toProvider(EscalationPermittedTypesProvider.class);
        bind(new TypeLiteral<HashSet<EventType>>(){})
            .annotatedWith(Names.named(ESCALATION_EVENT_ACTION_PROHIBITED))
            .toProvider(EscalationProhibitedTypesProvider.class);
        bind(new TypeLiteral<ImmutableList<AttributeFqn>>(){})
            .annotatedWith(Names.named(ESCALATION_EVENT_ACTION_ATTRS))
            .toProvider(EscalationEventActionAttrsProvider.class);
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, new TypeLiteral<AddEscalationEventActionCommand>(){})
            .build(new TypeLiteral<CommandProvider<AddEscalationEventActionCommand, CommandParam<EventActionWithScript, EventActionWithScript>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, new TypeLiteral<EditEscalationEventActionCommand>(){})
            .build(new TypeLiteral<CommandProvider<EditEscalationEventActionCommand, CommandParam<EventActionWithScript, EventActionWithScript>>>() {}));
        //@formatter:on
    }
}