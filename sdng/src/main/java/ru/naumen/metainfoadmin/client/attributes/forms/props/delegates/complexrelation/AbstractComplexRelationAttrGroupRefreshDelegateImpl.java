package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;

import jakarta.inject.Inject;

import java.util.HashMap;

import com.google.common.collect.Maps;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.AbstractSingleSelectProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.AttributeGroupInfo;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosAction;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosResponse;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

/**
 * Абстрактная реализация {@link AttributeFormPropertyDelegateRefresh} для групп
 * атрибутов на сложных формах добавления связи
 *
 * <AUTHOR>
 * @since 16 февр. 2016 г.
 */
public abstract class AbstractComplexRelationAttrGroupRefreshDelegateImpl<F extends ObjectForm,
        P extends AbstractSingleSelectProperty<String>> implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, P>
{
    @Inject
    private DispatchAsync dispatch;

    private final String propertyCode;

    public AbstractComplexRelationAttrGroupRefreshDelegateImpl(String propertyCode)
    {
        this.propertyCode = propertyCode;
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, P property,
            AsyncCallback<Boolean> callback)
    {
        if (!show(context))
        {
            callback.onSuccess(false);
            return;
        }
        List<ClassFqn> fqns = getFqns(context);
        fillAttrGroups(context, property, fqns, callback);
    }

    private void fillAttrGroups(final PropertyContainerContext context, final P property,
            final Collection<ClassFqn> fqns, final AsyncCallback<Boolean> callback)
    {
        GetAttributeGroupInfosAction action = new GetAttributeGroupInfosAction(fqns);
        dispatch.execute(action, new BasicCallback<GetAttributeGroupInfosResponse>()
        {
            @Override
            protected void handleSuccess(GetAttributeGroupInfosResponse response)
            {
                fillAttrGroupList(property, getVisibleAttrGroups(response.getGroupInfos(), fqns));

                String complexAttrGroup = context.getPropertyValues().getProperty(propertyCode);
                property.trySetObjValue(complexAttrGroup);

                callback.onSuccess(true);
            }
        });
    }

    abstract protected List<ClassFqn> getFqns(PropertyContainerContext context);

    abstract protected boolean show(PropertyContainerContext context);

    private void fillAttrGroupList(P property, Map<String, String> visibleGroups)
    {
        TreeMap<String, String> treeMap = Maps.newTreeMap();
        treeMap.putAll(visibleGroups);

        SingleSelectCellList<String> selList = property.getValueWidget();
        selList.clear();

        for (Entry<String, String> entry : treeMap.entrySet())
        {
            selList.addItem(entry.getValue(), entry.getKey());
        }
    }

    private static Map<String, String> getVisibleAttrGroups(
            Map<ClassFqn, ? extends List<AttributeGroupInfo>> attrGroups,
            Collection<ClassFqn> fqns)
    {
        Map<String, String> visibleGroups = new HashMap<>();
        Map<String, Integer> visibleGroupsCounter = new HashMap<>();
        for (ClassFqn fqn : fqns)
        {
            List<AttributeGroupInfo> groups = attrGroups.get(fqn);
            if (null == groups)
            {
                continue;
            }
            for (AttributeGroupInfo groupInfo : groups)
            {
                visibleGroups.put(groupInfo.getCode(), groupInfo.getTitle());
                int counter = 1;
                if (visibleGroupsCounter.containsKey(groupInfo.getCode()))
                {
                    counter = visibleGroupsCounter.get(groupInfo.getCode()) + 1;
                }
                visibleGroupsCounter.put(groupInfo.getCode(), counter);
            }
        }
        for (Entry<String, Integer> entry : visibleGroupsCounter.entrySet())
        {
            if (entry.getValue() != fqns.size())
            {
                visibleGroups.remove(entry.getKey());
            }
        }
        return visibleGroups;
    }
}
