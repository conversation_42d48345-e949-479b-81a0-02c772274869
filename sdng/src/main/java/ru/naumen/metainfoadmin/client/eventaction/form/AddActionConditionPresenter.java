package ru.naumen.metainfoadmin.client.eventaction.form;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.collect.Lists;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.WorkflowModificationService;
import ru.naumen.metainfo.client.eventaction.EventActionConstants;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.eventaction.ActionCondition;
import ru.naumen.metainfo.shared.eventaction.ActionConditionType;
import ru.naumen.metainfo.shared.eventaction.ActionConditionWithScript;
import ru.naumen.metainfoadmin.client.eventaction.ActionConditionCreator;
import ru.naumen.metainfoadmin.client.eventaction.ActionConditionsCreatorFactory;

/**
 * {@link Presenter} диалога добавления {@link ActionCondition}
 *
 * <AUTHOR>
 *
 */
public class AddActionConditionPresenter extends OkCancelPresenter<PropertyDialogDisplay> implements
        CallbackPresenter<ActionConditionWithScript, ActionConditionWithScript>
{

    @Inject
    EventActionMessages messages;
    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    @Inject
    SelectListProperty<String, SelectItem> type;
    @Inject
    WorkflowModificationService workflowModificationService;
    @Inject
    Processor validation;
    @Inject
    private NotNullValidator<SelectItem> notNullValidator;
    @Inject
    ActionConditionsCreatorFactory factory;
    @Inject
    EventActionConstants eventActionConstants;

    AsyncCallback<ActionConditionWithScript> saveCallback;
    ActionConditionCreator creator;

    @Inject
    public AddActionConditionPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(ActionConditionWithScript value, AsyncCallback<ActionConditionWithScript> callback)
    {
        this.saveCallback = callback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        IProperties properties = creator.getConditionProperties();
        if (null == properties)
        {
            return;
        }
        saveCallback.onSuccess(creator.getCondition());
    }

    protected void initConditionCreator(String code)
    {
        if (null != creator)
        {
            creator.removeProperties();
        }
        if (null == code)
        {
            creator = null;
        }
        else
        {
            creator = factory.create(ActionConditionType.valueOf(code), null);
            creator.bindProperties();
            creator.addProperties(getDisplay());
        }
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.addingCondition());
        initTypeProperty(Lists.newArrayList(ActionConditionType.SCRIPT.name()));
        getDisplay().display();
    }

    private void initTypeProperty(List<String> types)
    {
        type.setCaption(messages.conditionType());
        type.setValidationMarker(true);
        SingleSelectCellList<String> typeWidget = type.getValueWidget();
        for (String conditionType : types)
        {
            typeWidget.addItem(eventActionConstants.actionConditionsTypes().get(conditionType), conditionType);
        }
        registerHandler(type.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                initConditionCreator(SelectListPropertyValueExtractor.getValue(type));
            }
        }));
        validation.validate(type, notNullValidator);
        if (types.size() == 1)
        {
            type.trySetObjValue(types.get(0), true);
        }
        else
        {
            getDisplay().add(type);
        }
    }
}
