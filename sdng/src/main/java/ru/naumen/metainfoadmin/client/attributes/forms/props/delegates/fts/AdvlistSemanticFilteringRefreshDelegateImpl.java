package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.fts;

import static ru.naumen.metainfo.shared.Constants.SEMANTIC_FILTRATION_NOT_ALLOWED_ATTRS;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.ATTRIBUTE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.HAS_ADVLIST_SEMANTIC_FILTERING;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ADVLIST_SEMANTIC_FILTERING;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.INHERIT;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

/**
 * Делегат обновления свойства "Фильтрация с учётом морфологии"
 *
 * <AUTHOR>
 * @since 13 сентября 2019 г.
 */
public class AdvlistSemanticFilteringRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{

    private static boolean isAttributeDeclaredInMetaClass(@Nullable Attribute attribute, @Nullable MetaClass metaClass)
    {
        return (metaClass != null && attribute != null && metaClass.getFqn().equals(attribute.getDeclaredMetaClass()));
    }

    private static boolean isClass(@Nullable MetaClass metaClass)
    {
        return metaClass != null && metaClass.getFqn().isClass();
    }

    private static boolean isNewAttribute(@Nullable Attribute attribute)
    {
        return attribute == null;
    }

    private static boolean isSemanticFiltrationAllowed(@Nullable Attribute attribute)
    {
        return attribute == null || !SEMANTIC_FILTRATION_NOT_ALLOWED_ATTRS.contains(attribute.getFqn());
    }

    private static Boolean needAdvlistSemanticFilteringPropertyEnabled(@Nullable Attribute attribute,
            @Nullable MetaClass metaClass)
    {
        return isNewAttribute(attribute)
               || isClass(metaClass)
               || isAttributeDeclaredInMetaClass(attribute, metaClass);
    }

    private final SharedSettingsClientService settingsClientService;

    @Inject
    public AdvlistSemanticFilteringRefreshDelegateImpl(SharedSettingsClientService settingsClientService)
    {
        this.settingsClientService = settingsClientService;
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {

        Boolean hasAdvlistSemanticFiltering = context.getContextValues().getProperty(HAS_ADVLIST_SEMANTIC_FILTERING,
                Boolean.FALSE);
        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        Attribute attribute = context.getContextValues().getProperty(ATTRIBUTE);
        Boolean computable = context.getPropertyValues().getProperty(COMPUTABLE);
        if (hasAdvlistSemanticFiltering && CommonUtils.isNotSystemClass(metaClass) && settingsClientService
                .isDatabaseFTSEnabled() && !computable && isSemanticFiltrationAllowed(attribute))
        {
            Boolean inherited = context.getPropertyValues().getProperty(INHERIT, Boolean.FALSE);
            Boolean enabled = !inherited && needAdvlistSemanticFilteringPropertyEnabled(attribute, metaClass);
            context.setPropertyEnabled(ADVLIST_SEMANTIC_FILTERING, enabled);
            callback.onSuccess(Boolean.TRUE);
        }
        else
        {
            callback.onSuccess(Boolean.FALSE);
        }
    }
}
