package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hasgroupseparators;

import static ru.naumen.metainfo.shared.Constants.NUMBER_ATTRIBUTE_TYPES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import jakarta.inject.Inject;

/**
 * Делегат для обновления чекбокса "разделять на разряды"
 * @param <F>
 */
public class HasGroupSeparatorRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String attrType = context.getPropertyValues().getProperty(ATTR_TYPE);
        boolean suitableAttrType = NUMBER_ATTRIBUTE_TYPES.contains(attrType);

        if (Constants.AttributeOfRelatedObjectSettings.CODE.equalsIgnoreCase(attrType))
        {
            String relatedObjectMetaClass = context.getPropertyValues()
                    .getProperty(AttributeFormPropertyCode.RELATED_OBJECT_METACLASS);

            String attrFqn =
                    context.getPropertyValues().getProperty(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE);
            if (relatedObjectMetaClass == null || attrFqn == null)
            {
                callback.onSuccess(false);
                return;
            }

            String attrCode = AttributeFqn.parse(attrFqn).getCode();
            metainfoService.getMetaClass(AttributeFqn.parse(attrFqn).getClassFqn(), new BasicCallback<MetaClass>()
            {
                @Override
                protected void handleSuccess(MetaClass metaClass)
                {
                    context.getPropertyValues().setProperty(AttributeFormPropertyCode.HAS_GROUP_SEPARATORS, property
                            .getValue());
                    if (metaClass.hasAttribute(attrCode) &&
                        NUMBER_ATTRIBUTE_TYPES.contains(metaClass.getAttribute(attrCode).getType().getCode()))
                    {
                        callback.onSuccess(true);
                    }
                    else
                    {
                        callback.onSuccess(false);
                    }
                }
            });
        }
        else
        {
            context.getPropertyValues().setProperty(AttributeFormPropertyCode.HAS_GROUP_SEPARATORS, property
                    .getValue());
            callback.onSuccess(suitableAttrType);
        }
    }
}
