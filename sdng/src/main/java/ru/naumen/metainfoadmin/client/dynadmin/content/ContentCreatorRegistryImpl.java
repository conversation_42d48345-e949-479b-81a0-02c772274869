package ru.naumen.metainfoadmin.client.dynadmin.content;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates.UIContextPredicate;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Реализация {@link ContentCreatorRegistry}
 *
 * <AUTHOR>
 *
 */
@Singleton
public class ContentCreatorRegistryImpl implements ContentCreatorRegistry
{
    private class ContentCreatorFactoryImpl implements ContentCreatorFactory
    {
        private final String code;

        public ContentCreatorFactoryImpl(String code)
        {
            this.code = code;
        }

        @Override
        public ContentCreator<?> create()
        {
            return getCreator(code);
        }

        @Override
        public UIContextPredicate getContextPredicate()
        {
            return predicates.get(code);
        }

        @Override
        public String getTitle()
        {
            return contentTitles.content(code);
        }
    }

    @Inject
    private ContentTitles contentTitles;
    @Inject
    private Map<String, UIContextPredicate> predicates;
    @Inject
    @SuppressWarnings("rawtypes")
    private Map<String, ContentCreatorProvider> providers;

    @Override
    @SuppressWarnings("rawtypes")
    public ContentCreator<?> getCreator(final String name)
    {
        return (ContentCreator)providers.get(name).getProvider().get();
    }

    @Override
    public void getCreators(UIContext context,
            final AsyncCallback<ArrayList<Entry<String, ContentCreatorFactory>>> callback)
    {
        final List<String> codes = new ArrayList<>();
        final ReadyState readyState = new ReadyState(this);
        for (final Entry<String, UIContextPredicate> entry : predicates.entrySet())
        {
            entry.getValue().isApplicable(context, new BasicCallback<Boolean>(readyState)
            {
                @Override
                public void handleFailure(Throwable arg0)
                {
                    callback.onFailure(arg0);
                }

                @Override
                public void handleSuccess(Boolean arg0)
                {
                    if (Boolean.TRUE.equals(arg0))
                    {
                        codes.add(entry.getKey());
                    }
                }
            });
        }
        readyState.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                callback.onSuccess(toFactories(codes));
            }
        });
    }

    private ArrayList<Entry<String, ContentCreatorFactory>> toFactories(final List<String> codes)
    {
        return Lists.newArrayList(Lists.transform(codes, new Function<String, Entry<String, ContentCreatorFactory>>()
        {
            @Override
            @Nullable
            public Entry<String, ContentCreatorFactory> apply(@Nonnull String input)
            {
                return Maps.<String, ContentCreatorFactory> immutableEntry(input, new ContentCreatorFactoryImpl(input));
            }
        }));
    }
}
