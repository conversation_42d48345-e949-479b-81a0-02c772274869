package ru.naumen.metainfoadmin.client.attributes.attrusage;

import jakarta.inject.Inject;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.inject.Singleton;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfoadmin.client.wf.profile.WfProfilePlace;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInWfProfiles;

/**
 * Представление для отображения значения места использования "Профили связанных ЖЦ" на форме "Используется в
 * настройках" в таблице атрибутов
 * <AUTHOR>
 * @since 3 Jul 18
 */
@Singleton
public class AttributeUsageInWfProfilesHtmlFactoryImpl extends AttributeHtmlFactoryImpl<AttributeUsageInWfProfiles>
{
    @Inject
    private Formatters formatters;
    @Inject
    private CommonMessages messages;
    @Inject
    private PlaceHistoryMapper historyMapper;

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInWfProfiles usage)
    {
        return new SafeHtmlBuilder().append(formatters.formatHyperlinkAsHtml(createLinkToWfProfileCard(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLinkToWfProfileCard(AttributeUsageInWfProfiles usage)
    {
        WfProfilePlace wfProfilePlace = new WfProfilePlace(usage.getCode());
        //@formatter:off
        return new Hyperlink(
                messages.wfProfile() + " \"" + usage.getTitle() + "\"", 
                StringUtilities.getHrefByToken(historyMapper.getToken(wfProfilePlace)));
        //@formatter:on
    }
}
