package ru.naumen.metainfoadmin.client.attributes.columns;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import ru.naumen.core.client.widgets.grouplist.TableRowComparator;

/**
 * Описывает механизм сортировки уже отрисованной таблицы по содержимому ячеек колонки.
 * Сортировка строк производится без повторного запроса содержимого таблицы на сервера, на
 * основании уже имеющегося контента таблицы.
 * Элемент управления сортировкой (куда кликать для сортировки) определяется свойством sortingControl
 * и может быть либо непосредственно заголовком колонки, либо элементом в выпадающем списке при 
 * клике на заголовок колонки. 
 *
 * <AUTHOR>
 * @since 28 сент. 2018 г.
 *
 */
public class ColumnSorter<T extends Comparable<T>>
{
    private TableRowComparator<T> rowComparator;

    /**
     * Уникальный идентификатор сортировщика в рамках всего списка.
     */
    private String id;

    /**
     * Текст в выпадающем меню, при клике на который будет осуществляться сортировка.
     * null - сортировка осуществляется при клике непосредственно на заголовок. 
     */
    @Nullable
    private String sortingControlText;

    /**
     * Элемент, отображающий состояние сортировки в интерфейсе.
     */
    @Nullable
    private ColumnSorterCellModel sorterCellModel;

    /**
     * Активен ли сортировщик в данный момент
     */
    private boolean isActive;

    /**
     * Разделяет ли одну ячейку заголовка с другими сортировщиками?
     */
    private boolean isComplex;

    public ColumnSorter(@Nonnull String id, TableRowComparator<T> rowComparator, String sortingControl)
    {
        this.id = id;
        this.rowComparator = rowComparator;
        this.sortingControlText = sortingControl;
    }

    public TableRowComparator<T> getRowComparator()
    {
        return rowComparator;
    }

    public void setRowComparator(TableRowComparator<T> rowComparator)
    {
        this.rowComparator = rowComparator;
    }

    public String getSortingControlText()
    {
        return sortingControlText;
    }

    public void setSortingControlText(String sortingControl)
    {
        this.sortingControlText = sortingControl;
    }

    public void setColumnSorterCellModel(ColumnSorterCellModel sorterCellModel)
    {
        this.sorterCellModel = sorterCellModel;
    }

    public void updateSortingElement()
    {
        if (sorterCellModel != null)
        {
            sorterCellModel.updateHeaderCell(isActive(), getRowComparator().isDirect(), isComplex(),
                    getSortingControlText());
        }
    }

    public boolean isActive()
    {
        return isActive;
    }

    public void setActive(boolean isActive)
    {
        this.isActive = isActive;
    }

    public String getId()
    {
        return id;
    }

    @Override
    public boolean equals(Object other)
    {
        return other instanceof ColumnSorter && this.getId().equals(((ColumnSorter<?>)other).getId());
    }

    @Override
    public int hashCode()
    {
        return getId().hashCode();
    }

    public boolean isComplex()
    {
        return isComplex;
    }

    public void setComplex(boolean isComplex)
    {
        this.isComplex = isComplex;
    }
}
