package ru.naumen.metainfoadmin.client.embeddedapplications.usagelist.form;

import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsagePointApplication.CUSTOM;
import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsagePointApplication.FORM_TYPE;
import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsagePointApplication.TRANSITIONS;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.metainfo.shared.embeddedapplication.usage.UsagePointApplication;

/**
 * Форма редактирования места использования встроенного приложения
 * <AUTHOR>
 * @since 21.10.2021
 */
public class EditUsagePointApplicationFormPresenter extends UsagePointApplicationFormPresenterBase
{
    @Inject
    public EditUsagePointApplicationFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(embeddedApplicationMessages.editingUsagePlace());
        code.setDisable();

        updateFormTypeProperty(applicationUsagePoint.getProperty(FORM_TYPE),
                applicationUsagePoint.getProperty(TRANSITIONS), applicationUsagePoint.getProperty(CUSTOM));
        getDisplay().display();
    }

    @Override
    protected boolean isNewUsage()
    {
        return false;
    }

    @Override
    protected void addUsagePoint(UsagePointApplication usage)
    {
        embeddedApplication.getUsagePoints().removeIf(u -> u.getCode().equals(usage.getCode()));
        super.addUsagePoint(usage);
    }
}
