package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import java.util.List;

import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Виджет для отображения в колонках таблицы атрибутов.
 *
 * <AUTHOR>
 * @since 31 июл. 2018 г.
 *
 */
public interface AttrTypeColumnWidget
{
    IsWidget createWidget(Attribute attr);

    List<String> listAllowedAttrTypes();
}
