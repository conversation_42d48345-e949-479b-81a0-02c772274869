package ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem;

import java.util.ArrayList;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.properties.container.AttributePropertyDescription;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelAreaSettingsDTO;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;

/**
 * Презентер списка атрибутов области панели быстрого доступа
 * <AUTHOR>
 * @since 21.09.2020
 */
public class QuickAccessTileAreaAttributesPresenter extends BasicPresenter<InfoDisplay>
{
    private final ArrayList<AttributePropertyDescription<?, QuickAccessPanelAreaSettingsDTO>> properties;
    private final NavigationSettingsMessages messages;
    private QuickAccessPanelAreaSettingsDTO areaSettingsDTO;

    @Inject
    public QuickAccessTileAreaAttributesPresenter(InfoDisplay display, EventBus eventBus,
            @Named(NavigationMenuItemGinModule.QUICK_ACCESS_PANEL_AREA_ITEM_ATTRIBUTES)
            ArrayList<AttributePropertyDescription<?, QuickAccessPanelAreaSettingsDTO>> properties,
            NavigationSettingsMessages messages)
    {
        super(display, eventBus);
        this.properties = properties;
        this.messages = messages;
    }

    public void init(QuickAccessPanelAreaSettingsDTO areaSettingsDTO)
    {
        this.areaSettingsDTO = areaSettingsDTO;
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(messages.menuElementAttributes());

        for (AttributePropertyDescription<?, QuickAccessPanelAreaSettingsDTO> propDesc : properties)
        {
            getDisplay().add(propDesc.getProperty(), propDesc.getDebugId());
        }
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        for (AttributePropertyDescription<?, QuickAccessPanelAreaSettingsDTO> propDesc : properties)
        {
            propDesc.setValue(areaSettingsDTO);
        }
    }
}