package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.defaultsettings;

import static ru.naumen.core.client.jsinterop.JQuery.$;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.DATA_ATTR_FQN;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.GRID_PREFIX;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.KENDO_GRID;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.K_CUSTOM_COLUMN_WIDTH;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import jakarta.inject.Inject;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.shared.EventBus;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.jsinterop.JQuery;
import ru.naumen.core.client.jsinterop.JQueryElement;
import ru.naumen.core.client.jsinterop.kendo.GridColumn;
import ru.naumen.core.client.jsinterop.kendo.GridOptions;
import ru.naumen.core.client.jsinterop.kendo.data.GridData;
import ru.naumen.core.client.kendo.KendoInitializer;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.AdvlistColumn;
import ru.naumen.metainfo.shared.ui.HierarchyGridDefaultViewSettings;
import ru.naumen.metainfoadmin.shared.dynadmin.HierarchyItemSettingsContext;
import ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants;
import ru.naumen.objectlist.client.extended.advlist.kendo.IGridOptionsCreatedCallback;
import ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridDisplay;
import ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridHeaderHelper;
import ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridUtils;
import ru.naumen.objectlist.client.mode.active.extended.advlist.filter.or.ListFilterOrElementBlackList;

/**
 * Презентер кендо-грида для настройки ширины колонок по умолчанию
 *
 * <AUTHOR>
 * @since 12.04.2021
 */
public class KendoGridWidthColumnsDefaultPresenter extends BasicPresenter<KendoGridDisplay>
{
    private static final String KENDO_WIDTH_COLUMNS_DEFAULT_GRID = "withColumnsDefaultGridCode";
    private static final String WIDTH = "width";

    private final ListFilterOrElementBlackList blackList;
    private final KendoInitializer kendoInitializer;
    private HierarchyItemSettingsContext context;
    private HierarchyGridDefaultViewSettings settings;

    @Inject
    public KendoGridWidthColumnsDefaultPresenter(
            KendoGridDisplay display,
            EventBus eventBus,
            ListFilterOrElementBlackList blackList,
            KendoInitializer kendoInitializer)
    {
        super(display, eventBus);
        this.blackList = blackList;
        this.kendoInitializer = kendoInitializer;
    }

    public void init(HierarchyGridDefaultViewSettings settings, HierarchyItemSettingsContext context)
    {
        this.context = context;
        this.settings = settings;
    }

    @Override
    protected void onBind()
    {
        kendoInitializer.onComplete(this::createKendoTableHeader);
    }

    private void createKendoTableHeader()
    {
        KendoGridUtils.setBaseKendoOptions();
        display.addGrid(KENDO_WIDTH_COLUMNS_DEFAULT_GRID);
        Element element = display.getElementForGrid(KENDO_WIDTH_COLUMNS_DEFAULT_GRID).getElement();
        createGrid((options, context) -> $(element).kendoGrid(options));
        KendoGridHeaderHelper.addFilterSignAndHeaderAdjust(getWithColumnDefaultGrid());
    }

    private GridData getWithColumnDefaultGrid()
    {
        JQueryElement rootGridElement = $(getDisplay().asWidget().getElement());
        return rootGridElement.find(GRID_PREFIX + JQuery.escapeSelector(KENDO_WIDTH_COLUMNS_DEFAULT_GRID) + "_0")
                .data(KENDO_GRID);
    }

    private void createGrid(IGridOptionsCreatedCallback callback)
    {
        GridOptions gridOptions = new GridOptions();
        setColumnsToGridOptions(gridOptions);
        setColumnResizeHandlerToGridOptions(gridOptions);
        gridOptions.setDataBound(e ->
        {
            KendoGridHeaderHelper.customizeKendoResizableTableHeader(e);
            applyTableWidth();
            setMinimumColumnsWidthIfAutoWidthLessThanMinAfterBinding();
            markColumnsFromSettingsAsCustomWidth();
        });
        callback.onReady(gridOptions, null);
    }

    private void setColumnsToGridOptions(GridOptions gridOptions)
    {
        if (settings.isInherit())
        {
            setColumnsToOptionsFromAttrGroup(gridOptions);
        }
        else
        {
            setColumnsToOptionsFromSettings(gridOptions);
            saveTableWithFromAsColumnsWith(gridOptions);
        }
    }

    private void setColumnResizeHandlerToGridOptions(GridOptions gridOptions)
    {
        gridOptions.setColumnResizeHandler(e ->
        {
            String attributeFqn = e.getColumn().getAttributes().get(DATA_ATTR_FQN);
            int newColumnWith = (int)Math.round(e.getNewWidth());
            Optional<AdvlistColumn> advListColumn = settings.getColumnList()
                    .stream()
                    .filter(col -> Objects.equals(col.getAttrCode(), attributeFqn))
                    .findFirst();
            if (advListColumn.isPresent())
            {
                advListColumn.get().setWidthColumn(newColumnWith);
            }
            else
            {
                Attribute attr = context.getAttributesInGroup().get(attributeFqn);
                settings.getColumnList()
                        .add(new AdvlistColumn(attributeFqn, attr.getViewPresentation().getCode(), newColumnWith));
            }
            markColumnAsCustomWidth(e.getColumn());
            saveTableWithFromCssToSettings();
        });
    }

    private void markColumnsFromSettingsAsCustomWidth()
    {
        Arrays.stream(getWithColumnDefaultGrid().columns()).forEach(column ->
        {
            String attributeFqn = column.getAttributes().get(DATA_ATTR_FQN);
            settings.getColumnList()
                    .stream()
                    .filter(c -> c.getWidthColumn() > 0 && Objects.equals(c.getAttrCode(), attributeFqn))
                    .findFirst()
                    .ifPresent(c -> markColumnAsCustomWidth(column));
        });
    }

    private void markColumnAsCustomWidth(GridColumn column)
    {
        String field = column.getField();
        GridData gridData = getWithColumnDefaultGrid();
        JQueryElement columnHeader = gridData.thead().find("th[data-field=\"" + field + "\"]");
        if (!columnHeader.hasClass(K_CUSTOM_COLUMN_WIDTH))
        {
            columnHeader.addClass(K_CUSTOM_COLUMN_WIDTH);
        }
    }

    private void setColumnsToOptionsFromAttrGroup(GridOptions options)
    {
        List<AdvlistColumn> columnList = settings.getColumnList();
        options.setColumns(context.getAttributesInGroup().values().stream().map(attr ->
        {
            GridColumn column = KendoGridUtils.createColumn(attr, new ArrayList<>(), attr.getTitle());
            String attributeFqn = column.getAttributes().get(DATA_ATTR_FQN);
            Optional<AdvlistColumn> advlistColumn = columnList
                    .stream()
                    .filter(c -> Objects.equals(c.getAttrCode(), attributeFqn))
                    .findFirst();
            advlistColumn.ifPresent(value -> column.setWidth(value.getWidthColumn()));
            column.setFilterable(KendoGridUtils.isAttributeFilterable(attr, blackList));
            return column;
        }).toArray(GridColumn[]::new));
    }

    private void setColumnsToOptionsFromSettings(GridOptions options)
    {
        int columnAutoWidth = getColumnAutoWidthFromSettings();
        Map<String, Attribute> attributesInGroup = context.getAttributesInGroup();
        options.setColumns(settings.getColumnList()
                .stream()
                .filter(col -> attributesInGroup.containsKey(col.getAttrCode()))
                .map(advlistColumn ->
                {
                    Attribute attribute = attributesInGroup.get(advlistColumn.getAttrCode());
                    GridColumn column = KendoGridUtils.createColumn(attribute, new ArrayList<>(), attribute.getTitle());
                    if (advlistColumn.getWidthColumn() > 0)
                    {
                        column.setWidth(advlistColumn.getWidthColumn());
                    }
                    else
                    {
                        column.setWidth(columnAutoWidth);
                    }
                    column.setFilterable(KendoGridUtils.isAttributeFilterable(attribute, blackList));
                    return column;
                })
                .toArray(GridColumn[]::new));
    }

    /**
     * Возвращает расчитанную ширину для колонок, ширина которых не определена в настройках и должна быть расчитана
     * автоматически исходя из общей ширины таблицы
     */
    private int getColumnAutoWidthFromSettings()
    {
        int autoWidthColumnsCount =
                (int)settings.getColumnList().stream().filter(column -> column.getWidthColumn() == 0).count();
        autoWidthColumnsCount = autoWidthColumnsCount == 0 ? 1 : autoWidthColumnsCount;
        int overrideColumnsWidth = getOverrideColumnsWidth();
        return (settings.getTableWidth() - overrideColumnsWidth) / autoWidthColumnsCount;
    }

    private int getOverrideColumnsWidth()
    {
        return settings.getColumnList()
                .stream()
                .map(AdvlistColumn::getWidthColumn)
                .reduce(Integer::sum).orElse(0);
    }

    /**
     * Сохраняет в настройки вычисленную ширину таблицы как сумму всех колонок
     */
    private void saveTableWithFromAsColumnsWith(GridOptions options)
    {
        Arrays.stream(options.getColumns())
                .map(column -> column.getWidth() == null ? 0 : column.getWidth())
                .reduce(Integer::sum)
                .ifPresent(settings::setTableWidth);
    }

    private void applyTableWidth()
    {
        int tableWidth = settings.getTableWidth();
        if (tableWidth > 0)
        {
            KendoGridUtils.setHeaderTableWidth(getWithColumnDefaultGrid(), tableWidth);
            KendoGridUtils.setBodyTableWidth(getWithColumnDefaultGrid(), tableWidth);
        }
    }

    private void setMinimumColumnsWidthIfAutoWidthLessThanMinAfterBinding()
    {
        Scheduler.get().scheduleFixedDelay(() ->
        {
            if (!isBinding())
            {
                KendoGridUtils.setMinimumColumnsWidthIfAutoWidthLessThanMin(getWithColumnDefaultGrid(), true);
                return false;
            }
            return true;
        }, 50);
    }

    @Override
    public void refreshDisplay()
    {
        display.removeGrid(KENDO_WIDTH_COLUMNS_DEFAULT_GRID);
        createKendoTableHeader();
    }

    private void saveTableWithFromCssToSettings()
    {
        settings.setTableWidth(getTableWith());
    }

    private int getTableWith()
    {
        String width = getWithColumnDefaultGrid().thead()
                .closest(HierarchyGridConstants.DOT_K_GRID)
                .find(HierarchyGridConstants.TABLE)
                .first()
                .css(WIDTH)
                .replace(Unit.PX.getType(), StringUtilities.EMPTY);
        double dWith = Double.parseDouble(width);
        return (int)Math.round(dWith);
    }
}
