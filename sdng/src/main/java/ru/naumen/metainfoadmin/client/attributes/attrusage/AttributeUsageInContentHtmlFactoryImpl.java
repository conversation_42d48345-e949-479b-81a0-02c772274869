package ru.naumen.metainfoadmin.client.attributes.attrusage;

import static java.util.stream.Collectors.joining;

import java.util.stream.Stream;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfoadmin.client.ClassPresenterMessages;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.AttributeUsageInContent;

/**
 * Представление для отображения значения места использования "Контенты" на форме "Используется в настройках" в
 * таблице атрибутов
 * <AUTHOR>
 * @since 3 Jul 18
 */
@Singleton
public class AttributeUsageInContentHtmlFactoryImpl extends AttributeHtmlFactoryImpl<AttributeUsageInContent>
{
    @Inject
    private Formatters formatters;
    @Inject
    private CommonMessages messages;
    @Inject
    private PlaceHistoryMapper historyMapper;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private ClassPresenterMessages clsMessages;

    @Override
    public SafeHtml create(PresentationContext context, AttributeUsageInContent usage)
    {
        return new SafeHtmlBuilder().append(formatters.formatHyperlinkAsHtml(createLinkToContent(usage))).toSafeHtml();
    }

    private Hyperlink createLinkToContent(AttributeUsageInContent usage)
    {
        ClassFqn declaredMetaClass = usage.getTypes().isEmpty() ? usage.getClazz().getFqn() : usage.getTypes().get(0);

        //@formatter:off
        MetaClassPlace mcPlace = new MetaClassPlace(declaredMetaClass, "Class." + getClassTabId(usage
                .getFormId()));
        String tabParam = usage.getTabChain()
                .stream()
                .map(Tab::getUuid)
                .collect(joining(Constants.TAB_DELIMITER));
        //@formatter:on
        mcPlace.put(Constants.TAB_PARAM_KEY, tabParam);
        mcPlace.put(Constants.HIGHLIGHT_KEY, usage.getContent().getUuid());

        //@formatter:off
        return new Hyperlink(
                    messages.contentUsagePlace(metainfoUtils.getLocalizedValue(usage.getContent().getCaption()), createTabName(usage)), 
                    StringUtilities.getHrefByToken(historyMapper.getToken(mcPlace)));
        //@formatter:on
    }

    private String createTabName(AttributeUsageInContent usage)
    {
        //@formatter:off
        return  Stream.concat(
                Stream.of(getUIFormTitle(usage.getFormId())),
                usage.getTabChain()
                        .stream()
                        .map(Tab::getCaption)
                        .map(metainfoUtils::getLocalizedValue))
                .collect(joining(" / "));
        //@formatter:on
    }

    private String getClassTabId(String formId)
    {
        if (UI.WINDOW_KEY.equals(formId) || UI.Form.NEW.equals(formId) || UI.Form.EDIT.equals(formId))
        {
            return formId;
        }
        return UI.Form.CUSTOM;
    }

    private String getUIFormTitle(String formId)
    {
        switch (formId)
        {
            case UI.WINDOW_KEY:
                return clsMessages.dynadmin();
            case UI.Form.NEW:
                return clsMessages.newEntryForm();
            case UI.Form.EDIT:
                return clsMessages.editForm();
            default:
                return clsMessages.otherForms();
        }
    }
}