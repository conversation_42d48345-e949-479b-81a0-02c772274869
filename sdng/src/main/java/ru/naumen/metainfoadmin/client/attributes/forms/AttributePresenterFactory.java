/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.forms;

import ru.naumen.core.client.GenericFactory;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

public interface AttributePresenterFactory<F extends ObjectForm> extends
        GenericFactory<Context, CallbackPresenter<Attribute, MetaClass>>
{
}