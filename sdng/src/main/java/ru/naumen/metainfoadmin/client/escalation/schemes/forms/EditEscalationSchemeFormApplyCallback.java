package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import jakarta.inject.Inject;

import ru.naumen.core.client.mvp.PresenterCommandEvent;
import ru.naumen.core.client.mvp.PresenterCommandEvent.PresenterCommandCode;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;

import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 21.08.2012
 *
 */
public class EditEscalationSchemeFormApplyCallback extends EscalationSchemeFormApplyCallback<ObjectFormEdit>
{
    @Inject
    public EditEscalationSchemeFormApplyCallback(@Assisted EscalationSchemeForm<ObjectFormEdit> form)
    {
        super(form);
    }

    @Override
    protected void handleSuccess(SimpleResult<DtoContainer<EscalationScheme>> response)
    {
        super.handleSuccess(response);
        form.getLocalEventBus().fireEvent(new PresenterCommandEvent(PresenterCommandCode.REFRESH));
    }
}
