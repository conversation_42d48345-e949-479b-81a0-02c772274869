/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap.ValueMapItemFormConstants;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationVMapItemFormConstants extends ValueMapItemFormConstants<EscalationValueMapItemFormContext>
{
    // @formatter:off
    private static final String[] CODES = {
        CatalogItem.ITEM_TITLE,
        CatalogItem.ITEM_CODE,
        ValueMapCatalogItem.LINKED_CLASSES,
        ValueMapCatalogItem.TARGET_ATTRS,
        ValueMapCatalogItem.DEFAULT_OBJECT,
        ValueMapCatalogItem.SOURCE_ATTRS,
        ValueMapCatalogItem.DESCRIPTION,
        ValueMapCatalogItem.SETTINGS_SET
    };
    // @formatter:on

    @Override
    public String[] propertyCodes()
    {
        return CODES;
    }
}