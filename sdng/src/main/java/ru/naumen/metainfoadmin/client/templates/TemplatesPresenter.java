package ru.naumen.metainfoadmin.client.templates;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TEMPLATES;

import java.util.List;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.admin.client.AbstractSettingsPresenter;
import ru.naumen.admin.client.settings.SettingsItem;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;

/**
 * Презентер страницы "Шаблоны"
 * <AUTHOR>
 * @since 06.04.2018
 */
public class TemplatesPresenter extends AbstractSettingsPresenter<TemplatesPlace>
{
    @Inject
    public TemplatesPresenter(AdminTabDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected List<SettingsItem> getSettingsItems()
    {
        return settingsRegistry.getTemplatesSettings();
    }

    @Override
    protected String getSettingsTitle()
    {
        return messages.templates();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return TEMPLATES;
    }
}