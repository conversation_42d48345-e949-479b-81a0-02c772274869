package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ContentTemplate;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.ContentTemplatePropertiesTranslator;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateMessages;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateServiceAsync;

/**
 * Логика обновления свойства «Шаблон» для ссылок на контенты (кроме списков).
 * <AUTHOR>
 * @since Apr 12, 2021
 */
public class LinkToContentContentTemplateRefreshDelegateImpl
        implements PropertyDelegateRefresh<SelectItem, ListBoxWithEmptyOptProperty>
{
    private final ContentTemplateServiceAsync contentTemplateService;
    private final ContentTemplateMessages contentTemplateMessages;

    @Inject
    public LinkToContentContentTemplateRefreshDelegateImpl(
            ContentTemplateServiceAsync contentTemplateService,
            ContentTemplateMessages contentTemplateMessages)
    {
        this.contentTemplateService = contentTemplateService;
        this.contentTemplateMessages = contentTemplateMessages;
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Boolean> callback)
    {
        String contentType = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);
        boolean visible = LinkToContentMetaClassPropertiesProcessor.isLinkToContentElementType(context)
                          && HierarchyGrid.class.getSimpleName().equals(contentType);
        IProperties environment = ContentTemplatePropertiesTranslator.translate(context.getPropertyValues());
        String templateValue = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TEMPLATE);
        contentTemplateService.loadContentTemplates(environment, new BasicCallback<List<DtObject>>()
        {
            @Override
            protected void handleSuccess(List<DtObject> value)
            {
                if (!ContentTemplate.NEW_TEMPLATE.equals(templateValue) && null != templateValue
                    && value.stream().noneMatch(item -> templateValue.equals(item.getUUID())))
                {
                    property.setValue(null);
                    context.getPropertyValues().setProperty(MenuItemLinkToContentCode.CONTENT_TEMPLATE, null);
                }
                SingleSelectCellList<String> widget = property.getValueWidget();
                widget.clear();
                widget.addItem(contentTemplateMessages.addNew(), ContentTemplate.NEW_TEMPLATE);
                value.forEach(dto -> widget.addItem(dto.getTitle(), dto.getUUID()));
                property.trySetObjValue(templateValue);
            }
        });
        callback.onSuccess(visible);
    }
}
