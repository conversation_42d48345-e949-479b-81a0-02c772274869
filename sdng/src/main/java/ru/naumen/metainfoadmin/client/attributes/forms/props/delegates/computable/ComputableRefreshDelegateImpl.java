package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.computable;

import static ru.naumen.metainfo.shared.Constants.NOT_COMPUTABLE_ATTRIBUTE_TYPES;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.EXPORT_NDAP;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.GenerationRuleDelegateHelper;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат, вычисляющий, должно ли отображаться свойство "Вычислимый" на форме атрибута или нет 
 * <AUTHOR>
 * @since 05.07.2012
 *
 */
public class ComputableRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Boolean determinable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);
        Boolean definableByTemplate = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPOSITE);
        Boolean exportNDAP = context.getPropertyValues().getProperty(EXPORT_NDAP);
        callback.onSuccess(!exportNDAP && isComputable(context) && !determinable && !definableByTemplate
                           && !GenerationRuleDelegateHelper.hideDependenceField(context));
    }

    protected boolean isComputable(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        boolean computable = !NOT_COMPUTABLE_ATTRIBUTE_TYPES.contains(attrType);
        return computable;
    }
}
