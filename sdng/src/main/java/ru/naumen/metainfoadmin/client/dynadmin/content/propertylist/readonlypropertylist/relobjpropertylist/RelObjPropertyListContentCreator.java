package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.relobjpropertylist;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.collect.Lists;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.SimpleContentCreatorBase;

/**
 * {@link Presenter} для отображения параметров добавляемого контента типа "Параметры связанного объекта"
 * <AUTHOR>
 * @since 13.01.2011
 *
 */
public class RelObjPropertyListContentCreator extends SimpleContentCreatorBase<RelObjPropertyList>
{
    @Inject
    private MetainfoServiceAsync metainfoService;
    @Named(PropertiesGinModule.CHECK_BOX)
    @Inject
    private Property<Boolean> showAttrDescription;
    @Named(PropertiesGinModule.CHECK_BOX)
    @Inject
    private Property<Boolean> allowEdit;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> attribute;
    @Inject
    private TextBoxProperty relObjCalss;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> attributeGroup;

    private HashMap<String, MetaClassLite> attributesClsMetainfo = new HashMap<String, MetaClassLite>();

    public void refreshProperties()
    {
        attributeGroup.<SingleSelectCellList<?>> getValueWidget().clear();
        MetaClassLite selectedMetaClass = attributesClsMetainfo
                .get(SelectListPropertyValueExtractor.getValue(attribute));
        relObjCalss.setValue(selectedMetaClass.getTitle());
        metainfoService.getMetaClass(selectedMetaClass.getFqn(), new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass value)
            {
                ArrayList<AttributeGroup> groups = Lists.newArrayList(value.getAttributeGroups());
                metainfoUtils.sort(groups);
                for (AttributeGroup grp : groups)
                {
                    attributeGroup.<SingleSelectCellList<?>> getValueWidget().addItem(grp.getTitle(), grp.getCode());
                }
            }
        });
    }

    @Override
    protected void bindPropertiesInner()
    {
        super.bindPropertiesInner();

        ensureDebugId();
        add(cmessages.attribute(), attribute);
        relObjCalss.setDisable();
        add(messages.relatedObjectClass(), relObjCalss);
        add(cmessages.attributeGroup(), attributeGroup);
        add(cmessages.showAttrDescription(), showAttrDescription);
        if (!UI.WINDOW_KEY.equals(context.getCode()))
        {
            allowEdit.setEnabled(false);
        }
        add(cmessages.allowEditRelObj(), allowEdit);

        addValidation(caption, notEmptyValidator);

        attribute.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                refreshProperties();
            }
        });

        Collection<ClassFqn> fqns = new ArrayList<ClassFqn>();
        final List<Attribute> attributes = new ArrayList<Attribute>();
        for (Attribute attr : context.getMetainfo().getAttributes())
        {
            if (Constants.ObjectAttributeType.CODE.equals(attr.getType().getCode()) &&
                !attr.getType().isAttributeOfRelatedObject())
            {
                attributes.add(attr);
                ObjectAttributeType type = attr.getType().cast();
                fqns.add(type.getRelatedMetaClass());
            }
        }
        metainfoUtils.sort(attributes);
        metainfoService.getMetaClasses(fqns, new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> value)
            {
                for (Attribute attr : attributes)
                {
                    ObjectAttributeType type = attr.getType().cast();
                    for (MetaClassLite metainfo : value)
                    {
                        if (metainfo.getFqn().equals(type.getRelatedMetaClass()))
                        {
                            attributesClsMetainfo.put(attr.getCode(), metainfo);
                            attribute.<SingleSelectCellList<?>> getValueWidget().addItem(attr.getTitle(),
                                    attr.getCode());
                            break;
                        }
                    }
                }
                refreshProperties();
            }
        });
    }

    @Override
    protected RelObjPropertyList getContentInner()
    {
        RelObjPropertyList content = contentProvider.get();
        content.setAttributeGroup(SelectListPropertyValueExtractor.getValue(attributeGroup));
        content.setAttrCode(SelectListPropertyValueExtractor.getValue(attribute));
        content.setShowCaption(showCaption.getValue());
        content.setShowAttrDescription(showAttrDescription.getValue());
        content.setAllowEdit(allowEdit.getValue());
        content.setToolPanel(toolBarUtils.createDefault(content));
        return content;
    }

    private void ensureDebugId()
    {
        DebugIdBuilder.ensureDebugId(attribute, "attribute");
        DebugIdBuilder.ensureDebugId(attributeGroup, "attributeGroup");
        DebugIdBuilder.ensureDebugId(relObjCalss, "relObjCalss");
        DebugIdBuilder.ensureDebugId(showAttrDescription, "showAttrDescription");
        DebugIdBuilder.ensureDebugId(allowEdit, "allowEditRelObj");
    }
}
