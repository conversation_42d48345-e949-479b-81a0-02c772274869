package ru.naumen.metainfoadmin.client.embeddedapplications.usagelist.commands;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.EmbeddedApplicationAsyncServiceImpl;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;

/**
 * Команда удаления места использования встроенного приложения
 * <AUTHOR>
 * @since 25.10.2021
 */
public class UsagePointApplicationDeleteCommand extends ObjectCommandImpl<Collection<DtObject>, Void>
{
    private final CommonMessages cmessages;
    private final EmbeddedApplicationMessages embeddedApplicationMessages;
    private final EmbeddedApplicationAsyncServiceImpl adminSettingsService;

    @Inject
    public UsagePointApplicationDeleteCommand(@Assisted CommandParam<Collection<DtObject>, Void> param, Dialogs dialogs,
            CommonMessages cmessages,
            EmbeddedApplicationMessages embeddedApplicationMessages,
            EmbeddedApplicationAsyncServiceImpl adminSettingsService)
    {
        super(param, dialogs);
        this.cmessages = cmessages;
        this.embeddedApplicationMessages = embeddedApplicationMessages;
        this.adminSettingsService = adminSettingsService;
    }

    @Override
    protected String getDialogMessage(Collection<DtObject> values)
    {
        if (values.size() == 1)
        {
            return cmessages.confirmDeleteQuestion(embeddedApplicationMessages.usagePlace(),
                    values.iterator().next().getTitle());
        }
        return cmessages.confirmDeleteQuestion2(cmessages.usagePlaces());
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

    @Override
    protected void onDialogSuccess(CommandParam<Collection<DtObject>, Void> param)
    {
        List<String> usagePointsCodes = param.getValue()
                .stream()
                .map(IUUIDIdentifiable::getUUID)
                .collect(Collectors.toList());
        EmbeddedApplicationAdminSettingsDto embeddedApplication =
                ((UsagePointApplicationCommandParam)param).getEmbeddedApplication();
        embeddedApplication.getUsagePoints().removeIf(u -> usagePointsCodes.contains(u.getCode()));

        adminSettingsService.saveEmbeddedApplication(embeddedApplication, false, true, usagePointsCodes, false,
                new AsyncCallback<EmbeddedApplicationAdminSettingsDto>()
                {
                    @Override
                    public void onFailure(Throwable throwable)
                    {
                        // do nothing
                    }

                    @Override
                    public void onSuccess(EmbeddedApplicationAdminSettingsDto value)
                    {
                        param.getCallback().onSuccess(null);
                    }
                });
    }
}