/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.forms.edit;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.attr.EditFormAvailibleTypesProvider;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.metainfo.shared.dispatch2.EditAttributeAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormGinModule;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormPropertyControllerFactorySelectorImpl;

/**
 * <AUTHOR>
 * @since 28.06.2013
 *
 */
public class AttributeFormEditGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        //@formatter:off
        install(PropertyControllerGinModule.create(Attribute.class, ObjectFormEdit.class)
                .setPropertyControllerFactory(new TypeLiteral<AttributeFormPropertyControllerFactorySelectorImpl<ObjectFormEdit>>(){})
                .setPropertyParametersDescriptorFactory(new TypeLiteral<EditAttributeFormPropertyParametersDescriptorFactoryImpl>(){}));
        install(AttributeFormGinModule.create(ObjectFormEdit.class)
                .setAfterBindHandler(new TypeLiteral<EditAttributeFormAfterBindHandlerImpl>(){})
                .setContextPropertiesSetter(new TypeLiteral<EditAttributeFormContextPropertiesSetterImpl>(){})
                .setApplyFormHandler(AttributeFormApplyHandlerEditImpl.class)
                .setPropertyValuesInitializer(new TypeLiteral<EditAttributeFormPropertyValuesInitializerImpl<ObjectFormEdit>>(){})
                .setMessages(EditAttributeFormMessages.class)
                .setConstants(EditAttributeFormConstants.class)
                .setAction(EditAttributeAction.class)
                .setAttrService(EditFormAvailibleTypesProvider.class));
        //@formatter:on
    }
}