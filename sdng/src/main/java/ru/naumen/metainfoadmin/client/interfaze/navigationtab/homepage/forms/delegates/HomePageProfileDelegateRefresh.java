package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import java.util.Collection;
import java.util.List;
import java.util.function.Predicate;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.RoleUtils;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityProfilesAction;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityProfilesResponse;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;

/**
 * Делегат обновления для виджета "Профили" {@link HomePage#PROFILES}
 *
 * <AUTHOR>
 * @since 18.01.2023
 */
public class HomePageProfileDelegateRefresh
        implements PropertyDelegateRefresh<Collection<SelectItem>, MultiSelectBoxProperty>
{
    private final DispatchAsync dispatch;
    private final MetainfoUtils metainfoUtils;
    private final NavigationSettingsMessages messages;

    @Inject
    public HomePageProfileDelegateRefresh(
            final DispatchAsync dispatch,
            final MetainfoUtils metainfoUtils,
            final NavigationSettingsMessages messages)
    {
        this.dispatch = dispatch;
        this.metainfoUtils = metainfoUtils;
        this.messages = messages;
    }

    @Override
    public void refreshProperty(final PropertyContainerContext context, final MultiSelectBoxProperty property,
            final AsyncCallback<Boolean> callback)
    {
        property.setDescription(messages.allProfilesDescription());
        final Predicate<Profile> absoluteRoleProfilePredicate = profile -> !RoleUtils.hasOnlyRelativeRole(
                profile.getRoles());
        final Collection<String> selectedProfiles = context.getPropertyValues()
                .<Collection<String>> getProperty(HomePage.PROFILES);
        dispatch.execute(new GetSecurityProfilesAction(null, false), new BasicCallback<GetSecurityProfilesResponse>()
        {
            @Override
            protected void handleSuccess(GetSecurityProfilesResponse value)
            {
                property.getValueWidget().clear();
                List<Profile> profiles = value.get();
                metainfoUtils.sort(profiles);
                profiles.stream()
                        .filter(absoluteRoleProfilePredicate)
                        .forEach(p -> property.getValueWidget().addItem(p.getTitle(), p.getCode()));
                property.trySetObjValue(selectedProfiles);
                context.getPropertyValues().setProperty(HomePage.PROFILES, selectedProfiles);
                callback.onSuccess(true);
            }
        });
    }
}