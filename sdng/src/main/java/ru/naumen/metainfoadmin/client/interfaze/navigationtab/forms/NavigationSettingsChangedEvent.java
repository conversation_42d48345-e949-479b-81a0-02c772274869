package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms;

import com.google.gwt.event.shared.GwtEvent;

import jakarta.validation.constraints.NotNull;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

/**
 * <AUTHOR>
 * @since 29.07.2020
 *
 */
public class NavigationSettingsChangedEvent extends GwtEvent<NavigationSettingsChangedHandler>
{

    private static Type<NavigationSettingsChangedHandler> TYPE = new Type<NavigationSettingsChangedHandler>();

    public static Type<NavigationSettingsChangedHandler> getType()
    {
        return TYPE;
    }

    private final DtoContainer<NavigationSettings> settings;

    public NavigationSettingsChangedEvent(@NotNull DtoContainer<NavigationSettings> settings)
    {
        this.settings = settings;
    }

    @Override
    public Type<NavigationSettingsChangedHandler> getAssociatedType()
    {
        return TYPE;
    }

    public DtoContainer<NavigationSettings> getSettings()
    {
        return settings;
    }

    @Override
    protected void dispatch(NavigationSettingsChangedHandler handler)
    {
        handler.onNavigationSettingsChanged(this);
    }
}