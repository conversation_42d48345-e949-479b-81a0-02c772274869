package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import static ru.naumen.core.shared.script.places.EventActionCategories.EVENTACTION_INTEGRATION;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Provider;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.script.places.AttributeCategories;
import ru.naumen.core.shared.script.places.EscalationCategories;
import ru.naumen.core.shared.script.places.EventActionCategories;
import ru.naumen.core.shared.script.places.FormParameterScriptCategories;
import ru.naumen.core.shared.script.places.OtherCategories;
import ru.naumen.core.shared.script.places.RoleCategories;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.core.shared.script.places.TimerCategory;
import ru.naumen.core.shared.script.places.WorkflowCategory;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfoadmin.client.script.ScriptsAndModulesMessages;

/**
 * Фабрика представление атрибута "Категории" для каталога скриптов
 *
 * <AUTHOR>
 * @since 06.12.2017
 */
public class AttributeCategoriesInSriptsWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    protected MetainfoServiceAsync metainfoService;
    @Inject
    protected Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;
    @Inject
    private ScriptsAndModulesMessages messages;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(final PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        final SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();
        addItems(widget, getCategories());
        callback.onSuccess(widget);
    }

    protected void addItems(SingleSelectCellList<DtObject> widget, List<ScriptCategory> categories)
    {
        List<ScriptCategory> sortedCategories = categories.stream()
                .filter(category -> category != null && category.isCatalogCategory())
                .sorted(Comparator.comparing(a -> messages.scriptCategories(a.name())))
                .collect(Collectors.toList());
        for (ScriptCategory category : sortedCategories.subList(1, sortedCategories.size() - 1))
        {
            widget.addItem(new SimpleDtObject(category.name(), messages.scriptCategories(category.name())));
        }
        widget.addItem(new SimpleDtObject(sortedCategories.get(0).name(),
                messages.scriptCategories(sortedCategories.get(0).name())));
    }

    private List<ScriptCategory> getCategories()
    {
        List<ScriptCategory> scriptCategory = new ArrayList<>();
        Collections.addAll(scriptCategory, AttributeCategories.values());
        Collections.addAll(scriptCategory, FormParameterScriptCategories.values());
        Collections.addAll(scriptCategory, EscalationCategories.values());
        Collections.addAll(scriptCategory, OtherCategories.values());
        Collections.addAll(scriptCategory, RoleCategories.values());
        Collections.addAll(scriptCategory, TimerCategory.values());
        Collections.addAll(scriptCategory, WorkflowCategory.values());
        scriptCategory.addAll(getEventActionCategories());
        return scriptCategory;
    }

    private List<ScriptCategory> getEventActionCategories()
    {
        return Arrays.stream(EventActionCategories.values())
                .filter(sc -> isGatewayIntegrationEnabled() || !sc.equals(EVENTACTION_INTEGRATION))
                .collect(Collectors.toList());
    }

    private native boolean isGatewayIntegrationEnabled()/*-{
                                                var item = $wnd.gatewayIntegrationEnabled;
                                                return item==true;
                                                }-*/;
}