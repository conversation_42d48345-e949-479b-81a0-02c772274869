/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.columns;

import static ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode.TITLE_CODE_TYPE;

import java.util.List;
import java.util.function.Predicate;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.Key;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.name.Names;

import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.grouplist.GroupListGinModule;
import ru.naumen.core.client.widgets.grouplist.WidgetCreator;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.AttrTypeColumnWidget;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.DefaultValuePrsCustomizersRegistry;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.DefaultValuePrsCustomizersRegistryImpl;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.LinkedToColumnAttrTypeWidgetsProvider;

/**
 * <AUTHOR>
 *
 */
public class AttributeListColumnGinModule extends AbstractGinModule
{
    public interface AttributesListType
    {
        String STANDARD = "standard";
        String EXTENDED = "extended";
    }

    public interface AttrBooleanColumnFactory
    {
        AttrBooleanColumn create(@Assisted("debugId") String debugId, Predicate<? super Attribute> applicablePredicate,
                @Assisted("hint") String hint);
    }

    /**
     * Фабрика для создания колонки {@link AttrTitleCodeTypeColumn}
     */
    public interface AttrTitleCodeTypeColumnFactory
    {
        /**
         * Создать колонку
         * @param debugId id
         * @param registrationContainer контейнер для регистрации обработчиков событий
         * @return колонка
         */
        AttrTitleCodeTypeColumn create(String debugId, RegistrationContainer registrationContainer);
    }

    public interface AttributeColumnCode
    {
        String TITLE_CODE_TYPE = "titleCodeType";
        String EDITABLE = "editable";
        String EDITABLE_IN_LISTS = "editableInLists";
        String REQUIRED = "required";
        String REQUIRED_IN_INTERFACE = "requiredInInterface";
        String LINKED_TO = "linkedTo";
        String DETERMINED_BY = "determinedBy";
        String FILTERING_ON_EDIT = "filteringOnEdit";
        String CALCULATING_ON_EDIT = "calculatingOnEdit";
        String DEFAULT_VALUE = "defaultValue";
        String HIDE_EMPTY = "hideEmpty";
        String HIDE_NO_VALUE_TO_SELECT = "hideNoValueToSelect";

        String TITLE = "title";
        String CODE = "code";
        String TYPE = "type";
        String UNIQUE = "unique";
        String EDIT_BUTTON = "editButton";
        String SHOW_USAGE_BUTTON = "showUsageButton";
        String DELETE_BUTTON = "deleteButton";

        String MOVE_UP_BUTTON = "moveUpButton";
        String MOVE_DOWN_BUTTON = "moveDownButton";
    }

    public static final String DEFAULT_VALUE_EXT = "defaultValueExt";
    public static final String ATTR_LIST_READY_STATE = "attrListReadyState";

    public static final String COL_SORTER_TITLE = TITLE_CODE_TYPE + "Title";
    public static final String COL_SORTER_CODE = TITLE_CODE_TYPE + "Code";
    public static final String COL_SORTER_TYPE = TITLE_CODE_TYPE + "Type";
    public static final String COL_SORTER_SYSTEM_FIRST = TITLE_CODE_TYPE + "SystemFirst";
    public static final String COL_SORTER_USER_FIRST = TITLE_CODE_TYPE + "UserFirst";

    @Override
    protected void configure()
    {
        //@formatter:off
        bind(AttributeListColumnFactory.class)
            .annotatedWith(Names.named(AttributeListColumnGinModule.AttributesListType.STANDARD))
            .to(AttributeListColumnFactoryImpl.class);

        bind(AttributeListColumnFactory.class)
            .annotatedWith(Names.named(AttributeListColumnGinModule.AttributesListType.EXTENDED))
            .to(ExtAttributeListColumnFactoryImpl.class);
        
        bind(AttributeListColumnInfoFactory.class)
            .annotatedWith(Names.named(AttributeListColumnGinModule.AttributesListType.STANDARD))
            .to(AttributeListColumnInfoFactoryImpl.class);
        
        bind(AttributeListColumnInfoFactory.class)
            .annotatedWith(Names.named(AttributeListColumnGinModule.AttributesListType.EXTENDED))
            .to(ExtAttributeListColumnInfoFactoryImpl.class);

        //TODO dzevako удалить в 11589 bind(new TypeLiteral<BooleanColumnFactory<Attribute>>() {})

        install(new GinFactoryModuleBuilder()
                .implement(new TypeLiteral<WidgetCreator<Attribute>>(){}, AttrTitleCodeTypeColumn.class)
                .build(Key.get(AttrTitleCodeTypeColumnFactory.class, Names.named(AttributeListColumnGinModule.AttributeColumnCode.TITLE_CODE_TYPE))));

        bind(new TypeLiteral<WidgetCreator<Attribute>>(){})
            .annotatedWith(Names.named(AttributeListColumnGinModule.AttributeColumnCode.LINKED_TO))
            .to(AttrLinkedToColumn.class);
        
        bind(new TypeLiteral<List<AttrTypeColumnWidget>>(){}).toProvider(LinkedToColumnAttrTypeWidgetsProvider.class)
            .in(Singleton.class);
        
        bind(DefaultValuePrsCustomizersRegistry.class).to(DefaultValuePrsCustomizersRegistryImpl.class)
            .in(Singleton.class);
        
        bind(new TypeLiteral<WidgetCreator<Attribute>>(){})
            .annotatedWith(Names.named(AttributeListColumnGinModule.AttributeColumnCode.DETERMINED_BY))
            .to(AttrDeterminedByColumn.class);
        
        bind(new TypeLiteral<WidgetCreator<Attribute>>(){})
            .annotatedWith(Names.named(AttributeListColumnGinModule.AttributeColumnCode.FILTERING_ON_EDIT))
            .to(AttrFilteringOnEditColumn.class);
        
        bind(new TypeLiteral<WidgetCreator<Attribute>>(){})
            .annotatedWith(Names.named(AttributeListColumnGinModule.AttributeColumnCode.CALCULATING_ON_EDIT))
            .to(AttrCalculatingOnEditColumn.class);
        
        bind(new TypeLiteral<WidgetCreator<Attribute>>(){})
            .annotatedWith(Names.named(AttributeListColumnGinModule.AttributeColumnCode.DEFAULT_VALUE))
            .to(DefaultValueColumn.class);
        
        bind(new TypeLiteral<WidgetCreator<Attribute>>(){})
            .annotatedWith(Names.named(AttributeListColumnGinModule.DEFAULT_VALUE_EXT))
            .to(ExtDefaultValueColumn.class);
        
        bind(ReadyState.class)
            .annotatedWith(Names.named(AttributeListColumnGinModule.ATTR_LIST_READY_STATE))
            .to(AttrListReadyState.class).in(Singleton.class);

        install(new GroupListGinModule<>(Attribute.class));
        install(new GinFactoryModuleBuilder()
                .implement(AttrBooleanColumn.class, AttrBooleanColumn.class)
                .build(AttrBooleanColumnFactory.class));
        //@formatter:on
    }
}
