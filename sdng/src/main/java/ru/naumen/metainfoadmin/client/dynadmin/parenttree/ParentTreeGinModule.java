package ru.naumen.metainfoadmin.client.dynadmin.parenttree;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.tree.dto.DtoTreeGinModule.DtoTree;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithFolders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactoryGinModule;
import ru.naumen.core.client.tree.dto.factory.DtoTreeViewModelGinModule;
import ru.naumen.core.client.tree.dto.view.DtoTreeFilteredViewModelFactoryImpl;
import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Gin-модуль для редактирования родителей у контентов и вкладок
 *
 * <AUTHOR>
 * @since 10.08.2021
 */
public class ParentTreeGinModule extends AbstractGinModule
{
    //@formatter:off
    public static class ContentParentTree extends DtoTree {}
    public static class TabParentTree extends DtoTree {}
    //@formatter:on

    @Override
    protected void configure()
    {
        //@formatter:off
        install(DtoTreeViewModelGinModule.create(ContentParentTree.class));
        install(DtoTreeViewModelGinModule.create(TabParentTree.class));
        install(DtoTreeFactoryGinModule.single(ContentParentTree.class, WithFolders.class, ContentParentTreeFactoryContext.class)
                .setDtoTreeViewModelFactory(new TypeLiteral<ContentParentViewModelFactoryImpl>() {})
                .setDataSource(new TypeLiteral<ContentParentDataSource>() {}));

        install(DtoTreeFactoryGinModule.single(TabParentTree.class, WithFolders.class,TabParentTreeFactoryContext.class)
                .setDtoTreeViewModelFactory(new TypeLiteral<DtoTreeFilteredViewModelFactoryImpl
                        <TabParentTree, WithFolders, FilteredSingleSelectionModel<DtObject>, TabParentTreeFactoryContext>>() {})
                .setDataSource(new TypeLiteral<TabParentDataSource>() {}));
        //@formatter:on
    }
}