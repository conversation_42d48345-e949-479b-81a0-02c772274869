package ru.naumen.metainfoadmin.client.embeddedapplications.form;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.client.SafeHtmlTemplates;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Provider;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs.Buttons;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.events.UploadCompleteEvent;
import ru.naumen.core.client.events.UploadCompleteHandler;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.validation.EmbeddedApplicationCodeValidator;
import ru.naumen.core.client.validation.IntegerEmptyOrNotNegativeValidator;
import ru.naumen.core.client.validation.IntegerNotNegativeValidator;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.ValidationMessages;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.FileUploadProperty;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * <AUTHOR>
 * @since 07.07.2016
 *
 */
public abstract class AbstractEmbeddedApplicationFormPresenter extends OkCancelPresenter<PropertyDialogDisplay>
        implements CallbackPresenter<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto>
{
    private static class ApplicationFileUploadCompleteHandler implements UploadCompleteHandler
    {
        private final PropertyDialogDisplay display;

        ApplicationFileUploadCompleteHandler(PropertyDialogDisplay display)
        {
            this.display = display;
        }

        @Override
        public void onUploadComplete(UploadCompleteEvent event)
        {
            if (null != event.getUploadErrorMessage())
            {
                display.addErrorMessage(event.getUploadErrorMessage());
            }
        }
    }

    public interface Templates extends SafeHtmlTemplates
    {
        @Template("<div>{0}</div>")
        SafeHtml wrap(String formatted);
    }

    @Inject
    protected AdminMetainfoServiceAsync metainfoService;
    @Inject
    protected EmbeddedApplicationMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    protected SelectListProperty<String, SelectItem> applicationType;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    protected Property<Boolean> fullscreenAllowed;
    @Inject
    protected Processor validation;
    @Inject
    protected NotEmptyValidator notEmptyValidator;
    @Inject
    protected MetainfoModificationServiceAsync metainfoModificationService;
    protected EmbeddedApplicationAdminSettingsDto application;
    protected AsyncCallback<EmbeddedApplicationAdminSettingsDto> saveCallback;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> code;
    @Inject
    @Named(PropertiesGinModule.INTEGER)
    protected Property<Long> initialHeight;
    @Inject
    IntegerEmptyOrNotNegativeValidator integerEmptyOrNotNegativeValidator;
    private PropertyRegistration<SelectItem> applicationTypePR;
    @Inject
    private IntegerNotNegativeValidator notNegativeValidator;
    @Inject
    private NotEmptyCollectionValidator<Collection<DtObject>> applicationFileValidator;
    @Inject
    private FileUploadProperty applicationArchive;
    @Inject
    private I18nUtil i18nUtil;
    @Inject
    @Named(PropertiesGinModule.INTEGER)
    private Property<Long> mobileHeight;
    @Inject
    private ValidationMessages validationMessages;
    private AdditionalApplicationPropertiesProvider additionalApplicationPropertiesProvider;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> title;
    /**
     * Поле комплектов, может быть null в случае недоступности суперпользователю ни одного комплекта.
     */
    @Nullable
    private Property<SelectItem> settingsSet;
    @Inject
    @Named(PropertiesGinModule.TEXT_AREA)
    private Property<String> description;
    @Inject
    private Provider<ExternalApplicationAdditionalProperties> externalApplicationPropertiesProvider;
    @Inject
    private Provider<InternalApplicationAdditionalProperties> internalApplicationPropertiesProvider;
    @Inject
    private Provider<CustomLoginFormApplicationAdditionalProperties> customLoginFormApplicationAdditionalPropertiesProvider;
    @Inject
    private Provider<ClientSideApplicationAdditionalProperties> html5ApplicationPropertiesProvider;
    @Inject
    private EmbeddedApplicationCodeValidator codeValidator;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    protected AbstractEmbeddedApplicationFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        display.asWidget().addStyleName(WidgetResources.INSTANCE.form().embeddedApplicationModalForm());
    }

    @Override
    public void init(@Nullable EmbeddedApplicationAdminSettingsDto application,
            AsyncCallback<EmbeddedApplicationAdminSettingsDto> saveCallback)
    {
        this.application = application;
        this.saveCallback = saveCallback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        EmbeddedApplicationAdminSettingsDto app = new EmbeddedApplicationAdminSettingsDto();

        app.setInitialHeight(initialHeight.getValue().intValue());
        app.setMobileHeight(mobileHeight.getValue());
        app.setFullscreenAllowed(fullscreenAllowed.getValue());
        app.setDescription(description.getValue());
        app.setDisplayTitle(title.getValue());
        app.setCode(code.getValue());

        if (application != null)
        {
            app.setOn(application.isOn());
        }

        String applicationTypeString = SelectListPropertyValueExtractor.getValue(applicationType);

        if (applicationTypeString != null
            && applicationTypeString.equals(EmbeddedApplicationType.ExternalApplication.name()))
        {
            app.setEmbeddedApplicationType(EmbeddedApplicationType.ExternalApplication);
        }

        if (applicationTypeString != null
            && applicationTypeString.equals(EmbeddedApplicationType.ClientSideApplication.name()))
        {
            app.setEmbeddedApplicationType(EmbeddedApplicationType.ClientSideApplication);
        }

        if (applicationTypeString != null
            && applicationTypeString.equals(EmbeddedApplicationType.CustomLoginFormApplication.name()))
        {
            app.setEmbeddedApplicationType(EmbeddedApplicationType.CustomLoginFormApplication);
        }

        if (applicationTypeString != null
            && applicationTypeString.equals(EmbeddedApplicationType.InternalApplication.name()))
        {
            app.setEmbeddedApplicationType(EmbeddedApplicationType.InternalApplication);
        }

        app.setSettingsSet(SelectListPropertyValueExtractor.getValue(settingsSet));
        if (!Boolean.TRUE.equals(additionalApplicationPropertiesProvider.getValueOfAdditionalProperties(app)))
        {
            return;
        }

        if (null != applicationArchive.getValue())
        {
            DtObject file = applicationArchive.getValue().iterator().next();
            app.setFileUuid(file.getUUID());
            app.setApplicationFile(file);
        }
        else
        {
            app.setFileUuid(null);
            app.setApplicationFile(null);
        }

        application = app;

        confirmation(app);
    }

    private void confirmation(EmbeddedApplicationAdminSettingsDto app)
    {
        getApplyConfirmationMessage(app, new BasicCallback<List<String>>()
        {
            @Override
            protected void handleSuccess(List<String> value)
            {
                if (!value.isEmpty())
                {
                    List<String> wrapped = new ArrayList<>();
                    for (int i = 0; i < value.size(); i++)
                    {
                        String str = value.get(i);
                        wrapped.add(i < value.size() - 1 ? str + ";" : str);
                    }
                    dialogs.question(commonMessages.confirmAction(),
                            wrapped.stream().collect(Collectors.joining("<br>")),
                            new DialogCallback()
                            {
                                @Override
                                protected void onYes(Dialog widget)
                                {
                                    super.onYes(widget);
                                    saveApplication(getDisplay().getReadyState());
                                }
                            }, Buttons.YES, Buttons.NO);
                }
                else
                {
                    saveApplication(getDisplay().getReadyState());
                }
            }
        });
    }

    protected void getApplyConfirmationMessage(
            EmbeddedApplicationAdminSettingsDto app, //NOSONAR используется в потомках
            BasicCallback<List<String>> callback)
    {
        callback.onSuccess(new ArrayList<>());
    }

    protected void bindProperties()
    {
        title.setCaption(cmessages.title());
        title.setValidationMarker(true);
        title.setMaxLength(Constants.MAX_TITLE_LENGTH);
        validation.validate(title, notEmptyValidator);
        DebugIdBuilder.ensureDebugId(title, "title");
        getDisplay().add(title);

        code.setCaption(cmessages.code());
        code.setValidationMarker(true);
        code.setMaxLength(Constants.MAX_ID_LENGTH);
        validation.validate(code, codeValidator);
        DebugIdBuilder.ensureDebugId(code, "code");
        getDisplay().add(code);

        description.setCaption(cmessages.description());
        DebugIdBuilder.ensureDebugId(description, "description");
        getDisplay().add(description);

        applicationType.setCaption(messages.applicationType());
        applicationType.setValidationMarker(true);
        applicationTypePR = getDisplay().add(applicationType);
        SingleSelectCellList<?> selList = applicationType.getValueWidget();
        selList.addItem(messages.applicationHostedOnExternalServer(),
                EmbeddedApplicationType.ExternalApplication.name());

        if (isNodeJsEnabled())
        {
            selList.addItem(messages.applicationHostedOnInternalServer(),
                    EmbeddedApplicationType.InternalApplication.name());
        }

        selList.addItem(messages.applicationNoServer(), EmbeddedApplicationType.ClientSideApplication.name());

        /* TODO: Раскомментировать в NSDPRD-15623
        if (settingsClientService.isModuleInstalled(MOBILE_API) && settingsClientService.isMobileJwtAuthEnabled())
        {
            selList.addItem(messages.applicationCustomLoginForm(),
                    EmbeddedApplicationType.CustomLoginFormApplication.name());
        }
         */
        DebugIdBuilder.ensureDebugId(applicationType, "applicationType");

        bindApplicationArchive();

        initialHeight.setCaption(messages.initialApplicationHeight());
        initialHeight.setValidationMarker(true);
        initialHeight.setMaxLength(Constants.MAX_ID_LENGTH);

        mobileHeight.setCaption(messages.mobileApplicationHeight());
        mobileHeight.setValidationMarker(false);
        mobileHeight.setMaxLength(Constants.MAX_ID_LENGTH);
        validation.validate(mobileHeight, integerEmptyOrNotNegativeValidator);
        DebugIdBuilder.ensureDebugId(mobileHeight, "mobileHeight");

        validation.validate(initialHeight, notNegativeValidator);
        DebugIdBuilder.ensureDebugId(initialHeight, "initialHeight");
        getDisplay().add(initialHeight);
        getDisplay().add(mobileHeight);

        fullscreenAllowed.setCaption(messages.fullscreenAllowed());
        DebugIdBuilder.ensureDebugId(fullscreenAllowed, "fullscreenAllowed");
        getDisplay().add(fullscreenAllowed);

        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getDisplay());
    }

    protected void fillProperties()
    {
        if (application == null)
        {
            initAdditionalPropertiesProvider(EmbeddedApplicationType.ExternalApplication);
            return;
        }
        code.setValue(application.getCode());
        title.setValue(application.getDisplayTitle());
        description.setValue(application.getDescription());
        code.setValue(application.getCode());
        initialHeight.setValue((long)application.getInitialHeight());
        mobileHeight.setValue(application.getMobileHeight());
        if (!StringUtilities.isEmpty(application.getScript()) && application.getScript() == null)
        {
            application.setScriptDto(ScriptDtoFactory.createNeedLoad(application.getScriptDto().getCode()));
        }
        fullscreenAllowed.setValue(application.isFullscreenAllowed());
        applicationType.trySetObjValue(application.getEmbeddedApplicationType().name());
        initAdditionalPropertiesProvider(application.getEmbeddedApplicationType());
        additionalApplicationPropertiesProvider.fillAdditionalProperties(application);
        applicationArchive.setValue(Lists.newArrayList(application.getApplicationFile()));
        settingsSetOnFormCreator.setValue(settingsSet, application.getSettingsSet());
        updateApplicationArchiveValidation();
    }

    protected EmbeddedApplicationAdminSettingsDto getApplication()
    {
        if (application == null)
        {
            EmbeddedApplicationAdminSettingsDto app = new EmbeddedApplicationAdminSettingsDto();
            app.setCode(code.getValue());
            app.setDisplayTitle(title.getValue());
            app.setDescription(description.getValue());
            return app;
        }
        return application;
    }

    protected Property<String> getCode()
    {
        return code;
    }

    protected Property<String> getTitle()
    {
        return title;
    }

    @Override
    protected void onBind()
    {
        bindProperties();
        fillProperties();
        registerHandlers();
        super.onBind();
        getDisplay().display();
    }

    protected void registerHandlers()
    {
        registerHandler(applicationType.addValueChangeHandler(event ->
        {
            EmbeddedApplicationType appType;
            try
            {
                String value = SelectListPropertyValueExtractor.getValue(applicationType);
                appType = null != value ? EmbeddedApplicationType.valueOf(value) : null;
            }
            catch (Exception e)
            {
                appType = null;
            }

            if (additionalApplicationPropertiesProvider != null)
            {
                additionalApplicationPropertiesProvider.removeAdditionalProperties();
            }

            updateApplicationArchiveValidation();
            initAdditionalPropertiesProvider(appType);
            eventBus.fireEvent(new UpdateTabOrderEvent(false));
            TabOrderHelper.setFocusDeffered(applicationType.getValueWidget(), true);
        }));
    }

    protected void saveApplication(ReadyState readyState)
    {

    }

    private void bindApplicationArchive()
    {
        applicationArchive.setCaption(messages.applicationFile());
        applicationArchive.getValueWidget().setValidFileExtension(Sets.newHashSet("zip"));
        applicationFileValidator.setMessage(validationMessages.fileMustBeSelected());

        DebugIdBuilder.ensureDebugId(applicationArchive, "applicationArchive");
        getDisplay().add(applicationArchive);
        registrationContainer.registerHandler(applicationArchive.getValueWidget()
                .addUploadCompleteHandler(new ApplicationFileUploadCompleteHandler(display)));
    }

    private void initAdditionalPropertiesProvider(@Nullable EmbeddedApplicationType embeddedApplicationType)
    {
        changeViewParametersVisibility(embeddedApplicationType);

        if (EmbeddedApplicationType.ExternalApplication == embeddedApplicationType)
        {
            additionalApplicationPropertiesProvider = externalApplicationPropertiesProvider.get();

        }
        else if (EmbeddedApplicationType.InternalApplication == embeddedApplicationType)
        {
            additionalApplicationPropertiesProvider = internalApplicationPropertiesProvider.get();
        }

        else if (EmbeddedApplicationType.ClientSideApplication == embeddedApplicationType)
        {
            additionalApplicationPropertiesProvider = html5ApplicationPropertiesProvider.get();
        }
        else if (EmbeddedApplicationType.CustomLoginFormApplication == embeddedApplicationType)
        {
            additionalApplicationPropertiesProvider = customLoginFormApplicationAdditionalPropertiesProvider.get();
        }

        if (additionalApplicationPropertiesProvider != null)
        {
            additionalApplicationPropertiesProvider.addAdditionalProperties(getDisplay(), applicationTypePR,
                    registrationContainer);
        }
    }

    private void changeViewParametersVisibility(EmbeddedApplicationType embeddedApplicationType)
    {
        if (embeddedApplicationType == EmbeddedApplicationType.CustomLoginFormApplication)
        {
            hide(initialHeight, mobileHeight, fullscreenAllowed);
        }
        else
        {
            show(initialHeight, mobileHeight, fullscreenAllowed);
        }
    }

    private static void hide(Property<?>... components)
    {
        for (Property<?> component : components)
        {
            component.asWidget().getParent().getParent().setVisible(false);
        }
    }

    private static void show(Property<?>... components)
    {
        for (Property<?> component : components)
        {
            component.asWidget().getParent().getParent().setVisible(true);
        }
    }

    private native boolean isNodeJsEnabled()/*-{
                                            var item = $wnd.embeddedapplicationNodejsDisabled
                                            if(item==true) {
                                            return false;
                                            }
                                            return true;
                                            }-*/;

    private void updateApplicationArchiveValidation()
    {
        String value = SelectListPropertyValueExtractor.getValue(applicationType);
        EmbeddedApplicationType appType = null != value ? EmbeddedApplicationType.valueOf(value) : null;
        if (EmbeddedApplicationType.ExternalApplication == appType)
        {
            applicationArchive.setValidationMarker(false);
            applicationArchive.getValidationWidget().asWidget().setVisible(false);
            validation.unvalidate(applicationArchive);
        }
        else
        {
            applicationArchive.setValidationMarker(true);
            validation.validate(applicationArchive, applicationFileValidator);
        }
    }
}
