package ru.naumen.metainfoadmin.client;

import jakarta.inject.Inject;

import com.google.gwt.event.logical.shared.BeforeSelectionHandler;
import com.google.gwt.event.shared.HandlerRegistration;

import ru.naumen.core.client.TitledTabDisplayImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.tabbar.TitledCompactTabDisplayImpl;
import ru.naumen.core.client.widgets.NavigationLineWidget;
import ru.naumen.core.client.widgets.WidgetResources;

/**
 * Реализация {@link AdminTabDisplay} Рисует панель вкладок, которая заполняется {@link ClassPresenter презентером}
 *
 * <AUTHOR>
 *
 */
@SuppressWarnings("java:S1874") // так как класс ещё используется
public class AdminTabDisplayImpl extends TitledTabDisplayImpl implements AdminTabDisplay
{
    @Inject
    public AdminTabDisplayImpl(NavigationLineWidget navigationLine, TitledCompactTabDisplayImpl tabPanel,
            CommonMessages messages)
    {
        super(navigationLine, tabPanel, messages);
        innerHeader.add(tabPanel.getTabBar());
        // TODO dzevako удалить весь этот шлак
        innerHeader.addStyleName(WidgetResources.INSTANCE.all().flexGenerous());
        getContent().asWidget().addStyleName(WidgetResources.INSTANCE.all().flexGreedy());
        getContent().asWidget().addStyleName(WidgetResources.INSTANCE.all().flex());
        getContent().asWidget().addStyleName(WidgetResources.INSTANCE.all().flexVertical());
        tabPanel.getTabBar().addStyleName(WidgetResources.INSTANCE.all().flexGenerous());
        tabPanel.getDeckPanel().addStyleName(WidgetResources.INSTANCE.all().flexGreedy());
    }

    @Override
    public HandlerRegistration addBeforeSelectionHandler(BeforeSelectionHandler<Integer> handler)
    {
        return tabPanel.addBeforeSelectionHandler(handler);
    }

    @Override
    public boolean inProcessing()
    {
        return processing;
    }
}
