package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.readonlypropertylist.clientinfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ClientInfo;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.EditRelObjPropertyListContentPresenterBase;

/**
 * {@link Presenter} для редактирования контента типа {@link ClientInfo}
 *
 * <AUTHOR>
 *
 */
public class EditClientInfoContentPresenter extends EditRelObjPropertyListContentPresenterBase<ClientInfo>
{
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> ouAttributeGroup;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> emplAttributeGroup;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> teamAttributeGroup;

    @Inject
    public EditClientInfoContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected String getRelationAttrCode()
    {
        return Constants.Association.CLIENT;
    }

    @Override
    public void updateCurrentContent()
    {
        super.updateCurrentContent();
        content.setEmplAttributeGroup(SelectListPropertyValueExtractor.getValue(emplAttributeGroup));
        content.setOuAttributeGroup(SelectListPropertyValueExtractor.getValue(ouAttributeGroup));
        content.setTeamAttributeGroup(SelectListPropertyValueExtractor.getValue(teamAttributeGroup));
        content.setAttributeGroup(content.getEmplAttributeGroup());
    }

    @Override
    protected void restoreContent(ClientInfo oldContent)
    {
        super.restoreContent(oldContent);
        content.setEmplAttributeGroup(oldContent.getEmplAttributeGroup());
        content.setOuAttributeGroup(oldContent.getOuAttributeGroup());
        content.setTeamAttributeGroup(oldContent.getTeamAttributeGroup());
        content.setAttributeGroup(oldContent.getAttributeGroup());
    }

    @Override
    protected boolean isContentEquals(ClientInfo oldContent)
    {
        return super.isContentEquals(oldContent)
               && eq(oldContent.getEmplAttributeGroup(), content.getEmplAttributeGroup())
               && eq(oldContent.getOuAttributeGroup(), content.getOuAttributeGroup())
               && eq(oldContent.getTeamAttributeGroup(), content.getTeamAttributeGroup());
    }

    @Override
    protected void bindPropertiesInner()
    {
        emplAttributeGroup.setCaption(messages.emplAttributeGroup());
        emplAttributeGroup.setValidationMarker(true);
        DebugIdBuilder.ensureDebugId(emplAttributeGroup, "emplAttributeGroup");
        getDisplay().add(emplAttributeGroup);

        ouAttributeGroup.setCaption(messages.ouAttributeGroup());
        ouAttributeGroup.setValidationMarker(true);
        DebugIdBuilder.ensureDebugId(ouAttributeGroup, "ouAttributeGroup");
        getDisplay().add(ouAttributeGroup);

        teamAttributeGroup.setCaption(messages.teamAttributeGroup());
        teamAttributeGroup.setValidationMarker(true);
        DebugIdBuilder.ensureDebugId(teamAttributeGroup, "teamAttributeGroup");
        getDisplay().add(teamAttributeGroup);

        metainfoService.getFullMetaInfo(Arrays.asList(Employee.FQN, OU.FQN, Team.FQN),
                new BasicCallback<List<MetaClass>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(List<MetaClass> value)
                    {
                        for (MetaClass metaClass : value)
                        {
                            ArrayList<AttributeGroup> groups = Lists.newArrayList(metaClass.getAttributeGroups());
                            metainfoUtils.sort(groups);
                            Property<SelectItem> property = Employee.FQN.isSameClass(metaClass.getFqn())
                                    ? emplAttributeGroup
                                    : OU.FQN.isSameClass(metaClass.getFqn()) ? ouAttributeGroup : teamAttributeGroup;
                            SingleSelectCellList<?> sl = property.getValueWidget();
                            for (AttributeGroup grp : groups)
                            {
                                sl.addItem(grp.getTitle(), grp.getCode());
                            }
                        }

                        emplAttributeGroup.trySetObjValue(content.getEmplAttributeGroup());
                        ouAttributeGroup.trySetObjValue(content.getOuAttributeGroup());
                        teamAttributeGroup.trySetObjValue(content.getTeamAttributeGroup());
                    }
                });
    }
}
