/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item;

import java.util.ArrayList;
import java.util.function.Function;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.container.AttributePropertyDescription;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.VMapItemInfoPropertyDescProvider;
import ru.naumen.metainfoadmin.client.escalation.vmap.item.forms.EscalationVMapItemFormMessages;

import com.google.inject.Provider;

/**
 * <AUTHOR>
 * @since 06.11.2012
 *
 */
public class EscalationVMapItemInfoPropertyDescProvider extends
        VMapItemInfoPropertyDescProvider<EscalationValueMapItemContext>
{
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Provider<Property<String>> defaultObject;

    @Inject
    EscalationVMapItemFormMessages escalationMessages;

    @Override
    protected void addSpecialProperties(
            ArrayList<AttributePropertyDescription<?, EscalationValueMapItemContext>> result)
    {
        super.addSpecialProperties(result);
        result.add(new AttributePropertyDescription<String, EscalationValueMapItemContext>(escalationMessages
                .defaultObject(), defaultObject.get(), "defaultObject",
                new Function<EscalationValueMapItemContext, String>()
                {

                    @Override
                    public String apply(EscalationValueMapItemContext input)
                    {
                        if (input == null)
                        {
                            return "";
                        }
                        DtObject defaultObject = input.getCatalogItem().<DtObject> getProperty(
                                ValueMapCatalogItem.DEFAULT_OBJECT);
                        if (defaultObject == null)
                        {
                            return "";
                        }
                        return defaultObject.getTitle();
                    }
                }));
    }
}
