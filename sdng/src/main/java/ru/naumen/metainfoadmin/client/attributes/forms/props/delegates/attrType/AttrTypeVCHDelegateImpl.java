package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attrType;

import static ru.naumen.metainfo.shared.Constants.*;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;

import java.util.Arrays;
import java.util.stream.Collectors;

import java.util.ArrayList;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;
import ru.naumen.metainfo.shared.elements.CommonRestriction;
import ru.naumen.metainfo.shared.elements.HasDateTimeRestriction.RestrictionType;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.RefreshFormVCHDelegateImpl;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат, обрабатывающий изменение значения свойства "Тип атрибута" - вычисляет пару вспомогательных значений
 * и вызывает обновление формы
 * <AUTHOR>
 * @since 09.07.2012
 *
 */
public class AttrTypeVCHDelegateImpl<F extends ObjectForm> extends RefreshFormVCHDelegateImpl<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getPropertyValues().setProperty(AttributeFormPropertyCode.DEFAULT_VALUE, null);
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        boolean notComputable = NOT_COMPUTABLE_ATTRIBUTE_TYPES.contains(attrType);
        if (notComputable)
        {
            context.setProperty(AttributeFormPropertyCode.COMPUTABLE, false);
        }
        boolean notFilteredByScript = !FILTERED_BY_SCRIPT_ATTRIBUTE_TYPES.contains(attrType);
        if (notFilteredByScript)
        {
            context.setProperty(AttributeFormPropertyCode.FILTERED_BY_SCRIPT, false);
        }
        boolean notDeterminable = !SUITABLE_AS_VALUEMAP_TARGET_TYPES.contains(attrType);
        if (notDeterminable)
        {
            context.setProperty(AttributeFormPropertyCode.DETERMINABLE, false);
        }
        boolean editableInLists = EDITABLE_IN_LISTS_ATTRIBUTE_TYPES.contains(attrType);
        context.setPropertyEnabled(AttributeFormPropertyCode.EDITABLE_IN_LISTS, editableInLists);
        if (editableInLists)
        {
            context.setProperty(AttributeFormPropertyCode.EDITABLE_IN_LISTS, false);
        }
        boolean composite = COMPOSITE_ATTR_TYPES.contains(attrType);
        if (!composite)
        {
            context.setProperty(AttributeFormPropertyCode.COMPOSITE, false);
        }
        boolean ableToUseGenRule = ATTR_TYPES_WITH_GEN_RULE.contains(attrType);
        if (!ableToUseGenRule)
        {
            context.setProperty(AttributeFormPropertyCode.USE_GEN_RULE, false);
        }
        boolean ableToBeCompOnEdit = !ATTR_TYPES_WITH_NO_EDIT_SCRIPT.contains(attrType);
        if (!ableToBeCompOnEdit)
        {
            context.setProperty(AttributeFormPropertyCode.COMPUTABLE_ON_FORM, false);
        }
        boolean complexRelation = LINK_ATTRIBUTE_TYPES.contains(attrType);
        if (!complexRelation)
        {
            context.setProperty(AttributeFormPropertyCode.COMPLEX_RELATION, Boolean.FALSE.toString());
        }
        boolean alwaysNotHiddenWhenEmpty = NOT_HIDDEN_WHEN_EMPTY_ATTRIBUTE_TYPES.contains(attrType);
        if (alwaysNotHiddenWhenEmpty)
        {
            context.setProperty(AttributeFormPropertyCode.HIDDEN_WHEN_EMPTY, false);
        }
        boolean canBeHiddenOnEdit = Constants.CAN_HAVE_NO_POSSIBLE_VALUES_ATTRIBUTE_TYPES
                .contains(context.getPropertyValues().getProperty(ATTR_TYPE));
        if (!canBeHiddenOnEdit)
        {
            context.setProperty(AttributeFormPropertyCode.HIDDEN_WHEN_NO_POSSIBLE_VALUES, false);
        }
        boolean canHaveQuickForm = Constants.LINK_ATTRIBUTE_TYPES
                .contains(context.getPropertyValues().getProperty(ATTR_TYPE));
        if (!canHaveQuickForm)
        {
            context.setProperty(AttributeFormPropertyCode.QUICK_ADD_FORM_CODE, null);
            context.setProperty(AttributeFormPropertyCode.QUICK_EDIT_FORM_CODE, null);
        }
        if (DateTimeIntervalAttributeType.CODE.equals(attrType))
        {
            context.setProperty(AttributeFormPropertyCode.NEED_STORE_UNITS, true);
            context.setProperty(AttributeFormPropertyCode.INTERVAL_AVAILABLE_UNITS,
                    Arrays.stream(Interval.values()).map(Enum::name).collect(Collectors.toList()));
        }

        boolean isDateTime = Constants.DATE_TIME_TYPES.contains(attrType);
        context.setEnabled(AttributeFormPropertyCode.DATE_TIME_COMMON_RESTRICTIONS, isDateTime);
        context.setEnabled(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_TYPE, isDateTime);
        context.setEnabled(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_SCRIPT, isDateTime);
        context.setEnabled(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_ATTRIBUTE, isDateTime);
        context.setEnabled(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_CONDITION, isDateTime);
        boolean canHideArchived = COLLECTION_BOLINKS_TYPES
                .contains(context.getPropertyValues().getProperty(ATTR_TYPE));
        context.setEnabled(AttributeFormPropertyCode.HIDE_ARCHIVED, canHideArchived);

        context.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_TYPE, isDateTime
                ? RestrictionType.NO_RESTRICTION.name()
                : null);
        context.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_SCRIPT, null);
        context.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_ATTRIBUTE, null);
        context.setProperty(AttributeFormPropertyCode.DATE_TIME_RESTRICTION_CONDITION, null);
        context.setProperty(AttributeFormPropertyCode.DATE_TIME_COMMON_RESTRICTIONS, isDateTime ? Arrays.asList(
                CommonRestriction.values()) : new ArrayList<>());

        super.onValueChanged(context);
        context.getPropertyControllers().get(AttributeFormPropertyCode.ATTR_TYPE).fireUpdateTabOrderEvent();
    }
}
