package ru.naumen.metainfoadmin.client.structuredobjectsviews.items;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumn;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumnFactory;
import ru.naumen.core.client.widgets.columns.LinkToPlaceWithIndentColumn;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.StructuredObjectsViewItemClient;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.card.StructuredObjectsViewInfoDisplay;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.commands.AddStructuredObjectsViewItemCommand;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.commands.DelStructuredObjectsViewItemCommand;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.commands.EditStructuredObjectsViewItemCommand;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.commands.MoveStructuredObjectsViewItemDownCommand;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.commands.MoveStructuredObjectsViewItemUpCommand;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.commands.StructuredObjectsViewItemMassOperationCommand;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.items.commands.StructuredObjectsViewItemsCommandParam;

/**
 * <AUTHOR>
 * @since 24.10.2019
 */
public class StructuredObjectsViewItemsGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        bind(StructuredObjectsViewItemsCommandFactoryInitializer.class).asEagerSingleton();
        configureCommands();

        //@formatter:off
        bind(new TypeLiteral<TableDisplay<StructuredObjectsViewItemClient>>(){})
                .to(new TypeLiteral<StructuredObjectsViewInfoDisplay<StructuredObjectsViewItemClient>>(){});
        install(new GinFactoryModuleBuilder().implement(new TypeLiteral<LinkToPlaceColumn<StructuredObjectsViewItemClient>>(){},
                new TypeLiteral<LinkToPlaceWithIndentColumn<StructuredObjectsViewItemClient>>(){})
                .build(new TypeLiteral<LinkToPlaceColumnFactory<StructuredObjectsViewItemClient>>(){}));
        //@formatter:on
    }

    protected void configureCommands()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, AddStructuredObjectsViewItemCommand.class)
                .build(new TypeLiteral<CommandProvider<AddStructuredObjectsViewItemCommand, StructuredObjectsViewItemsCommandParam>>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditStructuredObjectsViewItemCommand.class)
                .build(new TypeLiteral<CommandProvider<EditStructuredObjectsViewItemCommand, StructuredObjectsViewItemsCommandParam>>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, DelStructuredObjectsViewItemCommand.class)
                .build(new TypeLiteral<CommandProvider<DelStructuredObjectsViewItemCommand, StructuredObjectsViewItemsCommandParam>>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, MoveStructuredObjectsViewItemUpCommand.class)
                .build(new TypeLiteral<CommandProvider<MoveStructuredObjectsViewItemUpCommand, StructuredObjectsViewItemsCommandParam>>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, MoveStructuredObjectsViewItemDownCommand.class)
                .build(new TypeLiteral<CommandProvider<MoveStructuredObjectsViewItemDownCommand, StructuredObjectsViewItemsCommandParam>>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, StructuredObjectsViewItemMassOperationCommand.class)
                .build(new TypeLiteral<CommandProvider<StructuredObjectsViewItemMassOperationCommand, StructuredObjectsViewItemsCommandParam>>(){}));
        //@formatter:on
    }
}
