/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap;

import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.metainfoadmin.client.catalog.components.CatalogContentCriteriaProviderImpl;

/**
 * <AUTHOR>
 * @since 30.10.2012
 *
 */
public class EscalationVMapContentCriteriaProviderImpl extends
        CatalogContentCriteriaProviderImpl<EscalationValueMapContext>
{
    @Override
    public DtoCriteria create()
    {
        DtoCriteria result = super.create();
        result.getProperties().add(ValueMapCatalogItem.TARGET_ATTRS, ValueMapCatalogItem.TYPE,
                ValueMapCatalogItem.DEFAULT_OBJECT);
        result.addFilters(Filters.eq(ValueMapCatalogItem.TYPE, ValueMapCatalogItem.ESCALATION_TYPE));
        return result;
    }
}