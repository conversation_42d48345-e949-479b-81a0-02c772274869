/**
 *
 */
package ru.naumen.metainfoadmin.client.attributes.columns;

import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.grouplist.WidgetCreator;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Фабрика колонок для списка атрибутов на карточке метакласса
 * <AUTHOR>
 *
 */
public interface AttributeListColumnFactory
{
    /**
     * Получение {@link WidgetCreator} виджета для колонки по коду колонки
     * @param code код колонки
     * @param registrationContainer контейнер для регистрации обработчиков событий
     * @return
     */
    WidgetCreator<Attribute> get(String code, RegistrationContainer registrationContainer);

    /**
     * Получение {@link WidgetCreator} виджета для колонки с кнопкой по коду колонки
     * @param code код колонки
     * @param command команда
     * @param context контекст
     * @return
     */
    WidgetCreator<Attribute> getButtonColumn(String code, BaseCommand<?, ?> command, Context context);
}
