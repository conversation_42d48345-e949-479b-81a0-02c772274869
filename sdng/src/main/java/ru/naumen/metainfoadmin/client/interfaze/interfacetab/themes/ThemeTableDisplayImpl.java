package ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes;

import com.google.gwt.user.cellview.client.CellTable;

import ru.naumen.core.client.table.builder.WithDebugIdCellTableBuilder;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.personalsettings.ThemeClient;
import ru.naumen.metainfoadmin.client.TableDisplayImpl;

/**
 * Дисплей таблицы тем 
 * <AUTHOR>
 * @since 09 авг. 2016 г.
 *
 */
public class ThemeTableDisplayImpl extends TableDisplayImpl<ThemeClient>
{
    public ThemeTableDisplayImpl()
    {
        super(cellTableResource);
        final CellTable<ThemeClient> cellTable = (CellTable<ThemeClient>)getTable();
        cellTable.setTableBuilder(new WithDebugIdCellTableBuilder<ThemeClient>(cellTable, HasCode.PROVIDES_KEY));
        wrapTableInScrollableContainer();
    }
}
