package ru.naumen.metainfoadmin.client.attributes.forms.info;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Создает {@link Property} для отображения информации о
 * представлениях для отображения/редактирования на модальной 
 * форме свойств атрибута 
 *
 * <AUTHOR>
 * @since 30 июл. 2018 г.
 */
public class PresentationAttributeInfoPropCreator extends AbstractAttributeInfoPropCreator
{
    @Inject
    protected PresentationFactories prsFactories;
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    protected void createInt(String code)
    {
        String prsCode = propertyValues.getProperty(code);
        if (StringUtilities.isEmptyTrim(prsCode))
        {
            return;
        }
        createInt(code, prsCode);
    }

    private void createInt(String code, @Nullable String prsCode)
    {
        if (StringUtilities.isEmptyTrim(prsCode))
        {
            return;
        }
        String attrCode = attribute.getType().getCode();
        if (code.equals(AttributeFormPropertyCode.EDIT_PRS) && attribute.getType().isAttributeOfRelatedObject())
        {
            return;
        }
        if (Constants.LINK_ATTRIBUTE_TYPES.contains(attrCode))
        {
            createPropertyForLinkTypes(code, prsCode);
        }
        else
        {
            createProperty(code, getPrsTitle(prsCode));
        }
    }

    private void createPropertyForLinkTypes(String code, String prsCode)
    {
        String fqnAsString = attribute.getType().getProperty(ObjectAttributeType.METACLASS_FQN);
        metainfoService.getMetaClass(ClassFqn.parse(fqnAsString), new BasicCallback<MetaClass>(rs)
        {
            @Override
            protected void handleSuccess(MetaClass metaClass)
            {
                createProperty(code, getPrsTitle(prsCode, getPrsSettings(attribute.getType().getCode(),
                        metaClass)));
            }
        });
        if (Presentations.STRUCTURE_BASED_EDIT_PRS.contains(prsCode))
        {
            bindStructuredObjectsViewProperty(STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE);
        }
    }

    private Map<String, Object> getPrsSettings(String attrCode, MetaClass metaClass)
    {
        if ((BOLinksAttributeType.CODE.equals(attrCode) || BackLinkAttributeType.CODE.equals(attrCode))
            && isSelfNested(metaClass))
        {
            return Presentations.SELF_NESTED_LINK;
        }
        return isWithParent(metaClass) ? Presentations.NESTED_LINK : Presentations.FLAT_LINK;
    }

    private String getPrsTitle(String prsCode)
    {
        return getPrsTitle(prsCode, null);
    }

    private String getPrsTitle(String prsCode, @Nullable Map<String, Object> settings)
    {
        String[] prsCodes = { prsCode };
        String[][] prsTitles = prsFactories.getPresentations(prsCodes, settings);
        return prsTitles[0][0];
    }

    private boolean isSelfNested(MetaClass metaClass)
    {
        if (!metaClass.hasAttribute(ru.naumen.core.shared.Constants.PARENT_ATTR))
        {
            return false;
        }
        ClassFqn fqn = metaClass.getAttribute(ru.naumen.core.shared.Constants.PARENT_ATTR)
                .getType().<ru.naumen.metainfo.shared.elements.ObjectAttributeType> cast()
                .getRelatedMetaClass();
        return metaClass.getFqn().isSameClass(fqn);
    }

    private boolean isWithParent(MetaClass metaClass)
    {
        Collection<String> codes = metaClass.getAttributeFqns().stream()
                .map(HasCode.CODE_EXTRACTOR).collect(Collectors.toList());
        return codes.contains(ru.naumen.core.shared.Constants.PARENT_ATTR);
    }
}
