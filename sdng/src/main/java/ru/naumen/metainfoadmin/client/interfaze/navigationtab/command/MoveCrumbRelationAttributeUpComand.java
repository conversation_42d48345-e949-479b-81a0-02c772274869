package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.CrumbRelationAttribute;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.SaveBreadCrumbAction;

/**
 * Команда изменения порядка связей в крошке (Вверх)
 *
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public class MoveCrumbRelationAttributeUpComand extends BaseCommandImpl<CrumbRelationAttribute,
        DtoContainer<NavigationSettings>>
{
    public static final String ID = MoveCrumbRelationAttributeUpComand.class.getSimpleName();

    @Inject
    private DispatchAsync dispatch;

    @Inject
    public MoveCrumbRelationAttributeUpComand(@Assisted BreadCrumbRelationAttributeCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<CrumbRelationAttribute, DtoContainer<NavigationSettings>> param)
    {
        BreadCrumbRelationAttributeCommandParam p = (BreadCrumbRelationAttributeCommandParam)param;
        Crumb crumb = p.getCrumb();
        CrumbRelationAttribute value = p.getValue();
        int index = crumb.getRelationAttributes().indexOf(value);
        crumb.getRelationAttributes().remove(index);
        crumb.getRelationAttributes().add(index - 1, value);

        SaveBreadCrumbAction action = new SaveBreadCrumbAction(crumb, false);
        dispatch.execute(action, new SimpleResultCallbackDecorator<>(p.getCallbackSafe()));
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof CrumbRelationAttribute) || null == param)
        {
            return false;
        }
        CrumbRelationAttribute item = (CrumbRelationAttribute)input;
        BreadCrumbRelationAttributeCommandParam p = (BreadCrumbRelationAttributeCommandParam)getParam();
        int idx = p.getCrumb().getRelationAttributes().indexOf(item);
        return idx >= 0 && isPossible(idx, p.getCrumb().getRelationAttributes().size());
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.UP;
    }

    protected boolean isPossible(int index, int count)
    {
        return index > 0;
    }
}
