package ru.naumen.metainfoadmin.client.dynadmin.content.embeddedapplications;

import static ru.naumen.metainfo.shared.embeddedapplication.Constants.EMBEDDED_APPLICATION_CONTENT_ID;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.GetEmbeddedApplicationAdminSettingsAction;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;
import ru.naumen.metainfoadmin.client.common.content.AbstractInfoContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;

/**
 * {@link Presenter} для {@link EmbeddedApplication}
 *
 * <AUTHOR>
 * @since 22.08.2016
 */
public class EmbeddedApplicationContentPresenter
        extends AbstractInfoContentPresenter<EmbeddedApplicationContentDisplay, EmbeddedApplicationContent, UIContext>
{

    private String applicationCaption;
    private String applicationStatus;

    @Inject
    private DispatchAsync dispatch;
    @Inject
    private EmbeddedApplicationMessages messages;

    @Inject
    protected EmbeddedApplicationContentPresenter(EmbeddedApplicationContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, EMBEDDED_APPLICATION_CONTENT_ID);
        applicationCaption = StringUtilities.EMPTY;
        applicationStatus = StringUtilities.EMPTY;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        getDisplay().setStatusText(applicationStatus);
    }

    @Override
    protected String getHelpText()
    {
        return messages.applicationHelpText() + " | " + applicationCaption;
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        GetEmbeddedApplicationAdminSettingsAction action =
                new GetEmbeddedApplicationAdminSettingsAction(content.getApplication());
        action.setWithScripts(true);
        dispatch.execute(action, new BasicCallback<EmbeddedApplicationAdminSettingsDto>(context.getReadyState())
        {
            @Override
            public void handleSuccess(EmbeddedApplicationAdminSettingsDto response)
            {

                applicationCaption = response.getDisplayTitle();
                applicationStatus = response.isOn() ? StringUtilities.EMPTY : messages.applicationIsOff();
                if (response.isOn() && !content.isVisible())
                {
                    // в админке может быть только одна причина когда приложение включено и невидимо - silent mode
                    applicationStatus = messages.silentModeIsOn();
                }
                refreshDisplay();
            }
        });
    }
}
