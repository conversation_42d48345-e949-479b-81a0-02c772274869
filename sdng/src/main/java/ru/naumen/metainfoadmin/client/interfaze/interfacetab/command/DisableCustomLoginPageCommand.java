package ru.naumen.metainfoadmin.client.interfaze.interfacetab.command;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataResponse;
import ru.naumen.core.shared.interfacesettings.dispatch.EditCustomLoginFormAction;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContextChangedEvent;

/**
 * Команда выключения пользовательской формы входа в систему
 *
 * <AUTHOR>
 * @since 11.04.2018
 */
public class DisableCustomLoginPageCommand extends BaseCommandImpl<InterfaceSettingsContext, InterfaceSettingsContext>
{
    public static final String ID = "DisableLoginPageCommand";

    @Inject
    private DispatchAsync dispatch;
    @Inject
    private EventBus eventBus;

    @Inject
    public DisableCustomLoginPageCommand(
            @Assisted CommandParam<InterfaceSettingsContext, InterfaceSettingsContext> param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<InterfaceSettingsContext, InterfaceSettingsContext> param)
    {
        dispatch.execute(new EditCustomLoginFormAction(null, false), new BasicCallback<GetInterfaceTabDataResponse>()
        {
            @Override
            protected void handleSuccess(GetInterfaceTabDataResponse response)
            {
                eventBus.fireEvent(new InterfaceSettingsContextChangedEvent(response));
            }
        });
    }

    @Override
    public boolean isPossible(Object input)
    {
        return input instanceof InterfaceSettingsContext
               && (((InterfaceSettingsContext)input).getSettings().getCustomLoginPageSettings().isEnabled());
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }
}
