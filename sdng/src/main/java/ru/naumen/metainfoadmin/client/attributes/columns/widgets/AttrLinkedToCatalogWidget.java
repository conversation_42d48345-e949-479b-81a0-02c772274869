package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TITLE_SEPARATOR;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.utils.CIUtils;
import ru.naumen.metainfo.client.CatalogPlace;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfoadmin.client.AdminCachedMetainfoService;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;

/**
 * Для атрибутов типа "Элемент справочника" и "Набор элементов справочника" 
 * отображается название справочника, который используется в атрибуте. 
 * Название справочника — ссылка на его карточку.
 * Формат: "Справочник %Название справочника%".
 *
 * <AUTHOR>
 * @since 1 авг. 2018 г.
 *
 */
public class AttrLinkedToCatalogWidget extends AttrTypeColumnWidgetWithReadyState
{
    private final static List<String> ATTR_TYPES = Arrays.asList(
            Constants.CatalogItemAttributeType.CODE,
            Constants.CatalogItemsAttributeType.CODE
    );

    private AdminCachedMetainfoService adminMetainfoService;
    private AttributesMessages messages;

    @Inject
    public AttrLinkedToCatalogWidget(AdminCachedMetainfoService metainfoService, AttributesMessages messages)
    {
        this.adminMetainfoService = metainfoService;
        this.messages = messages;
    }

    @Override
    public IsWidget createWidget(Attribute attr)
    {
        final FlowPanel result = new FlowPanel();
        final String catalogFqn = attr.getType().getProperty(Constants.ObjectAttributeType.METACLASS_FQN);
        final String catalogCode = CIUtils.getCatalogCode(catalogFqn);

        adminMetainfoService.getCatalog(ClassFqn.parse(catalogFqn), new BasicCallback<DtoContainer<Catalog>>(rs)
        {
            @Override
            protected void handleSuccess(DtoContainer<Catalog> catalog)
            {
                result.add(new InlineLabel(messages.catalog() + TITLE_SEPARATOR));
                Anchor anchor = new Anchor(catalog.get().getTitle(), false,
                        AttrWidgetsHelper.createLink(CatalogPlace.PLACE_PREFIX, catalogCode));
                result.add(anchor);
                anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
            }
        });

        return result;
    }

    @Override
    public List<String> listAllowedAttrTypes()
    {
        return ATTR_TYPES;
    }
}