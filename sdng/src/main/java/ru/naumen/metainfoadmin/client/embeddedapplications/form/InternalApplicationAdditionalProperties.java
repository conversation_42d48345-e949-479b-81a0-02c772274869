package ru.naumen.metainfoadmin.client.embeddedapplications.form;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfo.shared.embeddedapplication.InternalApplication;

/**
 * Дополняет презентер формы приложения свойствами, специфичными для внутреннего приложения
 *
 * <AUTHOR>
 * @since 28.10.16
 */
public class InternalApplicationAdditionalProperties extends ApplicationWithScriptPropertiesProvider
        implements AdditionalApplicationPropertiesProvider
{
    @Inject
    protected CommonMessages cmessages;

    @Override
    public void addAdditionalProperties(PropertyDialogDisplay display, PropertyRegistration<?> previousProperty,
            RegistrationContainer registrationContainer)
    {
        if (showScript())
        {
            super.addAdditionalProperties(display, previousProperty, registrationContainer);
        }
    }

    @Override
    public EmbeddedApplication createApplication()
    {
        InternalApplication internalApplication = new InternalApplication();
        internalApplication.setApplicationType(EmbeddedApplicationType.InternalApplication);
        return internalApplication;
    }

    @Override
    public void fillAdditionalProperties(EmbeddedApplicationAdminSettingsDto embeddedApplication)
    {
        if (embeddedApplication.getEmbeddedApplicationType() == getApplicationType()
            && showScript())
        {
            super.fillAdditionalProperties(embeddedApplication);
        }
    }

    @Override
    public Boolean getValueOfAdditionalProperties(EmbeddedApplicationAdminSettingsDto embeddedApplication)
    {
        if (!validation.validate())
        {
            return false;
        }
        if (showScript())
        {
            super.getValueOfAdditionalProperties(embeddedApplication);
        }
        return true;
    }

    @Override
    public void removeAdditionalProperties()
    {
        if (showScript())
        {
            super.removeAdditionalProperties();
        }
    }

    protected boolean showScript()
    {
        return true;
    }

    /**
     * Наследники InternalApplicationAdditionalProperties не имеют тип InternalApplication,
     * но для них должно выполняться условие.
     */
    protected EmbeddedApplicationType getApplicationType()
    {
        return EmbeddedApplicationType.InternalApplication;
    }
}
