package ru.naumen.metainfoadmin.client.templates.list.card;

import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.ContentTemplate.CONTENT_PERMISSIONS;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.SimpleEventBus;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.components.block.TitledBlockDisplay;
import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.ContextualCallback;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ListTemplate;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.DefaultContext;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListContentDisplayImpl;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMessages;
import ru.naumen.metainfoadmin.shared.dynadmin.GetEditToolPanelContextInfoAction;
import ru.naumen.metainfoadmin.shared.dynadmin.GetEditToolPanelContextInfoResponse;

/**
 * Блок "Настройка списка" на карточке.
 * <AUTHOR>
 * @since 20.04.2018
 */
public class ListTemplateSettingPresenter extends BasicPresenter<TitledBlockDisplay>
{
    @Inject
    private ListTemplatesMessages messages;
    @Inject
    private AdminContentFactory factory;
    @Inject
    private MetainfoServiceAsync metainfoService;
    @Inject
    private DispatchAsync dispatch;

    private String uuid;
    private ObjectListBase list;
    private ObjectListContentDisplayImpl presenterDisplay;
    private PermissionHolder permissions = new PermissionHolder();

    @Inject
    public ListTemplateSettingPresenter(TitledBlockDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        display.asWidget().ensureDebugId("settings");
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        if (presenterDisplay != null)
        {
            presenterDisplay.removeFromParent();
        }
        bindContent();
    }

    public void setTemplate(DtObject template)
    {
        list = template.getProperty(ListTemplate.TEMPLATE);
        list.setShowCaption(false);
        uuid = template.getUUID();
        permissions = template.getProperty(CONTENT_PERMISSIONS, new PermissionHolder());
        refreshDisplay();
    }

    protected void bindContent()
    {
        metainfoService.getMetaClass(list.getFqnOfClass(), new BasicCallback<MetaClass>()
        {
            @Override
            public void handleSuccess(MetaClass metaClass)
            {
                ObjectListUIContext context = new ObjectListUIContext(new DefaultContext(metaClass),
                        new ContentInfo(metaClass.getFqn(), Constants.UI.WINDOW_KEY, list), true, list,
                        new SimpleEventBus());
                context.setPermissions(permissions);
                context.setTemplateCode(uuid);
                if (list.getToolPanel().isUseSystemSettings() && list.getToolPanel().getTools().isEmpty())
                {
                    fillAndBuildContent(context);
                }
                else
                {
                    buildContent(context);
                }
            }
        });
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaption(messages.settingList());
    }

    private void buildContent(ObjectListUIContext context)
    {
        factory.build(list, context, new ContextualCallback<ContentPresenter<Content, UIContext>>(context, getDisplay())
        {
            @Override
            protected void handleSuccess(ContentPresenter<Content, UIContext> presenter)
            {
                presenterDisplay = (ObjectListContentDisplayImpl)presenter.getDisplay();
                presenterDisplay.addStyleName(AdminWidgetResources.INSTANCE.contentLayout().editableBlockHover());
                presenterDisplay.setCaptionVisible(false);
                getDisplay().setControlledWidget(presenterDisplay);

                if (isRevealed)
                {
                    presenter.revealDisplay();
                }
            }
        });
    }

    private void fillAndBuildContent(ObjectListUIContext context)
    {
        GetEditToolPanelContextInfoAction action = new GetEditToolPanelContextInfoAction(list.getFqnOfClass(),
                UI.WINDOW_KEY, null, ToolPanelKind.ACTION_BAR);
        action.setTemplateCode(uuid);
        dispatch.execute(action, new BasicCallback<GetEditToolPanelContextInfoResponse>(getDisplay())
        {
            @Override
            protected void handleSuccess(GetEditToolPanelContextInfoResponse response)
            {
                ToolPanel hardcodedToolPanel = response.getHardcodedToolPanel();
                for (ToolBar toolBar : hardcodedToolPanel.getToolBars())
                {
                    toolBar.setParent(list.getToolPanel());
                    list.getToolPanel().getToolBars().add(toolBar);
                }
                buildContent(context);
            }
        });
    }
}
