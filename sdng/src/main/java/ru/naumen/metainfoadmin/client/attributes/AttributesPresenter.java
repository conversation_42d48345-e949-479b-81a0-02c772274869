package ru.naumen.metainfoadmin.client.attributes;

import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

import com.google.common.collect.Multimap;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.debug.client.DebugInfo;
import com.google.gwt.dom.client.Document;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.DoubleClickEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.Window;
import com.google.inject.Provider;
import com.google.inject.name.Named;

import jakarta.inject.Inject;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.MainContentDisplayImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextChangedEvent;
import ru.naumen.core.client.content.ContextChangedHandler;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonUtils;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.menu.ShowNavTreeEvent;
import ru.naumen.core.client.menu.ShowNavTreeEventHandler;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.AttributeListCss;
import ru.naumen.core.client.widgets.ExpandCollapseEvent;
import ru.naumen.core.client.widgets.ExpandCollapseEventHandler;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.ResetAttributeAction.Type;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.AdminCachedMetainfoService;
import ru.naumen.metainfoadmin.client.CommonUtils;
import ru.naumen.metainfoadmin.client.attributes.AttributeList.GroupCodes;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributesListType;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnInfoFactory;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandCode;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributePresenterFactory;

/**
 * {@link Presenter} для списка атрибутов класса
 *
 * <AUTHOR>
 */
public class AttributesPresenter extends BasicPresenter<AttributesDisplay<ExtendedAttributeListCustomizer>>
        implements ContextChangedHandler<Context>, ShowNavTreeEventHandler
{
    //Коды столбцов в том порядке, в каком они будут добавляться в дисплей
    // @formatter:off
    final public static String[] COLUMNS = new String[] {
            AttributeColumnCode.TITLE_CODE_TYPE,
            AttributeColumnCode.EDITABLE,
            AttributeColumnCode.EDITABLE_IN_LISTS,
            AttributeColumnCode.REQUIRED,
            AttributeColumnCode.REQUIRED_IN_INTERFACE,
            AttributeColumnCode.LINKED_TO,
            AttributeColumnCode.DETERMINED_BY,
            AttributeColumnCode.FILTERING_ON_EDIT,
            AttributeColumnCode.CALCULATING_ON_EDIT,
            AttributeColumnCode.DEFAULT_VALUE,
            AttributeColumnCode.HIDE_EMPTY,
            AttributeColumnCode.HIDE_NO_VALUE_TO_SELECT
    };
    public final static List<String> DOES_NOT_SHOW_IN_COMPACT_MODE = Arrays.asList(
            AttributeColumnCode.FILTERING_ON_EDIT,
            AttributeColumnCode.CALCULATING_ON_EDIT,
            AttributeColumnCode.HIDE_EMPTY,
            AttributeColumnCode.HIDE_NO_VALUE_TO_SELECT
    );

    private static String ANCHOR_TAG = "A";
    // @formatter:on
    private static int HIDE_SHOW_LEFT_PANEL_TIME = 500;

    /**
     * Ширина клиентской части списка атрибутов, переход через которую
     * вызывает смену видов: обычный/компактный
     */
    private static int CHANGE_VIEW_TRIGGER = 1300;

    /**
     * Корекция ширины клиентской области таблицы относительно MainContent.
     * Необходимо при вычислении ширины таблицы до того, как она добавлена в DOM
     * Получена эмпирически.
     */
    private static int PARENT_WIDTH_CORRECTION = -54;

    private static final String MAIN_CONTENT_DEBUG_ID = DebugInfo.DEFAULT_DEBUG_ID_PREFIX +
                                                        MainContentDisplayImpl.MAIN_CONTENT_ID;
    protected HandlerRegistration handlerRegistration;
    protected int attrListWidth = 0;
    @Inject
    MetainfoUtils metainfoUtils;
    @Inject
    MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    CommonUtils commonUtils;
    @Inject
    AttributesMessages messages;
    @Inject
    CommonMessages cmessages;
    @Inject
    Dialogs dialogs;
    @Inject
    ButtonFactory buttonFactory;
    @Inject
    @Named(AttributesListType.EXTENDED)
    AttributeListColumnInfoFactory columnInfoFactory;
    @Inject
    AttributePresenterFactory<ObjectFormAdd> addAttributePresenterFactory;
    Multimap<String, ButtonPresenter<MetaClass>> buttons = CollectionUtils.newMultimap();
    @Inject
    private Provider<AttributeInfoPresenter> attrributeInfoPresenterProvider;
    @Inject
    private ButtonUtils buttonUtils;
    @Inject
    private AdminCachedMetainfoService metainfoCacheService;

    private Context context;
    /**
     * Возникла необходимость отображать некоторые кнопки непосредственно в заголовке таблицы,
     * а не в ToolPanel. Причем кнопки могут скрываться/отображаться в зависимости
     * от текущего состояния списка (наличия/отсутствия "прилипшего" заголовка).
     * Данная Map'а содержит эти кнопки.
     *
     * Возможно, щас этот функционал не нужен.
     * Т.к. кнопка Добавить атрибут - управляется стилями.
     * TODO выпилить или снять deprecated
     */
    @Deprecated
    private Map<String, ButtonPresenter<?>> buttonsManagedByAttrList = new HashMap<>();
    private boolean compact;

    @Inject
    AttributesPresenter(AttributesDisplay<ExtendedAttributeListCustomizer> display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void hideDisplay()
    {
        destroyTimer();
        super.hideDisplay();
    }

    public void init(Context context)
    {
        this.context = context;
        getDisplay().setParentContext(context);
    }

    @Override
    public void onContextChanged(ContextChangedEvent<Context> e)
    {
        if (isRevealed)
        {
            refreshDisplay();
        }
    }

    @Override
    public void onShowNavTree(ShowNavTreeEvent event)
    {
        getDisplay().getTable().setHeaderVisible(false);
        final Timer t = new Timer()
        {
            @Override
            public void run()
            {
                getDisplay().getTable().setHeaderVisible(true);
                getDisplay().getTable().syncWidth();
            }
        };
        t.schedule(HIDE_SHOW_LEFT_PANEL_TIME);
    }

    @Override
    public void refreshDisplay()
    {
        getDisplay().getTable().refresh(context.getMetainfo());
        getDisplay().setEditable(isEditable());

        for (ButtonPresenter<MetaClass> p : buttons.values())
        {
            p.refreshDisplay(context.getMetainfo());
        }
    }

    @Override
    public void revealDisplay()
    {
        if (handlerRegistration != null)
        {
            handlerRegistration.removeHandler();
        }
        handlerRegistration = Window.addResizeHandler(resizeEvent ->
        {
            if (getDisplay().getTable() instanceof AttributeListImpl)
            {
                ((AttributeListImpl)getDisplay().getTable()).clearHeaderWidth();
            }

            Scheduler.get().scheduleFinally(() ->
            {
                int newWidth = getWidth();
                if (attrListWidth < CHANGE_VIEW_TRIGGER && newWidth >= CHANGE_VIEW_TRIGGER
                    || attrListWidth >= CHANGE_VIEW_TRIGGER && newWidth < CHANGE_VIEW_TRIGGER)
                {
                    attrListWidth = newWidth;
                    bindColumns();
                    refreshDisplay();
                }
                getDisplay().getTable().syncWidth();
            });
        });
        super.revealDisplay();
    }

    protected void bindColumns()
    {
        if (attrListWidth == 0)
        {
            attrListWidth = getWidth() + PARENT_WIDTH_CORRECTION;
        }

        setCompact(attrListWidth < CHANGE_VIEW_TRIGGER);
        columnInfoFactory.reInit(isCompact());
        getDisplay().getTable().clearAll();

        AttributeList table = getDisplay().getTable();
        for (String code : COLUMNS)
        {
            if (!(isCompact() && DOES_NOT_SHOW_IN_COMPACT_MODE.contains(code)))
            {
                table.addColumn(columnInfoFactory.create(code, registrationContainer));
            }
        }

        //Первые два = null, т.к. соотв. атрибут подставляется в параметр при нажатии на toolDisplay,
        //а callback - потому что обратная связь идет через eventBus
        AttributeCommandParam param = new AttributeCommandParam(null, null, context);

        table.addColumn(columnInfoFactory.createCommandColumn(
                AttributeColumnCode.EDIT_BUTTON, param, AttributeCommandCode.EDIT));
        table.addColumn(columnInfoFactory.createCommandColumn(
                AttributeColumnCode.DELETE_BUTTON, param, AttributeCommandCode.DELETE));
    }

    /**
     *
     */
    protected void bindTitle()
    {
        if (context.getMetainfo().getFqn().isCase())
        {
            getDisplay().setCaption(messages.caseAtributes());
        }
        else
        {
            getDisplay().setCaption(messages.classAtributes());
        }

        if (getDisplay().getTable() instanceof ExpandCollapseEventHandler)
        {
            registerHandler(eventBus.addHandler(ExpandCollapseEvent.TYPE, (ExpandCollapseEventHandler)getDisplay()
                    .getTable()));
        }
    }

    protected void destroyTimer()
    {
        if (handlerRegistration != null)
        {
            handlerRegistration.removeHandler();
            handlerRegistration = null;
        }
    }

    /**
     * Получение ширины родительского элемента
     */
    protected int getParentWidth()
    {
        Element parent = getDisplay().asWidget().getElement().getParentElement();
        if (null == parent)
        {
            //Метод может быть вызван до добавления элемента в документ.
            //В этом случае берем MainContent.
            parent = Document.get().getElementById(MAIN_CONTENT_DEBUG_ID);
        }
        return parent == null ? 0 : parent.getOffsetWidth();
    }

    /**
     * Получение ширины видимой области
     */
    protected int getWidth()
    {
        return getParentWidth() - getWindowScrollRight();
    }

    protected boolean isEditable()
    {
        return metainfoUtils.isPossible(context.getMetainfo(), true) || context.getMetainfo().isAbstract();
    }

    @Override
    protected void onBind()
    {
        getDisplay().asWidget().addStyleName(AdminWidgetResources.INSTANCE.attributeList().attrList());
        bindTitle();
        generateButtons();
        initToolPanel();
        getDisplay().getTable().setManagedButtons(buttonsManagedByAttrList);
        bindColumns();
        registerHandler(context.getEventBus().addHandler(ContextChangedEvent.getType(), this));
        registerClickHandler();
        addHandler(ShowNavTreeEvent.getType(), this);
        attrListWidth = getWidth();
    }

    protected void onInherit(Type type)
    {
        metainfoModificationService.resetAttribute(context.getMetainfo(), type,
                new BasicCallback<MetaClass>(getDisplay()));
    }

    @Override
    protected void onUnbind()
    {
        destroyTimer();
        // инвалидируем кэш метаинфы, чтобы не держать в памяти информацию, загруженную при построении списка атрибутов
        metainfoCacheService.invalidateCache();
        super.onUnbind();
    }

    private void addButton(String group, String btnCode, String title, ClickHandler handler,
            Predicate<MetaClass> filter)
    {
        addButton(group, btnCode, title, handler, filter, false);
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    private void addButton(String group, String btnCode, String title, ClickHandler handler,
            Predicate<MetaClass> filter, boolean isManagedByAttributeList)
    {
        ButtonPresenter btn = buttonFactory.create(btnCode, title, handler);
        btn.getDisplay().asWidget().ensureDebugId(group + "-" + btnCode);
        btn.addPossibleFilter(filter);
        registerChildPresenter(btn);
        getDisplay().addButton(group, (ButtonToolDisplay)btn.getDisplay());
        if (isManagedByAttributeList)
        {
            buttonsManagedByAttrList.put(btnCode, btn);
        }
        buttons.put(group, btn);

    }

    private void bindAttributeInfoPresenter(Attribute attribute)
    {
        AttributeInfoPresenter presenter = attrributeInfoPresenterProvider.get();
        presenter.setAttribute(attribute);
        presenter.setContext(context);
        presenter.bind();
    }

    private void generateButtons()
    {
        Predicate<MetaClass> showResetButtonsPredicate = buttonUtils.<MetaClass> isShowResetButtonsPredicate()
                .and(metaClass -> hasPermission(context, PermissionType.EDIT, metaClass));
        addButton(GroupCodes.SYSTEM, ButtonCode.REFRESH, cmessages.resetSystemSettings(), new ClickHandler()
        {
            @Override
            public void onClick(ClickEvent event)
            {
                dialogs.question(cmessages.confirmResetSettings(),
                        commonUtils.getConfirmResetQuestion(cmessages.ofSystemAttr(), context.getMetainfo().getFqn()),
                        new DialogCallback()
                        {
                            @Override
                            protected void onYes(Dialog dialog)
                            {
                                dialog.hide();
                                onInherit(Type.SYSTEM);
                            }
                        });
            }
        }, showResetButtonsPredicate);
        addButton(GroupCodes.FLEX, ButtonCode.REFRESH, cmessages.resetFlexSettings(), new ClickHandler()
        {
            @Override
            public void onClick(ClickEvent event)
            {
                dialogs.question(cmessages.confirmResetSettings(),
                        commonUtils.getConfirmResetQuestion(cmessages.ofFlexes(), context.getMetainfo().getFqn()),
                        new DialogCallback()
                        {
                            @Override
                            protected void onYes(Dialog dialog)
                            {
                                dialog.hide();
                                onInherit(Type.FLEX);
                            }
                        });
            }
        }, ((Predicate<MetaClass>)input -> input.getFqn().isCase()).and(showResetButtonsPredicate));

        addButton(GroupCodes.FLEX, ButtonCode.ADD, cmessages.add(), new ClickHandler()
        {
            @Override
            public void onClick(ClickEvent event)
            {
                addAttributePresenterFactory.create(context).bind();
            }
        }, metaClass -> true);
    }

    /**
     * Получение ширины интерфейса, скрытой за правой границей экрана
     */
    private native int getWindowScrollRight()
    /*-{
        var windowWidth;
        if ($doc.documentElement && $doc.documentElement.clientWidth)
        {
            windowWidth = $doc.documentElement.clientWidth;
        }
        else
        {
            windowWidth = $doc.body.clientWidth;
        }
        return $doc.body.scrollWidth - windowWidth;
    }-*/;

    private void initToolPanel()
    {
        ToolBarDisplayMediator<MetaClass> toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
        for (ButtonPresenter<MetaClass> button : buttons.values())
        {
            if (!buttonsManagedByAttrList.containsValue(button))
            {
                toolBar.add(button);
            }
        }
        toolBar.bind();
    }

    private boolean isCompact()
    {
        return compact;
    }

    /**
     * Выделяет / убирает выделение текстового содержимого переданного элемента.
     *
     * @param elem элемент, текстовое содержимое которого нужно выделить / убрать выделение
     */
    private native void markText(Element elem)
    /*-{
        var text = "";
        if ($wnd.getSelection)
        {
            text = $wnd.getSelection().toString();
        }
        else if ($doc.selection && $doc.selection.type != "Control")
        {
            text = $doc.selection.createRange().text;
        }
        if (text == elem.textContent)
        {
            if ($wnd.getSelection)
            {
                $wnd.getSelection().removeAllRanges();
            }
            else if ($doc.selection)
            {
                $doc.selection.empty();
            }
        }
        else
        {
            if ($doc.selection && $doc.selection.createRange)
            {
                var range = $doc.selection.createRange();
                range.moveToElementText(elem);
                range.select();
            }
            else if ($doc.createRange && $wnd.getSelection)
            {
                var range = $doc.createRange();
                if(elem.childNodes.length > 0 && elem.childNodes[0].nodeType == Node.TEXT_NODE)
                {
                    elem = elem.childNodes[0];
                    var content = elem.textContent || elem.innerText;
                    range.setStart(elem, 0);
                    range.setEnd(elem, content.length);
                }
                else
                {
                    range.selectNode(elem);
                }
                var selection = $wnd.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
            }
        }
    }-*/;

    private boolean needMarkText(Element element)
    {
        AttributeListCss style = AdminWidgetResources.INSTANCE.attributeList();
        return element.hasClassName(style.codeBadge())
               || element.hasClassName(style.titleBadge())
               || element.hasClassName(style.typeBadge());
    }

    private void registerClickHandler()
    {
        registerHandler(display.asWidget().addDomHandler(event ->
        {
            Element element = Element.as(event.getNativeEvent().getEventTarget());
            if (needMarkText(element))
            {
                markText(element);
            }
            else if (!ANCHOR_TAG.equalsIgnoreCase(element.getTagName()))
            {
                display.getValueForClickEvent(event).ifPresent(this::bindAttributeInfoPresenter);
            }
        }, ClickEvent.getType()));

        registerHandler(display.asWidget().addDomHandler(event ->
        {
            Element element = Element.as(event.getNativeEvent().getEventTarget());
            if (needMarkText(element))
            {
                markText(element);
            }
        }, DoubleClickEvent.getType()));
    }

    private void setCompact(boolean compact)
    {
        this.compact = compact;
    }
}