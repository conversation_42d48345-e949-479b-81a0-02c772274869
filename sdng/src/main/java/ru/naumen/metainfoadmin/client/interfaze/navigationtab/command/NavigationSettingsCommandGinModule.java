package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.CrumbRelationAttribute;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelElementWrapper;

/**
 * <AUTHOR>
 * @since Mar 29, 2013
 */
public class NavigationSettingsCommandGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        bind(NavigationSettingsCommandFactoryInitializer.class).asEagerSingleton();

        //@formatter:off
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditNavigationSettingsCommand, CommandParam<DtoContainer<NavigationSettings>, DtoContainer<NavigationSettings>>>>(){},
                new TypeLiteral<ClosureCommand<DtoContainer<NavigationSettings>>>(){},
                new TypeLiteral<EditNavigationSettingsCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<MoveTopMenuItemUpCommand, NavigationSettingsTMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<MenuItem>>(){},
                new TypeLiteral<MoveTopMenuItemUpCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<MoveTopMenuItemDownCommand, NavigationSettingsTMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<MenuItem>>(){},
                new TypeLiteral<MoveTopMenuItemDownCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<AddTopMenuItemCommand, NavigationSettingsTMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<MenuItem>>(){},
                new TypeLiteral<AddTopMenuItemCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditTopMenuItemCommand, NavigationSettingsTMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<MenuItem>>(){},
                new TypeLiteral<EditTopMenuItemCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<DeleteTopMenuItemCommand, NavigationSettingsTMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<MenuItem>>(){},
                new TypeLiteral<DeleteTopMenuItemCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EnableTopMenuItemCommand, NavigationSettingsTMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<MenuItem>>(){},
                new TypeLiteral<EnableTopMenuItemCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<DisableTopMenuItemCommand, NavigationSettingsTMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<MenuItem>>(){},
                new TypeLiteral<DisableTopMenuItemCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<AddCrumbCommand, BreadCrumbCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<Crumb>>(){},
                new TypeLiteral<AddCrumbCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditCrumbCommand, BreadCrumbCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<Crumb>>(){}, 
                new TypeLiteral<EditCrumbCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<DeleteCrumbCommand, BreadCrumbCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<Crumb>>(){}, 
                new TypeLiteral<DeleteCrumbCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<MoveCrumbRelationAttributeUpComand, BreadCrumbRelationAttributeCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<CrumbRelationAttribute>>(){}, 
                new TypeLiteral<MoveCrumbRelationAttributeUpComand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<MoveCrumbRelationAttributeDownComand, BreadCrumbRelationAttributeCommandParam>>(){}, 
                new TypeLiteral<ClosureCommand<CrumbRelationAttribute>>(){}, 
                new TypeLiteral<MoveCrumbRelationAttributeDownComand>(){}));

        install(Gin.install(
                new TypeLiteral<CommandProvider<MoveLeftMenuItemUpCommand, NavigationSettingsLMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<LeftMenuItemSettingsDTO>>(){},
                new TypeLiteral<MoveLeftMenuItemUpCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<MoveLeftMenuItemDownCommand, NavigationSettingsLMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<LeftMenuItemSettingsDTO>>(){},
                new TypeLiteral<MoveLeftMenuItemDownCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<AddLeftMenuItemCommand, NavigationSettingsLMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<LeftMenuItemSettingsDTO>>(){},
                new TypeLiteral<AddLeftMenuItemCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditLeftMenuItemCommand, NavigationSettingsLMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<LeftMenuItemSettingsDTO>>(){},
                new TypeLiteral<EditLeftMenuItemCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<DeleteLeftMenuItemCommand, NavigationSettingsLMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<LeftMenuItemSettingsDTO>>(){},
                new TypeLiteral<DeleteLeftMenuItemCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EnableLeftMenuItemCommand, NavigationSettingsLMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<LeftMenuItemSettingsDTO>>(){},
                new TypeLiteral<EnableLeftMenuItemCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<DisableLeftMenuItemCommand, NavigationSettingsLMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<LeftMenuItemSettingsDTO>>(){},
                new TypeLiteral<DisableLeftMenuItemCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<PinLeftMenuItemCommand, NavigationSettingsLMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<LeftMenuItemSettingsDTO>>(){},
                new TypeLiteral<PinLeftMenuItemCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<UnpinLeftMenuItemCommand, NavigationSettingsLMCommandParam>>(){},
                new TypeLiteral<ClosureCommand<LeftMenuItemSettingsDTO>>(){},
                new TypeLiteral<UnpinLeftMenuItemCommand>(){}));

        install(Gin.install(
                new TypeLiteral<CommandProvider<AddQuickAccessTileCommand, QuickAccessPanelTileCommandParam>>(){},
                new TypeLiteral<ClosureCommand<QuickAccessPanelElementWrapper>>(){},
                new TypeLiteral<AddQuickAccessTileCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EditQuickAccessTileCommand, QuickAccessPanelTileCommandParam>>(){},
                new TypeLiteral<ClosureCommand<QuickAccessPanelElementWrapper>>(){},
                new TypeLiteral<EditQuickAccessTileCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<DeleteQuickTileCommand, QuickAccessPanelTileCommandParam>>(){},
                new TypeLiteral<ClosureCommand<QuickAccessPanelElementWrapper>>(){},
                new TypeLiteral<DeleteQuickTileCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<MoveQuickAccessTileUpCommand, QuickAccessPanelTileCommandParam>>(){},
                new TypeLiteral<ClosureCommand<QuickAccessPanelElementWrapper>>(){},
                new TypeLiteral<MoveQuickAccessTileUpCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<MoveQuickAccessTileDownCommand, QuickAccessPanelTileCommandParam>>(){},
                new TypeLiteral<ClosureCommand<QuickAccessPanelElementWrapper>>(){},
                new TypeLiteral<MoveQuickAccessTileDownCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<EnableQuickAccessTileCommand, QuickAccessPanelTileCommandParam>>(){},
                new TypeLiteral<ClosureCommand<QuickAccessPanelElementWrapper>>(){},
                new TypeLiteral<EnableQuickAccessTileCommand>(){}));
        install(Gin.install(
                new TypeLiteral<CommandProvider<DisableQuickAccessTileCommand, QuickAccessPanelTileCommandParam>>(){},
                new TypeLiteral<ClosureCommand<QuickAccessPanelElementWrapper>>(){},
                new TypeLiteral<DisableQuickAccessTileCommand>(){}));
        //@formatter:on
    }
}
