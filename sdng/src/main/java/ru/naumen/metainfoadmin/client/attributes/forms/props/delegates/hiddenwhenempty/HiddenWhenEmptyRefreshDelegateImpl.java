package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hiddenwhenempty;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.ATTR_TYPE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.REQUIRED;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.SHOW_PRS;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.BooleanAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Делегат обновления свойства "Скрывать, если не заполнен".
 * <AUTHOR>
 * @since Jul 14, 2017
 */
public class HiddenWhenEmptyRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Inject
    private AttributesMessages messages;

    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        if (isDisabledForSpecificAttrs(context))
        {
            callback.onSuccess(false);
            return;
        }

        boolean isRequired = Boolean.TRUE.equals(context.getPropertyValues().getProperty(REQUIRED));
        String attrType = context.getPropertyValues().getProperty(ATTR_TYPE);
        String showPresentation = context.getPropertyValues().getProperty(SHOW_PRS);
        boolean alwaysShow = Constants.NOT_HIDDEN_WHEN_EMPTY_ATTRIBUTE_TYPES.contains(attrType);
        String caption = !BooleanAttributeType.CODE.equals(attrType) ? messages.hideWhenEmpty()
                : (Presentations.BOOL_ONE_ZERO.equals(showPresentation) ? messages.hideWhenZero()
                        : messages.hideWhenNo());
        property.setCaption(caption);
        callback.onSuccess(!isRequired && !alwaysShow);
    }

    private boolean isDisabledForSpecificAttrs(PropertyContainerContext context)
    {
        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        String attrCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE);
        if (SuperUser.FQN.isSameClass(metaClass.getFqn())
            && (SuperUser.PERMISSION_EXPIRATION_DATE.equals(attrCode) || SuperUser.UUID.equals(attrCode)))
        {
            return true;
        }

        return false;
    }
}
