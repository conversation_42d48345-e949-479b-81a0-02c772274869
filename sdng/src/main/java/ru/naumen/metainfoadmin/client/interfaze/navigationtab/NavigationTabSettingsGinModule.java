package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import java.util.Arrays;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import java.util.HashMap;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.ResultProfilesDTO.ResultProfilesType;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO.MenuItemFormatting;
import ru.naumen.core.shared.navigationsettings.menu.MenuItemTypeOfCard;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelElementWrapper;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfo.shared.homepage.HomePageType;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb.BreadCrumbGinModule;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.NavigationSettingsCommandGinModule;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.HomePageGinModule;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.menuitem.NavigationMenuItemGinModule;

/**
 * <AUTHOR>
 * @since Mar 29, 2013
 */
public class NavigationTabSettingsGinModule extends AbstractGinModule
{
    public static class MenuTypeTitlesProvider implements Provider<Map<MenuItemType, String>>
    {
        @Inject
        private NavigationSettingsMessages messages;

        @Override
        public Map<MenuItemType, String> get()
        {
            Map<MenuItemType, String> result = new EnumMap<>(MenuItemType.class);
            result.put(MenuItemType.addButton, messages.addButtonMenuType());
            result.put(MenuItemType.chapter, messages.chapterMenuType());
            result.put(MenuItemType.favorites, messages.favoritesMenuType());
            result.put(MenuItemType.history, messages.historyMenuType());
            result.put(MenuItemType.reference, messages.referenceMenuType());
            result.put(MenuItemType.company, messages.companyMenuType());
            result.put(MenuItemType.linkToContent, messages.linkToContentMenuType());
            result.put(MenuItemType.customLink, messages.customLinkMenuType());
            result.put(MenuItemType.customButton, messages.customButtonMenuType());
            return result;
        }
    }

    /**
     * Провайдер типов домашней страницы, образует связь между {@link HomePageType видом домашней страницы} и
     * {@link NavigationSettingsMessages её названием}
     */
    public static class HomePageTypeProvider implements Provider<Map<HomePageType, String>>
    {
        @Inject
        private NavigationSettingsMessages messages;

        @Override
        public Map<HomePageType, String> get()
        {
            Map<HomePageType, String> result = new EnumMap<>(HomePageType.class);
            result.put(HomePageType.REFERENCE, messages.referenceMenuType());
            result.put(HomePageType.CUSTOM_LINK, messages.customLinkMenuType());
            return result;
        }
    }

    /**
     * Провайдер типов домашней страницы, образует связь между {@link HomePageType видом домашней страницы} и
     * {@link NavigationSettingsMessages её названием}
     */
    public static class ReferenceCardTypeProvider implements Provider<Map<MenuItemTypeOfCard, String>>
    {
        @Inject
        private NavigationSettingsMessages messages;

        @Override
        public Map<MenuItemTypeOfCard, String> get()
        {
            Map<MenuItemTypeOfCard, String> result = Arrays.stream(MenuItemTypeOfCard.values())
                    .collect(Collectors.toMap(value -> value, value -> messages.contentMenuItemObject(value.name()),
                            (a, b) -> b));
            return result;
        }
    }

    public static class MenuFormattingTitlesProvider implements Provider<Map<MenuItemFormatting, String>>
    {
        @Inject
        private NavigationSettingsMessages messages;

        @Override
        public Map<MenuItemFormatting, String> get()
        {
            Map<MenuItemFormatting, String> result = new EnumMap<>(MenuItemFormatting.class);
            result.put(MenuItemFormatting.section, messages.section());
            result.put(MenuItemFormatting.module, messages.module());
            return result;
        }
    }

    public static class QuickAccessAreaTitlesProvider implements Provider<Map<String, String>>
    {
        @Inject
        private NavigationSettingsMessages messages;

        @Override
        public Map<String, String> get()
        {
            Map<String, String> result = new HashMap<>();
            result.put(Constants.QuickAccessPanelSettings.ADMIN_AREA_CODE, messages.adminArea());
            result.put(Constants.QuickAccessPanelSettings.USER_AREA_CODE, messages.userArea());
            //            result.put(Constants.QuickAccessPanelSettings.SYSTEM_AREA_CODE, messages.systemArea());
            return result;
        }
    }

    public static class QuickAccessAreaDescriptionsProvider implements Provider<Map<String, String>>
    {
        @Inject
        private NavigationSettingsMessages messages;

        @Override
        public Map<String, String> get()
        {
            Map<String, String> result = new HashMap<>();
            result.put(Constants.QuickAccessPanelSettings.ADMIN_AREA_CODE, messages.adminAreaDescription().asString());
            result.put(Constants.QuickAccessPanelSettings.USER_AREA_CODE, messages.userAreaDescription().asString());
            //            result.put(Constants.QuickAccessPanelSettings.SYSTEM_AREA_CODE, messages
            //            .systemAreaDescription().asString());
            return result;
        }
    }

    public static class ResultProfilesTypeTitleProvider implements Provider<Map<ResultProfilesType, String>>
    {
        @Inject
        private NavigationSettingsMessages messages;

        @Override
        public Map<ResultProfilesType, String> get()
        {
            Map<ResultProfilesType, String> result = new EnumMap<>(ResultProfilesType.class);
            result.put(ResultProfilesType.ALL, messages.allProfiles());
            result.put(ResultProfilesType.LIST, messages.listOfProfiles());
            result.put(ResultProfilesType.NONE, messages.noProfiles());
            return result;
        }
    }

    public static class TopMenuTypeProvider implements Provider<List<MenuItemType>>
    {
        @Override
        public List<MenuItemType> get()
        {
            return MenuItemType.TOP_MENU_ITEM_TYPES;
        }
    }

    public static class LeftMenuTypeProvider implements Provider<List<MenuItemType>>
    {
        @Override
        public List<MenuItemType> get()
        {
            return MenuItemType.LEFT_MENU_ITEM_TYPES;
        }
    }

    public static final String MENU_TYPE_TITLES = "menuTypeTitles";
    public static final String MENU_FORMATTING_TITLES = "menuFormattingTitles";
    public static final String REFERENCE_CARD_TYPE_TITLES = "referenceCardTypeTitles";
    public static final String QUICK_ACCESS_AREA_TITLES = "quickAccessAreaTitles";
    public static final String RESULT_PROFILE_TYPE_TITLES = "resultProfileTypeTitles";
    public static final String HOME_PAGE_TYPE_TITLES = "homePageTypeTitles";

    public static final String LEFT_MENU_TYPES = "leftMenuTypes";
    public static final String TOP_MENU_TYPES = "topMenuTypes";
    public static final String QUICK_AREA_DESCRIPTIONS = "quickAccessAreaDescriptions";

    @Override
    protected void configure()
    {
        install(new NavigationMenuItemGinModule());
        install(new NavigationSettingsCommandGinModule());
        install(new BreadCrumbGinModule());
        install(new HomePageGinModule());

        //@formatter:off
        bind(new TypeLiteral<Map<MenuItemType, String>>(){})
            .annotatedWith(Names.named(MENU_TYPE_TITLES))
            .toProvider(MenuTypeTitlesProvider.class)
            .in(Singleton.class);
        bind(new TypeLiteral<Map<MenuItemFormatting, String>>(){})
                .annotatedWith(Names.named(MENU_FORMATTING_TITLES))
                .toProvider(MenuFormattingTitlesProvider.class)
                .in(Singleton.class);
        bind(new TypeLiteral<Map<MenuItemTypeOfCard, String>>(){})
                .annotatedWith(Names.named(REFERENCE_CARD_TYPE_TITLES))
                .toProvider(ReferenceCardTypeProvider.class)
                .in(Singleton.class);
        bind(new TypeLiteral<Map<String, String>>(){})
                .annotatedWith(Names.named(QUICK_ACCESS_AREA_TITLES))
                .toProvider(QuickAccessAreaTitlesProvider.class)
                .in(Singleton.class);
        bind(new TypeLiteral<Map<String, String>>(){})
                .annotatedWith(Names.named(QUICK_AREA_DESCRIPTIONS))
                .toProvider(QuickAccessAreaDescriptionsProvider.class)
                .in(Singleton.class);
        bind(new TypeLiteral<Map<ResultProfilesType, String>>(){})
                .annotatedWith(Names.named(RESULT_PROFILE_TYPE_TITLES))
                .toProvider(ResultProfilesTypeTitleProvider.class)
                .in(Singleton.class);

        bind(new TypeLiteral<Map<HomePageType, String>>(){})
                .annotatedWith(Names.named(HOME_PAGE_TYPE_TITLES))
                .toProvider(HomePageTypeProvider.class)
                .in(Singleton.class);

        bind(new TypeLiteral<List<MenuItemType>>(){})
                .annotatedWith(Names.named(TOP_MENU_TYPES))
                .toProvider(TopMenuTypeProvider.class)
                .in(Singleton.class);
        bind(new TypeLiteral<List<MenuItemType>>(){})
                .annotatedWith(Names.named(LEFT_MENU_TYPES))
                .toProvider(LeftMenuTypeProvider.class)
                .in(Singleton.class);

        install(Gin.bindSingleton(new TypeLiteral<NavigationSettingsMessages>(){}, null));
        
        bind(new TypeLiteral<TableDisplay<MenuItem>>() {}).to(new TypeLiteral<TableWithArrowsDisplay<MenuItem>>() {});
        bind(new TypeLiteral<TableDisplay<LeftMenuItemSettingsDTO>>() {}).to(new TypeLiteral<TableWithArrowsDisplay<LeftMenuItemSettingsDTO>>() {});
        bind(new TypeLiteral<TableDisplay<QuickAccessPanelElementWrapper>>() {}).to(new TypeLiteral<TableWithArrowsDisplay<QuickAccessPanelElementWrapper>>() {});
        bind(new TypeLiteral<TableDisplay<Crumb>>() {}).to(new TypeLiteral<TableWithArrowsDisplay<Crumb>>() {});
        bind(new TypeLiteral<TableDisplay<HomePageDtObject>>() {}).to(new TypeLiteral<TableWithArrowsDisplay<HomePageDtObject>>() {});

        bind(NavigationSettingsResources.class).in(Singleton.class);
        //@formatter:on
    }

}
