package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.tags;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.INHERIT;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.TAGS;

import java.util.Collection;

import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;

/**
 * Делегат обновления состояния свойства "Метки".
 * <AUTHOR>
 * @since Dec 20, 2018
 */
@Singleton
public class TagsRefreshDelegateImpl<F extends ObjectForm>
        implements AttributeFormPropertyDelegateRefresh<F, Collection<SelectItem>, TagsProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, TagsProperty property,
            AsyncCallback<Boolean> callback)
    {
        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        if (Constants.SYSTEM_METACLASSES.contains(metaClass.getFqn().fqnOfClass()))
        {
            callback.onSuccess(false);
            return;
        }

        Attribute attribute = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        if (null != attribute && (attribute.isHardcoded()
                                  || ru.naumen.core.shared.Constants.PARENT_ATTR.equals(attribute.getCode())))
        {
            callback.onSuccess(false);
            return;
        }

        boolean inherited = Boolean.TRUE.equals(context.getPropertyValues().getProperty(INHERIT));
        boolean isAggregatePart = null != attribute
                                  && (attribute.getCode().endsWith(AggregateAttributeType.EMPLOYEE_POSTFIX)
                                      || attribute.getCode().endsWith(AggregateAttributeType.OU_POSTFIX)
                                      || attribute.getCode().endsWith(AggregateAttributeType.TEAM_POSTFIX));
        context.setPropertyEnabled(TAGS, !inherited && !isAggregatePart);

        callback.onSuccess(true);
    }
}
