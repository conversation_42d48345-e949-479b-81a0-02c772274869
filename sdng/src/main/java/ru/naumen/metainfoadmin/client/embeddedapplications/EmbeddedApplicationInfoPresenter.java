package ru.naumen.metainfoadmin.client.embeddedapplications;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.safehtml.shared.UriUtils;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.IHyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.client.common.UrlUtils;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.guic.shared.LinkUtils;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfo.shared.embeddedapplication.ExternalApplicationUrlDefinition;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.shared.Constants;

/**
 * <AUTHOR>
 * @since 16.08.2016
 *
 */
public class EmbeddedApplicationInfoPresenter extends BasicPresenter<InfoDisplay>
        implements EmbeddedApplicationUpdatedHandler
{
    public final class EmbeddedApplicationParam
            extends CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto>
    {
        public EmbeddedApplicationParam(AsyncCallback<EmbeddedApplicationAdminSettingsDto> callback)
        {
            super(new FactoryParam.ValueSource<EmbeddedApplicationAdminSettingsDto>()
            {
                @Override
                public EmbeddedApplicationAdminSettingsDto getValue()
                {
                    return embeddedApplication;
                }
            }, callback);
        }
    }

    @Inject
    protected EmbeddedApplicationMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    protected Formatters formatters;
    @Inject
    protected AdminMetainfoServiceAsync metainfoService;
    @Inject
    protected PlaceController placeController;
    protected EmbeddedApplicationAdminSettingsDto embeddedApplication;
    protected ButtonPresenter<EmbeddedApplicationAdminSettingsDto> toggleButtonPresenter;
    @Inject
    CommonHtmlTemplates htmlTemplates;
    @Inject
    ApplicationValidationPropertiesPresenter validationPropertiesPresenter;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> code;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> description;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> applicationType;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> on;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> application;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> externalAppUrlDefinition;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> initialHeight;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> mobileHeight;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    private Property<Boolean> fullscreenAllowed;
    @Inject
    @Named(PropertiesGinModule.SCRIPT_COMPONENT_VIEW)
    private Property<ScriptDto> script;
    private Property<String> settingsSet;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private EmbeddedApplicationsPlace embeddedApplicationsPlace;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> applicationAddress;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> scriptModule;
    private final ToolBarDisplayMediator<EmbeddedApplicationAdminSettingsDto> toolBar;
    @SuppressWarnings("rawtypes")
    private OnStartCallback refreshCallback;

    @Inject
    public EmbeddedApplicationInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    @SuppressWarnings("rawtypes")
    public void init(EmbeddedApplicationAdminSettingsDto embeddedApplication, OnStartCallback refreshCallback)
    {
        this.embeddedApplication = embeddedApplication;
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void onEmbeddedApplicationUpdated(EmbeddedApplicationUpdatedEvent e)
    {
        embeddedApplication = e.getEmbeddedApplication();
        if (e.getScriptDto() != null)
        {
            embeddedApplication.setScriptDto(e.getScriptDto());
        }

        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        getDisplay().clearProperties();
        addProperties();
        setPropertiesValues();
        toolBar.refresh(embeddedApplication);
        toggleButtonPresenter
                .setTitle(embeddedApplication.isOn() ? cmessages.switchOff() : cmessages.switchOn());
        if (embeddedApplication.isHasValidationProperties())
        {
            validationPropertiesPresenter.setConfig(embeddedApplication);
            validationPropertiesPresenter.refreshDisplay();
        }
    }

    @SuppressWarnings("unchecked")
    protected ButtonPresenter<EmbeddedApplicationAdminSettingsDto> addTool(String btn, String title, String cmd,
            CommandParam<EmbeddedApplicationAdminSettingsDto, EmbeddedApplicationAdminSettingsDto> param)
    {
        ButtonPresenter<EmbeddedApplicationAdminSettingsDto> buttonPresenter =
                (ButtonPresenter<EmbeddedApplicationAdminSettingsDto>)buttonFactory
                        .create(btn, title, cmd, param);
        toolBar.add(buttonPresenter);
        return buttonPresenter;
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected void initToolBar()
    {
        OnStartCallback delCallback = new OnStartBasicCallback<Void>(getDisplay())
        {
            @Override
            protected void handleSuccess(Void value)
            {
                placeController.goTo(embeddedApplicationsPlace);
            }
        };

        toggleButtonPresenter = addTool(ButtonCode.SWITCH,
                embeddedApplication.isOn() ? cmessages.switchOff() : cmessages.switchOn(),
                Constants.EmbeddedAppCommandCode.TOGGLE_APPLICATION, new EmbeddedApplicationParam(refreshCallback));

        if (embeddedApplication.getPermissionTypes().contains(PermissionType.EDIT))
        {
            addTool(ButtonCode.EDIT, cmessages.edit(), selectEditComand(),
                    new EmbeddedApplicationParam(refreshCallback));
        }

        if (embeddedApplication.getPermissionTypes().contains(PermissionType.DELETE))
        {
            addTool(ButtonCode.DELETE, cmessages.delete(), Constants.EmbeddedAppCommandCode.DELETE_APPLICATION,
                    new EmbeddedApplicationParam(delCallback));
        }

        toolBar.bind();
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(EmbeddedApplicationUpdatedEvent.getType(), this));
        getDisplay().setTitle(cmessages.properties());
        initToolBar();
        refreshDisplay();
        ensureDebugIds();
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(description, "description");
        DebugIdBuilder.ensureDebugId(applicationType, "applicationType");
        DebugIdBuilder.ensureDebugId(application, "application");
        DebugIdBuilder.ensureDebugId(on, "on");
        DebugIdBuilder.ensureDebugId(initialHeight, "initialHeight");
        DebugIdBuilder.ensureDebugId(mobileHeight, "mobileHeight");
        DebugIdBuilder.ensureDebugId(fullscreenAllowed, "fullscreenAllowed");
        DebugIdBuilder.ensureDebugId(script, "script");
    }

    private void addProperties()
    {
        getDisplay().add(title);
        title.setCaption(cmessages.title());
        getDisplay().add(code);
        code.setCaption(cmessages.code());
        getDisplay().add(description);
        description.setCaption(cmessages.description());
        getDisplay().add(applicationType);
        applicationType.setCaption(messages.applicationType());
        getDisplay().add(on);
        on.setCaption(cmessages.on());
        EmbeddedApplicationType externalAppType = embeddedApplication.getEmbeddedApplicationType();

        switch (externalAppType)
        {
            case InternalApplication:
                validationPropertiesPresenter.init(getDisplay(), embeddedApplication);
                validationPropertiesPresenter.bindProperties();
                application.setCaption(messages.applicationFile());
                getDisplay().add(application);
                break;
            case ClientSideApplication:
                application.setCaption(messages.applicationFile());
                getDisplay().add(application);
                break;
            case ExternalApplication:
                externalAppUrlDefinition.setCaption(messages.methodOfUrlDefinition());
                getDisplay().add(externalAppUrlDefinition);
                application.setCaption(messages.applicationFile());
                getDisplay().add(application);
                if (embeddedApplication.getExternalApplicationUrlDefinition()
                    != ExternalApplicationUrlDefinition.SCRIPT_DEFINED)
                {
                    applicationAddress.setCaption(messages.applicationAddress());
                    getDisplay().add(applicationAddress);
                }
                break;
            case CustomLoginFormApplication:
                application.setCaption(messages.applicationFile());
                getDisplay().add(application);
                scriptModule.setCaption(messages.scriptModule());
                getDisplay().add(scriptModule);
                break;
            default:
                throw new IllegalArgumentException("Unknown embedded application type");
        }

        if (externalAppType != EmbeddedApplicationType.CustomLoginFormApplication)
        {
            getDisplay().add(initialHeight);
            initialHeight.setCaption(messages.initialApplicationHeight());
            getDisplay().add(mobileHeight);
            mobileHeight.setCaption(messages.mobileApplicationHeight());
            getDisplay().add(fullscreenAllowed);
            fullscreenAllowed.setCaption(messages.fullscreenAllowed());
        }

        if (externalAppType != EmbeddedApplicationType.ClientSideApplication &&
            externalAppType != EmbeddedApplicationType.CustomLoginFormApplication)
        {
            getDisplay().add(script);
            script.setCaption(cmessages.script());
        }
        settingsSet = settingsSetOnFormCreator.createFieldOnCard(getDisplay());
    }

    private String getAppFileUrl(EmbeddedApplicationAdminSettingsDto embeddedApplication)
    {
        DtObject templateFile = embeddedApplication.getApplicationFile();
        SafeHtml html = SafeHtmlUtils.fromString(StringUtilities.EMPTY);
        if (templateFile != null)
        {
            html = htmlTemplates.anchor(templateFile.getTitle(), UriUtils.fromString(UrlUtils.DOWNLOAD_URL_PREFIX
                                                                                     + this.embeddedApplication.getFileUuid()),
                    false, SafeHtmlUtils.EMPTY_SAFE_HTML);
        }
        return html.asString();
    }

    private static String selectEditComand()
    {
        return Constants.EmbeddedAppCommandCode.EDIT_APPLICATION;
    }

    private void setPropertiesValues()
    {
        title.setValue(embeddedApplication.getDisplayTitle());
        code.setValue(embeddedApplication.getCode());
        description.setValue(formatters
                .normalize(SafeHtmlUtils.fromTrustedString(
                        formatters.formatText(embeddedApplication.getDescription())))
                .asString());
        on.setValue(embeddedApplication.isOn());
        initialHeight.setValue(Integer.toString(embeddedApplication.getInitialHeight())); //NOPMD
        Long mobileHeightValue = embeddedApplication.getMobileHeight();
        mobileHeight.setValue(mobileHeightValue == null ? "" : mobileHeightValue.toString());
        fullscreenAllowed.setValue(embeddedApplication.isFullscreenAllowed());
        settingsSetOnFormCreator.setValueOnCardProperty(embeddedApplication.getSettingsSet(), settingsSet);
        switch (embeddedApplication.getEmbeddedApplicationType())
        {
            case InternalApplication:
                applicationType.setValue(messages.applicationHostedOnInternalServer());
                application.setValue(getAppFileUrl(embeddedApplication));
                script.setValue(embeddedApplication.getScriptDto());
                break;
            case ClientSideApplication:
                applicationType.setValue(messages.applicationNoServer());
                application.setValue(getAppFileUrl(embeddedApplication));
                break;
            case ExternalApplication:
                applicationType.setValue(messages.applicationHostedOnExternalServer());
                application.setValue(getAppFileUrl(embeddedApplication));
                applicationAddress.setValue(embeddedApplication.getApplicationAddress());
                script.setValue(embeddedApplication.getScriptDto());

                switch (embeddedApplication.getExternalApplicationUrlDefinition())
                {
                    case SCRIPT_DEFINED_PARAMETERS:
                        externalAppUrlDefinition.setValue(messages.parametersAreDefinedByScript());
                        break;
                    case SCRIPT_DEFINED:
                        externalAppUrlDefinition.setValue(messages.wholeUrlIsDefinedByScript());
                        break;
                    default:
                        externalAppUrlDefinition.setValue(messages.parametersAreDefinedBySystemLogic());
                        break;
                }
                break;
            case CustomLoginFormApplication:
                applicationType.setValue(messages.applicationCustomLoginForm());
                application.setValue(embeddedApplication.getApplicationAddress());
                if (embeddedApplication.getScriptModuleCode() != null && !embeddedApplication.getScriptModuleCode()
                        .isEmpty())
                {
                    IHyperlink scriptModuleLink = new Hyperlink(embeddedApplication.getScriptModuleCode(),
                            LinkUtils.getScriptModuleCardLink(embeddedApplication.getScriptModuleCode()));
                    SafeHtml scriptModuleHtml = formatters.formatHyperlinkAsHtml(scriptModuleLink);
                    scriptModule.setValue(scriptModuleHtml.asString());
                }
                else
                {
                    scriptModule.setValue("");
                }
                break;
            default:
                throw new IllegalArgumentException("Unknown embedded application type");
        }
    }
}
