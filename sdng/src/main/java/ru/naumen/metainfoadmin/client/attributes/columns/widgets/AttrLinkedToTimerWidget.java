package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TITLE_SEPARATOR;

import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.IsWidget;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.timer.definition.TimerDefinitionWithScript;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.AdminCachedMetainfoService;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.timer.TimerPlace;

/**
 * Для атрибутов типа "Счётчик времени" и "Счётчик времени (обратный)" отображается 
 * название счётчика, который используется в атрибуте. Название счётчика — ссылка на его карточку.
 * Формат: "Счётчик %Название счётчика%".
 *
 * <AUTHOR>
 * @since 1 авг. 2018 г.
 *
 */
public class AttrLinkedToTimerWidget extends AttrTypeColumnWidgetWithReadyState
{
    private static final List<String> ATTR_TYPES = Arrays.asList(
            Constants.TimerAttributeType.CODE,
            Constants.BackTimerAttributeType.CODE
    );

    private static final Logger LOG = Logger.getLogger(AttrLinkedToTimerWidget.class.getName());

    private final AdminCachedMetainfoService adminMetainfoService;
    private final MetainfoUtils metainfoUtils;
    private final AttributesMessages messages;

    @Inject
    public AttrLinkedToTimerWidget(AdminCachedMetainfoService metainfoService, MetainfoUtils metainfoUtils,
            AttributesMessages messages)
    {
        this.adminMetainfoService = metainfoService;
        this.metainfoUtils = metainfoUtils;
        this.messages = messages;
    }

    @Override
    public IsWidget createWidget(Attribute attr)
    {
        final FlowPanel result = new FlowPanel();
        final String definition = attr.getType().getProperty(Constants.TimerAttributeType.DEFINITION);
        result.add(new InlineLabel(messages.timer() + TITLE_SEPARATOR));
        //definition м.б. null, см.: "NSDPRD-1471 При копировании метакласса атрибут "Счетчик" копируется без счетчика"
        if (definition != null)
        {
            adminMetainfoService.getTimerDefinition(definition, new BasicCallback<TimerDefinitionWithScript>(rs)
            {
                @Override
                protected void handleSuccess(TimerDefinitionWithScript timerDefinition)
                {
                    String title = metainfoUtils.getLocalizedValue(timerDefinition.getTitle());
                    Anchor anchor = new Anchor(title, false,
                            AttrWidgetsHelper.createLink(TimerPlace.PLACE_PREFIX, definition));
                    result.add(anchor);
                    anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
                }

                @Override
                protected void handleFailure(String msg, @Nullable String details)
                {
                    //Без этого не проходят некоторые тесты: не предполагается, что timerDefinition реально существует.
                    //Пример: ru.naumen.selenium.cases.admin.system.administration.metainfo.AttributeInClassesTest
                    LOG.severe(msg + (details != null ? (": " + details) : ""));
                }
            });
        }
        return result;
    }

    @Override
    public List<String> listAllowedAttrTypes()
    {
        return ATTR_TYPES;
    }
}