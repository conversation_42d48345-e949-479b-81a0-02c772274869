package ru.naumen.metainfoadmin.client.customforms.command;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.IHasI18nTitle;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.client.ui.CustomFormMessages;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.common.content.commands.ContentCommandParam;

/**
 * <AUTHOR>
 * @since 26.04.2016
 *
 */
public class DeleteCustomFormCommand extends ObjectCommandImpl<CustomForm, Void>
{
    @Inject
    CustomFormMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    I18nUtil i18nUtil;

    @Inject
    public DeleteCustomFormCommand(@Assisted ContentCommandParam<CustomForm, Void> param, Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(CustomForm customForm)
    {
        return switch (customForm.getFormType())
        {
            case ChangeCaseForm -> cmessages.confirmDeleteQuestion2(messages.deleteChangeCaseFormConfirmation());
            case ChangeResponsibleForm ->
                    cmessages.confirmDeleteQuestion2(messages.deleteChangeResponsibleFormConfirmation());
            case QuickForm, MassEditForm ->
                    cmessages.confirmDeleteQuestion2(i18nUtil.getLocalizedTitle((IHasI18nTitle)customForm));
            default -> null;
        };
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    protected void onDialogSuccess(CommandParam<CustomForm, Void> param)
    {
        metainfoModificationService.deleteContent(null, param.getValue(), param.getCallbackSafe());
    }
}
