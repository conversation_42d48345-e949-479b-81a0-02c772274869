package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumn;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumnFactory;
import ru.naumen.core.client.widgets.columns.LinkToPlaceWithIndentColumn;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands.AddHomePageItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands.DeleteHomePageItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands.EditHomePageItemCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands.HomePageItemsListCommandFactoryInitializer;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands.MoveHomePageItemDownCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands.MoveHomePageItemUpCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.HomePageFormsGinModule;

/**
 * Общий GIN-модуль домашней страницы
 *
 * <AUTHOR>
 * @since 09.01.2023
 */
public class HomePageGinModule extends AbstractGinModule
{
    //@formatter:off
    @Override
    protected void configure()
    {
        bind(HomePageItemsListCommandFactoryInitializer.class).asEagerSingleton();
        install(new HomePageFormsGinModule());
        install(new NavigationHomePageGinModule());

        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, AddHomePageItemCommand.class)
                .build(new TypeLiteral<CommandProvider<AddHomePageItemCommand, HomePageCommandParam>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, MoveHomePageItemDownCommand.class)
                .build(new TypeLiteral<CommandProvider<MoveHomePageItemDownCommand, HomePageCommandParam>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, MoveHomePageItemUpCommand.class)
                .build(new TypeLiteral<CommandProvider<MoveHomePageItemUpCommand, HomePageCommandParam>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditHomePageItemCommand.class)
                .build(new TypeLiteral<CommandProvider<EditHomePageItemCommand, HomePageCommandParam>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, DeleteHomePageItemCommand.class)
                .build(new TypeLiteral<CommandProvider<DeleteHomePageItemCommand, HomePageCommandParam>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(new TypeLiteral<LinkToPlaceColumn<HomePageDtObject>>(){},
                        new TypeLiteral<LinkToPlaceWithIndentColumn<HomePageDtObject>>(){})
                .build(new TypeLiteral<LinkToPlaceColumnFactory<HomePageDtObject>>(){}));
    }
    //@formatter:on
}
