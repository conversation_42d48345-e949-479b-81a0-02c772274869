package ru.naumen.metainfoadmin.client.structuredobjectsviews.card;

import java.util.List;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.FactoryParam.ValueSource;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.StructuredObjectsView;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsCommandCode;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsPlace;

/**
 * Блок "Свойства" на карточке.
 * <AUTHOR>
 * @since 21.10.2019
 */
public class StructuredObjectsViewInfoPresenter extends BasicPresenter<InfoDisplay>
{
    private class StructuredObjectsViewInfoCommandParam extends CommandParam<List<DtObject>, DtObject>
    {
        public StructuredObjectsViewInfoCommandParam(AsyncCallback<DtObject> callback)
        {
            super(valueSource, callback);
        }
    }

    private class StructuredObjectsViewInfoEditCommandParam extends CommandParam<DtObject, DtObject>
    {
        public StructuredObjectsViewInfoEditCommandParam(AsyncCallback<DtObject> callback)
        {
            super(valueSourceEdit, callback);
        }
    }

    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> description;
    private Property<String> settingsSet;

    @Inject
    private CommonMessages cmessages;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private PlaceController placeController;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    private DtObject structuredObjectsView;
    private OnStartCallback<DtObject> refreshCallback;
    private final ToolBarDisplayMediator<DtObject> toolBar;

    private final OnStartCallback<DtObject> removeCallback = new SafeOnStartBasicCallback<DtObject>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            placeController.goTo(StructuredObjectsViewsPlace.INSTANCE);
        }
    };

    private final ValueSource<List<DtObject>> valueSource = new ValueSource<List<DtObject>>()
    {
        @Override
        public List<DtObject> getValue()
        {
            return Lists.newArrayList(structuredObjectsView);
        }
    };

    private final ValueSource<DtObject> valueSourceEdit = new ValueSource<DtObject>()
    {
        @Override
        public DtObject getValue()
        {
            return structuredObjectsView;
        }
    };

    @Inject
    public StructuredObjectsViewInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(display.getToolBar());
    }

    public void init(OnStartCallback<DtObject> refreshCallback)
    {
        settingsSet = settingsSetOnFormCreator.createFieldOnCard();
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();

        title.setValue(structuredObjectsView.getTitle());
        code.setValue(structuredObjectsView.getUUID());
        description.setValue(structuredObjectsView.getProperty(StructuredObjectsView.DESCRIPTION));
        settingsSetOnFormCreator.
                setValueOnCardProperty(structuredObjectsView.getProperty(StructuredObjectsView.SETTINGS_SET),
                        settingsSet);
        toolBar.refresh(structuredObjectsView);
    }

    public void setStructuredObjectsView(DtObject structuredObjectsView)
    {
        this.structuredObjectsView = structuredObjectsView;
        refreshDisplay();
    }

    protected void bindProperties()
    {
        title.setCaption(cmessages.title());
        getDisplay().add(title);
        code.setCaption(cmessages.code());
        getDisplay().add(code);
        description.setCaption(cmessages.description());
        getDisplay().add(description);
        if (settingsSet != null)
        {
            getDisplay().add(settingsSet);
        }
    }

    protected void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(description, "description");
    }

    @SuppressWarnings("unchecked")
    protected void initToolBar()
    {
        ButtonPresenter<DtObject> buttonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(ButtonCode.EDIT,
                cmessages.edit(), StructuredObjectsViewsCommandCode.EDIT, new StructuredObjectsViewInfoEditCommandParam(
                        refreshCallback));
        buttonPresenter.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        toolBar.add(buttonPresenter);
        buttonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(ButtonCode.DELETE, cmessages.delete(),
                StructuredObjectsViewsCommandCode.DELETE, new StructuredObjectsViewInfoCommandParam(removeCallback));
        buttonPresenter.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE));
        toolBar.add(buttonPresenter);
        toolBar.bind();
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(cmessages.properties());
        initToolBar();
        bindProperties();
        ensureDebugIds();
        refreshDisplay();
    }
}
