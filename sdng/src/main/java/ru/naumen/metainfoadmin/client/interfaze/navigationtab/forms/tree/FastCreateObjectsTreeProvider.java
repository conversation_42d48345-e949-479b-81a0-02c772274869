package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.tree;

import java.util.Collection;
import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import com.google.common.collect.Sets;
import com.google.inject.Provider;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.tree.selection.HierarchicalMetaClassMultiSelectionModel;
import ru.naumen.core.client.tree.view.ITreeViewModel;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.client.widgets.tree.formatters.TitledMultiFormatter;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;

/**
 * Создает дерево метаклассов из классов:
 * Услуга, Соглашение, Команда + вложенные
 *
 * <AUTHOR>
 * @since 01.04.2013
 */
public class FastCreateObjectsTreeProvider implements Provider<Property<Collection<DtObject>>>
{
    private static final Set<ClassFqn> FOR_HIERARCHY_CLASSES = Sets.newHashSet(Constants.AbstractBO.FQN,
            Constants.AbstractUserEntity.FQN);

    //@formatter:off
    private static final Predicate<DtObject> SELECTABLE_PREDICATE = 
            Predicates.not(Predicates.compose(Predicates.in(FOR_HIERARCHY_CLASSES), DtObject.FQN_EXTRACTOR));
    //@formatter:on

    //@formatter:off
    private static final Predicate<MetaClassLite> METACLASSES_FILTER = MetaClassFilters.not(
            MetaClassFilters.<MetaClassLite> in(
                    Sets.newHashSet(Constants.Root.FQN)));
    //@formatter:on

    @Inject
    private CommonMessages cmessages;
    @Inject
    private PopupValueCellTreeFactory<DtObject, Collection<DtObject>,
            HierarchicalMetaClassMultiSelectionModel<DtObject>> treeFactory;
    @Inject
    private DtoMetaClassesTreeFactory<HierarchicalMetaClassMultiSelectionModel<DtObject>,
            FastCreateMetaClassTreeContext> treeModelHelper;
    @Inject
    private TitledMultiFormatter<DtObject> formatter;

    @Override
    public Property<Collection<DtObject>> get()
    {
        return get(Constants.AbstractBO.FQN, METACLASSES_FILTER);
    }

    public Property<Collection<DtObject>> get(ClassFqn root, Predicate<MetaClassLite> filter)
    {
        //@formatter:off
        ITreeViewModel<DtObject, HierarchicalMetaClassMultiSelectionModel<DtObject>> viewModel = 
                treeModelHelper.createMetaClassTreeViewModel(Container.create(new FastCreateMetaClassTreeContext(root, filter)));
        //@formatter:on

        viewModel.getSelectionModel().setSelectableFilter(SELECTABLE_PREDICATE);
        return new PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                HierarchicalMetaClassMultiSelectionModel<DtObject>>>(
                cmessages.objects(), treeFactory.create(viewModel).setFormatter(formatter));
    }
}
