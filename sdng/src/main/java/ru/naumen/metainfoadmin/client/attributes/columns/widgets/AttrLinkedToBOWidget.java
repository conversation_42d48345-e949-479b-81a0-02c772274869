package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TITLE_SEPARATOR;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.AdminCachedMetainfoService;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;

/**
 * Для атрибутов "Ссылка на бизнес объект", "Набор ссылок на БО" отображаются 
 * класс и типы, на которые ссылается атрибут. Если ограничения по типам нет, 
 * то отображается только класс. Перечисление типов отображается с новой строки. 
 * Название класса и типов — ссылка на их карточку. 
 * Формат: "Класс %Название класса%. Типы: %список названий типов через запятую%" 
 *
 * <AUTHOR>
 * @since 30 июл. 2018 г.
 *
 */
public class AttrLinkedToBOWidget extends AttrTypeColumnWidgetWithReadyState
{
    private final static List<String> ATTR_TYPES = Arrays.asList(
            Constants.ObjectAttributeType.CODE,
            Constants.BOLinksAttributeType.CODE);

    private AdminCachedMetainfoService metainfoService;
    private AttributesMessages messages;
    private CommonMessages commonMessages;

    @Inject
    public AttrLinkedToBOWidget(AdminCachedMetainfoService metainfoService, AttributesMessages messages,
            CommonMessages commonMessages)
    {
        this.metainfoService = metainfoService;
        this.messages = messages;
        this.commonMessages = commonMessages;
    }

    @Override
    public IsWidget createWidget(Attribute attr)
    {
        final FlowPanel result = new FlowPanel();
        String metaclass = attr.getType().getProperty(Constants.ObjectAttributeType.METACLASS_FQN);
        metainfoService.getDescendantClasses(ClassFqn.parse(metaclass), true,
                new BasicCallback<List<MetaClassLite>>(rs)
                {
                    @Override
                    protected void handleSuccess(List<MetaClassLite> metaclasses)
                    {
                        MetaClassLite currentClass = metaclasses.get(0);
                        result.add(new InlineLabel(messages.clazz() + TITLE_SEPARATOR));
                        Anchor anchor = new Anchor(currentClass.getTitle(), false,
                                AttrWidgetsHelper.createLink(MetaClassPlace.PLACE_PREFIX, currentClass.getCode()));
                        result.add(anchor);
                        anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());

                        AttrWidgetsHelper.handleTypesOutput(result, messages, metaclasses, attr.getType()
                                .getPermittedTypes(), commonMessages);
                    }
                });

        return result;
    }

    @Override
    public List<String> listAllowedAttrTypes()
    {
        return ATTR_TYPES;
    }
}