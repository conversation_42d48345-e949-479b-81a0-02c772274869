package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import com.google.common.base.Preconditions;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;

/**
 * Плейс ссылки на карточку элемента левого меню
 * <AUTHOR>
 * @since 17.07.2020
 */
public class NavigationLeftMenuItemPlace extends NavigationMenuItemPlace<LeftMenuItemSettingsDTO>
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<NavigationLeftMenuItemPlace>
    {
        @Override
        public NavigationLeftMenuItemPlace getPlace(String token)
        {
            Preconditions.checkNotNull(token, "Bad NavigationMenuItemPlace");
            return new NavigationLeftMenuItemPlace(token);
        }

        @Override
        public String getToken(NavigationLeftMenuItemPlace place)
        {
            return place == null ? "" : place.getCode();
        }
    }

    public static final String PLACE_PREFIX = "left-menu-item";

    public NavigationLeftMenuItemPlace(LeftMenuItemSettingsDTO menuItem, DtoContainer<NavigationSettings> settings)
    {
        super(menuItem, settings);
    }

    public NavigationLeftMenuItemPlace(String code)
    {
        super(code);
    }

    protected NavigationLeftMenuItemPlace()
    {
        super();
    }
}
