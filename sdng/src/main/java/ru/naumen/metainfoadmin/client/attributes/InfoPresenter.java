package ru.naumen.metainfoadmin.client.attributes;

import static ru.naumen.metainfo.shared.Constants.MetaClassProperties.DEFAULT_CLIENT_AGREEMENT;
import static ru.naumen.metainfo.shared.Constants.MetaClassProperties.DEFAULT_CLIENT_SERVICE;
import static ru.naumen.metainfo.shared.Constants.MetaClassProperties.DEFAULT_SC_TYPE;
import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;
import static ru.naumen.metainfo.shared.filters.RelationFilters.and;
import static ru.naumen.metainfo.shared.filters.RelationFilters.isParent;
import static ru.naumen.metainfo.shared.filters.RelationFilters.right;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.google.common.base.Predicate;

import java.util.ArrayList;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.admin.client.AdminMessages;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.Attention;
import ru.naumen.core.client.widgets.ExpandCollapseEvent;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.buttons.Button;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.AbstractCasedBO;
import ru.naumen.core.shared.Constants.AbstractStateResponsibleEvent;
import ru.naumen.core.shared.Constants.AbstractSystemObject;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.Event;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.Constants.GeoHistoryRecord;
import ru.naumen.core.shared.Constants.LinkToVersion;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.settings.ScCaseFieldsOrderSettings;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.fts.client.SearchServiceAsync;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.MetaClassPermissionsUpdatedEvent;
import ru.naumen.metainfo.client.MetaClassPermissionsUpdatedHandler;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.client.events.MetainfoUpdatedEvent;
import ru.naumen.metainfo.client.events.MetainfoUpdatedHandler;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.QuotaSnapshot;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.Relation;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.CommonUtils;
import ru.naumen.metainfoadmin.client.MGinjector;
import ru.naumen.metainfoadmin.client.forms.CopyMetaclassDialogPresenter;
import ru.naumen.metainfoadmin.client.forms.EditMetaclassDialogPresenter;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.client.tags.data.TagServiceAsync;
import ru.naumen.metainfoadmin.client.tags.formatters.TagPropertyFormatter;

/**
 * Презентер информации о классе/ типе класса.
 *
 * <AUTHOR>
 * @since 14.10.2010
 *
 */
public class InfoPresenter extends BasicPresenter<InfoDisplay> implements MetainfoUpdatedHandler,
        MetaClassTagsChangedHandler, MetaClassQuotaUpdatedHandler, MetaClassPermissionsUpdatedHandler
{
    /**
     * Реализация {@link Strategy} для Типа
     *
     */
    class CaseStrategy extends Strategy
    {
        PropertyRegistration<String> parent;

        public CaseStrategy()
        {
            parent = createProperty(cmessages.clazz(), "Info.parent");
            initOtherProperties();
            initPlannedVersionsProperties();
            initTags();
            initSettingsSet();
            initBrowserTabProperties();
        }

        @Override
        public void refresh()
        {
            super.refresh();

            Property<String> property = code.getProperty();
            if (property != null)
            {
                property.setValue(metaclass.getFqn().getCase());
            }
            refreshParent();
        }

        @Override
        public void setPropertiesBlockCaption()
        {
            getDisplay().setTitle(attributesMessages.caseProperties());
        }

        private void refreshParent()
        {
            final ClassFqn fqnOfClass = metaclass.getFqn().fqnOfClass();
            metainfoService.getMetaClassesTitle(Collections.singletonList(fqnOfClass),
                    new BasicCallback<HashMap<ClassFqn, String>>(getDisplay())
                    {
                        @Override
                        protected void handleSuccess(HashMap<ClassFqn, String> value)
                        {
                            String historyToken = historyMapper.getToken(new MetaClassPlace(fqnOfClass));
                            Property<String> property = parent.getProperty();
                            if (property != null)
                            {
                                property.setValue(
                                        htmlTemplates.historyAnchor(value.get(fqnOfClass), historyToken).asString());
                            }
                        }
                    });
        }
    }

    /**
     * Реализация {@link Strategy} для Класса
     */
    class ClassStrategy extends Strategy
    {
        /**
         * Свойство "Вложенные классы"
         */
        PropertyRegistration<String> relations;
        /**
         * Свойство "Контролировать передачу ответственности между командами"
         */
        PropertyRegistration<Boolean> responsibilityTransferTableEnabled;
        /**
         * Свойство "Системный"
         */
        PropertyRegistration<Boolean> hardcoded;

        /**
         * Свойство "Жизненный цикл"
         */
        PropertyRegistration<Boolean> hasWorkflow;

        //У этих классов не будет свойства "Вложенные классы"
        private final boolean isFileClass = File.FQN.isSameClass(metaclass.getFqn());
        private final boolean isEventClass = Event.FQN.isSameClass(metaclass.getFqn());
        private final boolean isCommentClass = Comment.FQN.isSameClass(metaclass.getFqn());
        private final boolean isGeoHistoryClass = GeoHistoryRecord.FQN.isSameClass(metaclass.getFqn());
        private final boolean isSuperUserClass = SuperUser.FQN.isSameClass(metaclass.getFqn());
        private final boolean isLinkToVersionClass = LinkToVersion.FQN.isSameClass(metaclass.getFqn());

        public ClassStrategy()
        {
            hardcoded = addProperty(injector.booleanImageProperty(), adminMessages.systemMetaClass(), "Info.hardcoded");
            addHasWorkflowProperty();
            if (!(isFileClass || isEventClass || isCommentClass || isSuperUserClass || isGeoHistoryClass
                  || isLinkToVersionClass))
            {
                relations = createProperty(cmessages.metaChildren(), "Info.ClassStrategy");
            }
            initOtherProperties();
            if (metaclass.isHasResponsible())
            {
                responsibilityTransferTableEnabled = addProperty(injector.booleanImageProperty(),
                        messages.responsibilityTransferTableEnabled(), "Info.responsibilityTransferTableEnabled");
            }
            initPlannedVersionsProperties();
            initTags();
            initSettingsSet();
            initBrowserTabProperties();
        }

        @Override
        public void refresh()
        {
            super.refresh();

            Property<String> property = code.getProperty();
            if (property != null)
            {
                property.setValue(metaclass.getFqn().getId());
            }
            Property<Boolean> hardcodedProperty = hardcoded.getProperty();
            if (hardcodedProperty != null)
            {
                hardcodedProperty.setValue(metaclass.isHardcoded());
            }
            Property<Boolean> hasWorkflowProperty = hasWorkflow == null ? null : hasWorkflow.getProperty();
            if (hasWorkflowProperty != null)
            {
                hasWorkflowProperty.setValue(metaclass.isHasWorkflow());
            }
            if (responsibilityTransferTableEnabled != null)
            {
                Property<Boolean> responsibilityTransferTableEnabledProperty = responsibilityTransferTableEnabled
                        .getProperty();
                if (responsibilityTransferTableEnabledProperty != null)
                {
                    responsibilityTransferTableEnabledProperty
                            .setValue(metaclass.isResponsibilityTransferTableEnabled());
                }
            }
            if (metaClassPlannedVersionInfoPropCreator != null)
            {
                metaClassPlannedVersionInfoPropCreator.refresh(metaclass);
            }
            refreshChildren();
        }

        @Override
        public void setPropertiesBlockCaption()
        {
            getDisplay().setTitle(attributesMessages.classProperties());

        }

        /**
         * Обновление свойства "Вложенные классы"
         */
        void refreshChildren()
        {
            // все входящие связи для metaclass, которые являются типом "Вложение"
            Predicate<Relation> filter = and(right(metaclass.getFqn()), isParent());
            metainfoService.getRelatedMetaClasses(filter, true, new BasicCallback<List<MetaClassLite>>()
            {
                @Override
                protected void handleSuccess(List<MetaClassLite> value)
                {
                    metainfoUtils.sort(value);
                    SafeHtmlBuilder sb = new SafeHtmlBuilder();
                    for (Iterator<MetaClassLite> it = value.iterator(); it.hasNext(); )
                    {
                        MetaClassLite child = it.next();
                        String historyToken = historyMapper.getToken(new MetaClassPlace(child.getFqn()));
                        sb.append(htmlTemplates.historyAnchor(child.getTitle(), historyToken));
                        if (it.hasNext())
                        {
                            sb.appendEscaped(", ");
                        }
                    }
                    if (!(isFileClass || isEventClass || isCommentClass || isSuperUserClass || isGeoHistoryClass
                          || isLinkToVersionClass))
                    {
                        Property<String> property = relations.getProperty();
                        if (property != null)
                        {
                            property.setValue(sb.toSafeHtml().asString());
                        }
                    }
                }
            });
        }

        private void addHasWorkflowProperty()
        {
            if (isSystemObjectClass() || isRoot())
            {
                return;
            }
            hasWorkflow = addProperty(injector.booleanImageProperty(), adminMessages.hasWorkflow(), "Info.hasWorkflow");
        }

        private boolean isRoot()
        {
            return ru.naumen.core.shared.Constants.Root.FQN.isSameClass(metaclass.getFqn());
        }

        private boolean isSystemObjectClass()
        {
            return systemObjectClasses.stream()
                    .anyMatch(sysClass -> Objects.equals(metaclass.getFqn(), sysClass.getFqn()));
        }
    }

    /**
     * Стратегия отображения информации о {@link MetaClass}
     * Cодержbn общий код для отображения информации как о Классе , так и о Типе
     */
    abstract class Strategy
    {
        /**
         * Свойство "Название"
         */
        PropertyRegistration<String> title;
        /**
         * Свойство "Код"
         */
        PropertyRegistration<String> code;
        /**
         * Свойство "Описание"
         */
        PropertyRegistration<String> description;

        Map<String, PropertyRegistration<String>> generationRules = new HashMap<>();

        Map<String, PropertyRegistration<String>> metaClassProperties = new HashMap<>();

        PropertyRegistration<String> tags;
        Property<String> settingsSet;

        PropertyRegistration<Boolean> tabTitleAttributeOverridden;

        PropertyRegistration<String> tabTitleAttribute;

        PropertyRegistration<Boolean> quotingEnabled;
        PropertyRegistration<String> quotaCode;
        PropertyRegistration<String> quotaRemainder;
        PropertyRegistration<String> quotaExpirationDate;

        public Strategy()
        {
            title = createProperty(cmessages.title(), "Info.title");
            code = createProperty(cmessages.code(), "Info.code");
            description = createProperty(cmessages.description(), "Info.description");
        }

        /**
         * Добавляет кнопки на форму
         */
        public void fillButtons()
        {
            boolean hasEditPermission = hasPermission(permissions, PermissionType.EDIT, metaclass);
            boolean hasDeletePermission = hasPermission(permissions, PermissionType.DELETE, metaclass);
            if (hasEditPermission)
            {
                addButton(ButtonCode.EDIT, cmessages.edit(), event -> edit());
            }
            if (!metaclass.isHardcoded())
            {
                if (hasEditPermission)
                {
                    addButton(ButtonCode.COPY, cmessages.copy(), event -> copy());
                    if (metainfoUtils.isRemoved(metaclass))
                    {
                        addButton(ButtonCode.RESTORE, cmessages.restore(), event -> unremove());
                    }
                    else
                    {
                        addButton(ButtonCode.REMOVE, cmessages.remove(), event -> remove());
                    }
                }
                if (hasDeletePermission)
                {
                    addButton(ButtonCode.DELETE, cmessages.delete(), event -> delete());
                }
            }
            ClassFqn parent = metaclass.getParent();
            boolean isStateResponsibleEvent = AbstractStateResponsibleEvent.FQN.equals(metaclass.getFqn())
                                              || AbstractStateResponsibleEvent.FQN.equals(parent);
            if (MetainfoUtils.hasSearch(metaclass) && !isStateResponsibleEvent)
            {
                refreshButton = addButton(ButtonCode.REFRESH, cmessages.reindex(), new ClickHandler()
                {
                    @Override
                    public void onClick(ClickEvent event)
                    {
                        reindex();
                    }
                });
                reindexStatus();
            }
            toolBar.bind();
        }

        /**
         * Обновляет свойства метаинформации
         */
        public void refresh()
        {
            Property<String> property = title.getProperty();
            if (property != null)
            {
                property.setValue(formatters.formatText(metaclass.getTitle()));
            }
            Property<String> descrProperty = description.getProperty();
            if (descrProperty != null)
            {
                descrProperty.setValue(formatters.formatText(metaclass.getDescription()));
            }
            refreshTags();
            refreshSettingsSet();
            refreshOtherProperties();
            refreshBrowserTabProperties();
            refreshQuotingProperties();
        }

        protected <T> PropertyRegistration<T> addProperty(Property<T> property, String caption, String debugId)
        {
            property.setCaption(caption);
            property.ensureDebugId(debugId);
            return getDisplay().add(property);
        }

        protected PropertyRegistration<String> createProperty(String caption, String debugId)
        {
            Property<String> textProperty = injector.htmlTextProperty();
            return addProperty(textProperty, caption, debugId);
        }

        protected void initBrowserTabProperties()
        {
            Property<Boolean> property = injector.booleanImageProperty();
            property.setCaption(messages.overrideTabTitleAttribute());
            DebugIdBuilder.ensureDebugId(property, "Info.overrideTabTitleAttribute");
            tabTitleAttributeOverridden = getDisplay().add(property);
        }

        protected void initOtherProperties()
        {
            ClassFqn fqn = metaclass.getFqn();
            if (ServiceCall.CLIENT_TYPES.contains(fqn.fqnOfClass()))
            {
                metaClassProperties.put(DEFAULT_SC_TYPE,
                        createProperty(cmessages.serviceCallDefaultParameters(), "Info.scDefaultParams"));
            }
            if (!Root.FQN.equals(fqn) && !File.FQN.isSameClass(fqn))
            {
                initNameGeneratedValue();
                initNumberGeneratedValue();
            }
        }

        protected void initPlannedVersionsProperties()
        {
            if (metaClassPlannedVersionInfoPropCreator != null)
            {
                metaClassPlannedVersionInfoPropCreator.init(getDisplay());
                metaClassPlannedVersionInfoPropCreator.addPlannedVersionProperties(metaclass);
            }
        }

        protected void initTags()
        {
            ClassFqn fqn = metaclass.getFqn();
            if (!Root.FQN.equals(fqn) && !Constants.SYSTEM_METACLASSES.contains(fqn.fqnOfClass())
                && !fqn.getId().endsWith(AbstractStateResponsibleEvent.SUFFIX))
            {
                tags = createProperty(tagsMessages.tags(), "Info.tags");
            }
        }

        protected void initSettingsSet()
        {
            settingsSet = settingsSetOnFormCreator.createFieldOnCard(getDisplay());
        }

        protected void refreshQuotingProperties()
        {
            if (null == metaClassQuota && null != quotingEnabled)
            {
                quotingEnabled.unregister();
                quotingEnabled = null;
                quotaCode.unregister();
                quotaCode = null;
                quotaRemainder.unregister();
                quotaRemainder = null;
                quotaExpirationDate.unregister();
                quotaExpirationDate = null;
            }
            else if (null != metaClassQuota && null == quotingEnabled)
            {
                PropertyRegistration<?> addAfter = null == tabTitleAttribute ? tabTitleAttributeOverridden
                        : tabTitleAttribute;

                Property<Boolean> quotingEnabledProperty = injector.booleanImageProperty();
                quotingEnabledProperty.setCaption(messages.quotingEnabled());
                DebugIdBuilder.ensureDebugId(quotingEnabledProperty, "Info.quotingEnabled");
                quotingEnabled = display.addPropertyAfter(quotingEnabledProperty, addAfter);

                Property<String> quotaCodeProperty = injector.textProperty();
                quotaCodeProperty.setCaption(messages.quotaName());
                DebugIdBuilder.ensureDebugId(quotaCodeProperty, "Info.quotaCode");
                quotaCode = display.addPropertyAfter(quotaCodeProperty, quotingEnabled);

                Property<String> quotaRemainingObjectCountProperty = injector.textProperty();
                quotaRemainingObjectCountProperty.setCaption(messages.quotaRemainder());
                DebugIdBuilder.ensureDebugId(quotaRemainingObjectCountProperty, "Info.quotaRemainingObjectCount");
                quotaRemainder = display.addPropertyAfter(quotaRemainingObjectCountProperty, quotaCode);

                Property<String> quotaExpirationDateProperty = injector.textProperty();
                quotaExpirationDateProperty.setCaption(messages.quotaExpirationDate());
                DebugIdBuilder.ensureDebugId(quotaExpirationDateProperty, "Info.quotaExpirationDate");
                quotaExpirationDate = display.addPropertyAfter(quotaExpirationDateProperty, quotaRemainder);
            }
            if (null != metaClassQuota)
            {
                Objects.requireNonNull(quotingEnabled.getProperty()).setValue(true);
                Objects.requireNonNull(quotaCode.getProperty()).setValue(metaClassQuota.getCode());
                Objects.requireNonNull(quotaRemainder.getProperty()).setValue(
                        Long.toString(metaClassQuota.getRemainder()));
                Objects.requireNonNull(quotaExpirationDate.getProperty()).setValue(
                        formatters.formatDate(metaClassQuota.getExpirationDate()));
            }
        }

        protected void refreshOtherProperties()
        {
            refreshGeneratedValues(AbstractBO.TITLE, AbstractCasedBO.NUMBER);
            final PropertyRegistration<String> pr = metaClassProperties.get(DEFAULT_SC_TYPE);
            if (pr != null)
            {
                IProperties mcProps = metaclass.getProperties();
                final DtObject agreement = mcProps.getProperty(DEFAULT_CLIENT_AGREEMENT);
                final DtObject service = mcProps.getProperty(DEFAULT_CLIENT_SERVICE);
                final ClassFqn scCase = mcProps.getProperty(DEFAULT_SC_TYPE);
                if (scCase != null)
                {

                    metainfoService.getMetaClassesTitle(Arrays.asList(scCase),
                            new BasicCallback<HashMap<ClassFqn, String>>(getDisplay())
                            {
                                @Override
                                protected void handleSuccess(HashMap<ClassFqn, String> map)
                                {
                                    setDefaultAssocPropValue(pr, agreement, service, map);
                                }
                            });
                }
                else
                {
                    setDefaultAssocPropValue(pr, agreement, service, null);
                }
            }
        }

        protected void refreshTags()
        {
            if (null == tags)
            {
                return;
            }
            if (null != metaClassTags)
            {
                if (null != tags.getProperty())
                {
                    tags.getProperty().setValue(tagsFormatter.formatToAnchors(metaClassTags).asString()); //NOSONAR
                }
                Attention attention = getDisplay().getAttention();
                /*
                 FIXME Все последующие операции направлены на то, чтобы не затереть уже существующее предупреждение
                  Исправить на нормальный код как только будет сделана задача на
                  возможность нескольких предупреждений
                */
                String oldMsg = attention.getHTML();
                String newMsg = tagsMessages.disabledMetaClassWarning();
                if (!tagService.isElementEnabled(metaClassTags))
                {
                    if (oldMsg == null || !oldMsg.contains(newMsg))
                    {
                        attention.setHTML(SafeHtmlUtils.htmlEscape( // NOPMD NSDPRD-28509 unsafe html
                                (StringUtilities.isEmpty(oldMsg)
                                        ? StringUtilities.EMPTY : oldMsg + "\n")
                                + newMsg));
                    }
                }
                else if (oldMsg.contains(newMsg))
                {
                    String msg = oldMsg.replace(newMsg, "");
                    if (msg.trim().isEmpty())
                    {
                        attention.clear();
                    }
                    else
                    {
                        attention.setHTML(SafeHtmlUtils.htmlEscape(msg)); // NOPMD NSDPRD-28509 unsafe html
                    }
                }
            }
        }

        protected void refreshSettingsSet()
        {
            if (null == settingsSet)
            {
                return;
            }
            if (null != metaclass.getSettingsSet())
            {

                settingsSetOnFormCreator.setValueOnCardProperty(metaclass.getSettingsSet(), settingsSet);
            }
            else
            {
                settingsSet.setValue("");
            }
        }

        /**
         * Устанавливает название блока со свойствами
         */
        abstract void setPropertiesBlockCaption();

        private void initGeneratedValue(String code, String caption)
        {
            PropertyRegistration<String> registration = generationRules.get(code);
            if (null == registration)
            {
                registration = createProperty(caption, "Info." + code + "rule");
                generationRules.put(code, registration);
            }
        }

        private void initNameGeneratedValue()
        {
            ClassFqn fqn = metaclass.getFqn();
            Attribute attr = metaclass.getAttribute(AbstractBO.TITLE);
            if (attr != null && !Employee.FQN.isSameClass(fqn))
            {
                initGeneratedValue(AbstractBO.TITLE, cmessages.nameRuleFull(attr.getTitle()));
            }
        }

        private void initNumberGeneratedValue()
        {
            Attribute attr = metaclass.getAttribute(ServiceCall.NUMBER);
            if (attr != null)
            {
                initGeneratedValue(ServiceCall.NUMBER, cmessages.numberRuleFull(attr.getTitle()));
            }
        }

        private void refreshBrowserTabProperties()
        {
            Objects.requireNonNull(tabTitleAttributeOverridden.getProperty()).setValue(
                    metaclass.isTabTitleAttributeOverridden());
            if (metaclass.isTabTitleAttributeOverridden() && null == tabTitleAttribute)
            {
                Property<String> property = injector.textProperty();
                property.setCaption(messages.tabTitleAttribute());
                DebugIdBuilder.ensureDebugId(property, "Info.tabTitleAttribute");
                tabTitleAttribute = getDisplay().addPropertyAfter(property, tabTitleAttributeOverridden);
            }
            else if (!metaclass.isTabTitleAttributeOverridden() && null != tabTitleAttribute)
            {
                tabTitleAttribute.unregister();
                tabTitleAttribute = null;
            }
            if (null != tabTitleAttribute)
            {
                String tabTitleAttribute = metaclass.getTabTitleAttribute();
                String title = null == tabTitleAttribute || !metaclass.hasAttribute(tabTitleAttribute)
                        ? StringUtilities.EMPTY
                        : Objects.requireNonNull(metaclass.getAttribute(tabTitleAttribute)).getTitle();
                Objects.requireNonNull(this.tabTitleAttribute.getProperty()).setValue(title);
            }
        }

        private void refreshGeneratedValues(String... codes)
        {
            if (!File.FQN.isSameClass(metaclass.getFqn()))
            {
                for (String code : codes)
                {
                    PropertyRegistration<String> pr = generationRules.get(code);
                    if (null != pr)
                    {
                        Attribute attr = metaclass.getAttribute(code);
                        Property<String> property = pr.getProperty();
                        if (property != null)
                        {
                            if (Boolean.TRUE.equals(attr.isUseGenerationRule()))
                            {
                                property.setValue(attr.getGenerationRule());
                            }
                            else if (Boolean.TRUE.equals(attr.isComposite()))
                            {
                                property.setValue(attr.getTemplate());
                            }
                            else
                            {
                                property.setValue(cmessages.empty());
                            }
                        }
                    }
                }
            }
        }

        private void setDefaultAssocPropValue(final PropertyRegistration<String> pr, final DtObject agreement,
                final DtObject service, final HashMap<ClassFqn, String> titleInfo)
        {
            metainfoService.getSettings(new BasicCallback<Settings>()
            {
                @Override
                protected void handleSuccess(Settings settings)
                {
                    ScCaseFieldsOrderSettings orderSc = settings.getScParameters().getOrderScSetting();
                    SafeHtml titleHtml = null;

                    if (titleInfo != null)
                    {
                        String historyToken = historyMapper
                                .getToken(new MetaClassPlace(titleInfo.keySet().iterator().next()));
                        titleHtml = htmlTemplates.historyAnchor(titleInfo.values().iterator().next(), historyToken,
                                "sc", SafeHtmlUtils.EMPTY_SAFE_HTML);
                    }

                    SafeHtmlBuilder agsHtmlBuilder = new SafeHtmlBuilder();
                    if (agreement != null)
                    {
                        agsHtmlBuilder.append(formatters.linkToEntity(agreement, "agreement"));
                    }
                    if (service != null)
                    {
                        agsHtmlBuilder.appendEscaped(" / ").append(formatters.linkToEntity(service, "service"));
                    }
                    SafeHtml agsHtml = agsHtmlBuilder.toSafeHtml();

                    SafeHtmlBuilder shb = new SafeHtmlBuilder();

                    if (orderSc.isCaseMain())
                    {
                        if (titleHtml != null)
                        {
                            shb.append(titleHtml);
                            if (!agsHtml.asString().isEmpty())
                            {
                                shb.appendEscaped(" / ");
                            }
                        }
                        shb.append(agsHtml);
                    }
                    else
                    {
                        shb.append(agsHtml);
                        if (titleHtml != null)
                        {
                            if (!agsHtml.asString().isEmpty())
                            {
                                shb.appendEscaped(" / ");
                            }
                            shb.append(titleHtml);
                        }
                    }

                    Property<String> property = pr.getProperty();
                    if (property != null)
                    {
                        property.setValue(shb.toSafeHtml().asString());
                    }
                }
            });
        }
    }

    @Inject
    Formatters formatters;
    @Inject
    AdminMetainfoServiceAsync metainfoService;
    @Inject
    MetainfoUtils metainfoUtils;
    @Inject
    AdminDialogMessages messages;
    @Inject
    CommonMessages cmessages;
    @Inject
    private AdminMessages adminMessages;
    @Inject
    MGinjector injector;
    @Inject
    PlaceHistoryMapper historyMapper;
    @Inject
    CommonHtmlTemplates htmlTemplates;
    @Inject
    ButtonFactory buttonFactory;
    @Inject
    CommonUtils commonsUtils;
    @Inject
    AttributesMessages attributesMessages;
    @Inject
    private Provider<EditMetaclassDialogPresenter> editMetaClassPresenterProvider;
    @Inject
    private TagsMessages tagsMessages;

    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;
    @Inject
    private TagPropertyFormatter tagsFormatter;
    @Inject
    private TagServiceAsync tagService;
    @com.google.inject.Inject(optional = true)
    @CheckForNull
    private MetaClassPlannedVersionInfoPropCreator metaClassPlannedVersionInfoPropCreator;

    /**
     * {@link MetaClass} о котором отображается информация
     */
    MetaClass metaclass;

    List<DtObject> metaClassTags;

    QuotaSnapshot metaClassQuota;

    private PermissionHolder permissions;

    /**
     * Стратегия отображения информации
     */
    Strategy strategy;

    protected final ToolBarDisplayMediator<MetaClass> toolBar;
    protected ButtonPresenter<MetaClass> refreshButton = null;
    private List<MetaClassLite> systemObjectClasses = new ArrayList<>();

    @Inject
    SearchServiceAsync searchService;

    @Inject
    public InfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    public void init(MetaClass metaclass, List<DtObject> tags, @Nullable QuotaSnapshot quota,
            PermissionHolder permissions)
    {
        this.metaclass = metaclass;
        this.metaClassTags = tags;
        this.metaClassQuota = quota;
        this.permissions = permissions;
        metainfoService.getDescendantClasses(AbstractSystemObject.FQN, false, new BasicCallback<List<MetaClassLite>>(
                getDisplay())
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> systemObjectClasses)
            {
                InfoPresenter.this.systemObjectClasses = systemObjectClasses;
                bind();
            }
        });
    }

    @Override
    public void onMetaClassQuotaUpdated(MetaClassQuotaUpdatedEvent event)
    {
        if (null == metaclass || !Objects.equals(metaclass.getFqn(), event.getFqn()))
        {
            return;
        }
        metaClassQuota = event.getQuota();
        if (isRevealed)
        {
            strategy.refreshQuotingProperties();
        }
    }

    @Override
    public void onMetaClassTagsChanged(MetaClassTagsChangedEvent event)
    {
        if (null == metaclass || !Objects.equals(metaclass.getFqn(), event.getClassFqn()))
        {
            return;
        }
        this.metaClassTags = event.getTags();
        if (isRevealed)
        {
            strategy.refreshTags();
        }
    }

    @Override
    public void onMetainfoUpdated(MetainfoUpdatedEvent e)
    {
        if (metaclass == null || ObjectUtils.equals(metaclass.getFqn(), e.getMetainfo().getFqn()))
        {
            this.metaclass = e.getMetainfo();
            refreshDisplay();
        }
    }

    @Override
    public void refreshDisplay()
    {
        getDisplay().getToolBar().clean();
        strategy.fillButtons();
        strategy.refresh();
    }

    public void setMetaClass(MetaClass metaclass)
    {
        this.metaclass = metaclass;
    }

    protected void initStrategy()
    {
        if (metaclass.getFqn().isClass())
        {
            strategy = new ClassStrategy();
        }
        else
        {
            strategy = new CaseStrategy();
        }
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(MetainfoUpdatedEvent.getType(), this));
        registerHandler(eventBus.addHandler(MetaClassTagsChangedEvent.TYPE, this));
        registerHandler(eventBus.addHandler(MetaClassQuotaUpdatedEvent.TYPE, this));
        registerHandler(eventBus.addHandler(MetaClassPermissionsUpdatedEvent.TYPE, this));

        initStrategy();
        strategy.setPropertiesBlockCaption();
        getDisplay().setEventBus(eventBus);
        getDisplay().asWidget().addStyleName(AdminWidgetResources.INSTANCE.attributeList().classInfo());
        refreshDisplay();
        if (eventBus != null)
        {
            //Для работы нового списка атрибутов, необходимо рассчитать смещение "прилипающего" заголовка.
            //В случае, если список атрибутов успевает загрузиться быстрее "Свойств класса",
            //смещение рассчитывается без их учета.
            //Однако, список атрибутов ловит события открытия/закрытия блоков (в том числе, "Свойств класса")
            //и перерасчитывает смещение. Для устранения возможных проблем, кидаем событие явно.
            eventBus.fireEvent(new ExpandCollapseEvent(true));
        }
    }

    @Override
    protected void onUnbind()
    {
    }

    void copy()
    {
        CopyMetaclassDialogPresenter ap = injector.copyMetaclassDialogPresenter();
        ap.init(metaclass, new BasicCallback<MetaClass>(getDisplay()));
        ap.bind();
    }

    void delete()
    {
        commonsUtils.delClassMetainfo(metaclass, new BasicCallback<Void>(getDisplay()));
    }

    void edit()
    {
        EditMetaclassDialogPresenter presenter = editMetaClassPresenterProvider.get();
        presenter.init(metaclass, new BasicCallback<MetaClass>(getDisplay())
        {
            @Override
            protected void handleSuccess(MetaClass value)
            {
                if (value != null)
                {
                    metaclass = value;
                    eventBus.fireEvent(new MetainfoUpdatedEvent(value));
                    refreshDisplay();
                }
            }
        });
        presenter.bind();
    }

    void reindex()
    {
        searchService.reindex(metaclass.getFqn(), (Button)refreshButton.getDisplay());
    }

    void reindexStatus()
    {
        searchService.reindexStatus(metaclass.getFqn(), (Button)refreshButton.getDisplay());
    }

    void remove()
    {
        commonsUtils.archiveClassMetainfo(metaclass, new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass value)
            {
                if (value != null)
                {
                    metaclass = value;
                    refreshDisplay();
                }
            }
        });
    }

    void unremove()
    {
        commonsUtils.restoreClassMetainfo(metaclass, new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass value)
            {
                if (value != null)
                {
                    metaclass = value;
                    refreshDisplay();
                }
            }
        });
    }

    @SuppressWarnings("unchecked")
    private ButtonPresenter<MetaClass> addButton(String code, String title, ClickHandler handler)
    {
        ButtonPresenter<MetaClass> bp = (ButtonPresenter<MetaClass>)buttonFactory.create(code, title, handler);
        toolBar.add(bp);
        return bp;
    }

    @Override
    public void onMetaClassPermissionsUpdated(MetaClassPermissionsUpdatedEvent event)
    {
        if (null == metaclass || !Objects.equals(metaclass.getFqn(), event.getFqn()))
        {
            return;
        }
        this.permissions = event.getPermissions();
    }
}
