/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import com.google.inject.ImplementedBy;

import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.ToolPanelDisplay;
import ru.naumen.core.client.mvp.Display;

/**
 * Дисплей редактируемой тул панели
 *
 * <AUTHOR>
 * @since 26 мая 2015 г.
 */
@ImplementedBy(EditableToolPanelDisplayImpl.class)
public interface EditableToolPanelDisplay extends Display
{
    /**
     * Добавить дисплей тул панели для просмотра
     */
    void add(ToolPanelDisplay display);

    /**
     * Получить иконку редактирования (шестеренку)
     */
    FontIconDisplay<?> getEditIcon();
}