package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import static ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.MAX_CODE_LENGTH;
import static ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.MAX_TITLE_LENGTH;

import java.util.Collection;
import java.util.Map;

import java.util.HashMap;

import com.google.inject.Inject;

import ru.naumen.core.client.tree.metainfo.MetaClassMultiSelectionModelSameClassOnly;
import ru.naumen.core.client.validation.MetainfoEscalationSchemeKeyCodeValidator;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyObjectValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.TextAreaProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindTextBoxFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.EscalationSchemeFormPropertyCode;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetBindDelegate;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetRefreshDelegate;

/**
 * <AUTHOR>
 * @since 24.07.2012
 *
 */
public class EscalationSchemeFormPropertyControllerFactoryImpl<F extends ObjectForm>
        extends PropertyControllerFactorySyncImpl<EscalationScheme, F>
{
    @Inject
    PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<String, TextAreaProperty> textAreaPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<SelectItem, ListBoxProperty> listBoxPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxWithEmptyPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<Collection<DtObject>, PropertyBase<Collection<DtObject>,
            PopupValueCellTree<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly>>> metaClassTreePropertyFactory;

    @Inject
    EscalationSchemeTargetTimerRefreshDelegateImpl<F> timerRefreshDelegate;
    @Inject
    EscalationSchemeCodeRefreshDelegateImpl codeRefreshDelegate;
    @Inject
    EscalationSchemeTargetObjectsVCHDelegateImpl targetObjectsVCHDelegate;
    @Inject
    EscalationSchemeTitleVCHDelegateImpl titleVCHDelegate;

    @Inject
    PropertyDelegateBindTextBoxFactory textBoxBindDelegateFactory;

    @Inject
    SettingsSetBindDelegate timerSettingsSetBindDelegate;
    @Inject
    SettingsSetRefreshDelegate timerSettingsSetRefreshDelegate;

    private final Map<Validator<String>, String> titleValidators = new HashMap<>();
    private final Map<Validator<String>, String> codeValidators = new HashMap<>();
    private final Map<Validator<SelectItem>, String> timerValidators = new HashMap<>();
    private final Map<Validator<Collection<DtObject>>, String> objectsValidators = new HashMap<>();

    @Inject
    public void setUpValidators(NotEmptyCollectionValidator<Collection<DtObject>> notEmptyCollectionValidator,
            NotEmptyValidator notEmptyValidator,
            NotEmptyObjectValidator<SelectItem> notEmptySelectItemValidator,
            MetainfoEscalationSchemeKeyCodeValidator metainfoEscalationSchemeKeyCodeValidator)
    {
        titleValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        codeValidators.put(metainfoEscalationSchemeKeyCodeValidator,
                PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        codeValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        timerValidators.put(notEmptySelectItemValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        objectsValidators.put(notEmptyCollectionValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }

    @Override
    protected void build()
    {
        PropertyDelegateBind<String, TextBoxProperty> titleBindDelegate = textBoxBindDelegateFactory
                .create(MAX_TITLE_LENGTH);
        PropertyDelegateBind<String, TextBoxProperty> codeBindDelegate = textBoxBindDelegateFactory
                .create(MAX_CODE_LENGTH);
        //@formatter:off
        register(EscalationSchemeFormPropertyCode.TITLE, textBoxPropertyFactory)
            .setBindDelegate(titleBindDelegate)
            .setValidators(titleValidators)
            .setVchDelegate(titleVCHDelegate);
        register(EscalationSchemeFormPropertyCode.CODE, textBoxPropertyFactory)
            .setBindDelegate(codeBindDelegate)
            .setRefreshDelegate(codeRefreshDelegate)
            .setValidators(codeValidators);
        register(EscalationSchemeFormPropertyCode.DESCRIPTION, textAreaPropertyFactory);
        register(EscalationSchemeFormPropertyCode.TARGET_OBJECTS, metaClassTreePropertyFactory)
            .setVchDelegate(targetObjectsVCHDelegate)
            .setValidators(objectsValidators);
        register(EscalationSchemeFormPropertyCode.TIMER, listBoxPropertyFactory)
            .setRefreshDelegate(timerRefreshDelegate)
            .setValidators(timerValidators);
        register(EscalationSchemeFormPropertyCode.SETTINGS_SET, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(timerSettingsSetRefreshDelegate)
                .setBindDelegate(timerSettingsSetBindDelegate);
        //@formatter:on
    }
}