/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap.ValueMapItemFormContext;

import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationValueMapItemFormContext extends ValueMapItemFormContext
{
    @Inject
    public EscalationValueMapItemFormContext(@Assisted Catalog catalog, @Assisted boolean folder,
            @Assisted DtObject catalogItem)
    {
        super(catalog, folder, catalogItem);
    }
}