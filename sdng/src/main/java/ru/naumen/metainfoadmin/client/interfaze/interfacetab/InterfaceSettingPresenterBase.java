package ru.naumen.metainfoadmin.client.interfaze.interfacetab;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.components.block.TitledBlockWithToolDisplay;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;

/**
 * Базовый функционал презентера настройки на вкладке Интерфейс
 *
 * <AUTHOR>
 * @since 31.08.22
 */
public abstract class InterfaceSettingPresenterBase<D extends TitledBlockWithToolDisplay> extends BasicPresenter<D>
{
    protected final CommonMessages cmessages;
    protected final ButtonFactory buttonFactory;
    protected final ToolBarDisplayMediator<InterfaceSettingsContext> toolBar;
    protected InterfaceSettingsContext context;

    public InterfaceSettingPresenterBase(D display, EventBus eventBus, CommonMessages cmessages,
            ButtonFactory buttonFactory)
    {
        super(display, eventBus);
        this.cmessages = cmessages;
        this.buttonFactory = buttonFactory;
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    public void init(InterfaceSettingsContext context)
    {
        this.context = context;
    }

    @Override
    public final void refreshDisplay()
    {
        toolBar.refresh(context);
        refreshContent();
    }

    @SuppressWarnings("unchecked")
    protected void bindToolBar()
    {
        CommandParam<InterfaceSettingsContext, InterfaceSettingsContext> editParam =
                new CommandParam<InterfaceSettingsContext, InterfaceSettingsContext>(
                        context);
        toolBar.add((ButtonPresenter<InterfaceSettingsContext>)buttonFactory.create(ButtonCode.EDIT, cmessages.edit(),
                getEditCommandId(), editParam));
        toolBar.bind();
    }

    @Override
    protected final void onBind()
    {
        getDisplay().setCaption(getCaption());
        bindToolBar();
        bindContent();
    }

    /**
     * Выполнить bind содержимого
     */
    protected abstract void bindContent();

    /**
     * Обновить содержимое
     */
    protected abstract void refreshContent();

    /**
     * Название блока с настройкой
     */
    protected abstract String getCaption();

    /**
     * Идентификатор команды редактировать
     */
    protected abstract String getEditCommandId();
}
