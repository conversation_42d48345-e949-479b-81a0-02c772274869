package ru.naumen.metainfoadmin.client.scheduler.command;

import java.util.logging.Logger;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskMessages;

/**
 * Команда удаления правила выполнения задачи планировщика
 * <AUTHOR>
 */
public class DeleteTriggerCommand extends ObjectCommandImpl<DtoContainer<Trigger>, DtoContainer<Trigger>>
{
    @Inject
    private MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    private SchedulerTaskMessages messages;
    @Inject
    protected CommonMessages cmessages;

    protected final Logger LOG = Logger.getLogger(this.getClass().getName());

    @Inject
    public DeleteTriggerCommand(@Assisted CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>> param,
            Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    public void execute(CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>> param)
    {
        LOG.finest("Removing trigger: " + param.getValue().getCode() + " from scheduler task: "
                   + param.getValue().get().getSchTaskCode());
        super.execute(param);
    }

    @Override
    protected String getDialogMessage(DtoContainer<Trigger> trigger)
    {
        return cmessages.confirmDeleteQuestion(messages.trigger(), trigger.getTitle());
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    protected void onDialogSuccess(CommandParam<DtoContainer<Trigger>, DtoContainer<Trigger>> param)
    {
        TriggerCommandParam p = (TriggerCommandParam)param;
        metainfoModificationService.deleteTrigger(p.getSchedulerTask(), p.getValue().get(),
                new CallbackDecorator<Void, DtoContainer<Trigger>>(param.getCallbackSafe())
                {
                    @Override
                    protected DtoContainer<Trigger> apply(Void from)
                    {
                        return null;
                    }
                });
    }

}
