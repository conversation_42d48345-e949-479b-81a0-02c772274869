package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.HAS_DEFAULT_VALUE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPOSITE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.COMPUTABLE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DEFAULT_BY_SCRIPT;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.SCRIPT_FOR_DEFAULT;

import ru.naumen.metainfoadmin.client.widgets.properties.ScriptComponentEditProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 11.10.2012
 *
 */
public class ScriptForDefaultRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, ScriptDto, ScriptComponentEditProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ScriptComponentEditProperty property,
            AsyncCallback<Boolean> callback)
    {
        Boolean defaultByScript = context.getPropertyValues().getProperty(DEFAULT_BY_SCRIPT);
        Boolean hasDefault = context.getContextValues().getProperty(HAS_DEFAULT_VALUE);
        Boolean computable = context.getPropertyValues().getProperty(COMPUTABLE);
        Boolean definalbleByTemplate = context.getPropertyValues().getProperty(COMPOSITE);

        boolean isByScript = defaultByScript && hasDefault && !computable && !definalbleByTemplate;
        if (!isByScript)
        {
            context.setProperty(SCRIPT_FOR_DEFAULT, null);
        }
        callback.onSuccess(isByScript);
    }
}
