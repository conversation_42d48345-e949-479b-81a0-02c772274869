package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.show;

import java.util.Map;

import jakarta.inject.Inject;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.PresentationRefreshDelegateImpl;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Обновление значения свойства "Представление для отображения" на форме добавления атрибута
 * <AUTHOR>
 * @since 17.05.2012
 */
public class ShowPresentationRefreshDelegateAddImpl<F extends ObjectForm> extends PresentationRefreshDelegateImpl<F>
{
    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        if (AttributeOfRelatedObjectSettings.CODE.equalsIgnoreCase(typeCode))
        {
            String relatedObjectMetaClass = context.getPropertyValues()
                    .getProperty(AttributeFormPropertyCode.RELATED_OBJECT_METACLASS);

            String attrFqn =
                    context.getPropertyValues().getProperty(AttributeFormPropertyCode.RELATED_OBJECT_ATTRIBUTE);
            if (relatedObjectMetaClass == null || attrFqn == null)
            {
                callback.onSuccess(false);
                return;
            }

            String attrCode = AttributeFqn.parse(attrFqn).getCode();
            metainfoService.getMetaClass(AttributeFqn.parse(attrFqn).getClassFqn(), new BasicCallback<MetaClass>()
            {
                @Override
                protected void handleSuccess(MetaClass metaClass)
                {
                    if (metaClass.hasAttribute(attrCode))
                    {
                        updatePresentations(context, property, metaClass.getAttribute(attrCode).getType().getCode());
                        callback.onSuccess(true);
                    }
                    else
                    {
                        callback.onSuccess(false);
                    }
                }
            });

        }
        else
        {
            updatePresentations(context, property, typeCode);
            callback.onSuccess(true);
        }
    }

    private void updatePresentations(PropertyContainerContext context, ListBoxProperty property, String typeCode)
    {
        Map<String, Object> settings = getPrsSettings(typeCode, context);
        initPrsSelectList(property.getValueWidget(), attrService.getShow(typeCode, settings), settings);
        String prsCode = attrService.getShowDef(typeCode, settings);
        property.trySetObjValue(prsCode);
        context.setProperty(AttributeFormPropertyCode.SHOW_PRS, prsCode);
    }
}
