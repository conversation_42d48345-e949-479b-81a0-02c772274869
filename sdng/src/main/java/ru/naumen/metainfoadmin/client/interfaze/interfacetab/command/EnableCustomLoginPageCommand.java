package ru.naumen.metainfoadmin.client.interfaze.interfacetab.command;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataResponse;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.interfacesettings.dispatch.EditCustomLoginFormAction;
import ru.naumen.core.shared.interfacesettings.dispatch.ValidateCustomLoginFormAction;
import ru.naumen.metainfo.shared.loginpage.CustomLoginPageSettings;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContextChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.loginpage.CustomLoginPageMessages;

/**
 * Команда включения пользовательской формы входа в систему
 *
 * <AUTHOR>
 * @since 11.04.2018
 */
public class EnableCustomLoginPageCommand extends BaseCommandImpl<InterfaceSettingsContext, InterfaceSettingsContext>
{
    public static final String ID = "EnableLoginPageCommand";

    @Inject
    private CustomLoginPageMessages messages;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private EventBus eventBus;
    @Inject
    private Dialogs dialogs;

    @Inject
    public EnableCustomLoginPageCommand(
            @Assisted CommandParam<InterfaceSettingsContext, InterfaceSettingsContext> param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<InterfaceSettingsContext, InterfaceSettingsContext> param)
    {
        CustomLoginPageSettings loginPageSettings = param.getValue().getSettings().getCustomLoginPageSettings();

        if (loginPageSettings.getPageTemplate().isEmpty())
        {
            dialogs.error(messages.emptyTemplate());
            return;
        }

        dispatch.execute(new ValidateCustomLoginFormAction(), new BasicCallback<SimpleResult<Void>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<Void> response)
            {
                confirmAndEnable(loginPageSettings.getAdditionalDefaultPageAhref());
            }
        });
    }

    @Override
    public boolean isPossible(Object input)
    {
        return input instanceof InterfaceSettingsContext
               && (!((InterfaceSettingsContext)input).getSettings().getCustomLoginPageSettings().isEnabled());
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

    /**
     * Диалог подтверждения включения с последующим включением
     * @param additionalAhref
     */
    private void confirmAndEnable(String additionalAhref)
    {
        dialogs.question(messages.editLoginPageSettings(), messages.confirm(additionalAhref),
                new DialogCallback()
                {
                    @Override
                    protected void onYes(final Dialog widget)
                    {
                        dispatch.execute(new EditCustomLoginFormAction(null, true),
                                new BasicCallback<GetInterfaceTabDataResponse>()
                                {
                                    @Override
                                    protected void handleSuccess(GetInterfaceTabDataResponse response)
                                    {
                                        eventBus.fireEvent(new InterfaceSettingsContextChangedEvent(response));
                                    }
                                });
                        widget.destroy();
                    }
                });
    }
}
