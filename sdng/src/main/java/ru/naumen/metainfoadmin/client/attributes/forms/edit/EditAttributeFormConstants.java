package ru.naumen.metainfoadmin.client.attributes.forms.edit;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormConstants;

/**
 * <AUTHOR>
 * @since 01.06.2012
 */
public class EditAttributeFormConstants implements AttributeFormConstants<ObjectFormEdit>
{
    // @formatter:off
    private static final String[] PROPERTIES = new String[] {
        INHERIT,
        TITLE,
        CODE,
        ATTR_TYPE,
        COMPUTABLE,
        DETERMINABLE,
        DETERMINER,
        REQUIRED,
        EDITABLE,
        NEED_STORE_UNITS,
        INTERVAL_AVAILABLE_UNITS,
        COMPLEX_RELATION,
        COMPLEX_RELATION_ATTR_GROUP,
        COMPLEX_EMPLOYEE_ATTR_GROUP,
        COMPLEX_TEAM_ATTR_GROUP,
        COMPLEX_OU_ATTR_GROUP,
        COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW,
        EDIT_ON_COMPLEX_FORM_ONLY,
        EDITABLE_IN_LISTS,
        USE_GEN_RULE,
        GEN_RULE,
        UNIQUE,
        TARGET,
        PERMITTED_TYPES,
        FILTERED_BY_SCRIPT,
        SCRIPT_FOR_FILTRATION,
        COMPUTABLE_ON_FORM,
        COMPUTABLE_ON_FORM_SCRIPT,
        SCRIPT,
        SHOW_PRS,
        EDIT_PRS,
        STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE,
        HAS_GROUP_SEPARATORS,
        DIGITS_COUNT_RESTRICTION,
        SUGGEST_CATALOG,
        SELECT_SORTING,
        DEFAULT_VALUE,
        SCRIPT_FOR_DEFAULT,
        DEFAULT_BY_SCRIPT,
        DEFAULT_VALUE_LABEL,
        DESCRIPTION,
        COMPOSITE,
        TEMPLATE,
        INPUTMASK,
        INPUTMASK_MODE,
        REQUIRED_IN_INTERFACE,
        EXPORT_NDAP,
        RELATED_ATTRS_TO_EXPORT,
        HIDDEN_WHEN_EMPTY,
        HIDDEN_WHEN_NO_POSSIBLE_VALUES,
        QUICK_ADD_FORM_CODE,
        QUICK_EDIT_FORM_CODE,
        ATTR_CHAIN_VIEW,
        RELATED_OBJECT_ATTRIBUTE,
        RELATED_OBJECT_METACLASS,
        RELATED_OBJECT_HIERARCHY_LEVEL,
        DATE_TIME_COMMON_RESTRICTIONS,
        DATE_TIME_RESTRICTION_TYPE,
        DATE_TIME_RESTRICTION_SCRIPT,
        DATE_TIME_RESTRICTION_ATTRIBUTE,
        DATE_TIME_RESTRICTION_CONDITION,
        TAGS,
        SETTINGS_SET,
        ADVLIST_SEMANTIC_FILTERING,
        HIDDEN_ATTR_CAPTION,
        HIDE_ARCHIVED
    };
    // @formatter:on

    @Override
    public String[] properties()
    {
        return PROPERTIES;
    }
}