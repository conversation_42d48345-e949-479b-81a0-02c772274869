package ru.naumen.metainfoadmin.client.jmsqueue;

import java.util.HashMap;
import java.util.Map;

import jakarta.inject.Inject;

import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.JMSQueue;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.JMSQueue.Attributes;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.objectlist.client.metainfo.FakeMetainfoAdvlistProviderBase;

/**
 * Провайдер метаинформации для списка очередей JMS
 * <AUTHOR>
 * @since 15.02.2021
 */
public class JMSQueueAdvlistProvider extends FakeMetainfoAdvlistProviderBase
{
    @Inject
    private JMSQueueMessages messages;

    @Override
    protected Map<String, Attribute> createAttributes()
    {
        Map<String, Attribute> attrs = new HashMap<>(super.createAttributes());

        attrs.put(JMSQueue.Attributes.TYPE.toString(),
                createStringAttr(JMSQueue.Attributes.TYPE, messages.type()));

        attrs.put(JMSQueue.Attributes.THREAD_COUNT.toString(),
                createStringAttr(JMSQueue.Attributes.THREAD_COUNT, messages.threadCount()));

        attrs.put(JMSQueue.Attributes.ATTR_DESCRIPTION.toString(),
                createStringAttr(JMSQueue.Attributes.ATTR_DESCRIPTION, messages.description()));

        attrCodes.add(Attributes.TYPE.toString());
        attrCodes.add(Attributes.THREAD_COUNT.toString());
        attrCodes.add(Attributes.ATTR_DESCRIPTION.toString());

        return attrs;
    }

    @Override
    protected AttributeFqn getAttrCode()
    {
        return null;
    }

    @Override
    protected AttributeFqn getAttrTitle()
    {
        return JMSQueue.Attributes.ATTR_TITLE;
    }

    @Override
    protected ClassFqn getClassFqn()
    {
        return JMSQueue.FQN;
    }
}