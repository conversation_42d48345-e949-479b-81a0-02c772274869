package ru.naumen.metainfoadmin.client.eventaction.template;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.eventaction.EventActionWithTemplate;
import ru.naumen.metainfoadmin.client.style.templates.StyleTemplatePlace;
import ru.naumen.metainfoadmin.shared.Constants;

/**
 * Контроллер свойства "Шаблон" на карточке действия по событию.
 * Добавляет возможность просмотра текста с учетом шаблона.
 * <AUTHOR>
 * @since Feb 7, 2017 
 */
public class TemplateViewPropertiesController
{
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> template;
    @Inject
    private AdminMetainfoServiceAsync metainfoService;
    @Inject
    private CommonHtmlTemplates htmlTemplates;
    @Inject
    private MetainfoUtils metainfoUtils;

    private boolean asLink = true;

    public void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(template, "template");
    }

    public Property<String> getTemplateProperty()
    {
        return template;
    }

    public void loadTemplate(final EventActionWithTemplate action, final TemplateViewPropertyDecorator property,
            ReadyState readyState)
    {
        if (null != action.getMessageTemplate())
        {
            metainfoService.getStyleTemplate(action.getMessageTemplate(), true, new BasicCallback<DtObject>(readyState)
            {
                @Override
                protected void handleSuccess(DtObject value)
                {
                    String val = value == null ?
                            StringUtilities.EMPTY :
                            asLink ? createTemplateLink(value) : value.getTitle();

                    template.setValue(val);
                    property.setTemplate(value.<String> getProperty(Constants.StyleTemplate.TEMPLATE_TEXT));
                    property.setValue(metainfoUtils.getLocalizedValue(action.getMessage()));
                }

                ;
            });
        }
        else
        {
            template.setValue(StringUtilities.EMPTY);
            property.setTemplate(null);
            property.setValue(metainfoUtils.getLocalizedValue(action.getMessage()));
        }
    }

    private String createTemplateLink(DtObject template)
    {
        if (null == template)
        {
            return StringUtilities.EMPTY;
        }
        return htmlTemplates.historyAnchor(template.getTitle(),
                StyleTemplatePlace.PLACE_PREFIX + ':' + template.getUUID()).asString();
    }

    /**
     * Должна ли формироваться ссылка на шаблон
     * @param asLink - если true, в property устанавливается ссылка на шаблон, иначе простой текст
     */
    public void setAsLink(boolean asLink)
    {
        this.asLink = asLink;
    }
}
