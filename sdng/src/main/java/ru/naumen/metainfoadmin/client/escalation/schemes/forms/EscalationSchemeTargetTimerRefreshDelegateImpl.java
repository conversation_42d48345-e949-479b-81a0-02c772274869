package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import com.google.common.collect.Collections2;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ClassFqnHierarchy;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.timer.TargetTimerRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.EscalationSchemeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 26.07.2012
 *
 */
public class EscalationSchemeTargetTimerRefreshDelegateImpl<F extends ObjectForm>
        extends TargetTimerRefreshDelegateImpl<F>
{
    /**
     * Вспомогательный коллбэк, который получает список всех {@link MetaClassLite метаклассов}, вычисляет наименьшего
     * общего предка объектов, и вызывает коллбэк, обновляющий список таймеров.
     * <AUTHOR>
     * @since Aug 17, 2016
     */
    private class GetAllMetaclassesCallback extends BasicCallback<List<MetaClassLite>>
    {
        private RefreshCallback timerCallback;
        private Collection<DtObject> objects;

        /**
         * @param objects список выделенных объектов
         * @param timerCallback коллбэк, который обновняет список таймеров.
         * Выполняется после того, как будет выполнен данный коллбэк.
         */
        public GetAllMetaclassesCallback(Collection<DtObject> objects, RefreshCallback timerCallback)
        {
            this.objects = objects;
            this.timerCallback = timerCallback;
        }

        @Override
        protected void handleSuccess(List<MetaClassLite> response)
        {
            ClassFqn commonType = this.getLeastCommonParent(response);
            metainfoService.getTimerDefinitions(true, commonType, this.timerCallback);
        }

        /**
         * @param список всех метаклассов, зарегистрированных в системе
         * @return ближайший общий предок всех выделенных элементов
         */
        private ClassFqn getLeastCommonParent(List<MetaClassLite> listOfClasses)
        {
            Collection<ClassFqn> fqns = Collections2.transform(this.objects, DtObject.FQN_EXTRACTOR);
            if (fqns.size() == 1)
            {
                return fqns.iterator().next();
            }
            ClassFqnHierarchy fqnTree = new ClassFqnHierarchy(listOfClasses);
            Iterator<ClassFqn> fqnIterator = fqns.iterator();
            ClassFqn first = fqnIterator.next();
            List<ClassFqn> commonAncestors = fqnTree.getAncestors(first, false, true);
            for (ClassFqn selectedMetaClass; fqnIterator.hasNext(); )
            {
                selectedMetaClass = fqnIterator.next();
                List<ClassFqn> parents = fqnTree.getAncestors(selectedMetaClass, false, true);
                commonAncestors.retainAll(parents);
            }
            return commonAncestors.get(0);
        }
    }

    @Inject
    private AdminMetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        property.getValueWidget().clear();
        String timerCode = context.getPropertyValues().<String> getProperty(EscalationSchemeFormPropertyCode.TIMER);
        Collection<DtObject> objects = context.getPropertyValues()
                .<Collection<DtObject>> getProperty(EscalationSchemeFormPropertyCode.TARGET_OBJECTS);
        if (objects == null)
        {
            new RefreshCallback(context, callback, property, timerCode)
                    .onSuccess(new ArrayList<>());
            return;
        }
        AsyncCallback<ArrayList<DtoContainer<TimerDefinition>>> timerCallback = new RefreshCallback(context, callback,
                property,
                timerCode);
        if (objects.isEmpty())
        {
            timerCallback.onSuccess(new ArrayList<>());
            return;
        }
        processTimersRefresh(objects, new RefreshCallback(context, callback, property, timerCode));
    }

    /**
     * @param objects список выделенных объектов
     * @param timerCallback коллбэк, который обновняет список таймеров.
     */
    private void processTimersRefresh(Collection<DtObject> objects, RefreshCallback timerCallback)
    {
        Iterator<DtObject> dtIter = objects.iterator();
        ClassFqn firstFqn = DtObject.FQN_EXTRACTOR.apply(dtIter.next());

        GetAllMetaclassesCallback getAllClassesCallback = new GetAllMetaclassesCallback(objects, timerCallback);
        metainfoService.getDescendantClasses(firstFqn.fqnOfClass(), true, getAllClassesCallback);
    }
}
