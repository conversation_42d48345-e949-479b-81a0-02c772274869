package ru.naumen.metainfoadmin.client.fastlink.settings.forms;

import static ru.naumen.metainfo.shared.Constants.PREFIXED_CODE_SPECIAL_CHARS_FOR_VENDOR;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsGinModule;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsGinModule.FastLinkSettingFormPropertyCode;

/**
 * Делегат при изменении названия
 *
 * <AUTHOR>
 * @since 01.03.18
 */
public class TitleVCHDelegateImpl implements PropertyDelegateVCH
{
    @Inject
    private TransliterationService transliterationService;
    @Inject
    private SecurityHelper security;

    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        String codeValue = context.getPropertyValues().getProperty(FastLinkSettingFormPropertyCode.CODE);
        if (StringUtilities.isEmpty(codeValue))
        {
            String titleValue = context.getPropertyValues().getProperty(FastLinkSettingFormPropertyCode.TITLE);
            String specialSymbols = security.hasVendorProfile() ? PREFIXED_CODE_SPECIAL_CHARS_FOR_VENDOR : "";
            codeValue = transliterationService.transliterateToCode(titleValue,
                    FastLinkSettingsGinModule.MAX_CODE_LENGTH, specialSymbols);
            context.getPropertyValues().setProperty(FastLinkSettingFormPropertyCode.CODE, codeValue);
            context.getPropertyControllers().get(FastLinkSettingFormPropertyCode.CODE).refresh();
        }
    }
}