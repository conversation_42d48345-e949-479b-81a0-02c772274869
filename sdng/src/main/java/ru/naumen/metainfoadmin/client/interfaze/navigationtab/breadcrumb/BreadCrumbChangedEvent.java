package ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие изменения хлебных крошек
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public class BreadCrumbChangedEvent extends GwtEvent<BreadCrumbChangedHandler>
{
    private static Type<BreadCrumbChangedHandler> TYPE = new Type<BreadCrumbChangedHandler>();

    public static Type<BreadCrumbChangedHandler> getType()
    {
        return TYPE;
    }

    private final DtoContainer<NavigationSettings> settings;
    private final Crumb crumb;

    public BreadCrumbChangedEvent(DtoContainer<NavigationSettings> settings, Crumb crumb)
    {
        this.settings = settings;
        this.crumb = crumb;
    }

    @Override
    public Type<BreadCrumbChangedHandler> getAssociatedType()
    {
        return TYPE;
    }

    public Crumb getCrumb()
    {
        return crumb;
    }

    public DtoContainer<NavigationSettings> getSettings()
    {
        return settings;
    }

    @Override
    protected void dispatch(BreadCrumbChangedHandler handler)
    {
        handler.onBreadCrumbChanged(this);
    }
}
