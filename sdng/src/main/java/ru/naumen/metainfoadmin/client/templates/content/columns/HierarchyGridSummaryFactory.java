package ru.naumen.metainfoadmin.client.templates.content.columns;

import jakarta.inject.Inject;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import ru.naumen.core.client.common.FormattersImpl;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.card.StructuredObjectsViewPlace;

/**
 * Фабрика для генерации основных параметров шаблона контента «Иерархическое дерево».
 * <AUTHOR>
 * @since Apr 05, 2021
 */
public class HierarchyGridSummaryFactory implements ContentTemplateSummaryFactory<HierarchyGrid>
{
    private final Formatters formatters;

    @Inject
    public HierarchyGridSummaryFactory(Formatters formatters)
    {
        this.formatters = formatters;
    }

    @Override
    public SafeHtml createSummary(DtObject contentTemplateDto, HierarchyGrid templateContent)
    {
        String structuredObjectsViewTitle = contentTemplateDto.getProperty(
                FakeMetaClassesConstants.ContentTemplate.HierarchyGrid.STRUCTURED_OBJECTS_VIEW_TITLE);
        if (null != templateContent.getStructuredObjectsViewCode() && null != structuredObjectsViewTitle)
        {
            return ((FormattersImpl)formatters).linkToSystemObject(StructuredObjectsViewPlace.PLACE_PREFIX,
                    templateContent.getStructuredObjectsViewCode(), structuredObjectsViewTitle);
        }
        return SafeHtmlUtils.EMPTY_SAFE_HTML;
    }
}
