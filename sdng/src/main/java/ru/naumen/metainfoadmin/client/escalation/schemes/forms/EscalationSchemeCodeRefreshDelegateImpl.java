package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinModule.EscalationSchemeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 06.02.2017
 *
 */
public class EscalationSchemeCodeRefreshDelegateImpl implements PropertyDelegateRefresh<String, TextBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        property.setValue((String)context.getPropertyValues().getProperty(EscalationSchemeFormPropertyCode.CODE));
        callback.onSuccess(true);
    }
}
