package ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree;

import java.util.Collection;

import jakarta.inject.Inject;

import com.google.gwt.cell.client.Cell;

import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithoutFolders;
import ru.naumen.core.client.tree.view.TreeViewModelContext;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.client.widgets.tree.cell.ValueTreeDefaultCellFactory;
import ru.naumen.core.client.widgets.tree.cell.WidgetTreeCellGinModule.WithoutRemoved;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Реализация {@link EventsDtoTreePropertyFactory}
 * <AUTHOR>
 * @since 06.08.2023
 */
public class EventsDtoTreePropertyFactoryImpl implements EventsDtoTreePropertyFactory
{
    @Inject
    private EventsDtoTreeDataSourceFactory dataSourceFactory;
    @Inject
    private EventsDtoTreeSelectionModelFactory selectionModelFactory;
    @Inject
    private EventsDtoTreeViewModelFactory vmFactory;
    @Inject
    private ValueTreeDefaultCellFactory<DtObject, EventsDtoTreeSelectionModel, WithoutFolders, WithoutRemoved> cellFactory;
    @Inject
    private PopupValueCellTreeFactory<DtObject, Collection<DtObject>, EventsDtoTreeSelectionModel> treeFactory;

    @Override
    public PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
            EventsDtoTreeSelectionModel>> create(
            EventsDtoTreeFactoryContext context)
    {
        EventsDtoTreeDataSource dataSource = dataSourceFactory.create(context);
        EventsDtoTreeSelectionModel selectionModel = selectionModelFactory.create();
        dataSource.setNeedCleanSearcher(context.isNeedCleanSearcher());
        dataSource.setBadgesOnlyChild(context.isBadgesOnlyChild());
        selectionModel.setDataSource(dataSource);
        selectionModel.setContext(context);
        Cell<DtObject> cell = cellFactory.create(selectionModel);
        EventsDtoTreeViewModel viewModel = vmFactory
                .create(new TreeViewModelContext<DtObject, EventsDtoTreeSelectionModel>(dataSource, selectionModel,
                        cell, null));
        PopupValueCellTree<DtObject, Collection<DtObject>, EventsDtoTreeSelectionModel> tree = treeFactory
                .create(viewModel);
        if (context.getValueFormatter() != null)
        {
            tree.setFormatter(context.getValueFormatter());
        }
        tree.setHideFieldValue(context.isHideFieldValue());
        return new PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                EventsDtoTreeSelectionModel>>(
                tree);
    }
}