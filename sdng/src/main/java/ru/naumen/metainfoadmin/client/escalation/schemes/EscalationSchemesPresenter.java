package ru.naumen.metainfoadmin.client.escalation.schemes;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.ESCALATIONS;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.cellview.client.Column;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.components.table.TableWithToolPanelDisplay;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfoadmin.client.AdminListPresenterBase;
import ru.naumen.metainfoadmin.client.escalation.EscalationMessages;
import ru.naumen.metainfoadmin.client.escalation.schemes.EscalationSchemesGinModule.AddSchemeClickHandlerFactory;
import ru.naumen.metainfoadmin.client.escalation.schemes.columns.EscalationSchemesColumnsGinModule;
import ru.naumen.metainfoadmin.client.escalation.schemes.commands.EscalationSchemeCommandContext;
import ru.naumen.metainfoadmin.client.escalation.schemes.commands.EscalationSchemesCommandParam;
import ru.naumen.metainfoadmin.client.escalation.schemes.commands.EscalationSchemesCommandsGinModule.EscalationCommandCode;

/**
 * Презентер списка Схем эскалации
 * <AUTHOR>
 * @since 23.07.2012
 *
 */
public class EscalationSchemesPresenter extends AdminListPresenterBase<DtoContainer<EscalationScheme>>
{
    @Inject
    public EscalationSchemesPresenter(TableWithToolPanelDisplay<DtoContainer<EscalationScheme>> display,
            EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Inject
    private EscalationMessages messages;
    @Inject
    private AddSchemeClickHandlerFactory addSchemeCHFactory;
    @Inject
    @Named(EscalationSchemesColumnsGinModule.COLUMNS)
    private ArrayList<Pair<String, Column<DtoContainer<EscalationScheme>, ?>>> columns;

    @Override
    protected List<Object> getRowActions()
    {
        return Lists.newArrayList(EscalationCommandCode.TOGGLE, EscalationCommandCode.EDIT,
                EscalationCommandCode.DELETE);
    }

    @Override
    protected List<ButtonPresenter<?>> getListActions()
    {
        return Lists.newArrayList(buttonFactory.create(ButtonCode.ADD, messages.addScheme(), addSchemeCHFactory.create(
                localEventBus)));
    }

    @Override
    protected CommandParam<DtoContainer<EscalationScheme>, ?> getCommandParam()
    {
        return new EscalationSchemesCommandParam(null, refreshCallback,
                new EscalationSchemeCommandContext(localEventBus, getDisplay()));
    }

    @Override
    protected List<Pair<String, Column<DtoContainer<EscalationScheme>, ?>>> getColumns()
    {
        return columns;
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return ESCALATIONS;
    }

    protected Predicate<DtoContainer<EscalationScheme>> getVisibilityCondition(String command)
    {
        PermissionType permissionType = EscalationCommandCode.DELETE.equals(command)
                ? PermissionType.DELETE
                : PermissionType.EDIT;
        return AdminPermissionUtils.createPermissionPredicate(permissionType);
    }
}
