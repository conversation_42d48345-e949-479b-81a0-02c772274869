package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.GenerationRuleDelegateHelper;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 10 дек. 2013 г.
 */
public class UseGenerationRuleRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, Boolean, BooleanCheckBoxProperty>
{
    @Inject
    private CommonMessages messages;

    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        if (metaClass.getFqn().toString().endsWith("__Evt") || Constants.GeoHistoryRecord.FQN.equals(metaClass
                .getFqn()))
        {
            callback.onSuccess(false);
            return;
        }

        String attrTypeCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        property.setCaption(getCaption(attrTypeCode));

        boolean suitableAttrType = StringAttributeType.CODE.equals(attrTypeCode)
                                   || IntegerAttributeType.CODE.equals(attrTypeCode);
        boolean isDefinableByTemplate = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPOSITE);
        boolean isComputable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        boolean isDeterminable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);

        String attrCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.CODE);

        callback.onSuccess(suitableAttrType && !isDefinableByTemplate && !isDeterminable && !isComputable
                           && GenerationRuleDelegateHelper.isSuitableAttribute(metaClass.getFqn(), attrCode));
    }

    private String getCaption(String attrTypeCode)
    {
        if (IntegerAttributeType.CODE.equals(attrTypeCode))
        {
            return messages.useNumberRule();
        }
        return messages.useNameRule();
    }
}
