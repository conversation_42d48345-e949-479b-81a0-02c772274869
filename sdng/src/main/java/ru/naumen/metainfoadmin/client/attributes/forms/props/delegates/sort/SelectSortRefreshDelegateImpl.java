package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.sort;

import static ru.naumen.metainfo.shared.Constants.CATALOG_TYPES;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Обработчик обновления свойства "Сортировка списка" для атрибутов типа "Элемент справочника" и "Набор элементов
 * справочника"
 * <AUTHOR>
 * @since 20.02.2013
 *
 */
public class SelectSortRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    @Inject
    SelectSortConstants constants;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        Boolean computable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        Boolean determinable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);
        if (computable || determinable
            || !(isCatalogItemType(context) || isStringCatalogSuggestionPresentation(context)))
        {
            callback.onSuccess(false);
            return;
        }
        SingleSelectCellList<String> selectList = property.getValueWidget();
        selectList.clear();
        for (String attrCode : SelectSortAttributesCodesConstants.ATTR_CODES_FOR_SORT)
        {
            selectList.addItem(constants.selectSortTypes().get(attrCode), attrCode);
        }
        Attribute attribute = context.getContextValues().getProperty(AttributeFormContextValues.ATTRIBUTE);
        String value = Constants.CatalogItem.ITEM_CODE;
        if (null != attribute && null != MetainfoUtils.getAttrForSortFromAttr(attribute))
        {
            value = MetainfoUtils.getAttrForSortFromAttr(attribute);
        }
        property.trySetObjValue(value);
        context.getPropertyValues().setProperty(AttributeFormPropertyCode.SELECT_SORTING, value);
        callback.onSuccess(true);
    }

    private boolean isCatalogItemType(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        boolean catalogItemType = CATALOG_TYPES.contains(attrType);
        return catalogItemType;
    }

    private boolean isStringCatalogSuggestionPresentation(PropertyContainerContext context)
    {
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        String editPrs = context.getPropertyValues().getProperty(AttributeFormPropertyCode.EDIT_PRS);
        return StringAttributeType.CODE.equals(attrType)
               && Presentations.STRING_EDIT_WITH_CATALOG_SUGGESTION.equals(editPrs);
    }
}
