package ru.naumen.metainfoadmin.client.interfaze.interfacetab.command;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.personalsettings.ThemeClient;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;

/**
 * <AUTHOR>
 * @since 15.07.16
 */
public class ThemeCommandParam extends CommandParam<ThemeClient, InterfaceSettingsContext>
{
    private InterfaceSettingsContext context;

    public ThemeCommandParam(InterfaceSettingsContext context)
    {
        this.context = context;
    }

    public ThemeCommandParam(InterfaceSettingsContext context, @Nullable ThemeClient value)
    {
        super(value);
        this.context = context;
    }

    private ThemeCommandParam(InterfaceSettingsContext context,
            @Nullable FactoryParam.ValueSource<ThemeClient> valueSource)
    {
        super(valueSource);
        this.context = context;
    }

    @Override
    @SuppressWarnings("unchecked")
    public ThemeCommandParam cloneIt()
    {
        return new ThemeCommandParam(getContext(), getValueSource());
    }

    public InterfaceSettingsContext getContext()
    {
        return context;
    }
}
