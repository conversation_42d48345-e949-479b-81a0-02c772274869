package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;

import com.google.gwt.inject.client.GinModules;
import com.google.gwt.inject.client.Ginjector;
import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 01.08.2012
 *
 */
@GinModules(EscalationSchemesFormsGinModule.class)
public interface EscalationSchemesFormsGinjector extends Ginjector
{
    interface EscalationSchemeFormCallbackFactory<F extends ObjectForm>
    {
        AsyncCallback<SimpleResult<DtoContainer<EscalationScheme>>> create(EscalationSchemeForm<F> form);
    }
}
