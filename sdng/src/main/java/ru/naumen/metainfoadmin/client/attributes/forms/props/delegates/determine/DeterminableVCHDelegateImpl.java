package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determine;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.Arrays;

import java.util.ArrayList;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * <AUTHOR>
 */
public class DeterminableVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        Boolean determinable = context.getPropertyValues().getProperty(DETERMINABLE);
        if (determinable)
        {
            context.setProperty(COMPUTABLE, false);
            context.setProperty(COMPUTABLE_ON_FORM, false);
            context.setProperty(DATE_TIME_COMMON_RESTRICTIONS, new ArrayList<>());
            context.setProperty(DATE_TIME_RESTRICTION_TYPE, null);
            context.setProperty(DATE_TIME_RESTRICTION_ATTRIBUTE, null);
            context.setProperty(DATE_TIME_RESTRICTION_CONDITION, null);
        }

        context.getRefreshProcess()
                .startCustomProcess(Arrays.asList(DETERMINER, COMPUTABLE, REQUIRED, REQUIRED_IN_INTERFACE, EDITABLE,
                        EDITABLE_IN_LISTS, UNIQUE, PERMITTED_TYPES, SCRIPT, DATE_TIME_RESTRICTION_SCRIPT, EDIT_PRS,
                        SUGGEST_CATALOG, SELECT_SORTING, FILTERED_BY_SCRIPT, COMPUTABLE_ON_FORM,
                        COMPUTABLE_ON_FORM_SCRIPT, SCRIPT_FOR_FILTRATION, DEFAULT_VALUE_LABEL, DEFAULT_BY_SCRIPT,
                        SCRIPT_FOR_DEFAULT, COMPOSITE, USE_GEN_RULE, INTERVAL_AVAILABLE_UNITS, DEFAULT_VALUE,
                        QUICK_ADD_FORM_CODE, QUICK_EDIT_FORM_CODE, DATE_TIME_COMMON_RESTRICTIONS,
                        DATE_TIME_RESTRICTION_TYPE, DATE_TIME_RESTRICTION_ATTRIBUTE, DATE_TIME_RESTRICTION_CONDITION));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}
