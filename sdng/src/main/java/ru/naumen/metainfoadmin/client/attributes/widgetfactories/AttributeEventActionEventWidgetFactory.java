package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import java.util.ArrayList;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.client.eventaction.EventActionConstants;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.EventType;

/**
 * Фабрика виджета редактирования типов событий действия по событию
 *
 * <AUTHOR>
 * @since Feb 19, 2015
 */
public class AttributeEventActionEventWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;
    @Inject
    private EventActionConstants constants;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();

        List<DtObject> eventTypes = new ArrayList<>();
        for (EventType eventType : EventType.values())
        {
            if (!EventType.escalation.equals(eventType))
            {
                String uuid = eventType.name();
                String title = constants.eventTypes().get(eventType.name());
                eventTypes.add(new SimpleDtObject(uuid, title, ClassFqn.parse("")));
            }
        }
        eventTypes.sort(ITitled.COMPARATOR);
        widget.addItems(eventTypes);
        callback.onSuccess(widget);
    }
}
