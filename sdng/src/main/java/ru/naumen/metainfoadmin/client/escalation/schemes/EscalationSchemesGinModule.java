package ru.naumen.metainfoadmin.client.escalation.schemes;

import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.common.DefaultAsyncDataProviderSimpleResult;
import ru.naumen.core.client.components.table.TableWithToolPanelDisplay;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfo.shared.dispatch2.GetEscalationSchemesAction;
import ru.naumen.metainfoadmin.client.escalation.schemes.columns.EscalationSchemesColumnsGinjector;
import ru.naumen.metainfoadmin.client.escalation.schemes.commands.EscalationSchemesCommandsGinjector;
import ru.naumen.metainfoadmin.client.escalation.schemes.forms.EscalationSchemesFormsGinjector;

/**
 * <AUTHOR>
 * @since 23.07.2012
 *
 */
public class EscalationSchemesGinModule extends AbstractGinModule
{
    public interface AddSchemeClickHandlerFactory
    {
        ClickHandler create(EventBus presenterEventBus);
    }

    @Override
    protected void configure()
    { //@formatter:off
        bind(EscalationSchemesColumnsGinjector.class).to(EscalationSchemesGinjector.class).in(Singleton.class);
        bind(EscalationSchemesCommandsGinjector.class).to(EscalationSchemesGinjector.class).in(Singleton.class);
        bind(EscalationSchemesFormsGinjector.class).to(EscalationSchemesGinjector.class).in(Singleton.class);
        bind(EscalationSchemesPresenter.class);
        bind(new TypeLiteral<DefaultAsyncDataProviderSimpleResult<DtoContainer<EscalationScheme>, GetEscalationSchemesAction>>(){});
        bind(new TypeLiteral<TableWithToolPanelDisplay<DtoContainer<EscalationScheme>>>(){}).to(new TypeLiteral<EscalationSchemesDisplayImpl>() {});
        install(new GinFactoryModuleBuilder()
            .implement(ClickHandler.class, AddSchemeClickHandler.class)
            .build(AddSchemeClickHandlerFactory.class));
        //@formatter:on
    }
}