package ru.naumen.metainfoadmin.client.attributes.forms.typeEmul;

import com.google.inject.Singleton;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Класс позволяет сконструировать тип атрибута целого числа на основе данных из контекста и заполнить контекст на
 * основе типа атрибута
 * <AUTHOR>
 * @since 10.07.2019
 */
@Singleton
public class AttributeTypeBuilderIntegerImpl implements AttributeTypeBuilder
{
    @Override
    public void build(AttributeType type, IProperties propertyValues)
    {
        type.setProperty(IntegerAttributeType.HAS_GROUP_SEPARATOR, isHasGroupSeparators(propertyValues));
    }

    @Override
    public void invert(AttributeType type, IProperties propertyValues)
    {
        ru.naumen.metainfo.shared.elements.IntegerAttributeType integerAttrType = type.cast();
        propertyValues.setProperty(AttributeFormPropertyCode.HAS_GROUP_SEPARATORS,
                integerAttrType.isHasGroupSeparator());
    }

    private boolean isHasGroupSeparators(IProperties propertyValues)
    {
        Object hasGroupSeparators = propertyValues.getProperty(AttributeFormPropertyCode.HAS_GROUP_SEPARATORS);
        boolean hasGroupSeparatorsValue = hasGroupSeparators == null
                ? IntegerAttributeType.HAS_GROUP_SEPARATOR_DEFAULT
                : hasGroupSeparators instanceof String ? Boolean.parseBoolean((String)hasGroupSeparators)
                        : (Boolean)hasGroupSeparators;
        return hasGroupSeparatorsValue;
    }
}
