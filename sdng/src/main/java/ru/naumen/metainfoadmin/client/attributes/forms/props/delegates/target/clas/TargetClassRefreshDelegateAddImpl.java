package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.clas;

import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.CaseListAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public class TargetClassRefreshDelegateAddImpl<F extends ObjectForm> extends TargetClassRefreshDelegateImpl<F>
{
    protected class AddRefreshCallback extends RefreshCallback
    {
        public AddRefreshCallback(AsyncCallback<Boolean> callback, PropertyContainerContext context,
                ListBoxProperty property)
        {
            super(callback, context, property);
        }

        @Override
        protected void handleSuccess(List<MetaClassLite> classesList)
        {
            super.handleSuccess(classesList);
            context.getPropertyControllers().get(AttributeFormPropertyCode.TARGET_CLASS).getValue();
            setRelatedMetainfo(SelectListPropertyValueExtractor.getValue(property), callback);
        }
    }

    @Override
    public void refreshProperty(final PropertyContainerContext context, final ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String typeCode = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        if (!(CaseListAttributeType.CODE.equals(typeCode) || ObjectAttributeType.CODE.equals(typeCode)
              || BOLinksAttributeType.CODE.equals(typeCode)))
        {
            callback.onSuccess(false);
            return;
        }

        metainfoService.getNotSystemClasses(refreshCallback(context, property, callback));
    }

    protected RefreshCallback refreshCallback(final PropertyContainerContext context, final ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        return new AddRefreshCallback(callback, context, property);
    }

}
