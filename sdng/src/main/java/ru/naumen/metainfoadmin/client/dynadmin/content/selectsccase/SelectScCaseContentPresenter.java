package ru.naumen.metainfoadmin.client.dynadmin.content.selectsccase;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Provider;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.utils.FormUtils;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.settings.ScCaseFieldsOrderSettings;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ui.SelectScCase;
import ru.naumen.metainfoadmin.client.dynadmin.content.AbstractEditablePropsContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.PropertyGridFlowContentDisplay;

/**
 * {@link Presenter} для {@link SelectScCase выбора типа запроса, соглашения и услуги}
 * <AUTHOR>
 * @since 30.03.2011
 *
 */
public class SelectScCaseContentPresenter
        extends AbstractEditablePropsContentPresenter<SelectScCase>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<String>> selectListBuilderProvider;
    @Inject
    private AdminMetainfoServiceAsync metainfoService;
    @Inject
    private FormUtils formUtils;
    private Property<DtObject> caseProperty;
    private Property<SelectItem> agreementAndServiceProperty;

    @Inject
    public SelectScCaseContentPresenter(PropertyGridFlowContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "SelectScCase");
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();

        if (agreementAndServiceProperty != null)
        {
            agreementAndServiceProperty.getCaptionWidget()
                    .asWidget()
                    .setStyleName(getStyle(Constants.Association.AGREEMENT));
        }
        if (caseProperty != null)
        {
            caseProperty.getCaptionWidget().asWidget().setStyleName(getStyle(Constants.AbstractBO.METACLASS));
        }
    }

    @Override
    protected boolean isEditable()
    {
        return true;
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        metainfoService.getSettings(new BasicCallback<Settings>()
        {
            @Override
            protected void handleSuccess(Settings settings)
            {
                ScCaseFieldsOrderSettings orderSc = settings.getScParameters().getOrderScSetting();
                bindAgreement();
                bindSCCase(orderSc.getCaseIndex());
            }
        });
    }

    @Override
    protected String getHelpText()
    {
        return messages.selectScCase();
    }

    private void bindAgreement()
    {
        agreementAndServiceProperty = propertyCreator.create(
                formUtils.getAgreementServiceCaption(getContext(), true),
                selectListBuilderProvider.get().setHasSearch(true).setCellGlobalPaddingLeft(0).build());
        agreementAndServiceProperty.getCaptionWidget()
                .asWidget()
                .setStyleName(getStyle(Constants.Association.AGREEMENT));
        agreementAndServiceProperty.setValidationMarker(true);
        getDisplay().add(agreementAndServiceProperty);
    }

    private void bindSCCase(int index)
    {
        caseProperty = propertyCreator.create(getMetaClassAttrTitle(),
                selectListBuilderProvider.get().setHasSearch(true).setCellGlobalPaddingLeft(0).build());
        caseProperty.setValidationMarker(true);
        if (caseProperty.getValueWidget() instanceof SingleSelectCellList<?>)
        {
            caseProperty.<SingleSelectCellList<?>> getValueWidget().setHasSearchLite(true);
        }
        caseProperty.getCaptionWidget().asWidget().setStyleName(getStyle(Constants.AbstractBO.METACLASS));
        getDisplay().addProperty(caseProperty, index);
    }

    private String getStyle(String AttrCode)
    {
        return getContext().getMetainfo().getAttribute(AttrCode).isHiddenAttrCaption() ?
                WidgetResources.INSTANCE.form().hiddenAttrCaption() : StringUtilities.EMPTY;
    }

    private String getMetaClassAttrTitle()
    {
        return getContext().getMetainfo().getAttribute(Constants.AbstractBO.METACLASS).getTitle();
    }
}
