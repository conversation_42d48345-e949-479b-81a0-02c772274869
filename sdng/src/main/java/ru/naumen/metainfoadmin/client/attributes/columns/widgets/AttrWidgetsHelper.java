package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import static ru.naumen.core.shared.ITitled.ORDER;
import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TITLE_SEPARATOR;
import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TYPE_LIST_SEPARATOR;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.gwt.dom.client.BRElement;
import com.google.gwt.dom.client.Element;
import com.google.gwt.user.client.DOM;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.sourcecode.view.AbstractSourceCodeViewWidget;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.attributes.columns.AttrCodeCellValueExtractor;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 18 сент. 2018 г.
 *
 */
public class AttrWidgetsHelper
{
    public static String createLink(String placePrefix, String code)
    {
        return "#" + placePrefix + ":" + code;
    }

    public static void handleTypesOutput(FlowPanel result, AttributesMessages messages,
            List<MetaClassLite> descendantClasses, Set<ClassFqn> permittedTypes, CommonMessages commonMessages)
    {
        if (permittedTypes != null && descendantClasses != null
            && permittedTypes.size() < descendantClasses.size())
        {
            result.getElement().appendChild(DOM.createElement(BRElement.TAG));
            result.add(new InlineLabel(messages.type() + TITLE_SEPARATOR));
            int i = 0;
            for (ClassFqn permittedType : permittedTypes)
            {
                if (Constants.NOONE.equals(permittedType))
                {
                    result.add(new InlineLabel(commonMessages.noone()));
                }
                else
                {
                    MetaClassLite type = descendantClasses.stream().filter(metaClassLite -> metaClassLite.getFqn()
                            .equals(permittedType)).findFirst().orElse(null);
                    Anchor anchor = new Anchor(type.getTitle() + ((i++ < permittedTypes.size() - 1)
                            ? TYPE_LIST_SEPARATOR
                            : ""), false,
                            AttrWidgetsHelper.createLink(MetaClassPlace.PLACE_PREFIX, type.getCode()));
                    anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
                    result.add(anchor);
                }
            }
        }
    }

    /**
     * Получает код атрибута из строки
     * @param row строка
     * @return код атрибута либо null, если не удалось найти
     */
    public static String getAttributeCode(Element row)
    {
        return (new AttrCodeCellValueExtractor()).extractValue(
                getCellElementByClassName(row, AdminWidgetResources.INSTANCE.attributeList().colTitleCodeType()));
    }

    /**
     * Получает ячейку таблицы (элемент &lt;td/&gt;) в строке по имени класса. 
     * @param row строка для поиска
     * @return ячейка с указанным классом либо null, если такой нет
     */
    public static Element getCellElementByClassName(Element row, String className)
    {
        Element result = null;
        if (row != null && className != null)
        {
            for (int i = 0; i < row.getChildCount(); i++)
            {
                Element child = (Element)row.getChild(i);
                if (child.getClassName() != null && Arrays.asList(child.getClassName().split(" ")).contains(className))
                {
                    result = child;
                    break;
                }
            }
        }
        return result;
    }

    /**
     * Устанавливает видимость нумерации строк для дефолтного значения атрибута типа "Текст с подсветкой синтаксиса"
     * @param widget виджет для которого устанавливается отображение нумерации строк
     * @param useLineNumbers true - нужна нумерация
     */
    public static void setUseLineNumbers(Widget widget, boolean useLineNumbers)
    {
        if (widget instanceof AbstractSourceCodeViewWidget)
        {
            ((AbstractSourceCodeViewWidget)widget).setUseLineNumbers(useLineNumbers);
        }
    }

    /**
     * Заполнить допустимые типы в тип атрибута
     * @param type тип атрибута
     * @param propertyValues свойства атрибута
     */
    public static void setPermittedTypes(AttributeType type, IProperties propertyValues)
    {
        Collection<DtObject> permittedTypes = propertyValues.getProperty(AttributeFormPropertyCode.PERMITTED_TYPES,
                Collections.emptySet());
        Collection<ClassFqn> permittedTypesFqns =
                permittedTypes.stream().map(DtObject::getMetainfo).collect(Collectors.toSet());
        type.setProperty(ObjectAttributeType.PERMITTED_TYPES, permittedTypesFqns);
    }

    /**
     * Заполнить виджет группами атрибутов класса "Комментарий", содержащими атрибут "Текст"
     * @param commentMetaClass метакласс Комментарий
     * @param commentAttributeGroupWidget виджет для заполнения
     */
    public static void fillCommentAttributeGroupsToWidget(MetaClass commentMetaClass,
            SingleSelectCellList<String> commentAttributeGroupWidget)
    {
        List<AttributeGroup> sortedAttributeGroups = ORDER.sortedCopy(commentMetaClass.getAttributeGroups());
        for (AttributeGroup attributeGroup : sortedAttributeGroups)
        {
            if (attributeGroup.getAttributeCodes().contains(Comment.TEXT))
            {
                String attrGroupCaption = getAttributeTitleFormat(attributeGroup.getTitle(), attributeGroup.getCode());
                commentAttributeGroupWidget.addItem(attrGroupCaption, attributeGroup.getCode());
            }
        }
    }

    /**
     * Заполнить виджет атрибутами определенного типа из данного метакласса
     * @param metaClass метакласс содержащий атрибуты
     * @param attributeType тип атрибутов для заполнения
     * @param widget виджет для заполнения
     */
    public static void fillAttributesOfTypeToWidget(MetaClass metaClass, String attributeType,
            SingleSelectCellList<String> widget)
    {
        metaClass.getAttributes().stream()
                .filter(attr -> attr.getType().getCode().equals(attributeType))
                .sorted(Comparator.comparing(ITitled::getTitle))
                .forEach(attr ->
                        widget.addItem(getAttributeTitleFormat(attr.getTitle(), attr.getCode()), attr.getCode()));
    }

    public static String getAttributeTitleFormat(String title, String code)
    {
        return title + " (" + code + ")";
    }
}
