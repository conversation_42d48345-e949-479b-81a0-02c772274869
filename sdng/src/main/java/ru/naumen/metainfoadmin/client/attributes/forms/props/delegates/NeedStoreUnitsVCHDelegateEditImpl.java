package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

public class NeedStoreUnitsVCHDelegateEditImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Inject
    private AttributesMessages messages;

    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        boolean value = !(boolean)context.getPropertyValues().getProperty(AttributeFormPropertyCode.NEED_STORE_UNITS)
                        && (boolean)context.getContextValues().getProperty(AttributeFormPropertyCode.NEED_STORE_UNITS);
        context.getDisplay().addAttentionMessage(value ? messages.needStoreUnitsAttention() : StringUtilities.EMPTY);
    }
}
