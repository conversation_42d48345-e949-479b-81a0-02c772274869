package ru.naumen.metainfoadmin.client.attributes.widgetfactories;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.advimport.shared.AdvImportSchedulerTask;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEdit;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.mailreader.shared.task.ReceiveMailTask;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTaskMessages;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTasksPresenterSettings;

/**
 * Фабрика по созданию представления для редактирования "типа" планировщика задач. 
 *
 * <AUTHOR>
 * @since 29.11.2017
 */
public class AttributeSchedulerTaskTypeWidgetFactory implements AttributeWidgetFactoryEdit<SelectItem>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;

    @Inject
    private SchedulerTaskMessages messages;
    @Inject
    private SchedulerTasksPresenterSettings settings;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(PresentationContext context, final AsyncCallback<HasValueOrThrow<SelectItem>> callback)
    {
        SingleSelectCellList<DtObject> widget = selectListBuilderProvider.get().setHasSearch(true).build();

        widget.addItem(new SimpleDtObject(ReceiveMailTask.NAME, messages.receiveMailTask()));
        if (settings.getTypes() == null)
        {
            widget.addItem(new SimpleDtObject(AdvImportSchedulerTask.NAME, messages.advImportSchedulerTask()));
            widget.addItem(new SimpleDtObject(ExecuteScriptTask.NAME, messages.executeScriptTask()));
        }
        callback.onSuccess(widget);
    }
}
