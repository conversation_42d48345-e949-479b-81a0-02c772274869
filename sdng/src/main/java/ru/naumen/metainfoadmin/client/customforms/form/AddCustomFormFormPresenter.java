package ru.naumen.metainfoadmin.client.customforms.form;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.tree.dto.DtoPopupMultiValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.Constants.AttrGroup;
import ru.naumen.metainfo.shared.ui.customform.ChangeCaseForm;
import ru.naumen.metainfo.shared.ui.customform.Constants;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;
import ru.naumen.metainfo.shared.ui.customform.CustomFormType;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 26.04.2016
 *
 */
public class AddCustomFormFormPresenter extends AbstractCustomFormFormPresenter
{
    @Inject
    private CustomFormCodeCreator codeCreator;

    @Inject
    // @formatter:off
    public AddCustomFormFormPresenter(             
            PropertyDialogDisplay display,
            EventBus eventBus)
    // @formatter:on
    {
        super(display, eventBus);
    }

    @Override
    protected void initPropertiesValues(CustomForm customForm, AsyncCallback<Void> callback)
    {
        // Открыта форма добавления пользовательской формы
        this.customForm = new ChangeCaseForm();
        this.customForm.setFormType(CustomFormType.ChangeCaseForm);

        setTransitionCases();

        commentOnFormProperty.setValue(Constants.CustomUserForm.NOT_FILL);
        commentAttributeGroup.trySetObjValue(AttrGroup.ADD_FORM, true);
        showAttributeDescription.setValue(false);
        useStandardAttributesSet.setValue(false);

        callback.onSuccess(null);
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.addingForm());

        registerHandler(titleProperty.addValueChangeHandler(event ->
        {
            if (null != formTypeProperty.getValue()
                && CustomFormType.QuickForm.name().equals(formTypeProperty.getValue().getCode())
                && !StringUtilities.isEmptyTrim(event.getValue())
                && StringUtilities.isEmpty(codeProperty.getValue()))
            {
                codeProperty.setValue(codeCreator.createCode(event.getValue()));
            }
        }));
    }

    @Override
    protected void setTransitionCases()
    {
        if ((metaClassUnderEdit.getFqn().isCase()
             || CustomFormType.QuickForm.name().equals(SelectListPropertyValueExtractor.getValue(formTypeProperty))
             || CustomFormType.MassEditForm.name()
                     .equals(SelectListPropertyValueExtractor.getValue(formTypeProperty)))
            && casesForSetupCustomForm.contains(this.metaClassUnderEdit)
            && !nonSelectableCasesForCustomForm.contains(this.metaClassUnderEdit.getFqn()))
        {
            MetaClassAttributeGroups attrGroups = attributeGroupsCache.get(this.metaClassUnderEdit.getFqn());
            updateAttributeGroups(attrGroups.getGroups());
            DtoPopupMultiValueCellTree<?> casesTree = transitionCasesProperty.getValueWidget();
            casesTree.getTreeModel().getSelectionModel().clear();
            casesTree.getTreeModel().getSelectionModel()
                    .setSelected(DtObject.CREATE_FROM_METACLASSLITE.apply(metaClassUnderEdit), true);
        }
    }
}
