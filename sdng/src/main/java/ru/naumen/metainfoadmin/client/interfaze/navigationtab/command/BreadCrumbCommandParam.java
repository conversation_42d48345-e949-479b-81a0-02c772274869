package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public class BreadCrumbCommandParam extends NavigationSettingsAbstractCommandParam<Crumb>
{
    public BreadCrumbCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, (Crumb)null, callback);
    }

    public BreadCrumbCommandParam(DtoContainer<NavigationSettings> settings, @Nullable Crumb value,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, value, callback);
    }

    public BreadCrumbCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable FactoryParam.ValueSource<Crumb> valueSource,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, valueSource, callback);
    }

    @Override
    @SuppressWarnings("unchecked")
    public BreadCrumbCommandParam cloneIt()
    {
        return new BreadCrumbCommandParam(getSettings(), getValueSource(), getCallback());
    }
}
