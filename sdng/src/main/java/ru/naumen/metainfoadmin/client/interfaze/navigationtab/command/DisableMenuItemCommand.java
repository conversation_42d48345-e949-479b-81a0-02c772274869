package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.SwitchNavigationMenuItemAction;

/**
 * Абстрактная команда выключения элемента меню
 *
 * <AUTHOR>
 * @since 01 окт. 2013 г.
 */
public abstract class DisableMenuItemCommand<M extends IMenuItem> extends BaseCommandImpl<M,
        DtoContainer<NavigationSettings>>
{
    @Inject
    private DispatchAsync dispatch;

    public DisableMenuItemCommand(NavigationSettingsMenuItemAbstractCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<M, DtoContainer<NavigationSettings>> cparam)
    {
        NavigationSettingsMenuItemAbstractCommandParam p = (NavigationSettingsMenuItemAbstractCommandParam)prepareParam(
                cparam);
        disableItem((M)p.getValue());
        SwitchNavigationMenuItemAction action = getAction();
        final Map<String, LinkedList<String>> menuItemPaths = p.getMenuItemPaths();
        action.setPathToMenuItem(menuItemPaths.get(((IMenuItem)p.getValue()).getCode()));
        action.setEnabled(false);
        action.setMenuItemCode(((IMenuItem)p.getValue()).getCode());
        dispatch.execute(action, new SimpleResultCallbackDecorator<>(param.getCallback()));
    }

    protected abstract SwitchNavigationMenuItemAction getAction();

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof IMenuItem))
        {
            return false;
        }
        M item = (M)input;
        return item.isEnabled();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.SWITCH_OFF;
    }

    private void disableItem(M item)
    {
        item.setEnabled(false);
        for (M child : (List<M>)item.getChildren())
        {
            disableItem(child);
        }
    }
}
