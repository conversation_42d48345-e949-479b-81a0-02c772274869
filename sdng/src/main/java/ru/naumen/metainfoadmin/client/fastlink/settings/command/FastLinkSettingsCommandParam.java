package ru.naumen.metainfoadmin.client.fastlink.settings.command;

import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSetting;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSettingWithTitles;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 01.03.18
 */
public class FastLinkSettingsCommandParam extends CommandParam<FastLinkSetting,
        List<DtoContainer<FastLinkSettingWithTitles>>>
{
    private List<FastLinkSetting> settings;

    public FastLinkSettingsCommandParam(@Nullable List<FastLinkSetting> settings,
            @Nullable AsyncCallback<List<DtoContainer<FastLinkSettingWithTitles>>> callback)
    {
        this(settings, (FastLinkSetting)null, callback);
    }

    public FastLinkSettingsCommandParam(@Nullable List<FastLinkSetting> settings,
            @Nullable FactoryParam.ValueSource<FastLinkSetting> valueSource,
            @Nullable AsyncCallback<List<DtoContainer<FastLinkSettingWithTitles>>> callback)
    {
        super(valueSource, callback);
        this.settings = settings;
    }

    public FastLinkSettingsCommandParam(@Nullable List<FastLinkSetting> settings, @Nullable FastLinkSetting value,
            @Nullable AsyncCallback<List<DtoContainer<FastLinkSettingWithTitles>>> callback)
    {
        super(value, callback);
        this.settings = settings;
    }

    @Override
    @SuppressWarnings("unchecked")
    public FastLinkSettingsCommandParam cloneIt()
    {
        return new FastLinkSettingsCommandParam(getSettings(), getValueSource(), getCallback());
    }

    public List<FastLinkSetting> getSettings()
    {
        return settings;
    }

    public void setSettings(List<FastLinkSetting> settings)
    {
        this.settings = settings;
    }
}
