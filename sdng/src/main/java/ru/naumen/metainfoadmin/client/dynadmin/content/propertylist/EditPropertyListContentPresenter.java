package ru.naumen.metainfoadmin.client.dynadmin.content.propertylist;

import java.util.ArrayList;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.ui.PropertyList;
import ru.naumen.metainfo.shared.ui.PropertyListBase;

/**
 * {@link Presenter} для редактирования группы атрибутов контента типа {@link PropertyList}
 *
 * <AUTHOR>
 * @since 13.08.2010
 *
 */
public class EditPropertyListContentPresenter<T extends PropertyListBase>
        extends EditHasAttrDescriptionContentPresenterBase<T>
{
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> attributeGroup;

    @Inject
    public EditPropertyListContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void updateCurrentContent()
    {
        super.updateCurrentContent();

        AttributeGroup grp = context.getMetainfo().getAttributeGroup(context.getMetainfo()
                .getAttributeGroup(SelectListPropertyValueExtractor.getValue(attributeGroup)).getCode());
        if (!content.getAttributeGroup().equals(grp.getCode()))
        {
            content.getAttributeToolPanels().clear();
        }

        content.setAttributeGroup(grp.getCode());
    }

    @Override
    protected void restoreContent(T oldContent)
    {
        super.restoreContent(oldContent);
        content.setAttributeGroup(oldContent.getAttributeGroup());
        content.setAttributeToolPanels(oldContent.getAttributeToolPanels());
    }

    @Override
    protected boolean isContentEquals(T oldContent)
    {
        return super.isContentEquals(oldContent)
               && eq(oldContent.getAttributeGroup(), content.getAttributeGroup());
    }

    @Override
    protected void bindCustomProperties()
    {
        bindAttrGroup();
    }

    private void bindAttrGroup()
    {
        attributeGroup.setCaption(commonMessages.attributeGroup());
        attributeGroup.setValidationMarker(true);
        DebugIdBuilder.ensureDebugId(attributeGroup, "selectAttributeGroup");
        getDisplay().add(attributeGroup);

        ArrayList<AttributeGroup> groups = Lists.newArrayList(context.getMetainfo().getAttributeGroups());
        metainfoUtils.sort(groups);
        for (AttributeGroup grp : groups)
        {
            attributeGroup.<SingleSelectCellList<?>> getValueWidget().addItem(grp.getTitle(), grp.getCode());
        }

        AttributeGroup currentGrp = context.getMetainfo().getAttributeGroup(content.getAttributeGroup());
        attributeGroup.trySetObjValue(currentGrp.getCode());
    }
}
