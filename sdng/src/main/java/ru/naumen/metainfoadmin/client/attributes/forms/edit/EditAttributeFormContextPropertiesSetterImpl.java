package ru.naumen.metainfoadmin.client.attributes.forms.edit;

import static ru.naumen.metainfo.shared.Constants.*;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.ATTRIBUTE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.HAS_DEFAULT_VALUE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.METAINFO;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.List;
import java.util.Map.Entry;

import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormContextPropertiesSetterImpl;

/**
 * Вычисление различных вспомогательных значений для формы редактирования атрибута
 * <AUTHOR>
 * @since 31.05.2012
 */
public class EditAttributeFormContextPropertiesSetterImpl
        extends AttributeFormContextPropertiesSetterImpl<ObjectFormEdit>
{
    @Override
    public void setContextProperties(PropertyContainerContext context)
    {
        super.setContextProperties(context);
        String attrType = context.getPropertyValues().getProperty(ATTR_TYPE);
        Attribute attr = context.getContextValues().getProperty(ATTRIBUTE);
        MetaClass metainfo = context.getContextValues().getProperty(METAINFO);
        Boolean editable = context.getPropertyValues().getProperty(EDITABLE);
        Boolean inherit = context.getPropertyValues().getProperty(INHERIT);
        Boolean isDateTimeAttr = DATE_TIME_TYPES.contains(attrType);
        ClassFqn fqn = metainfo != null ? metainfo.getFqn() : null;

        boolean hasDefaultValue = !NO_DEFAULT_VALUE_ATTRIBUTE_TYPES.contains(attrType)
                                  && !isDefaultValueRestricted(fqn, attr.getCode())
                                  && (attr.isSystemEditable() || attr.isWithDefaultValue() || attr.isComputable());

        boolean editableEnabled = attr.isSystemEditable() && !Constants.PARENT_ATTR.equals(attr.getCode())
                                  && !NOT_EDITABLE_ATTRIBUTE_TYPES.contains(attr.getType().getCode());
        boolean isEditableOnlyInListsType = ru.naumen.metainfo.shared.Constants.EDITABLE_ONLY_IN_LISTS_ATTRIBUTE_TYPES
                .contains(attrType);

        boolean editableInListsEnabled = (editableEnabled || isEditableOnlyInListsType)
                                         && EDITABLE_IN_LISTS_ATTRIBUTE_TYPES.contains(attrType);

        boolean requiredEnabled = !attr.isSystemRequired() && !NOT_REQUIRED_ATTRIBUTE_TYPES.contains(attrType)
                                  && !TYPES_WITH_IMMUTABLE_REQUIRED.contains(attrType);

        boolean uniqueEnabled = !attr.isSystemUnique()
                                && ru.naumen.metainfo.shared.Constants.UNIQUE_ATTRIBUTE_TYPES.contains(
                attr.getType().getCode())
                                && (metainfo.getFqn().getCase() == null || metainfo.getFqn()
                .equals(attr.getDeclaredMetaClass()));

        context.getContextValues().setProperty(HAS_DEFAULT_VALUE, hasDefaultValue);

        context.setEnabled(REQUIRED, requiredEnabled);
        context.setEnabled(REQUIRED_IN_INTERFACE, requiredEnabled);
        context.setEnabled(EDITABLE, editableEnabled);
        context.setEnabled(EDITABLE_IN_LISTS, editableInListsEnabled);
        context.setEnabled(UNIQUE, uniqueEnabled);
        context.setEnabled(DEFAULT_VALUE, !inherit);
        context.setPropertyEnabled(SCRIPT, !inherit);
        context.setPropertyEnabled(SCRIPT_FOR_FILTRATION, !inherit);
        context.setPropertyEnabled(FILTERED_BY_SCRIPT, !inherit);
        context.setPropertyEnabled(SCRIPT_FOR_DEFAULT, !inherit);
        context.setPropertyEnabled(DEFAULT_BY_SCRIPT, !inherit);
        context.setPropertyEnabled(REQUIRED, !inherit);
        context.setPropertyEnabled(UNIQUE, editable && !inherit);
        context.setPropertyEnabled(EDITABLE_IN_LISTS, (editable || isEditableOnlyInListsType) && !inherit);
        context.setPropertyEnabled(SUGGEST_CATALOG, !inherit);
        context.setPropertyEnabled(SELECT_SORTING, !inherit);
        context.setPropertyEnabled(TARGET, !inherit);
        context.setPropertyEnabled(DESCRIPTION, !inherit);
        context.setPropertyEnabled(DATE_TIME_COMMON_RESTRICTIONS, isDateTimeAttr && !inherit);
        context.setPropertyEnabled(DATE_TIME_RESTRICTION_SCRIPT, isDateTimeAttr && !inherit);
        context.setPropertyEnabled(DATE_TIME_RESTRICTION_TYPE, isDateTimeAttr && !inherit);
        context.setPropertyEnabled(DATE_TIME_RESTRICTION_ATTRIBUTE, isDateTimeAttr && !inherit);
        context.setPropertyEnabled(DATE_TIME_RESTRICTION_CONDITION, isDateTimeAttr && !inherit);
    }

    private boolean isDefaultValueRestricted(ClassFqn fqn, String attrCode)
    {
        for (Entry<String, List<ClassFqn>> entry : NO_DEFAULT_VALUE_ATTRIBUTE_CODES().entrySet())
        {
            String code = entry.getKey();
            List<ClassFqn> fqns = entry.getValue();
            if (attrCode.equals(code))
            {
                if (fqns.isEmpty())
                {
                    //неуказанный fqn = любой fqn
                    return true;
                }
                else
                {
                    for (ClassFqn checkedFqn : fqns)
                    {
                        if (checkedFqn.equals(fqn) || checkedFqn.isClassOf(fqn))
                        {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
}