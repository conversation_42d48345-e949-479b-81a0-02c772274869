/**
 *
 */
package ru.naumen.metainfoadmin.client.escalation.vmap.item.toolbar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.base.Function;
import com.google.common.base.Functions;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.content.property.PropertiesReadyEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.GetEscalationSchemesAction;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.rows.props.VMapItemRowPropertyContainer;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.rows.props.target.VMapItemRowTargetPropertyContext;
import ru.naumen.metainfoadmin.client.escalation.EscalationMessages;

/**
 * <AUTHOR>
 * @since 02.11.2012
 *
 */
public class EscalationVMapItemTargetPropertyContainer implements VMapItemRowPropertyContainer
{
    /**
     * Небольшой хак - установка значения свойства происходит в VMapItemRowPropertiesReadyHandler
     * без возможности преобразования значения. Но нам-то надо устанавливать не набор эскалаций, а набор их кодов
     * Поэтому чтобы там не вызывалось setValue, делаем название свойства не соотв. context.getValues() 
     */
    private static final List<String> PROPERTIES = Arrays.asList(ValueMapCatalogItem.ESCALATION_TARGET_DATA + "1");

    @Inject
    MultiSelectBoxProperty property;
    @Inject
    DispatchAsync dispatch;
    @Inject
    I18nUtil i18nUtil;
    @Inject
    NotEmptyCollectionValidator<Collection<SelectItem>> validator;
    @Inject
    EscalationMessages messages;

    private final VMapItemRowTargetPropertyContext context;
    private final Processor validationProcessor;
    private Function<String, EscalationScheme> valueFilter;

    @Inject
    public EscalationVMapItemTargetPropertyContainer(@Assisted VMapItemRowTargetPropertyContext context,
            @Assisted Processor validationProcessor)
    {
        this.context = context;
        this.validationProcessor = validationProcessor;
    }

    @Override
    public void bind()
    {
        property.setCaption(messages.escalationSchemes());
        property.setValidationMarker(true);
        validationProcessor.validate(property, validator);
        dispatch.execute(new GetEscalationSchemesAction(getTargetTypes()),
                new BasicCallback<SimpleResult<ArrayList<DtoContainer<EscalationScheme>>>>()
                {
                    @Override
                    @SuppressWarnings("unchecked")
                    protected void handleSuccess(SimpleResult<ArrayList<DtoContainer<EscalationScheme>>> response)
                    {
                        List<EscalationScheme> escalationSchemes =
                                response.get().stream().map(DtoContainer::get).collect(Collectors.toList());
                        Map<String, EscalationScheme> schemes = CollectionUtils.convertToMap(escalationSchemes,
                                HasCode.CODE_EXTRACTOR, Functions.<EscalationScheme> identity());
                        valueFilter = Functions.forMap(schemes, null);
                        for (EscalationScheme scheme : escalationSchemes)
                        {
                            property.getValueWidget().addItem(i18nUtil.getLocalizedTitle(scheme), scheme.getCode());
                        }

                        Collection<EscalationScheme> schemeValue = (Collection<EscalationScheme>)context.getValues()
                                .get(ValueMapCatalogItem.ESCALATION_TARGET_DATA);
                        if (schemeValue != null)
                        {
                            property.trySetObjValue(Collections2.transform(schemeValue, HasCode.CODE_EXTRACTOR));
                        }
                        context.getLocalEventBus().fireEvent(
                                new PropertiesReadyEvent(Lists.<Property<?>> newArrayList(property)));
                    }
                });

    }

    @Override
    public List<String> getProperties()
    {
        return PROPERTIES;
    }

    @Override
    public void getValues()
    {
        Set<EscalationScheme> schemes = Sets
                .newHashSet(Collections2.transform(SelectListPropertyValueExtractor.getValue(property), valueFilter));
        context.getValues().put(ValueMapCatalogItem.ESCALATION_TARGET_DATA, schemes);
    }

    private HashSet<ClassFqn> getTargetTypes()
    {
        if (context.getCatalogItem() == null)
        {
            return null;
        }
        Collection<DtObject> linkedClasses = context.getCatalogItem().getProperty(ValueMapCatalogItem.LINKED_CLASSES);
        return Sets.newHashSet(Collections2.transform(linkedClasses, DtObject.FQN_EXTRACTOR));
    }
}