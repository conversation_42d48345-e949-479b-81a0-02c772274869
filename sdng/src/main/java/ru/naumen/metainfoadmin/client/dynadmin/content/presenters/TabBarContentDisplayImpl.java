package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import jakarta.inject.Inject;

import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.AbsolutePanel;
import com.google.gwt.user.client.ui.ComplexPanel;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HasText;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.common.client.utils.HtmlSanitizeUtils;
import ru.naumen.core.client.content.tabbar.CompactTabContentDisplayImpl;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.utils.GWTUtils;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfoadmin.client.common.content.ActionContentPanelDisplayFactory;
import ru.naumen.metainfoadmin.client.common.content.DraggableContentDisplay;

/**
 * Контент "Панель вкладок" в админке
 *
 * @see TabBarContentDisplay
 * <AUTHOR>
 * @since 10.08.2010
 *
 */
// TODO NSDPRD-12460 dzevako выделить отдельный дисплей для вкладок карточки
public class TabBarContentDisplayImpl extends DraggableContentDisplay implements TabBarContentDisplay
{
    /**
     * Тулбар настройки Панели вкладок
     */
    private final ToolBarDisplay toolBar;
    private final CompactTabContentDisplayImpl tabPanel;
    private final FlowPanel wrapper;
    private final FlowPanel header;
    private final FlowPanel captionPanel;
    private final FlowPanel captionWrapperPanel;
    private final HtmlSanitizeUtils htmlSanitizeUtils;

    @Inject
    public TabBarContentDisplayImpl(ActionContentPanelDisplayFactory displayFactory, FontIconFactory<?> iconFactory,
            CompactTabContentDisplayImpl tabPanel, ToolBarDisplay toolBar,
            HtmlSanitizeUtils htmlSanitizeUtils)
    {
        super(displayFactory);
        this.htmlSanitizeUtils = htmlSanitizeUtils;
        WidgetResources.INSTANCE.all().ensureInjected();
        this.toolBar = toolBar;
        this.tabPanel = tabPanel;
        wrapper = new FlowPanel();
        header = new FlowPanel();
        captionWrapperPanel = new FlowPanel();
        captionPanel = new FlowPanel();
        FontIconDisplay<?> icon = iconFactory.create(IconCodes.EDIT);
        initObjectCardCaptionPanel(icon.asWidget());
        // Пока табпанель админки наследуется от TitledBlockDisplayImpl - нужно скрывать название,
        // т.к. нужен только контейнер
        superSetCaptionVisible(false);
        // Добавление toolBar и tabPanel на панель делается в onLoad().
    }

    @Override
    public HandlerRegistration addSelectionHandler(SelectionHandler<Integer> handler)
    {
        return tabPanel.addSelectionHandler(handler);
    }

    @Override
    public void clearTabPanel()
    {
        tabPanel.clear();
    }

    @Override
    public HasText createTabCaption(Tab tab)
    {
        Label tabCaption = new Label();
        tabCaption.setWordWrap(false);
        DebugIdBuilder.ensureDebugId(tabCaption, "Tab." + tab.getUuid());
        return tabCaption;
    }

    @Override
    public void drawActionBar(Display display)
    {
        display.asWidget().addStyleName(WidgetResources.INSTANCE.all().toolPanelIsInsideUIDisplayContent());
        header.add(display.asWidget());
    }

    @Override
    public FlowPanel getCaptionPanel()
    {
        return captionPanel;
    }

    @Override
    public int getSelectedTab()
    {
        return tabPanel.getSelectedTab();
    }

    @Override
    public ToolBarDisplay getToolBar()
    {
        return toolBar;
    }

    @Override
    public void init()
    {
        tabPanel.init();
    }

    @Override
    public void insertTab(IsWidget content, IsWidget tab, IsWidget layoutEdit, int beforeIndex)
    {
        FlowPanel outer = new FlowPanel();
        outer.add(content);
        outer.add(layoutEdit);
        insert(outer, tab, beforeIndex);
    }

    @Override
    public void insertTab(IsWidget content, IsWidget toolPanel, IsWidget tab, IsWidget layoutEdit, int beforeIndex)
    {
        FlowPanel outer = new FlowPanel();
        outer.add(toolPanel);
        outer.add(content);
        outer.add(layoutEdit);
        insert(outer, tab, beforeIndex);
    }

    @Override
    public void moveTab(int index, boolean toLeft)
    {
        int newIndex = index + (toLeft ? -1 : +1);

        if (index < 0 || newIndex < 0 || newIndex >= tabPanel.getWidgetCount())
        {
            return;
        }
        Widget content = tabPanel.getWidget(index);
        Widget tabContent = GWTUtils.getWrapped((Composite)tabPanel.getTabWidget(index));
        int beforeIndex = newIndex + (toLeft ? 0 : 1);
        insert(content, tabContent, beforeIndex);
        tabPanel.selectTab(newIndex);
    }

    @Override
    public boolean needToFrame()
    {
        return false;
    }

    @Override
    public void removeTab(int index)
    {
        tabPanel.remove(index);
    }

    @Override
    public void selectTab(int index)
    {
        tabPanel.selectTab(index);
    }

    @Override
    public void setCaptionPanelVisible(boolean visible)
    {
        captionWrapperPanel.setVisible(visible);
    }

    @Override
    public void setCaptionText(String text)
    {
        ((HTML)captionPanel.getWidget(0)).setHTML(htmlSanitizeUtils.sanitize(text));
    }

    @Override
    public void setHeaderVisible(boolean visible)
    {
        header.setVisible(visible);
    }

    @Override
    protected void customize()
    {
        //Не применяем стили , нужные для остальных контентов.
    }

    @Override
    protected void onEnsureDebugId(String baseID)
    {
        DebugIdBuilder.ensureDebugId(toolBar.asWidget(), "toolBar");
        DebugIdBuilder.ensureDebugId(tabPanel.asWidget(), "tabPanel");
        DebugIdBuilder.ensureDebugId(wrapper.asWidget(), "panelWrapper");
        DebugIdBuilder.ensureDebugId(header.asWidget(), "panelHeader");
        DebugIdBuilder.ensureDebugId(captionPanel.asWidget(), "objectCardCaptionPanel");
        super.onEnsureDebugId(baseID);
    }

    @Override
    protected void onLoad()
    {
        super.onLoad();
        // Создаем панель - AbsolutePanel или FlowPanel.
        ComplexPanel panel = DraggableContentDisplay.getParentTabDisplay(this) != null ? new FlowPanel()
                : new AbsolutePanel();
        panel.add(toolBar);
        panel.add(wrapper);
        wrapper.add(header);
        header.insert(captionPanel, 0);
        wrapper.add(tabPanel.getTabBar());
        wrapper.add(tabPanel);
        addWidget(panel);
    }

    @Override
    public void updateStyles(boolean isHead)
    {
        AdminWidgetResources res = AdminWidgetResources.INSTANCE;
        if (isHead)
        {
            header.addStyleName(res.metainfoAdmin().cardHeader());
            wrapper.addStyleName(res.metainfoAdmin().uiDisplayContent());
            wrapper.addStyleName(res.all().objectCard());
            captionPanel.addStyleName(res.metainfoAdmin().editableBlockView());
        }
        else
        {
            addStyleName(res.contentLayout().editableBlock());
            wrapper.addStyleName(res.tablayoutpanel().tabbarContentWrapper());
        }
        toolBar.addStyleName(res.all().toolPanelContentSettings());
    }

    private void initObjectCardCaptionPanel(Widget icon)
    {
        HTML html = new HTML(); // NOPMD NSDPRD-28509 unsafe html
        html.ensureDebugId("objectCardCaption");
        html.addStyleName(WidgetResources.INSTANCE.contentHeader().headerTitleLine());
        html.addStyleName(AdminWidgetResources.INSTANCE.metainfoAdmin().objectCardCaption());
        captionPanel.add(html);

        icon.ensureDebugId("objectCardCaptionIcon");
        captionPanel.add(icon);
    }

    private void insert(IsWidget content, IsWidget tab, int beforeIndex)
    {
        tabPanel.insert(content, tab, beforeIndex);
    }
}
