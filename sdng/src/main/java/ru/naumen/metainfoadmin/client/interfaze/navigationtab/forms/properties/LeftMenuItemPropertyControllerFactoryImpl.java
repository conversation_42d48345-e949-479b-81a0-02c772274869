package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.metainfo.shared.Constants.MAX_CUSTOM_LINK;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import ru.naumen.core.client.validation.CustomLinkValidator;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyObjectValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.StringLengthValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.LabelProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindTextBoxFactory;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateValidator;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.AttributeForFillByCurrentObjectBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.QuickFormBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.QuickFormBindDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.ActionRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.AttributeForFillByCurrentObjectRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.CustomFormRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.CustomFormRefreshDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.GoToCardAfterCreationRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.UseQuickAddFormRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.QuickFormVCHDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.QuickFormVCHDelegete;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.UseCustomFormVCHDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.UseCustomFormVCHDelegateFactory;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetBindDelegate;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetRefreshDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentAfterHierarchyAttributeControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentAttrChainMetaClassRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentAttributeControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentAttributeGroupRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentBeforeHierarchyAttributeControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentCommonVCHDelegateFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentContentTemplateRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentContentTypeValueBindDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentHierarchyClassRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentHierarchyStructureBindDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentHierarchyStructureRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentLinkObjectAttributeControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentLinkObjectCaseRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentLinkObjectUUIDRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentLinkObjectUUIDValidatorDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentLinkObjectValueBindDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentLinkObjectValueRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentLinkObjectValueValidatorDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentListPresentationBindDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentListPresentationRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentMetaClassRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentNestedAttrLinkRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentObjectCasesRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentRelListWithHierarchyRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentSelectRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentShowHierarchyRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentShowNestedInNestedDelegateRefreshImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentTextBoxRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentValidatedListBoxPropertyBindDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentValidatedListBoxWithEmptyPropertyBindDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.ListTemplateSettingsTreePropertyControllerFactory;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Фабрика контроллеров атрибутов элемента левого меню
 *
 * <AUTHOR>
 * @since 25.06.2020
 */
public class LeftMenuItemPropertyControllerFactoryImpl
        extends PropertyControllerFactorySyncImpl<LeftMenuItemSettingsDTO, ObjectFormEdit>
{
    @Inject
    private PropertyControllerSyncFactoryInj<Boolean, BooleanCheckBoxProperty> booleanPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxProperty> listBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxWithEmptyPropertyFactory;
    @Inject
    private ListTemplateSettingsTreePropertyControllerFactory treeSelectPropertyFactory;
    @Inject
    private LinkToContentAttributeControllerFactory attributeChainFactory;
    @Inject
    private LinkToContentLinkObjectAttributeControllerFactory linkObjectAttributeFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, SingleSelectProperty<LeftMenuItemSettingsDTO>> selectMenuItemPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, DtObjectSelectProperty> dtoSelectPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, SingleSelectProperty<Reference>> referenceValuePropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<Collection<SelectItem>, MultiSelectBoxProperty> multiSelectPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<Boolean, BooleanCheckBoxProperty> checkBoxPropertyFactory;
    @Inject
    private UseCustomFormVCHDelegateFactory useCustomFormVCHDelegateFactory;
    @Inject
    private CustomFormRefreshDelegateFactory customFormRefreshDelegateFactory;
    @Inject
    private QuickFormBindDelegateFactory quickFormBindDelegateFactory;
    @Inject
    private QuickFormVCHDelegateFactory quickFormVCHDelegateFactory;

    @Inject
    private LeftMenuItemTitleVCHDelegateImpl titleVCHDelegate;

    @Inject
    private LeftMenuItemTypeBindDelegateImpl typeBindDelegate;
    @Inject
    private LeftMenuItemTypeVCHDelegateImpl typeVCHDelegate;

    @Inject
    private LeftMenuItemParentBindDelegateImpl parentBindDelegate;
    @Inject
    private LeftMenuItemParentDelegateRefreshImpl parentRefreshDelegate;

    @Inject
    private LeftMenuItemPresentationBindDelegateImpl presentationBindDelegate;
    @Inject
    private LeftMenuItemPresentationVCHDelegateImpl presentationVCHDelegate;

    @Inject
    private ReferenceValuePropertyDelegateRefreshImpl referenceValueRefreshDelegate;
    @Inject
    private ReferenceValueBindDelegateImpl referenceValueBindDelegate;
    @Inject
    private ReferenceValuePropertyVCHImpl referenceVCH;

    @Inject
    private ReferenceValueTabBindDelegateImpl referenceTabBindDelegate;
    @Inject
    private ReferenceValueTabPropertyDelegateRefreshImpl referenceTabRefreshDelegate;
    @Inject
    private ReferenceTabDelegateVCHImpl referenceTabDelegateVCH;
    @Inject
    private ReferenceTemplateRefreshDelegateImpl referenceTemplateRefreshDelegate;
    @Inject
    private ReferenceTemplateDelegateVCHImpl referenceTemplateDelegateVCH;

    @Inject
    private MenuSettingsIconBindDelegateImpl iconBindDelegate;
    @Inject
    private MenuSettingsIconDelegateRefreshImpl iconDelegateRefresh;

    @Inject
    private MenuSettingsAbbreviationDelegateRefreshImpl abbreviationDelegateRefresh;
    @Inject
    private MenuSettingsAbbreviationVCHDelegateImpl abbreviationVCHDelegate;

    @Inject
    private MenuSettingsProfileValueBindDelegateImpl profilesBindDelegate;
    @Inject
    private MenuSettingsProfileDelegateRefreshImpl profilesDelegateRefresh;

    @Inject
    private LeftMenuFormattingBindDelegateImpl formattingBindDelegate;
    @Inject
    private LeftMenuFormattingRefreshDelegateImpl formattingDelegateRefresh;

    @Inject
    private LinkToContentCommonVCHDelegateFactory commonVCHDelegateFactory;

    @Inject
    private LinkToContentValidatedListBoxPropertyBindDelegateImpl linkToContentValidatedSelectBindDelegate;
    @Inject
    private LinkToContentValidatedListBoxWithEmptyPropertyBindDelegateImpl linkToContentValidatedSelectWithEmptyBindDelegate;
    @Inject
    private LinkToContentSelectRefreshDelegateImpl linkToContentSelectDelegateRefresh;
    @Inject
    private LinkToContentListPresentationRefreshDelegateImpl linkToContentListPresentationRefreshDelegate;
    @Inject
    private LinkToContentTextBoxRefreshDelegateImpl linkToContentTextBoxDelegateRefresh;

    @Inject
    private LinkToContentContentTypeValueBindDelegateImpl contentTypeBindDelegate;

    @Inject
    private LinkToContentLinkObjectValueBindDelegateImpl linkObjectValueBindDelegate;
    @Inject
    private LinkToContentLinkObjectValueRefreshDelegateImpl linkObjectValueDelegateRefresh;
    @Inject
    private LinkToContentLinkObjectCaseRefreshDelegateImpl linkObjectCaseDelegateRefresh;

    @Inject
    private LinkToContentShowNestedInNestedDelegateRefreshImpl showNestedInNestedDelegateRefresh;

    @Inject
    private LinkToContentMetaClassRefreshDelegateImpl objectClassDelegateRefresh;
    @Inject
    private LinkToContentAttrChainMetaClassRefreshDelegateImpl chainAttrClassDelegateRefresh;

    @Inject
    private LinkToContentObjectCasesRefreshDelegateImpl objectCasesDelegateRefresh;
    @Inject
    private LinkToContentLinkObjectUUIDRefreshDelegateImpl linkObjectUUIDDelegateRefresh;

    @Inject
    private LinkToContentAttributeGroupRefreshDelegateImpl attributeGroupDelegateRefresh;

    @Inject
    private PropertyControllerSyncFactoryInj<String, LabelProperty> labelPropertyFactory;
    @Inject
    private LinkToContentBeforeHierarchyAttributeControllerFactory beforeHierarchyAttributeFactory;
    @Inject
    private LinkToContentAfterHierarchyAttributeControllerFactory afterHierarchyAttributeFactory;

    @Inject
    private LinkToContentShowHierarchyRefreshDelegateImpl showHierarchyDelegateRefresh;

    @Inject
    private LinkToContentRelListWithHierarchyRefreshDelegateImpl relListWithHierarchyRefreshDelegate;

    @Inject
    private LinkToContentNestedAttrLinkRefreshDelegateImpl nestedAttrLinkDelegateRefresh;

    @Inject
    private LinkToContentHierarchyClassRefreshDelegateImpl hierarchyClassDelegateRefresh;
    //  TODO Пока скрываем с формы, так как ждем починки дефекта NSDPRD-14869 на кнопку "Добавить связь"
    //   {$link https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$107823141}
    //
    //    @Inject
    //    private LinkToContentAddLinkFormTitleDelegateRefreshImpl addLinkFormTitleDelegateRefresh;

    @Inject
    private LinkToContentListPresentationBindDelegate listPresentationBindDelegate;

    @Inject
    private LinkToContentHierarchyStructureRefreshDelegateImpl linkToContentHierarchyStructureRefreshDelegate;
    @Inject
    private LinkToContentHierarchyStructureBindDelegateImpl linkToContentHierarchyStructureBindDelegate;
    @Inject
    private LinkToContentContentTemplateRefreshDelegateImpl linkToContentContentTemplateRefreshDelegate;

    @Inject
    private PropertyDelegateBindTextBoxFactory textBoxBindDelegateFactory;

    @Inject
    private PropertyControllerSyncFactoryInj<Collection<SelectItem>, TagsProperty> tagsPropertyFactory;
    @Inject
    private MenuSettingsTagsBindDelegateImpl tagsRefreshDelegate;
    @Inject
    private CustomLinkValuePropertyDelegateRefreshImpl customLinkValueRefreshDelegate;
    @Inject
    private CustomSystemLinkValuePropertyDelegateRefreshImpl customSystemLinkValueRefreshDelegate;
    @Inject
    private CustomSystemLinkValuePropertyVCHImpl customSystemLinkVCH;
    @Inject
    private NewTabValuePropertyDelegateRefreshImpl newTabValueRefreshDelegate;
    @Inject
    private AddButtonLeftMenuTreePropertyControllerFactory addButtonPropertyFactory;
    @Inject
    private AddButtonLeftMenuPropertyDelegateRefreshImpl addButtonRefreshDelegate;
    @Inject
    private ActionRefreshDelegate actionRefreshDelegate;
    @Inject
    private UseQuickAddFormRefreshDelegate useQuickAddFormRefreshDelegate;
    @Inject
    private GoToCardAfterCreationRefreshDelegate goToCardAfterCreationRefreshDelegate;
    @Inject
    private AttributeForFillByCurrentObjectBindDelegate attributeForFillByCurrentObjectBindDelegate;
    @Inject
    private AttributeForFillByCurrentObjectRefreshDelegate attributeForFillByCurrentObjectRefreshDelegate;
    @Inject
    private AttrChainMenuItemControllerFactory attrChainMenuItemControllerFactory;
    @Inject
    private TypeOfCardReferenceValueBindDelegateImpl typeOfCardReferenceValueBindDelegate;
    @Inject
    private TypeOfCardReferenceDelegateRefreshImpl typeOfCardReferenceDelegateRefreshImpl;
    @Inject
    private TypeOfCardReferenceVCHImpl typeOfCardReferenceVCH;
    @Inject
    private UseAttrTitleReferenceBindDelegateImpl useAttrTitleReferenceBindDelegate;
    @Inject
    private UseAttrTitleReferenceDelegateRefreshImpl useAttrTitleReferenceDelegateRefreshImpl;
    @Inject
    private UseAttrTitleLinkValuePropertyVCHImpl useAttrTitleLinkValuePropertyVCH;
    @Inject
    private AttrForUseInTitleDelegateBind attrForUseInTitleDelegateBind;
    @Inject
    private AttrForUseInTitleDelegateRefresh attrForUseInTitleDelegateRefresh;
    @Inject
    private TitleReferenceDelegateRefresh titleReferenceDelegateRefresh;
    @Inject
    private AttrChainReferenceDelegateRefreshImpl attrChainReferenceDelegateRefreshImpl;
    @Inject
    private ObjectClassReferenceBindDelegateImpl objectClassBindDelegate;
    @Inject
    private ObjectClassReferenceDelegateRefresh objectClassReferenceDelegateRefresh;
    @Inject
    private ObjectCasesReferenceDelegateBindImpl objectCasesReferenceDelegateBind;
    @Inject
    private ObjectCasesReferenceDelegateRefreshImpl objectCasesReferenceDelegateRefresh;
    @Inject
    private ObjectCasesPropertyVCHImpl objectCasesPropertyVCH;

    @Inject
    SettingsSetBindDelegate settingsSetBindDelegate;
    @Inject
    SettingsSetRefreshDelegate settingsSetRefreshDelegate;

    private final Map<Validator<String>, String> titleValidators = new HashMap<>();
    private final Map<Validator<String>, String> abbrevValidators = new HashMap<>();
    private final Map<Validator<SelectItem>, String> iconValueValidators = new HashMap<>();
    private final Map<Validator<SelectItem>, String> referenceValueValidators = new HashMap<>();
    private final Map<Validator<String>, String> linkObjectUUIDValidators = new HashMap<>();
    private final Set<PropertyDelegateValidator<String>> linkObjectUUIDValidatorDelegates = new HashSet<>();
    private final Set<PropertyDelegateValidator<SelectItem>> linkObjectValueValidatorDelegates = new HashSet<>();
    private final Map<Validator<String>, String> notEmptyValidators = new HashMap<>();
    private final Map<Validator<SelectItem>, String> notEmptySelectValidators = new HashMap<>();
    private final Map<Validator<RelationsAttrTreeObject>, String> notEmptyAttrObjectValidators = new HashMap<>();
    private final Map<Validator<String>, String> customLinkValidators = new HashMap<>();
    private final Map<Validator<Collection<DtObject>>, String> addButtonValueValidators = new HashMap<>();

    @Inject
    public void setUpValidators(NotEmptyValidator notEmptyValidator, StringLengthValidator stringLengthValidator,
            StringLengthValidator stringShortLengthValidator,
            NotEmptyObjectValidator<SelectItem> notEmptyObjectValidator,
            NotNullValidator<SelectItem> notNullReferenceValidator,
            NotNullValidator<RelationsAttrTreeObject> notNullAttrObjectValidator,
            LinkToContentLinkObjectUUIDValidatorDelegate objectUUIDValidatorDelegate,
            LinkToContentLinkObjectValueValidatorDelegate objectValueValidatorDelegate,
            CustomLinkValidator customLinkValidator,
            NotEmptyCollectionValidator<Collection<DtObject>> notEmptyCollectionValidator)
    {
        titleValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        titleValidators.put(stringLengthValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        abbrevValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        abbrevValidators.put(stringShortLengthValidator.setMaxLength(2),
                PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        iconValueValidators.put(notEmptyObjectValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        referenceValueValidators.put(notNullReferenceValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        linkObjectUUIDValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        linkObjectUUIDValidatorDelegates.add(objectUUIDValidatorDelegate);
        linkObjectValueValidatorDelegates.add(objectValueValidatorDelegate);
        notEmptyValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        notEmptySelectValidators.put(notNullReferenceValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        notEmptyAttrObjectValidators.put(notNullAttrObjectValidator,
                PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        customLinkValidators.put(customLinkValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        addButtonValueValidators.put(notEmptyCollectionValidator,
                PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }

    @Override
    protected void build()
    {
        PropertyDelegateBind<String, TextBoxProperty> titleBindDelegate = textBoxBindDelegateFactory
                .create(Constants.MAX_TITLE_LENGTH);
        PropertyDelegateBind<String, TextBoxProperty> abbrevBindDelegate = textBoxBindDelegateFactory
                .create(2);
        PropertyDelegateBind<String, TextBoxProperty> linkBindDelegate = textBoxBindDelegateFactory
                .create(MAX_CUSTOM_LINK);
        UseCustomFormVCHDelegate useQuickAddFormVCHDelegate = useCustomFormVCHDelegateFactory.create(
                ToolFormPropertyCodes.USE_QUICK_ADD_FORM, ToolFormPropertyCodes.QUICK_ADD_FORM);
        CustomFormRefreshDelegate quickAddFormRefreshDelegate = customFormRefreshDelegateFactory.create(
                ToolFormPropertyCodes.USE_QUICK_ADD_FORM);
        QuickFormBindDelegate quickAddFormBindDelegate = quickFormBindDelegateFactory.create(
                ToolFormPropertyCodes.QUICK_ADD_FORM);
        QuickFormVCHDelegete quickAddFormVCHDelegete = quickFormVCHDelegateFactory.create(
                ToolFormPropertyCodes.QUICK_ADD_FORM);
        //@formatter:off
        register(MenuItemPropertyCode.TITLE, textBoxPropertyFactory)
            .setBindDelegate(titleBindDelegate)
            .setRefreshDelegate(titleReferenceDelegateRefresh)
            .setVchDelegate(titleVCHDelegate)
            .setValidators(titleValidators);
        register(MenuItemPropertyCode.TYPE, listBoxPropertyFactory)
            .setBindDelegate(typeBindDelegate).setVchDelegate(typeVCHDelegate);
        register(MenuItemPropertyCode.USE_ATTR_TITLE, checkBoxPropertyFactory)
                .setBindDelegate(useAttrTitleReferenceBindDelegate)
                .setRefreshDelegate(useAttrTitleReferenceDelegateRefreshImpl)
                .setVchDelegate(useAttrTitleLinkValuePropertyVCH);
        register(MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE, dtoSelectPropertyFactory)
                .setBindDelegate(attrForUseInTitleDelegateBind)
                .setRefreshDelegate(attrForUseInTitleDelegateRefresh)
                .setValidators(referenceValueValidators);
        register(MenuItemPropertyCode.TYPE_OF_CARD, dtoSelectPropertyFactory)
            .setBindDelegate(typeOfCardReferenceValueBindDelegate)
            .setRefreshDelegate(typeOfCardReferenceDelegateRefreshImpl)
            .setVchDelegate(typeOfCardReferenceVCH)
            .setValidators(referenceValueValidators);
        register(ReferenceCode.ATTRIBUTE_CHAIN, attrChainMenuItemControllerFactory)
                .setRefreshDelegate(attrChainReferenceDelegateRefreshImpl)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                                ReferenceCode.OBJECT_CLASS,
                                ReferenceCode.OBJECT_CASES,
                                ReferenceCode.REFERENCE_VALUE,
                                MenuItemPropertyCode.ATTR_FOR_USE_IN_TITLE
                        ),new ArrayList<>()))
                .setValidators(notEmptyAttrObjectValidators);
        register(ReferenceCode.OBJECT_CLASS, textBoxPropertyFactory)
                .setBindDelegate(objectClassBindDelegate)
                .setRefreshDelegate(objectClassReferenceDelegateRefresh);
        register(ReferenceCode.OBJECT_CASES, multiSelectPropertyFactory)
                .setBindDelegate(objectCasesReferenceDelegateBind)
                .setRefreshDelegate(objectCasesReferenceDelegateRefresh)
                .setVchDelegate(objectCasesPropertyVCH);
        register(MenuItemPropertyCode.ADD_BUTTON_VALUE, addButtonPropertyFactory)
                .setRefreshDelegate(addButtonRefreshDelegate)
                .setValidators(addButtonValueValidators);
        register(MenuItemPropertyCode.PARENT, selectMenuItemPropertyFactory)
            .setBindDelegate(parentBindDelegate)
            .setRefreshDelegate(parentRefreshDelegate);
        register(ReferenceCode.REFERENCE_VALUE, dtoSelectPropertyFactory)
            .setBindDelegate(referenceValueBindDelegate)
            .setRefreshDelegate(referenceValueRefreshDelegate)
            .setVchDelegate(referenceVCH)
            .setValidators(referenceValueValidators);
        register(MenuItemPropertyCode.REFERENCE_TAB_VALUE, referenceValuePropertyFactory)
            .setBindDelegate(referenceTabBindDelegate)
            .setVchDelegate(referenceTabDelegateVCH)
            .setRefreshDelegate(referenceTabRefreshDelegate);
        register(MenuItemPropertyCode.REFERENCE_UI_TEMPLATE, listBoxWithEmptyPropertyFactory)
            .setRefreshDelegate(referenceTemplateRefreshDelegate)
            .setVchDelegate(referenceTemplateDelegateVCH);

        register(MenuSettingsPropertyCode.PRESENTATION, listBoxPropertyFactory)
                .setBindDelegate(presentationBindDelegate).setVchDelegate(presentationVCHDelegate);
        register(MenuSettingsPropertyCode.ABBREVIATION, textBoxPropertyFactory)
                .setBindDelegate(abbrevBindDelegate)
                .setVchDelegate(abbreviationVCHDelegate)
                .setRefreshDelegate(abbreviationDelegateRefresh)
                .setValidators(abbrevValidators);
        register(MenuSettingsPropertyCode.TAGS, tagsPropertyFactory)
               .setBindDelegate(tagsRefreshDelegate);
        register(MenuSettingsPropertyCode.ICON, dtoSelectPropertyFactory)
                .setBindDelegate(iconBindDelegate)
                .setRefreshDelegate(iconDelegateRefresh)
                .setValidators(iconValueValidators);
        register(MenuSettingsPropertyCode.PROFILES, multiSelectPropertyFactory)
                .setRefreshDelegate(profilesDelegateRefresh)
                .setBindDelegate(profilesBindDelegate);
        register(MenuItemPropertyCode.FORMATTING, listBoxPropertyFactory)
                .setBindDelegate(formattingBindDelegate)
                .setRefreshDelegate(formattingDelegateRefresh);

        register(MenuItemLinkToContentCode.CONTENT_TYPE, listBoxPropertyFactory)
                .setBindDelegate(contentTypeBindDelegate)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.LINK_OBJECT,
                                MenuItemLinkToContentCode.LINK_OBJECT_CASE,
                                MenuItemLinkToContentCode.LINK_OBJECT_ATTR,
                                MenuItemLinkToContentCode.LINK_OBJECT_UUID,
                                MenuItemLinkToContentCode.SHOW_HIERARCHY,
                                MenuItemLinkToContentCode.SHOW_NESTED_IN_NESTED,
                                MenuItemLinkToContentCode.ATTR_CHAIN,
                                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.HIERARCHY_CLASS,
                                MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                                MenuItemLinkToContentCode.OBJECT_CLASS,
                                MenuItemLinkToContentCode.OBJECT_CASES,
                                MenuItemLinkToContentCode.ATTRIBUTE_GROUP,
                                MenuItemLinkToContentCode.LIST_PRESENTATION,
                                MenuItemLinkToContentCode.LIST_TEMPLATE,
                                MenuItemLinkToContentCode.CONTENT_TEMPLATE,
                                MenuItemLinkToContentCode.LIST_PAGE_TITLE,
                                MenuItemLinkToContentCode.HIERARCHY_STRUCTURE),
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.LINK_OBJECT,
                                MenuItemLinkToContentCode.LINK_OBJECT_CASE,
                                MenuItemLinkToContentCode.LINK_OBJECT_ATTR,
                                MenuItemLinkToContentCode.LINK_OBJECT_UUID,
                                MenuItemLinkToContentCode.ATTR_CHAIN,
                                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.HIERARCHY_CLASS,
                                MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                                MenuItemLinkToContentCode.OBJECT_CLASS,
                                MenuItemLinkToContentCode.CONTENT_TEMPLATE,
                                MenuItemLinkToContentCode.HIERARCHY_STRUCTURE
                        )))
                .setRefreshDelegate(linkToContentSelectDelegateRefresh);

        register(MenuItemLinkToContentCode.LINK_OBJECT, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(linkObjectValueBindDelegate)
                .setRefreshDelegate(linkObjectValueDelegateRefresh)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.LINK_OBJECT_CASE,
                                MenuItemLinkToContentCode.LINK_OBJECT_ATTR,
                                MenuItemLinkToContentCode.LINK_OBJECT_UUID,
                                MenuItemLinkToContentCode.ATTR_CHAIN,
                                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.HIERARCHY_CLASS,
                                MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                                MenuItemLinkToContentCode.CONTENT_TEMPLATE,
                                MenuItemLinkToContentCode.OBJECT_CLASS),
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.LINK_OBJECT_CASE,
                                MenuItemLinkToContentCode.LINK_OBJECT_ATTR,
                                MenuItemLinkToContentCode.LINK_OBJECT_UUID,
                                MenuItemLinkToContentCode.ATTR_CHAIN,
                                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.HIERARCHY_CLASS,
                                MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                                MenuItemLinkToContentCode.OBJECT_CLASS
                                )))
                .setValidatorDelegates(linkObjectValueValidatorDelegates);

        register(MenuItemLinkToContentCode.LINK_OBJECT_CASE, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(linkObjectCaseDelegateRefresh)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.LINK_OBJECT_ATTR,
                                MenuItemLinkToContentCode.LINK_OBJECT_UUID,
                                MenuItemLinkToContentCode.ATTR_CHAIN,
                                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.HIERARCHY_CLASS,
                                MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                                MenuItemLinkToContentCode.CONTENT_TEMPLATE,
                                MenuItemLinkToContentCode.OBJECT_CLASS),
                        Lists.newArrayList(MenuItemLinkToContentCode.LINK_OBJECT_ATTR,
                                MenuItemLinkToContentCode.ATTR_CHAIN,
                                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.HIERARCHY_CLASS,
                                MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                                MenuItemLinkToContentCode.OBJECT_CLASS)));

        register(MenuItemLinkToContentCode.LINK_OBJECT_ATTR, linkObjectAttributeFactory)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.ATTR_CHAIN,
                                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.HIERARCHY_CLASS,
                                MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.OBJECT_CLASS,
                                MenuItemLinkToContentCode.OBJECT_CASES,
                                MenuItemLinkToContentCode.ATTRIBUTE_GROUP,
                                MenuItemLinkToContentCode.CONTENT_TEMPLATE,
                                MenuItemLinkToContentCode.LIST_TEMPLATE),
                        Lists.newArrayList(MenuItemLinkToContentCode.ATTR_CHAIN,
                                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.HIERARCHY_CLASS,
                                MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                                MenuItemLinkToContentCode.OBJECT_CLASS)))
                .setValidators(notEmptyAttrObjectValidators);

        register(MenuItemLinkToContentCode.LINK_OBJECT_UUID, textBoxPropertyFactory)
                .setRefreshDelegate(linkObjectUUIDDelegateRefresh)
                .setValidators(linkObjectUUIDValidators)
                .setValidatorDelegates(linkObjectUUIDValidatorDelegates);

        register(MenuItemLinkToContentCode.SHOW_NESTED_IN_NESTED, booleanPropertyFactory)
                .setRefreshDelegate(showNestedInNestedDelegateRefresh);

        register(MenuItemLinkToContentCode.ATTR_CHAIN, attributeChainFactory)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                            MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                            MenuItemLinkToContentCode.HIERARCHY_CLASS,
                            MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                            MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                            MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                            MenuItemLinkToContentCode.OBJECT_CLASS,
                            MenuItemLinkToContentCode.OBJECT_CASES,
                            MenuItemLinkToContentCode.ATTRIBUTE_GROUP),
                        Lists.newArrayList(
                            MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                            MenuItemLinkToContentCode.OBJECT_CLASS
                        )))
                .setValidators(notEmptyAttrObjectValidators);

        //TODO Ждёт реализации иерархических списков в API WebApi.list() NSDPRD-14981
        //   {$link https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$110106433}
        register(MenuItemLinkToContentCode.SHOW_HIERARCHY, booleanPropertyFactory)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.ATTR_CHAIN,
                                MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.HIERARCHY_CLASS,
                                MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                                MenuItemLinkToContentCode.OBJECT_CLASS,
                                MenuItemLinkToContentCode.OBJECT_CASES,
                                MenuItemLinkToContentCode.ATTRIBUTE_GROUP,
                                MenuItemLinkToContentCode.LIST_TEMPLATE),
                        null))
                .setRefreshDelegate(showHierarchyDelegateRefresh);

        register(MenuItemLinkToContentCode.BEFORE_HIERARCHY_ATTR, beforeHierarchyAttributeFactory)
                .setRefreshDelegate(relListWithHierarchyRefreshDelegate)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.HIERARCHY_CLASS,
                                MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS),
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.NESTED_ATTR_LINK,
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS)))
                .setValidators(notEmptyAttrObjectValidators);

        register(MenuItemLinkToContentCode.HIERARCHY_CLASS, labelPropertyFactory)
                .setRefreshDelegate(hierarchyClassDelegateRefresh);

        register(MenuItemLinkToContentCode.NESTED_ATTR_LINK, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(linkToContentValidatedSelectWithEmptyBindDelegate)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR,
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS),
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR)))
                .setRefreshDelegate(nestedAttrLinkDelegateRefresh)
                .setValidators(notEmptySelectValidators);

        register(MenuItemLinkToContentCode.AFTER_HIERARCHY_ATTR, afterHierarchyAttributeFactory)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.CHAIN_ATTR_CLASS,
                                MenuItemLinkToContentCode.OBJECT_CASES,
                                MenuItemLinkToContentCode.ATTRIBUTE_GROUP),
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.OBJECT_CASES,
                                MenuItemLinkToContentCode.ATTRIBUTE_GROUP)
                ))
                .setRefreshDelegate(relListWithHierarchyRefreshDelegate)
                .setValidators(notEmptyAttrObjectValidators);

        register(MenuItemLinkToContentCode.CHAIN_ATTR_CLASS, listBoxPropertyFactory)
                .setRefreshDelegate(chainAttrClassDelegateRefresh);

        register(MenuItemLinkToContentCode.OBJECT_CLASS, listBoxPropertyFactory)
                .setBindDelegate(linkToContentValidatedSelectBindDelegate)
                .setRefreshDelegate(objectClassDelegateRefresh)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.OBJECT_CASES,
                                MenuItemLinkToContentCode.ATTRIBUTE_GROUP,
                                MenuItemLinkToContentCode.SHOW_NESTED_IN_NESTED,
                                MenuItemLinkToContentCode.LIST_TEMPLATE),
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.OBJECT_CASES,
                                MenuItemLinkToContentCode.ATTRIBUTE_GROUP)
                ))
                .setValidators(notEmptySelectValidators);

        register(MenuItemLinkToContentCode.OBJECT_CASES, multiSelectPropertyFactory)
                .setRefreshDelegate(objectCasesDelegateRefresh)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(
                                MenuItemLinkToContentCode.ATTRIBUTE_GROUP,
                                MenuItemLinkToContentCode.LIST_TEMPLATE),
                        null));

        register(MenuItemLinkToContentCode.ATTRIBUTE_GROUP, listBoxPropertyFactory)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(MenuItemLinkToContentCode.LIST_TEMPLATE),null))
                .setRefreshDelegate(attributeGroupDelegateRefresh);

        register(MenuItemLinkToContentCode.LIST_PRESENTATION, listBoxPropertyFactory)
                .setBindDelegate(listPresentationBindDelegate)
                .setRefreshDelegate(linkToContentListPresentationRefreshDelegate);

        register(MenuItemLinkToContentCode.LIST_TEMPLATE, treeSelectPropertyFactory);

        register(MenuItemLinkToContentCode.CONTENT_TEMPLATE, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(linkToContentContentTemplateRefreshDelegate);

        register(MenuItemLinkToContentCode.LIST_PAGE_TITLE, textBoxPropertyFactory)
                .setBindDelegate(titleBindDelegate)
                .setRefreshDelegate(linkToContentTextBoxDelegateRefresh)
                .setValidators(notEmptyValidators);

        register(MenuItemLinkToContentCode.HIERARCHY_STRUCTURE, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(linkToContentHierarchyStructureBindDelegate)
                .setRefreshDelegate(linkToContentHierarchyStructureRefreshDelegate)
                .setValidators(notEmptySelectValidators)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Lists.newArrayList(MenuItemLinkToContentCode.CONTENT_TEMPLATE), null));

        register(MenuItemPropertyCode.CUSTOM_LINK_VALUE, textBoxPropertyFactory)
                .setBindDelegate(linkBindDelegate)
                .setRefreshDelegate(customLinkValueRefreshDelegate)
                .setValidators(customLinkValidators);

        register(MenuItemPropertyCode.CUSTOM_SYSTEM_LINK_VALUE, checkBoxPropertyFactory)
                .setVchDelegate(customSystemLinkVCH)
                .setRefreshDelegate(customSystemLinkValueRefreshDelegate);

        register(MenuItemPropertyCode.NEW_TAB_VALUE, checkBoxPropertyFactory)
                .setRefreshDelegate(newTabValueRefreshDelegate);
        register(ToolFormPropertyCodes.ACTION, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(actionRefreshDelegate);
        register(ToolFormPropertyCodes.USE_QUICK_ADD_FORM, checkBoxPropertyFactory)
                .setVchDelegate(useQuickAddFormVCHDelegate)
                .setRefreshDelegate(useQuickAddFormRefreshDelegate);
        register(ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION, checkBoxPropertyFactory)
                .setRefreshDelegate(goToCardAfterCreationRefreshDelegate);
        register(ToolFormPropertyCodes.QUICK_ADD_FORM, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(quickAddFormBindDelegate)
                .setRefreshDelegate(quickAddFormRefreshDelegate)
                .setVchDelegate(quickAddFormVCHDelegete);
        register(ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(attributeForFillByCurrentObjectBindDelegate)
                .setRefreshDelegate(attributeForFillByCurrentObjectRefreshDelegate);

        register(MenuSettingsPropertyCode.SETTINGS_SET, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(settingsSetBindDelegate)
                .setRefreshDelegate(settingsSetRefreshDelegate);

        //  TODO Пока скрываем с формы, так как ждем починки дефекта NSDPRD-14869 на кнопку "Добавить связь"
        //   {$link https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$107823141}
        //
        //        register(MenuItemLinkToContentCode.ADD_LINK_FORM_TITLE, textBoxPropertyFactory)
        //                .setRefreshDelegate(addLinkFormTitleDelegateRefresh);
        //@formatter:on
    }
}
