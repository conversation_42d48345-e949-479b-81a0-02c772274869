package ru.naumen.metainfoadmin.client.embeddedapplications.form;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;

/**
 * Интерфейс для описания дополнительных свойств форм,
 * зависящих от типа встроенного приложения
 *
 * <AUTHOR>
 * @since 07.11.16
 */
public interface AdditionalApplicationPropertiesProvider
{
    void addAdditionalProperties(PropertyDialogDisplay display, PropertyRegistration<?> previousProperty,
            RegistrationContainer registrationContainer);

    EmbeddedApplication createApplication();

    void fillAdditionalProperties(EmbeddedApplicationAdminSettingsDto embeddedApplication);

    Boolean getValueOfAdditionalProperties(EmbeddedApplicationAdminSettingsDto embeddedApplication);

    void removeAdditionalProperties();
}
