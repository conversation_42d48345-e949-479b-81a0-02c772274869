package ru.naumen.metainfoadmin.client.attributes.forms.typeEmul;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.shared.Constants.CaseListAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.inject.Singleton;

/**
 *
 * <AUTHOR>
 *
 */
@Singleton
public class AttributeTypeBuilderCaseListImpl implements AttributeTypeBuilder
{
    @Override
    public void build(AttributeType type, IProperties propertyValues)
    {
        type.setProperty(CaseListAttributeType.METACLASS_ID,
                propertyValues.getProperty(AttributeFormPropertyCode.TARGET_CLASS));
    }

    @Override
    public void invert(AttributeType type, IProperties propertyValues)
    {
        propertyValues.setProperty(AttributeFormPropertyCode.TARGET_CLASS,
                type.getProperty(CaseListAttributeType.METACLASS_ID));
    }
}