package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.safehtml.shared.SafeHtml;

/**
 * <AUTHOR>
 * @since Mar 29, 2013
 */
@DefaultLocale("ru")
public interface NavigationSettingsMessages extends Messages
{
    @Description("Кнопка добавления объектов")
    String addButtonMenuType();

    @Description("Добавить плитку")
    String addTile();

    @Description("Вместе с родительским элементом будут удалены все вложенные в него элементы\n{0}\nПродолжить "
                 + "удаление?")
    String additionalDeleteMenuItemInfo(CharSequence children);

    @Description("Шаблон '%s' не будет удалён. Удалить шаблон можно на странице \"Настройка системы -> Шаблоны -> "
                 + "Шаблоны списков\".")
    String additionalDeleteWithListTemplate(String templateTitle);

    SafeHtml adminAreaDescription();

    String attrForTitle(String title);

    @Description("Навигационная цепочка (\"Хлебные крошки\")")
    String breadCrumb();

    String cardTemplate();

    @Description("Раздел меню")
    String chapterMenuType();

    @Description("Содержание")
    String content();

    @Description("Содержимое (карточка)")
    String contentCard();

    @Description("Атрибут для названия")
    String contentAttrTitle();

    @Description("Название из атрибута объекта")
    String contentUseAttrTitle();

    @Description("Ссылка на карточку")
    String contentLinkToCard();

    String contentMenuItemObject(@Optional @Select String code);

    @Description("Атрибут связи")
    String contentLinkAttribute();

    @Description("Класс объекта")
    String contentObjectClass();

    @Description("Тип объекта")
    String contentObjectType();

    @Description("Содержимое (контент)")
    String contentContent();

    @Description("Тип контента")
    String contentType();

    @Description("Создание объектов через общую кнопку \"Добавить\"")
    String createBlockTitle();

    @Description("Текущий пользователь")
    String currentUser();

    @Description("Тип Сотрудника")
    String currentUserType();

    @Description("Редактирование общей кнопки \"Добавить\"")
    String editCreateBlockTitle();

    @Description("Избранное")
    String favoritesMenuType();

    SafeHtml hasElementWithTypeAlready();

    @Description("История")
    String historyMenuType();

    @Description("Домашняя страница")
    String homePage();

    @Description("элемента домашней страницы")
    String homePageElementBy();

    @Description("Домашняя страница")
    String homePageCardName(String name);

    @Description("Вложен в раздел")
    String insertedInChapter();

    @Description("Вид элемента")
    String itemType();

    @Description("Левое меню")
    String leftMenu();

    @Description("Объект связи")
    String linkObject();

    @Description("Атрибут, ссылающийся на объект")
    String linkObjectAttribute();

    @Description("UUID объекта")
    String linkObjectUUID();

    @Description("Шаблон списка ссылки на контент")
    String linkTemplateForContentOfDate(String date);

    @Description("Ссылка на контент")
    String linkToContentMenuType();

    @Description("Заголовок страницы с контентом")
    String listPageTitle();

    @Description("элемент меню")
    String menuElement();

    @Description("плитку меню быстрого доступа")
    String quickTileAccus();

    @Description("Атрибуты элемента")
    String menuElementAttributes();

    @Description("элемента верхнего меню")
    String menuTopElementBy();

    @Description("элемента левого меню")
    String menuLeftElementBy();

    @Description("элемента меню")
    String menuElementBy();

    @Description("Объект, связанный с текущим пользователем")
    String objLinkedToCurrentUser();

    @Description("плитки на панель быстрого доступа в левом меню")
    String quickAccessTileBy();

    @Description("Элемент меню '%s'")
    String menuElementWithTitle(String title);

    @Description("Ссылка на карточку")
    String referenceMenuType();

    @Description("Результирующие профили")
    String resultProfiles();

    @Description("Показывать хлебные крошки")
    String showBreadCrumb();

    @Description("Показывать кнопку Сделать домашней страницей")
    String showHomePage();

    @Description("Показывать левое меню")
    String showLeftMenu();

    @Description("Показывать верхнее меню")
    String showTopMenu();

    @Description("Плитка")
    String tile();

    @Description("К списку домашних страниц")
    String toHomePageElements();

    @Description("Верхнее меню")
    String topMenu();

    @Description("к списку элементов верхнего навигационного меню")
    String toTopMenuElements();

    SafeHtml userAreaDescription();

    @Description("Редактирование видимости")
    String visibilityEditing();

    @Description("Пользовательская область в панели быстрого доступа")
    String userArea();

    @Description("Неизменяемая область в панели быстрого доступа")
    String adminArea();

    @Description("Служебная область в панели быстрого доступа")
    String systemArea();

    @Description("Показывать пользовательскую область панели быстрого доступа")
    String showUserArea();

    @Description("Показывать неизменяемую область панели быстрого доступа")
    String showAdminArea();

    @Description("Показывать служебную область панели быстрого доступа")
    String showSystemArea();

    @Description("Форматирование")
    String formatting();

    @Description("Профили")
    String profiles();

    @Description("Компания")
    String companyMenuType();

    @Description("Раздел")
    String section();

    @Description("Введенный код уже используется в навигационном меню. Введите уникальный код.")
    String mobileMenuItemCodeIsFound();

    @Description("Секция")
    String module();

    @Description("Элемент левого меню")
    String menuItem();

    @Description("Область панели")
    String area();

    @Description("Текст подсказки")
    String hint();

    @Description("Панель быстрого доступа")
    String quickAccessPanel();

    @Description("Доступен всем")
    String allProfiles();

    @Description("Доступен ограниченному списку профилей")
    String listOfProfiles();

    @Description("Не доступен никому")
    String noProfiles();

    SafeHtml mustSelectOneArea();

    @Description("Корневой элемент левого меню")
    String leftMenuRoot();

    @Description("Подсказка поля выбора профилей при отсутствии ограничений от родителя")
    String allProfilesDescription();

    @Description("Подсказка поля выбора профилей при ограничениях от родителя")
    String listOfProfilesDescription();

    @Description("Хинт для блока, видимость которого отключена")
    String visibilityHidden();

    @Description("Значение свойства, которое не определено для данного типа элемента ЛМ")
    String notDefined();

    @Description("Произвольная ссылка")
    String customLinkMenuType();

    @Description("Произвольная ссылка")
    String customButtonMenuType();
}
