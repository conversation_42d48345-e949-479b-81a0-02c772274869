package ru.naumen.metainfoadmin.client.eventaction.form;

import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.eventaction.EventType;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import java.util.Set;

import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.AttrTree.CLASS_TITLE;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.AttrTree.TYPE_TITLE;

/**
 * Утилитарные методы для форм создания/редактирования ДПС
 *
 * <AUTHOR>
 * @since 04.04.2022
 */
public class EventActionFormUtils
{
    private static final Set<EventType> EVENT_TYPES_WITH_DESCRIPTION_AS_ADD = ImmutableSet.of(EventType.add,
            EventType.addComment, EventType.addFile);

    private static final Set<EventType> EVENT_TYPES_WITH_ATTRIBUTES = ImmutableSet.of(EventType.add, EventType.edit,
            EventType.addComment, EventType.editComment, EventType.addFile, EventType.changeMassAttr,
            EventType.bindToMassMaster, EventType.bindMassSlave, EventType.changeResponsible,
            EventType.changeState);

    private static final Set<EventType> EVENT_TYPES_WITHOUT_CONTEXT_ATTRIBUTES = ImmutableSet.of(
            EventType.insertMention, EventType.editComment);

    private static final Set<EventType> EVENT_TYPES_WITH_COMMENT_ATTRIBUTES = ImmutableSet.of(EventType.addComment,
            EventType.editComment);

    private final EventActionMessages messages;
    private final CommonMessages cmessages;

    @Inject
    public EventActionFormUtils(EventActionMessages messages, CommonMessages cmessages)
    {
        this.messages = messages;
        this.cmessages = cmessages;
    }

    /**
     * Название свойства "Выполнять действие при изменении атрибутов"/"Выполнять действие при изменении атрибутов
     * комментария" в зависимости от события
     *
     * @param eventType тип события
     * @return название свойства
     */
    public String getEvenAttrsCaption(@Nullable EventType eventType)
    {
        return EVENT_TYPES_WITH_COMMENT_ATTRIBUTES.contains(eventType) ?
                messages.performActionWhenChangingCommentAttributes() :
                messages.performActionWhenChangingAttributes();
    }

    /**
     * Описание свойства "Выполнять действие при изменении атрибутов"/"Выполнять действие при изменении атрибутов
     * комментария" в зависимости от события
     *
     * @param eventType тип события
     * @return описание свойства
     */
    public String getEventAttrsDescription(@Nullable EventType eventType)
    {
        return EVENT_TYPES_WITH_DESCRIPTION_AS_ADD.contains(eventType) ?
                messages.performActionWhenChangingAttributesOnAddDescription() :
                messages.performActionWhenChangingAttributesOnEditDescription();
    }

    /**
     * Проверяет наличие свойства "Выполнять действие при изменении атрибутов"/"Выполнять действие при изменении
     * атрибутов
     * комментария" для типа события
     *
     * @param eventType тип события
     * @return true, если для данного типа события свойство есть, иначе false
     */
    public static boolean isEventActionWithAttributes(@Nullable EventType eventType)
    {
        return EVENT_TYPES_WITH_ATTRIBUTES.contains(eventType);
    }

    /**
     * Проверяет наличие свойства "Атрибуты, передаваемые в контекст" для типа события
     *
     * @param eventType тип события
     * @param isSync является ли действие синхронным
     * @return true, если для данного типа события свойство есть, иначе false
     */
    public static boolean isEventActionWithContextAttributes(@Nullable EventType eventType, boolean isSync)
    {
        return eventType != null && !EVENT_TYPES_WITHOUT_CONTEXT_ATTRIBUTES.contains(eventType) && !isSync;
    }

    /**
     * Формирование полного названия атрибута с указанием класса и типа атрибута на карточке ДПС
     *
     * @param dto dto атрибута
     * @return полное название атрибута
     */
    public String getAttrTitleWithClass(DtObject dto)
    {
        String classTitle = (String)dto.get(CLASS_TITLE);
        String typeTitle = (String)dto.get(TYPE_TITLE);

        StringBuilder builder = new StringBuilder()
                .append(dto.getTitle())
                .append(" (").append(cmessages.clazz()).append(": ").append(classTitle);
        if (!Strings.isNullOrEmpty(typeTitle))
        {
            builder.append(", ").append(cmessages.type()).append(": ").append(typeTitle);
        }
        builder.append(')');
        return SafeHtmlUtils.htmlEscape(builder.toString());
    }
}
