package ru.naumen.metainfoadmin.client.fastlink.settings.forms;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsGinModule.FastLinkSettingFormPropertyCode;

/**
 * Делегат при изменении типов для упоминания
 *
 * <AUTHOR>
 * @since 01.03.18
 */
public class MentionTypesVCHDelegateImpl implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getPropertyControllers().get(FastLinkSettingFormPropertyCode.ATTRIBUTE_GROUP).refresh();
        context.getPropertyControllers().get(FastLinkSettingFormPropertyCode.MENTION_ATTRIBUTE).refresh();
        PropertyController profilesController = context.getPropertyControllers()
                .get(FastLinkSettingFormPropertyCode.PROFILES);
        if (profilesController != null)
        {
            profilesController.refresh();
        }
    }
}