package ru.naumen.metainfoadmin.client.customforms.parameters;

import static ru.naumen.core.client.inject.Gin.typeLiteral;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import java.util.Collection;

import jakarta.inject.Singleton;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.LabelProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.StructuredObjectsViewForBuildingTreeRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.widgets.properties.ScriptComponentEditProperty;
import ru.naumen.core.client.widgets.properties.ValueCellListProperty;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.elements.CommonRestriction;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.props.DefaultValuePropertyControllerFactory;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.BooleanComputableDependentRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.ComplexRelationRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.ComplexRelationStructuredObjectsViewRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.RefreshDefaultValueVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.SuggestCatalogRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attrType.AttrTypeBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attrType.AttrTypeVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.bolinks.FilterByScriptRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.bolinks.FilterByScriptVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.bolinks.ScriptForFiltrationRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation.ComplexRelationAggrAttrGroupRefreshDelegateFactory;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation.ComplexRelationAttrGroupRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.complexrelation.ComplexRelationVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.componform.ComputableOnFormScriptRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.componform.ComputableOnFormVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.computable.ComputableVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.AttributeDateTimeRestrictionRefreshDelegate;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.AvailableRestrictionTypesProvider;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeCommonRestrictionRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeCommonRestrictionsBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionConditionBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionConditionRefreshDelegate;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionScriptPropertyBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionScriptRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionTypeBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionTypeRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.DateTimeRestrictionTypeVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.FormAttributeDateTimeRestrictionBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.FormAvailableRestrictionTypesProvider;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue.DefaultByScriptRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue.DefaultByScriptVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue.DefaultValueLabelRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.defaultValue.ScriptForDefaultRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determine.DeterminableVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.determine.DeterminerBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.editoncomplexformonly.EditOnComplexFormOnlyRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule.GenerationRuleVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.generatedRule.UseGenerationRuleVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hasgroupseparators.HasGroupSeparatorRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hasgroupseparators.HasGroupSeparatorVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hiddenwhennovalues.HiddenWhenNoPossibleValuesRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hidearchived.HideArchivedRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hidecaption.HideAttrCaptionBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.hidecaption.HideAttrCaptionRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskBindDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskModeBindDeletageImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskModeRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskModeVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.inputmask.InputmaskVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes.PermittedTypesDelegatesGinModule.PermittedTypeRefreshDelegateCode;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes.PermittedTypesRefreshDelegate;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes.PermittedTypesRefreshDelegateBackLinkImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes.PermittedTypesRefreshDelegateSelectorImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes.PermittedTypesVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.edit.EditPresentationVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.show.ShowPresentationVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.quickform.QuickAddFormRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.quickform.QuickEditFormRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.required.RequiredRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.required.RequiredVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.sort.SelectSortRefreshDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.clas.TargetClassVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.directLink.DirectLinkRefreshDelegateAddImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.directLink.DirectLinkVCHDelegateImpl;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.timer.TargetTimerRefreshDelegateAddImpl;

/**
 *
 * <AUTHOR>
 * @since 23 мая 2016 г.
 */
public class PropertyDelegatesGinModule<F extends ParameterForm> extends AbstractGinModule
{
    private final Class<F> form;

    public PropertyDelegatesGinModule(Class<F> formClass)
    {
        this.form = formClass;
    }

    @Override
    protected void configure()
    {
        bindFormDelegates();
        bindParameterForm();

        //@formatter:off
        install(new GinFactoryModuleBuilder()
                .implement(PropertyController.class, FormParameterDefaultValuePropertyController.class)
                .build(Gin.parameterizedTypeLiteral(DefaultValuePropertyControllerFactory.class, form)));
        //@formatter:on
    }

    private void bindFormDelegates()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
                .implement(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxWithEmptyOptProperty.class),
                        typeLiteral(ComplexRelationAggrAttrGroupRefreshDelegateParameterImpl.class, form))
                .build(typeLiteral(ComplexRelationAggrAttrGroupRefreshDelegateFactory.class, form)));
        
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(EDITABLE))
            .to(typeLiteral(FormParameterEditableVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(ATTR_TYPE))
            .to(typeLiteral(AttrTypeVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(TARGET_CLASS))
            .to(typeLiteral(TargetClassVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(TARGET_CATALOG))
            .to(typeLiteral(RefreshDefaultValueVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(AGGREGATE_CLASSES))
            .to(typeLiteral(ParameterFormAggregateClassesVCHDelegate.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(SHOW_PRS))
            .to(typeLiteral(ShowPresentationVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(EDIT_PRS))
            .to(typeLiteral(EditPresentationVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(SUGGEST_CATALOG))
            .to(typeLiteral(RefreshDefaultValueVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(SELECT_SORTING))
            .to(typeLiteral(RefreshDefaultValueVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(DIRECT_LINK_TARGET))
            .to(typeLiteral(DirectLinkVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(COMPUTABLE))
            .to(typeLiteral(ComputableVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(USE_GEN_RULE))
            .to(typeLiteral(UseGenerationRuleVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(GEN_RULE))
            .to(typeLiteral(GenerationRuleVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(DETERMINABLE))
            .to(typeLiteral(DeterminableVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(FILTERED_BY_SCRIPT))
            .to(typeLiteral(FilterByScriptVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(DEFAULT_BY_SCRIPT))
            .to(typeLiteral(DefaultByScriptVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(PERMITTED_TYPES))
            .to(typeLiteral(PermittedTypesVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(REQUIRED))
            .to(typeLiteral(RequiredVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(InputmaskVCHDelegateImpl.class, form)).in(Singleton.class);
        bind(typeLiteral(InputmaskModeVCHDelegateImpl.class, form)).in(Singleton.class);

        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(ATTR_TYPE))
            .to(typeLiteral(AttrTypeBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form, 
                new TypeLiteral<Collection<MetaClassLite>>(){}.getType(), 
                new TypeLiteral<ValueCellListProperty<MetaClassLite>>(){}.getType()))
            .annotatedWith(Names.named(AGGREGATE_CLASSES))
            .to(typeLiteral(FormParameterAggregateClassesBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form, String.class, ListBoxWithEmptyOptProperty.class))
            .annotatedWith(Names.named(DETERMINER))
            .to(typeLiteral(DeterminerBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form, ScriptDto.class, ScriptComponentEditProperty.class))
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM_SCRIPT))
            .to(typeLiteral(FormParameterScriptPropertyBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form, ScriptDto.class, ScriptComponentEditProperty.class))
            .annotatedWith(Names.named(SCRIPT_FOR_DEFAULT))
            .to(typeLiteral(FormParameterScriptPropertyBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form, ScriptDto.class, ScriptComponentEditProperty.class))
            .annotatedWith(Names.named(SCRIPT_FOR_FILTRATION))
            .to(typeLiteral(FormParameterScriptPropertyBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form, ScriptDto.class, ScriptComponentEditProperty.class))
            .annotatedWith(Names.named(SCRIPT))
            .to(typeLiteral(FormParameterScriptPropertyBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form, ScriptDto.class, ScriptComponentEditProperty.class))
            .annotatedWith(Names.named(COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT))
            .to(typeLiteral(FormParameterScriptPropertyBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, ScriptDto.class, ScriptComponentEditProperty.class))
                .annotatedWith(Names.named(COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT))
                .to(typeLiteral(ComputeAnyCatalogElementsScriptRefreshDelegateImpl.class, form))
                .in(Singleton.class);
        bind(typeLiteral(InputmaskModeBindDeletageImpl.class, form)).in(Singleton.class);
        bind(typeLiteral(InputmaskBindDelegateImpl.class, form)).in(Singleton.class);

        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(TARGET_CATALOG))
            .to(typeLiteral(FormParamTargetCatalogRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(TARGET_CLASS))
            .to(typeLiteral(TargetClassRefreshDelegateFormParameterImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(TARGET_TIMER))
            .to(typeLiteral(TargetTimerRefreshDelegateAddImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(SHOW_PRS))
            .to(typeLiteral(FormParameterShowPrsRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(EDIT_PRS))
            .to(typeLiteral(FormParameterEditPrsRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(SUGGEST_CATALOG))
            .to(typeLiteral(SuggestCatalogRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(SELECT_SORTING))
            .to(typeLiteral(SelectSortRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(DIRECT_LINK_TARGET))
            .to(typeLiteral(DirectLinkRefreshDelegateAddImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, Boolean.class, BooleanCheckBoxProperty.class))
            .annotatedWith(Names.named(FILTERED_BY_SCRIPT))
            .to(typeLiteral(FilterByScriptRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, Boolean.class, BooleanCheckBoxProperty.class))
            .annotatedWith(Names.named(DEFAULT_BY_SCRIPT))
            .to(typeLiteral(DefaultByScriptRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, ScriptDto.class, ScriptComponentEditProperty.class))
            .annotatedWith(Names.named(SCRIPT_FOR_FILTRATION))
            .to(typeLiteral(ScriptForFiltrationRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, ScriptDto.class, ScriptComponentEditProperty.class))
            .annotatedWith(Names.named(SCRIPT_FOR_DEFAULT))
            .to(typeLiteral(ScriptForDefaultRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, Boolean.class, BooleanCheckBoxProperty.class))
            .annotatedWith(Names.named(EDITABLE))
            .to(typeLiteral(BooleanComputableDependentRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form, Boolean.class, BooleanCheckBoxProperty.class))
            .annotatedWith(Names.named(HIDDEN_ATTR_CAPTION))
            .to(typeLiteral(HideAttrCaptionBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, Boolean.class, BooleanCheckBoxProperty.class))
            .annotatedWith(Names.named(HIDDEN_ATTR_CAPTION))
            .to(typeLiteral(HideAttrCaptionRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, Boolean.class, BooleanCheckBoxProperty.class))
            .annotatedWith(Names.named(REQUIRED))
            .to(typeLiteral(RequiredRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, String.class, LabelProperty.class))
            .annotatedWith(Names.named(DEFAULT_VALUE_LABEL))
            .to(typeLiteral(DefaultValueLabelRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, Boolean.class, BooleanCheckBoxProperty.class))
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM))
            .to(typeLiteral(ComputableOnFormParameterRefreshDelegate.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM))
            .to(typeLiteral(ComputableOnFormVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, ScriptDto.class, ScriptComponentEditProperty.class))
            .annotatedWith(Names.named(COMPUTABLE_ON_FORM_SCRIPT))
            .to(typeLiteral(ComputableOnFormScriptRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, Boolean.class, BooleanCheckBoxProperty.class))
            .annotatedWith(Names.named(HIDDEN_WHEN_NO_POSSIBLE_VALUES))
            .to(typeLiteral(HiddenWhenNoPossibleValuesRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, Boolean.class, BooleanCheckBoxProperty.class))
                .annotatedWith(Names.named(EDIT_ON_COMPLEX_FORM_ONLY))
                .to(typeLiteral(EditOnComplexFormOnlyRefreshDelegateImpl.class, form))
                .in(Singleton.class);
        bind(typeLiteral(InputmaskRefreshDelegateImpl.class, form)).in(Singleton.class);
        bind(typeLiteral(InputmaskModeRefreshDelegateImpl.class, form)).in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form,
                new TypeLiteral<Collection<CommonRestriction>>(){}.getType(), 
                new TypeLiteral<ValueCellListProperty<CommonRestriction>>(){}.getType()))
            .annotatedWith(Names.named(DATE_TIME_COMMON_RESTRICTIONS))
            .to(typeLiteral(DateTimeCommonRestrictionsBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, 
                new TypeLiteral<Collection<CommonRestriction>>(){}.getType(),
                new TypeLiteral<ValueCellListProperty<CommonRestriction>>(){}.getType()))
            .annotatedWith(Names.named(DATE_TIME_COMMON_RESTRICTIONS))
            .to(typeLiteral(DateTimeCommonRestrictionRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_TYPE))
            .to(typeLiteral(DateTimeRestrictionTypeRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_TYPE))
            .to(typeLiteral(DateTimeRestrictionTypeVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form,
                new TypeLiteral<SelectItem>(){}.getType(), 
                new TypeLiteral<ListBoxProperty>(){}.getType()))
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_TYPE))
            .to(typeLiteral(DateTimeRestrictionTypeBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form, ScriptDto.class, ScriptComponentEditProperty.class))
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_SCRIPT))
            .to(typeLiteral(DateTimeRestrictionScriptPropertyBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, ScriptDto.class, ScriptComponentEditProperty.class))
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_SCRIPT))
            .to(typeLiteral(DateTimeRestrictionScriptRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form,
                new TypeLiteral<SelectItem>(){}.getType(), 
                new TypeLiteral<ListBoxProperty>(){}.getType()))
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_CONDITION))
            .to(typeLiteral(DateTimeRestrictionConditionBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, 
                new TypeLiteral<SelectItem>(){}.getType(),
                new TypeLiteral<ListBoxProperty>(){}.getType()))
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_CONDITION))
            .to(typeLiteral(DateTimeRestrictionConditionRefreshDelegate.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateBind.class, form,
                new TypeLiteral<SelectItem>(){}.getType(), 
                new TypeLiteral<ListBoxProperty>(){}.getType()))
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_ATTRIBUTE))
            .to(typeLiteral(FormAttributeDateTimeRestrictionBindDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, 
                new TypeLiteral<SelectItem>(){}.getType(),
                new TypeLiteral<ListBoxProperty>(){}.getType()))
            .annotatedWith(Names.named(DATE_TIME_RESTRICTION_ATTRIBUTE))
            .to(typeLiteral(AttributeDateTimeRestrictionRefreshDelegate.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AvailableRestrictionTypesProvider.class, form))
            .to(typeLiteral(FormAvailableRestrictionTypesProvider.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(COMPLEX_RELATION))
            .to(typeLiteral(ComplexRelationVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(COMPLEX_RELATION))
            .to(typeLiteral(ComplexRelationRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(COMPLEX_RELATION_ATTR_GROUP))
            .to(typeLiteral(ComplexRelationAttrGroupRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW))
            .to(typeLiteral(ComplexRelationStructuredObjectsViewRefreshDelegateImpl.class, form))
            .in(Singleton.class);

        
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(QUICK_ADD_FORM_CODE))
            .to(typeLiteral(QuickAddFormRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(QUICK_EDIT_FORM_CODE))
            .to(typeLiteral(QuickEditFormRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        
        bind(typeLiteral(AttributeFormPropertyDelegateVCH.class, form))
            .annotatedWith(Names.named(HAS_GROUP_SEPARATORS))
            .to(typeLiteral(HasGroupSeparatorVCHDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, Boolean.class, BooleanCheckBoxProperty.class))
            .annotatedWith(Names.named(HAS_GROUP_SEPARATORS))
            .to(typeLiteral(HasGroupSeparatorRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, Boolean.class, BooleanCheckBoxProperty.class))
                .annotatedWith(Names.named(HIDE_ARCHIVED))
                .to(typeLiteral(HideArchivedRefreshDelegateImpl.class, form))
                .in(Singleton.class);
        bind(typeLiteral(AttributeFormPropertyDelegateRefresh.class, form, SelectItem.class, ListBoxProperty.class))
            .annotatedWith(Names.named(STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE))
            .to(typeLiteral(StructuredObjectsViewForBuildingTreeRefreshDelegateImpl.class, form))
            .in(Singleton.class);
        //@formatter:on
    }

    private void bindParameterForm()
    {
        //@formatter:off
        bind(typeLiteral(PermittedTypesRefreshDelegate.class, form))
            .to(typeLiteral(PermittedTypesRefreshDelegateSelectorImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(PermittedTypesRefreshDelegate.class, form))
            .annotatedWith(Names.named(PermittedTypeRefreshDelegateCode.BACK_LINK))
            .to(typeLiteral(PermittedTypesRefreshDelegateBackLinkImpl.class, form))
            .in(Singleton.class);
        bind(typeLiteral(PermittedTypesRefreshDelegate.class, form))
            .annotatedWith(Names.named(PermittedTypeRefreshDelegateCode.DIRECT_LINK))
            .to(typeLiteral(PermittedTypesRefreshDelegateParameterFormImpl.class, form))
            .in(Singleton.class);
        //@formatter:on
    }
}
