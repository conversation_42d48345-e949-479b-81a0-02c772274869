package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import java.util.List;
import java.util.Set;
import java.util.logging.Logger;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.inject.name.Named;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.AbstractContentPresenter;
import ru.naumen.core.client.content.ContentGinModule;
import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.WindowContentDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.metainfoadmin.client.MGinjector;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeObjectCardCaptionEvent;
import ru.naumen.metainfoadmin.client.dynadmin.ChangeObjectCardCaptionEventEventHandler;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * {@link Presenter} настройки {@link Window карточки объекта}
 * <p>
 *
 * <AUTHOR>
 *
 */
public class WindowContentPresenter extends AbstractContentPresenter<WindowContentDisplay, Window, UIContext>
{
    private static Logger LOG = Logger.getLogger("WindowContentPresenter");

    @Inject
    MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    AdminContentFactory factory;
    @Inject
    CommonMessages barMessages;
    @Inject
    MetainfoUtils metainfoUtils;
    @Inject
    MGinjector injector;
    @Inject
    @Named(ContentGinModule.CONTENTS_IN_EDIT_MODE)
    private Set<String> editModeContents;
    TabBarContentPresenter tabBarPresenter;

    ContentPresenter<ToolPanel, UIContext> toolPanelPresenter;

    @Inject
    WindowContentPresenter(WindowContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "Window");
    }

    @Override
    public void onHide()
    {
        LOG.finest("Hide");
        tabBarPresenter.hideDisplay();

    }

    @Override
    public void onReveal()
    {
        LOG.finest("Reveal");
        toolPanelPresenter.refreshDisplay();
        tabBarPresenter.revealDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        if (toolPanelPresenter != null)
        {
            toolPanelPresenter.refreshDisplay();
        }
    }

    @Override
    protected void onBind()
    {
        editModeContents.clear();
        TabBar tabBar = getContent().getTabBar();
        if (null == tabBar)
        {
            return;
        }
        tabBar.setHasHead(true);
        tabBarPresenter = factory.build(tabBar, getContext());
        tabBarPresenter.getDisplay().setCaptionVisible(false);
        tabBarPresenter.getDisplay().setHeaderVisible(true);
        getDisplay().setContent(tabBarPresenter.getDisplay());
        toolPanelPresenter = factory.build(getContent().getToolPanel(), getContext());
        tabBarPresenter.bindActionBar(toolPanelPresenter);
        tabBarPresenter.getDisplay().setCaptionText(getObjCardCaptionText());

        addContextHandler(ChangeObjectCardCaptionEvent.getType(), new ChangeObjectCardCaptionEventEventHandler()
        {
            @Override
            public void onChangeObjectCardCaption(ChangeObjectCardCaptionEvent event)
            {
                tabBarPresenter.getDisplay()
                        .setCaptionText(getCaptionTitle(event.getCaptionAttributeCode(), event.getCaptionString()));
            }
        });
        addContextHandler(RefreshContentEvent.getType(), this);
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        if (null != tabBarPresenter)
        {
            tabBarPresenter.unbind();
        }
        if (null != toolPanelPresenter)
        {
            toolPanelPresenter.unbind();
        }
    }

    private String getCaptionTitle(String attrCode, List<? extends LocalizedString> captionString)
    {
        MetaClass metainfo = getContext().getMetainfo();
        switch (attrCode)
        {
            case Constants.WITHOUT_CAPTION_CODE:
                return "<i>" + barMessages.brackets(barMessages.noCaption()) + "</i>";
            case Constants.TYPE_NAME_AND_TITLE_CODE:
                return metainfo.getTitle() + " " + barMessages.quotes(metainfo.getAttribute("title").getTitle());
            case Constants.CARD_CAPTION_CODE:
                return barMessages.cardCaption();
            case Constants.STRING_CAPTION_CODE:
                return SafeHtmlUtils.htmlEscape(metainfoUtils.getLocalizedValue(captionString));
            default:
                Attribute attribute = metainfo.getAttribute(attrCode);
                return attribute.getTitle();
        }
    }

    private String getObjCardCaptionText()
    {
        String captionAttributeCode = getContent().getObjectCardCaptionAttributeCode();
        if (captionAttributeCode == null)
        {
            captionAttributeCode = initObjCardCaptionCode();
        }
        return getCaptionTitle(captionAttributeCode, getContent().getObjectCardCaptionString());
    }

    private String initObjCardCaptionCode()
    {
        String code = getContext().getMetainfo().getFqn().equals(Root.FQN) ? "title"
                : Constants.TYPE_NAME_AND_TITLE_CODE;
        getContent().setObjectCardCaptionAttributeCode(code);
        return code;
    }

}
