package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.defaultsettings;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.forms.DialogDisplay.DialogWidth;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.forms.FormPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.advlist.AdvlistConstants;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.SaveUIAction;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.HierarchyGridDefaultViewSettings;
import ru.naumen.metainfo.shared.ui.HierarchyGridDefaultViewSettings.HeaderAppearance;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.AdvlistDefaultPrsMessages;
import ru.naumen.metainfoadmin.shared.dynadmin.HierarchyItemSettingsContext;

/**
 * Представление формы настройки вида по умолчанию для иерархического списка.
 * <AUTHOR>
 * @since 11.01.2021
 */
public class EditHierarchyDefaultSettingsFormPresenter extends FormPresenter
{
    private final HierarchyDefaultSettingsItemsPresenterFactory itemsPresenterFactory;
    private final AdvlistDefaultPrsMessages messages;
    private final MetainfoUtils metainfoUtils;
    private final DispatchAsync dispatch;

    private final UIContext context;
    private final HierarchyGrid content;
    private HierarchyDefaultSettingsItemsPresenter itemsPresenter;

    @Inject
    public EditHierarchyDefaultSettingsFormPresenter(@Assisted UIContext context, @Assisted HierarchyGrid content,
            FormDisplay display, EventBus eventBus,
            HierarchyDefaultSettingsItemsPresenterFactory itemsPresenterFactory, AdvlistDefaultPrsMessages messages,
            MetainfoUtils metainfoUtils, DispatchAsync dispatch)
    {
        super(display, eventBus);
        this.context = context;
        this.content = content;
        this.itemsPresenterFactory = itemsPresenterFactory;
        this.messages = messages;
        this.metainfoUtils = metainfoUtils;
        this.dispatch = dispatch;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        itemsPresenter = itemsPresenterFactory.create(context, content);
        registerChildPresenter(itemsPresenter);
        itemsPresenter.bind();
        getDisplay().setCaptionText(messages.formCaption(metainfoUtils.getLocalizedValue(content.getCaption())));
        getDisplay().addAttentionMessage(messages.formMessage());
        getDisplay().addContent(itemsPresenter.getDisplay());
        getDisplay().setDialogWidth(DialogWidth.W660);
        getDisplay().display();
    }

    @Override
    public void onApply()
    {
        content.getDefaultSettings().clear();
        itemsPresenter.getItemContexts()
                .stream()
                .filter(EditHierarchyDefaultSettingsFormPresenter::isNotEmpty)
                .forEach(c -> content.getDefaultSettings().put(c.getCode(), c.getDefaultSettings()));
        SaveUIAction action = new SaveUIAction(context.getParentContext().getMetainfo().getFqn(),
                context.getRootContent(), null, context.getCode());
        action.setCheckContent(content);
        dispatch.execute(action, new BasicCallback<EmptyResult>(getDisplay())
        {
            @Override
            protected void handleSuccess(EmptyResult response)
            {
                // TODO dzevako убрать эти костыли. Тут должно быть достаточно обновить сам контент (как в else)
                // т.е. onRefresh() в HierarchyGridContentPresenter должен обеспечивать обновление всех его компонентов
                if (content.getParent() instanceof Layout)
                {
                    Layout parent = (Layout)content.getParent();
                    //сделаем unbind у контент презентера
                    int pos = parent.getContent().indexOf(content);
                    parent.getContent().remove(content);
                    context.getEventBus().fireEvent(new RefreshContentEvent(parent));

                    //сделаем bind у контент презентера
                    parent.getContent().add(pos, content);
                    context.getEventBus().fireEvent(new RefreshContentEvent(parent));
                    // Эти все манипуляции делаются потому что refresh презентера контента не обеспечивает
                    // обновления всех его компонентов(а должен), поэтому приходится принудительно делать заново bind()
                }
                else
                {
                    context.getEventBus().fireEvent(new RefreshContentEvent(content));
                }
                unbind();
            }
        });
    }

    private static boolean isNotEmpty(HierarchyItemSettingsContext context)
    {
        HierarchyGridDefaultViewSettings settings = context.getDefaultSettings();
        return !settings.getListSort().getElements().isEmpty()
               || !settings.getListFilter().getElements().isEmpty()
               || !settings.isInherit()
               || settings.getPageSize() != AdvlistConstants.DEFAULT_PAGE_SIZE
               || settings.getHeaderAppearance() != HeaderAppearance.defaultValue()
               || !settings.getColumnList().isEmpty()
               || settings.isInheritWidthFromParentLevel()
               || Boolean.FALSE.equals(settings.getInheritDefaultSortFromStructure());
    }
}
