package ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptorFactoryImpl;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.columns.EscalationSchemeLevelColumnsMessages;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinModule.EscalationSchemeLevelPropertyCode;

/**
 * <AUTHOR>
 * @since 22.08.2012
 *
 */
public class EscalationSchemeLevelFormPropertyParametersDescriptorFactoryImpl<F extends ObjectForm> extends
        PropertyParametersDescriptorFactoryImpl<EscalationSchemeLevel, F>
{
    @Inject
    EventActionMessages eventActionMessages;
    @Inject
    EscalationSchemeLevelColumnsMessages levelsMessages;
    @Inject
    AdminDialogMessages adminDialogMessages;

    @Override
    protected void build()
    {
        //@formatter:on
        registerOrModifyProperty(EscalationSchemeLevelPropertyCode.CONDITION, cmessages.condition(), true, "condition",
                0, true, true);
        registerOrModifyProperty(EscalationSchemeLevelPropertyCode.VALUE_PERCENT, cmessages.value(), true,
                "valuePercent", 1, true, true);
        registerOrModifyProperty(EscalationSchemeLevelPropertyCode.VALUE_DTI, cmessages.value(), true, "valueDTI", 1,
                true, true);
        registerOrModifyProperty(EscalationSchemeLevelPropertyCode.ACTION, eventActionMessages.action(), false,
                "action", 2, true, true);
        registerOrModifyProperty(EscalationSchemeLevelPropertyCode.EXEC_ACTION,
                levelsMessages.executeActionAfterSchemeChange(), false, "execAction", 3, true, true);
        registerOrModifyProperty(EscalationSchemeLevelPropertyCode.SETTINGS_SET, adminDialogMessages.settingsSet(),
                false, "settingsSet", 4, true, false);
        //@formatter:off
    }
}
