package ru.naumen.metainfoadmin.client.interfaze.interfacetab;

import java.util.List;

import ru.naumen.core.shared.interfacesettings.InterfaceSettings;
import ru.naumen.core.shared.personalsettings.ThemeClient;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 10 авг. 2016 г.
 *
 */
public class InterfaceSettingsContext
{
    private InterfaceSettings settings;
    private List<ThemeClient> themes;

    public InterfaceSettings getSettings()
    {
        if (settings == null)
        {
            settings = new InterfaceSettings();
        }
        return settings;
    }

    public List<ThemeClient> getThemes()
    {
        if (themes == null)
        {
            themes = new ArrayList<>();
        }
        return themes;
    }

    public void setSettings(InterfaceSettings settings)
    {
        this.settings = settings;
    }

    public void setThemes(List<ThemeClient> themes)
    {
        this.themes = themes;
    }
}