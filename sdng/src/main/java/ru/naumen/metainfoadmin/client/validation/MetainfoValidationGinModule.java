/**
 *
 */
package ru.naumen.metainfoadmin.client.validation;

import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfoadmin.client.catalog.CatalogMessages;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Inject;
import com.google.inject.Provider;

/**
 * <AUTHOR>
 * @since 18.10.2012
 *
 */
public class MetainfoValidationGinModule extends AbstractGinModule
{
    public static class CatalogCodeDuplicationValidatorProvider implements Provider<CatalogCodeDuplicationValidator>
    {
        @Inject
        CatalogMessages messages;
        @Inject
        AdminMetainfoServiceAsync metainfoService;

        private static CatalogCodeDuplicationValidator SINGLETON;

        @Override
        public CatalogCodeDuplicationValidator get()
        {
            if (SINGLETON == null)
            {
                SINGLETON = new CatalogCodeDuplicationValidator(messages, metainfoService);
            }
            SINGLETON.refreshExistingCodes();
            return SINGLETON;
        }
    }

    @Override
    protected void configure()
    {
        bind(CatalogCodeDuplicationValidator.class).toProvider(CatalogCodeDuplicationValidatorProvider.class);
    }
}