package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.attributeOfRelatedObject;

import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Делегат обновления свойства "Атрибут связи".
 * <AUTHOR>
 * @since 08.08.18
 */
public class AttrChainRefreshDelegateEdit<F extends ObjectForm>
        implements
        AttributeFormPropertyDelegateRefresh<F, RelationsAttrTreeObject, PropertyBase<RelationsAttrTreeObject,
                PopupValueCellTree<RelationsAttrTreeObject, RelationsAttrTreeObject,
                        FilteredSingleSelectionModel<RelationsAttrTreeObject>>>>
{
    @Override
    public void refreshProperty(PropertyContainerContext context,
            PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<RelationsAttrTreeObject, RelationsAttrTreeObject
                    , FilteredSingleSelectionModel<RelationsAttrTreeObject>>> property,
            AsyncCallback<Boolean> callback)
    {
        // Для просмотра значения выведено отдельное свойство
        callback.onSuccess(false);
    }
}
