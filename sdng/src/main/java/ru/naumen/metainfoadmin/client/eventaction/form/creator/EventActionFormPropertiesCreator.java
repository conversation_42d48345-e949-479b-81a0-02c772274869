package ru.naumen.metainfoadmin.client.eventaction.form.creator;

import java.util.List;

import com.google.gwt.event.logical.shared.ValueChangeHandler;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.IFormPropertiesCreator;
import ru.naumen.core.client.mvp.HasUnbind;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;

/**
 * <AUTHOR>
 * @since 02.12.2011
 *
 */
public interface EventActionFormPropertiesCreator extends IFormPropertiesCreator, HasUnbind
{
    void addProperty(EventActionFormDisplay display, Property<?> property, int index);

    /**
     * Добавить параметр, и расположить его после другого параметра
     * @param display представление, на котором отображаются параметры
     * @param property добавляемый параметр
     * @param afterProperty параметр, после которого необходимо добавить новый параметр
     */
    void addPropertyAfter(EventActionFormDisplay display, Property<?> property, Property<?> afterProperty);

    void bindProperties(EventActionFormDisplay display, List<ClassFqn> fqns);

    EventActionWithScript getEventAction();

    void init(EventActionWithScript eventAction, Property<SelectItem> event);

    void refreshProperties(EventActionFormDisplay display, List<ClassFqn> fqns, @Nullable String event);

    void setProperty(EventActionFormDisplay display, Property<?> property, int index);

    void setReadyState(ReadyState rs);

    EventActionFormPropertiesCreator setValidation(Processor validation);

    EventActionFormPropertiesCreator addValueChangeHandler(ValueChangeHandler<Boolean> handler);

    /**
     * Взаимодействие с внешней системой
     */
    boolean isSlowEventAction();

    /**
     * Выполнять асинхронно
     */
    boolean isTxNewEventAction();
}
