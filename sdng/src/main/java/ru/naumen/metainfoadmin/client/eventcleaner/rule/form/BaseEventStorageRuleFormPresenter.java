package ru.naumen.metainfoadmin.client.eventcleaner.rule.form;

import static ru.naumen.metainfo.shared.eventcleaner.EventCleanerJobDtoProperties.anyHasSubject;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Provider;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.admin.client.eventcleaner.EventCleanerSettingsMessages;
import ru.naumen.core.client.forms.DialogDisplay.DialogWidth;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.validation.IntegerNotEmptyValidator;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.LabelProperty;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.PropertyUtils;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.dispatch2.GetDescendantMetaClassesLiteAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesLiteResponse;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.eventcleaner.rule.dispatch.GetAvailableEventCategoriesAction;
import ru.naumen.metainfo.shared.eventcleaner.rule.dispatch.GetAvailableEventCategoriesResponse;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.EventStorageRuleGinModule;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDtoTreeFactoryContext;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDtoTreePropertyFactory;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree.EventsDtoTreeSelectionModel;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.metaclasstree.MetaClassSelectPropertyFactory;

/**
 * Базовый {@link Presenter} формы правила очистки лога событий
 * <AUTHOR>
 * @since 27.06.2023
 */
public abstract class BaseEventStorageRuleFormPresenter extends OkCancelPresenter<PropertyDialogDisplay>
        implements CallbackPresenter<DtObject, DtObject>
{
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> title;
    @Inject
    @Named(PropertiesGinModule.INTEGER)
    protected Property<Long> storageTime;
    @Inject
    @Named(PropertiesGinModule.MULTI_SELECT_BOX)
    private Provider<SelectListProperty<Collection<String>, Collection<SelectItem>>> multiSelectProvider;
    @Inject
    @Named(EventStorageRuleGinModule.METACLASS_TREE)
    private MetaClassSelectPropertyFactory metaClassTreeFactory;

    @Inject
    @Named(EventStorageRuleGinModule.SELECT_ALL)
    protected DtObject allOption;

    @Inject
    protected MetainfoServiceAsync metainfoService;
    @Inject
    private EventsDtoTreePropertyFactory eventTreeFactory;
    @Inject
    protected DispatchAsync dispatch;
    @Inject
    protected EventCleanerSettingsMessages eventCleanerMessages;
    @Inject
    protected PlaceController placeController;
    @Inject
    private Provider<LabelProperty> labelProvider;

    @Inject
    private Processor validation;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    private IntegerNotEmptyValidator integerNotEmptyValidator;
    @Inject
    private NotEmptyCollectionValidator<Collection<DtObject>> notEmptyEventsValidator;
    @Inject
    private NotEmptyCollectionValidator<Collection<DtObject>> notEmptyCollectionValidator;
    @Inject
    private StorageTimeValidator storageTimeValidator;
    @Inject
    private EventStorageRuleCodeValidator codeValidator;

    protected PropertyBase<Collection<DtObject>,
            PopupValueCellTree<DtObject, Collection<DtObject>, EventsDtoTreeSelectionModel>> events;
    protected Property<Collection<DtObject>> metaClasses;
    protected StateBlockView stateBlockView;
    private PropertyRegistration<Collection<DtObject>> mataClassPropertyRegistration;
    private ValidationUnit<Collection<DtObject>> metaClassValidationUnit;
    private String storageMessage;
    private String statesMessage;
    private boolean statesMessageOnlyClasses = false;
    private boolean metaClassInitialized = false;
    private boolean statesInitialized = false;

    protected AsyncCallback<DtObject> refreshCallback;
    protected DtObject eventStorageRule;

    protected BaseEventStorageRuleFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(@Nullable DtObject eventStorageRule, AsyncCallback<DtObject> refreshCallback)
    {
        this.eventStorageRule = eventStorageRule;
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }

        boolean isAllEvents = isCollectionContainsAll(events.getValue());
        boolean isAllMetaClass = mataClassPropertyRegistration != null &&
                                 isCollectionContainsAll(metaClasses.getValue());
        onApplyAction(eventStorageRule,
                title.getValue(),
                code.getValue(),
                isAllEvents,
                isAllEvents ? new HashSet<>() : extractEventsCode(),
                isAllMetaClass,
                isAllMetaClass ? new HashSet<>() : extractMetaClasses(),
                extractStates(),
                storageTime.getValue().intValue());
    }

    @Override
    protected void onBind()
    {
        stateBlockView = new StateBlockView(getDisplay(), createStateBlockProperty(), eventCleanerMessages,
                multiSelectProvider);

        bindProperties();
        super.onBind();
        getDisplay().asWidget().addStyleName(WidgetResources.INSTANCE.form().eventStorageRuleForm());
        getDisplay().setDialogWidth(DialogWidth.W600);
        getDisplay().display();
    }

    protected void bindProperties()
    {
        title.setCaption(commonMessages.title());
        title.setMaxLength(StringAttributeType.MAX_LENGTH_DEFAULT);
        title.setValidationMarker(true);
        validation.validate(title, notEmptyValidator);
        getDisplay().add(title);
        DebugIdBuilder.ensureDebugId(title, "title");

        code.setCaption(commonMessages.code());
        code.setMaxLength(Constants.MAX_METAINFO_KEY_LENGTH);
        code.setValidationMarker(true);
        validation.validate(code, codeValidator);
        validation.validate(code, notEmptyValidator);
        getDisplay().add(code);
        DebugIdBuilder.ensureDebugId(code, "code");

        initEvents();

        storageTime.setCaption(eventCleanerMessages.eventStorageRuleStorageTime());
        storageTime.setValidationMarker(true);
        storageTime.addValueChangeHandler(e ->
        {
            Long value = e.getValue();
            if (value != null && value == 0)
            {
                storageMessage = eventCleanerMessages.selectedRecordsWillNotBeSaved();
            }
            else
            {
                storageMessage = null;
            }
            updateAssertionMessage();
        });
        PropertyUtils.addDescriptionHint(storageTime, eventCleanerMessages.eventStorageRuleStorageTimeHint());
        validation.validate(storageTime, integerNotEmptyValidator);
        validation.validate(storageTime, storageTimeValidator);
        getDisplay().add(storageTime);
        DebugIdBuilder.ensureDebugId(storageTime, "storageTime");

        initBaseProperties(eventStorageRule);
    }

    /**
     * Инициализация значений свойств базовых свойств: название/код
     */
    protected void initBaseProperties(@Nullable DtObject rule)
    {
    }

    /**
     * Инициализация значений дерева событий
     */
    protected void initEventsTree(@Nullable DtObject rule)
    {
    }

    /**
     * Инициализация значений дерева классов
     * @param rule правило
     */
    protected void initMetaClassTree(@Nullable DtObject rule)
    {
        metaClassInitialized = true;
    }

    /**
     * Инициализация значений деревьев статусов
     * @param rule правило
     */
    protected void initStatesTree(@Nullable DtObject rule)
    {
        statesInitialized = true;
    }

    /**
     * Выполнить действие по нажатию кнопки "Сохранить"
     * @param rule правило
     * @param title название
     * @param code код
     * @param allEvents все события
     * @param eventCodes коды событий
     * @param allClasses все классы
     * @param classes классы
     * @param states статусы для типов
     * @param storageTime срок хранения
     */
    protected abstract void onApplyAction(@Nullable DtObject rule, String title, String code, boolean allEvents,
            Set<String> eventCodes, boolean allClasses, Set<ClassFqn> classes, Map<ClassFqn, List<String>> states,
            int storageTime);

    private boolean isCollectionContainsAll(Collection<DtObject> value)
    {
        return value.stream().anyMatch(v -> v.equals(allOption));
    }

    private Set<String> extractEventsCode()
    {
        return events.getValue()
                .stream()
                .map(IUUIDIdentifiable::getUUID)
                .collect(Collectors.toSet());
    }

    private Set<ClassFqn> extractMetaClasses()
    {
        if (mataClassPropertyRegistration == null)
        {
            return new HashSet<>();
        }
        return metaClasses.getValue().stream().filter(value -> !value.equals(allOption)).map(DtObject::getMetainfo)
                .collect(Collectors.toSet());
    }

    private Map<ClassFqn, List<String>> extractStates()
    {
        Map<ClassFqn, List<String>> values = new HashMap<>();
        stateBlockView.getProperties().forEach((fqn, property) ->
        {
            Collection<String> st = SelectListPropertyValueExtractor.getCollectionValue(property);
            if (!st.isEmpty())
            {
                values.put(fqn, new ArrayList<>(st));
            }
        });
        return values;
    }

    private void initEvents()
    {
        dispatch.execute(new GetAvailableEventCategoriesAction(),
                new BasicCallback<GetAvailableEventCategoriesResponse>()
                {
                    @Override
                    protected void handleSuccess(GetAvailableEventCategoriesResponse response)
                    {
                        EventsDtoTreeFactoryContext context = new EventsDtoTreeFactoryContext(response.getItems());
                        events = eventTreeFactory.create(context);
                        events.setCaption(eventCleanerMessages.eventStorageRuleEventTypes());
                        onEventsInit();
                    }
                });
    }

    private void onEventsInit()
    {
        events.setValidationMarker(true);
        validation.validate(events, notEmptyEventsValidator);
        events.addValueChangeHandler(e ->
        {
            if (!isCollectionContainsAll(e.getValue()) && !anyHasSubject(e.getValue()))
            {
                if (mataClassPropertyRegistration != null)
                {
                    mataClassPropertyRegistration.unregister();
                    mataClassPropertyRegistration = null;
                    stateBlockView.unregisterBlock();
                }
                if (metaClassValidationUnit != null)
                {
                    metaClassValidationUnit.unregister();
                }
                return;
            }
            if (mataClassPropertyRegistration == null)
            {
                metaClasses = metaClassTreeFactory.create();
                metaClasses.setValidationMarker(true);
                metaClassValidationUnit = validation.validate(metaClasses, notEmptyCollectionValidator);
                metaClasses.addValueChangeHandler(ev -> onMetaClassChangeHandler(ev.getValue()));
                mataClassPropertyRegistration = getDisplay().addProperty(metaClasses, 3);
                DebugIdBuilder.ensureDebugId(metaClasses, "metaClasses");

                if (!metaClassInitialized)
                {
                    initMetaClassTree(eventStorageRule);
                }
            }
        });
        getDisplay().addProperty(events, 2);
        DebugIdBuilder.ensureDebugId(events, "events");

        initEventsTree(eventStorageRule);
    }

    private void onMetaClassChangeHandler(Collection<DtObject> values)
    {
        handleMetaClassChange(values, new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> mcList)
            {
                List<MetaClassLite> mcWithWF = new ArrayList<>();
                List<MetaClassLite> mcWithoutWF = new ArrayList<>();
                filterWFMetaClasses(mcList, mcWithWF, mcWithoutWF);
                updateAttentionWfMessage(mcWithWF, mcWithoutWF);

                if (mcWithWF.isEmpty())
                {
                    stateBlockView.unregisterBlock();
                    return;
                }

                List<ClassFqn> wfFqns = mcWithWF.stream().map(MetaClassLite::getFqn).collect(Collectors.toList());
                dispatch.execute(new GetDescendantMetaClassesLiteAction(wfFqns, true),
                        new BasicCallback<GetMetaClassesLiteResponse>()
                        {
                            @Override
                            protected void handleSuccess(GetMetaClassesLiteResponse response)
                            {
                                metaClassesHandleSuccess(response.getMetaClasses());
                            }
                        });
            }
        });
    }

    private void metaClassesHandleSuccess(List<MetaClassLite> metaClasses)
    {
        List<MetaClassLite> cases = metaClasses
                .stream()
                .filter(mc -> mc.getFqn().isCase())
                .collect(Collectors.toList());
        Set<ClassFqn> additionalClassesForTitle =
                cases.stream().map(mc -> mc.getFqn().fqnOfClass()).collect(Collectors.toSet());
        metaClasses.stream()
                .map(MetaClassLite::getFqn)
                .forEach(additionalClassesForTitle::remove);

        Map<ClassFqn, MetaClassLite> classesByFqn = metaClasses.stream()
                .filter(metaClassLite -> metaClassLite.getFqn().isClass())
                .collect(Collectors.toMap(MetaClassLite::getFqn, Function.identity()));
        if (additionalClassesForTitle.isEmpty())
        {
            bindStates(getMetaClassesWithParent(cases, classesByFqn));
        }
        else
        {
            metainfoService.getMetaClasses(additionalClassesForTitle,
                    new BasicCallback<List<MetaClassLite>>()
                    {
                        @Override
                        protected void handleSuccess(List<MetaClassLite> classList)
                        {
                            classList.forEach(metaClassLite ->
                                    classesByFqn.put(metaClassLite.getFqn(), metaClassLite));
                            bindStates(getMetaClassesWithParent(cases, classesByFqn));
                        }
                    });
        }
    }

    /**
     * @param cases коллекция выбранных типов
     * @param classesByFqn мапа классов с парами ClassFqn и соответствующий ему MetaClassLite
     * @return metaClassesWithParent мапа классов с принадлежащих ему коллекцией выбранных типов
     */
    private static Map<MetaClassLite, List<MetaClassLite>> getMetaClassesWithParent(List<MetaClassLite> cases,
            Map<ClassFqn, MetaClassLite> classesByFqn)
    {
        return cases.stream()
                .collect(Collectors.groupingBy(mcCase -> classesByFqn.get(mcCase.getFqn().fqnOfClass()),
                        LinkedHashMap::new,
                        Collectors.mapping(Function.identity(), Collectors.toList())));
    }

    private void bindStates(Map<MetaClassLite, List<MetaClassLite>> metaClassesWithParent)
    {
        stateBlockView.registerBlock(metaClassesWithParent)
                .forEach(cl -> addStatesItem(stateBlockView.getProperty(cl), getStatesFromMetaClass(cl)));
        if (!statesInitialized)
        {
            initStatesTree(eventStorageRule);
        }
    }

    private void handleMetaClassChange(Collection<DtObject> values, AsyncCallback<List<MetaClassLite>> callback)
    {
        if (isCollectionContainsAll(values))
        {
            statesMessageOnlyClasses = true;
            metainfoService.getDescendantClasses(AbstractBO.FQN, false, callback);
        }
        else
        {
            statesMessageOnlyClasses = false;
            Collection<ClassFqn> fqns = values.stream().map(DtObject::getMetainfo).collect(Collectors.toList());
            metainfoService.getMetaClasses(fqns, callback);
        }
    }

    private void updateAttentionWfMessage(List<MetaClassLite> mcWithWF, List<MetaClassLite> mcWithoutWF)
    {
        if (mcWithWF.isEmpty() || mcWithoutWF.isEmpty())
        {
            statesMessage = null;
        }
        else
        {
            statesMessage = mcWithoutWF.size() == 1 ?
                    eventCleanerMessages.couldNotSetupStatusForMetaClass(mcWithoutWF.get(0).getTitle()) :
                    eventCleanerMessages.couldNotSetupStatusForMetaClasses(
                            mcWithoutWF.stream()
                                    .filter(mc -> !statesMessageOnlyClasses || mc.getFqn().isClass())
                                    .map(ITitled::getTitle)
                                    .collect(Collectors.joining(", ")));
        }
        updateAssertionMessage();
    }

    private void updateAssertionMessage()
    {
        if (storageMessage == null && statesMessage == null)
        {
            getDisplay().clearAttentionMessage();
            return;
        }
        if (storageMessage != null && statesMessage != null)
        {
            String bothMessage = storageMessage + "\n\n" + statesMessage;
            getDisplay().addAttentionMessage(bothMessage);
            return;
        }
        getDisplay().addAttentionMessage(storageMessage != null ? storageMessage : statesMessage);
    }

    private LabelProperty createStateBlockProperty()
    {
        LabelProperty label = labelProvider.get();
        label.setCaption(eventCleanerMessages.eventStorageRuleStateBlock());
        label.getCaptionWidget().asWidget().addStyleName(WidgetResources.INSTANCE.form().formPropertyBlock());
        label.setWide(true);
        PropertyUtils.addDescriptionHint(label, eventCleanerMessages.eventStorageRuleStateHint());
        return label;
    }

    private static void filterWFMetaClasses(List<MetaClassLite> mcList, List<MetaClassLite> mcWithWF,
            List<MetaClassLite> mcWithoutWF)
    {
        for (MetaClassLite mc : mcList)
        {
            if (mc.isHidden())
            {
                continue;
            }

            if (mc.isHasWorkflow())
            {
                mcWithWF.add(mc);
            }
            else
            {
                mcWithoutWF.add(mc);
            }
        }
    }

    private static void addStatesItem(Property<?> property, Map<String, String> states)
    {
        MultiSelectCellList<String> widget = property.getValueWidget();
        widget.clear();
        states.forEach((c, t) -> widget.addItem(t, c));
    }

    private static Map<String, String> getStatesFromMetaClass(MetaClassLite cls)
    {
        Map<String, String> states = new LinkedHashMap<>();
        cls.getWorkflowLite().getStates().forEach(state -> states.put(state.getCode(), state.getTitle()));
        return states;
    }
}
