package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.annotation.Nullable;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelElementWrapper;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;

/**
 * Параметр команд действий с плитками быстрого доступа
 *
 * <AUTHOR>
 * @since 17.07.2020
 */
public class QuickAccessPanelTileCommandParam
        extends NavigationSettingsAbstractCommandParam<QuickAccessPanelElementWrapper>
{
    private String menuItemCode;

    public QuickAccessPanelTileCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, (QuickAccessPanelElementWrapper)null, callback);
    }

    public QuickAccessPanelTileCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable QuickAccessPanelElementWrapper value,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, value, callback);
        setMenuItemCodeFromValue(value);
    }

    public QuickAccessPanelTileCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable ValueSource<QuickAccessPanelElementWrapper> valueSource,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(settings, valueSource, callback);
        setMenuItemCodeFromValue(valueSource.getValue());
    }

    @Override
    @SuppressWarnings("unchecked")
    public QuickAccessPanelTileCommandParam cloneIt()
    {
        return new QuickAccessPanelTileCommandParam(getSettings(), getValueSource(), getCallback());
    }

    public String getMenuItemCode()
    {
        return menuItemCode;
    }

    public void setMenuItemCode(String menuItemCode)
    {
        this.menuItemCode = menuItemCode;
    }

    public void setMenuItemCodeFromValue(QuickAccessPanelElementWrapper wrapper)
    {
        if (wrapper != null)
        {
            menuItemCode = ((QuickAccessTileDTO)wrapper.getWrappable()).getMenuItemCode();
        }
    }

    public void setValue(QuickAccessPanelElementWrapper value)
    {
        super.setValue(value);
        setMenuItemCodeFromValue(value);
    }
}