package ru.naumen.core.client.attr.presentation.factories.edit.widget.catalog;

import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.attr.presentation.factories.edit.widget.DtObjectSelectListUtils;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.SelectAttrEmptyOptionStrategy;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.select2.single.DtObjectSingleSelectListFactory;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * <AUTHOR>
 * @since 27.12.2011
 */
@Singleton
public class CatalogItemWidgetFactoryImpl<W extends CatalogItemWidgetGinModule.SimpleCatalog> implements
        CatalogItemWidgetFactory<SelectItem, W>
{
    @Inject
    SelectAttrEmptyOptionStrategy emptyOptionStrategy;
    @Inject
    DtObjectSelectListUtils dtObjectSelectListUtils;
    @Inject
    DtObjectSingleSelectListFactory dtoSelectListFactory;

    @Override
    public HasValueOrThrow<SelectItem> initWidget(List<DtObject> objects, @Nullable Attribute attribute)
    {
        SingleSelectCellList<DtObject> select = dtoSelectListFactory
                .create(emptyOptionStrategy.isWithEmptyOption(attribute));
        return select;
    }

    @Override
    public void refreshSelect(HasValueOrThrow<SelectItem> select, List<DtObject> objects)
    {
        dtObjectSelectListUtils.refreshSingleSelect(select, objects, true);
    }
}
