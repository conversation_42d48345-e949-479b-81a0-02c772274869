package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import jakarta.inject.Inject;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.mask.InputMaskOptions;
import ru.naumen.core.client.widgets.mask.MaskedTextBox;
import ru.naumen.metainfo.shared.Constants.StringAttributeType.InputMaskMode;
import ru.naumen.metainfo.shared.elements.StringAttributeType;

import com.google.inject.Provider;
import com.google.inject.Singleton;

/**
 * Фабрика виджета для редактирования атрибута типа Строка с представлением "Поле с маской ввода"
 * <AUTHOR>
 *
 */
@Singleton
public class AttributeStringWithMaskWidgetFactoryImpl extends AttributeWidgetFactoryEditImpl<String>
{
    @Inject
    Provider<MaskedTextBox> provider;

    @Override
    public HasValueOrThrow<String> create(PresentationContext context)
    {
        MaskedTextBox res = provider.get();
        StringAttributeType strType = context.getAttributeType().cast();
        res.setMaxLength(strType.getMaxLength());
        InputMaskMode inputMaskMode = getInputMaskMode(context);
        if (inputMaskMode != null)
        {
            InputMaskOptions options = InputMaskOptions.create(inputMaskMode, getInputMask(context));
            if (context.getAttribute() != null)
            {
                options.setAttrFqn(context.getAttribute().getFqn());
            }
            if (context.getInputMaskMode() != null)
            {
                options.setShowAliasError(true);
            }
            res.mask(options);
        }
        return res;
    }

    private String getInputMask(PresentationContext context)
    {
        //На форме атрибута записываем текущее значение только в контекст, а не в объект типа атрибута,
        //чтобы потом не откатывать изменения по нажатию на Отмена
        if (context.getInputMask() != null)
        {
            return context.getInputMask();
        }
        return context.getAttributeType().<StringAttributeType> cast().getInputMask();
    }

    private InputMaskMode getInputMaskMode(PresentationContext context)
    {
        if (context.getInputMaskMode() != null)
        {
            return context.getInputMaskMode();
        }
        return context.getAttributeType().<StringAttributeType> cast().getInputMaskMode();
    }
}