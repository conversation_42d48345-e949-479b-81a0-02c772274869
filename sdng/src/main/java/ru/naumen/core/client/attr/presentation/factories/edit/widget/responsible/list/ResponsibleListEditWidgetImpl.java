package ru.naumen.core.client.attr.presentation.factories.edit.widget.responsible.list;

import java.util.Map;

import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Focusable;
import com.google.inject.Inject;
import com.google.inject.Provider;

import ru.naumen.core.client.FormContextHolder;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.responsible.ResponsibleEditWidgetImpl;
import ru.naumen.core.client.common.DataSourceReadyEventHandler;
import ru.naumen.core.client.events.PendingActionsChangedEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.ValidationEvent; //NOPMD
import ru.naumen.core.client.widgets.HasPossibleValuesReadyHanlder;
import ru.naumen.core.client.widgets.HasSearchString;
import ru.naumen.core.client.widgets.MassEditState;
import ru.naumen.core.client.widgets.MayHavePossibleValues;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.WidgetStyleUpdater;
import ru.naumen.core.client.widgets.WidgetStyleUpdater.WidgetTypeCode;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.client.widgets.clselect.dto.SelectDtOGinModule.SelectResponsibleListDataProviderFactory;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.select.SelectListPendingActionsChangedHandler;
import ru.naumen.core.client.widgets.select.SelectResponsibleListDataProvider;
import ru.naumen.core.shared.dispatch.CanTakeResponsiblePermission;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Виджет редактирования свойства ответственного со ссылками "Себе" и "Своей команде"
 * для случая отображения со списком
 *
 * <AUTHOR>
 * @since 27.05.2015
 */
public class ResponsibleListEditWidgetImpl extends ResponsibleEditWidgetImpl implements ResponsibleListEditWidget,
        Focusable, HasPossibleValuesReadyHanlder, MayHavePossibleValues, HasSearchString
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectCellListBuilderProvider;
    @Inject
    private SelectResponsibleListDataProviderFactory dataProviderFactory;
    @Inject
    private WidgetResources resources;
    @Inject
    private WidgetStyleUpdater styleUpdater;
    @Inject
    private FormContextHolder formContextHolder;
    @Inject
    private Provider<SelectListPendingActionsChangedHandler> pendingActionsHandlerProvider;

    private SelectResponsibleListDataProvider dataProvider;

    private SingleSelectCellList<DtObject> listWidget;

    @Override
    public HandlerRegistration addPossibleValuesReadyHandler(DataSourceReadyEventHandler handler)
    {
        return addReadyHandler(handler);
    }

    @Override
    public HandlerRegistration addReadyHandler(DataSourceReadyEventHandler handler)
    {
        return dataProvider.addReadyHandler(handler);
    }

    @Override
    public void clearValue()
    {
        listWidget.clearValue();
    }

    @Override
    public Map<String, CanTakeResponsiblePermission> getCanTakes()
    {
        return dataProvider != null ? dataProvider.getCanTakes() : null;
    }

    @Override
    public MassEditState getMassEditState()
    {
        return listWidget.getMassEditState();
    }

    @Override
    public String getSearchString()
    {
        return listWidget.getSearchString();
    }

    @Override
    public int getTabIndex()
    {
        return listWidget.getTabIndex();
    }

    @Override
    public boolean hasAnyPossibleValue()
    {
        return listWidget.hasAnyPossibleValue();
    }

    @SuppressWarnings("unchecked")
    @Override
    public void init(PresentationContext context)
    {
        SimpleSelectCellListBuilder<DtObject> listBuilder = selectCellListBuilderProvider
                .get();
        listBuilder.setMultiSelect(false);
        listBuilder.setSelectSingleValue(formContextHolder.needSelectSingleValueForAttribute(context));
        dataProvider = dataProviderFactory.create(context);
        listBuilder.setDataProvider(dataProvider);
        listBuilder.withEmptyOption();
        listBuilder.setHasSearch(true);
        listBuilder.setPrsContext(context);
        selectWidget = listBuilder.build();

        listWidget = (SingleSelectCellList<DtObject>)selectWidget;

        listWidget.setStyleName(resources.form().formSelectSelected());
        styleUpdater.setFocusAndBlurHandlers(WidgetTypeCode.SIMPLE, listWidget);
        styleUpdater.setValidationHandler(WidgetTypeCode.SIMPLE, listWidget);

        addHandler(event -> listWidget.fireEvent(new ValidationEvent(listWidget, event.isValid())), ValidationEvent
                .getType());

        SelectListPendingActionsChangedHandler pendingActionsHandler = pendingActionsHandlerProvider.get();
        pendingActionsHandler.init(listWidget, dataProvider, context);
        listWidget.addHandler(pendingActionsHandler, PendingActionsChangedEvent.TYPE);
        addHandler(listWidget::fireEvent, PendingActionsChangedEvent.TYPE);

        takeWidget.getTake().setVisible(false);
        takeWidget.getTakeTeam().setVisible(false);

        DebugIdBuilder.ensureDebugId(listWidget, "responsibleInput");
        DebugIdBuilder.ensureDebugId(takeWidget, "take");

        FlowPanel fp = new FlowPanel();
        fp.add(selectWidget);
        fp.add(takeWidget);

        initWidget(fp);
    }

    @Override
    public boolean isValueEmpty()
    {
        return listWidget.isValueEmpty();
    }

    @Override
    public void refresh()
    {
        dataProvider.recalculate();
        ((SingleSelectCellList<DtObject>)selectWidget).refreshPopupCellList();
    }

    @Override
    public void setAccessKey(char key)
    {
        listWidget.setAccessKey(key);
    }

    @Override
    public void setFocus(boolean focused)
    {
        listWidget.setFocus(focused);
    }

    @Override
    public void setMassEditState(MassEditState state)
    {
        listWidget.setMassEditState(state);
    }

    @Override
    public void setTabIndex(int index)
    {
        listWidget.setTabIndex(index);
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        super.unbind(new BasicCallback<>());
        if (dataProvider != null)
        {
            dataProvider.unbind(callback);
        }
    }
}
