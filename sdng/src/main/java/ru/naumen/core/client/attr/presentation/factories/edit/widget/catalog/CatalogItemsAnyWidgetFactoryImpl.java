package ru.naumen.core.client.attr.presentation.factories.edit.widget.catalog;

import ru.naumen.core.client.attr.presentation.factories.edit.widget.catalog.CatalogItemWidgetGinModule.CatalogWithAnyOption;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.shared.dto.DtObject;

/**
 * <AUTHOR>
 * @since 05.07.2012
 *
 */
public class CatalogItemsAnyWidgetFactoryImpl extends CatalogItemsWidgetFactoryImpl<CatalogWithAnyOption>
{
    @Override
    protected MultiSelectCellList<DtObject> createWidget()
    {
        MultiSelectCellList<DtObject> select = super.createWidget();
        select.setHasAnyOption(true);
        return select;
    }
}
