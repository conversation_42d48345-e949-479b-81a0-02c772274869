/**
 *
 */
package ru.naumen.core.client.attr.presentation.factories.edit.widget.responsible.list;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;
import com.google.inject.Provider;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.responsible.CurrentResponsibleDtObjectFactory;
import ru.naumen.core.client.events.FilterFieldEvent;
import ru.naumen.core.client.events.FilterFieldEventHandler;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Фабрика виджета выбора ответственного для представления "Список со сдвигом"
 * <AUTHOR>
 * @since 27.05.2015
 */
public class ResponsibleListWidgetFactoryImpl extends AttributeWidgetFactoryEditImpl<DtObject>
{
    private class SelectListFilterHandler implements FilterFieldEventHandler
    {
        Attribute attribute;
        ResponsibleListEditWidget widget;

        SelectListFilterHandler(Attribute attribute, ResponsibleListEditWidget widget)
        {
            this.attribute = attribute;
            this.widget = widget;
        }

        @Override
        public void onFilterField(FilterFieldEvent e)
        {
            if (attribute.getCode().equals(e.getAttr()))
            {
                widget.refresh();
            }
        }
    }

    @Inject
    private Provider<ResponsibleListEditWidget> responsibleWidgetProvider;
    @Inject
    private Provider<ResponsibleListEditHandler> responsiblePresenterProvider;

    @Inject
    private CurrentResponsibleDtObjectFactory responsibleFactory;

    @Override
    public HasValueOrThrow<DtObject> create(PresentationContext context)
    {
        throw new UnsupportedOperationException();
    }

    @Override
    public void create(final PresentationContext context, final AsyncCallback<HasValueOrThrow<DtObject>> callback)
    {

        ResponsibleListEditWidget responsibleWidget = responsibleWidgetProvider.get();
        responsibleWidget.init(context);

        final ResponsibleListEditHandler responsiblePresenter = responsiblePresenterProvider.get();
        responsiblePresenter.init(responsibleWidget, context);

        Attribute attribute = context.getAttribute();
        if (attribute != null)
        {
            DtObject object = context.getObject();
            CommonUtils.assertObjectNotNull(object);
            object.setProperty(attribute.getCode(), responsibleFactory.create(object));
            responsibleWidget.asWidget().addHandler(new SelectListFilterHandler(attribute, responsibleWidget),
                    FilterFieldEvent.getType());
        }

        callback.onSuccess(responsibleWidget);
    }
}