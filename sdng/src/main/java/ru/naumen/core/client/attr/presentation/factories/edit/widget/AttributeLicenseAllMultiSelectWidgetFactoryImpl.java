package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import jakarta.inject.Singleton;

/**
 * Фабрика виджета для множественного выбора лицензий (с любым набором)
 * <AUTHOR>
 * @since 07.05.2019
 */
@Singleton
public class AttributeLicenseAllMultiSelectWidgetFactoryImpl extends AttributeLicenseWidgetFactoryImpl
{
    public AttributeLicenseAllMultiSelectWidgetFactoryImpl()
    {
        super(false, true);
    }
}