package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import java.util.ArrayList;
import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.common.collect.Lists;
import com.google.inject.Provider;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.timer.TimerConstants;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.timer.Status;
import ru.naumen.metainfo.shared.Constants.TimerAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * <AUTHOR>
 * @since 22.06.2012
 */
@Singleton
public class AttributeTimerStateWidgetFactoryImpl extends AttributeWidgetFactoryEditImpl<Collection<SelectItem>>
{
    @Inject
    TimerConstants timerConstants;
    @Inject
    private Provider<SimpleSelectCellListBuilder<String>> selectListBuilderProvider;

    @Override
    public HasValueOrThrow<Collection<SelectItem>> create(PresentationContext context)
    {
        MultiSelectCellList<String> widget = selectListBuilderProvider.get().setMultiSelect(true).setHasSearch(true)
                .build();

        for (Status status : getStatuses(context.getAttributeType()))
        {
            widget.addItem(getTitle(status), status.getCode());
        }
        return widget;
    }

    private ArrayList<Status> getStatuses(AttributeType type)
    {
        ArrayList<Status> statuses = Lists.newArrayList(Status.values());
        if (TimerAttributeType.CODE.equals(type.getCode()))
        {
            statuses.remove(Status.EXCEED);
        }
        return statuses;
    }

    private String getTitle(Status status)
    {
        if (!timerConstants.statuses().containsKey(status.name()))
        {
            return StringUtilities.EMPTY;
        }
        return timerConstants.statuses().get(status.name());
    }
}
