package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import jakarta.inject.Singleton;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.TextAreaWidget;

/**
 * <AUTHOR>
 * @since 27.12.2011
 */
@Singleton
public class AttributeTextWidgetFactoryImpl extends AttributeWidgetFactoryEditImpl<String>
{
    @Override
    public HasValueOrThrow<String> create(PresentationContext context)
    {
        TextAreaWidget w = new TextAreaWidget();
        w.setVisibleLines(10);
        return w;
    }
}
