package ru.naumen.core.client.attr.presentation.factories.edit.widget.catalog;

import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.attr.presentation.factories.edit.widget.DtObjectSelectListUtils;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.catalog.CatalogItemWidgetGinModule.SimpleCatalog;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.select2.single.DtObjectMultiSelectListFactory;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * <AUTHOR>
 * @since 27.12.2011
 */
@Singleton
public class CatalogItemsWidgetFactoryImpl<W extends SimpleCatalog> implements
        CatalogItemWidgetFactory<Collection<SelectItem>, W>
{
    @Inject
    protected DtObjectSelectListUtils dtObjectSelectListUtils;
    @Inject
    protected DtObjectMultiSelectListFactory dtoMultiSelectListFactory;

    @Override
    public MultiSelectCellList<DtObject> initWidget(List<DtObject> objects, @Nullable Attribute attribute)
    {
        MultiSelectCellList<DtObject> select = createWidget();
        select.addItems(objects);
        return select;
    }

    @Override
    public void refreshSelect(HasValueOrThrow<Collection<SelectItem>> select, List<DtObject> objects)
    {
        dtObjectSelectListUtils.refreshMultiSelect(select, objects);
    }

    protected MultiSelectCellList<DtObject> createWidget()
    {
        return dtoMultiSelectListFactory.create(false);
    }
}
