/**
 * Виджет для редактирования агрегирующего атрибута.
 * 
 * <p>Реализация фабрики виджета для редактирования агрегирующего атрибута в виде выпадающего списка либо дерева, 
 * в зависимости от наличия у объектов класса родителя. Если у объектов класса нет родителя (например, "Запрос"), 
 * то виджетом для редактирования будет выпадающий список, в противном случае - дерево объектов.
 */
@javax.annotation.ParametersAreNonnullByDefault
package ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate;