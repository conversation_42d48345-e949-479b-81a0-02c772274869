package ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate.list;

import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import com.google.inject.Provider;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.widgets.clselect.AbstractSelectCellList;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;

/**
 * <AUTHOR>
 * @since Sep 4, 2013
 */
public class AggregateSelectListMultiProviderImpl extends AggregateSelectListProviderImpl<Collection<SelectItem>>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;

    @Override
    public AbstractSelectCellList<DtObject, Collection<SelectItem>> get(PresentationContext context,
            List<DtObject> items)
    {
        MultiSelectCellList<DtObject> result = selectListBuilderProvider.get().setMultiSelect(true).setHasSearch(true)
                .build();
        if (formDescription.needSelectSingleValueForAttribute(context) && items.size() == 1)
        {
            result.setObjValue(items);
        }
        return result;
    }

}
