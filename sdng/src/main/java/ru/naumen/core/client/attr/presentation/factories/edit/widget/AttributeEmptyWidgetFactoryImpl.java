package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import jakarta.inject.Singleton;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.widgets.EmptyWidget;
import ru.naumen.core.client.widgets.HasValueOrThrow;

/**
 * Фабрика пустых виджетов.
 * <AUTHOR>
 * @since Aug 13, 2022
 */
@Singleton
public class AttributeEmptyWidgetFactoryImpl extends AttributeWidgetFactoryEditImpl<Object>
{
    @Override
    public HasValueOrThrow<Object> create(PresentationContext context)
    {
        return new EmptyWidget();
    }
}
