package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import jakarta.inject.Singleton;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.widgets.BooleanRadioButtonGroup;
import ru.naumen.core.client.widgets.HasValueOrThrow;

import com.google.gwt.user.client.Random;

/**
 * <AUTHOR>
 * @since 27.12.2011
 */
@Singleton
public class AttributeRadioButtonWidgetFactoryImpl extends AttributeWidgetFactoryEditImpl<Boolean>
{
    @Override
    public HasValueOrThrow<Boolean> create(PresentationContext context)
    {
        return new BooleanRadioButtonGroup("" + Random.nextInt());
    }
}
