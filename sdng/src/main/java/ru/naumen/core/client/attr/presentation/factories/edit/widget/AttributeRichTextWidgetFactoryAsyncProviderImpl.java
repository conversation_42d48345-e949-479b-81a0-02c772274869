package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import java.util.ArrayList;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.RichTextWidgetBase;
import ru.naumen.core.client.widgets.RichTextWidgetSplitPoint;
import ru.naumen.core.client.widgets.richtext.mentions.EditorMentionContext;
import ru.naumen.core.shared.Constants.AdvList;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.fts.shared.GetMentionSettingsAction;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Провайдер для виджета RTF
 *
 * <AUTHOR>
 * @since Dec 18, 2015
 */
@Singleton
public class AttributeRichTextWidgetFactoryAsyncProviderImpl extends AttributeWidgetFactoryEditImpl<String>
{
    @Inject
    private SplitPointService splitPointService;
    @Inject
    private DispatchAsync dispatch;

    @Override
    public RichTextWidgetBase<?> create(PresentationContext context)
    {
        RichTextWidgetSplitPoint rtfSP = splitPointService.get(RichTextWidgetSplitPoint.class);
        return rtfSP.getRichTextWidget(context);
    }

    @Override
    public void create(final PresentationContext context, final AsyncCallback<HasValueOrThrow<String>> callback)
    {
        final DtObject object = context.getObject();
        if (object == null)
        {
            return;
        }

        final ClassFqn contextClassFqn = object.getProperty(AdvList.MASS_OPERATION_MENTION_CONTEXT_FQN);
        if (contextClassFqn == null)
        {
            //Нет смысла вызывать GetMentionSettingsAction, передавая в него пустой contextClassFqn. В итоге в callback
            //придет пустой список. Поэтому сразу вызываем callback с пустым списком.
            mentionSettingsActionCallback(context, callback, new ArrayList<>());
        }
        else
        {
            dispatch.execute(new GetMentionSettingsAction(contextClassFqn, object.getUUID()),
                    new BasicCallback<SimpleResult<ArrayList<SimpleDtObject>>>()
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<ArrayList<SimpleDtObject>> value)
                        {
                            mentionSettingsActionCallback(context, callback, value.get());
                        }
                    });
        }
    }

    /**
     * Выполнить callback на полученном списке настроек упоминания.
     * @param context параметров.
     * @param callback для вызова.
     * @param settings список настроек упоминания.
     */
    private void mentionSettingsActionCallback(final PresentationContext context,
            final AsyncCallback<HasValueOrThrow<String>> callback,
            ArrayList<SimpleDtObject> settings)
    {
        EditorMentionContext mentionContext = new EditorMentionContext();
        mentionContext.setAvailableFastLinkSettings(settings);
        mentionContext.setSubject(context.getObject());
        mentionContext.setSourceForm(context.getSourceFormObject());
        mentionContext.setAttrCode(context.getAttributeCode());
        mentionContext.setOrigin(context.getOrigin());
        RichTextWidgetBase<?> widget = create(context);
        widget.setMentionContext(mentionContext);
        callback.onSuccess(widget);
    }
}
