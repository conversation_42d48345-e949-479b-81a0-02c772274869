package ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate.list;

import java.util.List;

import jakarta.inject.Inject;

import com.google.inject.Provider;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.widgets.clselect.AbstractSelectCellList;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;

/**
 * <AUTHOR>
 * @since Sep 4, 2013
 */
public class AggregateSelectListSingleProviderImpl extends AggregateSelectListProviderImpl<SelectItem>
{
    @Inject
    private Provider<SimpleSelectCellListBuilder<DtObject>> selectListBuilderProvider;

    @Override
    public AbstractSelectCellList<DtObject, SelectItem> get(PresentationContext context, List<DtObject> items)
    {
        SingleSelectCellList<DtObject> result = selectListBuilderProvider.get().setHasSearch(true).withEmptyOption()
                .build();
        result.addItems(items);
        if (formDescription.needSelectSingleValueForAttribute(context) && items.size() == 1)
        {
            result.setObjValue(items.get(0));
        }
        return result;
    }

}
