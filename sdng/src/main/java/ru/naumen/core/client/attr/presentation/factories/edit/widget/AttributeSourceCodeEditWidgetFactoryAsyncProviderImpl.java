package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.common.shared.utils.SourceCode;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.widgets.SourceCodeWidgetSplitPoint;
import ru.naumen.core.client.widgets.sourcecode.edit.SourceCodeEditWidget;
import ru.naumen.metainfo.shared.Constants.SourceCodeAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Асинхронный провайдер для фабрики виджета Текст с подсветкой синтаксиса
 *
 * <AUTHOR>
 * @since 20 мар. 2017 г.
 */
@Singleton
public class AttributeSourceCodeEditWidgetFactoryAsyncProviderImpl extends AttributeWidgetFactoryEditImpl<SourceCode>
{
    @Inject
    private SplitPointService splitPointService;

    @Override
    public SourceCodeEditWidget create(PresentationContext context)
    {
        SourceCodeWidgetSplitPoint rtfSP = splitPointService.get(SourceCodeWidgetSplitPoint.class);
        SourceCodeEditWidget widget = rtfSP.getSourceCodeEditWidget();
        AttributeType type = context.getAttributeType();

        if (type.hasProperty(SourceCodeAttributeType.DESCRIPTION_HELP_CODE))
        {
            widget.setHasHelp(true);
            widget.setDescriptionHelpCode(type.getProperty(SourceCodeAttributeType.DESCRIPTION_HELP_CODE));
        }
        if (type.hasProperty(SourceCodeAttributeType.API_HELP_CODE))
        {
            widget.setHasHelp(true);
            widget.setApiHelpCode(type.getProperty(SourceCodeAttributeType.API_HELP_CODE));
        }
        if (type.hasProperty(SourceCodeAttributeType.DISABLE_API_HELP_CODE))
        {
            widget.setApiButtonVisible(true);
        }
        return widget;
    }
}