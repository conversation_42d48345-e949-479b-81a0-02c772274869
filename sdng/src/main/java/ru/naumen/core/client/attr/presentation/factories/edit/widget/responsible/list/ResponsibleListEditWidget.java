package ru.naumen.core.client.attr.presentation.factories.edit.widget.responsible.list;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.responsible.ResponsibleEditWidget;

/**
 * Интерфейс отображения виджета ResponsibleListEditWidget
 * <AUTHOR>
 * @since 27.05.2015
 */
public interface ResponsibleListEditWidget extends ResponsibleEditWidget
{
    void init(PresentationContext context);
}
