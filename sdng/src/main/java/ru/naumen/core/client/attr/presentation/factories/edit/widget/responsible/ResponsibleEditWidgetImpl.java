package ru.naumen.core.client.attr.presentation.factories.edit.widget.responsible;

import java.text.ParseException;
import java.util.ArrayList;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.Composite;
import com.google.inject.Inject;

import ru.naumen.core.client.content.complexrelation.ComplexRelationFormHandler;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.HasUnbind;
import ru.naumen.core.client.widgets.HasMassEditState;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.MassEditState;
import ru.naumen.core.client.widgets.clselect.HasComplexRelationForm;
import ru.naumen.core.client.widgets.clselect.ValueToSelectItemConverter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;

/**
 * Базовый класс виджета редактирования свойства ответственного с ссылками "Себе" и "Своей команде"
 * <AUTHOR>
 * @since 27.05.2015
 */
public abstract class ResponsibleEditWidgetImpl extends Composite
        implements ResponsibleEditWidget, HasUnbind, HasMassEditState
{
    @Inject
    protected TakeWidget takeWidget;
    protected HasValueOrThrow<SelectItem> selectWidget;
    private ArrayList<HasUnbind> unbindHandlers = new ArrayList<>();

    @Inject
    private ValueToSelectItemConverter<DtObject> valueTransformer;

    @Override
    public void addUnbindHandler(HasUnbind unbindHandler)
    {
        unbindHandlers.add(unbindHandler);
    }

    @Override
    public HandlerRegistration addValueChangeHandler(ValueChangeHandler<DtObject> handler)
    {
        return selectWidget.addValueChangeHandler((event) ->
        {
            handler.onValueChange(new ValueChangeEvent<DtObject>(event.getValue())
            {
            });
        });
    }

    @Override
    public void clearValue()
    {
        selectWidget.clearValue();
    }

    @Override
    public MassEditState getMassEditState()
    {
        return selectWidget instanceof HasMassEditState ? ((HasMassEditState)selectWidget).getMassEditState()
                : MassEditState.SINGLE_VALUE;
    }

    @Override
    public Anchor getTake()
    {
        return takeWidget.getTake();
    }

    @Override
    public Anchor getTakeTeam()
    {
        return takeWidget.getTakeTeam();
    }

    @Override
    public DtObject getValue()
    {
        return selectWidget.getValue();
    }

    @Override
    public DtObject getValueOrThrow() throws ParseException
    {
        return selectWidget.getValueOrThrow();
    }

    @Override
    public void initComplexRelationMode(ComplexRelationFormHandler handler)
    {
        ((HasComplexRelationForm)selectWidget).initComplexRelationMode(handler);
    }

    @Override
    public boolean isEnabled()
    {
        return selectWidget.isEnabled();
    }

    @Override
    public boolean isValueEmpty()
    {
        return selectWidget.isValueEmpty();
    }

    @Override
    public void setEnabled(boolean enabled)
    {
        selectWidget.setEnabled(enabled);
    }

    @Override
    public void setMassEditState(MassEditState state)
    {
        if (selectWidget instanceof HasMassEditState)
        {
            ((HasMassEditState)selectWidget).setMassEditState(state);
        }
    }

    @Override
    public void setValue(DtObject value)
    {
        selectWidget.setValue(valueTransformer.transform(value));
    }

    @Override
    public void setValue(DtObject value, boolean fireEvents)
    {
        selectWidget.setValue(valueTransformer.transform(value), fireEvents);
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        for (HasUnbind uh : unbindHandlers)
        {
            uh.unbind(new BasicCallback<Void>());
        }
        unbindHandlers.clear();
    }
}
