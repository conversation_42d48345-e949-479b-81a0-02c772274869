package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import java.util.Date;

import jakarta.inject.Inject;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.widgets.DateTextBox;
import ru.naumen.core.client.widgets.datepicker.DateTextBoxWithPickerWidgetBase;
import ru.naumen.core.client.widgets.datepicker.DateTimeWidgetsSplitPoint;

/**
 * <AUTHOR>
 * @since Dec 22, 2015
 */
public class AttributeDateWidgetFactoryAsyncProviderImpl extends AttributeWidgetFactoryEditImpl<Date>
{
    @Inject
    private SplitPointService splitPointService;

    @Override
    public DateTextBoxWithPickerWidgetBase<DateTextBox> create(PresentationContext context)
    {
        DateTimeWidgetsSplitPoint datePickerSP = splitPointService.get(DateTimeWidgetsSplitPoint.class);
        return datePickerSP.getDateTextBoxPickerWidget();
    }
}
