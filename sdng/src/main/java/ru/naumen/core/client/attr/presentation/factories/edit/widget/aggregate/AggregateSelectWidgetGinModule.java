package ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate;

import ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate.list.AggregateSelectListGinModule;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate.permitted.AggregateSelectPermittedTypesGinModule;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate.tree.AggregateSelectTreeGinModule;

import com.google.gwt.inject.client.AbstractGinModule;

/**
 * <AUTHOR>
 * @since 27.02.2013
 */
public class AggregateSelectWidgetGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        install(new AggregateSelectListGinModule());
        install(new AggregateSelectPermittedTypesGinModule());
        install(new AggregateSelectTreeGinModule());
    }

}
