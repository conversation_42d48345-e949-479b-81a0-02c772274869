package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.widgets.HasValueOrThrow;

import com.google.inject.Provider;

/**
 * <AUTHOR>
 * @since 27.12.2011
 */
@Singleton
public class AttributeWidgetFactoryProviderImpl<T, W extends HasValueOrThrow<T>> extends
        AttributeWidgetFactoryEditImpl<T>
{
    @Inject
    Provider<W> provider;

    @Override
    public HasValueOrThrow<T> create(PresentationContext context)
    {
        return provider.get();
    }
}
