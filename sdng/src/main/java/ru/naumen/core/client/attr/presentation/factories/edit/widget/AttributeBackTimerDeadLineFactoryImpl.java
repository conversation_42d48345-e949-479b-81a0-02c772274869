package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import java.util.Date;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.widgets.DateIntervalTwoBoxesWidget;
import ru.naumen.core.client.widgets.DateTimeTextBox;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.WidgetGinjector.DateIntervalTwoBoxesWidgetFactory;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.datepicker.DateTextBoxWithPickerWidgetBase;
import ru.naumen.core.client.widgets.datepicker.DateTimeWidgetsSplitPoint;

/**
 *
 * <AUTHOR>
 * @since 26.06.2012
 */
@Singleton
public class AttributeBackTimerDeadLineFactoryImpl extends AttributeWidgetFactoryEditImpl<List<Date>>
{
    @Inject
    CommonMessages messages;
    @Inject
    WidgetResources widgetResources;
    @Inject
    DateIntervalTwoBoxesWidgetFactory factory;
    @Inject
    private SplitPointService splitPointService;

    @Override
    public HasValueOrThrow<List<Date>> create(PresentationContext context)
    {
        DateTimeWidgetsSplitPoint datePickerSP = splitPointService.get(DateTimeWidgetsSplitPoint.class);

        DateTextBoxWithPickerWidgetBase<DateTimeTextBox> dateTimeTextBox1 = datePickerSP
                .getDateTimeTextBoxPickerWidget();
        DateTextBoxWithPickerWidgetBase<DateTimeTextBox> dateTimeTextBox2 = datePickerSP
                .getDateTimeTextBoxPickerWidget();

        DateIntervalTwoBoxesWidget widget = factory.create(dateTimeTextBox1, dateTimeTextBox2);
        widget.setHorizontal(true);
        widget.getFromLabel().setText(messages.from() + ":");
        widget.getToLabel().setText(messages.to() + ":");
        return widget;
    }

}
