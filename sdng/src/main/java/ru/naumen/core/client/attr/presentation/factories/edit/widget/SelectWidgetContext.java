package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import java.util.Set;

import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Контекст виджета для ссылочного атрибута
 * <AUTHOR>
 * @since 27.01.2023
 */
public interface SelectWidgetContext
{
    SelectWidgetContext setPermittedTypes(Set<ClassFqn> permittedTypes);

    ReadyState getReadyState();
}
