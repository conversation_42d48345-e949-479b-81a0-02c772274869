package ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate.permitted;

import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.PrsFactoryEditSelectGinModule.WithPermittedTypes;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.PrsFactoryEditSelectGinModule.WithPermittedTypesUnion;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.PrsFactoryEditSelectGinModule.WithoutPermittedTypes;
import ru.naumen.core.client.inject.Gin;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

/**
 * <AUTHOR>
 * @since 27.02.2013
 */
public class AggregateSelectPermittedTypesGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        //@formatter:off
        install(Gin.bindSingleton(
                new TypeLiteral<AggregateSelectPermittedTypesInitializer<WithPermittedTypes>>(){}, 
                new TypeLiteral<AggregateSelectWithPermittedTypesInitializer>(){}));
        
        install(Gin.bindSingleton(
                new TypeLiteral<AggregateSelectPermittedTypesInitializer<WithoutPermittedTypes>>(){}, 
                new TypeLiteral<AggregateSelectWithoutPermittedTypesInitializer>(){}));
        
        install(Gin.bindSingleton(
                new TypeLiteral<AggregateSelectPermittedTypesInitializer<WithPermittedTypesUnion>>(){}, 
                new TypeLiteral<AggregateSelectWithPermittedTypesUnionInitializer>(){}));
        //@formatter:on
    }
}
