package ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate.permitted;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate.AggregateSelectWidgetContext;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.PrsFactoryEditSelectGinModule.PermittedTypes;

/**
 * <AUTHOR>
 * @since 27.02.2013
 */
public interface AggregateSelectPermittedTypesInitializer<P extends PermittedTypes>
{
    void init(PresentationContext prsContext, AggregateSelectWidgetContext selectContext);
}