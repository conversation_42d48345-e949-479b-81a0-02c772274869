<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui">
	<ui:with field="msg" type="ru.naumen.core.shared.attr.presentation.PresentationMessages" />
	<ui:with field="res" type="ru.naumen.core.client.widgets.WidgetResources" />
	<g:FlowPanel ui:field="panel" styleName="{res.all.takeContainer}">
		<g:Anchor ui:field="take" text="{msg.takeButton}" styleName="{res.buttons.actionLink} {res.buttons.colorActionLink}"/>
		<g:Anchor ui:field="takeTeam" text="{msg.takeTeamButton}" styleName="{res.buttons.actionLink} {res.buttons.colorActionLink}"/>
	</g:FlowPanel>
</ui:UiBinder>