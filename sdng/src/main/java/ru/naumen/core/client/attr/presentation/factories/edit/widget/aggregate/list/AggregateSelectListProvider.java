package ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate.list;

import java.util.List;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.widgets.clselect.AbstractSelectCellList;
import ru.naumen.core.shared.dto.DtObject;

/**
 * <AUTHOR>
 * @since Sep 4, 2013
 */
public interface AggregateSelectListProvider<T>
{
    AbstractSelectCellList<DtObject, T> get(PresentationContext context, List<DtObject> items);
}
