package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import java.util.Map.Entry;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.inject.Provider;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.timer.TimerConstants;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dto.SelectItem;

/**
 * <AUTHOR>
 * @since 25.06.2012
 *
 */
@Singleton
public class AttributeBackTimerExceedWidgetFactoryImpl extends AttributeWidgetFactoryEditImpl<SelectItem>
{
    @Inject
    private TimerConstants timerConstants;
    @Inject
    private Provider<SimpleSelectCellListBuilder<String>> selectListBuilderProvider;

    @Override
    public HasValueOrThrow<SelectItem> create(PresentationContext context)
    {
        SingleSelectCellList<String> widget = selectListBuilderProvider.get().setHasSearch(false)
                .setCellGlobalPaddingLeft(0).build();
        for (Entry<String, String> entry : timerConstants.deadLineStatuses().entrySet())
        {
            widget.addItem(entry.getValue(), entry.getKey());
        }
        return widget;
    }

}
