package ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate.list;

import java.util.Collection;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;

/**
 * <AUTHOR>
 * @since 27.02.2013
 */
public class AggregateSelectListGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        //@formatter:off        
        install(Gin.bindSingleton(
                new TypeLiteral<AggregateSelectListProvider<SelectItem>>(){}, 
                new TypeLiteral<AggregateSelectListSingleProviderImpl>(){}));
        
        install(Gin.bindSingleton(
                new TypeLiteral<AggregateSelectListProvider<Collection<SelectItem>>>(){}, 
                new TypeLiteral<AggregateSelectListMultiProviderImpl>(){}));
        
      //@formatter:off
        install(Gin.bindSingleton(
                new TypeLiteral<AggregateSelectListFactory<DtObject, SelectItem>>(){},
                new TypeLiteral<AggregateSelectListFactoryImpl<DtObject, SelectItem>>(){}));
        
        install(Gin.bindSingleton(
                new TypeLiteral<AggregateSelectListFactory<Collection<DtObject>, Collection<SelectItem>>>(){},
                new TypeLiteral<AggregateSelectListFactoryImpl<Collection<DtObject>, Collection<SelectItem>>>(){}));
        //@formatter:on
    }
}
