package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import jakarta.inject.Inject;

import com.google.inject.Provider;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.jsinterop.inputmask.Alias;
import ru.naumen.core.client.widgets.DoubleTextBox;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.mask.InputMaskOptions;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.DoubleAttributeType;

/**
 * Фабрика для создания виджета поля "Вещественное число" по заданному представлению атрибута
 * <AUTHOR>
 * @since 10.07.2019
 */
public class AttributeDoubleWithMaskWidgetFactoryImpl extends AttributeWidgetFactoryEditImpl<Double>
{
    public static void applyMask(DoubleAttributeType doubleType, DoubleTextBox res)
    {
        boolean hasGroupSeparator = doubleType.isHasGroupSeparator();
        Long decimalsCountRestriction = doubleType.getDecimalsCountRestriction();
        Alias alias = null;
        if (hasGroupSeparator || decimalsCountRestriction != null)
        {
            int decimalsCount = decimalsCountRestriction == null ? 0 : decimalsCountRestriction.intValue();
            alias = InputMaskUtils.createAlias(hasGroupSeparator, decimalsCount > 0, decimalsCount);
        }
        if (alias != null)
        {
            InputMaskOptions options = new InputMaskOptions();
            options.setRightAlign(false);
            res.setupMask(options, alias);
        }
    }

    @Inject
    private Provider<DoubleTextBox> provider;

    @Override
    public HasValueOrThrow<Double> create(PresentationContext context)
    {
        DoubleTextBox res = provider.get();
        // если виджет с данным представлением используется для отображения данных атрибута другого типа
        if (!Constants.DoubleAttributeType.CODE.equals(context.getAttributeType().getCode()))
        {
            return res;
        }
        DoubleAttributeType doubleType = context.getAttributeType().cast();
        applyMask(doubleType, res);
        return res;
    }
}
