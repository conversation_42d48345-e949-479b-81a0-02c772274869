package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import java.util.Date;

import jakarta.inject.Inject;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.widgets.DateTextBoxBase;
import ru.naumen.core.client.widgets.datepicker.DateTextBoxWithPickerWidgetBase;
import ru.naumen.core.client.widgets.datepicker.DateTimeWidgetsSplitPoint;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * <AUTHOR>
 * @since Dec 22, 2015
 */
public class AttributeDateTimeWidgetFactoryAsyncProviderImpl extends AttributeWidgetFactoryEditImpl<Date>
{
    @Inject
    private SplitPointService splitPointService;

    @Override
    public DateTextBoxWithPickerWidgetBase<? extends DateTextBoxBase> create(PresentationContext context)
    {
        DateTimeWidgetsSplitPoint datePickerSP = splitPointService.get(DateTimeWidgetsSplitPoint.class);
        Attribute attribute = context.getAttribute();
        if (attribute == null)
        {
            return datePickerSP.getDateTimeTextBoxPickerWidget();
        }
        String presentationCode = attribute.getViewPresentation().getCode();
        if (Presentations.DATETIME_WITH_SECONDS_VIEW.equals(presentationCode))
        {
            return datePickerSP.getDateTimeWithSecondsTextBoxPickerWidget();
        }
        if (Presentations.DATETIME_WITH_MILLIS_VIEW.equals(presentationCode))
        {
            return datePickerSP.getDateTimeWithMillisTextBoxPickerWidget();
        }
        return datePickerSP.getDateTimeTextBoxPickerWidget();
    }
}
