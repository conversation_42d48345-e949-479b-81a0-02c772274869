package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import java.util.Collection;
import java.util.Objects;

import jakarta.inject.Inject;

import com.google.inject.Singleton;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.AttributeSelectPermittedTypesProvider;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Вспомогательные методы инициализации контекста при создании виджетов атрибутов
 *
 * <AUTHOR>
 * @since 27.01.2023
 */
@Singleton
public class AttributeTypesInitializerHelper
{
    @Inject
    private AttributeSelectPermittedTypesProvider<DtObject> permittedTypesProvider;

    public void initPermittedTypes(final PresentationContext prsContext, final SelectWidgetContext selectContext)
    {
        Attribute attribute = Objects.requireNonNull(prsContext.getAttribute());
        permittedTypesProvider.get(attribute, prsContext.getTypes(), new BasicCallback<Collection<ClassFqn>>(
                selectContext.getReadyState())
        {
            @Override
            protected void handleSuccess(Collection<ClassFqn> result)
            {
                selectContext.setPermittedTypes(CollectionUtils.asHashSet(result));
            }
        });
    }
}