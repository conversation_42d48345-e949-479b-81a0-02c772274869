package ru.naumen.core.client.attr.presentation.factories.edit.widget.catalog;

import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.attr.presentation.factories.edit.widget.catalog.CatalogItemWidgetGinModule.SimpleCatalog;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * <AUTHOR>
 * @since 27.12.2011
 */
public interface CatalogItemWidgetFactory<T, W extends SimpleCatalog>
{
    HasValueOrThrow<?> initWidget(List<DtObject> objects, @Nullable Attribute attribute);

    void refreshSelect(HasValueOrThrow<T> select, List<DtObject> objects);
}
