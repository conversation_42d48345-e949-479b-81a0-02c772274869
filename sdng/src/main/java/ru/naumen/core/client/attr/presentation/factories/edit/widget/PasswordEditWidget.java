package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import java.text.ParseException;

import com.google.common.base.Strings;
import com.google.gwt.event.dom.client.BlurEvent;
import com.google.gwt.event.dom.client.BlurHandler;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.dom.client.KeyPressEvent;
import com.google.gwt.event.dom.client.KeyPressHandler;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.Panel;

import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.TextBoxWidget;

/**
 * Виджет для представления для редактирования атрибута Пароль
 * <AUTHOR>
 * @since Dec 15, 2017
 */
public class PasswordEditWidget extends Composite implements HasValueOrThrow<String>, Focusable
{
    private static final String EIGHT_BULLETS_STR = "\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022";
    // JavaScript код нажатия клаваши Backspace
    private static final int BACKSPACE_KEY_CODE = 8;
    // JavaScript код нажатия клаваши Delete
    private static final int DEL_KEY_CODE = 46;
    //Поле отвечает за ввод пароля в режиме скрытия пароля
    private TextBoxWidget passwordTextBox;
    //Поле отвечает за ввод пароля в открытом режиме
    private TextBoxWidget textBoxWidget;

    //Флаг принимает значение true в случае если виджет работает в 
    //режиме открытого пароля
    private boolean revealPasswordMode = false;

    private String value = "";

    public PasswordEditWidget()
    {
        Panel container = new FlowPanel();

        passwordTextBox = new TextBoxWidget();
        container.add(passwordTextBox);
        passwordTextBox.addFocusHandler(new FocusHandler()
        {

            @Override
            public void onFocus(FocusEvent event)
            {
                revealPasswordMode = true;
                switchMode();

            }
        });

        textBoxWidget = new TextBoxWidget();
        textBoxWidget.setVisible(false);

        container.add(textBoxWidget);
        textBoxWidget.addBlurHandler(new BlurHandler()
        {

            @Override
            public void onBlur(BlurEvent event)
            {
                revealPasswordMode = false;
                switchMode();
            }
        });

        textBoxWidget.addKeyPressHandler(new KeyPressHandler()
        {

            @Override
            public void onKeyPress(KeyPressEvent event)
            {
                int keyCode = event.getNativeEvent().getKeyCode();
                if (EIGHT_BULLETS_STR.equals(textBoxWidget.getValue())
                    && (keyCode == BACKSPACE_KEY_CODE || keyCode == DEL_KEY_CODE))
                {
                    textBoxWidget.setValue("", true);
                }
                else
                {
                    value = textBoxWidget.getValue();
                }
            }
        });

        initWidget(container);
    }

    @Override
    public HandlerRegistration addValueChangeHandler(ValueChangeHandler<String> handler)
    {
        return textBoxWidget.addValueChangeHandler(handler);
    }

    @Override
    public int getTabIndex()
    {
        return passwordTextBox.getTabIndex();
    }

    @Override
    public String getValue()
    {
        return value;
    }

    @Override
    public String getValueOrThrow() throws ParseException
    {
        return getValue();
    }

    @Override
    public boolean isEnabled()
    {
        return true;
    }

    @Override
    public void setAccessKey(char key)
    {
        passwordTextBox.setAccessKey(key);
        textBoxWidget.setAccessKey(key);
    }

    @Override
    public void setFocus(boolean focused)
    {
        if (revealPasswordMode)
        {
            textBoxWidget.setFocus(true);
        }
    }

    @Override
    public void setTabIndex(int index)
    {
        passwordTextBox.setTabIndex(index);
        textBoxWidget.setTabIndex(index);
    }

    @Override
    public void setValue(String value)
    {
        setValue(value, true);
    }

    @Override
    public void setValue(String value, boolean fireEvents)
    {
        this.value = value;
        if (!Strings.isNullOrEmpty(value))
        {
            passwordTextBox.setValue(EIGHT_BULLETS_STR);
        }
    }

    /**
     * Метод отвечает за переключение между открытым и закрытым (в виде точек) отображением пароля
     */
    private void switchMode()
    {
        if (revealPasswordMode)
        {
            String textBoxVal = (Strings.isNullOrEmpty(value) || passwordTextBox.getValue().isEmpty()) ? ""
                    : EIGHT_BULLETS_STR;
            changeVisibility(passwordTextBox, textBoxWidget, textBoxVal);
            textBoxWidget.setFocus(true);
        }
        else
        {
            String passwordBoxVal = textBoxWidget.getValue().isEmpty() ? "" : EIGHT_BULLETS_STR;
            changeVisibility(textBoxWidget, passwordTextBox, passwordBoxVal);
        }
    }

    private void changeVisibility(TextBoxWidget widgetHide, TextBoxWidget widgetShow, String value)
    {
        widgetShow.setValue(value, false);
        widgetHide.setVisible(false);
        widgetShow.setVisible(true);
    }

    @Override
    public void setEnabled(boolean enabled)
    {
    }

    public void setMaxLength(int length)
    {
        textBoxWidget.setMaxLength(length);
        passwordTextBox.setMaxLength(length);
    }
}
