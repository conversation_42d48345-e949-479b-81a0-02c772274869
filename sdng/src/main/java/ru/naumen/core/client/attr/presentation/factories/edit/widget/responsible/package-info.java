/**
 * {@link ru.naumen.core.shared.dto.DtObject} с UUID'ом ответственного.
 * 
 * <p>Реализация фабрики, которая создает из DtObject-запроса новый DtObject с UUID'ом ответственного, записаного в формате DtObject,
 * приходящих с сервера в {@link ru.naumen.core.shared.dispatch.GetPossibleResponsibleActionBase}.
 */
@javax.annotation.ParametersAreNonnullByDefault
package ru.naumen.core.client.attr.presentation.factories.edit.widget.responsible;