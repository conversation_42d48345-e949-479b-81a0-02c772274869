package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import static ru.naumen.metainfo.shared.Constants.PARAMETER_CODE;
import static ru.naumen.metainfo.shared.Constants.REPORT_PARAMETERS;
import static ru.naumen.metainfo.shared.Constants.REPORT_PARAMETERS_SCRIPT;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Singleton;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;
import com.google.inject.Provider;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.events.FilterFieldEvent;
import ru.naumen.core.client.events.FilterFieldEventHandler;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.shared.dispatch.GetPossibleStatesByScriptAction;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.AbstractDtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.metainfo.shared.elements.wf.StateLite;
import ru.naumen.metainfo.shared.elements.wf.WorkflowLite;
import ru.naumen.reports.client.common.ReportContext;
import ru.naumen.reports.client.common.ReportParametersHelper;

/**
 * Фабрика для мультиселект виджета выбора статусов
 *
 * <AUTHOR>
 * @since Oct 7, 2013
 */
@Singleton
public class AttributeStateMultiSelectWidgetFactoryImpl extends AttributeWidgetFactoryEditImpl<Collection<SelectItem>>
{
    private static class SelectListFilterHandler implements FilterFieldEventHandler
    {
        private final PresentationContext prsContext;
        private final MultiSelectCellList<StateLite> widget;
        private final List<State> statesItems;

        public SelectListFilterHandler(PresentationContext prsContext, MultiSelectCellList<StateLite> widget,
                List<State> statesItems)
        {
            this.prsContext = prsContext;
            this.widget = widget;
            this.statesItems = statesItems;
        }

        @SuppressWarnings({ "rawtypes", "unchecked" })
        @Override
        public void onFilterField(FilterFieldEvent e)
        {
            if (!Objects.equals(prsContext.getAttributeType().getProperty(PARAMETER_CODE), e.getAttr()))
            {
                return;
            }
            Collection<SelectItem> initialValue = widget.getValue();
            widget.clearValue();
            Set<String> values = initialValue.stream().map(AbstractDtObject::getUUID)
                    .filter(code -> statesItems.stream().anyMatch(s -> s.getCode().equals(code)))
                    .collect(Collectors.toSet());
            widget.setObjValue((Collection)values, false);
            ReportParametersHelper.updateParameterValueInContext(prsContext, values);
        }
    }

    @Inject
    private Provider<SimpleSelectCellListBuilder<StateLite>> widgetBuilderProvider;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    public HasValueOrThrow<Collection<SelectItem>> create(PresentationContext context)
    {
        return null;
    }

    @Override
    public void create(final PresentationContext context,
            final AsyncCallback<HasValueOrThrow<Collection<SelectItem>>> callback)
    {
        Attribute attribute = context.getAttribute();
        Context parentContext = context.getParentContext();
        AttributeType attrType = context.getAttributeType();
        if (parentContext instanceof ReportContext)
        {
            if (attrType.hasProperty(REPORT_PARAMETERS_SCRIPT))
            {
                GetPossibleStatesByScriptAction getByScriptAction = new GetPossibleStatesByScriptAction(
                        Sets.newHashSet(context.getObject()), context.getInitialValue());
                attrType.setProperty(REPORT_PARAMETERS, ((ReportContext)parentContext).getParameters());
                getByScriptAction.setAttributeType(attrType);
                dispatch.execute(getByScriptAction,
                        new BasicCallback<SimpleResult<Map<String, List<State>>>>(parentContext.getReadyState())
                        {
                            @Override
                            protected void handleSuccess(SimpleResult<Map<String, List<State>>> response)
                            {
                                List<State> states = response.get().values().iterator().next();
                                callback.onSuccess(createWidget(null, context, states));
                            }
                        });
            }
            else
            {
                String metaClassFqn = context.getAttributeType().getProperty(ObjectAttributeType.METACLASS_FQN);
                metainfoService.getMetaClass(ClassFqn.parse(Objects.requireNonNull(metaClassFqn)),
                        new BasicCallback<MetaClass>()
                        {
                            @Override
                            protected void handleSuccess(MetaClass metaClass)
                            {
                                callback.onSuccess(createWidget(metaClass, context, null));
                            }
                        });
            }
        }
        else
        {
            callback.onSuccess(createWidget(Objects.requireNonNull(attribute).getMetaClassLite(), context, null));
        }
    }

    private MultiSelectCellList<StateLite> createWidget(@Nullable MetaClassLite metaClass,
            PresentationContext context, @Nullable List<State> states)
    {
        SimpleSelectCellListBuilder<StateLite> builder = widgetBuilderProvider.get().setMultiSelect(true);
        if (context.isSelectAnyOption())
        {
            builder.withAnyOption();
        }
        MultiSelectCellList<StateLite> widget = builder.build();
        if (context.isSelectAnyOption())
        {
            widget.setHasAnyOption(true);
        }
        if (metaClass != null)
        {
            WorkflowLite workflow = metaClass.getWorkflowLite();
            if (workflow == null && metaClass instanceof MetaClass)
            {
                workflow = ((MetaClass)metaClass).getWorkflow();
            }
            widget.addItems(Lists.newArrayList(Objects.requireNonNull(workflow).getStates()));
        }
        else
        {
            widget.addHandler(new SelectListFilterHandler(context, widget, Objects.requireNonNull(states)),
                    FilterFieldEvent.getType());
            widget.addItems(Lists.newArrayList(states));
        }
        return widget;
    }
}
