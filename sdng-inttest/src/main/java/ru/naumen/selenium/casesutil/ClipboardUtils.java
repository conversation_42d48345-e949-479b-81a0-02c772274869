package ru.naumen.selenium.casesutil;

import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.StringSelection;
import java.io.File;

import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.util.FileUtils;

/**
 * Класс для работы с clipboard. Содержит методы для работы с удаленным буфером обмена через {@link RemoteWebDriver}
 *
 * <AUTHOR>
 * @since 05.08.19
 */
public class ClipboardUtils extends CoreTester
{
    private static final String CLIPBOARD_BUTTON_ID = "button-for-clipboard";
    private static final String CLIPBOARD_TEXT_AREA_ID = "textarea-for-clipboard";

    //@formatter:off
    private static final String COPY_IMAGE_JS =
                "var f = function copy() {\n"
                + "    var elem = document.querySelector('#div-for-clipboard');\n"
                + "    var selection = window.getSelection();\n"
                + "    var range = document.createRange();\n"
                + "    range.selectNodeContents(elem);\n"
                + "    selection.removeAllRanges();\n"
                + "    selection.addRange(range);\n"
                + "    document.execCommand('copy');\n"
                + "}\n"
                + "\n"
                + "var div = document.createElement('div');\n"
                + "div.setAttribute('id', 'div-for-clipboard');\n"
                + "var img = document.createElement('img');\n"
                + "img.setAttribute('id', 'image-for-clipboard');\n"
                + "img.setAttribute('src', '%s');\n"
                + "div.appendChild(img);\n"
                + "document.body.insertBefore(div, document.body.firstChild);\n"
                + "\n"
                + "var button = document.createElement('button');\n"
                + "button.setAttribute('id', '%s');\n"
                + "button.addEventListener('click', f);\n"
                + "document.body.insertBefore(button, document.body.firstChild);\n";
    //@formatter:on

    //@formatter:off
    private static final String COPY_TEXT_JS =
                "var f = function copy() {\n"
                + "    var elem = document.querySelector('#textarea-for-clipboard');\n"
                + "    elem.select();\n"
                + "    document.execCommand('copy');\n"
                + "}\n"
                + "\n"
                + "var textArea = document.createElement('textarea');\n"
                + "textArea.setAttribute('id', 'textarea-for-clipboard');\n"
                + "textArea.value = `%s`;\n"
                + "document.body.insertBefore(textArea, document.body.firstChild);\n"
                + "\n"
                + "var button = document.createElement('button');\n"
                + "button.setAttribute('id', '%s');\n"
                + "document.body.insertBefore(button, document.body.firstChild);\n"
                + "button.addEventListener('click', f);\n";
    //@formatter:on

    //@formatter:off
    private static final String CREATE_TEXT_AREA_JS =
                "var textArea = document.createElement('textarea');\n"
                + "textArea.setAttribute('id', '%s');\n"
                + "document.body.insertBefore(textArea, document.body.firstChild);\n";
    //@formatter:on

    /**
     * Копирует текст из параметра checkText в буфер обмена. Не работает с {@link RemoteWebDriver}.
     * Предпочтительней использовать {@link #copyToRemoteClipboard(String)}
     * @param checkText текст для буфера обмена
     */
    @Deprecated
    public static void copyToClipboard(String checkText)
    {
        StringSelection stringSelection = new StringSelection(checkText);
        Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
        clipboard.setContents(stringSelection, null);
    }

    /**
     * Копирует изображение в буфер обмена. Работает с {@link RemoteWebDriver}
     * @param path путь в файлу с изображением
     */
    public static void copyImageToRemoteClipboard(String path)
    {
        String src = "data:image/png;base64," + FileUtils.encodeBase64String(new File(path));
        tester.doInNewTab(() ->
        {
            tester.runJavaScript(String.format(COPY_IMAGE_JS, src, CLIPBOARD_BUTTON_ID));
            tester.getWebDriver().findElement(By.id(CLIPBOARD_BUTTON_ID)).click();
        });
    }

    /**
     * Копирует текст в буфер обмена. Работает с {@link RemoteWebDriver}
     * @param text текст для буфера обмена
     */
    public static void copyToRemoteClipboard(String text)
    {
        tester.doInNewTab(() ->
        {
            tester.runJavaScript(String.format(COPY_TEXT_JS, text, CLIPBOARD_BUTTON_ID));
            tester.getWebDriver().findElement(By.id(CLIPBOARD_BUTTON_ID)).click();
        });
    }

    /**
     * Получить текст из буфера обмена. Работает с {@link RemoteWebDriver}
     * @return текст из буфера
     */
    public static String getTextFromRemoteClipboard()
    {
        return tester.doInNewTab(() ->
        {
            tester.runJavaScript(String.format(CREATE_TEXT_AREA_JS, CLIPBOARD_TEXT_AREA_ID));
            WaitTool.waitMills(250L);
            String xpath = String.format("//*[@id='%s']", CLIPBOARD_TEXT_AREA_ID);
            tester.click(xpath);
            tester.actives().pressingPasteKey();
            return GUITester.getValue(xpath);
        });
    }
}
