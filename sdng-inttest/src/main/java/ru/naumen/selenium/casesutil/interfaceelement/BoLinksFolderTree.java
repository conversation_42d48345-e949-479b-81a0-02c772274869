package ru.naumen.selenium.casesutil.interfaceelement;

import java.util.Arrays;

import org.junit.Assert;

/**
 * Общие методы для работы с выпадающими списками типа дерево в атрибутах типа "Набор ссылок на БО".
 * Представляет собой простейшую реализацию {@link CoreTree}.
 * Не содержит логики вычисления id текстовых элементов узлов (их названий)
 * <AUTHOR>
 * @since 20.05.2013
 */
public class BoLinksFolderTree extends CoreTree
{
    /**
     * Конструктор
     * @param treeId id поля, являющегося выпадающим списком
     */
    public BoLinksFolderTree(String treeId)
    {
        super(treeId);
    }

    public BoLinksFolderTree(String treeIdOrXpath, Object... args)
    {
        super(treeIdOrXpath, args);
    }

    /**
     * Проверить предка у элемента дерева
     * (Если в наборе один элемент, то будет проверяться, является ли он корневым)
     * @param nodes набор id родителей до элемента, включая сам узел, в котором проверяется родитель
     */
    @Override
    public void assertParentOfElement(String... nodes)
    {
        if (nodes.length > 1)
        {
            String message = String.format("Элемент %s не является предком.", nodes[nodes.length - 2]);
            String parentId = getHierchyId(Arrays.copyOf(nodes, nodes.length - 1));
            Assert.assertTrue(message,
                    tester.waitAppear(getXNode(nodes) + "//ancestor::div[4]//span[@id='%s']", parentId));
        }
        else
        {
            assertIsRootElement(nodes[0]);
        }
    }

    @Override
    public String getXNode(String... nodes)
    {
        StringBuilder buffer = new StringBuilder(X_VALUE_TREE);
        int level = 1;
        String currentNodeId = "";
        for (String node : nodes)
        {
            currentNodeId += currentNodeId.isEmpty() ? node : "." + node;
            buffer.append(String.format(X_TREE_ITEM, currentNodeId, level++));
        }
        return buffer.toString();
    }
}
