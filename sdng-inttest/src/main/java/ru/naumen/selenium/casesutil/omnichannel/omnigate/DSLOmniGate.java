package ru.naumen.selenium.casesutil.omnichannel.omnigate;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.model.omnichannel.OmniGateConnectionSettings;
import ru.naumen.selenium.casesutil.omnichannel.DSLOmniGateConnectionSettings;
import ru.naumen.selenium.casesutil.omnichannel.OmnichannelConfiguration;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.WaitTool;

/**
 * Утилитарные методы для работы со шлюзом OmniGate
 * <AUTHOR>
 * @since 30.01.2024
 */
public class DSLOmniGate
{
    private static final long WAITING_CONNECT_TIMEOUT_IN_SECONDS = 30L;

    /**
     * Включает/отключает интеграцию с настройками по умолчанию
     * @param enabled true-включить, false-выключить
     */
    public static void enable(boolean enabled)
    {
        OmniGateConnectionSettings settings = new OmniGateConnectionSettings(
                OmnichannelConfiguration.getOmniGateServerAddress(), enabled, true);
        DSLOmniGateConnectionSettings.edit(settings);
    }

    /**
     * Выполняет ожидание подключения к шлюзу в течение {@link DSLOmniGate#WAITING_CONNECT_TIMEOUT_IN_SECONDS}
     */
    public static void waitForConnected()
    {
        String script = "beanFactory.getBean('omniGateConnectionServiceImpl').isActive()";
        Boolean connected = WaitTool.waitSomething(new ScriptRunner(script), WAITING_CONNECT_TIMEOUT_IN_SECONDS, 3000L,
                runner -> Boolean.parseBoolean(runner.runScript().getFirst()));
        Assert.assertTrue("Подключение к шине не установлено", connected != null && connected);
    }
}
