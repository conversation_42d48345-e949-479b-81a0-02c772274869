package ru.naumen.selenium.casesutil;

import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.util.FileUtils;

/**
 * Класс для хранения констант, необходимых для формирования xpath-ов.
 * <AUTHOR>
 */
public class GUIXpath
{
    /**
     * Класс для хранения xpath-ов, которые начинаются на префикс '//a'
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class A
    {
        public static final String A_PREFIX = "//a";
        public static final String ID_PATTERN = "//a[@id='%s']";
        public static final String DEBUG_ID_PATTERN = "//a[@id='gwt-debug-%s']";
        public static final String CODE_PATTERN = "//a[@_code='%s']";
        public static final String AGREEMENT = String.format(DEBUG_ID_PATTERN, "agreement");
        public static final String CONTAINS_TEXT_PATTERN = "//a[contains(text(),'%s')]";
        public static final String HREF = "//a[@href]";
        public static final String HREF_AND_TEXT = "//a[@href and text() = '%s']";
        public static final String LOGO = String.format(DEBUG_ID_PATTERN, "Logo");
        public static final String LOGOUT = String.format(DEBUG_ID_PATTERN, "logout");
        public static final String SC = String.format(DEBUG_ID_PATTERN, "sc");
        public static final String SERVICE = String.format(DEBUG_ID_PATTERN, "service");
        public static final String TEXT_PATTERN = "//a[text()='%s']";
        public static final String TITLE = String.format(DEBUG_ID_PATTERN, "title");
        public static final String X_UUID = "//a[@href='#uuid:%s']";
        public static final String HREF_CONTAINS = "//a[contains(@href, '%s')]";
    }

    /**
     * Класс для хранения xpath-ов, которые начинаются на префикс '//*'
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class Any
    {
        public static final String ANY_TITLE = "//*[@title='%s']";
        public static final String ANY_TEXT_CONTAINS = "//*[contains(text(),'%s')]";
        public static final String ADD_CONTAINS = "//*[contains(@id,'gwt-debug-add.')]";
        public static final String ANY_CLASS_CONTAINS = "//*[contains(@class,'%s')]";
        public static final String ANY_ID_CONTAINS = "//*[contains(@id,'%s')]";
        public static final String ADMIN_SWITCH_ICON = "//*[@id='gwt-debug-switchInterface' and contains(@href, "
                                                       + "'operator')]";
        public static final String AGREEMENTS_FILTRATION_SCRIPT_VALUE = "//*[@id='gwt-debug"
                                                                        + "-agreementsFiltrationScript-value']";
        public static final String ALIAS_CAPTION = "//*[@id='gwt-debug-alias-caption']";
        public static final String ALIAS_VALUE = "//*[@id='gwt-debug-alias-value']";
        public static final String ALLOW_HIDE_VALUE_INPUT = "//*[@id='gwt-debug-allow-hide-value-input']";
        public static final String ALLOW_HIDE_VALUE_LABEL = "//*[@id='gwt-debug-allow-hide-value-label']";
        public static final String ANCESTOR_CLASS_PATTERN = "/ancestor::*[contains(@class, '%s')]";
        public static final String ANY = "//*[@id='gwt-debug-%s']";
        public static final String ANY_ANYSCRIPT = "//*[@id='gwt-debug-%s:%sscript']";
        public static final String ANY_ANY_INPUT = "//*[@id='gwt-debug-%s:%s-input']";
        public static final String ANY_CAPTION = "//*[@id='gwt-debug-%s-caption']";
        public static final String ANY_CAPTION_AND_TEXT = "//div[@id='gwt-debug-%s-caption' and text()='%s']";
        public static final String ANY_CONTAINS = "//*[contains(@id,'gwt-debug-%s')]";
        public static final String ANY_RULES_SETTINGS_COLOR = "//*[@id='%s.rulesSettings.color']";
        public static final String ANY_VALUE = "//*[@id='gwt-debug-%s-value']";
        public static final String ANY_VALUE_CONTAINS = "//*[contains(@id,'gwt-debug-%s') and contains(@id, '-value')]";
        public static final String ANY_VALUE_AND_ANY_TEXT = "//*[@id='gwt-debug-%s-value' and text() = '%s']";

        public static final String FILE_VALUE_AND_BADGE_TEXT =
                "//*[@id='gwt-debug-%s-value']" + A.HREF_CONTAINS + Span.CONTAINS_TEXT;
        public static final String ANY_VALUE_AND_BADGE_TEXT = "//*[@id='gwt-debug-%s-value']" + A.X_UUID
                                                              + Span.CONTAINS_TEXT;

        public static final String ANY_VALUE_AND_TEXT = "//*[@id='gwt-debug-%s-value' and text()]";
        public static final String ANY_VALUE_AND_TEXT_VALUE = "//*[@id='gwt-debug-%s-value']/*[text() = '%s']";
        public static final String ANY_VALUE_INPUT = "//*[@id='gwt-debug-%s-value-input']";
        public static final String ANY_CLASS = "//*[@class='%s']";

        public static final String ANY_VALUE_INPUT_CONTAINS = "//*[contains(@id,'gwt-debug-%s') and contains(@id, "
                                                              + "'-value-input')]";
        public static final String ANY_EDITABLE_VALUE_INPUT_CONTAINS = "//*[contains(@id,'gwt-debug-editable-') and "
                                                                       + "contains(@id, '-value-input')]";
        public static final String ANY_VALUE_LABEL = "//*[@id='gwt-debug-%s-value-label']";
        public static final String ANY_VALUE_LABEL_CONTAINS = "//*[contains(@id,'gwt-debug-%s') and contains(@id,"
                                                              + "'-value-label')]";
        public static final String APPLY_BUTTON_CODE = "apply";
        public static final String APPLY_BUTTON = String.format(ANY, Any.APPLY_BUTTON_CODE);
        public static final String CONTINUE_BUTTON = String.format(ANY, "continue");

        //TODOAT заменить на GUIAttention
        public static final String ATTENTION = "//*[@id='gwt-debug-attention']";
        public static final String INFO_MESSAGE = "//*[@id='gwt-debug-infoMessage']";
        public static final String ERROR_MESSAGE = String.format(ANY, "errorMessage");

        public static final String ATTRIBUTE_GROUP_CAPTION = "//*[@id='gwt-debug-attributeGroup-caption']";
        public static final String ATTRIBUTE_GROUP_VALUE = "//*[@id='gwt-debug-attributeGroup-value']";
        public static final String ATTRIBUTE_INFO_MODAL_FORM_CAPTION = "//*[@id='gwt-debug-attributeInfoModalForm"
                                                                       + "-caption']";
        public static final String ATTRIBUTE_INFO_MODAL_FORM = "//*[@id='gwt-debug-attributeInfoModalForm']";
        public static final String ATTRIBUTE_SELECT_VALUE = "//*[@id='gwt-debug-attributeSelect-value']";
        public static final String A_USER_ELEMENT_CONTROL = "//*[contains(@id, 'gwt-debug-fireUserEvent')]";
        public static final String A_USER_ELEMENT_CONTROL_CONTAINS = A_USER_ELEMENT_CONTROL + Div.TEXT_ANY;
        public static final String BUTTON_TOOLBAR = "//*[@id='gwt-debug-toolBar']";
        public static final String CAPTION_AND_CONTAINS_TEXT_AGREEMENT = "//*[@id='gwt-debug-caption' and contains"
                                                                         + "(text(), 'Соглашение/Услуга')]";
        public static final String CAPTION_CASE_CONTAINS = "//*[@id='gwt-debug-caption' and contains(text(), 'Тип "
                                                           + "объекта')]";
        public static final String CAPTION_HOLDER = "//*[@id=\"gwt-debug-captionHolder\"]";
        public static final String CAPTION_VALUE = "//*[@id='gwt-debug-caption-value']";
        public static final String CASE_PROPERTY_VALUE = "//*[@id='gwt-debug-caseProperty-value']";
        public static final String CLASS_VALUE = "//*[@id='gwt-debug-class-value']";
        public static final String CLEAR_BUTTON_CONTAINS = "//*[contains(@id,'gwt-debug-clearButton')]";
        public static final String CLIENT_VALUE = "//*[@id='gwt-debug-client-value']";
        public static final String CODE_VALUE = "//*[@id='gwt-debug-code-value']";
        public static final String COLLAPSED_VALUE_LABEL = "//*[@id='gwt-debug-collapsed-value-label']";
        public static final String COMPLEX_ATTR_GROUP_CAPTION = "//*[@id='gwt-debug-complexAttrGroup-caption']";
        public static final String COMPLEX_EMPLOYEE_ATTR_GROUP_CAPTION = "//*[@id='gwt-debug-complex-employee"
                                                                         + "-attrGroup-caption']";
        public static final String COMPLEX_EMPLOYEE_ATTR_GROUP_VALUE = "//*[@id='gwt-debug-complex-employee-attrGroup"
                                                                       + "-value']";
        public static final String COMPUTABLE_VALUE = "//*[@id='gwt-debug-computable-value']";
        public static final String DEBUG_CONTENT = "//*[@id='gwt-debug-content']";
        public static final String COMPUTABLE_VALUE_ON_EDIT_VALUE_INPUT = "//*[@id='gwt-debug-computeValueOnEdit"
                                                                          + "-value-input']";
        public static final String COMPUTABLE_VALUE_ON_EDIT_VALUE_LABEL = "//*[@id='gwt-debug-computeValueOnEdit"
                                                                          + "-value-label']";
        public static final String COMPUTE_ANY_CATALOG_ELEMENTS_SCRIPT_CAPTION = "//*[@id='gwt-debug"
                                                                                 + "-computeAnyCatalogElementsScript"
                                                                                 + "-caption']";

        public static final String CONDITION = "//*[@id='gwt-debug-condition']";
        public static final String CONDITION_VALUE = "//*[@id='gwt-debug-condition-value']";
        public static final String CONTEXT_TYPES_CAPTION = "//*[@id='gwt-debug-contextTypes-caption']";
        public static final String CONTEXT_TYPES_VALUE = "//*[@id='gwt-debug-contextTypes-value']";
        public static final String CURRENT_PASSWORD = String.format(ANY_VALUE, "current-password");
        public static final String DATE_PICKER_IMAGE = "//*[@id='gwt-debug-datePickerImage']";
        public static final String DATE_TIME_INTERVAL = "//*[@id='gwt-debug-dateTimeInterval']";
        public static final String DATE_TIME_VALIDATION_SCRIPT_VALUE = String.format(ANY_VALUE,
                AttributeConstant.DateTimeType.DATETIME_RESTRICTION_SCRIPT);
        public static final String DEBUG_VALUE = "//*[@id='gwt-debug-value']";
        public static final String DEFAULT_BY_SCRIPT_VALUE = "//*[@id='gwt-debug-defaultByScript-value']";
        public static final String DEFAULT_BY_SCRIPT_VALUE_INPUT = "//*[@id='gwt-debug-defaultByScript-value-input']";
        public static final String DEFAULT_BY_SCRIPT_VALUE_LABEL = "//*[@id='gwt-debug-defaultByScript-value-label']";
        public static final String EDIT_ON_COMPLEX_FORM_ONLY_VALUE_INPUT = "//*[@id='gwt-debug-editOnComplexFormOnly"
                                                                           + "-value-input']";
        public static final String EDIT_ON_COMPLEX_FORM_ONLY_VALUE_LABEL = "//*[@id='gwt-debug-editOnComplexFormOnly"
                                                                           + "-value-label']";
        public static final String DEFAULT_VALUE_LABEL_CAPTION = "//*[@id='gwt-debug-defaultValueLabel-caption']";
        public static final String DEFAULT_VALUE_VALUE = "//*[@id='gwt-debug-defaultValue-value']";
        public static final String DESCRIPTION_AND_TEXT = "//*[@id='description' and text()='%s']";
        public static final String DESCRIPTION_VALUE = "//*[@id='gwt-debug-description-value']";
        public static final String DETERMINER_VALUE = "//*[@id='gwt-debug-determiner-value']";
        public static final String DIALOG_WIDGET_BODY = "//*[@id='gwt-debug-dialogWidgetBody']";
        public static final String DIALOG_WIDGET_DESCRIPTION_ELEMENT = "//*[@id='gwt-debug"
                                                                       + "-dialogWidgetDescriptionElement']";
        public static final String EDITABLE_VALUE = "//*[@id='gwt-debug-editable-value']";
        public static final String EDIT_CONFIG_BUTTON = String.format(ANY, "editConfigButton");
        public static final String EDIT_PROFILE = "//*[@id='gwt-debug-editProfile']";
        public static final String EMPTY_SELECTION_ITEM = "//*[@id='emptySelectionItem']";
        public static final String ENCRYPT_PROTO_VALUE = "//*[@id='gwt-debug-encryptProto-value']";
        public static final String END_DATE_VALUE = "//*[@id='gwt-debug-endDate-value']";
        public static final String EXPORT_INPUT_MASK = "//*[@id='gwt-debug-exportInputmask']";
        public static final String EXPORT_LICENSE = "//*[@id='gwt-debug-exportLicense']";
        public static final String EXPORT_METAINFO = "//*[@id='gwt-debug-exportMetainfo']";
        public static final String EXPORT_REPORT_TEMPLATES = "//*[@id='gwt-debug-exportReportTemplates']";
        public static final String EXPORT_STATISTICS = "//*[@id='gwt-debug-exportStatistics']";
        public static final String EXPORT_SYSTEM_INFO = "//*[@id='gwt-debug-exportSystemInfo']";
        public static final String EVENT_ACTION_EXCLUDE_AUTHOR_VALUE = "//*[@id='gwt-debug-excludeAuthor-value']";
        public static final String SKIP_IF_USER_HAS_ACTIVE_SESSION_VALUE =
                "//*[@id='gwt-debug-skipIfUserHasActiveSession-value']";
        public static final String EVENT_ACTION_USE_DEFAULT_MESSAGE_VALUE = "//*[@id='gwt-debug-useDefaultMessage"
                                                                            + "-value']";
        public static final String EVENT_CLEANER_JOB_SETTINGS = String.format(ANY, "eventCleanerJobSettings");
        public static final String EVENT_STORAGE_RULES = String.format(ANY, "eventStorageRuleListBlock");
        public static final String FAVORITES_NAVIGATION_TREE = "//*[@id='gwt-debug-favoritesNavigationTree']";
        public static final String FILTERED_BY_SCRIPT_VALUE = "//*[@id='gwt-debug-filteredByScript-value']";
        public static final String FORM_TITLE = "//*[@id='gwt-debug-formTitle']";
        public static final String HIDDEN_WHEN_EMPTY_VALUE = "//*[@id='gwt-debug-hiddenWhenEmpty-value']";
        public static final String HIDDEN_WHEN_NO_POSSIBLE_VALUES_VALUE = "//*[@id='gwt-debug"
                                                                          + "-hiddenWhenNoPossibleValues-value']";
        public static final String ID_PATTERN = "//*[@id='%s']";
        public static final String INFO = "//*[@id='gwt-debug-Info']";
        public static final String LOGS = "//*[@id='gwt-debug-logs']";
        public static final String INTERVAL_VALUE = "//*[@id='gwt-debug-interval-value']";
        public static final String LICENSE_VALUE = "//*[@id='gwt-debug-license-value']";
        public static final String LIST_FILTER_SCRIPT_VALUE = "//*[@id='gwt-debug-list_filter.script-value']";
        public static final String MENTION_ATTRIBUTE_CAPTION = "//*[@id='gwt-debug-mentionAttribute-caption']";
        public static final String MENTION_ATTRIBUTE_VALUE = "//*[@id='gwt-debug-mentionAttribute-value']";
        public static final String MENTION_TYPES_CAPTION = "//*[@id='gwt-debug-mentionTypes-caption']";
        public static final String MENTION_TYPES_VALUE = "//*[@id='gwt-debug-mentionTypes-value']";
        public static final String MOBILE_CLIP = "//*[@_code='mobile-clip']";
        public static final String MOBILE_COMMENT = "//*[@_code='mobile-comment']";
        public static final String MOBILE_CONTENT_CONTAINS = "//*[contains(@class, 'mobileContent')]";
        public static final String MOBILE_CONTENT_DOWN = "//*[@_code='mobileContentDown']";
        public static final String MOBILE_CONTENT_UP = "//*[@_code='mobileContentUp']";
        public static final String MOBILE_DELETE_CONTENT = "//*[@_code='mobileDeleteContent']";
        public static final String MOBILE_EDIT_CONTENT = "//*[@_code='mobileEditContent']";
        public static final String MOBILE_EDIT_CONTENT_ATTRIBUTES = "//*[@_code='mobileEditContentAttributes']";
        public static final String MOBILE_EDIT_TOOL_PANEL = "//*[@_code='mobileEditToolPanel']";
        public static final String MOBILE_CONTENT_ATTR_TITLE_ANY = "//*[contains(@id ,'gwt-debug-title.')]";
        public static final String MOBILE_CONTENT_CONTROL_ANY = "//*[contains(@id,'gwt-debug-mobile') and contains(@id,"
                                                                + "'%s')]";
        public static final String MOBILE_LIST_CONTENT_BASE_ANY = "//*[@id='gwt-debug-MobileListContentBase.%s']";
        public static final String MOBILE_EMBEDDED_APPLICATION_ANY = "//*[@id='gwt-debug-mobileEmbeddedAppContent.%s']";
        public static final String MOBILE_LIST_GROUP_CONTENT = "//*[@id='gwt-debug-mobileListsGroup.%s']";
        public static final String MOBILE_PROPERTIES_LIST_ANY = "//*[@id='gwt-debug-mobilePropertiesList.%s']";
        public static final String MOBILE_PROPERTIES_LIST_CONTAINS = "//*[contains(@id, "
                                                                     + "'gwt-debug-mobilePropertiesList.')]";
        public static final String NAVIGATION_TREE_INTERFACE = "//*[@id='gwt-debug-NavigationTree.interface:']";
        public static final String NEW_STATE_PROPERTY_VALUE = "//*[@id='gwt-debug-newStateProperty-value']";
        public static final String PERMITTED_TYPES_VALUE = "//*[@id='gwt-debug-permittedTypes-value']";
        public static final String POPUP_LIST_SELECT = "//*[@id='gwt-debug-PopupListSelect']";
        public static final String PROFILES_CAPTION = "//*[@id='gwt-debug-profiles-caption']";
        public static final String PROFILES_VALUE = "//*[@id='gwt-debug-profiles-value']";
        public static final String VERS_PROFILES_VALUE = "//*[@id='gwt-debug-versProfiles-value']";
        public static final String PROPERTY_LIST_CONTAINS = "//*[contains(@id, 'gwt-debug-PropertyList.')]";
        public static final String REL_OBJ_PROPERTY_LIST_CONTAINS = "//*[contains(@id, 'gwt-debug-RelObjPropertyList"
                                                                    + ".')]";
        public static final String QUESTION_DIALOG = "//*[@id='gwt-debug-questionDialog']";
        public static final String QUICK_ADD = "//*[@id='gwt-debug-quickAdd']";
        public static final String QUICK_EDIT = "//*[@id='gwt-debug-quickEdit']";
        public static final String RECIPIENT_AGREEMENTS_VALUE = "//*[@id='gwt-debug-recipientAgreements-value']";
        public static final String REQUIRED_CAPTION = "//*[@id='gwt-debug-required-caption']";
        public static final String REQUIRED_IN_INTERFACE_VALUE = "//*[@id='gwt-debug-requiredInInterface-value']";
        public static final String REQUIRED_VALUE = "//*[@id='gwt-debug-required-value']";
        public static final String RESOLUTION_TIME_VALUE = "//*[@id='gwt-debug-resolutionTime-value']";
        public static final String RESPONSIBLE_VALUE = "//*[@id='gwt-debug-responsible-value']";
        public static final String RESTRICT_CONDITION = "//*[@id='gwt-debug-restrictCondition-value']" + CONDITION;
        public static final String SCRIPT_FOR_DEFAULT_VALUE = "//*[@id='gwt-debug-scriptForDefault-value']";
        public static final String SCRIPT_FOR_FILTRATION_VALUE = "//*[@id='gwt-debug-scriptForFiltration-value']";
        public static final String SCRIPT_FOR_EVALUATE_VALUE = "//*[@id='gwt-debug-computeAnyCatalogElementsScript"
                                                               + "-value']";
        public static final String SCRIPT_PERMISSION_VALUE = "//*[@id='gwt-debug-scriptPermission-value']";
        public static final String SCRIPT_VALUE = "//*[@id='gwt-debug-script-value']";
        public static final String SHOW_ATTR_DESCRIPTION_FOR_ADD_FORM_VALUE = ANY_VALUE.formatted(
                "showAttrDescriptionOnAddForm");
        public static final String SHOW_ATTR_DESCRIPTION_FOR_EDIT_FORM_VALUE = ANY_VALUE.formatted(
                "showAttrDescriptionOnEditForm");
        public static final String SELECTED = "//*[@id='gwt-debug-selected']";
        public static final String ALL = "//*[@id='gwt-debug-all']";
        public static final String SETS = "//*[@id='gwt-debug-partialSets']";
        public static final String SELECT_VALUE = "//*[@id='gwt-debug-select-value']";
        public static final String SERVICES_FILTRATION_SCRIPT_VALUE = "//*[@id='gwt-debug-servicesFiltrationScript"
                                                                      + "-value']";
        public static final String SHOW_TITLE_VALUE_INPUT = "//*[@id='gwt-debug-show-title-value-input']";
        public static final String SHOW_TITLE_VALUE_LABEL = "//*[@id='gwt-debug-show-title-value-label']";
        public static final String SOURCE_ATTRS_VALUE = "//*[@id='gwt-debug-sourceAttrs-value']";
        public static final String STRATEGY_LIST = String.format(ANY_VALUE, "strategyList");
        public static final String STRATEGY_LIST_VALUE = "//*[@id='gwt-debug-strategyList-value']";
        public static final String SWITCH_TO_SYSTEM_QA_TILES = String.format(ANY, "switchToSystemQATiles");
        public static final String STATE_EDIT_ANY_VALUE = "//*[@id='gwt-debug-state-%s-value']";
        public static final String STATE_EDIT_ANY_VALUE_ID = "gwt-debug-state-%s-value";
        public static final String TARGET_METACLASS_CAPTION = "//*[@id='gwt-debug-targetMetaClass-caption']";
        public static final String TARGET_METACLASS_VALUE = "//*[@id='gwt-debug-targetMetaClass-value']";
        public static final String TEMPLATE_FILE_VALUE = "//*[@id='gwt-debug-templateFile-value']";
        public static final String TEXT = String.format(ANY, "text");
        public static final String TEXT_ATSCRIPT = "//*[text()='ATScript']";
        public static final String TEXT_CONTAINS_PATTERN = "//*[contains(text(), '%s')]";
        public static final String TEXT_CONTAINS_AND_ID_PATTERN = "//*[@id='gwt-debug-text' and contains(text(), "
                                                                  + "'%s')]";
        public static final String TEXT_PATTERN = "//*[text()='%s']";
        public static final String TIME_ALLOWANCE_TIMER_VALUE = "//*[@id='gwt-debug-timeAllowanceTimer-value']";
        public static final String TITLE = "//*[@id='gwt-debug-title']";
        public static final String TITLE_AND_TEXT = "//*[@id='title' and text()='%s']";
        public static final String TITLE_VALUE = "//*[@id='gwt-debug-title-value']";
        public static final String TYPE_VALUE = "//*[@id='gwt-debug-type-value']";
        public static final String UNIQUE_CAPTION = "//*[@id='gwt-debug-unique-caption']";
        public static final String UNIQUE_VALUE = "//*[@id='gwt-debug-unique-value']";
        public static final String UPLOADING_ICON = "//*[@class='svg-uploading-icon']";
        public static final String USER_AREA = String.format(ANY, "userArea");
        public static final String VALUE = "//*[@id='gwt-debug-value']";
        public static final String WF_STATE = "//*[@href='#wfState:%s@%s']";
        public static final String DATA_ROLE = "//*[@data-role='%s']";
        public static final String VALUE_ON_EDIT_SCRIPT_VALUE = "//*[@id='gwt-debug-valueOnEditScript-value']";
        public static final String XML_CONFIG_VALUE = String.format(ANY_VALUE, "XMLconfig");
        public static final String X_LAST_DATE_IN_DATE_PICKER_PATH =
                "(//div[contains(@class, 'datePickerDay') and text()"
                + "=%d])[last()]";
        public static final String X_DATE_PIKER_MONTH_PATH = "//div[@class='datePickerMonth' and contains(text(), "
                                                             + "'%s')]";
        public static final String COMPLEX_RELATION_CAPTION = "//*[@id='gwt-debug-complexRelation-caption']";
        public static final String CASE_LIST_VALUE = "//*[@id='gwt-debug-caseList-value']";
        public static final String PARENT_CODE = "//*[@id='gwt-debug-parent-value']";
        public static final String CODE_CAPTION = "//*[@id='gwt-debug-code-caption']";
        public static final String CODE_PATTERN = "//*[@__code='%s']";
        public static final String COMPLEX_ATTR_GROUP_VALUE = "//*[@id='gwt-debug-complexAttrGroup-value']";
        public static final String COMPLEX_RELATION_VALUE = "//*[@id='gwt-debug-complexRelation-value']";
        public static final String CONTAINER = "//*[@id='gwt-debug-container']";
        public static final String READ_ONLY_CLUSTER_SETTINGS = GUIXpath.divGwtDebugId("readOnlyClusterSettings");
        public static final String RESTORE_VALUES = String.format(ANY, "restoreValues");
        public static final String COLLAPSE_BY_DEFAULT_ID = "gwt-debug-collapseByDefault-value";
        public static final String URL_TEXT_AREA = "//textarea[@id='linkWidgetId']";
        public static final String IFRAME = "//iframe";
        public static final String MOBILE_OBJECT_CARD_CONTENTS_PRESENTER = "//*[@id='gwt-debug"
                                                                           + "-mobileObjectCardContentsPresenter']";
        public static final String REMOVED_OBJECT_LINK = "//*[contains(@class, 'removedObjectLink')]";
        public static final String EDITOR = "//*[@id='gwt-debug-editor-value']";
        public static final String DOCUMENTATION = "//*[@id='gwt-debug-documentation-value']";
        public static final String TITLE_CAPTION = "//*[@id='gwt-debug-title-caption']";
        public static final String FORM_LABEL_DISABLED_CAPTION_BY_CLASS = "//*[@class='formlabel-disabled-caption']";
        public static final String FORM_LABEL_DISABLED_VALUE_BY_CLASS = "//*[@class='formlabel-disabled-value']";
        public static final String DOCUMENTATION_CAPTION_VALUE = "//*[@id='gwt-debug-documentationCaption-value']";
        public static final String USER_CONTROL_ON_LIST_CONTAINS = "//*[contains(@id, 'gwt-debug-text') and contains"
                                                                   + "(text(), '%s')]";
        public static final String LIST_SORT = "//*[contains(@id,'gwt-debug-ListSort')]";
        public static final String ADMIN_PROFILES = ANY_VALUE.formatted("adminProfiles");
        private static final String AFTER_HIERARCHY_VALUE = "//*[@id='gwt-debug-afterHierarchy-value']";
        private static final String ANY_FILE_LOGO = "//*[@id='gwt-debug-%s_fileLogo']";
        public static final String FILE_LOGO_EMPTY = String.format(ANY_FILE_LOGO, FilterCondition.EMPTY);
        public static final String BLUE_THEME_FILE_LOGO = String.format(ANY_FILE_LOGO, Constant.BLUE_THEME);
        private static final String ANY_STANDART_LOGO = "//*[@id='gwt-debug-%s_standartLogo']";
        public static final String STANDART_LOGO_EMPTY = String.format(ANY_STANDART_LOGO, FilterCondition.EMPTY);
        public static final String BLUE_THEME_STANDART_LOGO = String.format(ANY_STANDART_LOGO, Constant.BLUE_THEME);
        private static final String ANY_UPLOAD_LOGO = "//*[@id='gwt-debug-%s_uploadLogo']";
        public static final String LOGIN_UPLOAD_LOGO = String.format(ANY_UPLOAD_LOGO, FilterCondition.EMPTY);
        public static final String BLUE_THEME_UPLOAD_LOGO = String.format(ANY_UPLOAD_LOGO, Constant.BLUE_THEME);
        private static final String ATTRIBUTE_CHAIN_VALUE = "//*[@id='gwt-debug-attributeChain-value']";
        private static final String DETAILED_ATTRIBUTE_GROUP_VALUE =
                "//*[@id='gwt-debug-detailedAttributeGroup-value']";
        private static final String FOR_ADD_ATTRIBUTE_GROUP_VALUE =
                "//*[@id='gwt-debug-forAddFormAttributeGroup-value']";
        private static final String FOR_EDIT_ATTRIBUTE_GROUP_VALUE =
                "//*[@id='gwt-debug-forEditFormAttributeGroup-value']";
        private static final String COMMENT_ATTRIBUTE_GROUP_VALUE =
                "//*[@id='gwt-debug-forEditFormAttributeGroup-value']";
        private static final String ATTRIBUTE_GROUP_LIST_VALUE = "//*[@id='gwt-debug-attributeGroup-value']";
        private static final String ATTR_TYPE_CAPTION = "//*[@id='gwt-debug-attrType-caption']";
        private static final String ATTR_TYPE_VALUE = "//*[@id='gwt-debug-attrType-value']";
        private static final String BEFORE_HIERARCHY_VALUE = "//*[@id='gwt-debug-beforeHierarchy-value']";
        private static final String CLASS_LIST_CAPTION = "//*[@id='gwt-debug-classList-caption']";
        private static final String CLASS_LIST_VALUE = "//*[@id='gwt-debug-classList-value']";
        private static final String CODE_CODE = "//*[@__code='code']";
        private static final String CODE_DESCRIPTION = "//*[@__code='description']";
        private static final String CODE_ENABLED = "//*[@__code='enabled']";
        private static final String CODE_END_STATE = "//*[@__code='endState']";
        private static final String CODE_RESPONSIBLE = "//*[@__code='responsible']";
        private static final String CODE_RESPONSIBLE_TYPE = "//*[@__code='responsibleType']";
        private static final String CODE_TITLE = "//*[@__code='title']";
        private static final String COMPLEX_ANY_ATTR_GROUP_VALUE = "//*[@id='gwt-debug-complex-%s-attrGroup-value']";
        public static final String STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE_VALUE =
                Div.ANY_VALUE.formatted(Attribute.STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE);
        private static final String DEFAULT_VALUE_VALUE_INPUT = "//*[@id='gwt-debug-defaultValue-value-input']";
        private static final String DELETE_CONTAINS = "//*[contains(@id, 'gwt-debug-delete')]";
        private static final String DETERMINABLE_VALUE = "//*[@id='gwt-debug-determinable-value']";
        private static final String EDITABLE_IN_LISTS_VALUE = "//*[@id='gwt-debug-editableInLists-value']";
        private static final String EDIT_PRESENTATION_VALUE = "//*[@id='gwt-debug-editPresentation-value']";
        private static final String EMBEDDED_APPLICATION_VALUE = "//*[@id='gwt-debug-embeddedApplication-value']";
        private static final String EXPORT_NDAP_VALUE = "//*[@id='gwt-debug-exportNDAP-value']";
        private static final String RELATED_ATTRS_TO_EXPORT = "//*[@id='gwt-debug-relatedAttrsToExport-value']";
        private static final String HIERARCHY_ATTRIBUTE_VALUE = "//*[@id='gwt-debug-hierarchyAttribute-value']";
        private static final String HIERARCHY_CLASS_CAPTION = "//*[@id='gwt-debug-hierarchyClass-caption']";
        public static final String ICON_VALUE = "//*[@id='gwt-debug-icon-value']";
        private static final String INHERIT_VALUE = "//*[@id='gwt-debug-inherit-value']";
        private static final String INTERVAL_AVAILABLE_UNITS_VALUE = "//*[@id='gwt-debug-intervalAvailableUnits-value"
                                                                     + "']";
        private static final String LEVEL_VALUE = "//*[@id='gwt-debug-level-value']";
        private static final String MAINTENANCE_SETTINGS = GUIXpath.divGwtDebugId("maintenance");
        private static final String SCHEMA_OPTIMIZATION_PROCESS = GUIXpath.divGwtDebugId("schemaOptimizationProcess");
        private static final String PARENT_VALUE = "//*[@id='gwt-debug-parent-value']";
        private static final String PERIOD_VALUE = "//*[@id='gwt-debug-period-value']";
        private static final String RELATED_OBJECT_ATTRIBUTE_VALUE = "//*[@id='gwt-debug-relatedObjectAttribute-value"
                                                                     + "']";
        private static final String ROW_PTRN = "//*[@id='Row-%s']";
        private static final String SELECT_ATTRIBUTE_GROUP_VALUE = "//*[@id='gwt-debug-selectAttributeGroup-value']";
        public static final String SELECT_ATTRIBUTE_GROUP_VALUE_INPUT = SELECT_ATTRIBUTE_GROUP_VALUE
                                                                        + Input.INPUT_PREFIX;
        private static final String SELECT_PARENT_PROPERTY_VALUE = "//*[@id='gwt-debug-selectParentProperty-value']";
        private static final String SERVICES_VALUE = "//*[@id='gwt-debug-services-value']";
        private static final String SHOW_CAPTION_VALUE = "//*[@id='gwt-debug-showCaption-value']";
        private static final String COLLAPSE_BY_DEFAULT_VALUE = "//*[@id='" + COLLAPSE_BY_DEFAULT_ID + "']";
        private static final String SHOW_NESTED_IN_NESTED_VALUE = "//*[@id='gwt-debug-showNestedInNested-value']";
        private static final String SHOW_PRESENTATION_VALUE = "//*[@id='gwt-debug-showPresentation-value']";
        private static final String SHOW_RELATED_WITH_NESTED_VALUE = "//*[@id='gwt-debug-showRelatedWithNested-value']";
        private static final String SUGGEST_CATALOG_VALUE = "//*[@id='gwt-debug-suggestCatalog-value']";
        private static final String TARGET_CATALOG_CAPTION = "//*[@id='gwt-debug-targetCatalog-caption']";
        private static final String TEMPLATES_LIST_VALUE = "//*[@id='gwt-debug-templatesList-value']";
        private static final String TIMER_VALUE = "//*[@id='gwt-debug-timer-value']";
        private static final String TREE = "//*[@id='gwt-debug-tree']";
    }

    /**
     * Класс для хранения xpath-ов, которые составлены из констант из других классов
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class Complex
    {
        public static final String ANY_BOOLEAN_VALUE = Any.ANY_VALUE + Span.SPAN_PREFIX;
        public static final String ANY_DISABLED_VALUE = Any.ANY_CAPTION + Span.SPAN_PREFIX;
        public static final String ANY_SELECT_FORMTAG = Div.FORMTAGS + Div.FORMTAG_CONTAINS;
        public static final String ANY_VALUE_ON_DIALOG = Div.FORM + Any.ANY_VALUE;
        public static final String ANY_VALUE_ON_DIALOG_CONTAINS = Div.FORM + Any.ANY_VALUE_CONTAINS;
        public static final String ANY_VALUE_ON_MODAL_FORM_INPUT = SpecificComplex.ANY_VALUE_ON_MODAL_FORM
                                                                   + Input.INPUT_PREFIX;
        public static final String ARCHIVED_ICON = Div.HEADER_TITLE + "[@_code='remove']";
        public static final String ARCHIVE_ICON_IN_ROW = Any.ROW_PTRN + Span.REMOVE_ICON;
        public static final String ATTENTION_BUTTON = Any.ATTENTION + Div.FEATURE_PANEL;
        public static final String CAPTION = Div.CAPTION + Span.SPAN_PREFIX;
        public static final String CLOSE_ATTENTION = Any.ATTENTION + Span.CLOSE_ICON;
        public static final String CODE_RESPONSIBLE_INPUT = Any.CODE_RESPONSIBLE + InputComplex.VALUE;
        public static final String CODE_RESPONSIBLE_TYPE_INPUT = Any.CODE_RESPONSIBLE_TYPE + InputComplex.VALUE;
        public static final String COMPUTABLE_VALUE_LABEL = Any.COMPUTABLE_VALUE + Other.LABLE_PREFIX;
        public static final String CONTACT_FACE_SELECT_INPUT = String.format(InputComplex.ANY_VALUE,
                Constant.CONTACT_FACE_CODE);
        /** Для редактирования вкладок */
        public static final String CONTAINER_ON_FORM = Div.FORM_CONTAINS + Any.CONTAINER;
        /** Поле отображающее название компании в типе контента "Параметры объекта." */
        public static final String CONTENT_TITLE_VALUE = Div.ID_PATTERN + Div.TITLE_VALUE;
        public static final String CONTINUE_WITHOUT_SAVING_FORM = Any.QUESTION_DIALOG + Div.CONTINUE_WITHOUT_SAVING;
        /** Поле "Родительский тип" на форме копирования типа */
        public static final String COPY_FORM_PARENT_TYPE = Div.PARENT_TYPE_CAPTION + Span.SPAN_PREFIX;
        public static final String DEFAULT_VALUE_VALUE_UNITS_FOR_TIME_INTERVAL = Any.DEFAULT_VALUE_VALUE
                                                                                 + Div.DATE_TIME_INTERVAL
                                                                                 + Input.INPUT_PREFIX;
        public static final String DEL2_ICON_IN_ROW = Any.ROW_PTRN + Span.DELETE_ICON;
        public static final String EDITABLE_CHECKBOX_LABEL = Any.EDITABLE_VALUE + Other.LABLE_PREFIX;
        public static final String EDITABLE_IN_LISTS_VALUE_LABEL = Any.EDITABLE_IN_LISTS_VALUE + Other.LABLE_PREFIX;
        public static final String EDITABLE_VALUE_LABEL = Any.EDITABLE_VALUE + Other.LABLE_PREFIX;
        public static final String EDIT_BUTTON_IN_LIST = SpecificComplex.CONNECTION_CELL + Span.EDIT_ICON;
        public static final String EDIT_ICON_IN_ROW = Any.ROW_PTRN + Span.EDIT_ICON;
        public static final String EDIT_PROPERTIES = Div.ID_PATTERN + Div.CONTAINS_EDIT_PROPERTIES;
        /** Кнопка Включения/Выключения статуса */
        public static final String ENABLE_DISABLE_BTN = Div.SWITCH + Div.TEXT_ANY;
        /** Работа с файлами в контенте Список файлов */
        public static final String FILE_NAME_PATTERN = Div.ID_PATTERN + Any.TEXT_PATTERN;
        public static final String FILTER_CHECKBOX_LABEL = Any.FILTERED_BY_SCRIPT_VALUE + Other.LABLE_PREFIX;
        public static final String FORMTAGS_CARD = Div.FORMTAGS + Div.ANY;

        /**
         * Элемент списка выбора с временным айдишником
         */
        public static final String FORMTAGS_TEMP_ITEM_CARD = Div.FORMTAGS + Div.TEMP_ITEM_CARD;
        public static final String FORMTAG_WITH_TITLE = Div.FORMTAG_CONTAINS + Span.SPAN_TITLE_PATTERN;
        /** Поле ввода текста внутри диалогового окна */
        public static final String FORM_TEXTAREA_VALUE = Div.PROPERTY_DIALOG_BOX + Other.TEXTAREA;
        public static final String HIDDEN_WHEN_NO_POSSIBLE_VALUES_CHECKBOX_LABEL =
                Any.HIDDEN_WHEN_NO_POSSIBLE_VALUES_VALUE
                + Other.LABLE_PREFIX;
        public static final String INFO_DIALOG_TITLE = Div.INFO_DIALOG + Div.DIALOG_WIDGET_HEAD;
        public static final String INHERIT_VALUE_INPUT = PropertyDialogBoxContent.INHERIT_VALUE + Input.INPUT_PREFIX;
        public static final String LAST_DIALOG_BOX = Div.PROPERTY_DIALOG_BOX + Other.LAST_PATTERN;
        public static final String MAIL_LAST_RULE_VERSION_VALUE = Div.VALUE + Other.PREFIX;
        /** Константы для правил обработки в задачах планировщика. */
        public static final String MAIL_RULE_TITLE_VALUE = Div.TITLE_VALUE + Other.PREFIX;
        public static final String MAIL_RULE_VERSION_VALUE = Div.VERSION_VALUE + Other.PREFIX;
        public static final String METACLASS_SELECT_PATTERN = Any.ID_PATTERN + InputComplex.CASE_PROPERTY_VALUE;
        public static final String MOBILE_PROPERTIES_LIST_TITLE = Any.MOBILE_PROPERTIES_LIST_ANY + Any.TITLE;
        public static final String MOBILE_PROPERTIES_LIST_TITLE_VALUE_XPATH =
                MOBILE_PROPERTIES_LIST_TITLE + "[text()='%s']";
        public static final String POPUP_CHECKBOX = Any.POPUP_LIST_SELECT + Div.ID_PATTERN + Other.IMG_PREFIX;
        /** Шаблон для доступа к элементам выпадающего списка по id */
        public static final String POPUP_LIST_SELECT_PATTERN = Any.POPUP_LIST_SELECT + Any.ID_PATTERN;
        public static final String QUESTION_DIALOG_BTN_SAVE = Any.QUESTION_DIALOG + Div.SAVE_BUTTON;
        public static final String QUESTION_DIALOG_BTN_YES = Any.QUESTION_DIALOG + "//div[@id='gwt-debug-yes']";
        public static final String QUESTION_DIALOG_CONTINUE_WITHOUT_SAVING = Any.QUESTION_DIALOG
                                                                             + Div.CONTINUE_WITHOUT_SAVING;
        public static final String QUESTION_DIALOG_TITLE = Any.QUESTION_DIALOG + Div.DIALOG_WIDGET_HEAD;
        public static final String REPORT_CONTENT_WITH_UUID = Div.REPORT_CONTENT_ANY
                                                              + SpecificComplex.REPORT_LIST_RARAM_VALUE;
        public static final String REQUIRED_VALUE = Any.REQUIRED_VALUE + Other.LABLE_PREFIX;
        public static final String REQUIRED_VALUE_LABEL = Any.REQUIRED_VALUE + Other.LABLE_PREFIX;
        public static final String RESTORE_ICON_IN_ROW = Any.ROW_PTRN + Span.RESTORE_ICON;
        public static final String ROW_IN_CONTAINER_DOWN = SpecificComplex.ROW_IN_CONTAINER + Span.DOWN_ICON;// fontIcon
        public static final String ROW_IN_CONTAINER_EDIT =
                SpecificComplex.ROW_IN_CONTAINER + Span.EDIT_ICON; // fontIcon
        public static final String SCS_CODE = Any.CODE_CODE + Div.VALUE;
        public static final String SCS_DESCR = Any.CODE_RESPONSIBLE + Div.VALUE;
        public static final String SCS_RESP_TYPE = Any.CODE_RESPONSIBLE_TYPE + Div.VALUE;
        public static final String SCS_SRT = Any.CODE_DESCRIPTION + Div.VALUE;
        public static final String SCS_STATE = Any.CODE_ENABLED + Div.VALUE + Span.SPAN_PREFIX;
        public static final String SCS_TITLE = Any.CODE_TITLE + Div.VALUE;
        public static final String SC_DEFAULT_AGREEMENT = Div.INFO_SC_DEFAULT_PARAMS_VALUE + A.AGREEMENT;
        public static final String SC_DEFAULT_CASE = Div.INFO_SC_DEFAULT_PARAMS_VALUE + A.SC;
        public static final String SC_DEFAULT_SERVICE = Div.INFO_SC_DEFAULT_PARAMS_VALUE + A.SERVICE;
        public static final String SELECT_FORMTAG_WITH_TEXT = ANY_SELECT_FORMTAG + Span.SPAN_TEXT_PATTERN;
        public static final String SELECT_PARENT_PROPERTY_VALUE_INPUT = Any.SELECT_PARENT_PROPERTY_VALUE
                                                                        + Input.INPUT_PREFIX;
        public static final String SELECT_PARENT_PROPERTY_VALUE_LABEL = Any.SELECT_PARENT_PROPERTY_VALUE
                                                                        + Other.LABLE_PREFIX;
        public static final String SELECT_STATE = Div.FORM + InputComplex.NEW_STATE_PROPERTY_VALUE;
        /** Поле ввода "Услуги" на форме добавления/редактирования. */
        public static final String SERVICES_VALUE_INPUT = Div.FORM_CONTAINS + Any.SERVICES_VALUE + Input.INPUT_PREFIX;
        /** Содержимое строки формы "Используется в настройках" */
        public static final String SHOW_USAGE_ATTR_ROW = Div.USAGE_ATTR_FORM + Other.TABLE
                                                         + Other.TR_GWT_ROW;
        public static final String NDAP_TRIGGER_CALCSTATUS_ROW = Div.NDAP_TRIGGER_CALCSTATUS_FORM + Other.TABLE
                                                                 + Other.TR_GWT_ROW;
        public static final String STATUS_CODE = Any.CODE_CODE + Input.VALUE;
        public static final String STATUS_DESCR = Any.CODE_DESCRIPTION + Any.DEBUG_VALUE;
        public static final String STATUS_TITLE = Any.CODE_TITLE + Input.VALUE;
        public static final String TAB_ROW_UP = SpecificComplex.ROW_IN_CONTAINER + Span.UP_ICON;// fontIcon
        public static final String TEXT_AREA_ON_LAST_FORM = LAST_DIALOG_BOX + Other.TEXTAREA;
        public static final String TITLE_VALUE_INPUT = Input.TITLE_VALUE + Other.LAST_PATTERN;
        /** Поле для ввода названия на форме */
        public static final String TITLE_ON_FORM = LAST_DIALOG_BOX + TITLE_VALUE_INPUT;
        public static final String TR_DID_A_PATTERN = Other.TR_DID_PATTERN + A.ID_PATTERN;
        public static final String VALIDATION_MESSAGE = Div.VALIDATION_TEXT_CONTAINS + SpecificComplex.POPUP_TEXT;
        public static final String IMG_ENABLED = Div.ENABLED_VALUE + GUIXpath.Span.YES_ICON;
        /** Содержимое колонки "Выполнять проверку синхронно" в списке условий выполнения действий */
        public static final String SYNC_VERIFICATION_ROW_IMAGE = Div.SYNC_VERIFICATION_VALUE + GUIXpath.Span.YES_ICON;
        public static final String END_STATE_CHECKBOX_LABEL = Any.CODE_END_STATE + Other.LABLE_PREFIX;
        public static final String END_STATE = Any.CODE_END_STATE + Div.VALUE + Span.SPAN_PREFIX;
        public static final String END_STATE_INPUT = Any.CODE_END_STATE + Input.INPUT_PREFIX;
        /** Пути к изображению "галочка" и отсутствие изображения "галочка" напротив чекбокса "Передача геопозиции"*/
        public static final String X_SEND_DEVICE_LOCATION_SPAN =
                Div.SEND_DEVICE_LOCATION_AVAILABLE + GUIXpath.Span.SPAN_PREFIX;
        /** Пути к изображению "i" (хранит описание) у логического атрибута"*/
        public static final String BOOL_ATTR_DESCRIPTION_ICON = Other.ANY_VALUE_LABEL_CONTAINS + Span.SPAN_PREFIX;
        /** Содержимое строки формы "Используется в настройках" для групп атрибутов */
        private static final String SHOW_USAGE_ATTR_GROUP_ROW = Div.USAGE_ATTR_GROUP_FORM
                                                                + Other.TABLE + Other.TR_GWT_ROW;
    }

    /**
     * Класс для хранения констант, которые не являются xpath
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class Constant
    {
        public static final String BLUE_LOGO = "uploadFiles/logo/blueLogo.png";
        public static final String CONTACT_FACE_CODE = "contactFaceSelect";
        public static final String CTI_PAGE_PATH = "#cti:";
        public static final String DND_HELPER = FileUtils
                .readAll(Config.get().getResourceDir() + "js/drag_and_drop_helper.js");
        /**
         * Данный метод инициирует не все события, необходимые для корректной работы dragAndDrop,
         * рекомендуется использовать DND_SCRIPT_WITH_SRC_OFFSET_TIMEOUT
         */
        //@formatter:off
        @Deprecated
        public static final String DND_SCRIPT = "jQuery(arguments[0]).simulate('drag', { dropTarget: arguments[1], dx: arguments[2] });";
        public static final String DND_SCRIPT_WITH_SRC_OFFSET_TIMEOUT = "jQuery(arguments[0]).simulate('drag-and-drop', { dragTarget:  arguments[0], dropTarget: arguments[1], dx: arguments[2], dy: arguments[3], timeout: arguments[4] });";
        public static final String INVALID_CONNECTION_IMG_ID = "invalidConnection";
        public static final String IS_ENABLED_RESUMMING_ON_RESOLUTION_TIME_CHANGE = "enableResumingOnResolutionTimeChange";
        //@formatter:on
        public static final String JQUERY_URL = "http://code.jquery.com/jquery-3.7.0.min.js";
        public static final String LOAD_JQUERY_JS = FileUtils
                .readAll(Config.get().getResourceDir() + "js/jquery_load_helper.js");
        /** Строка, аналог изображения, обозначающего "Нет". */
        public static final String NO = "empty";
        public static final String OPERATOR_MODULE = "operatorModule";
        public static final String RED_LOGO = "uploadFiles/logo/redLogo.png";
        public static final String USER_LOGO = "uploadFiles/blank_A4_russ_q.png";
        public static final String VALID_CONNECTION = "validConnection";
        /** Строка, аналог изображения, обозначающего "Да". */
        public static final String YES = "yes";
        /**
         * Постфикс для id контента, являющегося единственным на вкладке.
         * Его id формируется как id вкладки + постфикс.
         */
        public static final String SINGLE_CONTENT_ON_TAB_ID_POSTFIX = "Content";
        private static final String BLUE_THEME = "blue";
    }

    /**
     * Класс для хранения xpath-ов, которые начинаются на префикс '//div'
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class Div
    {
        public static final String WITHOUT_CLASS = "//div";
        public static final String ANY_ICON = "//div[@_code='%s']";
        public static final String ENABLED_VALUE = "//div[@id='gwt-debug-enabled-value']";
        public static final String ADD = "//div[@id='gwt-debug-add']";
        public static final String ADDRESS_VALUE = "//div[@id='gwt-debug-address-value']";
        public static final String ADD_ELEMENT = "//div[@id='gwt-debug-addElememt']";
        public static final String ADD_GROUP = "//div[@id='gwt-debug-addGroup']";
        public static final String ADD_EVENT_ACTION_CONTAINS = "//div[contains(@id, 'gwt-debug-addEventAction')]";
        public static final String ADD_FILE_CONTAINS = "//div[contains(@id,'gwt-debug-addFile')]";
        public static final String ADD_POST_ACTION = "//div[@id='gwt-debug-addPostAction']";
        public static final String ADD_POST_CONDITION = "//div[@id='gwt-debug-addPostCondition']";
        public static final String ADD_PRE_ACTION = "//div[@id='gwt-debug-addPreAction']";
        public static final String ADD_PRE_CONDITION = "//div[@id='gwt-debug-addPreCondition']";
        public static final String ADD_SUPER_USER_BUTTON = "//div[@id='gwt-debug-addSuperUserButton']";
        public static final String DEFAULT_WITH_TEXT = "//div[@id='gwt-debug-default' and .='%s']";
        public static final String AGGREGATE_CLASSES_VALUE = "//div[@id='gwt-debug-aggregateClasses-value']";
        public static final String AGREEMENT_SERVICE_PROPERTY_CONTAINS = "//div[contains(@id, "
                                                                         + "'agreementServiceProperty')]";
        public static final String AGREEMENT_SERVICE_PROPERTY_VALUE = "//div[@id='gwt-debug-agreementServiceProperty"
                                                                      + "-value']";
        public static final String AGREEMENT_SERVICE_PROPERTY_CAPTION = "//div[@id='gwt-debug-agreementServiceProperty"
                                                                        + "-caption']";
        public static final String AGREEMENT_VALUE = "//div[@id='gwt-debug-agreement-value']";
        public static final String ALERT_RULE_VALUE = "//div[@id='gwt-debug-alertRule-value']";
        public static final String ALL_CONTENTS_BY_TYPE = "//div[contains(@id, 'gwt-debug-%s.')]";
        public static final String ANY = "//div[@id='gwt-debug-%s']";
        public static final String ADV_IMPORT_CONFIG_ATTRIBUTES = divGwtDebugId("advimportConfigAttributes");
        public static final String ADV_IMPORT_CONFIG_HISTORY = divGwtDebugId("advimportConfigHistory");
        public static final String EMPLOUEE_ADVLIST = GUIXpath.divGwtDebugId("emplList");
        public static final String OU_ADVLIST = GUIXpath.divGwtDebugId("ouList");
        public static final String TEAM_ADVLIST = GUIXpath.divGwtDebugId("teamList");
        public static final String ANY_AND_ANY_TEXT = "//div[@id='%s' and text()='%s']";
        public static final String ANY_CONTAINS = "//div[contains(@id,'gwt-debug-%s')]";
        public static final String ANY_CONTAINS2 = "//div[contains(@id, '%s')]";
        public static final String ANY_CLASS_CONTAINS = "//div[contains(@class, '%s')]";
        public static final String ANY_CODE_CONTAINS = "//div[contains(@_code, '%s')]";
        public static final String ANY_DIV_VALUE = "//div[@id='gwt-debug-%s-value']";
        public static final String ANY_IN_STATE = "//div[@id='gwt-debug-%s.inState']";
        public static final String ANY_IN_STATE_ANY = "//div[@id='gwt-debug-%s.inState:%s']";
        public static final String ANY_POST_FILL = "//div[@id='gwt-debug-%s.postFill']";
        public static final String ANY_POST_FILL_ANY = "//div[@id='gwt-debug-%s.postFill:%s']";
        public static final String ANY_PRE_FILL = "//div[@id='gwt-debug-%s.preFill']";
        public static final String ANY_PRE_FILL_ANY = "//div[@id='gwt-debug-%s.preFill:%s']";
        public static final String ANY_VALUE = "//div[@id='gwt-debug-%s-value']";
        public static final String CONTENT_CONTAINER = String.format(ANY, "contentContainer");
        public static final String ANY_FIRST_BLOCK_TITLE = CONTENT_CONTAINER + "/div/div/span";
        public static final String ASSOCIATE_EMPLOYEE_VALUE = "//div[@id='gwt-debug-associateEmployee-value']";
        public static final String AUTH_VALUE = "//div[@id='gwt-debug-auth-value']";
        public static final String BUTTONS = "//div[@id='gwt-debug-buttons']";
        public static final String CALCULATION_STRATEGY_CAPTION = "//div[@id='gwt-debug-calculationStrategy-caption']";
        public static final String CALCULATION_STRATEGY_VALUE = "//div[@id='gwt-debug-calculationStrategy-value']";
        public static final String CALC_RULE_VALUE = "//div[@id='gwt-debug-calcRule-value']";
        public static final String CALL_PROCESSING_RULE = "//div[@id='gwt-debug-callProcessingRule']";
        public static final String CANCEL = "//div[@id='gwt-debug-cancel']";
        public static final String CANCELATION = divGwtDebugId("cancelation");
        public static final String RETURN_CANCELLATION = divGwtDebugId("return_cancellation");
        public static final String CAPTION = "//div[@id='gwt-debug-caption']";
        public static final String CASE_CODE_ARCHIVE = "//div[@id='gwt-debug-caseCode.%s.archive']";
        public static final String CARD_OBJECT_FOCUS_VALUE = "//div[@id='gwt-debug-cardObjectFocus-value']";
        public static final String CHANGE_STATE_CONTAINS = "//div[contains(@id,'gwt-debug-changeState')]";
        public static final String CHARACTER_ENCODING_VALUE = "//div[@id='gwt-debug-characterEncoding-value']";
        public static final String CHILD_OBJECT_LIST = "//div[@id='ChildObjectList']";
        public static final String CLASS_ADD_FORM_TAB = divGwtDebugId("Class.newEntryForm");
        public static final String CLASS_ATTRIBUTE_GROUP_TAB = divGwtDebugId("Class.Groups");
        public static final String CLASS_ATTRIBUTE_TAB = divGwtDebugId("Class.Attributes");
        public static final String CLASS_CARD_OBJECT_TAB = divGwtDebugId("Class.__window__");
        public static final String CLASS_CUSTOM_FORM_TAB = divGwtDebugId("Class.customForm");
        public static final String CLASS_EDIT_FORM_TAB = divGwtDebugId("Class.editForm");
        public static final String CLASS_FTS_TAB = divGwtDebugId("Class.fts");
        public static final String CLASS_PERMISSION_SETTINGS_TAB = divGwtDebugId("Class.PermissionSettings");
        public static final String CLASS_VALUE = "//div[@id='gwt-debug-class-value']";
        public static final String CLASS_WF_TAB = "//div[@id='gwt-debug-Class.wf']";
        public static final String CLIENT_VALUE = "//div[@id='gwt-debug-client-value']";
        public static final String CLIENT_VALUE_INPUT = CLIENT_VALUE + Input.INPUT_PREFIX;
        public static final String CODEVALUE = "//div[@id='gwt-debug-codeValue']";
        public static final String CODE_CAPTION = "//div[@id='gwt-debug-code-caption']";
        public static final String CODE_OF_CLOSING_VALUE = "//div[@id='gwt-debug-codeOfClosing-value']";
        public static final String CODE_VALUE = "//div[@id='gwt-debug-code-value']";
        public static final String COLOR_CIRCLE_CONTAINER = "//div[@class='colorCircleContainer']";
        public static final String COLOR_CIRCLE = "//div[@class='colorCircle']";
        public static final String COLOR_VALUE = "//div[@id='gwt-debug-color-value']" + COLOR_CIRCLE;
        public static final String CONFIG_LIST_TAB = divGwtDebugId("configList");
        public static final String CONNECTION_LIST_TAB = divGwtDebugId("connectionList");
        public static final String CONNECTION_STRING_VALUE = "//div[@id='gwt-debug-connectionString-value']";
        public static final String CONNECTION_TASK_VALUE = "//div[@id='gwt-debug-task-value']";
        public static final String CONTAINS_CREATE_NEW_REPORT = "//div[contains(@id,'gwt-debug-createNewReport')]";
        public static final String CONTAINS_FORM = "//div[contains(@id, 'gwt-debug-Form')]";
        public static final String CONTENT_FILE = "//div[contains(@id,'gwt-debug-EditablePropertyList.')]";
        public static final String CORRECT_MAIL_FOLDER_CAPTION = "//div[@id='gwt-debug-correctMailFolder-caption']";
        public static final String CREATE_NEW = "//div[@id='gwt-debug-createNew']";
        public static final String CTI_TEAMS = "//div[@id='gwt-debug-ctiTeams']";
        public static final String DATA_VALUE = "//div[@id='gwt-debug-dataValue']";
        public static final String DATE_TIME_INTERVAL = "//div[@id='gwt-debug-dateTimeInterval']";
        public static final String DAYS_VALUE = "//div[@id='gwt-debug-days-value']";
        public static final String CONFIRM = "//div[@id='gwt-debug-confirm']";
        public static final String CONFIRMATION = "//div[@id='gwt-debug-confirmation']";
        public static final String DEFAULT_OBJECT_VALUE = "//div[@id='gwt-debug-defaultObject-value']";
        public static final String DEFAULT_VALUE_VALUE = "//div[@id='gwt-debug-defaultValue-value']";
        public static final String DEL = "//div[@id='gwt-debug-del']";
        public static final String DEL_CONTAINS = "//div[contains(@id, 'gwt-debug-del.')]";
        public static final String DESCRIPTION_CAPTION = "//div[@id='gwt-debug-description-caption']";
        public static final String DESCRIPTION_VALUE = "//div[@id='gwt-debug-description-value']";
        public static final String DIALOG_WIDGET_DESCRIPTION_ELEMENT = "//div[@id='gwt-debug"
                                                                       + "-dialogWidgetDescriptionElement']";
        public static final String DIALOG_WIDGET_HEAD = "//div[@id='gwt-debug-dialogWidgetHead']";
        public static final String DIRECT_LINK_TARGET_VALUE = "//div[@id='gwt-debug-directLinkTarget-value']";
        public static final String EDIT = "//div[@id='gwt-debug-edit']";
        public static final String EDITABLE_PROPERTY_LIST_CONTAINS = "//div[contains(@id,"
                                                                     + "'gwt-debug-EditablePropertyList.')]";
        public static final String EDIT_ANY_VALUE = "//div[@id='gwt-debug-edit-%s-value']";
        public static final String EDIT_CONTAINS = "//div[contains(@id, 'gwt-debug-edit.')]";
        public static final String EDIT_SCRIPT_VALUE = "//div[@id='gwt-debug-edit-script-value']";
        public static final String EMPLOYEE_RESPONSIBLE_STRATEGY = "//div[@id='employeeResponsibleStrategy:%s']";
        public static final String ENABLED_CAPTION = "//div[@id='gwt-debug-enabled-caption']";
        public static final String ENABLE_LAYOUT_MODE = "//div[@id='gwt-debug-enableLayoutMode']";
        public static final String EVENT_FILTER_VALUE = "//div[@id='gwt-debug-eventFilter-value']";
        public static final String ERASE = "//div[@id='gwt-debug-erase']";
        public static final String ERROR_DIALOG = "//div[@id='gwt-debug-errorDialog']";
        public static final String ESCALATION_ACTIONS_TAB = "//div[@id='gwt-debug-actions']";
        public static final String ESCALATION_SCHEMES_TAB = "//div[@id='gwt-debug-schemes']";
        public static final String ESCALATION_VALUE_MAPS_TAB = "//div[@id='gwt-debug-valueMaps']";
        public static final String EVENT_ACTIONS_TAB = "//div[@id='gwt-debug-eventActions']";
        public static final String JMS_QUEUES_TAB = "//div[@id='gwt-debug-jmsQueues']";
        public static final String EXECUTION_RESULT_VALUE = "//div[@id='gwt-debug-executionResult-value']";
        public static final String EXECUTION_ERROR_VALUE = "//div[@id='gwt-debug-executionError-value']";
        public static final String FAST_CREATE_EDIT = "//div[@id='gwt-debug-fastCreateEdit']";
        public static final String FEED_BACK_ADDRESS_VALUE = "//div[@id='gwt-debug-feedbackAddress-value']";
        public static final String FIRST_NAME_VALUE = "//div[@id='gwt-debug-firstName-value']";
        public static final String FOLDERS_CAPTION = "//div[@id='gwt-debug-folders-caption']";
        public static final String FOLDERS_VALUE = "//div[@id='gwt-debug-folders-value']";
        public static final String FORM = "//div[@id='gwt-debug-Form']";
        public static final String FORM_CONTAINS = "//div[contains(@id, 'gwt-debug-Form')]";
        public static final String FORM_RICH_TEXT_AREA_GLASS = "//div[@id='gwt-debug-formRichTextAreaGlass']";
        public static final String SYSTEM_EMAIL_FROM_VALUE = "//div[@id='gwt-debug-systemEmailFrom-value']";
        public static final String SYSTEM_EMAIL_SENDER_NAME_VALUE = "//div[@id='gwt-debug-systemEmailSenderName-value"
                                                                    + "']";
        public static final String GRP_TITLE_VALUE = "//div[@id='gwt-debug-grpTitle-value']";
        public static final String HEADER_TOOLS = "//div[@id='gwt-debug-HeaderTools']";
        public static final String ID_PATTERN = "//div[@id='%s']";
        public static final String BANNER_PANEL = String.format(Div.ID_PATTERN, "bannerPanel");
        public static final String HEADER_PANEL = String.format(Div.ID_PATTERN, "headerPanel");
        public static final String IMPACT_VALUE = "//div[@id='gwt-debug-impact-value']";
        public static final String INCORRECT_MAIL_FOLDER_CAPTION = "//div[@id='gwt-debug-incorrectMailFolder-caption']";
        public static final String INFO_CLASS_STRATEGY_VALUE = "//div[@id='gwt-debug-Info.ClassStrategy-value']";
        public static final String INFO_DIALOG = "//div[@id='gwt-debug-infoDialog']";
        public static final String INFO_SC_DEFAULT_PARAMS_VALUE = "//div[@id='gwt-debug-Info.scDefaultParams-value']";
        public static final String INTERFACE_SETTINGS_TAB = "//div[@id='gwt-debug-settings']";
        public static final String INTERFACE_NAVIGATION_TAB = "//div[@id='gwt-debug-navigation']";
        public static final String ITERATION_DELAY_VALUE = "//div[@id='gwt-debug-iterationDelay-value']";
        public static final String LAST_CONNECTION_DATE_VALUE = "//div[@id='gwt-debug-lastConnectionDate-value']";
        public static final String LAYOUT_LEFT_COL = "//div[@id='gwt-debug-layoutLeftCol']";
        public static final String LAYOUT_RIGHT_COL = "//div[@id='gwt-debug-layoutRightCol']";
        public static final String LOGIN_AS_USER = "//div[@id='gwt-debug-loginAsUser']";
        public static final String LOGIN_CAPTION = "//div[@id='gwt-debug-login-caption']";
        public static final String LOGIN_VALUE = "//div[@id='gwt-debug-login-value']";
        public static final String MAIL_INBOUND_MAIL_TAB = divGwtDebugId("inboundMail");
        public static final String MAIL_INBOX_MESSAGES_TAB = divGwtDebugId("inboxMessages");
        public static final String MAIL_LOG_TAB = divGwtDebugId("mailLog");
        public static final String MAIL_OUTGOING_MAIL_TAB = divGwtDebugId("outgoingMail");
        public static final String MAIL_PROCESSOR_CAPTION = "//div[@id='gwt-debug-mailProcessor-caption']";
        public static final String MAIL_PROCESSOR_PROCESSOR_TAB = divGwtDebugId("processor");
        public static final String MAIL_PROCESSOR_VERSIONS_TAB = divGwtDebugId("versions");
        public static final String MAIL_PROCESSOR_VALUE = "//div[@id='gwt-debug-mailProcessor-value']";
        public static final String MAIL_SERVER_CAPTION = "//div[@id='gwt-debug-mailServer-caption']";
        public static final String MAIL_SERVER_VALUE = "//div[@id='gwt-debug-mailServer-value']";
        public static final String MAIN_CONTENT = String.format(ANY, "mainContent");
        public static final String MAIN_CONTENT_CONTAINER = String.format(ANY, "mainContentContainer");
        public static final String MESSAGE = "//div[@id='gwt-debug-message']";
        public static final String MESSAGES_PER_ITERATION_VALUE = "//div[@id='gwt-debug-messagesPerIteration-value']";
        public static final String METACLASS_VALUE = "//div[@id='gwt-debug-metaClass-value']";
        public static final String NAME_VALUE = "//div[@id='gwt-debug-name-value']";
        public static final String N_TREE_ITEM_CONTENT_CONTAINS = "//div[contains(@id,'gwt-debug-NTreeItemContent"
                                                                  + ".uuid:%s')]";
        public static final String OBJECT_CARD_CAPTION = "//div[@id='gwt-debug-objectCardCaption']";
        public static final String OK = "//div[@id='gwt-debug-ok']";
        public static final String OLD_CASE_PROPERTY_CAPTION = "//div[@id='gwt-debug-oldCaseProperty-caption']";
        public static final String OLD_SCRIPT_CAPTION = "//div[@id='gwt-debug-old-script-caption']";
        public static final String OLD_SCRIPT_VALUE = "//div[@id='gwt-debug-old-script-value']";
        public static final String ON_CAPTION = "//div[@id='gwt-debug-on-caption']";
        public static final String ON_VALUE = "//div[@id='gwt-debug-on-value']";
        public static final String PARENT_CLS_PROP_VALUE = "//div[@id='gwt-debug-parentClsProp-value']";
        public static final String PARENT_VALUE = "//div[@id='gwt-debug-parent-value']";
        public static final String PASSWORD_CAPTION = "//div[@id='gwt-debug-password-caption']";
        public static final String PASSWORD_VALUE = "//div[@id='gwt-debug-password-value']";
        public static final String PDF_CURRENT = "//div[@id='pdf_current']";
        public static final String PERIOD_CAPTION = "//div[@id='gwt-debug-period-caption']";
        public static final String PERIOD_VALUE = "//div[@id='gwt-debug-period-value']";
        public static final String PERMITTED_TYPES_VALUE = "//div[@id='gwt-debug-permittedTypes-value']";
        public static final String PLAN_EXECUTION_DATE_CAPTION = "//div[@id='gwt-debug-planExecutionDate-caption']";
        public static final String PLAN_EXECUTION_DATE_VALUE = "//div[@id='gwt-debug-planExecutionDate-value']";
        public static final String PLANNING_MODE_HEADLINE = String.format(Div.ANY, "ObjectList.ObjList");
        public static final String POPUP_CONTENT = "//div[@class='popupContent']";
        public static final String POPUP_LIST_SELECT = "//div[@id='gwt-debug-PopupListSelect']";
        public static final String PORT_VALUE = "//div[@id='gwt-debug-port-value']";
        public static final String POST_ACTIONS = "//div[@id='gwt-debug-postActions']";
        public static final String POST_CONDITIONS = "//div[@id='gwt-debug-postConditions']";
        public static final String PRE_ACTIONS = "//div[@id='gwt-debug-preActions']";
        public static final String PRE_CONDITIONS = "//div[@id='gwt-debug-preConditions']";
        public static final String PROCESSING_RULE_VALUE = "//div[@id='gwt-debug-processingRule-value']";
        public static final String PROCESS_BATCH_SIZE_CAPTION = "//div[@id='gwt-debug-processBatchSize-caption']";
        public static final String PROCESS_BATCH_SIZE_VALUE = "//div[@id='gwt-debug-processBatchSize-value']";
        public static final String PROPERTY_DIALOG_BOX = "//div[@id='gwt-debug-PropertyDialogBox']";
        public static final String PROPERTY_DIALOG_BOX_LAST = "//div[@id='gwt-debug-PropertyDialogBox'][last()]";
        public static final String PROPERTY_DIALOG_BOX_CAPTION = "//div[@id='gwt-debug-PropertyDialogBox-caption']";
        public static final String PROPERTY_DIALOG_BOX_CAPTION_AND_TEXT = "//div[@id='gwt-debug-PropertyDialogBox"
                                                                          + "-caption' and text()='%s']";
        public static final String PROPERTY_DIALOG_BOX_CONTENT = "//div[@id='gwt-debug-PropertyDialogBox-content']";
        public static final String PROPERTY_DIALOG_BOX_CAPTION_BADGE =
                PROPERTY_DIALOG_BOX_CAPTION + Span.CONTAINS_TEXT;
        public static final String PROPERTY_LIST_ANY = "//div[@id='gwt-debug-PropertyList.%s']";
        public static final String PROTOCOL_CAPTION = "//div[@id='gwt-debug-protocol-caption']";
        public static final String PROTOCOL_VALUE = "//div[@id='gwt-debug-protocol-value']";
        public static final String QUICK_ADD_FORM_VALUE = "//div[@id='gwt-debug-quickAddForm-value']";
        public static final String QUICK_EDIT_FORM_VALUE = "//div[@id='gwt-debug-quickEditForm-value']";
        public static final String READ_COMMUNITY_VALUE = "//div[@id='gwt-debug-readCommunity-value']";
        public static final String RECEIVE_BATCH_SIZE_CAPTION = "//div[@id='gwt-debug-receiveBatchSize-caption']";
        public static final String RECEIVE_BATCH_SIZE_VALUE = "//div[@id='gwt-debug-receiveBatchSize-value']";
        public static final String REFRESH = "//div[@id='gwt-debug-refresh']";
        public static final String REMOVE = "//div[@id='gwt-debug-remove']";
        public static final String REPORT_CONTENT_ANY = "//div[@id='gwt-debug-ReportContent.%s']";
        public static final String REPORT_CSV = "//div[@id='csv']";
        public static final String REPORT_HTML_ZIP = "//div[@id='htmlZip']";
        public static final String REPORT_LIST_ANY = "//div[@id='gwt-debug-ReportsList.%s']";
        public static final String REPORT_PARAMETERS = "//div[@id='gwt-debug-reportParameters']";
        public static final String REPORT_PARAMETRS_CONTAINS = "//div[contains(@id,'gwt-debug-reportParameters')]";
        public static final String REPORT_XLSX = "//div[@id='xlsx']";
        public static final String REPORT_DOCX = "//div[@id='docx']";
        public static final String RESEND_DELAY_VALUE = "//div[@id='gwt-debug-resendDelay-value']";
        public static final String RESOLUTION_TIME_ATTR_VALUE = "//div[@id='gwt-debug-resolutionTimeAttr-value']";
        public static final String RESOLVE = "//div[@id='gwt-debug-resolve']";
        public static final String RESTORE = "//div[@id='gwt-debug-restore']";
        public static final String RICH_TEXT_WIDE_VIEW = "//div[@id='richTextWideView']";
        public static final String SAVE = "//div[@id='gwt-debug-save']";
        public static final String SAVE_REPORT = "//div[@id='gwt-debug-saveReport']";
        public static final String SAVE_REPORT_CONTAINS = "//div[contains(@id,'gwt-debug-saveReport')]";
        public static final String SELECT_ALL = "//div[@id='gwt-debug-selectAll']";
        public static final String SCRIPT_TEXT_VALUE = "//div[@id='gwt-debug-scriptText-value']";
        public static final String SCRIPT_VALUE = "//div[@id='gwt-debug-script-value']";
        public static final String SECURITY_GROUPS_TAB = "//div[@id='gwt-debug-groups']";
        public static final String SECURITY_PROTOCOL_VALUE = "//div[@id='gwt-debug-securityProtocol-value']";
        public static final String SECURITY_ROLES_TAB = "//div[@id='gwt-debug-roles']";
        public static final String CONNECTION_PROTOCOL_VALUE = "//div[@id='gwt-debug-connectionProtocol-value']";
        public static final String SELECTED_STATE_VALUE = "//div[@id='gwt-debug-selectedState-value']";
        public static final String SELECT_CONTACTS = "//div[@id='SelectContacts']";
        public static final String SELECT_SEARCH_BOTTOM_CONTAINS = "//div[contains(@class,'selectSearch_bottom')]";
        public static final String SELECT_SEARCH_TOP_HEADER = "//div[@class='selectSearch_top_header']";
        public static final String SELECT_SEARCH_TOP_FOOT = "//div[@class='selectSearch_top_foot']";
        public static final String SEND_ATTEMPS_VALUE = "//div[@id='gwt-debug-sendAttempts-value']";
        public static final String SEND_PARTIAL_VALUE = "//div[@id='gwt-debug-sendPartial-value']";
        public static final String SEND_DEVICE_LOCATION_AVAILABLE = "//div[@id='gwt-debug-sendDeviceLocationAvailable"
                                                                    + "-value']";
        public static final String SERVER_ADDRESS_VALUE = "//div[@id='gwt-debug-serverAddress-value']";
        public static final String CTI_API_WEBSOCKET_URL_VALUE = "//div[@id='gwt-debug-ctiApiWebsocketUrl-value']";
        public static final String SERVER_CAPTION = "//div[@id='gwt-debug-server-caption']";
        public static final String SERVER_CONFIG = "//div[@id='gwt-debug-serverConfig']";
        public static final String SERVER_TYPE_VALUE = "//div[@id='gwt-debug-serverType-value']";
        public static final String SERVER_VALUE = "//div[@id='gwt-debug-server-value']";
        public static final String SERVICE_TIME_ATTR_VALUE = "//div[@id='gwt-debug-serviceTimeAttr-value']";
        public static final String SERVICE_VALUE = "//div[@id='gwt-debug-service-value']";
        public static final String SNMP_VERSION_VALUE = "//div[@id='gwt-debug-snmpVersion-value']";
        public static final String SOURCE_LEFT = "//div[@id='gwt-debug-source-left']";
        public static final String START_DATE_CAPTION = "//div[@id='gwt-debug-startDate-caption']";
        public static final String START_DATE_VALUE = "//div[@id='gwt-debug-startDate-value']";
        public static final String STATES_FOR_STOP_VALUE = "//div[@id='gwt-debug-statesForStop-value']";
        public static final String STRUCTURED_OBJECTS_VIEW_ITEM_CODE_VALUE = "//div[@id='gwt-debug"
                                                                             + "-structuredObjectsViewItemCode-value']";
        public static final String SWITCH = "//div[@id='gwt-debug-switch']";
        public static final String SYNC_VERIFICATION_VALUE = "//div[@id='gwt-debug-sync-verification-value']";
        public static final String TARGET_TYPES_VALUE = "//div[@id='gwt-debug-targetTypes-value']";
        public static final String TASKS_CAPTION = "//div[@id='gwt-debug-tasks-caption']";
        public static final String TASKS_VALUE = "//div[@id='gwt-debug-tasks-value']";
        public static final String EMBEDDED_APP_FULLSCREEN = String.format(Any.ANY_VALUE, "fullscreenAllowed");
        public static final String TEXT_CONTAINS = "//div[contains(text(),'%s')]";
        public static final String TEXT_FIND_CONTAINS = "//div[contains(text(),'найдено')]";
        public static final String TEXT_WIDE_VIEW = "//div[@id='textWideView']";
        public static final String TIMER_CONDITION_VALUE = "//div[@id='gwt-debug-timerCondition-value']";
        public static final String TIME_METRIC_VALUE = "//div[@id='gwt-debug-timeMetric-value']";
        public static final String TIME_UPDATE_VALUE = "//div[@id='gwt-debug-timeUpdate-value']";
        public static final String TIME_ZONE_ATTR_VALUE = "//div[@id='gwt-debug-timeZoneAttr-value']";
        public static final String TIME_ZONE_VALUE = "//div[@id='gwt-debug-timeZone-value']";
        public static final String TIMER_DEFIFNITION_CAPTION = "//div[@id='gwt-debug-timerDefinition-caption']";
        public static final String TITLE = "//div[@id='gwt-debug-title']";
        public static final String TRANSITION_ANY = "//div[@id='gwt-debug-%s.%s.%s']";
        public static final String MAIN_TAB_BAR = String.format(ANY, "tabBar");
        public static final String MAIN_CONTENT_HEADER = divGwtDebugId("mainContentHeader");
        public static final String HEADER_TITLE = String.format(ANY, "headerTitle");
        public static final String HEADER_TITLE_TEXT = HEADER_TITLE + "[text()='%s']";
        public static final String HEADER_TITLE_CONTAINS_TEXT = HEADER_TITLE + "[contains(text(), '%s')]";
        public static final String HEADER_TITLE_ANY_FORM_ADD = HEADER_TITLE + "[text()='%s / Форма добавления']";
        public static final String HEADER_TITLE_ANY_FORM_EDIT = HEADER_TITLE + "[text()='%s / Форма редактирования']";
        public static final String HEADER_TITLE_SC_FORM_ADD = HEADER_TITLE + "[text()='Запрос / Форма добавления']";
        public static final String HEADER_TITLE_LINE = String.format(ANY, "headerTitleLine");
        public static final String HEADER_TITLE_BADGE =
                HEADER_TITLE_LINE + Span.SPAN_PREFIX + "[@id='gwt-debug-headerBadge' and contains(text(),'%s')]";
        public static final String TITLE_CAPTION = "//div[@id='gwt-debug-title-caption']";
        public static final String TITLE_VALUE = "//div[@id='gwt-debug-title-value']";
        public static final String TITLE_VALUE_ADV = "//div[@id='gwt-debug-titleValue']";
        public static final String TRANSLITERATE_SUBJECT_VALUE = "//div[@id='gwt-debug-transliterateSubject-value']";
        public static final String TYPE_VALUE = "//div[@id='gwt-debug-type-value']";
        public static final String OUTER_PANEL = String.format(ANY, "outer");
        public static final String URGENCY_VALUE = "//div[@id='gwt-debug-urgency-value']";
        public static final String URL_VALUE = "//div[@id='gwt-debug-url-value']";
        public static final String USERNAME_VALUE = "//div[@id='gwt-debug-username-value']";
        public static final String VALIDATION_TEXT_AND_TEXT_CONTAINS = "//div[contains(@class,'validation-text') and "
                                                                       + "text()='%s']";
        public static final String VALIDATION_TEXT_CONTAINS = "//div[contains(@class,'validation-text')]";
        public static final String VALUE = "//div[@id='gwt-debug-value']";
        public static final String VALUE_CELL_TREE = "//div[@id='gwt-debug-valueCellTree']";
        public static final String TREE_ITEM = "//div[@role='treeitem']";
        public static final String VALUE_WIDGET_VALUE = "//div[@id='gwt-debug-valueWidget-value']";
        public static final String VERSION_AUTHOR_CAPTION = "//div[@id='gwt-debug-versionAuthor-caption']";
        public static final String VERSION_CAPTION = "//div[@id='gwt-debug-version-caption']";
        public static final String WF_EDIT_ATTRS = "//div[@id='gwt-debug-wfEditAttrs']";
        public static final String WF_EDIT_PROPS = "//div[@id='gwt-debug-wfEditProps']";
        public static final String COPY = "//div[@id='gwt-debug-copy']";
        public static final String FORMTAGS = "//div[@class='formtags']";
        public static final String TARGET_TYPES_CAPTION = "//div[@id='gwt-debug-targetTypes-caption']";
        public static final String TEXT_ANY = "//div[text()='%s']";
        public static final String X_BLOCK_TITLE = ANY + "/div/span";
        public static final String X_BLOCK_HIDDEN = X_BLOCK_TITLE + "[contains(@class, 'down')]";
        public static final String ADVLIST_PANEL = "//div[contains(@class,'advListPanel')]";
        public static final String USAGE_ATTR_FORM = GUIXpath.divGwtDebugId("usageAttr");
        public static final String NDAP_TRIGGER_CALCSTATUS_FORM = GUIXpath.divGwtDebugId("triggerStatus");
        public static final String NDAP_TRIGGER_SHOW_STACKTRACE = "showStackTrace";
        public static final String NDAP_SCRIPT_MODULE_STATE_VALUE = "//div[@id='gwt-debug-moduleState-value']";
        public static final String NDAP_STATE_SCRIPT_MODULES_VALUE = "//div[@id='gwt-debug-sys_stateScriptModules"
                                                                     + "-value']";
        public static final String NDAP_ERROR_SCRIPT_MODULES_VALUE = "//div[@id='gwt-debug-sys_errorScriptModules"
                                                                     + "-value']";
        public static final String USAGE_ATTR_GROUP_FORM = GUIXpath.divGwtDebugId("usageAttrGroup");
        public static final String WIDTH_COLUMNS_DEFAULT = "//div[@id='gwt-debug-widthColumnsDefault']";
        public static final String TABS_LIMIT_EXCEEDED = "//div[@id='gwt-debug-tabs-limit-exceeded']";
        public static final String NESTED_DIVS_PATH = Div.ID_PATTERN + Div.ID_PATTERN;
        public static final String CLOSED_BY_VALUE = "//div[@id='gwt-debug-closedBy-value']";
        public static final String IFRAME_PATTERN = Div.ID_PATTERN + Any.IFRAME;
        public static final String COMMENT_LIST = "//div[@id='gwt-debug-CommentList.%s']";
        public static final String INLINE_COMMENT = "//div[@id='inlineContainer']";
        public static final String PLAIN_TEXT_WRAPPER = "<div style=\"white-space: pre-line\" "
                                                        + "_code=\"plainTextWrapper\">%s</div>";
        public static final String DISABLED_TAG = "//div[@id='gwt-debug-%s' and @class='formtag formtagDisabled "
                                                  + "grey-text']";
        public static final String CONTAINS_EDIT_PROPERTIES = String.format(ANY_CONTAINS, "editProperties.");
        public static final String CONTAINS_ADD_COMMENT = String.format(ANY_CONTAINS, "addComment.");
        public static final String CONTAINS_ADD = String.format(ANY_CONTAINS, "add.");
        public static final String TRANSITION_HEADER = "//div[@class='transitionHeader']";
        public static final String BUTTON_BY_TEXT = "//div[contains(@class, 'g-button') and ./div/div[text()='%s']]";
        public static final String LINK_BUTTON_BY_TEXT = "//div[contains(@class, 'actionLink') and ./div/div[text()"
                                                         + "='%s']]";
        public static final String GATE_TYPE_VALUE = "//div[@id='gwt-debug-gateType-value']";
        /**
         * Карточка временно-созданного элемента
         */
        private static final String TEMP_ITEM_CARD = "//div[contains(@id, 'gwt-debug-tempUuid')]";
        private static final String AGREEMENTS_VALUE = "//div[@id='gwt-debug-agreements-value']";
        private static final String AGREEMENT_SERVICE_EDIT_PRS_VALUE = "//div[@id='gwt-debug-agreementServiceEditPrs"
                                                                       + "-value']";
        private static final String ATTRIBUTE_VALUE = "//div[@id='gwt-debug-attribute-value']";
        private static final String AVAILABLE_CLASSES_VALUE = "//div[@id='gwt-debug-availableClasses-value']";
        private static final String CALCULATE_STRATEGY_VALUE = "//div[@id='gwt-debug-calculateStrategy-value']";
        private static final String CALL_CASES_VALUE = "//div[@id='gwt-debug-callCases-value']";
        private static final String CATEGORIES_VALUE = "//div[@id='gwt-debug-categories-value']";
        private static final String CONTACT_ATTRIBUTES_VALUE = "//div[@id='gwt-debug-contactAttributes-value']";
        private static final String CONTINUE_WITHOUT_SAVING = "//div[@id='gwt-debug-continue_without_saving']";
        private static final String EMPL_ATTRIBUTE_GROUP_VALUE = "//div[@id='gwt-debug-emplAttributeGroup-value']";
        private static final String FEATURE_PANEL = "//div[@id='gwt-debug-featurePanel']";
        private static final String FORMTAG_CONTAINS = "//div[contains(@class, 'formtag')]";
        private static final String GRP_USERS_VALUE = "//div[@id='gwt-debug-grpUsers-value']";
        private static final String LICENSE_VALUE = "//div[@id='gwt-debug-license-value']";
        private static final String OU_ATTRIBUTE_GROUP_VALUE = "//div[@id='gwt-debug-ouAttributeGroup-value']";
        private static final String PARENT_TYPE_CAPTION = "//div[@id='gwt-debug-parentType-caption']";
        private static final String POSITION_VALUE = "//div[@id='gwt-debug-position-value']";
        private static final String PRESENTATION_LIST_VALUE = "//div[@id='gwt-debug-presentationList-value']";
        private static final String PRESENTATION_TYPE_VALUE = "//div[@id='gwt-debug-presentationType-value']";
        private static final String PRIORITY_RULE_VALUE = "//div[@id='gwt-debug-priorityRule-value']";
        private static final String PRIORITY_VALUE = "//div[@id='gwt-debug-priority-value']";
        private static final String PRS_VALUE = "//div[@id='gwt-debug-prs-value']";
        private static final String PRIVATE_VALUE_CONTAINS = "//*[contains(@id,'gwt-debug-private') and contains(@id,"
                                                             + " 'value')]";
        private static final String RECEPIENT_AGREEMENTD_VALUE = "//div[@id='gwt-debug-recipientAgreements-value']";
        private static final String RESOLUTION_TIME_VALUE = "//div[@id='gwt-debug-resolutionTimeRule-value']";
        private static final String SAVE_BUTTON = "//div[@id='gwt-debug-save']";
        private static final String SERVER_CONFIG_VALUE = "//div[@id='gwt-debug-serverConfig-value']";
        private static final String SERVICES_VALUE = "//div[@id='gwt-debug-services-value']";
        private static final String SERVICE_HOURS_VALUE = "//div[@id='gwt-debug-serviceHours-value']";
        private static final String SERVICE_TIME_VALUE = "//div[@id='gwt-debug-serviceTime-value']";
        private static final String SHOW_PRESENTATION_VALUE = "//div[@id='gwt-debug-showPresentation-value']";
        private static final String SUPPORT_HOURS_VALUE = "//div[@id='gwt-debug-supportHours-value']";
        private static final String TARGET_CATALOG_VALUE = "//div[@id='gwt-debug-targetCatalog-value']";
        private static final String TARGET_CLASS_VALUE = "//div[@id='gwt-debug-targetClass-value']";
        private static final String TARGET_LEFT = "//div[@id='gwt-debug-target-left']";
        private static final String TEAM_ATTRIBUTE_GROUP_VALUE = "//div[@id='gwt-debug-teamAttributeGroup-value']";
        private static final String TIMER_DEFENITION_VALUE = "//div[@id='gwt-debug-timerDefinition-value']";
        private static final String VERSION_VALUE = "//div[@id='gwt-debug-version-value']";
    }

    /**
     * Класс для хранения id-констант, которые начинаются на gwt-debug
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class Id
    {
        public static final String ANY = "gwt-debug-%s";
        public static final String ANY_VALUE = "gwt-debug-%s-value";
        public static final String AGREEMENT_SERVICE_PROPERTY_VALUE = "gwt-debug-agreementServiceProperty-value";
        public static final String CLIENT_VALUE = "gwt-debug-client-value";
        public static final String CLOSED_BY_VALUE = "gwt-debug-closedBy-value";
        public static final String DEFAULT_VALUE_VALUE = "gwt-debug-defaultValue-value";
        public static final String DESTINATION_PROPERTY_VALUE = "gwt-debug-destinationProperty-value";
        public static final String ENABLED_VALUE = "gwt-debug-enabled-value";
        public static final String FQN_VALUE = "gwt-debug-fqn-value";
        public static final String GRP_GROUPS_VALUE = "gwt-debug-grpUsers-value";
        public static final String LEADER_VALUE = "gwt-debug-leader-value";
        public static final String MEMBERS_VALUE = "gwt-debug-members-value";
        public static final String PERMITTED_TYPES_VALUE = "gwt-debug-permittedTypes-value";
        public static final String RECIPIENTS_VALUE = "gwt-debug-recipients-value";
        public static final String RESPONSIBLE_VALUE = "gwt-debug-responsible-value";
        public static final String SELECTED_STATE_VALUE = "gwt-debug-selectedState-value";
        public static final String SELECTED_STOPED_STATE_VALUE = "gwt-debug-selectedStopedState-value";
        public static final String SOLVED_BY_VALUE = "gwt-debug-solvedBy-value";
        public static final String TARGET_METACLASS_VALUE = "gwt-debug-targetMetaClass-value";
        public static final String TARGET_OBJECTS_VALUE = "gwt-debug-targetObjects-value";
        public static final String TARGET_TYPES_VALUE = "gwt-debug-targetTypes-value";
        public static final String VALUE_WIDGET_VALUE = "gwt-debug-valueWidget-value";
        public static final String STANDALONE_OBJECT_LIST_CONTENT_UUID = "gwt-debug-ObjectList.standaloneObjectList.%s";
        public static final String STANDALONE_REL_OBJECT_LIST_CONTENT_UUID = "gwt-debug-RelObjectList"
                                                                             + ".standaloneObjectList.%s";
        public static final String STANDALONE_CHILD_OBJECT_LIST_CONTENT_UUID = "gwt-debug-ChildObjectList"
                                                                               + ".standaloneObjectList.%s";
    }

    /**
     * Класс для хранения xpath-ов, которые начинаются на префикс '//input'
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class Input
    {
        public static final String ANY_INPUT = "//input[@id='gwt-debug-%s-input']";
        public static final String INPUT_DISABLED = "//input[@disabled]";
        public static final String ANY_INPUT_WITH_CONDITION = "//input[@id='gwt-debug-%s-input' %s]";
        public static final String ANY_VALUE = "//input[@id='gwt-debug-%s-value']";
        public static final String ANY_VALUE_INPUT = Div.ANY_VALUE + "/input";
        public static final String ANY_INPUT_VALUE = "//input[@id='gwt-debug-%s-value-input']";
        public static final String APP_ID_VALUE = "//input[@id='gwt-debug-appId-value']";
        public static final String AUTH_VALUE_INPUT = String.format(ANY_INPUT_VALUE, "auth");
        public static final String CLIENT_EMAIL_VALUE = "//input[@id='gwt-debug-clientEmail-value']";
        public static final String CLIENT_ID_VALUE = "//input[@id='gwt-debug-clientId-value']";
        public static final String CLIENT_NAME_VALUE = "//input[@id='gwt-debug-clientName-value']";
        public static final String CLIENT_PHONE_VALUE = "//input[@id='gwt-debug-clientPhone-value']";
        public static final String CLIENT_SECRET_VALUE = "//input[@id='gwt-debug-clientSecret-value']";
        public static final String CODE_VALUE = "//input[@id='gwt-debug-code-value']";
        public static final String COMPACT_VIEW_INPUT = "//input[@id='gwt-debug-view-compact-input']";
        public static final String CONNECTION_STRING_VALUE = "//input[@id='gwt-debug-connectionString-value']";
        public static final String CONNECTION_TIMEOUT_VALUE = "//input[@id='gwt-debug-connectionTimeout-value']";
        public static final String COPIES_VALUE = "//input[@id='gwt-debug-copies-value']";
        public static final String CORRECT_MAIL_FOLDER_VALUE = "//input[@id='gwt-debug-correctMailFolder-value']";
        public static final String CP_FORM_DUPLICATE_PASSWORD_VALUE = "//input[@id='gwt-debug-cpFormDuplicatePassword"
                                                                      + "-value']";
        public static final String CP_FORM_PASSWORD_VALUE = "//input[@id='gwt-debug-cpFormPassword-value']";
        public static final String CURRENT_PASSWORD_VALUE = "//input[@id='gwt-debug-current-password-value']";
        public static final String CTI_API_WEBSOCKET_URL_VALUE = "//input[@id='gwt-debug-ctiApiWebsocketUrl-value']";
        public static final String DATE_TEXT_BOX = "//input[@id='gwt-debug-dateTextBox']";
        public static final String DEFAULT_VIEW_INPUT = "//input[@id='gwt-debug-view-default-input']";
        public static final String ENABLED_VALUE_INPUT = "//input[contains(@id,'gwt-debug-enabled') and contains(@id,"
                                                         + " '-value-input')]";
        public static final String FILTER_SERVICES_VALUE_INPUT = "//input[@id='gwt-debug-filterServices-value-input']";
        public static final String FITER_AGREEMENTS_VALUE_INPUT = "//input[@id='gwt-debug-filterAgreements-value"
                                                                  + "-input']";
        public static final String FOLDERS_VALUE = "//input[@id='gwt-debug-folders-value']";
        public static final String FUTURE = "//input[@id='FUTURE']";
        public static final String FUTURE_DISABLED = "//input[@id='FUTURE' and @disabled]";
        public static final String GRP_CODE_VALUE = "//input[@id='gwt-debug-grpCode-value']";
        public static final String GRP_TITLE_VALUE = "//input[@id='gwt-debug-grpTitle-value']";
        public static final String HAS_RESPONSIBLE_VALUE_INPUT = "//input[@id='gwt-debug-hasResponsible-value-input']";
        public static final String HAS_WORKFLOW_VALUE_INPUT = "//input[@id='gwt-debug-hasWorkflow-value-input']";
        public static final String HYPERLINK_TEXT = "//input[@id='gwt-debug-hyperlinkText']";
        public static final String HYPERLINK_URL = "//input[@id='gwt-debug-hyperlinkURL']";
        public static final String ID_PATTERN = "//input[@id='%s']";
        public static final String INCORRECT_MAIL_FOLDER_VALUE = "//input[@id='gwt-debug-incorrectMailFolder-value']";
        public static final String INHERIT_VALUE_INPUT = "//input[@id='gwt-debug-inherit-value-input']";
        public static final String INPUT_PREFIX = "//input";
        public static final String INVENTORY_NUMBER_VALUE = "//input[@id='gwt-debug-inventoryNumber-value']";
        public static final String LANGUAGES_CLIENT_INPUT = "//input[@id='gwt-debug-languages-client-input']";
        public static final String LANGUAGES_EN_INPUT = "//input[@id='gwt-debug-languages-en-input']";
        public static final String LANGUAGES_RU_INPUT = "//input[@id='gwt-debug-languages-ru-input']";
        public static final String LEFT_INPUT = "//input[@id='gwt-debug-LEFT-input']";
        public static final String LOGIN_VALUE = "//input[@id='gwt-debug-login-value']";
        public static final String MASS_CHECKBOX_INPUT = "//input[@id='gwt-debug-massCheckBox-input']";
        public static final String FULL_RELOAD_METAINFO_CHECKBOX_INPUT = "//input[@id='gwt-debug-fullReloadCheckbox"
                                                                         + "-value-input']";
        public static final String NAME_VALUE = "//input[@id='gwt-debug-name-value']";
        public static final String NEED_AUTHENTICATION_VALUE_INPUT = "//input[contains(@id,"
                                                                     + "'gwt-debug-needAuthentication') and contains"
                                                                     + "(@id, '-value-input')]";
        public static final String NEED_AUTH_VALUE_INPUT = String.format(ANY_INPUT_VALUE, "needAuth");
        public static final String NORMAL_FONTSIZE_INPUT = "//input[@id='gwt-debug-fontSize-normal-input']";
        public static final String OPERATOR_SCHEME3_INPUT = "//input[@id='gwt-debug-operator-scheme3-input']";
        public static final String OPERATOR_SYSTEM_DEFAULT_INPUT = "//input[@id='gwt-debug-operator-system#default"
                                                                   + "-input']";
        public static final String PASSWORD_VALUE = "//input[@id='gwt-debug-password-value']";
        public static final String PAST = "//input[@id='PAST']";
        public static final String PAST_AND_DISABLED = "//input[@id='PAST' and @disabled]";
        public static final String PORT_VALUE = "//input[@id='gwt-debug-port-value']";
        public static final String PROCESS_BATCH_SIZE_VALUE = "//input[@id='gwt-debug-processBatchSize-value']";
        public static final String PROXY_USE_SSL_VALUE_INPUT = "//input[contains(@id,'gwt-debug-proxyUseSSL') and "
                                                               + "contains(@id, '-value-input')]";
        public static final String RECEIVE_BATCH_SIZE_VALUE = "//input[@id='gwt-debug-receiveBatchSize-value']";
        public static final String RESPONSIBILITY_TRANSFER_TABLE_ENABLED_VALUE_INPUT = "//input[@id='gwt-debug"
                                                                                       +
                                                                                       "-responsibilityTransferTableEnabled-value-input']";
        public static final String SAVE_ORIGINAL_VALUE_INPUT = "//input[@id='gwt-debug-saveOriginal-value-input']";
        public static final String SEND_PARTITIAL_VALUE_INPUT = "//input[@id='gwt-debug-sendPartitial-value-input']";
        public static final String SERVER_ADDRESS_VALUE = "//input[@id='gwt-debug-serverAddress-value']";
        public static final String SERVER_VALUE = "//input[@id='gwt-debug-server-value']";
        public static final String SHARED_MAILBOX_VALUE = "//input[@id='gwt-debug-sharedMailbox-value']";
        public static final String SMALL_FONTSIZE_INPUT = "//input[@id='gwt-debug-fontSize-small-input']";
        public static final String SPAN_VALUE_WITHOUT_ID = "//span[@id='gwt-debug-value']";
        public static final String SSL_VALUE_INPUT = "//input[@id='gwt-debug-ssl-value-input']";
        public static final String SKIP_CERT_VERIFICATION_VALUE_INPUT = "//input[@id='gwt-debug-skipCertVerification"
                                                                        + "-value-input']";
        public static final String TIME_BOX = "//input[@id='gwt-debug-timeBox']";
        public static final String TITLE_VALUE = "//input[@id='gwt-debug-title-value']";
        public static final String TRANSLITERATE_SUBJECT_VALUE_INPUT = "//input[@id='gwt-debug-transliterateSubject"
                                                                       + "-value-input']";
        public static final String USE_PROXY_VALUE_INPUT = "//input[contains(@id,'gwt-debug-useProxy') and contains"
                                                           + "(@id, '-value-input')]";

        public static final String USE_BASIC_AUTH_VALUE_INPUT = "//input[@id='gwt-debug-sys_useBasicAuth-value-input']";
        public static final String USE_BASIC_AUTH_VALUE_INPUT_CONTAINS = "//input[contains"
                                                                         + "(@id, 'gwt-debug-sys_useBasicAuth') and "
                                                                         + "contains(@id, '-value-input')]";
        public static final String USE_SSL_VALUE_INPUT = "//input[@id='gwt-debug-useSSL-value-input']";
        public static final String USE_SSL_VALUE_INPUT_CONTAINS = "//input[contains(@id,'gwt-debug-useSSL') and "
                                                                  + "contains(@id, '-value-input')]";
        public static final String USE_QUICK_ADD_FORM = "//input[@id='gwt-debug-useQuickAddForm-value-input']";
        public static final String VALUE = "//input[@id='gwt-debug-value']";
        public static final String VALUE_INPUT = "//input[@id='gwt-debug-value-input']";
        public static final String IS_PLAN_VERSIONS_ALLOWED_VALUE_INPUT =
                "//input[@id='gwt-debug-isPlanVersionsAllowedDebugId-value-input']";
        public static final String DEPTH_ENVIRONMENT_VALUE_INPUT =
                "//input[@id='gwt-debug-depthEnvDebugId-value']";
        public static final String INPUT_CONTAINS = "//input[contains(@id, 'gwt-debug-%s')]";
        private static final String FLAT_VALUE_INPUT = "//input[@id='gwt-debug-flat-value-input']";
        private static final String WITH_FOLDERS_VALUE_INPUT = "//input[@id='gwt-debug-withFolders-value-input']";
    }

    /**
     * Класс для хранения xpath-ов, которые составлены из двух xpath-ов, второй обязательно '//input'
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class InputComplex
    {
        public static final String AGREEMENTS_VALUE = Div.AGREEMENTS_VALUE + Input.INPUT_PREFIX;
        public static final String AGREEMENT_VALUE = Div.AGREEMENT_VALUE + Input.INPUT_PREFIX;
        public static final String AGREEMENT_SERVICE_EDIT_PRS_VALUE = Div.AGREEMENT_SERVICE_EDIT_PRS_VALUE
                                                                      + Input.INPUT_PREFIX;
        public static final String AGREEMENT_SERVICE_PROPERTY_VALUE = Div.AGREEMENT_SERVICE_PROPERTY_VALUE
                                                                      + Input.INPUT_PREFIX;
        public static final String ANY = Any.ANY + Input.INPUT_PREFIX;
        public static final String ANY_VALUE = Any.ANY_VALUE + Input.INPUT_PREFIX;
        public static final String ANY_VALUE_CONTAINS = Any.ANY_VALUE_CONTAINS + Input.INPUT_PREFIX;
        public static final String ATTRIBUTE_SELECT_VALUE = Any.ATTRIBUTE_SELECT_VALUE + Input.INPUT_PREFIX;
        public static final String ATTRIBUTE_INPUT = Div.ATTRIBUTE_VALUE + Input.INPUT_PREFIX;
        public static final String AVAILABLE_CLASSES_VALUE = Div.AVAILABLE_CLASSES_VALUE + Input.INPUT_PREFIX;
        public static final String CALCULATE_STRATEGY_VALUE = Div.CALCULATE_STRATEGY_VALUE + Input.INPUT_PREFIX;
        public static final String CALL_CASES_VALUE = Div.CALL_CASES_VALUE + Input.INPUT_PREFIX;
        public static final String CASE_PROPERTY_VALUE = Any.CASE_PROPERTY_VALUE + Input.INPUT_PREFIX;
        public static final String CATEGORIES_VALUE = Div.CATEGORIES_VALUE + Input.INPUT_PREFIX;
        public static final String CLIENT_VALUE_ANY = Any.CLIENT_VALUE + Input.INPUT_PREFIX;
        public static final String CLIENT_VALUE_DIV = Div.CLIENT_VALUE + Input.INPUT_PREFIX;
        public static final String CODE_OF_CLOSING_VALUE = Div.CODE_OF_CLOSING_VALUE + Input.INPUT_PREFIX;
        public static final String COMPLEX_ANY_ATTR_GROUP_VALUE = Any.COMPLEX_ANY_ATTR_GROUP_VALUE + Input.INPUT_PREFIX;
        public static final String COMPLEX_ATTR_GROUP_VALUE = Any.COMPLEX_ATTR_GROUP_VALUE + Input.INPUT_PREFIX;
        public static final String COMPLEX_RELATION_VALUE = Any.COMPLEX_RELATION_VALUE + Input.INPUT_PREFIX;
        public static final String COMPUTABLE_VALUE = Any.COMPUTABLE_VALUE + Input.INPUT_PREFIX;
        public static final String CONTACT_ATTRIBUTES_VALUE = Div.CONTACT_ATTRIBUTES_VALUE + Input.INPUT_PREFIX;
        public static final String DATE_TIME_INTERVAL = Any.DATE_TIME_INTERVAL + Input.INPUT_PREFIX;
        public static final String DATE_TIME_RESTRICTION_ATTRIBUTE_VALUE = String.format(ANY_VALUE,
                AttributeConstant.DateTimeType.DATETIME_RESTRICTION_ATTRIBUTE);
        public static final String DATE_TIME_RESTRICTION_CONDITION_VALUE = String.format(ANY_VALUE,
                AttributeConstant.DateTimeType.DATETIME_RESTRICTION_CONDITION);
        public static final String DATE_TIME_RESTRICTION_TYPE_VALUE = String.format(ANY_VALUE,
                AttributeConstant.DateTimeType.DATETIME_RESTRICTION_TYPE);
        public static final String DAYS_VALUE = Div.DAYS_VALUE + Input.INPUT_PREFIX;
        public static final String DEFAULT_BY_SCRIPT_VALUE = Any.DEFAULT_BY_SCRIPT_VALUE + Input.INPUT_PREFIX;
        public static final String DEFAULT_VALUE_VALUE_ANY = Any.DEFAULT_VALUE_VALUE + Input.INPUT_PREFIX;
        public static final String DEFAULT_VALUE_VALUE_DIV = Div.DEFAULT_VALUE_VALUE + Input.INPUT_PREFIX;
        public static final String DESTINATION_VALUE_INPUT = String.format(Div.ID_PATTERN + Input.INPUT_PREFIX,
                Id.DESTINATION_PROPERTY_VALUE);
        public static final String DETERMINABLE_VALUE = Any.DETERMINABLE_VALUE + Input.INPUT_PREFIX;
        public static final String DETERMINER_VALUE = Any.DETERMINER_VALUE + Input.INPUT_PREFIX;
        public static final String DIRECT_LINK_TARGET_VALUE = Div.DIRECT_LINK_TARGET_VALUE + Input.INPUT_PREFIX;
        public static final String EDITABLE_IN_LISTS_VALUE = Any.EDITABLE_IN_LISTS_VALUE + Input.INPUT_PREFIX;
        public static final String EDITABLE_VALUE = Any.EDITABLE_VALUE + Input.INPUT_PREFIX;
        public static final String EMBEDDED_APPLICATION_VALUE = Any.EMBEDDED_APPLICATION_VALUE + Input.INPUT_PREFIX;
        public static final String EVENT_ACTION_EXCLUDE_AUTHOR_VALUE =
                Any.EVENT_ACTION_EXCLUDE_AUTHOR_VALUE + Input.INPUT_PREFIX;
        public static final String SKIP_IF_USER_HAS_ACTIVE_SESSION_VALUE =
                Any.SKIP_IF_USER_HAS_ACTIVE_SESSION_VALUE + Input.INPUT_PREFIX;
        public static final String EVENT_ACTION_USE_DEFAULT_MESSAGE_VALUE =
                Any.EVENT_ACTION_USE_DEFAULT_MESSAGE_VALUE + Input.INPUT_PREFIX;
        public static final String EXPORT_NDAP_VALUE = Any.EXPORT_NDAP_VALUE + Input.INPUT_PREFIX;
        public static final String FILTERED_BY_SCRIPT_VALUE = Any.FILTERED_BY_SCRIPT_VALUE + Input.INPUT_PREFIX;
        public static final String GRP_USERS_VALUE = Div.GRP_USERS_VALUE + Input.INPUT_PREFIX;
        public static final String HIDDEN_WHEN_EMPTY_VALUE = Any.HIDDEN_WHEN_EMPTY_VALUE + Input.INPUT_PREFIX;
        public static final String HIDDEN_WHEN_NO_POSSIBLE_VALUES_VALUE = Any.HIDDEN_WHEN_NO_POSSIBLE_VALUES_VALUE
                                                                          + Input.INPUT_PREFIX;
        public static final String IMPACT_VALUE = Div.IMPACT_VALUE + Input.INPUT_PREFIX;
        public static final String INTERVAL_AVAILABLE_UNITS_VALUE = Any.INTERVAL_AVAILABLE_UNITS_VALUE
                                                                    + Input.INPUT_PREFIX;
        public static final String LICENSE_VALUE = Div.LICENSE_VALUE + Input.INPUT_PREFIX;
        public static final String MAIL_PROCESSOR_VALUE = Div.MAIL_PROCESSOR_VALUE + Input.INPUT_PREFIX;
        public static final String METACLASS_VALUE = Div.METACLASS_VALUE + Input.INPUT_PREFIX;
        public static final String NEW_STATE_PROPERTY_VALUE = Any.NEW_STATE_PROPERTY_VALUE + Input.INPUT_PREFIX;
        public static final String PARENT_CLS_PROP_VALUE = Div.PARENT_CLS_PROP_VALUE + Input.INPUT_PREFIX;
        public static final String PERIOD_VALUE = Any.PERIOD_VALUE + Input.INPUT_PREFIX;
        public static final String PERMITTED_TYPES_VALUE = Div.PERMITTED_TYPES_VALUE + Input.INPUT_PREFIX;
        public static final String PRESENTATION_LIST_VALUE = Div.PRESENTATION_LIST_VALUE + Input.INPUT_PREFIX;
        public static final String PRIORITY_RULE_VALUE = Div.PRIORITY_RULE_VALUE + Input.INPUT_PREFIX;
        public static final String PRIORITY_VALUE = Div.PRIORITY_VALUE + Input.INPUT_PREFIX;
        public static final String PRIVATE_VALUE_CONTAINS = Div.PRIVATE_VALUE_CONTAINS + Input.INPUT_PREFIX;
        public static final String PROTOCOL_VALUE = Div.PROTOCOL_VALUE + Input.INPUT_PREFIX;
        public static final String PRS_INPUT_VALUE = Div.PRS_VALUE + Input.INPUT_PREFIX;
        public static final String RECEPIENT_AGREEMENTD_VALUE = Div.RECEPIENT_AGREEMENTD_VALUE + Input.INPUT_PREFIX;
        public static final String REQUIRED_IN_INTERFACE_VALUE = Any.REQUIRED_IN_INTERFACE_VALUE + Input.INPUT_PREFIX;
        public static final String REQUIRED_VALUE = Any.REQUIRED_VALUE + Input.INPUT_PREFIX;
        public static final String RESOLUTION_TIME_VALUE_ANY = Any.RESOLUTION_TIME_VALUE + Input.INPUT_PREFIX;
        public static final String RESOLUTION_TIME_VALUE_DIV = Div.RESOLUTION_TIME_VALUE + Input.INPUT_PREFIX;
        public static final String SECURITY_PROTOCOL_VALUE = Div.SECURITY_PROTOCOL_VALUE + Input.INPUT_PREFIX;
        public static final String CONNECTION_PROTOCOL_VALUE = Div.CONNECTION_PROTOCOL_VALUE + Input.INPUT_PREFIX;
        public static final String SERVER_CONFIG_VALUE = Div.SERVER_CONFIG_VALUE + Input.INPUT_PREFIX;
        public static final String SERVICES_VALUE = Div.SERVICES_VALUE + Input.INPUT_PREFIX;
        public static final String SERVICE_HOURS_VALUE = Div.SERVICE_HOURS_VALUE + Input.INPUT_PREFIX;
        public static final String SERVICE_TIME_VALUE = Div.SERVICE_TIME_VALUE + Input.INPUT_PREFIX;
        public static final String SHOW_PRESENTATION_VALUE = Div.SHOW_PRESENTATION_VALUE + Input.INPUT_PREFIX;
        public static final String SNMP_VERSION_VALUE = Div.SNMP_VERSION_VALUE + Input.INPUT_PREFIX;
        public static final String SUPPORT_HOURS_VALUE = Div.SUPPORT_HOURS_VALUE + Input.INPUT_PREFIX;
        public static final String TARGET_CATALOG_VALUE = Div.TARGET_CATALOG_VALUE + Input.INPUT_PREFIX;
        public static final String TARGET_CLASS_VALUE = Div.TARGET_CLASS_VALUE + Input.INPUT_PREFIX;
        public static final String TEMPLATES_LIST_VALUE = Any.TEMPLATES_LIST_VALUE + Input.INPUT_PREFIX;
        public static final String TIMER_DEFENITION_VALUE = Div.TIMER_DEFENITION_VALUE + Input.INPUT_PREFIX;
        public static final String TIMER_VALUE = Any.TIMER_VALUE + Input.INPUT_PREFIX;
        public static final String TIME_ZONE_VALUE = Div.TIME_ZONE_VALUE + Input.INPUT_PREFIX;
        public static final String TREE = Any.TREE + Input.INPUT_PREFIX;
        public static final String TYPE_VALUE = Div.TYPE_VALUE + Input.INPUT_PREFIX;
        public static final String UNIQUE_VALUE = Any.UNIQUE_VALUE + Input.INPUT_PREFIX;
        public static final String URGENCY_VALUE = Div.URGENCY_VALUE + Input.INPUT_PREFIX;
        public static final String VALUE = Div.VALUE + Input.INPUT_PREFIX;
        public static final String VALUE_WIDGET_VALUE = Div.VALUE_WIDGET_VALUE + Input.INPUT_PREFIX;
        public static final String TREE_VALUE = Complex.ANY_VALUE_ON_DIALOG + Input.INPUT_PREFIX;
        public static final String GATE_TYPE_VALUE_INPUT = Div.GATE_TYPE_VALUE + Input.INPUT_PREFIX;
    }

    /**
     * Класс для хранения xpath-ов, которые начинаются на различный префикс
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class Other
    {
        public static final String BUTTON = "//button";
        public static final String ANY_LABEL = "//label[@id='gwt-debug-%s-label']";
        public static final String ANY_VALUE_LABEL = "//label[@id='gwt-debug-%s-value-label']";
        public static final String ANY_VALUE_LABEL_CONTAINS = "//label[contains(@id,'gwt-debug-%s') and contains(@id,"
                                                              + " '-value-label')]";
        public static final String BODY = "//body";
        public static final String DEL_FILE_BUTTON = "//button[text()='x']";
        public static final String DESCRIPTION = "//textarea[@id='gwt-debug-description-value']";
        public static final String IFRAME_ID = "//iframe[contains(@id, '%s')]";
        public static final String MAIL_RULE_SCRIPT = "//textarea[@id='gwt-debug-script-value']";
        /** Главное окно */
        public static final String MAIN_WIN = "//body[@id='adminModule']";
        public static final String TABLE = "//table[@id='gwt-debug-table']";
        public static final String TABLE_ID = "//table[@id='gwt-debug-%s']";
        public static final String TOOL_BAR = "//table[@id='gwt-debug-toolBar']";
        public static final String TBODY = "//tbody";
        public static final String TD_DID_EMPLOYEE_PATTERN = "//td[@__did='employee@%s']";
        public static final String TD_ID_PATTERN = "//td[@id='%s']";
        public static final String TD_ID_PATTERN_WITH_TEXT = "//td[@id='%s' and text()='%s']";
        /** Поле ввода значения */
        public static final String TEXTAREA_ANY_VALUE = "//textarea[@id='gwt-debug-%s-value']";
        public static final String TR_DID_PATTERN = "//tr[@__did='%s']";
        public static final String TD_DID_PATTERN = "//td[@__did='%s']";
        public static final String TR_ID_PATTERN = "//tr[@id='%s']";
        public static final String TR_DID_PATTERN_TWO = "//tr[@__did='%s@%s']";
        public static final String TD_DID_PATTERN_TWO = "//td[@__did='%s@%s']";
        public static final String LAST_PATTERN = "[last()]";
        public static final String IMG_PREFIX = "//img";
        public static final String LABLE_PREFIX = "//label";
        public static final String PREFIX = "/..";
        public static final String TR_DEBUG_ID = "//tr[@debug-id='%s']";
        public static final String TABLE_VALUE = "//table[@id='gwt-debug-%s-value']";
        public static final String SYSICON_CODE = "sysicons";
        public static final String TR_BY_NUMBER = "//tr[%s]";
        public static final String SVG = "//*[name()='svg']";
        public static final String DISABLED_BUTTON = "//button[@id='%s' and @aria-disabled='true']";
        public static final String TD_BY_NUMBER = "//td[%s]";
        private static final String IMG_ID_PATTERN = "//img[@id='%s']";
        public static final String LOGIN_LOGO_IMG = String.format(IMG_ID_PATTERN, "logo");
        private static final String TEXTAREA = "//textarea";
        private static final String TR_GWT_ROW = "//tr[@__gwt_row]";
    }

    /**
     * Класс для хранения xpath-ов, которые составлены из нескольких xpath-ов, первый
     * обязательно "//div[@id='gwt-debug-PropertyDialogBox-content']"
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class PropertyDialogBoxContent
    {
        /** Выпадающий список "Атрибуты, доступные из системы мониторинга" на диалоговом окне. */
        public static final String RELATED_ATTRS_TO_EXPORT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                             + Any.RELATED_ATTRS_TO_EXPORT + Input.INPUT_PREFIX;
        /** Дерево "Объекты иерархии связаны с объектами списка через атрибут" на диалоговом окне */
        public static final String AFTER_HIERARCHY_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.AFTER_HIERARCHY_VALUE;
        /** Дерево выбора атрибутов связи на диалоговом окне. */
        public static final String ATTRIBUTE_CHAIN_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.ATTRIBUTE_CHAIN_VALUE;
        public static final String DETAILED_ATTRIBUTE_GROUP_VALUE =
                Div.PROPERTY_DIALOG_BOX_CONTENT + Any.DETAILED_ATTRIBUTE_GROUP_VALUE + Input.INPUT_PREFIX;
        public static final String FOR_ADD_ATTRIBUTE_GROUP_VALUE =
                Div.PROPERTY_DIALOG_BOX_CONTENT + Any.FOR_ADD_ATTRIBUTE_GROUP_VALUE + Input.INPUT_PREFIX;
        public static final String FOR_EDIT_ATTRIBUTE_GROUP_VALUE =
                Div.PROPERTY_DIALOG_BOX_CONTENT + Any.FOR_EDIT_ATTRIBUTE_GROUP_VALUE + Input.INPUT_PREFIX;
        public static final String COMMENT_ATTRIBUTE_GROUP_VALUE =
                Div.PROPERTY_DIALOG_BOX_CONTENT + Any.COMMENT_ATTRIBUTE_GROUP_VALUE + Input.INPUT_PREFIX;
        /** Выпадающий список "Группа"(группа атрибутов) на диалоговом окне. */
        public static final String ATTRIBUTE_GROUP_LIST_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                      + Any.ATTRIBUTE_GROUP_LIST_VALUE
                                                                      + Input.INPUT_PREFIX;
        /** Выпадающий список "Группа"(группа атрибутов) на диалоговом окне. */
        public static final String ATTRIBUTE_GROUP_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                 + Any.ATTRIBUTE_GROUP_VALUE + Input.INPUT_PREFIX;
        /** Выпадающий список "Атрибут" на диалоговом окне. */
        public static final String ATTRIBUTE_VALUE_INPUT_ON_FORM = Div.PROPERTY_DIALOG_BOX_CONTENT + Div.ATTRIBUTE_VALUE
                                                                   + Input.INPUT_PREFIX;
        /** Значение "типа атрибута" на диалоговом окне. */
        public static final String ATTR_TYPE_CAPTION = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.ATTR_TYPE_CAPTION;
        /** Выпадающий список выбора типа атрибутов на диалоговом окне. */
        public static final String ATTR_TYPE_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.ATTR_TYPE_VALUE
                                                           + Input.INPUT_PREFIX;
        /** Дерево "Построить иерархию объектов (вниз), начиная с объекта" на диалоговом окне */
        public static final String BEFORE_HIERARCHY_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                            + Any.BEFORE_HIERARCHY_VALUE;
        /** Поле ввода "Название" на диалоговом окне. */
        public static final String CAPTION_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.CAPTION_VALUE;
        /** Выпадающий список "Типы объектов" на диалоговом окне. */
        public static final String CASE_LIST_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.CASE_LIST_VALUE
                                                           + Input.INPUT_PREFIX;
        /** Значение "Класс объектов списка" на диалоговом окне */
        public static final String CLASS_LIST_CAPTION = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.CLASS_LIST_CAPTION
                                                        + Span.SPAN_PREFIX;
        /** Выпадающий список "Класс объектов" на диалоговом окне. */
        public static final String CLASS_LIST_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.CLASS_LIST_VALUE
                                                            + Input.INPUT_PREFIX;
        /** Поле ввода "Код" на диалоговом окне. */
        public static final String CODE_VALUE_OR_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                         + "//*[@id='gwt-debug-code-value' or @id='gwt-debug-value']";
        /** Чекбокс "По умолчанию" на диалоговом окне. */
        public static final String DEFAULT_VALUE_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                               + Any.DEFAULT_VALUE_VALUE_INPUT;
        /** Радиобатон "По умолчанию" на диалоговом окне. */
        public static final String DEFAULT_VALUE_VALUE_ON_FORM = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                 + Any.DEFAULT_VALUE_VALUE;
        /** Поле ввода "Описание" на диалоговом окне. */
        public static final String DESCR_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.DESCRIPTION_VALUE;
        /** Значение свойства расположенного на диалоговом окне */
        public static final String DIALOG_CONTENT_PROPERTY = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.ANY_VALUE;
        public static final String DIALOG_CONTENT_PROPERTY_CONTAINS =
                Div.PROPERTY_DIALOG_BOX_CONTENT + Any.ANY_VALUE_CONTAINS;
        /** Input свойства расположенного на диалоговом окне */
        public static final String DIALOG_CONTENT_INPUT = DIALOG_CONTENT_PROPERTY + Input.INPUT_PREFIX;
        /** Выпадающий список выбора представление для редактирования атрибутов на диалоговом окне */
        public static final String EDIT_PRESENTATION_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                   + Any.EDIT_PRESENTATION_VALUE + Input.INPUT_PREFIX;
        public static final String EMPL_ATTR_GROUP = Div.PROPERTY_DIALOG_BOX_CONTENT + Div.EMPL_ATTRIBUTE_GROUP_VALUE
                                                     + Input.INPUT_PREFIX;
        /** Выпадающий список "В иерархии потомки связаны с предками через атрибут" на диалоговом окне */
        public static final String HIERARCHY_ATTRIBUTE_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                     + Any.HIERARCHY_ATTRIBUTE_VALUE
                                                                     + Input.INPUT_PREFIX;
        /** Значение "Класс объектов иерархии" на диалоговом окне */
        public static final String HIERARCHY_CLASS_CAPTION = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                             + Any.HIERARCHY_CLASS_CAPTION + Span.SPAN_PREFIX;
        /** Кнопка "Обзор..." на диалоговом окне. */
        public static final String ICON_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.ICON_VALUE;
        /** Чекбокс "Наследовать параметры" на диалоговом окне. */
        public static final String INHERIT_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.INHERIT_VALUE;
        /** Поле ввода "Описание" на диалоговом окне. */
        public static final String INPUT_FIELD_DESCRIPTION = Div.PROPERTY_DIALOG_BOX_CONTENT + Other.DESCRIPTION;
        public static final String INPUT_FLAT_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT + Input.FLAT_VALUE_INPUT;
        public static final String INPUT_WITH_FOLDERS_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                    + Input.WITH_FOLDERS_VALUE_INPUT;
        public static final String LEVEL_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.LEVEL_VALUE;
        public static final String OU_ATTR_GROUP = Div.PROPERTY_DIALOG_BOX_CONTENT + Div.OU_ATTRIBUTE_GROUP_VALUE
                                                   + Input.INPUT_PREFIX;
        /** Поле ввода "Родитель" на диалоговом окне. */
        public static final String PARENT_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.PARENT_VALUE
                                                        + Input.INPUT_PREFIX;
        /** Выпадающий список "Расположение" на диалоговом окне. */
        public static final String POSITION_VALUE_INPUT_ON_FORM = Div.PROPERTY_DIALOG_BOX_CONTENT + Div.POSITION_VALUE
                                                                  + Input.INPUT_PREFIX;
        /** Выпадающий список "Представление" на диалоговом окне. */
        public static final String PRESENTATION_LIST_VALUE_INPUT_ON_FORM = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                           + InputComplex.PRESENTATION_LIST_VALUE;
        /** Выпадающий список "Представление" на диалоговом окне. */
        public static final String PRESENTATION_TYPE_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                   + Div.PRESENTATION_TYPE_VALUE + Input.INPUT_PREFIX;
        /** Значение "Код" на диалоговом окне. */
        public static final String PROPERTY_DIALOG_BOX_CODE_CAPTION = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                      + Any.CODE_CAPTION;
        /** Выпадающий список выбора атрибута связанного класса на диалоговом окне. */
        public static final String RELATED_OBJECT_ATTRIBUTE_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                          + Any.RELATED_OBJECT_ATTRIBUTE_VALUE
                                                                          + Input.INPUT_PREFIX;
        /** Поле ввода "Скрипт" на диалоговом окне. */
        public static final String SCRIPT_VALUE_ON_FORM = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.SCRIPT_VALUE;
        /** Выпадающий список "Группа"(группа атрибутов) на диалоговом окне. */
        public static final String SELECT_ATTRIBUTE_GROUP_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                        + Any.SELECT_ATTRIBUTE_GROUP_VALUE
                                                                        + Input.INPUT_PREFIX;
        /** Чекбокс "Отображать описание атрибутов на форме добавления" на диалоговом окне. */
        public static final String SHOW_ATTR_DESCRIPTION_FOR_ADD_FORM_VALUE_ON_FORM = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                                      + Any.SHOW_ATTR_DESCRIPTION_FOR_ADD_FORM_VALUE
                                                                                      + Input.INPUT_PREFIX;
        /** Чекбокс "Отображать описание атрибутов на форме редактирования" на диалоговом окне. */
        public static final String SHOW_ATTR_DESCRIPTION_FOR_EDIT_FORM_VALUE_ON_FORM = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                                       + Any.SHOW_ATTR_DESCRIPTION_FOR_EDIT_FORM_VALUE
                                                                                       + Input.INPUT_PREFIX;
        /** Чекбокс "Отображать название" на диалоговом окне. */
        public static final String SHOW_CAPTION_VALUE_ON_FORM = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                + Any.SHOW_CAPTION_VALUE;
        /** Чекбокс "Свёрнут по умолчанию" на диалоговом окне. */
        public static final String COLLAPSE_BY_DEFAULT_ON_FORM = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                 + Any.COLLAPSE_BY_DEFAULT_VALUE;
        /** Чекбокс "Отображать название" на диалоговом окне. */
        public static final String SHOW_CAPTION_VALUE_INPUT = SHOW_CAPTION_VALUE_ON_FORM + Input.INPUT_PREFIX;
        /** Чекбокс "Свёрнут по умолчанию" на диалоговом окне. */
        public static final String COLLAPSE_BY_DEFAULT_INPUT = COLLAPSE_BY_DEFAULT_ON_FORM + Input.INPUT_PREFIX;
        /** Чекбокс "Показывать в списке объекты, вложенные во вложенные" на диалоговом окне */
        public static final String SHOW_NESTED_IN_NESTED_VALUE_ON_FORM = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                         + Any.SHOW_NESTED_IN_NESTED_VALUE;
        public static final String SHOW_NESTED_IN_NESTED_VALUE_INPUT = SHOW_NESTED_IN_NESTED_VALUE_ON_FORM
                                                                       + Input.INPUT_PREFIX;
        public static final String SHOW_PRESENTATION_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                   + Any.SHOW_PRESENTATION_VALUE + Input.INPUT_PREFIX;
        /** Чекбокс "Показывать в списке объекты, связанные со вложенными" на диалоговом окне */
        public static final String SHOW_RELATED_WITH_NESTED_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                    + Any.SHOW_RELATED_WITH_NESTED_VALUE;
        public static final String SHOW_RELATED_WITH_NESTED_VALUE_INPUT = SHOW_RELATED_WITH_NESTED_VALUE
                                                                          + Input.INPUT_PREFIX;
        /** Выпадающий список выбора справочника для списка выбора строкового атрибута на диалоговом окне */
        public static final String SUGGEST_CATALOG_VALUE_INPUT = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                                 + Any.SUGGEST_CATALOG_VALUE + Input.INPUT_PREFIX;
        /** Значение "Справочника" на диалоговом окне. */
        public static final String TARGET_CATALOG_CAPTION = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                            + Any.TARGET_CATALOG_CAPTION;
        public static final String TEAM_ATTRIBUTE_GROUP = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                          + Div.TEAM_ATTRIBUTE_GROUP_VALUE + Input.INPUT_PREFIX;
        /** Поле ввода "Название" на диалоговом окне. */
        public static final String TITLE_VALUE_ON_DIALOG = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.TITLE_VALUE;
        /** Выпадающий список выбора типа чего-либо на диалоговом окне. */
        public static final String TYPE_LIST_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.TYPE_VALUE
                                                     + Input.INPUT_PREFIX;
        public static final String X_ATTR_EDIT_LIST = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                      + "//*[@id='gwt-debug-editPresentation-value']";
        public static final String X_ATTR_EDIT_LIST_VALUE = X_ATTR_EDIT_LIST + Input.INPUT_PREFIX;

        public static final String STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE_VALUE_INPUT =
                Any.STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE_VALUE + Input.INPUT_PREFIX;

        /** Выпадающий список выбора типов объектов для редактирования атрибутов на диалоговом окне */
        public static final String X_PERMITTED_TYPES_VALUE = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                             + Any.PERMITTED_TYPES_VALUE;
        /** Значение "Вложен в" на диалоговом окне. */
        public static final String PARENT_FIELD = Div.PROPERTY_DIALOG_BOX_CONTENT
                                                  + Any.PARENT_CODE;
        /** Выпадающий список "Профили" на диалоговом окне. */
        private static final String PROFILES_VALUE_ON_FORM = Div.PROPERTY_DIALOG_BOX_CONTENT + Any.PROFILES_VALUE;
        /** Выпадающий список "Профили" на диалоговом окне. */
        public static final String PROFILES_VALUE_INPUT = PROFILES_VALUE_ON_FORM + Input.INPUT_PREFIX;
        /** Выпадающий список "Профили режима планирования" на диалоговом окне. */
        private static final String VERS_PROFILES_VALUE_ON_FORM =
                Div.PROPERTY_DIALOG_BOX_CONTENT + Any.VERS_PROFILES_VALUE;
        /** Выпадающий список "Профили режима планирования" на диалоговом окне. */
        public static final String VERS_PROFILES_VALUE_INPUT = VERS_PROFILES_VALUE_ON_FORM + Input.INPUT_PREFIX;
    }

    /**
     * Класс для хранения xpath-ов, которые начинаются на префикс '//span'
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class Span
    {
        public static final String ANY = "//span[@id='gwt-debug-%s']";
        public static final String ANY_ICON = "//span[@_code='%s']";
        public static final String ANY_VALUE = "//span[@id='gwt-debug-%s-value']";
        public static final String ASTATE_DOT = "//span[@_code='stateDot']";
        public static final String ASTATE_IN_EDIT = "//span[@_code='inStateEdit']";
        public static final String ASTATE_IN_HIDE = "//span[@_code='inStateNotShow']";
        public static final String ASTATE_IN_REQUIRED = "//span[@_code='inStateEditStar']";
        public static final String ASTATE_POST_FILL_NORMAL = "//span[@_code='postFill']";
        public static final String ASTATE_POST_FILL_STAR = "//span[@_code='postFillStar']";
        public static final String ASTATE_PRE_FILL_NORMAL = "//span[@_code='preFill']";
        public static final String ASTATE_PRE_FILL_STAR_NORMAL = "//span[@_code='preFillStar']";
        public static final String CHANGE_PASSWORD_ICON = "//span[@_code='changePassword']";
        public static final String CLEAR_ICON = String.format(ANY_ICON, "clear");
        public static final String CLOSE2 = String.format(ANY_ICON, "close2");
        public static final String CLOSE_ICON = String.format(ANY_ICON, "close");
        public static final String COPY_TO_TEMPLATE = String.format(ANY_ICON, "copyToTemplate");
        public static final String DEAD_LINE_IN_PAUSED_TIMER = "//span[contains(@class, 'deadLineInPausedTimer')]";
        public static final String DELETE_ICON = String.format(ANY_ICON, "delete"); //переименовать
        public static final String DELETE_ICON_CODE = "delete_icon";
        public static final String DEL_2_ICON = String.format(ANY_ICON, "del2");
        public static final String DEL_ICON = String.format(ANY_ICON, "del");
        public static final String DEL_LINK_ICON = String.format(ANY_ICON, "delLink");
        public static final String DOWN_ICON = String.format(ANY_ICON, "down");
        public static final String EDIT_BO_CARD_CAPTION_ICON = "//span[@id='gwt-debug-objectCardCaptionIcon']";
        public static final String EDIT_ICON = String.format(ANY_ICON, "edit");
        public static final String SWITCH_ICON = String.format(Span.ANY_ICON, "switch");
        public static final String EDIT_COMMENT_ICON = String.format(ANY_ICON, "editComment");
        public static final String EMPTY_ICON = String.format(ANY_ICON, Constant.NO);
        public static final String EXP_ICON = String.format(ANY_ICON, "exp");
        public static final String GRANT_ACCESS_ICON = "//span[@_code='grantAccess']";
        public static final String ACTIVE_ICON_PATTERN = "//div[@id='gwt-debug-%s'][contains(@class, 'switched')]";
        public static final String IMG_DOT = "//span[@id='gwt-debug-%s.%s' and @_code='dot']";
        public static final String IMG_NO = "//span[@id='gwt-debug-%s.%s' and @_code='" + Constant.NO + "']";
        public static final String IMG_TICK = "//span[@id='gwt-debug-%s.%s' and @_code='tick']";
        public static final String MASS_ICON = String.format(ANY_ICON, "massAction");
        public static final String SPAN_CLASS = "//span[@class='%s']";
        public static final String SPAN_CLASS_TITLE = "//span[@class='%s' and @title='%s']";
        public static final String CONTAINS_CLASS = "//span[contains(@class, '%s')]";
        public static final String CONTAINS_TEXT = "//span[contains(text(), '%s')]";
        public static final String MULTI_SELECT_LEFT = String.format(CONTAINS_CLASS, "arrowLeft");
        public static final String MULTI_SELECT_RIGHT = String.format(CONTAINS_CLASS, "arrowRight");
        public static final String REFRESH_ICON = String.format(ANY_ICON, "refresh");
        public static final String RESTORE_ICON = String.format(ANY_ICON, "restore");
        public static final String RULES_SETTINGS_OBJ = "//span[@id='%s.objects']";
        public static final String RUN_SCH_ICON = String.format(ANY_ICON, "runSchTask");
        public static final String PIN_ICON = String.format(ANY_ICON, "pin");
        public static final String UNPIN_ICON = String.format(ANY_ICON, "unpin");
        public static final String SPAN_CODE = "//span[@_code]";
        public static final String SPAN_ID_PATTERN = "//span[@id='%s']";
        public static final String INVALID_CONNECTION = String.format(Span.SPAN_ID_PATTERN,
                Constant.INVALID_CONNECTION_IMG_ID);
        public static final String SPAN_PREFIX = "//span";
        public static final String SPAN_TEXT_PATTERN = "//span[text()='%s']";
        public static final String SPAN_TITLE_PATTERN = "//span[@title='%s']";
        public static final String SWITCH_OFF_ICON = String.format(ANY_ICON, "switchOff");
        public static final String SWITCH_ON_ICON = String.format(ANY_ICON, "switchOn");
        public static final String UNMASS_ICON = String.format(ANY_ICON, "unmassAction");
        public static final String UP_ICON = String.format(ANY_ICON, "up");
        public static final String YES_ICON = String.format(ANY_ICON, Constant.YES);
        public static final String INLINE_BADGE_CONTAINS_TEXT = "//span[contains(text(),'%s')]";
        public static final String INLINE_BADGE_CONTAINS_ANY = "//span[contains(text(),'%s')"
                                                               + " or contains(text(),'%s')]";
        private static final String REMOVE_ICON = String.format(ANY_ICON, "remove");
    }

    /**
     * Класс для хранения составных xpath-ов, не для всех частей есть константы
     * <AUTHOR>
     * @since 26.06.2019
     */
    public static class SpecificComplex
    {
        public static final String SELECTED_TAB =
                "//*[contains(@class, 'gwt-TabLayoutPanelTab') and contains(@class, 'selected')]"
                + Any.ANY;
        public static final String IMPORT_EDIT_CONFIG = Any.XML_CONFIG_VALUE
                                                        + "//*[contains(@class, 'cm-attribute') and text()"
                                                        + "='threads-number']"
                                                        + "/following-sibling::*[@class='cm-string']";
        public static final String ACTION_ICON_IN_TABLE = "//*[contains(@href, '%s')]/../..//*[contains(@id, "
                                                          + "'gwt-debug-userEvent')]";
        /** Надпись "Классы объектов", присутствующая только в ИА */
        public static final String ADMIN_UI = "//div[@class='mainContentPanel']//div[text()='Классы объектов']";
        //Все элементы, по которым можно пройти табуляцией
        public static final String ALL_TAB_ELEMENTS = "//*[@tabindex > 0]/ancestor-or-self::*[@id and "
                                                      + "@id!='gwt-debug-inputField'][1]";
        public static final String ANY_FAST_BUTTON_PTRN = "//div[contains(@id,'gwt-debug-ToolPanel')]"
                                                          + "//div[contains(@id,'gwt-debug-transitionChangeState.')]";
        public static final String ANY_VALUE_ON_MODAL_FORM = "//td[@class='dialogMiddleCenter']" + Any.ANY_VALUE;
        public static final String ARCHIVED_TR_DID_PATTERN = "//tr[@__did='%s' and contains(@class, "
                                                             + "'tableRowTextGray')]";
        public static final String ANY_VALUE_TD_CLASS = "//td[@class='%s']";
        public static final String ARCHIVED_TR_DID_PATTERN_WITH_UNDERSCORE = "//tr[@__did='%s' and contains(@class, "
                                                                             + "'table__row_text-gray')]";
        public static final String ASTATE_BUTTON_REFRESH_SETTINGS = Div.REFRESH
                                                                    + "//*[text()='Сбросить настройки']/../..";
        public static final String ATTENTION2 = Any.ATTENTION + "//*[contains(text(),'Внимание!')]";
        public static final String ATTR_INPUT_VALUE = Any.ANY_VALUE + "//descendant-or-self::input";
        public static final String ATTR_INPUT_VALUE_CONTAINS = Any.ANY_VALUE_CONTAINS + "//descendant-or-self::input";
        public static final String BREAD_CRUMB_TREE_ID = "//tr[@__code='casesOfbreadCrumb']/td";
        public static final String BUTTON_RENAME_FORM = Div.EDIT + "//*[text()='Переименовать форму']/../..";
        public static final String CASES_TYPES_ATTR_EDIT_ICON = "//*[@id='gwt-debug-callCases.editButton']/span";
        public static final String CONNECTION_CELL = "//tr[.//a[text()='%s']]/td/div";
        public static final String CONNECTION_STATUS = CONNECTION_CELL + "/span[@id='lastConnectionStatus']/span";
        public static final String CONTACT_FACE_SELECT_INPUT = String.format(InputComplex.ANY_VALUE,
                "contactFaceSelect");
        /** Xpath до всех названий атрибутов в контенте */
        public static final String CONTENT_ATTRIBUTE_CAPTIONS = Div.ID_PATTERN + "//div[contains(@id, '-caption')]";
        /** Пиктограмма Разорвать связь */
        public static final String DELETE_RELATION_ICON = Span.DEL_LINK_ICON + "[contains(@id,'%s')]";
        /** Кнопка Удаления статуса */
        public static final String DELETE_STATE_BTN = "//tr[@__did='%1$s']" + Any.DELETE_CONTAINS + "|"
                                                      + "//tr[@__did='%1$s']" + Span.DELETE_ICON;
        public static final String EMPTY_SELECTION_SPAN_NOT_SELECTED = Any.EMPTY_SELECTION_ITEM + " | "
                                                                       + String.format(Span.SPAN_TEXT_PATTERN,
                "[не указано]");
        public static final String FAST_BUTTON_PTRN = "//div[contains(@id,'gwt-debug-ToolPanel')]"
                                                      + "//*[@id='gwt-debug-transitionChangeState.%s']";

        public static final String FILE_VALUE = "//div[@id='gwt-debug-reportFile-value']" + A.A_PREFIX;
        /** Условие фильтрации */
        public static final String FILTER_CONDITION = "//div[@id='gwt-debug-condition']" + Span.SPAN_PREFIX;
        public static final String FILTER_SETTINGS = "//*[@__did='%s']" + "//*[@__code='editFilterSettings']";
        public static final String FIRST_COLUMN_ON_SHOW_GROUP_USAGE_FORM = Complex.SHOW_USAGE_ATTR_GROUP_ROW + "/td[1]";
        public static final String FIRST_COLUMN_ON_SHOW_USAGE_FORM = Complex.SHOW_USAGE_ATTR_ROW + "/td[1]";
        public static final String IMAGE_PNG_BASE64 = "//a[@title='Перейти на домашнюю страницу']"
                                                      + "//img[contains(@src,'data:image/png;base64')]";
        /** Caption ввода атрибута, определенного его кодом,на форме расширенного поиска. (в том числе возвращает
         * название класса) */
        public static final String INPUT_FIELD_SOME_ATTR_CAPTION =
                "//input[contains(@id,'gwt-debug') and contains(@id, 'value')]"
                + "/ancestor::div[@class='propertyContainer']/*//div[contains(@id,'gwt-debug') and contains"
                + "(@id, 'caption')]";
        public static final String INTERFACE_AND_NAVIGATION = "admin/#interface:!{\"tab\":\"%s\"}";
        public static final String IS_ENABLED_RECALC_ON_RESOLUTION_TIME_CHANGE_VALUE = "//div[@id='gwt-debug"
                                                                                       +
                                                                                       "-enableRecalcOnResolutionTimeChange-value']/span";
        public static final String LAST_CONNECTION_STATUS_VALUE = "//div[@id='gwt-debug-lastConnectionStatus-value']"
                                                                  + "//span[@_code]";
        public static final String LAST_CONNECTION_STATUS_VALUE_TR = "//div[@id='gwt-debug-lastConnectionStatus-value']"
                                                                     + "//tr/td[2]";
        public static final String LAST_CONNECTION_STATUS_VALUE_TR_TD =
                "//div[@id='gwt-debug-lastConnectionStatus-value']"
                + "//tr/td[1]";
        public static final String LAST_ELEMENT_PATTERN = "(%s)" + "[last()]";
        public static final String LIST_SCHED_TASK_TITLE = Other.TR_DID_PATTERN + "/td[2]";
        public static final String LOCATION_PROPERTY_CAPTION = "//div[@id='gwt-debug-locationProperty-caption']"
                                                               + Span.SPAN_PREFIX;
        public static final String LOGO_UUID_FILE = "//a[@title='Перейти на домашнюю страницу']//img[contains(@src,"
                                                    + "'images/logo?uuid=file$')]";
        public static final String SCHEMA_OPTIMIZATION_PROCESS_BUTTON_RUN_IT_NOW_XPATH = Any.SCHEMA_OPTIMIZATION_PROCESS
                                                                                         + "//*[@id='gwt-debug"
                                                                                         + "-runItNow']";
        public static final String SCHEMA_OPTIMIZATION_PROCESS_BUTTON_RUN_IT_NOW_XPATH_DISABLE =
                Any.SCHEMA_OPTIMIZATION_PROCESS
                + "//*[@id='gwt-debug-runItNow' and contains(@class, 'buttonDisabled')]";
        public static final String SCHEMA_OPTIMIZATION_PROCESS_BUTTON_RUN_IT_NOW_XPATH_ENABLE =
                Any.SCHEMA_OPTIMIZATION_PROCESS
                + "//*[@id='gwt-debug-runItNow' and not(contains(@class, 'buttonDisabled'))]";
        public static final String SCHEMA_OPTIMIZATION_PROCESS_FIELD_STATE_VALUE_XPATH = Any.SCHEMA_OPTIMIZATION_PROCESS
                                                                                         + "//*[@id='gwt-debug-state"
                                                                                         + "-value']";
        public static final String SCHEMA_OPTIMIZATION_PROCESS_FIELD_COUNT_VALUE_XPATH = Any.SCHEMA_OPTIMIZATION_PROCESS
                                                                                         + "//*[@id='gwt-debug-count"
                                                                                         + "-value']";

        public static final String READ_ONLY_CLUSTER_WRITERS_SELECT_INPUT =
                GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + "//*[@id='gwt-debug"
                + "-writers-value']//input";

        public static final String READ_ONLY_CLUSTER_WRITERS_VALUE_XPATH = Any.READ_ONLY_CLUSTER_SETTINGS
                                                                           + "//*[@id='gwt-debug-writers-value']";
        public static final String MAINTENANCE_BUTTON_RUN_IT_NOW_XPATH = Any.MAINTENANCE_SETTINGS
                                                                         + "//*[@id='gwt-debug-runItNow']";
        public static final String MAINTENANCE_BUTTON_WAIT_XPATH = Any.MAINTENANCE_SETTINGS
                                                                   + "//*[@id='gwt-debug-wait']";
        /* Пути относящиеся к элементам на диалоговом окне   */
        public static final String MAINTENANCE_BUTTON_TURN_OFF_XPATH = Any.MAINTENANCE_SETTINGS
                                                                       + "//*[@id='gwt-debug-switch']";
        public static final String MAINTENANCE_CANCEL_BUTTON = Any.MAINTENANCE_SETTINGS + "//*[@id='gwt-debug-del']";
        public static final String MAINTENANCE_FIELD_STATE_VALUE_XPATH = Any.MAINTENANCE_SETTINGS
                                                                         + "//*[@id='gwt-debug-state-value']";
        public static final String METACLASS_SELECT = "//div[@id='gwt-debug-caseProperty-value']" + Input.INPUT_PREFIX;
        public static final String METACLASS_SELECT_VALUE = "//div[@id='gwt-debug-caseProperty-value']"
                                                            + "//input | //td[@id='gwt-debug-body-2']//div[@id='gwt"
                                                            + "-debug-value']"
                                                            + Input.INPUT_PREFIX;
        public static final String METAINFO_MESSAGE_XPATH = Div.INFO_DIALOG
                                                            + "//div[@id='gwt-debug-dialogWidgetDescriptionElement' "
                                                            + "and text()=\"Метаинформация загружена\"]";
        public static final String METAINFO_WITH_PROBLEMS_MESSAGE_XPATH = Div.INFO_DIALOG
                                                                          + "//div[@id='gwt-debug"
                                                                          + "-dialogWidgetDescriptionElement' and "
                                                                          + "text()=\"Метаинформация загружена с "
                                                                          + "замечаниями (см. лог приложения).\"]";
        public static final String MOBILE_CONTENT_HEADER_XPATH =
                Any.MOBILE_OBJECT_CARD_CONTENTS_PRESENTER + "//div[@class='mobileCardHeader']"
                + "//div[@class='gMh1' and text()='%s']";
        public static final String MOBILE_FLOW_CONTENT_POPUP_COMMAND_BY_ID_XPATH = "//*[contains(@class, "
                                                                                   + "'mobileFlowContentPopup')"
                                                                                   + "]//div/span[@_code='%s']/span"
                                                                                   + "[@class='icon-title-text']";
        public static final String MOBILE_FLOW_CONTENT_POPUP_COMMAND_XPATH = "//*[contains(@class, "
                                                                             + "'mobileFlowContentPopup')"
                                                                             + "]//div/span[@_code]";
        public static final String MOBILE_PROPERTIES_LIST_ATTRIBUTE_TITLE_XPATH = Any.MOBILE_PROPERTIES_LIST_ANY
                                                                                  + "//*[@id='gwt-debug-title.%s']";
        public static final String MOBILE_PROPERTIES_LIST_ATTRIBUTE_VALUE_XPATH = Any.MOBILE_PROPERTIES_LIST_ANY
                                                                                  + "//*[@id='gwt-debug-value.%s']";
        /** Система монторинга */
        public static final String MOVE_DOWN = Other.TR_DID_PATTERN + "//*[@_code='down']"; // fontIcon
        /** Кнопки перемещения статуса вврх\вниз в списке статусов */
        public static final String MOVE_UP = "//tr[contains(@__did,'%s')]" + "//*[@_code='up']"; // fontIcon
        public static final String NEW_REPORT_CURRENT_PARAMS = Div.CREATE_NEW;
        public static final String ONE_COPY_A = "//div[@id='gwt-debug-oneCopy-value']" + A.A_PREFIX;
        public static final String PARAMETERS_LABEL_REPORT_CONTENT = Div.REPORT_CONTENT_ANY
                                                                     + "//div[@id='gwt-debug-parametersLabel']";
        public static final String PAUSE_CONDITION_SCRIPT = "//div[@id='gwt-debug-pauseConditionScript-value']/..";
        /** Шаблон для доступа к элементам выпадающего списка по признаку содержащемуся в id */
        public static final String POPUP_SELECT_CONTAINS = Any.POPUP_LIST_SELECT + "//*[contains(@id,'%s')]";
        public static final String POPUP_SELECT_ELEMENT = Any.POPUP_LIST_SELECT + "//*[@id]";
        public static final String POPUP_SELECT_ENDS_WITH = Any.POPUP_LIST_SELECT
                                                            + "//*[substring(@id, string-length(@id)-string-length"
                                                            + "('%1$s')+1)='%1$s']";
        /** Поле профили */
        public static final String PROFILES_VALUE = Div.PROPERTY_DIALOG_BOX + "//div[@id='gwt-debug-profiles-value']"
                                                    + Input.INPUT_PREFIX;
        public static final String QUESTION_DIALOG_BTN_NO = Any.QUESTION_DIALOG + "//div[@id='gwt-debug-no']";
        public static final String RADIOBUTTON_NO = "//span[contains(@class, 'gwt-RadioButton')]/label[text()='нет']";
        public static final String RADIOBUTTON_YES = "//span[contains(@class, 'gwt-RadioButton')]/label[text()='да']";
        /** Кнопка обновления отчета на контенте */
        public static final String REFRESH_REPORT_BUTTON = Div.ID_PATTERN
                                                           + "//div[contains(@id, 'gwt-debug-refreshReport')]";
        public static final String REPORT_LIST_RARAM_VALUE = Other.TBODY + "//td[@id='%s' and contains(.,'$')]";
        public static final String RESPONSIBLE_TAKE_LINK_PATTERN = Div.PROPERTY_DIALOG_BOX
                                                                   + "//*[@id='gwt-debug-take']/a[text()='%s']";
        public static final String RESPONSIBLE_VALUE_INPUT = "//div[@id='gwt-debug-responsibleInput']"
                                                             + Input.INPUT_PREFIX + " | "
                                                             + "//div[@id='gwt-debug-responsible-value']"
                                                             + Input.INPUT_PREFIX;
        public static final String RESTORE_VALUES_BUTTON = "(" + Any.RESTORE_VALUES + Div.FEATURE_PANEL
                                                           + Span.SPAN_PREFIX + ")" + "";
        public static final String RESUME_CONDITION_SCRIPT = "//div[@id='gwt-debug-resumeConditionScript-value']"
                                                             + Other.PREFIX;
        public static final String RS_TEXT_ROW = "//*[contains(@id,'Row-')]" + "//td[1]";
        public static final String SECOND_COLUMN_ON_SHOW_GROUP_USAGE_FORM = Complex.SHOW_USAGE_ATTR_GROUP_ROW
                                                                            + "/td[2]";
        public static final String SECOND_COLUMN_ON_SHOW_USAGE_FORM = Complex.SHOW_USAGE_ATTR_ROW + "/td[2]";
        public static final String SKIP_CERT_VERIFICATION_ELEMENT = "//div[@id='gwt-debug-skipCertVerification-value']";
        public static final String SOURCE_LEFT_ELEMENT = Div.SOURCE_LEFT + "//div[@__did and position()=%d]";
        public static final String SOURCE_LEFT_ELEMENT_VALUE = Div.SOURCE_LEFT + "//div[contains(@__did, '%s')]";
        /** Счетчики */
        public static final String START_CONDITION_SCRIPT = "//div[@id='gwt-debug-startConditionScript-value']"
                                                            + Other.PREFIX;// fontIcon
        public static final String TAB_ROW_SET = Complex.CONTAINER_ON_FORM + "//*[contains(@id,'Row')]";
        public static final String STOP_CONDITION_SCRIPT = "//div[@id='gwt-debug-stopConditionScript-value']"
                                                           + Other.PREFIX;
        public static final String TAB_N_ROW_DOWN = TAB_ROW_SET + "[%d]" + Span.DOWN_ICON;
        public static final String TAB_N_ROW_EDIT = TAB_ROW_SET + "[%d]" + Span.EDIT_ICON;
        public static final String TAGS_VALUE = Div.PROPERTY_DIALOG_BOX + "//div[@id='gwt-debug-tags-value']"
                                                + Input.INPUT_PREFIX;
        public static final String TARGET_LEFT_ELEMENT = Div.TARGET_LEFT + "//div[@__did and position()=%d]";
        public static final String TARGET_LEFT_ELEMENT_VALUE = Div.TARGET_LEFT + "//div[contains(@__did, '%s')]";
        public static final String TARGET_TYPES_SPAN = Div.TARGET_TYPES_CAPTION + Span.SPAN_PREFIX;
        /** Заголовок объекта в ссылке "Назад". */
        public static final String TITLE_IN_BACK_LINK = Div.MAIN_CONTENT_HEADER + String.format(Div.ANY, "lastCrumb");
        public static final String TITLE_XPATH = "//td[@__did='abstractBO@title']/a/div[text()='%s']";
        public static final String TREE_ITEM = "//*[@id='gwt-debug-valueCellTree']"
                                               + "//div[@role='treeitem' and .//span[@id='%s' and ("
                                               + ".//ancestor::div[@role='treeitem'][1])"
                                               + "[@aria-level='1']] and @aria-level='1']";
        public static final String TREE_ITEM_BY_TITLE = "//*[@id='gwt-debug-valueCellTree']"
                                                        + "//div[@role='treeitem' and .//span[contains(text(), '%s') "
                                                        + "and ("
                                                        + ".//ancestor::div[@role='treeitem'][1])"
                                                        + "[@aria-level='%2$d']] and @aria-level='%2$d']";
        public static final String UPLOAD_FORM_PATH = "(//*[@id='gwt-debug-metainfoUpload'])"
                                                      + "//form//input[@name='file']";
        public static final String UPLOAD_METAINFO_FORM_PATH = "(//*[@id='gwt-debug-metaInfoFile-value'])"
                                                               + "//form//input[@name='file']";

        public static final String VALIDATE_CONNECTION = ".//div[@id='gwt-debug-validateConnection']";
        public static final String VERSION_AUTHOR_VALUE = "//div[@id='gwt-debug-versionAuthor-value']" + Other.PREFIX;
        public static final String X_FUTURE_DISABLED_LABEL = "//div[@id='gwt-debug-dateTimeCommonRestrictions-value"
                                                             + "']//input[@id='FUTURE' and @disabled]/."
                                                             + "./label[contains(@class, 'disabledLabel')]";// fontIcon
        public static final String X_IFRAME = "//*[@id='%s']//*[@__did='%s']//*[@__did='%s@%s']//iframe";// fontIcon
        public static final String X_PAST_DISABLED_LABEL = "//div[@id='gwt-debug-dateTimeCommonRestrictions-value"
                                                           + "']//input[@id='PAST' and @disabled]/../label[contains"
                                                           + "(@class, 'disabledLabel')]";
        public static final String X_STRATEGY = Div.FORM_CONTAINS + Other.TABLE + "//tbody[1]"
                                                + Other.TR_DID_PATTERN_TWO;
        public static final String ROW_IN_CONTAINER = Complex.CONTAINER_ON_FORM
                                                      + "//*[@id=concat('Row-',substring-after('%s','gwt-debug-Tab.')"
                                                      + ")]";
        public static final String ROW_IN_CONTAINER_DEL3 = ROW_IN_CONTAINER + String.format(Span.ANY_ICON, "del3");
        public static final String ROW_IN_CONTAINER_SWITCH = ROW_IN_CONTAINER + Span.SWITCH_ICON;
        public static final String ROW_IN_CONTAINER_TEXT = ROW_IN_CONTAINER + Div.TEXT_ANY;
        private static final String POPUP_TEXT = "[text()='Поле должно быть заполнено.']";
    }

    /**
     * Метод формирует //div[@id='gwt-debug-id']
     */
    public static String divGwtDebugId(String id)
    {
        return String.format(Div.ANY, id);
    }

    /**
     * Метод формирует //input[@id='gwt-debug-%s-input']
     */
    public static String inputGwtDebugId(String id)
    {
        return String.format(Input.ANY_INPUT, id);
    }
}