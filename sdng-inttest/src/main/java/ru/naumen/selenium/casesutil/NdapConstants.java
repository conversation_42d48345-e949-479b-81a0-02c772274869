package ru.naumen.selenium.casesutil;

import java.util.Set;

/**
 * Базовые константы для системы мониторинга NDAP
 *
 * <AUTHOR>
 * @since Jan 12, 2016
 */
public interface NdapConstants
{
    /**
     * Константы, относящиеся к ДПС
     */
    interface EventAction
    {
        String OLD_STATUS = "oldStatus";
        String NEW_STATUS = "newStatus";
        String TIME = "time";
        String EVENT_TIME = "eventTime";
        String SUBJECT = "subject";
        String METRIC = "metric";
        String MODEL = "model";
        String EVENT_SOURCE = "eventSource";

        Set<String> CONTEXT_VARIABLES = Set.of(OLD_STATUS, NEW_STATUS, TIME, EVENT_TIME, SUBJECT,
                METRIC, MODEL, EVENT_SOURCE);
    }

    /**
     * Константы, относящиеся к Обработчикам событий
     */
    interface EventListener
    {
        String ENABLED = "enabled";
        String PROCESSING_RULE = "processingRule";
        String EVENT_FILTER = "eventFilter";
        String EVENT_TYPES = "eventTypes";
    }

    /**
     * Константы GWT идентификаторов
     */
    interface GwtId
    {
        String TASK_PLANNER_SCHEDULES_LIST = "gwt-debug-RelObjectList.ndap_scheduleParam";
    }

    String F_ID = "id";
    String F_ADDRESS = "address";
    String F_TYPE = "type";
    String F_TITLE = "title";
    String F_MESSAGE = "message";
    String F_NAME = "name";
    String F_LABELS = "labels";
    String F_METRIC_NAME = "metricName";
    String F_MODULE_NAME = "moduleName";
    String F_METRIC_ID = "metricId";
    String F_URL = "url";
    String F_CONNECTION_STRING = "connectionString";
    String F_USERNAME = "username";
    String F_SYS_USE_BASIC_AUTH = "sys_useBasicAuth";
    String F_USE_BASIC_AUTH = "useBasicAuth";
    String F_SYS_USERNAME = "sys_username";
    String F_USER_NAME = "userName";
    String F_PASSWORD = "password";
    String F_SYS_PASSWORD = "sys_password";
    String F_PORT = "port";
    String F_READ_COMMUNITY = "readCommunity";
    String F_CALC_RULE = "calcRule";
    String F_USE_SSL = "useSSL";
    String F_USE_PROXY = "useProxy";
    String F_PROXY_USE_SSL = "proxyUseSSL";
    String F_PROXY_ADDRESS = "proxyAddress";
    String F_PROXY_PORT = "proxyPort";
    String F_DOMAIN = "domain";
    String F_NAMESPACE = "namespace";
    String F_LAST_VALUE = "lastValue";
    String F_SEVERITY = "severity";
    String F_TIMEOUT = "timeout";
    String F_WAIT_PERIOD = "waitPeriod";
    String F_DEPENDS_ON = "dependsOn";
    String F_METRICS = "metrics";
    String F_ENABLED = "enabled";
    String F_MODELS = "models";
    String F_ENDPOINT_NAME = "endpointName";
    String F_SERVER_NAME = "serverName";
    String F_COLLECTOR_NAME = "collectorName";
    String F_PREDICTIVE_MODEL_NAME = "modelName";
    String F_ALERTING_RULE_NAME = "ruleName";
    String F_SSO_URL = "ssoUrl";
    String F_SDK_URL = "sdkUrl";
    String F_AUTHENTICATION = "authentication";
    String F_PRINCIPAL = "principal";
    String F_SECURITY_PROTOCOL = "securityProtocol";
    String PARAM_SYNC = "sync";
    String ENDPOINT_URL = "/rest/endpoint/{endpointName}";
    String SERVER_URL = "/rest/server/{serverName}";
    String PARAMETER_URL = "/rest/parameter/{name}";
    String METRIC_URL = "/rest/metric/{metricName}";
    String METRIC_LAST_VALUE_URL = "/rest/metricLastValue/{metricName}";
    String STORAGE_URL = "/rest/storage/{name}";
    String STORAGES_URL = "/rest/storage";
    String METRIC_CALC_VALUE_URL = METRIC_URL + "/calcValue";
    String COLLECTOR_URL = "/rest/collector/{collectorName}";
    String SCHEDULE_URL = "/rest/schedule/{name}";
    String TASK_PLANNER_URL = "/rest/taskPlanner/{name}";
    String TRIGGER_URL = "/rest/trigger/{name}";
    String PUSH_URL = "/rest/push";
    String EVENT_LISTENER_URL = "/rest/eventListener/{name}";
    String PREDICTIVE_MODEL_URL = "/rest/predictiveModel/{modelName}";
    String ALERTING_RULE_URL = "/rest/alertRule/{ruleName}";
    String SCRIPT_MODULE_URL = "/rest/scriptModule/{moduleName}";
    String EXEC_URL = "/rest/exec";
    String API_DOCS_URL = "/v3/api-docs";
    String ENDPOINT_TYPE = "ru.naumen.core.model.Endpoint";
    String PARAMETER_TYPE = "ru.naumen.core.model.Parameter";
    String TRIGGER_TYPE = "ru.naumen.core.model.Trigger";
    String METRIC_TYPE = "ru.naumen.core.model.Metric";
    String SCRIPT_MODULE_TYPE = "ru.naumen.core.model.script.module.ScriptModule";
    String SCHEDULE_TYPE = "ru.naumen.core.model.Schedule";
    String PERIODIC_SCHEDULE_TYPE = "ru.naumen.core.model.PeriodicSchedule";
    String CRON_SCHEDULE_TYPE = "ru.naumen.core.model.CronSchedule";
    String STORAGE_TYPE = "ru.naumen.core.model.Storage";
    String SERVER_TYPE = "ru.naumen.core.model.Server";
    String TASK_PLANNER_TYPE = "ru.naumen.core.model.TaskPlanner";
    String COLLECTOR_TYPE = "ru.naumen.core.model.collector.Collector";
    String EVENT_LISTENER_TYPE = "ru.naumen.core.model.EventListener";
    String ICMP_ENDPOINT_TYPE = "ru.naumen.drivers.icmp.ICMPEndpoint";
    String PREDICTIVE_MODEL_TYPE = "ru.naumen.core.model.PredictiveModel";
    String ALERT_RULE_TYPE = "ru.naumen.core.model.AlertRule";
    String NOT_FOUND_MESSAGE = "Object with type=%s and name=%s not found";
    String URL_EXAMPLE = "http://example.com"; //NOPMD
    String URL_EXAMPLE2 = "http://example.ru"; //NOPMD
    String LDAP_URL_EXAMPLE = "ldap://example.ru"; //NOPMD
    String LDAP_URL_EXAMPLE2 = "ldaps://example.com"; //NOPMD
    String IP_EXAMPLE = "***********"; //NOPMD
    String PORT_EXAMPLE = "80"; //NOPMD
    String SCRIPT_EXAMPLE = "return 1"; //NOPMD
    String TRIGGER_SCRIPT_EXAMPLE = "return false";
    String CONNECTION_STRING_EXAMPLE = "*************************************"; //NOPMD
    String USER_EXAMPLE = "user";
    String PASSWORD_EXAMPLE = "password";
    String READ_COMMUNITY_EXAMPLE = "public";
    String PROTOCOL_EXAMPLE = "udp";
    String SNMP_VERSION_EXAMPLE = "1";
    String LOCALHOST = "localhost"; //NOPMD
    String SCRIPT_ENDPOINT_IS_REACHABLE = "endpoint.isReachable();"; //NOPMD
    String DOMAIN_EXAMPLE = "domain";
    String NAMESPACE_EXAMPLE = "\\root\\cimv2"; //NOPMD
    String CLI_CONNECTION_TYPE = "[\"monitoringConnection$cli\"]"; //NOPMD
    String HTTP_CONNECTION_TYPE = "[\"monitoringConnection$http\"]"; //NOPMD
    String ICMP_CONNECTION_TYPE = "[\"monitoringConnection$icmp\"]"; //NOPMD
    String JDBC_CONNECTION_TYPE = "[\"monitoringConnection$jdbc\"]"; //NOPMD
    String JMX_CONNECTION_TYPE = "[\"monitoringConnection$jmx\"]"; //NOPMD
    String SSH_CONNECTION_TYPE = "[\"monitoringConnection$ssh\"]"; //NOPMD
    String SNMP_CONNECTION_TYPE = "[\"monitoringConnection$snmp\"]"; //NOPMD
    String SMTP_CONNECTION_TYPE = "[\"monitoringConnection$smtp\"]"; //NOPMD
    String IMAP4_CONNECTION_TYPE = "[\"monitoringConnection$imap4\"]"; //NOPMD
    String POP3_CONNECTION_TYPE = "[\"monitoringConnection$pop3\"]"; //NOPMD
    String WMI_CONNECTION_TYPE = "[\"monitoringConnection$wmi\"]"; //NOPMD
    String SELF_CONNECTION_TYPE = "[\"monitoringConnection$self\"]"; //NOPMD
    String ALERT_RULE = "alertRule";
    String SEVERITY = "severity";
    String CONNECTION = "connection";
    String PARAMETER = "parameter";
    String TIMEOUT = "timeout";
    String TRIGGERS = "triggers";
    String CENTRAL_SERVER = "monitoringServer";
    String ENCRYPTION_PROTOCOL = "encryptionProtocol";
    String ENCRYPT_PROTO = "encryptProto";
    String RETENTION_POLICY = "retentionPolicy";
    String PASSWORD_BULLETS = "••••••••";
    int POLL_PERIOD = 5000; // 5 sec
    int F_HORIZON = POLL_PERIOD * 2;
    int SEASON_LENGTH = 60000; // 60 sec
    // Скрипт изменения периода запуска джобы для нового типа триггера "Триггер отсутствия данных"
    String SCRIPT_REINIT_TRIGGER_JOB = "beans.getBean('triggerServiceImpl').reinitTriggerJob(%s, 0)";
    // Малый период запуска джобы для нового типа триггера "Триггер отсутствия данных"
    Long QUICK_TRIGGER_JOB_PERIOD = 10_000L;
    // Псевдоатрибут отключения синхронизации между SMP и NDAP
    String DISABLE_NDAP_SYNC = "@disableNdapSync";
}