package ru.naumen.selenium.casesutil.omnichannel;

import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;

/**
 * Утилитарные методы для работы со списком каналов через интерфейс
 * <AUTHOR>
 * @since 25.03.2021
 */
public class GUIChannelList extends GUIAdvListUtil
{
    private static GUIChannelList advlist;
    private static final String ADVLIST_X_PATH = GUIXpath.divGwtDebugId("channels");

    // XPath пиктограммы удаления
    public static final String PICT_DELETE = "omnichannelDeleteChannel";
    // XPath пиктограммы редактирования
    public static final String PICT_EDIT = "omnichannelEditChannel";
    // XPath пиктограммы помещения в архив
    public static final String PICT_REMOVE = "omnichannelRemoveChannel";
    // XPath пиктограммы восстановления из архива
    public static final String PICT_RESTORE = "omnichannelRestoreChannel";

    /**
     * Возвращает утилитарный объект для доступа к адвлисту каналов
     * @return объект для работы с адвлистом каналов
     */
    public static GUIChannelList advlist()
    {
        if (advlist == null)
        {
            advlist = new GUIChannelList();
        }
        return advlist;
    }

    private GUIChannelList()
    {
        super(ADVLIST_X_PATH);
    }
}
