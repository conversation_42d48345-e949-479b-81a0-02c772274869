package ru.naumen.selenium.casesutil;

/**
 * Класс предназначен для хранения Base-64 представлений иконок
 * для возможности оперативно заменять иконки в темах.
 *
 * <AUTHOR>
 * @since 4.04.2018
 */
public class GUIIcon
{
    /**
     * Чекбокс с галочкой
     */
    public static final String CHECKBOX_CHECKED = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8"
                                                  + "/9hAAAAr0lEQVR4XpWT4Q3CIBCFbwRHcARHcQMJ1N"
                                                  + "+6gY7gJmcn6AiO4AiMUA9oIuWd2CP5UnjHe5AcJQrjicIzCrORd"
                                                  + "/aWCRS3ElNAK9oAwQoIfSbQQPgJPygNz"
                                                  + "/dewIu0jgxiSsPxAerVIsqGnZzgVHPgI5ibgJn8eFuuWUIGvhZzfitroxpQhzjeL"
                                                  + "+YL7OkG1CHp29ZaQPgyKRoCghUQrJDWmq14"
                                                  + "+RFzy9KkLf7Dy8Fndh9a0fMJSGWCHAAAAABJRU5ErkJggg==";

    /**
     * Чекбокс с точкой
     */
    public static final String CHECKBOX_DOT = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8"
                                              + "/9hAAAAfklEQVR4Xq2T0QnAIBBDb5RuVlE6n"
                                              +
                                              "+1EHcERWr0fa7wGFA8eSJqLhKJIuHYJZ8o8g9y6qwcXNxmdslMu1rTZ0d3lAdqt61t7t14zAJda0GuKDPSaIgO9pshAryky0GuKDPR2oie/sXz7jhkwMqsC0vRj0ko+Otr7D58vPqJ7AdVvAcL4qG0OAAAAAElFTkSuQmCC";

    /**
     * Пустой чекбокс
     */
    public static final String CHECKBOX_UNCHECKED = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8"
                                                    +
                                                    "/9hAAAATklEQVR4XmOQkpKSlZSUdJeWlvYhETuD9DKAGDIyMpwMJAKQHpDFIAN80CWJBWC9owaMGjBMDABlCHIzE9AAJ5ApMiAGyDRSMMhiUHYGAHJOJSkXQETXAAAAAElFTkSuQmCC";
}
