package ru.naumen.selenium.casesutil.omnichannel;

/**
 * Класс содержит параметры конфигурации для интеграции с NCC
 * <AUTHOR>
 * @since 19.08.2021
 */
public class OmnichannelConfiguration
{
    // хост шлюза NCC
    private static final String NCC_HOST = "127.0.0.1"; //NOPMD
    // порт подключения к шлюзу NCC
    private static final String NCC_PORT = "3242";
    // ключ доступа для подключения к шлюзу NCC
    private static final String NCC_ACCESS_KEY = "1dc2054c93262ecf7d6625fdf5fd8528";
    // URL для подключения к ФХ NCC (HTTP)
    private static final String NCC_FILE_STORAGE_HTTP_URL = "http://127.0.0.1:8088/";
    // URL для подключения к ФХ NCC (HTTPS)
    private static final String NCC_FILE_STORAGE_HTTPS_URL = "https://127.0.0.1/fx/";

    // URL для подключения к шлюзу OmniGate
    private static final String OMNI_GATE_SERVER_ADDRESS = "https://localhost/omnigate";

    /**
     * Получить ключ доступа для подключения к шлюзу NCC
     */
    public static String getNccAccessKey()
    {
        return NCC_ACCESS_KEY;
    }

    /**
     * Получить URL для подключения к ФХ NCC (HTTP)
     */
    public static String getNccFileStorageHttpUrl()
    {
        return NCC_FILE_STORAGE_HTTP_URL;
    }

    /**
     * Получить URL для подключения к ФХ NCC (HTTPS)
     */
    public static String getNccFileStorageHttpsUrl()
    {
        return NCC_FILE_STORAGE_HTTPS_URL;
    }

    /**
     * Получить хост для подключения к шлюзу NCC
     */
    public static String getNccHost()
    {
        return NCC_HOST;
    }

    /**
     * Получить порт для подключения к шлюзу NCC
     */
    public static String getNccPort()
    {
        return NCC_PORT;
    }

    /**
     * Возвращает URL для подключения к шлюзу OmniGate
     */
    public static String getOmniGateServerAddress()
    {
        return OMNI_GATE_SERVER_ADDRESS;
    }
}