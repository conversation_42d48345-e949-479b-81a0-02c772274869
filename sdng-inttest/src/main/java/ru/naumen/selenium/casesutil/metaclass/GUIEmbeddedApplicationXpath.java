package ru.naumen.selenium.casesutil.metaclass;

import static ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentType.EMBEDDED_APPLICATION;

import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.Id;

/**
 * Константы, необходимые для формирования xpath для встроенных приложений
 *
 * <AUTHOR>
 * @since 31.10.2022
 */
public interface GUIEmbeddedApplicationXpath
{
    /**
     * Константы, необходимые для формирования xpath для контентов ВП
     */
    interface Content
    {
        String ID_PATTERN = String.format(Id.ANY, EMBEDDED_APPLICATION.getType() + ".%s");
        String DIV_PATTERN = String.format(Div.ID_PATTERN, ID_PATTERN);
    }
}
