package ru.naumen.selenium.casesutil;

import org.junit.Assert;

import ru.naumen.selenium.core.WaitTool;

/**
 * Класс для работы с окнами браузеров.
 * <AUTHOR>
 */
public class GUIWindow extends CoreTester
{
    /**
     * Ждет открытия определенного количества окон
     * @param windowNumber ожидаемое количество открытых окон
     */
    public static void assertWindowsPresent(int windowNumber)
    {
        WaitTool.wait(tester.getWebDriver(), WaitTool.WAIT_TIME, (webDriver) ->
        {
            return tester.getWindowHandlers().size() == windowNumber;
        });
        Assert.assertEquals(tester.getWindowHandlers().size(), windowNumber);
    }

}

