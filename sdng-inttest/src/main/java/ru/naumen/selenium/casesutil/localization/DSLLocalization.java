package ru.naumen.selenium.casesutil.localization;

import java.util.List;
import java.util.regex.Pattern;

import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.modules.IModuleLocalization;
import ru.naumen.selenium.modules.ScriptModules;

/**
 * Методы для работы с локализацией системы
 *
 * <AUTHOR>
 * @since 21 фев. 2019 г.
 */
public class DSLLocalization
{
    /**
     * Русская локаль
     */
    public static final String RU = "ru";
    /**
     * Английская локаль
     */
    public static final String EN = "en";

    private DSLLocalization()
    {
    }

    private static final Pattern SPLIT_PATTERN = Pattern.compile(", ");

    /**
     * Получить модуль для работы с локализацией системы.
     */
    public static IModuleLocalization getLocalizationModule()
    {
        return ScriptModules.getLocalizationModule();
    }

    /**
     * Выключить локализацию атрибута title для БО
     */
    public static void disableTitleLocalization()
    {
        getLocalizationModule().disableTitleLocalization();
    }

    /**
     * Включить локализацию атрибута title для БО
     */
    public static void enableTitleLocalization()
    {
        Cleaner.afterTest(DSLLocalization::disableTitleLocalization);
        getLocalizationModule().enableTitleLocalization();
    }

    /**
     * Редактировать название клиентской локали
     * @param title новое название
     */
    static void editClientLocaleTitle(String title)
    {
        getLocalizationModule().editClientLocaleTitle(title);
    }

    /**
     * Включить локализацию оповещений
     */
    public static void enableNotificationLocalization()
    {
        Cleaner.afterTest(DSLLocalization::disableNotificationLocalization);
        getLocalizationModule().enableNotificationLocalization();
    }

    /**
     * Выключить локализацию оповещений
     */
    public static void disableNotificationLocalization()
    {
        getLocalizationModule().disableNotificationLocalization();
    }

    /**
     * @return список локалей, включенных на этапе сборки приложения
     */
    public static List<String> getBuildLocales()
    {
        String result = getLocalizationModule().getBuildLocales();
        return List.of(SPLIT_PATTERN.split(result.substring(1, result.length() - 1)));
    }
}
