package ru.naumen.selenium.casesutil.omnichannel.chatOnSite;

import java.net.URISyntaxException;
import java.util.Date;

import org.apache.http.client.utils.URIBuilder;

import ru.naumen.selenium.casesutil.omnichannel.ChatOnSite;
import ru.naumen.selenium.util.StringUtils;

/**
 * Содержит конфигурацию чата на сайте. Чат на сайте содержит следующие параметры:
 * <ol>
 *     <li>caseId - идентификатор витрины (см. {@link ShowCase}). По умолчанию {@link ShowCase#MESSAGE_BOX_CASE_1}.</li>
 *     <li>crmId - идентификатор пользователя (по нему будет восстановлена история переписки). По умолчанию
 *     {@link ChatOnSiteConfiguration#SHOW_CASE_CRM_ID_DEFAULT}.</li>
 *     <li>name - имя пользователя (по нему формируется наименование диалога). По умолчанию
 *     {@link ChatOnSiteConfiguration#SHOW_CASE_NAME_DEFAULT}.</li>
 *     <li>ver - версия виджета чата на сайте (1 - старая версия, 2 - новая версия). По умолчанию
 *     {@link ChatOnSiteConfiguration#SHOW_CASE_VERSION_DEFAULT}.</li>
 * </ol>
 * <AUTHOR>
 * @since 21.07.2022
 */
public class ChatOnSiteConfiguration
{
    public static class ChatOnSiteConfigurationBuilder
    {
        private final ChatOnSiteConfiguration config;

        public ChatOnSiteConfigurationBuilder()
        {
            config = new ChatOnSiteConfiguration();
            config.chatOnSite = ChatOnSite.MESSAGE_BOX;
            config.caseId = ShowCase.MESSAGE_BOX_CASE_1.getCaseId();
            config.crmId = SHOW_CASE_CRM_ID_DEFAULT;
            config.name = SHOW_CASE_NAME_DEFAULT;
            config.version = SHOW_CASE_VERSION_DEFAULT;
        }

        public ChatOnSiteConfiguration build()
        {
            return config;
        }

        public ChatOnSiteConfigurationBuilder setChatOnSite(ChatOnSite chatOnSite)
        {
            config.chatOnSite = chatOnSite;
            return this;
        }

        public ChatOnSiteConfigurationBuilder setCrmId(String crmId)
        {
            config.crmId = crmId;
            return this;
        }

        public ChatOnSiteConfigurationBuilder setName(String name)
        {
            config.name = name;
            return this;
        }

        public ChatOnSiteConfigurationBuilder setShowCase(ShowCase showCase)
        {
            config.caseId = showCase.getCaseId();
            return this;
        }

        public ChatOnSiteConfigurationBuilder setVersion(int version)
        {
            config.version = version;
            return this;
        }
    }

    // название параметров для формирования url адреса
    private static final String CASE_ID_PARAM = "caseId";
    private static final String CRM_ID_PARAM = "crmId";
    private static final String NAME_PARAM = "name";
    private static final String VERSION_PARAM = "ver";

    // идентификатор пользователя в чате на сайте по умолчанию
    private static final String SHOW_CASE_CRM_ID_DEFAULT = "test";
    // имя пользователя в чате на сайте по умолчанию
    private static final String SHOW_CASE_NAME_DEFAULT = "test";
    // версия виджета чата на сайте по умолчанию
    private static final int SHOW_CASE_VERSION_DEFAULT = 2;

    /**
     * Создать билдер конфигурации чата на сайте
     */
    public static ChatOnSiteConfigurationBuilder builder()
    {
        return new ChatOnSiteConfigurationBuilder();
    }

    /**
     * Возвращает уникальную конфигурацию чата на сайте со случайным значением crm_id и name. Случайное значение crm_id
     * позволит вести переписку от разных пользователей и изолировать тесты друг от друга.
     * @param showCase витрина чата на сайте, для которой создается конфигурация
     * @return адрес чата на сайте
     */
    public static ChatOnSiteConfiguration createChatOnSiteRandomConfig(ShowCase showCase)
    {
        String id = String.valueOf(new Date().getTime());
        return ChatOnSiteConfiguration.builder()
                .setChatOnSite(showCase.getChatOnSite())
                .setShowCase(showCase)
                .setCrmId(id)
                .setName("Dialog-" + id)
                .build();
    }

    private static void addParamIfPresent(URIBuilder uriBuilder, String paramName, String paramValue)
    {
        if (StringUtils.isNotEmpty(paramValue))
        {
            uriBuilder.addParameter(paramName, paramValue);
        }
    }

    private ChatOnSite chatOnSite;
    private String caseId;
    private String crmId;
    private String name;
    private int version;

    /**
     * Создавать только через билдер {@link ChatOnSiteConfiguration#builder()}
     */
    private ChatOnSiteConfiguration()
    {
    }

    /**
     * Возвращает URL чата на сайте исходя из текущей конфигурации
     * @return URL чата на сайте
     */
    public String getUrl()
    {
        try
        {
            URIBuilder uriBuilder = new URIBuilder(chatOnSite.getAddress());
            addParamIfPresent(uriBuilder, CASE_ID_PARAM, caseId);
            addParamIfPresent(uriBuilder, CRM_ID_PARAM, crmId);
            addParamIfPresent(uriBuilder, NAME_PARAM, name);
            addParamIfPresent(uriBuilder, VERSION_PARAM, String.valueOf(version));
            return uriBuilder.toString();
        }
        catch (URISyntaxException e)
        {
            return chatOnSite.getAddress();
        }
    }
}
