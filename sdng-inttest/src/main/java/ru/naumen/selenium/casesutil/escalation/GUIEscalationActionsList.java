package ru.naumen.selenium.casesutil.escalation;

import ru.naumen.selenium.casesutil.GUIXpath.Constant;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;

public class GUIEscalationActionsList extends GUIAdvListUtil
{
    private static final String ADVLIST = "gwt-debug-actions" + Constant.SINGLE_CONTENT_ON_TAB_ID_POSTFIX;

    /**Пиктограммы*/
    public static final String PICT_EDIT = "editEventAction";
    public static final String PICT_DELETE = "deleteEventAction";
    public static final String PICT_ENABLE = "toggleProvider";

    private static volatile GUIEscalationActionsList advlist;

    public static GUIEscalationActionsList advlist()
    {
        if (advlist == null)
        {
            advlist = new GUIEscalationActionsList();
        }
        return advlist;
    }

    public GUIEscalationActionsList(String contentIdOrXpath, Object[] args)
    {
        super(contentIdOrXpath, args);
    }

    protected GUIEscalationActionsList()
    {
        super(ADVLIST);
    }
}
