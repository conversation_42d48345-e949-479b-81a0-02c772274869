package ru.naumen.selenium.casesutil.escalation;

import java.util.List;
import java.util.Set;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.interfaceelement.MetaTree;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для работы с таблицами соответствий в эскалациях через интерфейс
 * <AUTHOR>
 * @since 03.11.2014
 */
public class GUIRulesSettingsEscalation extends CoreTester
{
    public static final String ROW_DID = "//tr[@__did='%s']//td[@__did='%s']";
    public static final String X_LIST_CELL_ICO = ROW_DID + "//span";
    public static final String RESTORE_BUTTON = "//div[@id='gwt-debug-restore']";
    public static final String GO_TO_RS_LINK = String.format(GUINavigational.BACK_LINK_PATTERN,
            "к таблицам соответствия схем эскалаций");

    /**
     * Проверить отсутствие таблицы соответствий в списке таблиц
     * @param escalationRule модель таблицы соответствий
     */
    public static void assertAbsence(CatalogItem escalationRule)
    {
        String titleMsg = String.format("Таблицами соответствий %s присутствует в списке.", escalationRule.getTitle());
        GUITester.assertAbsent(GUICatalogItem.ROW_DID_TITLE, titleMsg, escalationRule.getUuid());
    }

    /**
     * Проверить в списке, что таблица соответствий заархивирована
     * @param escalationRule модель таблицы соответствий
     */
    public static void assertArchivedInList(CatalogItem escalationRule)
    {
        assertTitleIcon(GUIRulesSettingsEscalation.X_LIST_CELL_ICO, "Восстановить из архива",
                escalationRule.getUuid(), "removeRestore");
    }

    /**
     * Проверить описание таблицы соответствий в списке таблиц
     * @param escalationRule модель таблицы соответствий
     */
    public static void assertDescription(CatalogItem escalationRule)
    {
        String descrMsg = "Полученное описание таблицы соответствий не совпало с ожидаемым.";
        GUITester.assertTextPresentWithMsg(GUICatalogItem.ROW_DID_DESCRIPTION, escalationRule.getDescription(),
                descrMsg, escalationRule.getUuid());
    }

    /**
     * Проверить поле объекты таблицы соответствий в списке таблиц
     * @param escalationRule модель таблицы соответствий
     * @param metaClass класс в поле Объекты
     */
    public static void assertObjects(CatalogItem escalationRule, MetaClass metaClass)
    {
        String objectsMsg = "Полученный класс объектов таблицы соответствий не совпал с ожидаемым.";
        GUITester.assertTextPresentWithMsg(GUICatalogItem.ROW_DID_OBJECTS, metaClass.getTitle(), objectsMsg,
                escalationRule.getUuid());
    }

    /**
     * Проверить присутствие ссылки 'к таблицам соответствия схем эскалаций'
     */
    public static void assertPresentGoToRsLink()
    {
        String message = String.format("Отсутствует ссылка 'к таблицам соответствия схем эскалаций'.");
        Assert.assertTrue(message, tester.waitAppear(GO_TO_RS_LINK));
    }

    /**
     * Проверить значения атрибутов таблицы соотвествий на странице списка таблиц соответствий
     * @param escalationRule проверяемая таблица соответствий
     * @param metaClass класс в поле Объекты
     */
    public static void assertRsInList(CatalogItem escalationRule, MetaClass metaClass)
    {
        GUIRulesSettingsEscalation.assertTitle(escalationRule);
        GUIRulesSettingsEscalation.assertObjects(escalationRule, metaClass);
        GUIRulesSettingsEscalation.assertDescription(escalationRule);
        GUIRulesSettingsEscalation.assertSourceAttrs(escalationRule);
    }

    /**
     * Проверить, что открыта вкладка "Таблицы соответствий"
     */
    public static void assertRsTabSelected()
    {
        String message = String.format("Вкладка 'Таблицы соответствий' не является активной.");
        Assert.assertTrue(message, tester.waitAppear(
                GUIXpath.divGwtDebugId("valueMaps") + "//ancestor::div[contains(@class, '-selected')]"));
    }

    /**
     * Проверить коды определяющих атрибутов таблицы соответствий в списке таблиц
     * @param escalationRule модель таблицы соответствий
     */
    public static void assertSourceAttrs(CatalogItem escalationRule)
    {
        List<String> sourceAttrs = Json.GSON.fromJson(escalationRule.getRulesSettingsSources(), Json.LIST_STRING_TYPE);
        String expectedSourceAttrs = StringUtils.join(sourceAttrs, ", ");
        GUITester.assertTextPresentWithMsg(GUICatalogItem.ROW_DID_SOURCES, expectedSourceAttrs,
                "Полученные коды определяющих атрибутов таблицы соответствий не совпали с ожидаемыми.",
                escalationRule.getUuid());
    }

    /**
     * Проверить название таблицы соответствий в списке таблиц
     * @param escalationRule модель таблицы соответствий
     */
    public static void assertTitle(CatalogItem escalationRule)
    {
        String titleMsg = "Полученное название таблицы соответствий не совпало с ожидаемым.";
        GUITester.assertTextPresentWithMsg(GUICatalogItem.ROW_DID_TITLE, escalationRule.getTitle(), titleMsg,
                escalationRule.getUuid());
    }

    /**
     * Проверить название пиктограммы напротив таблицы соответствий
     * @param xpath путь к пиктограмме
     * @param expected ожидаемое название
     * @param args параметры для формирования xpath
     */
    public static void assertTitleIcon(String xpath, String expected, Object... args)
    {
        String message = "Название пиктограммы не соответствует ожидаемому.";
        Assert.assertEquals(message, expected, GUITester.getAttributeProperty(xpath, "title", args));
    }

    /**
     * Нажать кнопку Добавить элемент
     */
    public static void clickAdd()
    {
        tester.click(GUIXpath.Div.ADD_ELEMENT);
    }

    /**
     * Нажать пиктограмму архивации напротив таблицы соответствий
     * @param escalationRule архивируемая таблица соответствий
     */
    public static void clickArchiveIcon(CatalogItem escalationRule)
    {
        tester.click(X_LIST_CELL_ICO, escalationRule.getUuid(), "removeRestore");
    }

    /**
     * Нажать пиктограмму копирования напротив таблицы соответствий
     * @param escalationRule копируемая таблица соответствий
     */
    public static void clickCopyIcon(CatalogItem escalationRule)
    {
        tester.click(X_LIST_CELL_ICO, escalationRule.getUuid(), "copy");
        GUIForm.assertDialogAppear("Форма не открылась");
    }

    /**
     * Нажать пиктограмму удаления напротив таблицы соответствий
     * @param escalationRule удаляемая таблица соответствий
     */
    public static void clickDeleteIcon(CatalogItem escalationRule)
    {
        tester.click(X_LIST_CELL_ICO, escalationRule.getUuid(), "delete");
    }

    /**
     * Нажать пиктограмму редактирования напротив таблицы соответствий
     * @param escalationRule редактируемая таблица соответствий
     */
    public static void clickEditIcon(CatalogItem escalationRule)
    {
        tester.click(X_LIST_CELL_ICO, escalationRule.getUuid(), "edit");
        GUIForm.assertDialogAppear("Форма не открылась");
    }

    /**
     * Нажать на ссылку 'к таблицам соответствия схем эскалаций'
     */
    public static void clickGoToRsLink()
    {
        tester.click(GO_TO_RS_LINK);
    }

    /**
     * Заполнить строковый атрибут на форме добавления строки таблицы соответствий
     * @param attribute - атрибут значение которого нужно заполнить
     * @param value - значение
     */
    public static void fillStringAttribute(Attribute attribute, String value)
    {
        tester.sendKeysWithoutClear(GUIXpath.Any.ANY_VALUE, value, attribute.getCode());
    }

    /**
     * Перейти на карточку таблицы соответствий
     * (Необходимо находится на странице списка таблиц соответствий)
     * @param escalationRule модель таблицы соответствий
     */
    public static void goToCardFromList(CatalogItem escalationRule)
    {
        tester.click(GUICatalogItem.ROW_DID_TITLE + "//a", escalationRule.getUuid());
    }

    /**
     * Заполнить поле Код
     * @param code устанавливаемый Код 
     */
    public static void setCode(String code)
    {
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, code);
    }

    /**
     * Заполнить поле Описание
     * @param description устанавливаемое описание
     */
    public static void setDescription(String description)
    {
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.DESCR_VALUE, description);
    }

    /**
     * Выбрать метакласс в поле Объекты
     * @param metaClasses путь к выбираемому метаклассу
     */
    public static void setObjects(MetaClass... metaClasses)
    {
        MetaTree tree = new MetaTree(GUIXpath.Id.TARGET_METACLASS_VALUE);
        tree.setElementInMultiSelectTree(metaClasses);
    }

    /**
     * Заполнить поле Название
     * @param title устанавливаемое название
     */
    public static void setTitle(String title)
    {
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, title);
    }

    /**
     * Установить uuid таблице соответствий через список таблиц соответствий
     * (Необходимо находится на странице списка таблиц соотвествий)
     * @param escalationRule модель таблицы соотвествствий
     * @param oldElementsUuids множество uuid'ов из списка таблиц соответствий перед созданием новой таблицы
     */
    public static void setUuidByList(CatalogItem escalationRule, Set<String> oldElementsUuids)
    {
        Set<String> newElementsUuid = DSLCatalogItem.getRuleSettingsEscalationUuids();
        newElementsUuid.removeAll(oldElementsUuids);
        if (newElementsUuid.size() != 1)
        {
            Assert.fail("Таблица соответствий не добавлена.");
        }
        escalationRule.setUuid(newElementsUuid.iterator().next());
        escalationRule.setExists(true);
    }
}
