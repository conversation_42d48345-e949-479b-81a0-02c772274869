package ru.naumen.selenium.casesutil.advimport;

import org.junit.Assert;
import org.openqa.selenium.WebElement;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Constant;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.Input;
import ru.naumen.selenium.casesutil.GUIXpath.Other;
import ru.naumen.selenium.casesutil.GUIXpath.SpecificComplex;
import ru.naumen.selenium.casesutil.admin.GUIScriptField;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.model.advimport.AdvimportConfig;
import ru.naumen.selenium.casesutil.model.advimport.AdvimportConnection;
import ru.naumen.selenium.casesutil.model.advimport.DAOAdvimport;
import ru.naumen.selenium.casesutil.user.StandTypeHolder;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.exception.DialogErrorException;
import ru.naumen.selenium.util.FileUtils;
import ru.naumen.selenium.util.StringUtils;

/**
 * Методы для работы с advimport через интерфейс
 * <AUTHOR>
 * @since 19.04.2013
 *
 */
public class GUIAdvimportList extends GUIAdvListUtil
{
    /**
     * Режимы Advimport
     */
    public enum Mode
    {
        CREATE, UPDATE, EMPTY
    }

    /**Путь до кнопки "Добавить конфигурацию" в карточке списка конфигураций импорта*/
    public static final String ADD_CONFIG_ADV = "//div[@id='gwt-debug-addConfigButton']";

    /**Путь до кнопки "Добавить подключение" вкладки Подключение карточки Синхронизация*/
    public static final String ADD_CONNECTION = "//div[@id='gwt-debug-addConnectionButton']";

    private static final String ADVLIST_CONFIG = "gwt-debug-configList" + Constant.SINGLE_CONTENT_ON_TAB_ID_POSTFIX;
    private static final String VERSIONS_ADVLIST_CONFIG = "gwt-debug-versions";

    private static final String ADVLIST_CONNECTION =
            "gwt-debug-connectionList" + Constant.SINGLE_CONTENT_ON_TAB_ID_POSTFIX;

    /**Путь до кнопки "Запустить" в карточке конфигурации импорта*/
    public static final String RUN_BUTTON = "//div[@id='gwt-debug-runItNow']";

    /**Путь до кнопки "Удалить" в карточке конфигурации импорта*/
    public static final String DELETE_BUTTON_ADV = "//div[@id='gwt-debug-del']";

    /**Путь до кнопки "Сменить пароль" в карточке подключения импорта*/
    public static final String CNHG_PSWD_BUTTON = "//div[@id=\"gwt-debug-changePasswd\"]";

    /**Путь до формы смены пароля подключения импорта*/
    public static final String CNHG_PSWD_FORM = "//div[@id=\"gwt-debug-PropertyDialogBox\"]";

    /**Путь до поля ввода "Тип подключения" на форме добавления подключения*/
    public static final String CONNECTION_TYPE_INPUT = "//div[@id='gwt-debug-connectionType-value']//input";

    /**Путь до поля ввода "Пароль" на форме смены пароля подключения импорта*/
    public static final String PSWD_INPUT = CNHG_PSWD_FORM + "//input[@id=\"gwt-debug-password-value\"]";

    /**Путь до поля ввода "Тип идентификации" на форме редактирования LDAP подключения*/
    public static final String LDAP_AUTH_TYPE_INPUT = "//input[@id='gwt-debug-LDAPAuthType-value']";

    /**Путь до поля ввода "Протокол безопасности" на форме редактирования LDAP подключения*/
    public static final String LDAP_SEC_PROTOCOL_INPUT = "//input[@id='gwt-debug-LDAPSecProtocol-value']";

    /**Путь до поля ввода "Игнорирование проверки сертификата" на форме редактирования LDAP подключения*/
    public static final String SKIP_CERT_VERIFICATION_INPUT = "//input[@id='gwt-debug-skipCertVerification-value"
                                                              + "-input']";

    /**Путь до поля ввода "Имя пользователя" на форме редактирования подключения*/
    public static final String USERNAME_INPUT = "//input[@id='gwt-debug-username-value']";

    /**Путь до поля ввода "Подтверждение пароля" на форме смены пароля подключения импорта*/
    public static final String PSWD_CONFIRM_INPUT = CNHG_PSWD_FORM
                                                    + "//input[@id=\"gwt-debug-passwordConfirmation-value\"]";

    /**Вкладка История карточки конфигурации импорта*/
    public static final String ADVIMPORT_CONFIG_HISTORY = "//*[@id='gwt-debug-advimportConfigHistory']";
    public static final String CONFIG_HISTORY_CONTENT = GUIXpath.divGwtDebugId("attrs");
    public static final String CONFIG_HISTORY_EDIT_BUTTON = String.format("(%s)" + Other.LAST_PATTERN,
            CONFIG_HISTORY_CONTENT + "//td[.//div[text()='Редактировать']]");
    public static final String HIDE_LOGS_DAYS_VALUE = CONFIG_HISTORY_CONTENT + "//tr[.//div[@id='gwt-debug-caption' "
                                                      + "and text()='Скрывать логи старше (дн.)']]" + Div.VALUE;

    /**Вкладка Подключения карточки Синхронизация*/
    public static final String ADVIMPORT_CONNECTION = GUIXpath.divGwtDebugId("connectionList");

    /**Название подключения в списке подключений синхронизации*/
    public static final String CONNECTION_ROW_TITLE = "//tr//a[@id='title' and contains (text(), '%s')]";

    /**Подключение в списке подключений синхронизации по uuid*/
    public static final String CONNECTION_ROW_UUID = "//tr//a[@id='title' and contains(@href,'%s')]";

    /**Путь до форма загрузки файла в диалоговом окне запуска импорта*/
    public static final String UPLOAD_FILE = "//*[contains(@id, 'gwt-debug-uploadedFile-')]//form";

    /**ссылка "к списку конфигураций"*/
    public static final String BACK_LINK_CONFIG = String.format(GUINavigational.BACK_LINK_PATTERN,
            "к списку конфигураций");

    /**ссылка для загрузки файла источника импорта*/
    public static final String FILE_INPUT = GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + "//table[@id]//form";

    public static final String METACLASS_PARAMETER_PATTERN = "<parameter name=\"metaClass\">%s</parameter>";
    public static final String METACLASS_RESOLVER_PATTERN = "<constant-metaclass-resolver metaclass=\"%s\"/>";
    public static final String METACLASS_RELOLVER_EMPTY = "<constant-metaclass-resolver/>";
    public static final String OBJECT_SEARCHER_PATTERN = "<object-searcher attr=\"%s\" metaclass=\"%s\"/>";
    public static final String METACLASS_VALUE = "${metaClass}";
    public static final String ATTRIBUTE_ID_ADVIMPORT = "idAttr";
    public static final String CREATED = "created";
    public static final String REMOVING = "Removing";
    public static final String SKIPPED = "skipped";
    public static final String EDIT_ACTION = "editConfig";
    public static final String DELETE_ACTION = "deleteConfig";

    public static final String ADV_IMPORT_CONNECTION_CARD_INFO_BLOCK = String.format(GUIXpath.Div.ANY, "info");

    private static volatile GUIAdvimportList advlistConfig;

    private static volatile GUIAdvimportList advlistConnection;

    private static GUIAdvListUtil versionsAdvlistConfig;

    public static GUIAdvimportList advlistConfig()
    {
        if (advlistConfig == null)
        {
            advlistConfig = new GUIAdvimportList(ADVLIST_CONFIG);
        }
        return advlistConfig;
    }

    public static GUIAdvimportList advlistConnection()
    {
        if (advlistConnection == null)
        {
            advlistConnection = new GUIAdvimportList(ADVLIST_CONNECTION);
        }
        return advlistConnection;
    }

    /**
     * Проверить отсутвие конфигурации в списке конфигураций
     * @param config модель конфигурации импорта
     */
    public static void assertConfigAbsent(AdvimportConfig config)
    {
        advlistConfig().content().asserts().rowsAbsence(config);
    }

    /**
     * Проверить наличие конфигурации в списке конфигураций
     * @param config модель конфигурации импорта
     */
    public static void assertConfigPresent(AdvimportConfig config)
    {
        advlistConfig().content().asserts().rowsPresenceByTitle(config);
    }

    /**
     * Проверить название конфигурации в списке конфигураций
     * @param config модель конфигурации импорта
     */
    public static void assertConfigTitle(AdvimportConfig config)
    {
        GUITester.assertTextPresentWithMsg(CONNECTION_ROW_UUID, config.getTitle(),
                "Название конфигурации импорта не совпало с ожидаемым", config.getCode());
    }

    /**
     * Проверить наличие подключения в списке подключений
     * @param cfg модель конфигурации импорта
     */
    public static void assertConnectionPresent(AdvimportConnection cfg)
    {
        advlistConnection().content().asserts().rowsPresenceByTitle(cfg);
    }

    /**
     * Проверить, что на карточке подключения в поле игнорирования SSL отображается слово ДА
     */
    public static void assertSkipCertVerificationTextOnCard()
    {
        WebElement element = tester.findDisplayed(SpecificComplex.SKIP_CERT_VERIFICATION_ELEMENT);
        Assert.assertEquals("Неверное значение поля", element.getText().trim(), "да");
    }

    /**
     * Проверить значение параметра "Скрывать логи старше (дн.)"
     */
    public static void assertHideLogsDays(String value)
    {
        GUITester.assertTextPresent(HIDE_LOGS_DAYS_VALUE, value);
    }

    /**
     * Нажать Добавить конфигурацию импорта
     */
    public static void clickAddConfig()
    {
        advlistConfig().toolPanel().clickAdd();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
    }

    /**
     * Нажать Добавить подключение
     */
    public static void clickAddConnection()
    {
        advlistConnection().toolPanel().clickAdd();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
    }

    /**
     * Кликнуть по ссылке "к списку конфигураций"
     */
    public static void clickBackConfigLink()
    {
        tester.click(BACK_LINK_CONFIG);
    }

    /**
     * Нажать кнопку "Сменить пароль" в карточке подключения импорта
     */
    public static void clickChangePasswordButton()
    {
        tester.click(GUIXpath.Other.TOOL_BAR + CNHG_PSWD_BUTTON);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
    }

    /**
     * Нажать кнопку "Редактировать" в карточке конфигурации импорта
     */
    public static void clickEditButton()
    {
        tester.click(Div.EDIT);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
    }

    /**
     * Нажать кнопку "Редактировать" в контенте Параметры отображения истории
     */
    public static void clickConfigHistoryEditButton()
    {
        tester.click(CONFIG_HISTORY_EDIT_BUTTON);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
    }

    /**
     * Нажать кнопку "Редактировать" в карточке подключения импорта
     */
    public static void clickEditButtonConnect()
    {
        tester.click(Div.EDIT);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
    }

    /**
     * Откывает форму редактирования конфигурации
     * @param config модель конфигурации
     */
    public static void clickEditConfig(AdvimportConfig config)
    {
        tester.click("//tr[@id='Row-%s']" + GUIXpath.Span.EDIT_ICON, config.getCode());
        GUIForm.assertDialogAppear("Окно редактирования конфигурации не открылось");
    }

    /**
     * Нажать на кнопку "Запустить" в карточке конфигурации импорта
     */
    public static void clickRun()
    {
        tester.click(RUN_BUTTON);
    }

    /**
     * Заполнение полей на форме запуска конфигурации импорта (должна быть раскрыта форма запуска конфигурации импорта)
     * @param params список парметров, используемых на форме запуска импорта
     */
    public static void fillAdvimportConfig(IParameterImport... params)
    {
        try
        {
            for (IParameterImport param : params)
            {
                param.fillAdvimportForm(param.getValue());
            }
        }
        finally
        {
            for (IParameterImport param : params)
            {
                param.clear();
            }
        }
    }

    /**
     * Заполнить форму добавления или редактирования конфигурации импорта
     * @param cfg модель конфигурации импорта
     */
    public static void fillForm(AdvimportConfig cfg)
    {
        fillForm(cfg, true);
    }

    /**
     * Заполнить форму добавления или редактирования конфигурации импорта
     * @param cfg модель конфигурации импорта
     * @param fillCode необходимо ли заполнять поле code. Необходимо для диалога редактирования конфигурации.
     */
    public static void fillForm(AdvimportConfig cfg, boolean fillCode)
    {
        tester.sendKeys(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.TITLE_VALUE, cfg.getTitle());
        if (fillCode)
        {
            tester.sendKeys(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.CODE_VALUE, cfg.getCode());
        }
        GUIScriptField.sendKeysUsingJS(cfg.getConfig());
    }

    /**
     * Заполнить "Скрывать логи старше" на форме настройки отображения логов
     */
    public static void fillHideLogsDays(String value)
    {
        tester.sendKeys(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + Input.VALUE, value);
    }

    /**
     * Перейти в карточку конфигурации импорта.
     * @param cfg модель конфигурации импорта
     */
    public static void goToCard(AdvimportConfig cfg)
    {
        tester.goToPage(StandTypeHolder.getCurrentStandUrl()
                        + GUINavigational.URL_POSTFIX_ADMIN + "#advImportConfig:" + cfg.getCode());
    }

    /**
     * Перейти в карточку конфигурации импорта, затем обновить страницу
     * @param cfg модель конфигурации импорта
     */
    public static void goToCardWithRefresh(AdvimportConfig cfg)
    {
        goToCard(cfg);
        tester.refresh();
    }

    /**
     * Перейти в карточку подключения импорта.
     * @param ac модель подключения импорта
     */
    public static void goToCard(AdvimportConnection ac)
    {
        tester.goToPage(StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN
                        + "#advimport-connection:" + ac.getCode());
    }

    /**
     * Перейти в карточку подключения импорта, затем обновить страницу.
     * @param ac модель подключения импорта
     */
    public static void goToCardWithRefresh(AdvimportConnection ac)
    {
        goToCard(ac);
        tester.refresh();
    }

    /**
     * Перейти на вкладку Подключения
     * (необходимо находиться на карточке Синхронизация)
     */
    public static void goToConnectionTab()
    {
        tester.click(ADVIMPORT_CONNECTION);
    }

    /**
     * Перейти на вкладку Истории
     * (необходимо находиться на карточке конфигурации импорта)
     */
    public static void goToHistoryTab()
    {
        tester.click(ADVIMPORT_CONFIG_HISTORY);
    }

    /**
     * Выставить код конфигурации в списке конфигураций
     * @param config модель конфигурации импорта
     */
    public static void setCode(AdvimportConfig config)
    {
        String href = tester.findDisplayed(CONNECTION_ROW_TITLE, config.getTitle()).getAttribute("href");
        String id = StringUtils.substringAfter(href, "#advImportConfig:");
        config.setCode(id);
        config.setExist(true);
    }

    /**
     * Метод для проверки сохранения некорректной конфигурации
     * @param replaceFrom строка, которую заменим в корректном файле конфигурации
     * @param replaceTo строка, на которую заменим в корректном файле конфигурации
     */
    public static void tryLoadConfig(String replaceFrom, String replaceTo)
    {
        AdvimportConfig advConfig = DAOAdvimport
                .createConfig(FileUtils.readAll("src/test/resources/advimport/config1.xml"));
        GUIScriptField.sendKeysUsingJS(advConfig.getConfig().replace(replaceFrom, replaceTo).replace('\n', ' '));
        GUIForm.clickApply();
        try
        {
            GUIError.waitError(WaitTool.WAIT_TIME);
            Assert.fail("Ошибка не появилась.");
        }
        catch (DialogErrorException e)
        {
            Assert.assertTrue(e.getMessage()
                    .contains("Невозможно сохранить конфигурацию. XML-представление конфигурации некорректно"));
            GUIError.ignoreError();
        }
    }

    /**
     * Возвращает объект для работы со списком версий в истории изменений конфигурации импорта.
     * @return объект для работы со списком версий
     */
    public static GUIAdvListUtil versionsAdvlist()
    {
        if (null == versionsAdvlistConfig)
        {
            versionsAdvlistConfig = new GUIAdvListUtil(VERSIONS_ADVLIST_CONFIG);
        }
        return versionsAdvlistConfig;
    }

    protected GUIAdvimportList(String advlist)
    {
        super(advlist);
    }

    /**
     * Проверить присутствие/отсутствие блока "Свойства" на карточке подключения синхронизации.
     */
    public static void assertPresentAdvImportConnectionCardInfoBlock(boolean isPresent)
    {
        GUITester.assertExists(ADV_IMPORT_CONNECTION_CARD_INFO_BLOCK, isPresent, "Блок 'Свойства' %s"
                .formatted(isPresent ? "отсутствует" : "присутствует"));
    }
}
