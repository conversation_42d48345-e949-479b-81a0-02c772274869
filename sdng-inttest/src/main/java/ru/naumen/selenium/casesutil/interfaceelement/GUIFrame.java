package ru.naumen.selenium.casesutil.interfaceelement;

import static ru.naumen.selenium.core.WaitTool.WAIT_TIME;

import java.util.List;
import java.util.Locale;
import java.util.function.Consumer;

import org.junit.Assert;
import org.openqa.selenium.By;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.StaleElementReferenceException;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.core.BrowserTS.WebBrowserType;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.WebTester;
import ru.naumen.selenium.core.config.Config;

import com.google.common.base.Function;
import com.google.common.base.Preconditions;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

/**
 * Утилитарные методы для работы с iframe-ми
 * <AUTHOR>
 * @since 25.12.2014
 */
public class GUIFrame extends CoreTester
{
    private static final String MSG_APPEAR_FRAME = "В контенте отображается iframe";
    private static final String MSG_DISAPPEAR_FRAME = "В контенте не отображается iframe";
    private static final String MSG_APPEAR_IMG = "В контенте отображается img";

    public static final String X_Path_TO_IFRAME = "//iframe[contains(@id,'iframe')]";

    /**
     * Выполнить действие в IFrame, при этом ничего не возвращая 
     * @param xPathToIFrame - путь до IFrame
     * @param action - выполняемое действие 
     */
    @SuppressFBWarnings
    public static void actionsInFrame(String xPathToIFrame, Consumer<WebTester> action)
    {
        actionsInFrame(tester.find(xPathToIFrame), action);
    }

    /**
     * Выполнить действие в IFrame для веб элемента 
     * @param element - IFrame, в котором нужно выполнить действия
     * @param action - выполняемое действие 
     */
    @SuppressFBWarnings
    public static void actionsInFrame(WebElement element, Consumer<WebTester> action)
    {
        tester.getWebDriver().switchTo().frame(element);
        action.accept(tester);
        tester.getWebDriver().switchTo().defaultContent();
    }

    /**
     * Выполнить действие в IFrame для текущих веб элементов 
     * @param action - выполняемое действие 
     */
    @SuppressFBWarnings
    public static void actionsInFrameForActiveWebElement(Consumer<WebTester> action)
    {
        actionsInFrame(tester.actives().getElement(), action);
    }

    /**
     * Проверить, что  iframe отображается, по xpath
     * @param xpath - xpath IFrame
     */
    public static void appearFrame(String xpath)
    {
        Assert.assertTrue(MSG_DISAPPEAR_FRAME, tester.waitAppear(xpath));
    }

    /**
     * Проверить, что  iframe отображается, по id
     * @param id - id IFrame
     */
    public static void appearFrameId(String id)
    {
        String body = GUIFrame.getTextFromFrame("//*[@id='" + id + "']", "//body");
        Assert.assertTrue(MSG_DISAPPEAR_FRAME, "".equals(body));
    }

    /**
     * Проверить количество картинок в iframe
     * @param xpath путь до iframe-а, может иметь символы форматирования
     * @param expected ожидаемое количество картинок
     * @param args символы форматироваия для xpath, могут отсутствовать
     */
    public static void assertImgCount(String xpath, int expected, Object... args)
    {
        String failMsg = "Полученное количество изображений не совпало с ожидаемым.";
        //TODO for chrome
        if (WebBrowserType.CHROME.toString().equals(Config.get().getWebBrowserType().toUpperCase(Locale.ENGLISH)))
        {
            //@formatter:off
            String js = "var theFrameDocument = arguments[0].contentDocument || arguments[0].contentWindow.document;"
                      + "return theFrameDocument.getElementsByTagName('img').length;";
            //@formatter:on

            WebElement iframe = tester.find(xpath, args);
            Boolean actual = WaitTool.waitSomething(tester.getWebDriver(), new Function<WebDriver, Boolean>()
            {
                @Override
                public Boolean apply(WebDriver input)
                {
                    Long actual = (Long)tester.runJavaScript(js, iframe);
                    return actual.equals(Long.valueOf(expected));
                }
            });
            Assert.assertTrue(failMsg, actual != null && actual);
        }
        else
        {
            try
            {
                WebDriver frame = tester.getWebDriver().switchTo().frame(tester.find(xpath, args));
                List<WebElement> images = WaitTool.waitForListDisplayedElementsPresent(frame, By.tagName("img"),
                        WAIT_TIME);
                Assert.assertEquals(failMsg, expected, images.size());
            }
            catch (StaleElementReferenceException e)
            {
                assertImgCount(xpath, expected, args);
            }
            catch (TimeoutException e)
            {
                Assert.assertEquals(failMsg, expected, 0);
            }
            finally
            {
                tester.getWebDriver().switchTo().defaultContent();
            }
        }
    }

    /**
     * Проверить текст в iframe-е, ожидает в течение времени ожидания ({@link WaitTool#WAIT_TIME})
     * (проверяется, что указанный текст содержится в iframe-е)
     * @param xpath путь до iframe-а, может иметь символы форматирования
     * @param text ожидаемый текст
     * @param msg сообщение об ошибке
     * @param args символы форматирования для xpath, могут отсутствовать
     * @return true если элемент появился в течение времени ожидания, false - элемент все
     * еще отсутствует на странице, по прошествию времени ожидания
     */
    public static void assertText(final String xpath, final String text, String msg, final Object... args)
    {
        Preconditions.checkNotNull(text);
        String xpathIframe = String.format(xpath, args);
        Assert.assertTrue(msg, getValueFromFrame(xpathIframe, (tester) ->
        {
            return WaitTool.waitForContainsText(tester.getBrowserTS().getWebDriver(), By.tagName("body"), text,
                    WAIT_TIME);
        }));
    }

    /**
     * Проверить совпадение текста во встроенном iframe-е (когда внутри RTF-текста добавлен другой RTF-текст).
     * Проверяется, что указанный текст содержится во встроенном iframe-е.
     * @param xpath путь до основного iframe-а, может иметь символы форматирования
     * @param text ожидаемый текст
     * @param msg сообщение об ошибке
     * @param args символы форматирования для xpath, могут отсутствовать
     */
    public static void assertTextInEmbeddedRichText(String xpath, String text, String msg, Object... args)
    {
        Preconditions.checkNotNull(text);
        String xpathIframe = String.format(xpath, args);

        //сперва получить значение основного iframe
        getValueFromFrame(xpathIframe, (tester) ->
        {
            final WebDriver webDriverIframe = tester.getBrowserTS().getWebDriver();
            //а затем уже проверить внутренний iframe
            Assert.assertTrue(
                    msg,
                    getValueFromFrame(webDriverIframe, webDriverIframe.findElement(By.tagName("iframe")),
                            (testerEmbedded) ->
                                    WaitTool.waitForContainsText(
                                            testerEmbedded.getBrowserTS().getWebDriver(),
                                            By.tagName("body"),
                                            text,
                                            WAIT_TIME)));
            return true;
        });
    }

    /**
     * Кликнуть на элемент, расположенный в iframe
     * @param xPathToIFrame - путь до IFrame
     * @param xpath элемента, может иметь символы форматирования
     * @param args параметры для форматированного xpath, могут отсутствовать
     */
    public static void clickInFrame(String xPathToIFrame, String xpath, Object... args)
    {
        actionsInFrame(xPathToIFrame, (tester) ->
        {
            tester.click(xpath, args);
        });
    }

    /**
     * Кликнуть по конкретному тексту в iframe-е
     * @param xpath путь до iframe-а, может иметь символы форматирования
     * @param text текст
     * @param args символы форматирования для xpath, могут отсутствовать
     */
    public static void clickText(String xpath, String text, Object... args)
    {
        GUIError.expectError();
        Preconditions.checkNotNull(text);

        actionsInFrame(String.format(xpath, args), (tester) ->
        {
            WebElement editorBody = tester.getWebDriver().findElement(By.tagName("body"));
            String textXpath = String.format(GUIXpath.Any.TEXT_PATTERN, text);
            editorBody.findElement(By.xpath(textXpath)).click();
        });
    }

    /**
     * Проверить, что  iframe не отображается, по id
     * @param id - id IFrame
     */
    public static void disappearFrameId(String id)
    {
        String body = GUIFrame.getTextFromFrame("//*[@id='" + id + "']", "//body");
        Assert.assertFalse(MSG_APPEAR_FRAME, "".equals(body));
    }

    /**
     * Проверить, что  в iframe не отображается картинка
     * @param xpath - xpath IFrame
     */
    public static void disappearImgFrame(String xpath)
    {
        Assert.assertTrue(MSG_APPEAR_IMG, tester.waitDisappear(xpath));
    }

    /**
     * Получить текст элемента, расположенного в iframe
     * @param xPathToIFrame - путь до IFrame
     * @param xpath элемента, может иметь символы форматирования
     * @param args параметры для форматированного xpath, могут отсутствовать
     *  @return текст элемента
     */
    public static String getTextFromFrame(String xPathToIFrame, String xpath, Object... args)
    {
        return getValueFromFrame(xPathToIFrame, (tester) ->
        {
            return tester.getText(xpath, args);
        });
    }

    /**
     * Получить html элемента, расположенного в iframe
     * @param xPathToIFrame - путь до IFrame
     * @param xpath элемента, может иметь символы форматирования
     * @param args параметры для форматированного xpath, могут отсутствовать
     *  @return html элемента
     */
    public static String getHtmlFromFrame(String xPathToIFrame, String xpath, Object... args)
    {
        return getValueFromFrame(xPathToIFrame, (tester) -> tester.getHtml(xpath, args));
    }

    /**
     * Выполнить действие в IFrame, и вернуть результат 
     * @param xPathToIFrame - путь до IFrame
     * @param action - выполняемое действие
     * @return результат действия
     */
    @SuppressFBWarnings
    public static <T> T getValueFromFrame(String xPathToIFrame, Function<WebTester, T> action)
    {
        return getValueFromFrame(tester.getWebDriver(), tester.find(xPathToIFrame), action);
    }

    /**
     * Выполнить действие в IFrame, и вернуть результат
     * @param iframe - IFrame, на который нужно переключиться
     * @param action - выполняемое действие
     * @return результат действия
     */
    @SuppressFBWarnings
    public static <T> T getValueFromFrame(WebDriver webdriver, WebElement iframe, Function<WebTester, T> action)
    {
        webdriver.switchTo().frame(iframe);
        T result = action.apply(tester);
        webdriver.switchTo().defaultContent();
        return result;
    }

    /**
     * Ввести текст в iframe-е
     * @param xpath путь до iframe-а, может иметь символы форматирования
     * @param text текст
     * @param args символы форматироваия для xpath, могут отсутствовать
     */
    public static void sendKeys(String xpath, String text, Object... args)
    {
        GUIError.expectError();
        Preconditions.checkNotNull(text);

        actionsInFrame(String.format(xpath, args), (tester) ->
        {
            WebElement editorBody = tester.getWebDriver().findElement(By.tagName("body"));
            editorBody.sendKeys(text);
        });
    }

    /**
     * Послать последовательность символов в элемент, расположенный в iframe
     * @param xPathToIFrame - путь до IFrame
     * @param xpath элемента, может иметь символы форматирования
     * @param keysToSend последовательность символов
     * @param args параметры для форматированного xpath, могут отсутствовать
     * @throws NoSuchElementException если не найден ни один соответствующий элемент
     */
    public static void sendKeysInFrame(String xPathToIFrame, String xpath, String keysToSend, Object... args)
    {
        actionsInFrame(xPathToIFrame, (tester) ->
        {
            tester.sendKeys(xpath, keysToSend, args);
        });
    }

    /**
     * Послать последовательность символов в текущий элемент, расположенный в iframe
     * @param xPathToIFrame - путь до IFrame
     * @param xpath элемента, может иметь символы форматирования
     * @param keysToSend последовательность символов
     * @param args параметры для форматированного xpath, могут отсутствовать
     * @throws NoSuchElementException если не найден ни один соответствующий элемент
     */
    public static void sendKeysInFrameForActiveWebElement(String keysToSend)
    {
        actionsInFrameForActiveWebElement((tester) ->
        {
            WebElement editorBody = tester.getWebDriver().findElement(By.tagName("body"));
            editorBody.sendKeys(keysToSend);
        });
    }

    /**
     * Ввести текст в iframe-е при помощи JS (позволяет записывать специальные символы под браузером GC)
     * Перед вводом поле ввода очищается
     * @param xpath путь до iframe-а, может иметь символы форматирования
     * @param text текст
     * @param args символы форматироваия для xpath, могут отсутствовать
     */
    public static void sendKeysJs(String xpath, String text, Object... args)
    {
        GUIError.expectError();
        Preconditions.checkNotNull(text);

        // Заменяем на специальные символы html
        String updatedText = text.replaceAll(" ", "&nbsp;").replaceAll("<", "&lt;").replaceAll(">", "&gt;");

        // открытие и закрытие документа добавлены для очистки поля ввода
        //@formatter:off
        String js = "var theFrameDocument = arguments[0].contentDocument || arguments[0].contentWindow.document;"
                + "theFrameDocument.open(); "
                + "theFrameDocument.write(arguments[1]); "
                + "theFrameDocument.close();";
        //@formatter:on

        WebElement iframe = tester.find(xpath, args);
        tester.runJavaScript(js, iframe, updatedText);
    }

    /**
     * Переключить контекст во фрейм
     * @param xpath XPath для фрейма
     * @param args аргументы для форматирования xpath
     */
    @SuppressFBWarnings(value = "RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT", justification = "На самом деле сайд эффект "
                                                                                          + "есть: переключение "
                                                                                          + "контекста")
    public static void switchToFrame(String xpath, Object... args)
    {
        WaitTool.waitForElement(tester.getWebDriver(), tester.byXpath(xpath, args), 10);
        tester.getWebDriver().switchTo().frame(tester.find(xpath, args));
    }

    /**
     * Переключить контекст в главное окно
     */
    @SuppressFBWarnings(value = "RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT", justification = "На самом деле сайд эффект "
                                                                                          + "есть: переключение "
                                                                                          + "контекста")
    public static void switchToTopWindow()
    {
        tester.getWebDriver().switchTo().defaultContent();
    }

}
