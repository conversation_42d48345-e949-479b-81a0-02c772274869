package ru.naumen.selenium.casesutil.eventaction;

import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.modules.IModuleFeatureConfiguration;
import ru.naumen.selenium.modules.ScriptModules;

/**
 * Утилитарные методы для работы c внешним шлюзом:
 * - с настройками видимости ДПС с действием "Отправка во внешнюю очередь"
 * <AUTHOR>
 * @since 03.09.2020
 **/
public class DSLFeatureConfiguration
{
    /**
     * Получить модуль для работы с внешним шлюзом (действий "Отправка во внешнюю очередь" в ДПС)
     */
    public static IModuleFeatureConfiguration getGatewayIntegrationModule()
    {
        return ScriptModules.getModuleFeatureConfiguration();
    }

    /**
     * Выключить интеграцию с внешним шлюзом
     * Отключить действие "Отправка во внешнюю очередь"
     */
    public static void disableGatewayIntegration()
    {
        getGatewayIntegrationModule().disableGatewayIntegration();
    }

    /**
     * Включить интеграцию с внешним шлюзом
     * Включить действие "Отправка во внешнюю очередь"
     * по окончанию теста отключает обратно, т.к. по дефолту доступ выключен
     */
    public static void enableGatewayIntegration()
    {
        getGatewayIntegrationModule().enableGatewayIntegration();
        Cleaner.afterTest(DSLFeatureConfiguration::disableGatewayIntegration);
    }
}