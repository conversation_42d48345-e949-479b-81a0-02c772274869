package ru.naumen.selenium.casesutil;

import org.junit.Assert;
import org.openqa.selenium.JavascriptExecutor;

import ru.naumen.selenium.core.WaitTool;

/**
 * Методы для работы со всплывающими уведомлениями
 * <AUTHOR>
 * @since 12.11.15
 */
public class GUIPush extends CoreTester
{
    public static final String NOTIFICATION = "//*[@id='gwt-debug-notification']";
    public static final String NOTIFICATION_CIRCLE = "//*[@id='gwt-debug-notificationLogIcon']/div";
    public static final String NOTIFICATION_CIRCLE_RGB_COLOR = "rgb(235, 87, 87)";
    public static final String NOTIFICATION_CIRCLE_RGBA_COLOR = "rgba(235, 87, 87, 1)";
    public static final String NOTIFICATION_CLOSE_BUTTON = NOTIFICATION + "//div[@id='gwt-debug-closeNotification']";
    public static final String NOTIFICATION_EXPAND_BUTTON = NOTIFICATION + "//div[@_code='up']";
    public static final String NOTIFICATION_MENTION_LINK = NOTIFICATION + "//*[@class='atwho-inserted']";
    public static final String NOTIFICATION_LINK = NOTIFICATION + GUIXpath.A.ID_PATTERN;
    public static final Integer NOTIFICATION_ANIMATION_TIME_MS = 700;

    /**
     * Проверяет, что количество включенных действий по событию типа "Уведомление"
     * соответствует ожидаемому количеству
     * @param expectedCount ожидаемое количество включенных уведомлений
     */
    public static void assertCountOfEnabledPushNotifications(int expectedCount)
    {
        JavascriptExecutor executor = (JavascriptExecutor)tester.getWebDriver();
        int count = Integer
                .parseInt(executor.executeScript("return window.countOfEnabledPushNotifications").toString());
        Assert.assertEquals("Количество включенных уведомлений не соответствует ожидаемому", expectedCount, count);
    }

    /**
     * Проверка сообщения в появившемся уведомлении
     * @param expectedMessage ожидаемое сообщение
     */
    public static void assertPush(String expectedMessage)
    {
        // Уведомления появляются после анимации. 
        WaitTool.waitMills(NOTIFICATION_ANIMATION_TIME_MS);
        GUITester.assertTextPresentWithMsg(NOTIFICATION, expectedMessage,
                "Полученное значение уведомления не совпало с ожидаемым.");
    }

    /**
     * Проверить, что всплывающие уведомление содержит переданный html
     * @param html содержимое
     */
    public static void assertPushHtmlContains(String html)
    {
        // Уведомления появляются после анимации.
        WaitTool.waitMills(NOTIFICATION_ANIMATION_TIME_MS);
        String innerHtml = GUITester.getAttributeProperty(NOTIFICATION, "innerHTML");
        Assert.assertTrue("Строка '" + html + "' не содержится в тексте '" + innerHtml + "'", innerHtml.contains(html));
    }

    /**
     * Проверить, что всплывающие уведомление НЕ содержит переданный html
     * @param html содержимое
     */
    public static void assertPushHtmlNotContains(String html)
    {
        // Уведомления появляются после анимации.
        WaitTool.waitMills(NOTIFICATION_ANIMATION_TIME_MS);
        String innerHtml = GUITester.getAttributeProperty(NOTIFICATION, "innerHTML");
        Assert.assertFalse("Строка '" + html + "' содержится в тексте '" + innerHtml + "'", innerHtml.contains(html));
    }

    /**
     * Проверить отсутствие всплывающего уведомления на странице   
     */
    public static void assertPushAbsence()
    {
        Assert.assertTrue("На странице присутствует уведомление.", tester.waitDisappear(5, NOTIFICATION));
    }

    /**
     * Проверить присутствие всплывающего уведомления на странице   
     */
    public static void assertPushPresent()
    {
        GUITester.assertPresent(NOTIFICATION, "На странице отсутствует уведомление.");
    }

    /**
     * Нажать кнопку "Закрыть" на всплывающем уведомлении
     */
    public static void clickClosePushButton()
    {
        assertPushPresent();
        tester.click(NOTIFICATION_CLOSE_BUTTON);
        // Уведомления исчезают после анимации 
        assertPushAbsence();
    }

    /**
     * Развернуть всплывающее уведомление
     */
    public static void clickExpand()
    {
        assertPushPresent();
        tester.click(NOTIFICATION_EXPAND_BUTTON);
    }

    /**
     * Нажать ссылку внутри всплывающего уведомления
     * @param linkId идентификатор ссылки в рамках уведомления
     */
    public static void clickLinkById(String linkId)
    {
        tester.click(String.format(NOTIFICATION_LINK, linkId));
        // Уведомления исчезают после анимации  
        assertPushAbsence();
    }

    /**
     * Нажать ссылку внутри всплывающего уведомления  
     */
    public static void clickMentionLink()
    {
        tester.click(NOTIFICATION_MENTION_LINK);
        // Уведомления исчезают после анимации  
        assertPushAbsence();
    }

    /**
     * Нажать ссылку внутри всплывающего уведомления по xpath
     */
    public static void clickMentionLink(String xpath)
    {
        tester.click(NOTIFICATION + xpath);
        // Уведомления исчезают после анимации  
        assertPushAbsence();
    }
}
