package ru.naumen.selenium.casesutil.embeddedapplication;

import jakarta.annotation.Nullable;

/**
 * Утилитарные методы для диалогового окна
 *
 * <AUTHOR>
 * @since 01.12.2023
 */
public class JsApiModalDialogBuilderTemplates
{
    /**
     * Поддерживаемые типы кнопок
     */
    public enum Button
    {
        OK("Ok"),
        YES("Yes"),
        NO("No"),
        CANCEL("Cancel");

        private final String jsCode;

        Button(String jsCode)
        {
            this.jsCode = jsCode;
        }

        public String asCode()
        {
            return jsCode.toLowerCase();
        }
    }

    /**
     * Создаёт через js-код построитель диалогового окна
     *
     * @param content контент для выведения в диалоговом окне
     */
    public static String withCreateDialogBuilder(@Nullable String content)
    {
        return String.format("jsApi.modals.getDialogBuilder(%s)%n", content != null ? "'%s'".formatted(content) : null);
    }

    /**
     * Устанавливает через js-код название для диалогового окна
     *
     * @param title название диалогового окна
     */
    public static String withTitle(@Nullable String title)
    {
        return String.format("\t.setTitle(%s)%n", title != null ? "'%s'".formatted(title) : null);
    }

    /**
     * Добавляет через js-код кнопку на диалоговое окно
     *
     * @param button кнопка
     */
    public static String withButton(Button button)
    {
        return String.format("\t.add%sButton()%n", button.jsCode);
    }

    /**
     * Добавляет через js-код кнопку с пользовательским названием на диалоговое окно
     *
     * @param button кнопка
     * @param title название кнопки
     */
    public static String withButton(Button button, String title)
    {
        return String.format("\t.add%sButton('%s')%n", button.jsCode, title);
    }
}
