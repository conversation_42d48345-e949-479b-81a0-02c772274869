package ru.naumen.selenium.casesutil.metaclass;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.model.metaclass.UsagePointApplication;

/**
 * Утилитарные методы для работы со списком мест использования встроенных приложений
 * <AUTHOR>
 * @since 22.11.2021
 */
public class GUIEmbeddedApplicationUsagePointsList extends GUIAdvListUtil
{
    private static final String ADVLIST = "gwt-debug-usagePointsListApplication";

    /**Пиктограммы*/
    public static final String PICT_EDIT = "editUsagePointApplication";
    public static final String PICT_DELETE = "deleteUsagePointApplication";

    private static volatile GUIEmbeddedApplicationUsagePointsList advlist;

    public static GUIEmbeddedApplicationUsagePointsList advlist()
    {
        if (advlist == null)
        {
            advlist = new GUIEmbeddedApplicationUsagePointsList();
        }
        return advlist;
    }

    /**
     * Проверить наличие места использования приложения в списке
     */
    public static void assertPresenceUsagePointApplication(UsagePointApplication... model)
    {
        advlist().content().asserts().rowsPresence(model);
    }

    /**
     * Проверить отсутствие места использования приложения в списке
     */
    public static void absencePresenceUsagePointApplication(UsagePointApplication... model)
    {
        advlist().content().asserts().rowsAbsence(model);
    }

    /**
     * Проверить, что присутствует блок "Места использования"
     */
    public static void assertBlockExist()
    {
        GUITester.assertTextPresent(GUIXpath.Div.X_BLOCK_TITLE, "Места использования приложения на модальных формах",
                "usagePlaces");
    }

    /**
     * Нажать кнопку "Добавить место использования"
     */
    public static void clickAdd()
    {
        advlist().toolPanel().clickAdd();
    }

    /**
     * Нажать кнопку "Удалить"
     * @param model - модель места использования приложения
     */
    public static void clickDelete(UsagePointApplication model)
    {
        advlist().content().clickPict(model, GUIEmbeddedApplicationUsagePointsList.PICT_DELETE);
    }

    /**
     * Открыть форму редактирования приложения
     * @param model - модель места использования приложения
     */
    public static void clickEdit(UsagePointApplication model)
    {
        advlist().content().clickPict(model, GUIEmbeddedApplicationUsagePointsList.PICT_EDIT);
    }

    private GUIEmbeddedApplicationUsagePointsList()
    {
        super(ADVLIST);
    }
}
