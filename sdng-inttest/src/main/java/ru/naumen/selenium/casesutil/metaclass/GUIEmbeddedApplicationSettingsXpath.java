package ru.naumen.selenium.casesutil.metaclass;

import ru.naumen.selenium.casesutil.GUIXpath.Input;
import ru.naumen.selenium.casesutil.GUIXpath.Other;
import ru.naumen.selenium.casesutil.model.Model;
import ru.naumen.selenium.casesutil.model.ModelCode;

/**
 * Константы, необходимые для формирования xpath для настройки встроенных приложений
 *
 * <AUTHOR>
 * @since 07.11.2022
 */
public interface GUIEmbeddedApplicationSettingsXpath
{
    /**
     * Константы, необходимые для формирования xpath для формы настройки ВП
     */
    interface Form
    {
        String TITLE = String.format(Input.ANY_VALUE, Model.TITLE);
        String CODE = String.format(Input.ANY_VALUE, ModelCode.CODE);
        String DESCRIPTION = String.format(Other.TEXTAREA_ANY_VALUE, "description");
        String SERVER_LINK = String.format(Input.ANY_VALUE, "serverLink");
        String APPLICATION_TYPE = String.format(Input.ANY_VALUE_INPUT, "applicationType");
        String APPLICATION_FILE = "//*[@id='gwt-debug-applicationArchive-value']";
        String INITIAL_HEIGHT = String.format(Input.ANY_VALUE, "initialHeight");
    }
}
