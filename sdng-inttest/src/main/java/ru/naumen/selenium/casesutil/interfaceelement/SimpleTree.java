package ru.naumen.selenium.casesutil.interfaceelement;

import java.util.List;

import org.junit.Assert;
import org.openqa.selenium.WebElement;

import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;

/**
 * Общие методы для работы с выпадающими списками типа дерево, в котором id каждого узла никак не завязан
 * на id родительского узла
 * <AUTHOR>
 * @since 18.06.2014
 */
public class SimpleTree extends CoreTree
{
    public SimpleTree(String treeIdOrXpath, Object... args)
    {
        super(treeIdOrXpath, args);
    }

    private static final String DELETE_TAG_FORM_ITEM = "%s" + GUIXpath.Any.ANY + GUIXpath.Span.CLOSE2;

    @Override
    public String getXNode(String... nodes)
    {
        StringBuilder buffer = new StringBuilder(X_VALUE_TREE);
        int level = 1;
        for (String node : nodes)
        {
            buffer.append(String.format(X_TREE_ITEM, node, level++));
        }
        return buffer.toString();
    }

    /** Получить все дочерние элементы родительского узла. Список должен быть открыт, узел должен быть развернут.
     * @param nodes перечисление узлов до узла
     * @return
     */
    private List<WebElement> getChildElements(String... nodes)
    {
        return tester.findElements(getXNode(nodes) + Div.TREE_ITEM);
    }

    /** Проверить, что все элементы родительского узла выбраны (проставлены чекбоксы). Список должен быть открыт, узел
     * должен быть развернут.
     * @param nodes перечисление узлов до узла, элементы которого надо проверить
     */
    public void assertChildSelected(String... nodes)
    {
        List<WebElement> childElements = getChildElements(nodes);
        for (WebElement item : childElements)
        {
            Assert.assertTrue(String.format("Элемент \"%s\" не выбран!", item.getText()),
                    Boolean.parseBoolean(item.getAttribute("aria-selected")));
        }
    }

    /**
     * Удалить "плашки" в поле типа Дерево
     * @param ids id элементов, которые надо удалить
     */
    public void clickDeleteTagFormItems(String... ids)
    {
        for (String item : ids)
        {
            tester.click(DELETE_TAG_FORM_ITEM, treeXpath, item);
        }
    }
}
