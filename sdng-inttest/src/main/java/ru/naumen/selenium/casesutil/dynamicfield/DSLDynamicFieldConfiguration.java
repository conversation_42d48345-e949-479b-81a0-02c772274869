package ru.naumen.selenium.casesutil.dynamicfield;

import ru.naumen.selenium.casesutil.model.dynamicfield.DynamicFieldConfiguration;
import ru.naumen.selenium.modules.IModuleDynamicFieldConfiguration;
import ru.naumen.selenium.modules.ScriptModules;

/**
 * Методы для работы с конфигурацией динамических полей.
 * <AUTHOR>
 * @since Apr 07, 2024
 */
public class DSLDynamicFieldConfiguration
{
    private static IModuleDynamicFieldConfiguration getModule()
    {
        return ScriptModules.getModuleDynamicFieldConfiguration();
    }

    /**
     * Перезагружает конфигурацию динамических полей.
     */
    public static void reload()
    {
        getModule().reload();
    }

    /**
     * Перезагружает конфигурацию динамических полей из указанного объекта.
     * @param configuration объект с описанием конфигурации
     */
    public static void reload(DynamicFieldConfiguration configuration)
    {
        getModule().reload(ConfigurationXmlSerializer.serialize(configuration));
        configuration.setApplied(true);
    }

    /**
     * Перезагружает конфигурацию динамических полей из строки-содержимого файла конфигурации xml.
     * @param xml строка с содержимым
     */
    public static void reload(String xml)
    {
        getModule().reload(xml);
    }
}
