package ru.naumen.selenium.casesutil.omnichannel.chatOnSite;

import ru.naumen.selenium.casesutil.omnichannel.ChatOnSite;

/**
 * Содержит параметры подключения к доступным витринам чата на сайте (в наших терминах витрина - это фактически канал):
 * <ol>
 *     <li>chatOnSite - чат на сайте которому принадлежит витрина см. {@link ChatOnSite}</li>
 *     <li>caseId - идентификатор витрины</li>
 *     <li>caseLogin - логин подключения к витрине</li>
 *     <li>casePassword - пароль подключения к витрине</li>
 * </ol>
 * <AUTHOR>
 * @since 22.07.2022
 */
public enum ShowCase
{
    MESSAGE_BOX_CASE_1(ChatOnSite.MESSAGE_BOX, "25", "portal_test", "123"),
    MESSAGE_BOX_CASE_2(ChatOnSite.MESSAGE_BOX, "69", "portal_test2", "123"),

    OMNI_GATE_CASE_1(ChatOnSite.OMNI_GATE, "45", "test1", "123"),
    OMNI_GATE_CASE_2(ChatOnSite.OMNI_GATE, "46", "test2", "123");

    private final ChatOnSite chatOnSite;
    private final String caseId;
    private final String caseLogin;
    private final String casePassword;

    ShowCase(ChatOnSite chatOnSite, String caseCode, String caseLogin, String casePassword)
    {
        this.chatOnSite = chatOnSite;
        this.caseId = caseCode;
        this.caseLogin = caseLogin;
        this.casePassword = casePassword;
    }

    public String getCaseLogin()
    {
        return caseLogin;
    }

    public String getCasePassword()
    {
        return casePassword;
    }

    public String getCaseId()
    {
        return caseId;
    }

    public ChatOnSite getChatOnSite()
    {
        return chatOnSite;
    }
}
