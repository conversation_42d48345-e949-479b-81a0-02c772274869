package ru.naumen.selenium.casesutil.escalation;

import java.util.List;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.interfaceelement.MetaTree;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.user.StandTypeHolder;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для работы эскалациями через интерфейс
 * <AUTHOR>
 * @since 30.03.2013
 *
 */
public class GUIEscalation extends CoreTester
{
    public static final String SELECT_INPUT_TIMER = "//div[@id='gwt-debug-timer-value']//input";

    public static final String ESCALATION_SCEME_ROW_PTRN = "//*[@id='Row-%s']";

    public static final String TITLE_FROM_ROW_PTRN = ESCALATION_SCEME_ROW_PTRN + "//a[@id='title']";

    public static final String TARGET_OBJECT_FROM_ROW_PTRN = ESCALATION_SCEME_ROW_PTRN + "//span[@id='targetObject']";

    public static final String DESCRIPTION_FROM_ROW_PTRN = ESCALATION_SCEME_ROW_PTRN + "//span[@id='description']";

    public static final String TIMER_FROM_ROW_PTRN = ESCALATION_SCEME_ROW_PTRN + "//span[@id='timer']";

    public static final String ON_OR_OFF_FROM_ROW_PTRN = ESCALATION_SCEME_ROW_PTRN
                                                         + "//span[@id='timer']/../../self::*/following::*[1]/div/img";

    public static final String ESCALATION_LEVEL_ROW_PTRN = "//*[@id='level' and text()='%s']/../../../td";

    public static final String ESCALATION_LEVEL_LEVEL_PTRN = ESCALATION_LEVEL_ROW_PTRN + "//*[@id='level']";

    public static final String ESCALATION_LEVEL_CONDITION_PTRN = ESCALATION_LEVEL_ROW_PTRN + "//*[@id='condition']";

    public static final String ESCALATION_LEVEL_VALUE_PTRN = ESCALATION_LEVEL_ROW_PTRN + "//*[@id='value']";
    public static final String ESCALATION_LEVEL_ACTION_PTRN = ESCALATION_LEVEL_ROW_PTRN + "[6]";
    public static final String ESCALATION_LEVEL_EXECUTE_PTRN = ESCALATION_LEVEL_ROW_PTRN
                                                               + "//span[@id='gwt-debug-executeAction']"; // fontIcon

    public static final String ADD_ESCALATION_SCHEME_BUTTON_VALUE = "gwt-debug-targetObjects-value";

    /** Используется в качестве определяемого атрибута таблицы соответствий эскалации */
    public static final String ESCALATION_TARGET_DATA = "escalationTargetData";

    public static final String ESCALATION_OBJECTS = "//tr[@__code='objects']";
    public static final String ESCALATION_SCHEME_OBJECTS = "//tr[@id='Row-%s']//span[@id='targetObject']/a";

    /** Путь до пиктограмки "Редактировать" в списке схем экскалаций */
    public static final String ESCALATION_EDIT_ICON = "//tr[@__did='%s']" + GUIXpath.Span.EDIT_ICON;

    /** Путь до пиктограмки "Удалить" в списке схем экскалаций */
    public static final String ESCALATION_DELETE_ICON = "//tr[@__did='%s']" + GUIXpath.Span.DELETE_ICON;

    public static final String ESCALATION_SCHEME_CARD_ATTRS_BLOCK = String.format(GUIXpath.Div.ANY, "attrs");
    public static final String ESCALATION_SCHEME_CARD_LEVELS_BLOCK = String.format(GUIXpath.Div.ANY, "levels");

    /**
     * Проверить отсутствие заданной схемы в списке схем эскалаций
     * @param scheme схема эскалации, отсутствие которой проверяется
     */
    public static void assertAbsenseEscalationSchemeInList(EscalationScheme scheme)
    {
        GUITester.assertExists(String.format(TITLE_FROM_ROW_PTRN, scheme.getCode()), false,
                "Указанная схема эскалации присутсвует в списке схем");
    }

    /**
     * Проверить, что открыта вкладка Действия
     */
    public static void assertActionsTabSelected()
    {
        String message = String.format("Вкладка 'Действия' не является активной.");
        Assert.assertTrue(message, tester
                .waitAppear(GUIXpath.divGwtDebugId("actions") + "//ancestor::div[contains(@class, '-selected')]"));
    }

    /**
     * Проверка отсутствия уровня эскалации
     * @param level уровень эскалации
     */
    public static void assertAbsenceEscalationLevel(String level)
    {
        String message = String.format("Уровень %s присутствует", level);
        GUITester.assertAbsent(ESCALATION_LEVEL_LEVEL_PTRN, message, level);
    }

    /**
     * Проверка полей указанного уровня схемы эскалации в карточке Эскалация на вкадке "Схемы эскалации"
     * (Для вызова данного метода необходимо находится в карточке "Схемы эскалации"
     * (ИА->Настройка бизнес процессов->Эскалация[вкладка: Схемы эскалации]))
     * @param level уровень эскалации
     * @param condition условие
     * @param value значение
     * @param executeAction выполняется ли действие при изменении схемы
     * @param eventActions действия
     */
    public static void assertEscalationLevel(String level, String condition, String value, boolean executeAction,
            EventAction... eventActions)
    {
        //Уровень эскалации
        GUITester.assertTextPresent(ESCALATION_LEVEL_LEVEL_PTRN, level, level);
        //Условие уровня эскалации
        GUITester.assertTextPresentWithMsg(ESCALATION_LEVEL_CONDITION_PTRN, condition,
                "Ожидаемое условие уровня эскалации не совпало с полученным.", level);
        //Значение уровня эскалации
        GUITester.assertTextPresentWithMsg(ESCALATION_LEVEL_VALUE_PTRN, value,
                "Ожидаемое значение уровня эскалации не совпало с полученным.", level);
        //Названия действий уровня эскалации
        for (EventAction eventAction : eventActions)
        {
            String actionTitle = eventAction.getTitle();
            String message = String.format("Уровень эскалации %s действие с названием '%s', но не содержит его.", value,
                    actionTitle);
            GUITester.assertTextContainsWithMsg(ESCALATION_LEVEL_ACTION_PTRN, actionTitle, message, level);
        }
        //Выполнять действие при изменении схемы эскалации
        String style = tester.findDisplayed(String.format(ESCALATION_LEVEL_EXECUTE_PTRN, level)).getAttribute("class");
        Assert.assertTrue("Ожидаемое выполнение действия при изменении схемы не совпало с полученным",
                style.contains(executeAction ? GUIXpath.Constant.YES : GUIXpath.Constant.NO));
    }

    /**
     * Проверка названия эскалации в карточке (необходимо находится на карточке эскалации)
     * @param title ожидаемое название эскалации
     */
    public static void assertEscalationObjectNameOnCard(String title)
    {
        GUITester.assertTextContains(ESCALATION_OBJECTS + GUIXpath.Any.VALUE, title);
    }

    /**
     * Проверка полей схемы в списке схем эскалаций на вкадке "Схемы эскалации"
     * (ИА->Настройка бизнес процессов->Эскалация[вкладка: Схемы эскалации]))
     * @param code код схемы эскалации
     * @param title название схемы эскалации
     * @param description описание схемы эскалации
     * @param objectTitle название класса объектов. 
     * @param counterTitle название счетчика времени
     * @param on включена ли данная схема или нет
     */
    public static void assertEscalationScheme(String code, String title, String description, String objectTitle,
            String counterTitle, boolean on)
    {
        //Название схемы эскалации
        assertEscalationTitle(code, title);
        //Название объекта схемы эскалации
        String actual = tester.getText(TARGET_OBJECT_FROM_ROW_PTRN, code).trim();
        Assert.assertEquals("Ожидаемое название объекта схемы эскалации не совпало с полученным.", objectTitle, actual);
        //Описание схемы эскалации
        actual = tester.getText(DESCRIPTION_FROM_ROW_PTRN, code).trim();
        Assert.assertEquals("Ожидаемое описание схемы эскалации не совпало с полученным.", description, actual);
        //Название счетчика времени схемы эскалации
        actual = tester.getText(TIMER_FROM_ROW_PTRN, code).trim();
        Assert.assertEquals("Ожидаемое название счетчика времени схемы эскалации не совпало с полученным.",
                counterTitle, actual);
        //Состояние схемы эскалации (вкл/выкл)
        boolean actualSwitch = on ? tester.waitAppear(ON_OR_OFF_FROM_ROW_PTRN, code)
                : tester.waitDisappear(ON_OR_OFF_FROM_ROW_PTRN, code);
        Assert.assertTrue("Состояние схемы не соответсвует ожидаемому", actualSwitch);
    }

    /**
     * Проверка полей схемы эскалации в карточке Эскалация на вкадке "Схемы эскалации"
     * (Для вызова данного метода необходимо находится в карточке "Схемы эскалации" 
     * (ИА->Настройка бизнес процессов->Эскалация[вкладка: Схемы эскалации]))
     * @param code код схемы эскалации
     * @param title название схемы эскалации
     * @param description описание схемы эскалации
     * @param objectsTitle список названий типов объектов.
     * @param counterTitle название счетчика времени
     * @param on включена ли данная схема или нет
     */
    public static void assertEscalationSchemeOnCard(String code, String title, String description,
            List<String> objectsTitle, String counterTitle, boolean on)
    {
        String xPathPtrn = "//tr[@__code='%s']//*[@id='gwt-debug-value']";
        //Название схемы эскалации
        String actual = tester.getText(xPathPtrn, "title").trim();
        Assert.assertEquals("Ожидаемое название схемы эскалации не совпало с полученным.", title, actual);
        //Код схемы эскалации
        actual = tester.getText(xPathPtrn, "code").trim();
        Assert.assertEquals("Ожидаемый код схемы эскалации не совпал с полученным.", code, actual);
        //Описание схемы эскалации
        actual = tester.getText(xPathPtrn, "description").trim();
        Assert.assertEquals("Ожидаемое описание схемы эскалации не совпало с полученным.", description, actual);
        //Название класса и типов объектов схемы эскалации
        actual = tester.getText(xPathPtrn, "objects").trim();
        String msg = "Ожидаемое название объекта схемы эскалации не совпало с полученным. Получено: '%s'. Ожидалось: "
                     + "'%s'.";
        for (String objTitle : objectsTitle)
        {

            Assert.assertTrue(String.format(msg, actual, objTitle), actual.contains(objTitle));
        }
        //Название счетчика времени схемы эскалации
        actual = tester.getText(xPathPtrn, "timer").trim();
        Assert.assertEquals("Ожидаемое название счетчика времени схемы эскалации не совпало с полученным.",
                counterTitle, actual);
        //Состояние схемы эскалации (вкл/выкл)
        boolean actualOn = tester.find(xPathPtrn, "enabled").getAttribute("class").contains(GUIXpath.Constant.YES);
        Assert.assertEquals("Ожидаемое состояние схемы эскалации не совпало с полученным.", on, actualOn);
    }

    /**
     * Проверяет название эскалации в списке схем
     * @param code код эскалации
     * @param title ожидаемое название эскалации
     */
    public static void assertEscalationTitle(String code, String title)
    {
        String actual = tester.getText(TITLE_FROM_ROW_PTRN, code).trim();
        Assert.assertEquals("Ожидаемое название схемы эскалации не совпало с полученным.", title, actual);
    }

    /**
     * Проверяет название класса на карточке эскалации (необходимо находится в карточке)
     * @param title ожидаемое название класса
     */
    public static void assertMetaClassTitleOnCard(String title)
    {
        GUITester.assertTextPresent(GUIXpath.Any.ANY_VALUE, title, "class");
    }

    /**
     * Проверить состояние схемы эскалации (вкл/выкл)
     * @param on true - включена, false - выключена
     */
    public static void assertOnOrOffEscalationSheme(boolean on)
    {
        Assert.assertTrue("Ожидаемое состояние схемы эскалации не совпало с полученным.",
                tester.waitAppear("//span[contains(@class, '%s')]", on ? GUIXpath.Constant.YES : GUIXpath.Constant.NO));
    }

    /**
     * Проверить присутствие схемы в списке схем эскалаций\Таблицы соответствия в списке таблиц соответствий
     * @param code код схемы эскалации, присутствие которой проверяется
     */
    public static void assertPresenceEscalationSchemeInList(String code)
    {
        GUITester.assertExists(String.format(TITLE_FROM_ROW_PTRN, code), true,
                "Указанная схема эскалации отсутствует в списке схем");
    }

    /**
     * Проверить присутствие Таблицы соответствия в списке таблиц соответствий\Элемента справочника в справочнике
     * Таблицы соответствий
     * @param code код Таблицы соответствия, присутствие которой проверяется
     */
    public static void assertPresenceTableRulesInList(String code)
    {
        GUITester.assertExists(String.format(GUIXpath.Any.ANY_RULES_SETTINGS_COLOR, code), true,
                "Указанная таблица соответствия отсутствует в списке схем");
    }

    /**
     * Сохранить форму добавления эскалации
     */
    public static void applyAddForm(EscalationScheme scheme)
    {
        GUIForm.applyForm();
        String code = StringUtils.substringAfter(GUITester.getCurrentUrl(), "#escalationScheme:");
        scheme.setCode(code);
        scheme.setExists(true);
    }

    /**
     * Нажать кнопку "Добавить" на вкладке "Схемы экскалации"
     */
    public static void clickAdd()
    {
        tester.click(GUIXpath.Div.ADD);
    }

    /**
     * Нажать кнопку "Добавить элемент" на вкладке "Таблицы соответствий"
     */
    public static void clickAddElement()
    {
        tester.click(GUIXpath.Div.ADD_ELEMENT);
    }

    /**
     * Нажать кнопку "Удалить".
     * (Для вызова метода нужно находиться на карточке)
     */
    public static void clickDelete()
    {
        GUIForm.openDeleteForm();
    }

    /**
     * Нажать кнопку удаления.
     * (Для вызова метода нужно находиться на карточке)
     */
    public static void delete()
    {
        GUIForm.openDeleteForm();
        GUIForm.confirmByYes();
    }

    /**
     * Нажать на пиктограмку "Удалить" в списке схем экскалаций
     * @param scheme модель удаляемой схемы
     */
    public static void clickDeleteIcon(EscalationScheme scheme)
    {
        tester.click(ESCALATION_DELETE_ICON, scheme.getCode());
    }

    /**
     * Нажать кнопку "Редактировать".
     * (Для вызова метода нужно находиться на карточке)
     */
    public static void clickEditButton()
    {
        tester.click(GUIXpath.Div.EDIT);
    }

    /**
     * Нажать на пиктограмку "Редактировать" в списке схем экскалаций
     * @param scheme редактируемая схема
     */
    public static void clickEditIcon(EscalationScheme scheme)
    {
        tester.click(ESCALATION_EDIT_ICON, scheme.getCode());
    }

    /**
     * Нажать кнопку Включить\Выключить
     * (Для вызова метода нужно находиться на карточке)
     */
    public static void clickSwitch()
    {
        tester.click(GUIXpath.Div.SWITCH);
    }

    /**
     * Удалить Таблицу соответствия
     * @param code код таблицы, которую нужно удалить
     * */
    public static void deleteTableRule(String code)
    {
        tester.click(String.format(GUIXpath.Any.ANY_RULES_SETTINGS_COLOR, code));
        tester.click(GUIXpath.Div.DEL);
        GUIForm.confirmDelete();
    }

    /**
     * Переход на карточку "Действия"
     * @param model модель действия по событию
     */
    public static void goToActionCard(EventAction model)
    {
        goToActionCard(model.getUuid());
    }

    /**
     * Переход на карточку "Действия"
     * @param uuid действия по событию
     */
    public static void goToActionCard(String uuid)
    {
        tester.goToPage(StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN
                        + "#eventAction:" + uuid);
    }

    /**
     * Переход в список действий (ИА->Настройка бизнес процессов->Эскалация[вкладка: Действия])
     */
    public static void goToActions()
    {
        tester.goToPage(StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN
                        + "#escalation:!%7B%22tab%22:%22actions%22%7D");
        tester.refresh();
    }

    /**
     * Переход на карточку таблицы соответствий
     * @param rulesSettingsEscalation - Таблица соответствия
     */
    public static void goToRuleSettingCard(CatalogItem rulesSettingsEscalation)
    {
        tester.goToPage(StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN + "#ci:"
                        + rulesSettingsEscalation.getUuid());
        tester.refresh();
    }

    /**
     * Переход на карточку таблицы соответствий
     * @param rulesSettingsEscalation - Таблица соответствия
     */
    public static void goToRuleSettingCardFromList(CatalogItem rulesSettingsEscalation)
    {
        tester.click("//*[contains(@id, '%s.rulesSettings.')]", rulesSettingsEscalation.getCode());
    }

    /**
     * Переход на карточку таблицы соответствий
     */
    public static void goToRuleSettings()
    {
        tester.goToPage(StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN
                        + "#escalation:!%7B%22tab%22:%22valueMaps%22%7D");
        tester.refresh();
    }

    /**
     * Переход в карточку конкретной схемы эскалации (заданной моделью схемы).
     * @param scheme модель схемы эскалации
     */
    public static void goToScheme(EscalationScheme scheme)
    {
        goToScheme(scheme.getCode());
    }

    /**
     * Переход в карточку конкретной схемы эскалации (заданной кодом схемы).
     * @param code код схемы эскалации
     */
    public static void goToScheme(String code)
    {
        tester.goToPage(StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN +
                        "#escalationScheme:" + code);
    }

    /**
     * Переход в карточку конкретной схемы эскалации (заданной моделью схемы), затем обновить страницу.
     * @param scheme модель схемы эскалации
     */
    public static void goToSchemeWithRefresh(EscalationScheme scheme)
    {
        goToSchemeWithRefresh(scheme.getCode());
    }

    /**
     * Переход в карточку конкретной схемы эскалации (заданной кодом схемы), затем обновить страницу.
     * @param code код схемы эскалации
     */
    public static void goToSchemeWithRefresh(String code)
    {
        goToScheme(code);
        tester.refresh();
    }

    /**
     * Переход в карточку "Схемы эскалации" (ИА->Настройка бизнес процессов->Эскалация[вкладка: Схемы эскалации]).
     */
    public static void goToSchemes()
    {
        tester.goToPage(StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN
                        + "#escalation:!%7B%22tab%22:%22schemes%22%7D");
    }

    /**
     * Переход в карточку "Схемы эскалации" (ИА->Настройка бизнес процессов->Эскалация[вкладка: Схемы эскалации]),
     * затем обновить страницу.
     */
    public static void goToSchemesWithRefresh()
    {
        goToSchemes();
        tester.refresh();
    }

    /**
     * Заполнить поле "Код" 
     * (нужно находиться на форме Добавления\Редактирования схемы эскалации)
     * @param code название схемы
     */
    public static void setCode(String code)
    {
        tester.sendKeys(GUIXpath.Any.CODE_VALUE, code);
    }

    /**
     * Заполнить поле "Описание" 
     * (нужно находиться на форме Добавления\Редактирования схемы эскалации)
     * @param description описание схемы
     */
    public static void setDescription(String description)
    {
        tester.sendKeys(GUIXpath.Other.DESCRIPTION, description);
    }

    /**
     * Заполнить поле "Объекты" 
     * (нужно находиться на форме Добавления\Редактирования схемы эскалации)
     * @param nodes набор id родителей до нужного узла
     */
    public static void setObjects(String... nodes)
    {
        MetaTree tree = new MetaTree(GUIXpath.Id.TARGET_OBJECTS_VALUE);
        tree.setElementInMultiSelectTree(nodes);
    }

    /**
     * Заполнить поле "Объекты"
     * (нужно находиться на форме Добавления\Редактирования Таблицы соответствия)
     * @param nodes набор id родителей до нужного узла
     */
    public static void setObjectsTable(String... nodes)
    {
        MetaTree tree = new MetaTree(GUIXpath.Id.TARGET_METACLASS_VALUE);
        tree.setElementInMultiSelectTree(nodes);
    }

    /**
     * Выбрать определяющий атрибут
     * (нужно находиться на форме Добавления\Редактирования таблицы соответствия)
     * @param element атрибут (напр. author)
     */
    public static void setSourceAttributes(String element)
    {
        tester.click(GUIXpath.SpecificComplex.SOURCE_LEFT_ELEMENT_VALUE, element);
        tester.click(GUICatalogItem.X_SOURCE_TO_RIGHT);
    }

    /**
     * Выбрать определяемый атрибут
     * (нужно находиться на форме Добавления\Редактирования таблицы соответствия)
     * @param element атрибут (напр. author)
     */
    public static void setTargetAttributes(String element)
    {
        tester.click(GUIXpath.SpecificComplex.TARGET_LEFT_ELEMENT_VALUE, element);
        tester.click(GUICatalogItem.X_TARGET_TO_RIGHT);
    }

    /**
     * Заполнить поле "Счетчик времени" 
     * (нужно находиться на форме Добавления\Редактирования схемы эскалации)
     * @param value id элемента в выпадающем списке
     */
    public static void setTimer(String value)
    {
        GUISelect.select(GUIXpath.InputComplex.TIMER_VALUE, value);
    }

    /**
     * Заполнить поле "Название" 
     * (нужно находиться на форме Добавления\Редактирования схемы эскалации)
     * @param title название схемы
     */
    public static void setTitle(String title)
    {
        tester.sendKeys(GUIXpath.Any.TITLE_VALUE, title);
    }

    /**
     * Проверить присутствие/отсутствие блока "Атрибуты схемы эскалации" на карточке схемы эскалации.
     */
    public static void assertPresentEscalationSchemeCardAttrsBlock(boolean isPresent)
    {
        GUITester.assertExists(ESCALATION_SCHEME_CARD_ATTRS_BLOCK, isPresent, "Блок 'Атрибуты схемы эскалации' %s"
                .formatted(isPresent ? "отсутствует" : "присутствует"));
    }

    /**
     * Проверить присутствие/отсутствие блока "Уровни эскалации" на карточке схемы эскалации.
     */
    public static void assertPresentEscalationSchemeCardLevelsBlock(boolean isPresent)
    {
        GUITester.assertExists(ESCALATION_SCHEME_CARD_LEVELS_BLOCK, isPresent, "Блок 'Уровни эскалации' %s"
                .formatted(isPresent ? "отсутствует" : "присутствует"));
    }
}
