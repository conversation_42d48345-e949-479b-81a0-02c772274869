package ru.naumen.selenium.casesutil.eventcleaner;

import ru.naumen.selenium.casesutil.model.eventcleaner.EventStorageRule;
import ru.naumen.selenium.modules.IModuleEventCleanerJob;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.RandomUtils;

/**
 * Методы для работы с правилами хранения логов событий
 * <AUTHOR>
 * @since 27.07.2023
 */
public class DSLEventStorageRule
{
    /**
     * Получить модуль для работы с задачей очистки событий.
     */
    public static IModuleEventCleanerJob getEventCleanerJobModule()
    {
        return ScriptModules.getModuleEventCleanerJob();
    }

    /**
     * Создает на сервере новые правила хранения.
     * @param rules модели правил
     */
    public static void add(EventStorageRule... rules)
    {
        String cleaningUuid = RandomUtils.randomUUID();
        for (EventStorageRule rule : rules)
        {
            getEventCleanerJobModule().addRule(rule.getFields());
            rule.setExists(true);
            rule.setCleaningUuid(cleaningUuid);
        }
    }

    /**
     * Редактирует правило хранения.
     * @param rule модель правила
     */
    public static void edit(EventStorageRule rule)
    {
        getEventCleanerJobModule().editRule(rule.getFields());
    }

    /**
     * Удаляет правила очистки с сервера.
     * @param rules модели правил
     */
    public static void delete(EventStorageRule... rules)
    {
        for (EventStorageRule rule : rules)
        {
            getEventCleanerJobModule().deleteRule(rule.getCode());
            rule.setExists(false);
        }
    }
}