package ru.naumen.selenium.casesutil;

import ru.naumen.selenium.casesutil.GUIXpath.Input;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;

/**
 * Утилитарные методы для работы с формой добавления\редактирования Сотрудника через интерфейс
 *
 * <AUTHOR>
 * @since 01.06.2018
 */
public class GUIEmployee extends CoreTester
{
    /** Поле ввода Фамилия*/
    public static final String X_EMPL_LASTNAME = "//*[@id='gwt-debug-lastName-value']";
    /** Поле ввода Имя*/
    public static final String X_EMPL_NAME = "//*[@id='gwt-debug-firstName-value']";
    /** Поле ввода Отчество*/
    public static final String X_EMPL_MIDNAME = "//*[@id='gwt-debug-middleName-value']";
    /** Поле ввода Должность*/
    public static final String X_EMPL_POSITION = "//*[@id='gwt-debug-post-value']";

    /** Поле ввода Группы пользователей сотрудника */
    private static final String X_EMPL_SECGROUP = String.format(GUIXpath.InputComplex.ANY_VALUE, "employeeSecGroups");

    /**
     * Заполнить поле Фамилия на форме
     * @param surname - фамилия
     * */
    public static void fillSurname(String surname)
    {
        tester.sendKeys(X_EMPL_LASTNAME, surname);
    }

    /**
     * Заполнить поле Имя на форме
     * @param name - имя
     * */
    public static void fillName(String name)
    {
        tester.sendKeys(X_EMPL_NAME, name);
    }

    /**
     * Заполнить поле Отчество на форме
     * @param middleName - отчество
     * */
    public static void fillMiddleName(String middleName)
    {
        tester.sendKeys(X_EMPL_MIDNAME, middleName);
    }

    /**
     * Заполнить поле Должность на форме
     * @param position - должность
     * */
    public static void fillPosition(String position)
    {
        tester.sendKeys(X_EMPL_POSITION, position);
    }

    /**
     * Заполнить поле Логин на форме
     * @param login - логин
     */
    public static void fillLogin(String login)
    {
        tester.sendKeys(Input.LOGIN_VALUE, login);
    }

    /**
     * Заполнить поле Группы пользователей сотрудника на форме
     * @param codes список кодов групп пользователей сотрудника
     */
    public static void selectSecGroups(String... codes)
    {
        GUIMultiSelect.select(X_EMPL_SECGROUP, codes);
    }
}
