package ru.naumen.selenium.casesutil;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.model.attr.Attribute;

/**
 * Методы для работы с валидацией тестовой системы
 * <AUTHOR>
 * @since 25.04.2016
 */
public class GUIValidation extends CoreTester
{
    /**Путь к блоку валидации*/
    public static final String XPATH_VALIDATION = String.format(GUIXpath.Div.ID_PATTERN, "gwt-debug-validation");

    /** Путь к тексту валидации пароля */
    public static final String XPATH_PASSWORD_VALIDATION = String.format(GUIXpath.Div.ANY, "password-validation");

    /** Путь к тексту валидации кода */
    public static final String XPATH_CODE_VALIDATION = String.format(GUIXpath.Div.ANY, "code-validation");

    /**
     * Проверить, что появился попап с сообщением "Поле должно быть заполнено."
     * @param attrCode код проверяемого атрибута
     */
    public static void assertNotEmptyValidation(String attrCode)
    {
        assertValidation(attrCode, ConfirmMessages.VALIDATION_REQUIRED_FIELD);
    }

    /**
     * Проверить, что появился попап с сообщением "Поле должно быть заполнено."
     * @param attr проверяемый атрибут
     */
    public static void assertNotEmptyValidation(Attribute attr)
    {
        assertNotEmptyValidation(attr.getCode());
    }

    /**
     * Проверить, что появилось указанное сообщение валидации
     * @param attrCode код проверяемого атрибута
     * @param messageValidation ожидаемый текст сообщения валидации
     */
    public static void assertValidation(String attrCode, String messageValidation)
    {
        String actual = tester.getText(String.format(GUIXpath.Div.ID_PATTERN,
                "gwt-debug-" + (attrCode.isEmpty() ? "" : attrCode + "-") + "validation"));
        String msg = String.format(
                "Значение '%s' не совпало с ожидаемым. Ожидалось:" + messageValidation, actual);
        Assert.assertTrue(msg, messageValidation.contains(actual));
    }

    /**
     * Проверить, что сообщение валидации исчезло
     * @param attrCode код проверяемого атрибута
     */
    public static void assertValidationAbsence(String attrCode)
    {
        Assert.assertTrue(tester.waitDisappear(String.format(GUIXpath.Div.ID_PATTERN,
                "gwt-debug-" + (attrCode.isEmpty() ? "" : attrCode + "-") + "validation")));
    }

    /**
     * Проверить, что у сообщения валидации присутствует указанный css-параметр c нужным значением.
     * @param xpath путь до сообщения валидации.
     * @param cssName параметр, который необходимо проверить.
     * @param expectedValue значение, которое должно быть.
     */
    public static void assertCssValueExist(String xpath, String cssName, String expectedValue)
    {
        String actualValue = tester.find(xpath).getCssValue(cssName);
        String msg = String.format("Значение '%s' не совпало с ожидаемым. Ожидалось: %s", actualValue, expectedValue);
        Assert.assertTrue(msg, actualValue.contains(expectedValue));
    }
}