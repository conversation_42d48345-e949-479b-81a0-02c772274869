package ru.naumen.selenium.casesutil.interfaceelement;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.Assert;
import org.openqa.selenium.By;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.StaleElementReferenceException;
import org.openqa.selenium.WebElement;

import com.google.common.base.Preconditions;

import java.util.ArrayList;

import com.google.common.collect.Sets;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIIcon;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.Other;
import ru.naumen.selenium.casesutil.GUIXpath.Span;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.util.Colors;

/**
 * Методы для работы с выпадающими списками множественного выбора
 * <AUTHOR>
 */
public class GUIMultiSelect extends CoreTester
{
    private static final String X_ITEM_CHECK = GUIXpath.Any.POPUP_LIST_SELECT + "//*[@id='%s']";

    public static final String X_ITEM_CHECK_IMG = X_ITEM_CHECK + Other.IMG_PREFIX;

    private static final String X_ALL_ITEMS = GUIXpath.Any.POPUP_LIST_SELECT + "//*[@id]";

    private static final String X_IMG_ITEM = "img[contains(@src, '%s')]";

    private static final String X_RELATED_TITLE_ITEM = "following-sibling::*[1]";

    private static final String SELECT_TITLES_MISMATCH_TEMPLATE =
            "В мультиселекте(%s) ожидаемые(%s) и выбранные (%s) элементы не совпадают";

    /**
     * Код опции [Любой] в выпадающих спсиках и деревьях
     */
    public static final String ANY_ID = "anyOptionItem";

    public static final String ANY_VALUE = "[Любой]";

    /**
     * Проверить количество элементов в мультиселекте
     * @param select xpath элемента, являющегося мультиселектом
     * @param expected ожидаемое количество элементов в мультиселекте
     */
    public static void assertCheckboxCount(String select, int expected)
    {
        expand(select);
        Assert.assertEquals("Полученное кол-во элементов в мультиселекте не совпало с ожидаемым.", expected,
                getAllCheckBoxElements(GUIIcon.CHECKBOX_CHECKED).size()
                + getAllCheckBoxElements(GUIIcon.CHECKBOX_UNCHECKED).size());
        GUISelect.hideSelect(select);
    }

    /**
     * Проверить, что элементы с указанными id присутствуют в поле множественного выбора
     * @param select xpath элемента, являющегося мультиселектом
     * @param valueIds id элементов в выпадающем списке.
     */
    public static void assertContainsElements(String select, String... valueIds)
    {
        assertContains(select, true, valueIds);
    }

    /**
     * Проверить отсутствие элемента в выпадающем списке
     * @param select xpath элемента, являющегося мультиселектом
     * @param valueId id элемента, который должен отсутствовать
     */
    public static void assertElementAbsent(String select, String valueId)
    {
        Set<String> allIdSet = getElementsInExpandedMultiSelect(select);
        Assert.assertFalse(String.format("Элемент %s присутствует в выпадающем списке", valueId),
                allIdSet.contains(valueId));
    }

    /**
     * Проверить присутствие элемента в выпадающем списке
     * @param select xpath элемента, являющегося мультиселектом
     * @param valueId id элемента, который должен присутствовать
     */
    public static void assertElementPresent(String select, String valueId)
    {
        Set<String> allIdSet = getElementsInExpandedMultiSelect(select);
        Assert.assertTrue(String.format("Элемент %s отсутствует в выпадающем списке", valueId),
                allIdSet.contains(valueId));
    }

    /**
     * Проверить, что элемент доступен для выбора
     * Присутствует чекбокс
     * @param uuid элемента
     */
    public static void assertEnable(String uuid)
    {
        Assert.assertTrue("Элемент недоступен для выбора",
                tester.waitAppear(String.format(GUIXpath.Complex.POPUP_CHECKBOX, uuid)));
    }

    /**
     * Проверить, что у элемента нет чекбокса (элемент - папка)
     * @param uuid папки
     */
    public static void assertFolder(String uuid)
    {
        Assert.assertTrue("Папка доступна для выбора",
                tester.waitDisappear(String.format(GUIXpath.Complex.POPUP_CHECKBOX, uuid)));
    }

    /**
     * Проверить, что элементы с указанными id отсутствуют в поле множественного выбора
     * @param select xpath элемента, являющегося мультиселектом
     * @param valueIds id элементов в выпадающем списке, которые должны отсутствовать.
     */
    public static void assertNotContainsElements(String select, String... valueIds)
    {
        assertContains(select, false, valueIds);
    }

    /**
     * Проверить, что не выбраны указанные элементы
     * @param select xpath элемента, являющегося мультиселектом
     * @param valueIds id элементов в выпадающем списке, которые не должны быть выбраны
     */
    public static void assertNotSelected(String select, String... valueIds)
    {
        expand(select);
        String msg = "В мультиселекте(" + select + ") выбран элемент: ID = ";
        for (String id : valueIds)
        {
            List<WebElement> selectedElements = getCheckBoxElements(GUIIcon.CHECKBOX_UNCHECKED, id);
            Assert.assertFalse(msg + id, selectedElements.isEmpty());
        }
        GUISelect.hideSelect(select);
    }

    /**
     * Проверить, что выбраны указанные элементы
     * @param select xpath элемента, являющегося мультиселектом
     * @param valueIds id элементов в выпадающем списке, которые должны быть выбраны
     */
    public static void assertSelected(String select, String... valueIds)
    {
        expand(select);
        String msg = "В мультиселекте(" + select + ") не выбран элемент: ID = ";

        for (String id : valueIds)
        {
            List<WebElement> selectedElements = getCheckBoxElements(GUIIcon.CHECKBOX_CHECKED, id);
            Assert.assertFalse(msg + id, selectedElements.isEmpty());
        }
        GUISelect.hideSelect(select);
    }

    /**
     * Проверить, что выбраны элементы с указанными названиями.
     * Необходимо полное соответствие, порядок указанных названий не важен.
     * @param select xpath элемента, являющегося мультиселектом
     * @param titles имена элементов в выпадающем списке, которые должны быть выбраны
     */
    public static void assertSelectedTitles(String select, String... titles)
    {
        expand(select);
        List<String> actualTitles = getAllCheckBoxElements(GUIIcon.CHECKBOX_CHECKED)
                .stream()
                .map(el -> el.findElement(By.xpath(X_RELATED_TITLE_ITEM)).getText())
                // Порядок не важен, приводим к единому порядку
                .sorted()
                .toList();

        // Порядок не важен, приводим к единому порядку
        Arrays.sort(titles);
        List<String> expectedTitles = List.of(titles);
        String errorMessage = SELECT_TITLES_MISMATCH_TEMPLATE.formatted(select, expectedTitles, actualTitles);
        Assert.assertEquals(errorMessage, expectedTitles, actualTitles);

        GUISelect.hideSelect(select);
    }

    /**
     * Проверить количество выбранных элементов в мультиселекте
     * @param select xpath элемента, являющегося мультиселектом
     * @param expected ожидаемое количество выбранных элементов в мультиселекте
     */
    public static void assertSelectedSize(String select, int expected)
    {
        int size = 0;
        if (!GUISelect.NO_ELEMENTS.equals(GUITester.getValue(select)))
        {
            expand(select);
            size = getAllCheckBoxElements(GUIIcon.CHECKBOX_CHECKED).size();
            GUISelect.hideSelect(select);
        }
        Assert.assertEquals("Полученное кол-во выбранных элементов в мультиселекте не совпало с ожидаемым.", expected,
                size);
    }

    /**
     * Проверить наличие объекта в списке множественного выбора на форме добавления объекта 
     * @param bo объект, который должен быть выбран
     */
    public static void assertValueByTitleContains(Bo bo)
    {
        GUITester.assertPresent(GUIXpath.Div.ANY + GUIXpath.Any.ANY_TEXT_CONTAINS,
                "Элемент отсутствует в выбранных "
                + "значениях!",
                bo.getUuid(), bo.getTitle());
    }

    /**
     * Проверяет, что отсутствуют "плашки" с указанными значениями под селектом для выбора значения атрибута
     * @param attr атрибут
     * @param values значения плашек, которые должны отсутствовать
     */
    public static void assertValuesNotPresentOnForm(Attribute attr, String... values)
    {
        GUITester.assertValuesNotPresentOnTagForm("gwt-debug-" + attr.getCode() + "-value", values);
    }

    /**
     * Проверяет, что отсутствуют "плашки" с указанными значениями под селектом для выбора значения атрибута
     * @param attrCode код атрибута
     * @param values значения плашек, которые должны отсутствовать
     */
    public static void assertValuesNotPresentOnForm(String attrCode, String... values)
    {
        GUITester.assertValuesNotPresentOnTagForm("gwt-debug-" + attrCode + "-value", values);
    }

    /**
     * Проверяет, что присутствуют "плашки" с указанными значениями под селектом для выбора значения атрибута
     * @param attr атрибут
     * @param values значения плашек, которые должны отсутствовать
     */
    public static void assertValuesPresentOnForm(Attribute attr, String... values)
    {
        GUITester.assertValuesPresentOnTagForm("gwt-debug-" + attr.getCode() + "-value", values);
    }

    /**
     * Проверяет, что присутствуют "плашки" с указанными значениями под селектом для выбора значения атрибута
     * @param code код атрибута
     * @param values значения плашек, которые должны отсутствовать
     */
    public static void assertValuesPresentOnForm(String code, String... values)
    {
        GUITester.assertValuesPresentOnTagForm("gwt-debug-" + code + "-value", values);
    }

    /**
     * Кликает на иконку быстрого редактирования в поле быстрого выбора. Ждет открытия диалогового окна редактирования
     */
    public static void clickQuickEditObject(Bo bo)
    {
        tester.click(GUIXpath.Div.ANY + GUIXpath.Span.EDIT_ICON, bo.getUuid());
        GUIForm.assertDialogAppear("Диалоговое окно быстрого редактирования не открылось!");
    }

    /**
     * Раскрыть селект
     * @param select xpath
     * @param args параметры для форматирования
     */
    public static void expand(String select, Object... args)
    {
        //Клик в верхний левый угол, т.к. при заполнении форм появляются всплывающие подсказки, которые закрывают часть
        //поля, по которому нужно кликнуть (для CHROME)
        tester.clickTopLeftCorner(select, args);
    }

    /**
     * Установить только выбраные значения в выпадающем, остальные значения будут сброшены
     * @param select xpath элемента, являющегося мультиселектом
     * @param valueIds коллекция из id элементов в выпадающем списке. (Если пустая -
     * то вызов данного метода снимет выделение со всех элементов мультиселекта)
     */
    public static void select(String select, Collection<String> valueIds)
    {
        select(select, Sets.newHashSet(valueIds));
    }

    /**
     * Установить только выбраные значения в выпадающем, остальные значения будут сброшены
     * @param select xpath элемента, являющегося мультиселектом
     * @param valueIds id элементов в выпадающем списке. (Если отсутствует -
     * то вызов данного метода снимет выделение со всех элементов мультиселекта)
     */
    public static void select(String select, String... valueIds)
    {
        select(select, Sets.newHashSet(valueIds));
    }

    /**
     * Выбрать все элементы в мультиселекте
     * @param select xpath
     */
    public static void selectAll(String select)
    {
        expand(select);
        GUITester.assertPresent(GUIXpath.Any.POPUP_LIST_SELECT, "Мультиселект не отображается");
        getAllSelectIds(select).forEach(GUIMultiSelect::setCheckBox);
        GUISelect.hideSelect(select);
    }

    /**
     * Установить выбраные значения в выпадающем, остальные значения сохранятся.
     * (После выбора выпадающий список сворачивается)
     * @param select xpath элемента, являющегося мультиселектом
     * @param valueIds id элементов в выпадающем списке. (Если отсутствует,
     * то вызов данного метода ничего не делает)
     */
    public static void selectNotClean(String select, String... valueIds)
    {
        selectNotCleanWithoutHide(select, valueIds);
        GUISelect.hideSelect(select);
    }

    /**
     * Установить выбраные значения в выпадающем, остальные значения сохранятся.
     * (После выбора выпадающий список НЕ сворачивается)
     * @param select xpath элемента, являющегося мультиселектом
     * @param valueIds id элементов в выпадающем списке. (Если отсутствует,
     * то вызов данного метода ничего не делает)
     */
    public static void selectNotCleanWithoutHide(String select, String... valueIds)
    {
        expand(select);

        //Ставим необходимые чекбоксы
        for (String id : valueIds)
        {
            setCheckBox(id);
        }
    }

    /**
     * Установить чекбокс
     * @param id идентификатор чекбокса
     */
    public static void setCheckBox(final String id)
    {
        boolean result = setImgCheckBox(id, GUIIcon.CHECKBOX_UNCHECKED);
        Preconditions.checkArgument(result, "Не удалось установить чекбокс.");
    }

    /**
     * Убрать выбраные значения в выпадающем, остальные значения сохранятся.
     * (После выбора выпадающий список сворачивается)
     * @param select xpath элемента, являющегося мультиселектом
     * @param valueIds id элементов в выпадающем списке. (Если отсутствует,
     * то вызов данного метода ничего не делает)
     */
    public static void unselectNotClean(String select, String... valueIds)
    {
        expand(select);
        //Снимаем необходимые чекбоксы
        for (String id : valueIds)
        {
            unsetCheckBox(id);
        }
        GUISelect.hideSelect(select);
    }

    /**
     * Проверить, что указанная плашка отображается как архивная.
     * @param item {@link Bo} Плашка, которую необходимо проверить
     * @param asArchived {@link boolean} true - текст плашки должен быть серым, false - не должен быть серым
     */
    public static void assertSelectedIsArch(Bo item, boolean asArchived)
    {
        String archTitle = GUIMetaClass.ARCH_PREFIX.concat(item.getTitle());
        String xPath = String.format(Div.FORMTAGS + Div.ANY + Span.SPAN_TEXT_PATTERN, item.getUuid(), archTitle);
        GUITester.assertPresent(Div.FORMTAGS + Div.ANY + Span.SPAN_TEXT_PATTERN,
                "Элемент '" + archTitle + "' не найден!",
                item.getUuid(), archTitle);
        if (asArchived)
        {
            GUITester.assertColor(xPath, Colors.GREY);
        }
    }

    /**
     * Проверить присутствуют/отсутствуют элементы с указанными id в поле множественного выбора.
     * @param select xpath элемента, являющегося мультиселектом
     * @param isContains true - проверяем, что элементы содержатся. false - не содержаться.
     * @param valueIds id элементов в выпадающем списке.
     */
    private static void assertContains(String select, boolean isContains, String... valueIds)
    {
        expand(select);
        for (String id : valueIds)
        {
            String msg = "В поле множественого выбора элемент с id " + id
                         + (isContains ? " отсутствует." : " присутствует.");
            GUITester.assertExists(GUIXpath.Any.POPUP_LIST_SELECT + "//*[@id='" + id + "']", isContains, msg);
        }
        GUISelect.hideSelect(select);
    }

    /**
     * Получить список всех чекбоксов в мультиселекте: либо выбранных, либо нет
     * @param img указывает выбран или нет чекбокс: {@link #IMG_UNCHECK}, {@link #IMG_CHECK}
     */
    private static List<WebElement> getAllCheckBoxElements(String img)
    {
        List<WebElement> result = new ArrayList<>();
        for (WebElement element : tester.findDisplayedElements(X_ALL_ITEMS))
        {
            List<WebElement> checkElements = element.findElements(By.xpath(String.format(X_IMG_ITEM, img)));
            if (!checkElements.isEmpty())
            {
                result.add(checkElements.get(0));
            }
        }
        return result;
    }

    /**
     * Получить множество идентификаторов всех элементов мультиселекта
     * @param select xpath
     * @return множество идентификаторов
     */
    private static List<String> getAllSelectIds(String select)
    {
        List<String> allSelectIds = new ArrayList<>();
        if (!GUISelect.NO_ELEMENTS.equals(GUITester.getValue(select)) && tester.isPresence(
                GUIXpath.SpecificComplex.POPUP_SELECT_ELEMENT))
        {
            for (WebElement element : tester.findDisplayedElements(GUIXpath.SpecificComplex.POPUP_SELECT_ELEMENT))
            {
                allSelectIds.add(element.getAttribute("id"));
            }
        }
        return allSelectIds;
    }

    /**
     * Получить список чекбоксов в мультиселекте: либо выбранных, либо нет
     * @param img указывает выбран или нет чекбокс: {@link #IMG_UNCHECK}, {@link #IMG_CHECK}
     * @param valueIds id элементов в выпадающем списке. (Если отсутствует, то вернется пустой список)
     */
    private static List<WebElement> getCheckBoxElements(String img, String... valueIds)
    {
        List<WebElement> result = new ArrayList<>();
        for (String id : valueIds)
        {
            WebElement element = tester.find(X_ITEM_CHECK, id);
            List<WebElement> checkElements = element.findElements(By.xpath(String.format(X_IMG_ITEM, img)));
            if (!checkElements.isEmpty())
            {
                result.add(checkElements.get(0));
            }
        }
        return result;
    }

    private static Set<String> getElementsInExpandedMultiSelect(String select)
    {
        expand(select);

        Set<String> allIdSet = new HashSet<>();
        for (WebElement element : tester.findDisplayedElements(GUIXpath.SpecificComplex.POPUP_SELECT_ELEMENT))
        {
            allIdSet.add(element.getAttribute("id"));
        }
        return allIdSet;
    }

    /**
     * Установить только выбраные значения в выпадающем, остальные значения будут сброшены
     * @param select xpath элемента, являющегося мультиселектом
     * @param idSet сет из id элементов в выпадающем списке. (Если пустой -
     * то вызов данного метода снимет выделение со всех элементов мультиселекта)
     */
    private static void select(String select, Set<String> idSet)
    {
        expand(select);
        GUITester.assertPresent(GUIXpath.Any.POPUP_LIST_SELECT, "Выпадающий список не отображается");
        Set<String> allIdSet = Sets.newHashSet(getAllSelectIds(select));

        //Ставим необходимые чекбоксы
        idSet.forEach(GUIMultiSelect::setCheckBox);

        //Снимаем остальные чекбоксы
        allIdSet.removeAll(idSet);
        allIdSet.forEach(GUIMultiSelect::unsetCheckBox);

        GUISelect.hideSelect(select);
    }

    /**
     * Установить/снять чекбокс в зависимости от переданной картинки
     * @param id идентификатор чекбокса
     * @param img картинка, определяющая установлен или снят чекбокс
     * {@link #IMG_CHECK} - чекбокс нужно снять
     * {@link #IMG_UNCHECK} - чекбокс нужно установить
     */
    private static boolean setImgCheckBox(final String id, final String img)
    {
        return WaitTool.wait(tester.getWebDriver(), WaitTool.WAIT_TIME, driver ->
        {
            if (driver == null)
            {
                throw new ErrorInCodeException("Веб-драйвер не может быть null.");
            }
            try
            {
                for (WebElement element : driver.findElements(By.xpath(String.format(X_ITEM_CHECK, id))))
                {
                    if (element.isDisplayed() && element.isEnabled())
                    {
                        List<WebElement> uncheckedElements = element
                                .findElements(By.xpath(String.format(X_IMG_ITEM, img)));
                        if (!uncheckedElements.isEmpty())
                        {
                            uncheckedElements.get(0).click();
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (NoSuchElementException | StaleElementReferenceException e)
            {
                return false;
            }
        });
    }

    /**
     * Снять чекбокс
     * @param id идентификатор чекбокса
     */
    private static void unsetCheckBox(final String id)
    {
        boolean result = setImgCheckBox(id, GUIIcon.CHECKBOX_CHECKED);
        Preconditions.checkArgument(result, "Не удалось снять чекбокс.");
    }

    /**
     * Проверяет, что присутствует "плашка" с указанным значением и бейджем '(вер.)'
     */
    public static void assertBadgeVersionValuePresence(Attribute attribute, Bo value)
    {
        Assert.assertTrue("На форме отсутствует серая плашка с бейджем '(вер.)'",
                tester.waitAppear(Div.ANY_DIV_VALUE + Div.ANY + Span.INLINE_BADGE_CONTAINS_TEXT, attribute.getCode(),
                        value.getUuid(), GUIBo.VERSION_INLINE_BADGE));
    }

    /**
     * Проверяет, что присутствует "плашка" с указанным значением и бейджем '(окр.)'
     */
    public static void assertBadgeEnvironmentValuePresence(Attribute attribute, Bo value)
    {
        Assert.assertTrue("На форме отсутствует серая плашка с бейджем '(окр.)'",
                tester.waitAppear(Div.ANY_DIV_VALUE + Div.ANY + Span.INLINE_BADGE_CONTAINS_TEXT, attribute.getCode(),
                        value.getUuid(), GUIBo.ENVIRONMENT_INLINE_BADGE));
    }
}