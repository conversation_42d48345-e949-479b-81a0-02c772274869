package ru.naumen.selenium.casesutil.metaclass.accessmatrix;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;

/**
 * Методы для работы с формой профиля через интерфейс
 * <AUTHOR>
 * @since 02 марта 2015 г.
 *
 */
public class GUIProfileForm extends CoreTester
{
    public static final String ROLE_CHECKBOX = GUIProfileForm.ROLES_BLOCK + "//input[@id='%s']";
    /**Таблица "Группы пользователей" на форме добавления/редактирования профиля*/
    public static final String SEC_GROUPS = "//div[@id='gwt-debug-groups']";
    public static final String ROLES_BLOCK = "//div[@id='gwt-debug-roles-value']";
    public static final String ROLES = "//div[@id='gwt-debug-roles']";
    public static final String PROFILE_ADD_TITLE = "//input[@id='gwt-debug-title-value']";
    public static final String PROFILE_ADD_CODE = "//input[@id='gwt-debug-code-value']";
    public static final String PROFILE_EDIT_CODE = "//*[@id='gwt-debug-code-caption']//span";
    public static final String FOR_UNLICENSED = "Для нелицензированных пользователей:";
    public static final String UNLICENSED_ADD_FORM_LABEL = "//*[@id='gwt-debug-forUnlicensed-value-label']";
    public static final String UNLICENSED_SPAN = "//*[@id='gwt-debug-forUnlicensed-caption']/span";
    public static final String UNLICENSED_EDIT_FORM_LABEL = "//*[@id='gwt-debug-forUnlicensed-caption']/label";
    public static final String PROFILES_FOR_COPY = "//*[@id='gwt-debug-profiles-value']//input";

    /**
     * Проверить поле "Код" на форме редактирования профиля
     * @param expected
     */
    public static void assertEditFormCode(String expected)
    {
        GUITester.assertTextPresent(GUIXpath.Div.PROPERTY_DIALOG_BOX + PROFILE_EDIT_CODE, expected);
    }

    /**
     * Проверить, что указанные группы пользователей выбраны
     * @param userGroups группы пользователей
     */
    public static void assertGroupSelected(SecurityGroup... userGroups)
    {
        for (SecurityGroup userGroup : userGroups)
        {
            GUITester.assertCheckboxState(GUIProfileForm.SEC_GROUPS + GUIXpath.Input.ID_PATTERN, true,
                    userGroup.getCode());
        }
    }

    /**
     * Проверить, что указанные группы пользователей не выбраны
     * @param userGroups группы пользователей
     */
    public static void assertGroupNotSelected(SecurityGroup... userGroups)
    {
        for (SecurityGroup userGroup : userGroups)
        {
            GUITester.assertCheckboxState(GUIProfileForm.SEC_GROUPS + GUIXpath.Input.ID_PATTERN, false,
                    userGroup.getCode());
        }
    }

    /**
     * Проверить поле "Для нелицензированных пользователей" на форме редактирования профиля
     * @param licensed true = для нелицензированных, false = для лицензированных
     */
    public static void assertOnLicensed(boolean licensed)
    {
        GUITester.assertTextPresent(GUIXpath.Div.PROPERTY_DIALOG_BOX + UNLICENSED_EDIT_FORM_LABEL, FOR_UNLICENSED);
        String expected = licensed ? "да" : "нет";
        GUITester.assertTextPresent(GUIXpath.Div.PROPERTY_DIALOG_BOX + UNLICENSED_SPAN, expected);
    }

    /**
     * Проверить присутствие чекбокса "Для нелицензированных пользователей"
     * @param present присутствует ли
     */
    public static void assertOnLicensedPresence(boolean present)
    {
        GUITester.assertExists(GUIXpath.Div.PROPERTY_DIALOG_BOX + UNLICENSED_ADD_FORM_LABEL, present,
                String.format("На форме "
                              + "%s чекбокс 'Для нелицензированных пользователей'",
                        present ? "отсутствует" : "присутствует"));
    }

    /**
     * Проверить, что указанные роли отсутствуют в профиле
     * (Для вызова метода необходимо находится на форме редактирования профиля)
     * @param roles набор моделей ролей
     */
    public static void assertRoleAbsence(SecurityRole... roles)
    {
        String msg = "В профиле присутствует роль: '%s'.";
        for (SecurityRole role : roles)
        {
            Assert.assertTrue(String.format(msg, role.getTitle()),
                    tester.waitDisappear(GUIProfileForm.ROLE_CHECKBOX + "//..", role.getCode()));
        }
    }

    /**
     * Проверить, что указанные роли не выбраны в профиле
     * (Для вызова метода необходимо находится на форме редактирования профиля)
     * @param roles набор моделей ролей
     */
    public static void assertRoleNotSelected(SecurityRole... roles)
    {
        String msg = "В профиле указана роль: '%s'.";
        for (SecurityRole role : roles)
        {
            Assert.assertFalse(String.format(msg, role.getTitle()),
                    tester.findDisplayed(GUIProfileForm.ROLE_CHECKBOX, role.getCode()).isSelected());
        }
    }

    /**
     * Проверить, что указанные роли присутствуют в профиле
     * (Для вызова метода необходимо находится на форме редактирования профиля)
     * @param roles набор моделей ролей
     */
    public static void assertRolePresent(SecurityRole... roles)
    {
        String msg = "В профиле отсутствует роль: '%s'.";
        for (SecurityRole role : roles)
        {
            Assert.assertTrue(String.format(msg, role.getTitle()),
                    tester.waitAppear(GUIProfileForm.ROLE_CHECKBOX + "//..", role.getCode()));
        }
    }

    /**
     * Проверить, что указанные роли выбраны в профиле
     * (Для вызова метода необходимо находится на форме редактирования профиля)
     * @param roles набор моделей ролей
     */
    public static void assertRoleSelected(SecurityRole... roles)
    {
        String msg = "В профиле не указана роль: '%s'.";
        for (SecurityRole role : roles)
        {
            Assert.assertTrue(String.format(msg, role.getTitle()), tester.waitAppear(
                    String.format("(%s//..)[input[@checked]]", GUIProfileForm.ROLE_CHECKBOX), role.getCode()));
        }
    }

    /**
     * Проверить, что указанные роли установлены в профиле
     * (Для вызова метода необходимо находится на форме редактирования профиля)
     * @param roles набор моделей ролей
     */
    public static void assertSetRole(SecurityRole... roles)
    {
        for (SecurityRole role : roles)
        {
            GUITester.assertCheckboxState(GUIProfileForm.ROLE_CHECKBOX, true, role.getCode());
        }
    }

    /**
     * Проверить поле "Название" на форме редактирования-добавления профиля
     * @param expected
     */
    public static void assertTitle(String expected)
    {
        GUITester.assertValue(GUIXpath.Div.PROPERTY_DIALOG_BOX + PROFILE_ADD_TITLE, expected);
    }

    /**
     * Проверить название группы пользователей на форме добавления/редактирования профиля
     * (Должна быть открыта форма добавления/редактирования профиля)
     * @param secGroup группа пользователей
     */
    public static void assertTitleSecGroup(SecurityGroup secGroup)
    {
        GUITester.assertTextPresent(
                GUIProfileForm.SEC_GROUPS + GUIXpath.Input.ID_PATTERN + "//following-sibling::label",
                secGroup.getTitle(), secGroup.getCode());
    }

    /**
     * Проверить, что указанные роли не установлены в профиле
     * (Для вызова метода необходимо находится на форме редактирования профиля)
     * @param roles набор моделей ролей
     */
    public static void assertUnsetRole(SecurityRole... roles)
    {
        for (SecurityRole role : roles)
        {
            GUITester.assertCheckboxState(GUIProfileForm.ROLE_CHECKBOX, false, role.getCode());
        }
    }

    /**
     * Кликнуть на чекбокс "Для нелицензированных пользователей"
     */
    public static void clickOnUnlicensedLabel()
    {
        tester.click(GUIProfileForm.UNLICENSED_ADD_FORM_LABEL);
    }

    /**
     * Установить код профиля в форме
     * @param code код профиля
     */
    public static void setCode(String code)
    {
        tester.sendKeys(GUIProfileForm.PROFILE_ADD_CODE, code);
    }

    /**
     * Сбросить или установить флажки с ролей в таблице "Роли пользователей"
     * (Должна быть открыта форма редактирования профиля)
     * @param enable true - выставить флажок, false - сбросить
     * @param roles сбрасываемые роли
     */
    public static void setRole(boolean enable, SecurityRole... roles)
    {
        for (SecurityRole role : roles)
        {
            tester.setCheckbox(ROLE_CHECKBOX, enable, role.getCode());
        }
    }

    /**
     * Установить наименование профиля в форме
     * @param title наименование профиля
     */
    public static void setTitle(String title)
    {
        tester.sendKeys(GUIProfileForm.PROFILE_ADD_TITLE, title);
    }

}
