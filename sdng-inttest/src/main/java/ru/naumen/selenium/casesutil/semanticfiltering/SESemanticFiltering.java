package ru.naumen.selenium.casesutil.semanticfiltering;

import ru.naumen.selenium.casesutil.scripts.ScriptElement;

/**
 * Создание скриптовых элементов для работы с фильтрацией с учетом морфологии
 *
 * <AUTHOR>
 * @since 07 окт. 2019 г.
 */
class SESemanticFiltering
{
    /**
     * Выключает фильтрацию с учетом морфологии
     */
    static ScriptElement setDisabled()
    {
        ScriptElement element = new ScriptElement();
        element.setPathToFunctionFile("scripts/groovy/semanticfiltering/setDisabled.groovy");
        return element;
    }

    /**
     * Включает фильтрацию с учетом морфологии
     */
    static ScriptElement setEnabled()
    {
        ScriptElement element = new ScriptElement();
        element.setPathToFunctionFile("scripts/groovy/semanticfiltering/setEnabled.groovy");
        return element;
    }

    /**
     * Устанавливает конфигурацию словарей морфологической фильтрации - russian,
     * которая входит в поставку postgresql. Необходимо для успешного прохождения тестов
     * без дополнительной настройки postgresql.
     */
    static ScriptElement setTestDictionaryConfigurationName()
    {
        ScriptElement element = new ScriptElement();
        element.setPathToFunctionFile("scripts/groovy/semanticfiltering/setTestDictionaryConfiguration.groovy");
        return element;
    }
}
