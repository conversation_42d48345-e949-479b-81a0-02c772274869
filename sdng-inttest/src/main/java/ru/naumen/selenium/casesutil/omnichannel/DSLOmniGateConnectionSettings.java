package ru.naumen.selenium.casesutil.omnichannel;

import ru.naumen.selenium.casesutil.model.IRemoveOperation;
import ru.naumen.selenium.casesutil.model.RemoveOperation;
import ru.naumen.selenium.casesutil.model.omnichannel.OmniGateConnectionSettings;
import ru.naumen.selenium.casesutil.scripts.element.SEConnectionSettings;
import ru.naumen.selenium.core.Cleaner;

/**
 * DSL методы для работы с {@link OmniGateConnectionSettings}
 * <AUTHOR>
 * @since 19.10.2023
 */
public class DSLOmniGateConnectionSettings
{
    private static final IRemoveOperation REMOVE_OPERATION =
            new RemoveOperation(SEConnectionSettings.editOmniGateConnectionSettings(new OmniGateConnectionSettings()));

    /**
     * Возвращает операцию, которая восстановит значения по умолчанию на карточке "настроек подключения" к шлюзу
     * OmniGate
     * @return операция удаления
     */
    public static IRemoveOperation getDefaultOmniGateConnectionSettings()
    {
        return REMOVE_OPERATION;
    }

    /**
     * Редактировать "настройки подключения" к шлюзу OmniGate
     * @param settings модель "настроек подключения"
     */
    public static void edit(OmniGateConnectionSettings settings)
    {
        SEConnectionSettings.connectionSettingsModule.editOmniGateConnectionSettings(settings.getFields());
        Cleaner.push(getDefaultOmniGateConnectionSettings());
    }
}
