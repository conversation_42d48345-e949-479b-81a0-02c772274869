package ru.naumen.selenium.casesutil.omnichannel;

import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.omnichannel.DAOChannel;
import ru.naumen.selenium.casesutil.omnichannel.chatOnSite.ShowCase;

/**
 * Утилитарные методы работы с каналом модуля омниканальности
 * <AUTHOR>
 * @since 12.10.2021
 */
public class DSLChannel
{
    /**
     * Добавляет в систему канал "Чат на сайте"
     * @param showCase витрина чата на сайте, для которой добавляется канал
     * @return модель канала
     */
    public static Bo addChatOnSite(ShowCase showCase)
    {
        CatalogItem gateType = ChatOnSite.OMNI_GATE.equals(showCase.getChatOnSite())
                ? DSLGateTypesItem.getOmniGateItem()
                : DSLGateTypesItem.getMessageBoxItem();

        Bo scBo = DAOChannel.createSiteChatBuilder()
                .gateType(gateType)
                .login(showCase.getCaseLogin())
                .password(showCase.getCasePassword())
                .build();
        DSLBo.add(scBo);
        return scBo;
    }
}
