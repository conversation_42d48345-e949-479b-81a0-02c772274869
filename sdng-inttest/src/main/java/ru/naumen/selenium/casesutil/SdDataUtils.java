package ru.naumen.selenium.casesutil;

import java.util.List;
import java.util.stream.Collectors;

import com.google.common.base.Function;
import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.model.Model;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.modules.IModuleSdDataUtils;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.Json;

/**
 * Утилитартные методы для извлечения данных с тестируемого стенда
 * Для использования данных методов необходимо быть залогиненым как суперпользователь. 
 * <AUTHOR>
 * @since 06.08.2012
 */
public class SdDataUtils
{
    /**
     * Получить модуль для извлечения данных с тестируемого стенда
     */
    public static IModuleSdDataUtils getSdDataUtilsModule()
    {
        return ScriptModules.getModuleSdDataUtils();
    }

    /**Время ожидания применения атрибута у БО, мс.*/
    public static final long WAIT_ATTR_VALUE = 20000;

    /**
     * Найти объекты в системе sd по идентификатору метакласса.
     *
     * @param fqn идентификатор метакласса
     * @return возвращает список найденых объектов хранящихся в картах, в которых в качестве ключа выступают коды
     * атрибутов
     */
    public static List<ModelMap> findObjects(String fqn)
    {
        ModelMap paramForSearch = ModelMap.newMap();
        return findObjects(fqn, paramForSearch);
    }

    /**
     * Найти объекты в системе sd по идентификатору метакласса и карте кодов атрибутов и их значений в объекте
     *
     * @param branch модель БО ветка
     * @param fqn идентификатор метакласса
     * @param map карта кодов атрибутов и их значений
     * @return возвращает список найденых объектов хранящихся в картах, в которых в качестве ключа выступают коды
     * атрибутов
     */
    public static List<ModelMap> findObjects(Bo branch, String fqn, ModelMap map)
    {
        String branchUuid = branch == null ? null : branch.getUuid();
        String scriptResult = getSdDataUtilsModule().getInfoAboutBo(branchUuid, fqn, Json.GSON.toJson(map));
        List<String> list = Json.GSON.fromJson(scriptResult, Json.LIST_STRING_TYPE);

        return list.stream()
                .map(input -> (ModelMap)Json.GSON.fromJson(input, Json.MAP_STRING_STRING_TYPE))
                .collect(Collectors.toList());
    }

    /**
     * Найти объекты в системе sd по идентификатору метакласса и карте кодов атрибутов и их значений в объекте
     *
     * @param fqn идентификатор метакласса
     * @param map карта кодов атрибутов и их значений
     * @return возвращает список найденых объектов хранящихся в картах, в которых в качестве ключа выступают коды
     * атрибутов
     */
    public static List<ModelMap> findObjects(String fqn, ModelMap map)
    {
        return findObjects(null, fqn, map);
    }

    /**
     * Найти объекты в системе sd по идентификатору метакласса, коду атрибута и его значению в объекте
     *
     * @param fqn идентификатор метакласса
     * @param attrCode код атрибута
     * @param attrValue значение атрибута
     * @return возвращает список найденых объектов хранящихся в картах, в которых в качестве ключа выступают коды
     * атрибутов
     */
    public static List<ModelMap> findObjects(String fqn, String attrCode, String attrValue)
    {
        ModelMap paramForSearch = ModelMap.newMap();
        paramForSearch.put(attrCode, attrValue);
        return findObjects(fqn, paramForSearch);
    }

    /**
     * Найти объекты в системе sd по коду атрибута и его значению в объекте
     *
     * @param bo модель БО
     * @param attrCode код атрибута
     * @param attrValue значение атрибута
     * @return возвращает список найденых объектов хранящихся в картах, в которых в качестве ключа выступают коды
     * атрибутов
     */
    public static List<ModelMap> findObjects(Bo bo, String attrCode, String attrValue)
    {
        ModelMap paramForSearch = ModelMap.newMap();
        paramForSearch.put(attrCode, attrValue);
        return findObjects(bo.getBranch(), bo.getMetaclassFqn(), paramForSearch);
    }

    /**
     * Получить первый найденый элемент справочника в системе sd по коду
     *
     * @param catalogItem модель элемента справочника
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getCatalogItemByCode(CatalogItem catalogItem)
    {
        String catalogFqn = catalogItem.getParentFqn();
        // для возможности извлекать папки (не являющиеся элементами конкретного справочника, но лежащие в той же
        // таблице)
        // характерно для иконочных справочников
        String classId = catalogFqn.split("\\$")[0];
        List<ModelMap> result = findObjects(classId, "code", catalogItem.getCode());
        return result.isEmpty() ? new ModelMap() : result.get(0);
    }

    /**
     * Получить первый найденый элемент справочника в системе sd по uuid'у
     * @param catalogItem модель элемента справочника
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getCatalogItemByUUID(CatalogItem catalogItem)
    {
        List<ModelMap> result = findObjects(catalogItem.getParentFqn(), "UUID", catalogItem.getUuid());
        return result.isEmpty() ? new ModelMap() : result.get(0);
    }

    /**
     * Получить первый найденый элемент справочника в системе sd по uuid'у, ожидая, что атрибут с указанным
     * кодом у найденного объекта изменился, т.е. не равен текущему значению значению
     * @param item модель элемента справочника
     * @param attrCode код атрибута
     * @param expected текущее значение атрибута
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getCatalogItemByUUIDAndChangedAttribute(CatalogItem item, String attrCode, String expected)
    {
        String actual = null;
        ModelMap object = null;
        long currentTime = System.currentTimeMillis();
        do
        {
            object = getCatalogItemByUUID(item);
            actual = object.get(attrCode);
        }
        while ((expected == null ? actual == null : expected.equals(actual))
               && (System.currentTimeMillis() - currentTime < WAIT_ATTR_VALUE));
        return object;
    }

    /**
     * Получить черновик, соответствующий классу обслуживания
     * @param catalogItem модель элемента справочника
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getDraftCatalogItem(CatalogItem catalogItem)
    {
        List<ModelMap> result = findObjects(catalogItem.getParentFqn(), "status", "draft");

        for (ModelMap modelMap : result)
        {
            if (modelMap.get(Model.TITLE).equalsIgnoreCase(catalogItem.getTitle()))
            {
                return modelMap;
            }
        }

        return new ModelMap();
    }

    /**
     * Получить первый найденый объект в системе sd по типу, ожидая, что атрибут с указанным кодом у найденного
     * объекта равен ожидаемому значению
     * @param bo модель БО
     * @param attrCode код атрибута
     * @param expected ожидаемое значение атрибута
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getFirstObjectByCase(Bo bo, String attrCode, String expected)
    {
        String actual = null;
        ModelMap object = null;
        long currentTime = System.currentTimeMillis();
        do
        {
            List<ModelMap> objects = findObjects(bo.getMetaclassFqn());
            if (!objects.isEmpty())
            {

                object = objects.get(0);
                actual = object.get(attrCode);
            }
        }
        while ((expected == null ? actual != null : !expected.equals(actual))
               && (System.currentTimeMillis() - currentTime < WAIT_ATTR_VALUE));
        return object;
    }

    /**
     * Получить первый найденый объект в системе sd по идентификатору метакласса
     * @param fqn идентификатор метакласса
     * @return возвращает объект хранящися в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getFirstObjectByMetaclassFqn(String fqn)
    {
        return findObjects(fqn, ModelMap.newMap()).get(0);
    }

    /**
     * Получить из объекта представленого картой атрибут хранящий список объектов представленных картами. 
     * @param map объект представленный картой
     * @param attrCode код атрибута содержащего набор ссылок на БО, набор элементов справочника и обратная ссылка
     * @return возвращает список объектов представленных картами
     */
    public static List<ModelMap> getMapsArrayValue(ModelMap map, String attrCode)
    {
        List<String> list = Json.GSON.fromJson(map.get(attrCode), Json.LIST_STRING_TYPE);
        return Lists.transform(list, new Function<String, ModelMap>()
        {
            @Override
            public ModelMap apply(String input)
            {
                return Json.GSON.fromJson(input, Json.MAP_STRING_STRING_TYPE);
            }
        });
    }

    /**
     * Получить значение атрибута типа ссылка на БО по модели и атрибуту(или модели и коду атрибута)
     * @param bo модель БО
     * @param attr атрибут(или код атрибута)
     * @return значение атрибута по модели и атрибуту(или модели и коду атрибута)
     */
    public static ModelMap getMapValue(Bo bo, Object attr)
    {
        ModelMap map = getObjectByUUID(bo);
        return Json.stringToMapJson(map.get(attr));
    }

    /**
     * Получить из объекта представленого картой атрибут хранящий объект представленный картой. 
     * @param map объект представленный картой
     * @param attrCode код атрибута содержащего ссылку на БО, ссылку на элемент справочника, 
     * гиперссылку, прямой и обратный таймер, временной интервал
     * @return возвращает объект представленный картой
     */
    public static ModelMap getMapValue(ModelMap map, String attrCode)
    {
        return Json.GSON.fromJson(map.get(attrCode), Json.MAP_STRING_STRING_TYPE);
    }

    /**
     * Получить первый найденый объект в системе sd по идентификатору метакласса, коду атрибута и его значению в объекте
     * @param fqn идентификатор метакласса
     * @param attrCode код атрибута
     * @param attrValue значение атрибута
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getObject(String fqn, String attrCode, String attrValue)
    {
        return findObjects(fqn, attrCode, attrValue).get(0);
    }

    /**
     * Получить первый найденый объект в системе sd по uuid'у
     * @param bo модель БО
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getObjectByUUID(Bo bo)
    {
        final ModelMap model = new ModelMap();
        model.put(SystemAttrEnum.UUID.getCode(), bo.getUuid());
        List<ModelMap> result = findObjects(bo.getBranch(), bo.getMetaclassFqn(), model);
        return result.isEmpty() ? new ModelMap() : result.get(0);
    }

    /**
     * Получить первый найденый объект в системе sd по uuid'у, ожидая, что атрибут, типа ссылка на БО, с указанным 
     * кодом у найденного объекта равен ожидаемому значению
     * @param bo модель БО
     * @param attrCode код атрибута
     * @param expected модель БО - ожидаемое значение атрибута
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getObjectByUUID(Bo bo, String attrCode, Bo expected)
    {
        return getObjectByUUIDAndObjectAttr(bo, attrCode, expected == null ? null : expected.getUuid());
    }

    /**
     * Получить первый найденый объект в системе sd по uuid'у, ожидая, что атрибут с указанным кодом у найденного
     * объекта равен ожидаемому значению
     * @param bo модель БО
     * @param attrCode код атрибута
     * @param expected ожидаемое значение атрибута
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getObjectByUUID(Bo bo, String attrCode, String expected)
    {
        //TODO придумать удобочитаемое название, которое будет отражать то, что метод ожидает выставления
        //значения указанного атрибута у БО с UUID, указанным в модели.Подумать о возможности выноса 
        //подобных метов в отдельный класс.
        String actual = null;
        ModelMap object = null;
        long currentTime = System.currentTimeMillis();
        do
        {
            object = getObjectByUUID(bo);
            actual = object.get(attrCode);
        }
        while ((expected == null ? actual != null : !expected.equals(actual))
               && (System.currentTimeMillis() - currentTime < WAIT_ATTR_VALUE));
        return object;
    }

    /**
     * Получить первый найденый объект в системе sd по uuid'у
     * @param uuid объекта
     * @param fqn идентификатор метакласса
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getObjectByUUID(String uuid, String fqn)
    {
        List<ModelMap> result = findObjects(fqn, "UUID", uuid);
        return result.isEmpty() ? new ModelMap() : result.get(0);
    }

    /**
     * Получить первый найденный объект в системе sd по uuid'у, ожидая, что атрибут, типа ссылка на БО, с указанным 
     * кодом у найденного объекта равен ожидаемому значению
     * @param bo модель БО
     * @param attrCode код атрибута
     * @param expectedUuid uuid БО - ожидаемое значение атрибута
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getObjectByUUIDAndObjectAttr(Bo bo, String attrCode, String expectedUuid)
    {
        String actual = null;
        ModelMap object = null;
        long currentTime = System.currentTimeMillis();
        do
        {
            object = getObjectByUUID(bo);
            String actualObject = object.get(attrCode);
            actual = actualObject == null ? null : Json.stringToMapJson(actualObject).get("UUID");
        }
        while ((expectedUuid == null ? actual != null : !expectedUuid.equals(actual))
               && (System.currentTimeMillis() - currentTime < WAIT_ATTR_VALUE));
        return object;
    }

    /**
     * Получить черновик, соответствующий классу обслуживания
     * @param catalogItem модель элемента справочника
     * @return возвращает объект хранящийся в карте, в которой в качестве ключа выступают коды атрибутов
     */
    public static ModelMap getOldCatalogItem(CatalogItem catalogItem)
    {
        List<ModelMap> result = findObjects(catalogItem.getParentFqn(), "status", "old");

        for (ModelMap modelMap : result)
        {
            if (modelMap.get(Model.TITLE).equalsIgnoreCase(catalogItem.getTitle()))
            {
                return modelMap;
            }
        }

        return new ModelMap();
    }

    /**
     * Получить часовой пояс сервера, на котором поднят стенд
     * @return код часового пояса
     */
    public static String getServerTimeZone()
    {
        return getSdDataUtilsModule().getServerTimeZone();
    }

    /**
     * Получить значение атрибута со строковым значением по модели и атрибуту(или модели и коду атрибута)
     * @param bo модель БО
     * @param attrCode код атрибута
     * @return значение атрибута по модели и атрибуту(или модели и коду атрибута)
     */
    public static String getStringAttributeValue(ModelUuid bo, String attrCode)
    {
        return getSdDataUtilsModule().getStringAttributeValue(bo.getUuid(), attrCode);
    }

    /**
     * Получить значение атрибута со строковым значением по модели и атрибуту(или модели и коду атрибута)
     * @param bo модель БО
     * @param attr атрибут(или код атрибута)
     * @return значение атрибута по модели и атрибуту(или модели и коду атрибута)
     */
    public static String getStringValue(Bo bo, Object attr)
    {
        ModelMap map = getObjectByUUID(bo);
        return map.get(attr);
    }
}
