package ru.naumen.selenium.casesutil.omnichannel;

import static ru.naumen.selenium.casesutil.websocket.DSLWebSocket.connectToWebSocket;

import java.lang.reflect.Type;
import java.util.Map;

import org.junit.Assert;
import org.openqa.selenium.Cookie;
import org.springframework.http.HttpHeaders;
import org.springframework.messaging.simp.stomp.StompFrameHandler;
import org.springframework.messaging.simp.stomp.StompHeaders;
import org.springframework.messaging.simp.stomp.StompSession;

import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.websocket.DSLWebSocket;

/**
 * WebSocket клиент для работы с модулем омниканальности
 * <AUTHOR>
 * @since 12.11.2021
 */
public class OmnichannelWebSocketClient
{
    private static class OmnichannelStompFrameHandler implements StompFrameHandler
    {
        private final OmnichannelWebSocketMessageAcceptor acceptor;

        public OmnichannelStompFrameHandler(OmnichannelWebSocketMessageAcceptor acceptor)
        {
            this.acceptor = acceptor;
        }

        @Override
        public Type getPayloadType(StompHeaders headers)
        {
            return Map.class;
        }

        @Override
        @SuppressWarnings("unchecked")
        public void handleFrame(StompHeaders headers, Object payload)
        {
            acceptor.addMessage((Map<String, Object>)payload);
        }
    }

    private static final String QUEUE_NAME = "Queue.Websocket.Omnichannel";
    private static final String MESSAGE_CHANGE_STATE_DESTINATION = QUEUE_NAME + ".%s.messageStatusChange";
    private static final String NEW_MESSAGE_DESTINATION = QUEUE_NAME + ".%s.newMessage";
    private static final String ADD_DIALOG_DESTINATION = QUEUE_NAME + ".%s.addDialog";
    private static final String REMOVE_DIALOG_DESTINATION = QUEUE_NAME + ".%s.removeDialog";
    private static final String LOAD_FILE_DESTINATION = QUEUE_NAME + ".%s.loadFile";
    private static final String USER_AGENT = DSLWebSocket.DEFAULT_USER_AGENT;

    private final String employeeUuid;
    private final Cookie employeeSessionCookie;

    /**
     * @param employee сотрудник, для которого нужно создать WebSocket клиент омниканальности
     */
    public OmnichannelWebSocketClient(Bo employee)
    {
        this.employeeUuid = employee.getUuid();
        this.employeeSessionCookie = DSLWebSocket.getNewSessionCookieWithUserAgent(
                employee, USER_AGENT);
    }

    /**
     * Подписаться на очередь сообщений о добавлении диалога
     * @return фьючерс сообщения о добавлении диалога
     */
    public OmnichannelWebSocketMessageAcceptor subscribeAddDialog()
    {
        return subscribe(ADD_DIALOG_DESTINATION);
    }

    /**
     * Подписаться на очередь сообщений о изменении статуса сообщения
     * @return фьючерс сообщения о изменении статуса сообщения
     */
    public OmnichannelWebSocketMessageAcceptor subscribeChangeState()
    {
        return subscribe(MESSAGE_CHANGE_STATE_DESTINATION);
    }

    /**
     * Подписаться на очередь сообщений о загрузке файла
     * @return фьючерс сообщения о загрузке файла
     */
    public OmnichannelWebSocketMessageAcceptor subscribeLoadFile()
    {
        return subscribe(LOAD_FILE_DESTINATION);
    }

    /**
     * Подписаться на очередь сообщений о поступлении нового сообщения
     * @return фьючерс сообщения о поступлении нового сообщения
     */
    public OmnichannelWebSocketMessageAcceptor subscribeNewMessage()
    {
        return subscribe(NEW_MESSAGE_DESTINATION);
    }

    /**
     * Подписаться на очередь сообщений о удалении диалога
     * @return фьючерс сообщения о удалении диалога
     */
    public OmnichannelWebSocketMessageAcceptor subscribeRemoveDialog()
    {
        return subscribe(REMOVE_DIALOG_DESTINATION);
    }

    private OmnichannelWebSocketMessageAcceptor subscribe(String destinationPattern)
    {
        try
        {
            String destination = String.format(destinationPattern, employeeUuid);
            StompSession stompSession = connectToWebSocket(
                    Map.of(HttpHeaders.COOKIE, employeeSessionCookie.getName() + "=" + employeeSessionCookie.getValue(),
                            HttpHeaders.USER_AGENT, USER_AGENT)
            );
            OmnichannelWebSocketMessageAcceptor acceptor = new OmnichannelWebSocketMessageAcceptor(stompSession);
            stompSession.subscribe(destination, new OmnichannelStompFrameHandler(acceptor));
            return acceptor;
        }
        catch (Exception e)
        {
            Assert.fail(e.getMessage());
        }
        return null;
    }
}
