package ru.naumen.selenium.casesutil.omnichannel;

import java.util.List;
import java.util.Map;

import org.apache.tika.Tika;
import org.junit.Assert;

import ru.naumen.selenium.casesutil.OmnichannelConstants.WSAttrs;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.core.WaitTool;

/**
 * Утилитарные методы для работы с WebSocket сообщениями модуля омниканальности
 * <AUTHOR>
 * @since 17.11.2021
 */
public class DSLOmnichannelWebSocketMessage
{
    // таймаут ожидания поступления сообщения по WS каналу в сек
    private static final long GET_MESSAGE_TIMEOUT = 5;

    /**
     * Проверяет диалог в сообщении
     * @param dialog информация о диалоге
     * @param dialogUuid идентификатор диалога
     * @param responsibleUuid идентификатор ответственного
     * @param unreadCount кол-во непрочитанных сообщений в диалоге
     */
    public static void assertDialog(Map<String, Object> dialog, String dialogUuid, String responsibleUuid,
            long unreadCount)
    {
        Assert.assertEquals(dialog.get(WSAttrs.UUID), dialogUuid);
        Assert.assertEquals(dialog.get(WSAttrs.RESPONSIBLE_UUID), responsibleUuid);
        Assert.assertEquals(((Integer)dialog.get(WSAttrs.UNREAD_COUNT)).longValue(), unreadCount);
    }

    /**
     * Проверяет пользователя в сообщении
     * @param client информация о пользователе
     * @param title имя пользователя
     * @param employeeUuid идентификатор пользователя
     */
    public static void assertClient(Map<String, Object> client, String title, String employeeUuid)
    {
        Assert.assertNull(client.get(WSAttrs.AVATAR_URL));
        Assert.assertEquals(client.get(WSAttrs.TITLE), title);
        Assert.assertEquals(client.get(WSAttrs.UUID), employeeUuid);
    }

    /**
     * Проверяет сообщение
     * @param message информация о сообщении
     * @param read прочитанно ли сообщение
     * @param author автор сообщения (либо строка либо мапа)
     * @param state статус сообщения
     * @param text текст сообщения
     * @param messageUuid идентификатор сообщения
     * @param timestamp время сообщения
     */
    @SuppressWarnings("unchecked")
    public static void assertMessage(Map<String, Object> message, Boolean read, Object author, String state,
            String text, String messageUuid, long timestamp)
    {
        Assert.assertEquals(message.get(WSAttrs.READ), read);
        Assert.assertEquals(message.get(WSAttrs.STATE), state);
        Assert.assertEquals(message.get(WSAttrs.TEXT), text);
        Assert.assertEquals(message.get(WSAttrs.UUID), messageUuid);
        Assert.assertEquals(message.get(WSAttrs.TIMESTAMP), timestamp);

        if (author instanceof String)
        {
            Assert.assertEquals(message.get(WSAttrs.AUTHOR), author);
        }
        else
        {
            assertClient((Map<String, Object>)message.get(WSAttrs.AUTHOR), ((Bo)author).getTitle(),
                    ((Bo)author).getUuid());
        }
    }

    /**
     * Проверяет файл в сообщении
     * @param file информация о файле
     * @param size размер файла
     * @param name имя файла
     * @param uuid идентификатор файла
     * @param status статус файла
     */
    public static void assertFile(Map<String, Object> file, long size, String name, String uuid, String status)
    {
        Assert.assertEquals(((Integer)file.get(WSAttrs.SIZE)).longValue(), size);
        Assert.assertEquals(file.get(WSAttrs.NAME), name);
        Assert.assertEquals(file.get(WSAttrs.MIME_TYPE), new Tika().detect(name));
        Assert.assertEquals(file.get(WSAttrs.UUID), uuid);
        Assert.assertEquals(file.get(WSAttrs.STATUS), status);
        Assert.assertNull(file.get(WSAttrs.ERROR_MESSAGE));
        Assert.assertNull(file.get(WSAttrs.PLAYER));
        Assert.assertNotNull(file.get(WSAttrs.INTERNAL_UUID));
    }

    /**
     * Проверить, что сообщений по WS каналу не поступило. Ожидание в течение
     * {@link DSLOmnichannelWebSocketMessage#GET_MESSAGE_TIMEOUT}.
     * @param acceptor приёмщик сообщений
     */
    public static void assertMessagesAbsence(OmnichannelWebSocketMessageAcceptor acceptor)
    {
        Assert.assertNull("Сообщения были получены",
                WaitTool.waitSomething(acceptor, GET_MESSAGE_TIMEOUT, 1000, input -> !input.getMessages().isEmpty()));
    }

    /**
     * Возвращает сообщения поступившие из WebSocket канала. Ожидание сообщений в течение
     * {@link DSLOmnichannelWebSocketMessage#GET_MESSAGE_TIMEOUT}.
     * @param acceptor приёмщик сообщений
     * @param expectedCount ожидаемое кол-во сообщений
     * @return сообщение поступившее по WS каналу
     */
    public static List<Map<String, Object>> getMessages(OmnichannelWebSocketMessageAcceptor acceptor,
            long expectedCount)
    {
        List<Map<String, Object>> result = WaitTool.waitSomething(acceptor, GET_MESSAGE_TIMEOUT, 1000, input ->
        {
            List<Map<String, Object>> messages = input.getMessages();
            return messages.size() != expectedCount ? null : messages;
        });
        Assert.assertNotNull("Ожидаемое кол-во сообщений не поступило", result);
        return result;
    }
}
