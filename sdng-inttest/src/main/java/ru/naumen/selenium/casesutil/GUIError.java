package ru.naumen.selenium.casesutil;

import static ru.naumen.selenium.core.WaitTool.WAIT_TIME_APPEAR;
import static ru.naumen.selenium.core.WaitTool.waitToBeClickableAndClick;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

import org.junit.Assert;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import com.google.gdata.util.common.base.StringUtil;

import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.user.StandTypeHolder;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.WebTester;
import ru.naumen.selenium.core.exception.DialogErrorException;
import ru.naumen.selenium.core.exception.LoginFormException;
import ru.naumen.selenium.core.exception.NotIgnoredErrorException;

/**
 * Методы для работы с ошибками тестируемой системы
 * <AUTHOR>
 * @since 12.08.2013
 */
public class GUIError extends CoreTester
{
    public static final String INTERNAL_SERVER_ERROR_MESSAGE =
            "Внутренняя ошибка приложения. Обратитесь к администратору системы";

    /**Форма ошибки*/
    public static final String XPATH_ERROR_FORM_DIALOG =
            "//div[@id='gwt-debug-errorDialog']//div[@id='gwt-debug-dialogWidgetDescriptionElement']";

    /**Кнопка "OK" на форме ошибки.*/
    public static final String XPATH_ERROR_FORM_DIALOG_BUTTON_OK = GUIXpath.Div.ERROR_DIALOG + GUIXpath.Div.OK;

    /**Xpath видимого окна ошибки с заданным текстом**/
    public static final String XPATH_ERROR_FORM_WITH_TEXT =
            "//div[@id='gwt-debug-errorDialog']//div[@id='gwt-debug-dialogWidgetDescriptionElement' "
            + " and text()[contains(.,'%s')]]";

    /**Сообщение об ошибке*/
    public static final String XPATH_ERROR_MESSAGE1 =
            "//div[@id='gwt-debug-errorMessage' and not"
            + "(ancestor::div[contains(@style,'display:none')]) and not"
            + "(ancestor::div[contains(@style,'display: "
            + "none')]) and not(div[contains(@style,'display: none')])]";

    public static final String XPATH_ERROR_MESSAGE2 = "//div[@id='errorMessage']";

    /**
     * Xpath для проверки текста сообщения в окне с ошибкой
     */
    public static final String XPATH_ERROR_MESSAGE_CHECK = "//div[@id='errorMessage' and contains(text(), '%s')]";

    /**Кнопка закрытия сообщения ошибки (крестик).*/
    private static final String XPATH_ERROR_MESSAGE_CLOSE = XPATH_ERROR_MESSAGE1 + GUIXpath.Span.CLOSE_ICON;

    /**
     * Проверить, что появился диалог с ошибкой и указанное сообщение является частью текста ошибки
     * @param expected ожидаемый текст сообщения об ошибке
     */
    public static void assertContainsInErrorDialog(String expected)
    {
        processError(expected, actual ->
                Assert.assertTrue("Полученное сообщение об ошибке не совпало с ожидаемым.", actual.contains(expected)));
    }

    /**
     * Проверить, что появилось указанное сообщение об ошибке (проверка части сообщения)
     * @param messageXpath xpath элемента с ошибкой
     * @param messageError ожидаемый текст сообщения об ошибке
     */
    public static void assertContainsInErrorMessage(String messageXpath, String messageError)
    {
        WaitTool.waitForContainsText(tester.getWebDriver(), By.xpath(messageXpath), messageError, WaitTool.WAIT_TIME);
        String actualMessageError = GUIError.getErrorMessage(messageXpath);
        Assert.assertTrue("Полученное сообщение об ошибке не совпало с ожидаемым. Exp: " + messageError + " " + "Act: "
                          + actualMessageError, actualMessageError.contains(messageError));
    }

    /**
     * Проверить, что появилось указанное сообщение об ошибке
     * @param messageError ожидаемый текст сообщения об ошибке
     */
    public static void assertDialogError(String messageError)
    {
        processError(messageError, actual ->
                Assert.assertEquals("Полученное сообщение об ошибке не совпало с ожидаемым. Ожидалось: "
                                    + messageError + "\nПолучено: " + actual, messageError, actual));
    }

    /**
     * Обработать сообщение
     * @param expectedMessage ожидаемое сообщение
     * @param errorProcessor код обработки
     */
    private static void processError(String expectedMessage, Consumer<String> errorProcessor)
    {
        try
        {
            waitError();
            Assert.fail("Не появилось сообщение об ошибке: " + expectedMessage);
        }
        catch (DialogErrorException e)
        {
            errorProcessor.accept(e.getMessage());
            ignoreError();
        }
    }

    /**
     * Проверить, что появилось указанное сообщение об ошибке
     * @param messageError ожидаемый текст сообщения об ошибке
     * @param timeOutInSeconds время ожидания в секундах
     */
    public static void assertDialogError(String messageError, long timeOutInSeconds)
    {
        assertDialogError(Arrays.asList(messageError), timeOutInSeconds);
    }

    /**
     * Проверить, что появилось одно из указанных сообщений об ошибке
     * @param messageErrors список возможных ошибок
     * @param timeOutInSeconds время ожидания в секундах
     */
    public static void assertDialogError(List<String> messageErrors, long timeOutInSeconds)
    {
        try
        {
            waitError(timeOutInSeconds);
            Assert.fail("Не появилось сообщение об ошибке");
        }
        catch (DialogErrorException e)
        {
            boolean isError = false;
            for (String message : messageErrors)
            {
                if (e.getMessage().contains(message))
                {
                    isError = true;
                    break;
                }
            }
            Assert.assertTrue(String.format("Полученное сообщение об ошибке не совпало с ожидаемым. Act: %s, exp: %s",
                    e.getMessage(), messageErrors), isError);
            ignoreError();
        }
    }

    /**
     * Проверить, что появилось указанное сообщение об ошибке, часть которого непостоянная (каждый раз может отличаться)
     * @param format шаблон сообщения
     * @param parts набор пар: <индекс параметра, набор возможных значений> для непостоянных параметров
     * @param timeOutInSeconds время ожидания в секундах
     * @param args список постоянных (неизменяемых) параметров
     */
    public static void assertDialogErrorWithOptinalMessagePart(String format, Map<Integer, Set<String>> parts,
            long timeOutInSeconds, String... args)
    {
        try
        {
            waitError(timeOutInSeconds);
            Assert.fail("Не появилось сообщение об ошибке: " + format);
        }
        catch (DialogErrorException e)
        {
            String actualMessage = e.getMessage();
            for (int index : parts.keySet())
            {
                for (String part : parts.get(index))
                {
                    List<String> allArgs = new ArrayList<>();
                    Collections.addAll(allArgs, args);
                    allArgs.add(index, part);
                    String messageError = String.format(format, allArgs.toArray());
                    if (StringUtil.equals(messageError, actualMessage))
                    {
                        ignoreError();
                        return;
                    }
                }
            }
            Assert.fail("Не появилось сообщение об ошибке: " + format);
        }
    }

    /**
     * Проверить отсутствие предупреждения на странице
     * @deprecated <b>Метод тупо 3 секунды ждёт появления ошибки</b>. Это всё равно,
     * что поставить sleep(3000) в код теста. Лучше проверять не отсутствие чего-либо,
     * а <b>наличие</b> результата работы тестируемой функциональности.
     * Можно использовать методы, которые используют {@link WebTester#waitAppear(String, Object...)} и т.п.
     */
    @Deprecated // NOSONAR Просто отметка, чтобы никто никогда не использовал этот метод
    public static void assertErrorAbsence()
    {
        waitError(WAIT_TIME_APPEAR);
    }

    /**
     * Проверяет наличие сообщения об ошибке в заголовке страницы.
     * @param errorMessage ожидаемое сообщение
     */
    public static void assertErrorInTitleBar(String errorMessage)
    {
        GUITester.assertTextContains(GUIXpath.Div.HEADER_TITLE, errorMessage);
    }

    /**
     * Проверить что открыта страница с сообщением "У Вас нет прав на выполнение этой операции"
     */
    public static void assertNoRightsMessage()
    {
        Assert.assertTrue("Сообщение об ошибке \"У Вас нет прав на выполнение этой операции\" не появилось", tester
                .isPresence(XPATH_ERROR_MESSAGE_CHECK, ErrorMessages.NO_RIGHTS_MESSAGE));
        ignoreError();
    }

    /**
     * Проверить отсутствие ошибки с сообщением "У Вас нет прав на выполнение этой операции"
     */
    public static void assertNoRightsMessageAbsence()
    {
        Assert.assertFalse("Появилось сообщение об ошибке \"У Вас нет прав на выполнение этой операции\"", tester
                .isPresence(XPATH_ERROR_MESSAGE_CHECK, ErrorMessages.NO_RIGHTS_MESSAGE));
    }

    /**
     * Проверить что открыта страница с сообщением "У Вас нет прав на настройку системы."
     */
    public static void assertNoRightsToAdmin()
    {
        Assert.assertTrue("Сообщение об ошибке \"У Вас нет прав на насройку системы\" не появилось", tester
                .isPresence(XPATH_ERROR_MESSAGE_CHECK, ErrorMessages.NO_RIGHTS_TO_ADMIN_MESSAGE));
        ignoreError();
    }

    /**
     * Проверяем появилась ли ошибка
     * @throws DialogErrorException сообщение о появлении формы ошибки в тестируемой системе
     */
    public static void expectError()
    {
        tester.waitAsyncCall();
        WebDriver driver = tester.getWebDriver();
        // Ожидаем появления сообщений об ошибке
        expectErrorMessage(XPATH_ERROR_FORM_DIALOG, driver);
        expectErrorMessage(XPATH_ERROR_MESSAGE1, driver);
    }

    /**
     * Проверяем появилась ли ошибка и возвращаем результат проверки
     * @return true - появилась, false - не появилась
     */
    public static boolean checkError()
    {
        tester.waitAsyncCall();
        // Проверяем появления сообщений об ошибке
        return checkErrorMessage(XPATH_ERROR_FORM_DIALOG) || checkErrorMessage(XPATH_ERROR_MESSAGE1);
    }

    /**
     * Метод необходимо вызвать после того как будет поймана ошибка, обнаруженная методом {@link #expectError()},
     * если данная ошибка ожидаема и ее необходимо проигнорировать и продолжить работу тестирующей системы далее
     * После того как ошибка поймана - данный метод закроет диалоговое окно ошибки
     * Вызов метода в каком-либо другом случае не произведет никаких действий
     */
    public static void ignoreError()
    {
        ignoreError(1);
    }

    /**
     * Метод необходимо вызвать после того как будет поймана ошибка, обнаруженная методом {@link #expectError()},
     * если данная ошибка ожидаема и ее необходимо проигнорировать и продолжить работу тестирующей системы далее
     * После того как ошибка поймана - данный метод закроет диалоговое окно ошибки
     * Вызов метода в каком-либо другом случае не произведет никаких действий
     * @param counter сколько раз пытаемся закрыть диалоговое окно, если оно не закрылось с 1 раза
     */
    public static void ignoreError(int counter)
    {
        // Игнорировать диалоговое окно с ошибкой
        ignoreErrorDialog(counter);
        // Игнорирование сообщения об ошибке на текущей форме
        ignoreErrorMessage();
        // Игнорирование сообщения об ошибке на форме логина
        ignoreErrorMessageOnLoginForm();
    }

    /**
     * Ожидаем появления окна об ошибки в тестируемой системе в течениии времени ожидания 
     * ({@link ru.naumen.selenium.core.WaitTool#WAIT_TIME})
     * При появлении ошибки будет выброшено исключение
     * @throws DialogErrorException сообщение о появлении формы ошибки в тестирующей системе
     */
    public static void waitError()
    {
        waitError(WaitTool.WAIT_TIME);
    }

    /**
     * Ожидаем появления окна об ошибки в тестируемой системе в течениии указанного времени
     * При появлении ошибки будет выброшено исключение
     * @param timeOutInSeconds - время ожидания в секундах
     * @throws DialogErrorException сообщение о появлении формы ошибки в тестирующей системе
     */
    public static void waitError(long timeOutInSeconds)
    {
        long time = System.currentTimeMillis() + timeOutInSeconds * 1000;
        do
        {
            expectError();
            WaitTool.waitMills(WaitTool.REPETITION_PERIOD);
        }
        while (time > System.currentTimeMillis());
    }

    /**
     * Проверяет наличие ошибки
     * Ожидает появления сообщения об ошибке
     * @param messageXpath xpath элемента с ошибкой
     * @throws DialogErrorException сообщение о появлении ошибки в тестируемой системе
     */
    private static void expectErrorMessage(String messageXpath, WebDriver driver)
    {
        if (tester.isPresence(messageXpath))
        {
            //Цикл нужен для получения текста ошибки
            for (WebElement error : driver.findElements(By.xpath(messageXpath)))
            {
                WebTester.scrollIntoView(driver, error);
                if (error.isDisplayed())
                {
                    throw new DialogErrorException(error.getText());
                }
            }
        }
    }

    /**
     * Проверяет наличие ошибки и возвращает результат проверки
     * @param messageXpath xpath элемента с ошибкой
     * @return результат проверки (true - найдена, false - не найдена)
     */
    private static boolean checkErrorMessage(String messageXpath)
    {
        if (tester.isPresence(messageXpath))
        {
            //Цикл нужен для получения текста ошибки
            for (WebElement error : tester.getWebDriver().findElements(By.xpath(messageXpath)))
            {
                WebTester.scrollIntoView(tester.getWebDriver(), error);
                if (error.isDisplayed())
                {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Проверяет наличие ошибки на форме логина
     * @throws LoginFormException сообщение о появлении ошибки в тестируемой системе
     */
    public static void expectErrorMessageOnLoginForm()
    {
        tester.waitAsyncCall();
        WebDriver driver = tester.getWebDriver();
        if (tester.waitAppear(XPATH_ERROR_MESSAGE2))
        {
            //Цикл нужен для получения текста ошибки
            for (WebElement error : driver.findElements(By.xpath(XPATH_ERROR_MESSAGE2)))
            {
                WebTester.scrollIntoView(driver, error);
                if (error.isDisplayed())
                {
                    throw new LoginFormException(error.getText());
                }
            }
            throw new DialogErrorException();
        }
    }

    private static String getErrorMessage(String messageXpath)
    {
        if (tester.isPresence(messageXpath))
        {
            //Цикл нужен для получения текста ошибки
            for (WebElement error : tester.getWebDriver().findElements(By.xpath(messageXpath)))
            {
                WebTester.scrollIntoView(tester.getWebDriver(), error);
                if (error.isDisplayed())
                {
                    return error.getText();
                }
            }
        }
        return "";
    }

    /**
     * Игнорировать диалоговое окно с ошибкой
     * @param counter сколько раз пытаемся закрыть диалоговое окно, если оно не закрылось с 1 раза
     */
    private static void ignoreErrorDialog(int counter)
    {
        int c = counter;
        while (c-- > 0 && tester.isPresence(XPATH_ERROR_FORM_DIALOG))
        {
            waitToBeClickableAndClick(tester.getWebDriver(), By.xpath(XPATH_ERROR_FORM_DIALOG_BUTTON_OK));
        }
        if (tester.isPresence(XPATH_ERROR_FORM_DIALOG))
        {
            throw new NotIgnoredErrorException("Не удалось проигнорировать ошибку на странице");
        }
    }

    /**
     * Игнорирование сообщения об ошибке на текущей форме
     */
    private static void ignoreErrorMessage()
    {
        if (tester.isPresence(XPATH_ERROR_MESSAGE1))
        {
            // Ожидаем сообщения об ошибке на текущей форме
            for (WebElement closeIcon : tester.getWebDriver().findElements(By.xpath(XPATH_ERROR_MESSAGE_CLOSE)))
            {
                WebTester.scrollIntoView(tester.getWebDriver(), closeIcon);
                if (closeIcon.isDisplayed())
                {
                    //для корректной работы chrome скролл со смещением
                    tester.scrollWindowToElementWithOffSet(closeIcon, 0, -200);
                    closeIcon.click();
                }
            }
        }
    }

    /**
     * Игнорирование сообщения об ошибке на форме логина
     */
    private static void ignoreErrorMessageOnLoginForm()
    {
        if (tester.isPresence(XPATH_ERROR_MESSAGE2))
        {
            tester.getWebDriver().get(StandTypeHolder.getCurrentStandUrl());
        }
    }

    /**
     * Ожидаем появления окна об ошибки на форме логина в течениии указанного времени
     * При появлении ошибки будет выброшено исключение
     * @param timeOutInSeconds - время ожидания в секундах
     * @throws DialogErrorException сообщение о появлении формы ошибки в тестирующей системе
     */
    public static void waitErrorOnLogin(long timeOutInSeconds)
    {
        long time = System.currentTimeMillis() + timeOutInSeconds * 1000;
        do
        {
            expectErrorMessageOnLoginForm();
            WaitTool.waitMills(WaitTool.REPETITION_PERIOD);
        }
        while (time > System.currentTimeMillis());
    }
}