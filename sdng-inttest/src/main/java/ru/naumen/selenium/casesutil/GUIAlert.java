package ru.naumen.selenium.casesutil;

import java.util.Objects;

import org.junit.Assert;
import org.openqa.selenium.Alert;
import org.openqa.selenium.ElementNotVisibleException;
import org.openqa.selenium.NoAlertPresentException;
import org.openqa.selenium.TimeoutException;

import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.core.ScreenshotTool;
import ru.naumen.selenium.core.WaitTool;

/**
 * Методы для работы с алертами
 * <AUTHOR>
 * @since 24.10.2013
 *
 */
public class GUIAlert extends CoreTester
{

    /**
     * Подтвердить алерт
     */
    public static void accept()
    {
        tester.getAlert().accept();
    }

    /**
     * Проверить текст в алерте
     * @param expectedMessages ожидаемое сообщение об ошибке
     */
    public static void assertAlert(String... expectedMessages)
    {
        Alert alert = tester.getWebDriver().switchTo().alert();
        if (expectedMessages.length == 1)
        {
            Assert.assertEquals("Отсутствует сообщение об ошибке", expectedMessages[0], alert.getText());
        }
        else
        {
            String actualMessage = alert.getText();
            boolean equals = false;
            for (String expectedMessage : expectedMessages)
            {
                equals |= Objects.equals(expectedMessage, actualMessage);
            }
            Assert.assertTrue("Отсутствует сообщение об ошибке", equals);
        }
        alert.accept();
    }

    /**
     * Проверить отсутствие алерта на странице
     */
    public static void assertAlertAbsent()
    {
        Alert alert = null;
        try
        {
            // На случай, если появление алерта произойдет не сразу
            WaitTool.waitMills(400);
            alert = tester.getWebDriver().switchTo().alert();
        }
        catch (NoAlertPresentException e)
        {
            return;
        }

        if (alert != null)
        {
            alert.accept();
            Assert.fail("Появилось неожиданное сообщение");
        }
    }

    /**
     * Проверить присутствие всплывающего алерта на странице
     * @return true, если алерт есть на странице, иначе false
     */
    public static boolean alertIsPresence()
    {
        try
        {
            WaitTool.waitSomething(tester.getWebDriver(), 1, (elem) ->
            {
                tester.getWebDriver().switchTo().alert();
                return true;
            });
            return true;
        }
        catch (TimeoutException ex)
        {
            return false;
        }
    }

    /**
     * Проверить текст алерта
     * @param message - ожидаемый текст
     */
    public static void assertText(String message)
    {
        Assert.assertEquals(message, tester.getAlert().getText());
    }

    /**
     * Проверить присутствие алерта о потере внесенных изменений
     */
    public static void assertUnsavedChanges()
    {
        assertText(ErrorMessages.FORM_HAS_UNSAVED_CHANGES);
    }

    /**
     * Отклонить алерт
     */
    public static void dismiss()
    {
        tester.getAlert().dismiss();
    }

    /**
     * Послать текст в алерт
     * @param keysToSend
     */
    public static void sendKeys(String keysToSend)
    {
        Alert alert = tester.getAlert();
        try
        {
            alert.sendKeys(keysToSend);
        }
        catch (ElementNotVisibleException e)
        {
            ScreenshotTool.createWindowScreenshot();
            alert.dismiss();
            throw e;
        }
    }
}
