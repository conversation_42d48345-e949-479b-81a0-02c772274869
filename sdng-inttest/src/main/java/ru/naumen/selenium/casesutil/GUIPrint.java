package ru.naumen.selenium.casesutil;

import java.util.List;

import org.junit.Assert;
import org.openqa.selenium.WebDriver;

import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.core.exception.TestingSystemException;

/**
 * Методы для работы с диалогом печати
 * <AUTHOR>
 * @since 13.07.2017
 */
public class GUIPrint extends CoreTester
{
    private static String chromePrintForm = "//html[@id='print-preview']";
    private static String chromePrintCancel = "//button[@class = 'cancel']";
    private static String printDialogNotFound = "Диалог печати не открылся";

    /**
     * Проверить, открыт ли диалог печати после проверки диалог закрывается
     */
    public static void assertPrintDialog()
    {
        if (Config.isChrome())
        {
            Assert.assertTrue(printDialogNotFound, isPrintDialogChrome());
        }
        else
        {
            Assert.assertTrue(printDialogNotFound, isPrintDialogFirefox());
        }
    }

    /**
     * Закрыть диалог печати браузера Chrome
     */
    private static void closePrintDialogChrome()
    {
        tester.click(chromePrintCancel);
    }

    /**
     * Открыт ли диалог печати браузера Chrome
     * @return true-открыт, false-закрыт
     */
    private static boolean isPrintDialogChrome()
    {
        String currentWindowHandle = tester.getWindowHandle();
        List<String> windowHandlers = tester.getWindowHandlers();
        if (windowHandlers.size() < 2)
        {
            return false;
        }
        windowHandlers.remove(currentWindowHandle);
        Boolean isPrintDialog = false;
        WebDriver window = null;
        try
        {
            for (String windowHandle : windowHandlers)
            {
                List<String> windows = tester.getWindowHandlers();
                if (windows.contains(windowHandle))
                {
                    window = tester.getWebDriver().switchTo().window(windowHandle);
                    if (tester.isPresence(chromePrintForm))
                    {
                        isPrintDialog = true;
                        closePrintDialogChrome();
                    }
                    else
                    {
                        window.close();
                    }
                }
            }
        }
        catch (Exception e)
        {
            throw new TestingSystemException(e);
        }
        finally
        {
            window = tester.getWebDriver().switchTo().window(currentWindowHandle);
        }
        return isPrintDialog;
    }

    /**
     * Открыт ли диалог печати браузера Firefox
     * @return true-открыт, false-закрыт
     */
    private static boolean isPrintDialogFirefox()
    {
        return tester.getWindowHandlers().size() >= 2;
    }
}
