package ru.naumen.selenium.casesutil.omnichannel;

import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.omnichannel.catalog.GateTypes;

/**
 * Утилитарные методы для работы с элементами системного справочника "Типы шлюза"
 * <AUTHOR>
 * @since 12.10.2023
 */
public class DSLGateTypesItem
{
    /**
     * Получить системный элемент справочника "MessageBox"
     * @return системный элемент справочника "MessageBox"
     */
    public static CatalogItem getMessageBoxItem()
    {
        return DSLCatalogItem.getExistingCatalogItem(SystemCatalog.GATE_TYPES, GateTypes.MESSAGE_BOX.getCode());
    }

    /**
     * Получить системный элемент справочника "OmniGate"
     * @return системный элемент справочника "OmniGate"
     */
    public static CatalogItem getOmniGateItem()
    {
        return DSLCatalogItem.getExistingCatalogItem(SystemCatalog.GATE_TYPES, GateTypes.OMNI_GATE.getCode());
    }
}
