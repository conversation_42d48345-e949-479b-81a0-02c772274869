package ru.naumen.selenium.casesutil.omnichannel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.messaging.simp.stomp.StompSession;

import ru.naumen.selenium.core.Cleaner;

/**
 * Принимает сообщения из WebSocket канала (автоматически закрывает соединение после прохождения теста)
 * <AUTHOR>
 * @since 30.11.2021
 */
public class OmnichannelWebSocketMessageAcceptor
{
    private final StompSession stompSession;
    private final List<Map<String, Object>> messages = new ArrayList<>();

    public OmnichannelWebSocketMessageAcceptor(StompSession stompSession)
    {
        this.stompSession = stompSession;
        Cleaner.afterTest(this::close);
    }

    /**
     * Добавляет новое сообщение в результирующий список из WebSocket канала
     * @param message новое сообщение
     */
    public void addMessage(Map<String, Object> message)
    {
        messages.add(message);
    }

    /**
     * Возвращает список сообщений поступивших из WebSocket канала
     * @return список сообщений
     */
    public List<Map<String, Object>> getMessages()
    {
        return messages;
    }

    /**
     * Закрывает подключение к WebSocket каналу
     */
    private void close()
    {
        if (stompSession.isConnected())
        {
            stompSession.disconnect();
        }
        messages.clear();
    }
}
