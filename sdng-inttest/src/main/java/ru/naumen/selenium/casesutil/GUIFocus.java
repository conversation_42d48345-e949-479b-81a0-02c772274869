package ru.naumen.selenium.casesutil;

import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.interfaceelement.CoreTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;

/**
 * Утилитарные методы для работы с фокусом
 *
 * <AUTHOR>
 * @since 25.08.2020
 */
public class GUIFocus extends CoreTester
{
    private static final String PARENT_DIV_XPATH = "/parent::div";
    private static final String ANCESTOR_LABEL_XPATH = "/ancestor::label";

    /**
     * Проверить, что элемент находится в фокусе в выпадающем списке
     * @param id идентификатор элемента в списке
     */
    public static void assertFocusInList(String id)
    {
        GUITester.assertFocus(GUIXpath.Div.POPUP_LIST_SELECT + Any.ID_PATTERN + PARENT_DIV_XPATH, true, id);
    }

    /**
     * Проверить, что элемент находится в фокусе в выпадающем списке
     * @param index индекс элемента в списке
     */
    public static void assertFocusInListByIndex(int index)
    {
        GUITester.assertFocus(GUIXpath.Div.POPUP_LIST_SELECT + GUISelect.ELEMENT_INDEX, true, index);
    }

    /**
     * Проверить, что элемент находится в фокусе в дереве множественного выбора
     * @param id идентификатор элемента в дереве
     */
    public static void assertFocusInMultiTree(String id)
    {
        GUITester.assertFocus(CoreTree.X_VALUE_TREE + Any.ID_PATTERN + ANCESTOR_LABEL_XPATH + PARENT_DIV_XPATH, true,
                id);
    }

    /**
     * Проверить, что элемент находится в фокусе в дереве выбора
     * @param id идентификатор элемента в дереве
     */
    public static void assertFocusInTree(String id)
    {
        GUITester.assertFocus(CoreTree.X_VALUE_TREE + Any.ID_PATTERN + PARENT_DIV_XPATH, true, id);
    }

    /**
     * Проверить, что элемент находится в фокусе в дереве выбора  с папками
     * @param id идентификатор элемента в дереве
     */
    public static void assertFocusInTreeFolder(String id)
    {
        GUITester.assertFocus(CoreTree.X_VALUE_TREE + Any.ID_PATTERN + PARENT_DIV_XPATH + PARENT_DIV_XPATH, true, id);
    }
}
