package ru.naumen.selenium.casesutil.omnichannel;

import ru.naumen.selenium.casesutil.model.IRemoveOperation;
import ru.naumen.selenium.casesutil.model.RemoveOperation;
import ru.naumen.selenium.casesutil.model.omnichannel.ConnectionSettings;
import ru.naumen.selenium.casesutil.scripts.element.SEConnectionSettings;
import ru.naumen.selenium.core.Cleaner;

/**
 * DSL методы для работы с {@link ConnectionSettings}
 * <AUTHOR>
 * @since 17.02.2021
 */
public class DSLConnectionSettings
{
    private static final IRemoveOperation REMOVE_OPERATION =
            new RemoveOperation(SEConnectionSettings.editConnectionSettings(new ConnectionSettings()));

    /**
     * Получить операцию, которая восстановит значения по умолчанию на карточке "настроек подключения"
     * @return операция удаления
     */
    public static IRemoveOperation getDefaultConnectionSettings()
    {
        return REMOVE_OPERATION;
    }

    /**
     * Редактировать "настройки подключения" к шлюзу омниканальности
     * @param connectionSettings модель "настроек подключения"
     */
    public static void edit(ConnectionSettings connectionSettings)
    {
        SEConnectionSettings.connectionSettingsModule.editConnectionSettings(connectionSettings.getFields());
        Cleaner.push(getDefaultConnectionSettings());
    }
}
