package ru.naumen.selenium.casesutil.styletemplate;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.model.styletemplate.StyleTemplate;
import ru.naumen.selenium.modules.IModuleStyleTemplate;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.RandomUtils;

/**
 * Методы для работы с шаблонами стилей в приложении.
 * <AUTHOR>
 * @since Dec 14, 2016
 */
public class DSLStyleTemplate
{
    /**
     * Получить модуль для работы с шаблонами стилей.
     */
    public static IModuleStyleTemplate getStyleTemplateModule()
    {
        return ScriptModules.getStyleTemplateModule();
    }

    /**
     * Создает на сервере новые шаблоны стилей, определенные их моделями.
     * @param templates модели шаблонов
     */
    public static void add(StyleTemplate... templates)
    {
        String cleaningUuid = RandomUtils.randomUUID();
        for (StyleTemplate template : templates)
        {
            getStyleTemplateModule().addTemplate(template.getFields());
            template.setExists(true);
            template.setCleaningUuid(cleaningUuid);
        }
    }

    /**
     * Проверяет, что дата создания шаблона совпадает с датой последнего изменения.
     * @param templateCode код шаблона
     */
    public static void assertCreationAndModificationDatesAreEqual(String templateCode)
    {
        Assert.assertTrue("Дата создания не равна дате последнего изменения.",
                getStyleTemplateModule().isTemplateModifiedAfterCreation(templateCode));
    }

    /**
     * Проверяет, что дата создания шаблона не совпадает с датой последнего изменения.
     * @param templateCode код шаблона
     */
    public static void assertCreationAndModificationDatesAreNotEqual(String templateCode)
    {
        Assert.assertFalse("Дата создания равна дате последнего изменения.",
                getStyleTemplateModule().isTemplateModifiedAfterCreation(templateCode));
    }

    /**
     * Удаляет шаблоны стилей с сервера.
     * @param templates модели шаблонов
     */
    public static void delete(StyleTemplate... templates)
    {
        for (StyleTemplate template : templates)
        {
            getStyleTemplateModule().deleteTemplate(template.getFields());
            template.setExists(false);
        }
    }

    /**
     * Изменяет существующие шаблоны стилей на сервере.
     * @param templates модели шаблонов
     */
    public static void edit(StyleTemplate... templates)
    {
        for (StyleTemplate template : templates)
        {
            getStyleTemplateModule().editTemplate(template.getFields());
        }
    }

    /**
     * Возвращает модель существующего шаблона стилей с указанным кодом.
     * @param code код шаблона
     * @return модель шаблона
     */
    public static StyleTemplate getTemplate(String code)
    {
        return new StyleTemplate(getStyleTemplateModule().getTemplate(code));
    }
}
