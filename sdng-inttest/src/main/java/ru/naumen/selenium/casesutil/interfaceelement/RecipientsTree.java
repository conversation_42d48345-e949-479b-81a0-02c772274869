package ru.naumen.selenium.casesutil.interfaceelement;

import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Общие методы для работы с выпадающими списками типа дерево в поле "Сотрудники" (получатели оповещения) на
 * форме добавления/редактирования действия по событию типа "Оповещение"
 * <AUTHOR>
 * @since 18.06.2014
 */
public class RecipientsTree extends SimpleTree
{
    public static final String TEAMS_NODE = "teams";
    public static final String ROLES_NODE = "roles";

    private final MetaClass metaClass;

    private BoTree rootTree;
    private RolesTree rolesTree;
    private RecipientsTeamTree teamsTree;

    /**
     * Конструктор
     * @param metaClass модель метакласса, для которого создано действие по событию
     */
    public RecipientsTree(MetaClass metaClass)
    {
        super(GUIXpath.Id.RECIPIENTS_VALUE);
        this.metaClass = metaClass;
    }

    /**
     * Проверить, что основные узлы дерева задизейблены: Компания, Команды, Роли,
     * Роли/Абсолютные роли, Роли/Название метакласса
     */
    public void assertDisableElements()
    {
        BoTree tree = new BoTree(GUIXpath.Id.RECIPIENTS_VALUE, false);
        tree.assertEnableElement(false, SharedFixture.root());
        tree.assertEnableElement(false, TEAMS_NODE);
        roles().assertEnableElement(false, ROLES_NODE);
        roles().assertEnableElement(false, ROLES_NODE, RolesTree.GLOBAL_ROLES_NODE);
        roles().assertEnableElement(false, ROLES_NODE, metaClass.getFqn());
    }

    /**
     * Возвращает дерево для работы с ролями
     * @return дерево для работы с ролями
     */
    public RolesTree roles()
    {
        if (rolesTree == null)
        {
            rolesTree = new RolesTree(metaClass);
        }
        return rolesTree;
    }

    /**
     * Возвращает дерево для работы с узлом Компания
     */
    public BoTree root()
    {
        if (rootTree == null)
        {
            rootTree = createRootTree();
        }
        return rootTree;
    }

    /**
     * Создать дерево для работы с узлом Компания
     */
    public static BoTree createRootTree()
    {
        return new BoTree(GUIXpath.Id.RECIPIENTS_VALUE, true);
    }

    /**
     * Возвращает дерево для работы с узлом Команды
     * @return дерево для работы с узлом Команды
     */
    public RecipientsTeamTree teams()
    {
        if (teamsTree == null)
        {
            teamsTree = new RecipientsTeamTree();
        }
        return teamsTree;
    }
}
