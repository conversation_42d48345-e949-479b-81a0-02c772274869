package ru.naumen.selenium.casesutil.schemaoptimization;

import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.modules.IModuleSchemaOptimizationProcess;
import ru.naumen.selenium.modules.ScriptModules;

/**
 * Утилитарные методы для работы с процессом оптимизации БД
 * <AUTHOR>
 * @since 08.02.2021
 **/
public class DSLSchemaOptimizationProcess
{
    /**
     * Получить модуль для работы с процессом оптимизации БД
     */
    public static IModuleSchemaOptimizationProcess getSchemaOptimizationProcess()
    {
        return ScriptModules.getIModuleSchemaOptimizationProcess();
    }

    /**
     * Выключить отложенное удаление колонок/таблиц
     * Выключить видимость блока "Оптимизация базы данных" в админке
     * по окончанию теста отключает обратно, т.к. по дефолту доступ выключен
     */
    public static void disable()
    {
        getSchemaOptimizationProcess().disableDbDeferredDeletion();
    }

    /**
     * Включить отложенное удаление колонок/таблиц
     * Включить видимость блока "Оптимизация базы данных" в админке
     */
    public static void enable()
    {
        getSchemaOptimizationProcess().enableDbDeferredDeletion();
        Cleaner.afterTest(DSLSchemaOptimizationProcess::disable);
    }
}