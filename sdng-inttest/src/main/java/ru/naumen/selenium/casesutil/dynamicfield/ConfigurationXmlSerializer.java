package ru.naumen.selenium.casesutil.dynamicfield;

import java.io.ByteArrayOutputStream;
import java.nio.charset.Charset;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import org.w3c.dom.Document;
import org.w3c.dom.Element;

import ru.naumen.selenium.casesutil.model.dynamicfield.DynamicFieldConfiguration;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.util.XmlUtils;

/**
 * Компонент для преобразования конфигурации динамических полей в строку XML.
 * <AUTHOR>
 * @since Apr 07, 2024
 */
public class ConfigurationXmlSerializer
{
    /**
     * Преобразует объект конфигурации динамических полей в XML, который можно использовать как конфиг для SMP.
     * @param configuration объект конфигурации
     * @return строка XML с конфигурацией
     */
    public static String serialize(DynamicFieldConfiguration configuration)
    {
        try
        {
            Document xmlDocument = DocumentBuilderFactory.newInstance().newDocumentBuilder().newDocument();
            Element root = xmlDocument.createElement("configuration");
            xmlDocument.appendChild(root);

            Element templateClassFqn = xmlDocument.createElement("templateClassFqn");
            templateClassFqn.setTextContent(configuration.getTemplateClassFqn());
            root.appendChild(templateClassFqn);

            Element attributeMappings = xmlDocument.createElement("templateAttributes");
            configuration.getAttributeMapping().forEach((from, to) ->
            {
                Element mapping = xmlDocument.createElement("attribute");
                mapping.setAttribute("name", from);
                mapping.setTextContent(to);
                attributeMappings.appendChild(mapping);
            });
            root.appendChild(attributeMappings);

            Element typeMappings = xmlDocument.createElement("templateTypes");
            configuration.getTypeMapping().forEach((from, to) ->
            {
                Element mapping = xmlDocument.createElement("type");
                mapping.setAttribute("from", from);
                mapping.setAttribute("to", to);
                typeMappings.appendChild(mapping);
            });
            root.appendChild(typeMappings);

            if (configuration.getPathToGroups() != null)
            {
                Element pathToGroups = xmlDocument.createElement("pathToGroups");
                pathToGroups.setTextContent(configuration.getPathToGroups());
                root.appendChild(pathToGroups);
            }

            Element visibleInListConditions = xmlDocument.createElement("visibleInListConditions");
            configuration.getTemplateVisibilityConditions().forEach(attribute ->
            {
                Element condition = xmlDocument.createElement("templateAttribute");
                condition.setTextContent(attribute);
                visibleInListConditions.appendChild(condition);
            });
            configuration.getGroupVisibilityConditions().forEach(attribute ->
            {
                Element condition = xmlDocument.createElement("groupAttribute");
                condition.setTextContent(attribute);
                visibleInListConditions.appendChild(condition);
            });
            root.appendChild(visibleInListConditions);

            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            XmlUtils.save(xmlDocument, stream);
            return stream.toString(Charset.defaultCharset());
        }
        catch (ParserConfigurationException e)
        {
            throw new ErrorInCodeException(e);
        }
    }
}
