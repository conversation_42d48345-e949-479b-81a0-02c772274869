package ru.naumen.selenium.casesutil.metaclass;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.UsagePointApplication;

/**
 * Форма добавления/редактирования места использования встроенного приложения
 * <AUTHOR>
 * @since 22.11.2021
 */
public class GUIEmbeddedApplicationUsagePointForm extends CoreTester
{
    private static final String TITLE_VALUE_INPUT = "//input[@id='gwt-debug-title-value']";
    private static final String CODE_VALUE_INPUT = "//input[@id='gwt-debug-code-value']";
    private static final String FORM_TYPE_VALUE_INPUT = "//*[@id='gwt-debug-formType-value']//input";
    private static final String FQNS_VALUE_INPUT = "//*[@id='gwt-debug-classFqns-value']//input";
    private static final String USER_EVENT_ACTIONS_VALUE_INPUT = "//*[@id='gwt-debug-userEventActions-value']//input";

    /**
     * Заполнить поля места использования при добавлении
     * @param appUsageModel модель места использования встроенного приложения
     */
    public static void fillAdd(UsagePointApplication appUsageModel)
    {
        setTitle(appUsageModel);
        setCode(appUsageModel);
        setFqns(appUsageModel);
        setFormType(appUsageModel);
    }

    /**
     * Установить значение поля Код
     * @param appUsageModel модель места использования
     */
    public static void setCode(UsagePointApplication appUsageModel)
    {
        setCode(appUsageModel.getCode());
    }

    /**
     * Установить значение поля Код
     * @param code - код места использования
     */
    public static void setCode(String code)
    {
        CoreTester.tester.sendKeys(CODE_VALUE_INPUT, code);
    }

    /**
     * Установить значение поля "Объекты"
     * @param model - модель места использования
     */
    public static void setFqns(UsagePointApplication model)
    {
        setFqns(model.getFqns());
    }

    /**
     * Установить значение поля "Объекты"
     * @param fqns - список fqn объектов
     */
    public static void setFqns(List<String> fqns)
    {
        fqns.forEach(f -> GUISelect.selectById(FQNS_VALUE_INPUT, f));
    }

    /**
     * Установить значение поля "Тип формы"
     * @param model - модель места использования
     */
    public static void setFormType(UsagePointApplication model)
    {
        setFormType(model.getFormType());
    }

    /**
     * Установить значение поля "Тип формы"
     * @param formType - код типа формы
     */
    public static void setFormType(String formType)
    {
        GUISelect.select(FORM_TYPE_VALUE_INPUT, formType);
    }

    /**
     * Установить значение поля Название
     * @param model - модель места использования
     */
    public static void setTitle(UsagePointApplication model)
    {
        setTitle(model.getTitle());
    }

    /**
     * Установить значение поля Название
     * @param title - название места использования
     */
    public static void setTitle(String title)
    {
        CoreTester.tester.sendKeys(TITLE_VALUE_INPUT, title);
    }

    /**
     *  Установить значение поля "Доступные пользовательские действия по событию"
     */
    public static void setUserEventActions(Collection<EventAction> eventActions)
    {
        GUIMultiSelect.select(USER_EVENT_ACTIONS_VALUE_INPUT,
                eventActions.stream().map(EventAction::getUuid).collect(Collectors.toList()));
    }
}
