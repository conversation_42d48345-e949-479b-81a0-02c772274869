package ru.naumen.selenium.casesutil.metaclass;

import static ru.naumen.selenium.casesutil.model.attr.DAOAttribute.createPseudo;

import jakarta.annotation.Nullable;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Constant;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;

/**
 * Утилитарные методы для работы со списком встроенных приложений
 * <AUTHOR>
 */
public class GUIEmbeddedApplicationList extends GUIAdvListUtil
{
    private static final String ADVLIST = "gwt-debug-embeddedApplications";

    private static final String X_SPAN_ENABLED_ICON = "//span[@id='gwt-debug-enabled' and @_code='%s']";

    public static final Attribute TITLE_ATTR = createPseudo("Название", "title", null);
    public static final Attribute APPLICATION_TYPE_ATTR = createPseudo("Тип приложения", "applicationType", null);

    /**Пиктограммы*/
    public static final String PICT_EDIT = "editEmbeddedApp";
    public static final String PICT_DELETE = "deleteEmbeddedApp";
    public static final String PICT_ENABLE = "toggleEmbeddedAppProvider";

    private static volatile GUIEmbeddedApplicationList advlist;

    /**
     * Добавить приложение и заполнить новый скрипт (если есть)
     */
    public static void addEmbeddedApplication(EmbeddedApplication model, @Nullable ScriptInfo script)
    {
        clickAdd();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        GUIEmbeddedApplicationForm.fillAdd(model);
        if (script != null)
        {
            GUIEmbeddedApplicationForm.fillNewScript(script);
        }
        GUIForm.applyForm();
        model.setExists(true);
    }

    /**
     * Добавить приложение
     */
    public static void addEmbeddedApplication(EmbeddedApplication model)
    {
        addEmbeddedApplication(model, null);
    }

    /**
     * Добавить приложение, согласно его модели
     * @param model - модель приложения
     */
    public static void addEmbeddedApplicationAndEnable(EmbeddedApplication model)
    {
        addEmbeddedApplication(model);

        GUIForm.assertFormDisappear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        if (isEnabled(model) != model.getEnable())
        {
            clickEnable(model);
        }
    }

    public static GUIEmbeddedApplicationList advlist()
    {
        if (advlist == null)
        {
            advlist = new GUIEmbeddedApplicationList();
        }
        return advlist;
    }

    /**
     * Проверить отсутствие приложения в списке
     */
    public static void assertAbsenceEmbeddedApplication(EmbeddedApplication... model)
    {
        advlist().content().asserts().rowsAbsence(model);
    }

    /**
     * Проверка что приложение включено/выключено
     * @param state состояние которое должно быть
     */
    public static void assertEnabled(EmbeddedApplication application, boolean state)
    {
        if (state)
        {
            GUITester.assertPresent(GUIAdvListXpath.CONTENT_ROW + X_SPAN_ENABLED_ICON,
                    "Встроенное приложение не включено",
                    application.getCode(), Constant.YES);
        }
        else
        {
            GUITester.assertPresent(GUIAdvListXpath.CONTENT_ROW + X_SPAN_ENABLED_ICON,
                    "Встроенное приложение не выключено",
                    application.getCode(), Constant.NO);
        }
    }

    /**
     * Проверить наличие приложения в списке
     */
    public static void assertPresenceEmbeddedApplication(EmbeddedApplication... model)
    {
        advlist().content().asserts().rowsPresenceByTitle(model);
    }

    /**
     * Проверить, что находишься в разделе 'Приложения'
     */
    public static void assertThatList()
    {
        String message = "Находимся не в разделе 'Приложения'";
        GUITester.assertTextPresentWithMsg(Div.HEADER_TITLE, "Приложения", message);
    }

    /**
     * Нажать кнопку "Добавить приложение"
     */
    public static void clickAdd()
    {
        advlist().toolPanel().clickAdd();
    }

    /**
     * Нажать кнопку "Удалить"
     * @param model - модель приложения
     */
    public static void clickDelete(EmbeddedApplication model)
    {
        advlist().content().clickPict(model, GUIEmbeddedApplicationList.PICT_DELETE);
    }

    /**
     * Открыть форму редактирования приложения
     * @param model - модель приложения
     */
    public static void clickEdit(EmbeddedApplication model)
    {
        advlist().content().clickPict(model, GUIEmbeddedApplicationList.PICT_EDIT);
    }

    /**
     * Нажать кнопку включить приложение
     */
    public static void clickEnable(EmbeddedApplication model)
    {
        advlist().content().clickPict(model, GUIEmbeddedApplicationList.PICT_ENABLE);
    }

    /**
     * Удалить приложение
     * @param model - модель приложения
     */
    public static void deleteEmbeddedApplication(EmbeddedApplication model)
    {
        clickDelete(model);
        GUIForm.assertQuestionAppear("Диалог подтверждения удаления встроенного приложения не появился.");
        GUIForm.clickYes();
        model.setExists(false);
    }

    /**
     * Добавить приложение, согласно его модели
     * @param model - модель приложения
     */
    public static void editEmbeddedApplication(EmbeddedApplication model)
    {
        clickEdit(model);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        GUIEmbeddedApplicationForm.fillEdit(model);

        GUIForm.applyForm();

        if (isEnabled(model) != model.getEnable())
        {
            clickEnable(model);
        }
    }

    /**
     * Включено ли приложение
     */
    public static boolean isEnabled(EmbeddedApplication model)
    {
        return tester.isPresence(GUIAdvListXpath.CONTENT_ROW + X_SPAN_ENABLED_ICON,
                model.getCode(), Constant.YES);
    }

    /**
     * Выключено ли приложение
     */
    public static boolean isNotEnabled(EmbeddedApplication model)
    {
        return tester.isPresence(GUIAdvListXpath.CONTENT_ROW + X_SPAN_ENABLED_ICON,
                model.getCode(), Constant.NO);
    }

    /**
     * Отредактировать и сохранить встроенное приложение
     * @param model
     */
    public static void simpleEditEmbeddedApplication(EmbeddedApplication model)
    {
        clickEdit(model);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        GUIEmbeddedApplicationForm.fillEdit(model);

        GUIForm.applyForm();
    }

    private GUIEmbeddedApplicationList()
    {
        super(ADVLIST);
    }
}
