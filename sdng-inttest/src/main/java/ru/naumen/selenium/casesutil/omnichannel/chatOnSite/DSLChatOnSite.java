package ru.naumen.selenium.casesutil.omnichannel.chatOnSite;

import java.util.Comparator;

import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.model.ModelFactory;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.omnichannel.SessionContext;
import ru.naumen.selenium.casesutil.omnichannel.DSLMessage;
import ru.naumen.selenium.core.WaitTool;

/**
 * Утилитарные методы работы с "Чатом на сайте" (витриной)
 * <AUTHOR>
 * @since 17.08.2021
 */
public class DSLChatOnSite
{
    // первое сообщения в чате при открытии сессии
    public static final String FIRST_MESSAGE_TEXT = "Посетитель начал диалог";
    // таймаут ожидания отправки ответного сообщения
    private static final long WAITING_SEND_TIMEOUT_IN_MILLISECOND = 1000L;

    /**
     * Получить последний {@link SessionContext} обмена сообщениями с чатом. Все элементы контекста автоматически
     * регистрируется в очередь на удаление.
     * @param firstMessageText текст первого сообщения в сессии
     * @return последний контекст сессии обмена сообщениями с чатом
     */
    public static SessionContext getSessionContext(String firstMessageText)
    {
        ModelMap messageFirst = SdDataUtils.findObjects(SystemClass.SYS_MESSAGE.getCode(),
                        ModelMap.newMap(SystemAttrEnum.TEXT.getCode(), firstMessageText))
                .stream()
                .max(Comparator.comparing(model -> model.get(SystemAttrEnum.CREATION_DATE.getCode())))
                .orElseThrow();
        ModelMap chat = SdDataUtils.getMapValue(messageFirst, SystemAttrEnum.CHAT.getCode());
        ModelMap session = SdDataUtils.getMapValue(messageFirst, SystemAttrEnum.DIALOG_SESSION.getCode());
        ModelMap dialog = SdDataUtils.getMapValue(
                SdDataUtils.getObjectByUUID(session.get(SystemAttrEnum.UUID.getCode()),
                        SystemClass.SYS_DIALOGSESSION.getCode()), SystemAttrEnum.DIALOG.getCode());

        SessionContext context = ModelFactory.create(SessionContext.class);
        context.setExists(true);
        context.setDialogUuid(dialog.get(SystemAttrEnum.UUID.getCode()));
        context.setChatUuid(chat.get(SystemAttrEnum.UUID.getCode()));
        context.setDialogSessionUuid(session.get(SystemAttrEnum.UUID.getCode()));
        context.setFirstMessageUuid(messageFirst.get(SystemAttrEnum.UUID.getCode()));
        return context;
    }

    /**
     * Подождать {@link DSLChatOnSite#WAITING_SEND_TIMEOUT_IN_MILLISECOND}. Метод необходимо использовать после
     * приема сообщения из чата и перед отправкой ответного сообщения.
     * Т.к. ncc измеряет время сообщения в секундах, то существует вероятность, что быстрый ответ (меньше секунды)
     * приведет к рассинхрону сообщений в чате.
     */
    public static void waitMessageSend()
    {
        WaitTool.waitMills(WAITING_SEND_TIMEOUT_IN_MILLISECOND);
    }

    /**
     * Ожидает открытия сессии шлюза для обмена сообщениями
     * @param firstMessageText текст первого сообщения в сессии
     * @param serialNumber ожидаемое кол-во сообщений с таким тестом в системе
     * @return контекст сессии
     */
    public static SessionContext waitSessionOpening(String firstMessageText, int serialNumber)
    {
        DSLMessage.waitMessageByText(firstMessageText, serialNumber);
        return getSessionContext(firstMessageText);
    }

    /**
     * Ожидает открытия сессии шлюза для обмена сообщениями
     * @return контекст сессии
     */
    public static SessionContext waitSessionOpening()
    {
        return waitSessionOpening(FIRST_MESSAGE_TEXT, 1);
    }
}