package ru.naumen.selenium.casesutil;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;

import org.junit.Assert;
import org.openqa.selenium.By;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebElement;

import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.Span;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListFastFilterForm;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BooleanType;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.context.CVConsts;
import ru.naumen.selenium.core.ActionToActiveElement;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.WebTester;
import ru.naumen.selenium.core.WebTester.ScrollAlignment;
import ru.naumen.selenium.core.exception.DialogErrorException;
import ru.naumen.selenium.util.StringUtils;

import java.util.Arrays;
import java.util.List;

import static java.util.stream.Collectors.toList;
import static ru.naumen.selenium.casesutil.GUIXpath.SpecificComplex.LAST_ELEMENT_PATTERN;

/**
 * Утилитарные методы для работы с формами через интерфейс
 * <AUTHOR>
 * @since 27.05.2013
 */
public class GUIForm extends CoreTester
{
    /** Путь до поля на форме привязки*/
    public static final String ANY_FIELD = "//div[@id='gwt-debug-%s-value']//input";
    /** Путь до блокирующего экрана на форме */
    private static final String X_FORM_BLOCKER = String.format(GUIXpath.Any.ANY, "form_blocker");

    /** Добавочные пути для поиска частей поля */
    // TODOAT Эти штуки надо бы вынести в более общий класс
    public static final String X_VALUE_INPUT = "//input";
    public static final String X_VALUE_LABEL = "//label";
    /** Добавочные проверки для полей checkbox и radio */
    public static final String X_HAS_CHECKED_INPUT = "[input[@checked]]";
    public static final String X_HAS_UNCHECKED_INPUT = "[input[not(@checked)]]";

    /*id-ки полей на форме привязки запроса(используются в xpath'ах)*/
    /** Поле "Соглашение/Услуга"*/
    public static final String AGREEMENT_SERVICE_FIELD = "agreementServiceProperty";
    public static final String CASE_FIELD = "caseProperty";

    /** Поле "Новый тип"*/
    public static final String NEW_CASE_FIELD = "newCaseProperty";

    /** Поле "Родитель"*/
    public static final String PARENT_FIELD = "destinationProperty";

    public static final String POPUP = "//div[@class='popupContent']";

    public static final String QUICK_ACTIONS_PANEL = "//div[@id='gwt-debug-%s.quickActions']";
    public static final String QUICK_ACTION_ADD = QUICK_ACTIONS_PANEL + "//a[@id='gwt-debug-quickAdd']";
    public static final String QUICK_ACTION_EDIT = QUICK_ACTIONS_PANEL + "//a[@id='gwt-debug-quickEdit']";
    private static final String FORM_XPATH = GUIXpath.Div.FORM_CONTAINS + "//*[contains(@id, 'caption')]";
    private static final String FORM_CAPTION = "//div[@class='form-window-title']";
    public static final String VERSION_BADGE = "Версия";
    public static final String ENVIRONMENT_BADGE = "Окружение";
    private static final String WRONG_FORM_MESSAGE_ERROR = "Полученное сообщение с подтверждением действия не совпало"
                                                           + " с ожидаемым.";

    /**
     * Нажать кнопку сохранить на форме и проверить, что форма закрылась
     * для модальных форм использовать {@link #applyModalForm()}
     */
    public static void applyForm()
    {
        clickApply();
        assertFormDisappear(GUIXpath.Div.FORM_CONTAINS);
    }

    /**
     * Нажать кнопку сохранить и проверить, что появилось диалоговое окно содержащее указанное сообщение
     * @param message текст сообщения
     */
    public static void applyFormAndAssertDialog(String message, Object... args)
    {
        clickApply();
        GUIForm.assertDialogContainsText(String.format(message, args));
    }

    /**
     * Нажать кнопку сохранить и проверить, что на форме появились указанные ошибки
     * @param messages текст сообщений
     */
    public static void applyFormAndAssertErrorAnyMessageOnForm(String... messages)
    {
        clickApply();
        GUIForm.assertErrorAnyMessageOnForm(messages);
    }

    /**
     * Нажать кнопку сохранить, проверить, что появилось предупреждение
     * @param message текст предупреждения, может иметь символы форматирования
     * @param args символы форматирования для message
     */
    public static void applyFormAssertAttention(String message, Object... args)
    {
        clickApply();
        assertFormAppear(GUIXpath.Any.ATTENTION);
        assertAttention(message, args);
    }

    /**
     * Нажать кнопку сохранить, проверить, что появилось заданное количество всплывающих сообщений
     * "Поле должно быть заполнено", форма не закрылась
     * @param xpath - xpath формы
     * @param count - количество всплывающих сообщений
     */
    public static void applyFormAssertCountPopup(String xpath, int count)
    {
        clickApply();
        List<WebElement> validationElements = tester.findElements(GUIXpath.Div.VALIDATION_TEXT_CONTAINS);
        int size = 0;
        for (WebElement el : validationElements)
        {
            if (!el.getText().isEmpty())
            {
                Assert.assertEquals(ConfirmMessages.VALIDATION_REQUIRED_FIELD, el.getText());
                size++;
            }
        }
        Assert.assertEquals("Количество всплывающих сообщений не совпало с ожидаемым.", count, size);
        Assert.assertTrue("Отсутствует форма. Xpath: " + xpath, tester.waitAppear(xpath));
    }

    /**
     * Нажать кнопку сохранить на форме и проверить что появилась ошибка
     * @param message текст ошибки
     */
    public static void applyFormAssertError(String message)
    {
        applyFormAssertError(GUIXpath.Any.APPLY_BUTTON, message);
    }

    /**
     * Нажать кнопку сохранить на форме и проверить что появилась ошибка
     * @param message текст ошибки
     * @param args символы форматирования для message
     */
    public static void applyFormAssertError(String buttonXpath, String message, Object... args)
    {
        String msg = String.format(message, args);
        try
        {
            tester.click(buttonXpath);
            GUIError.waitError(WaitTool.WAIT_TIME);
            Assert.fail(String.format("Ошибка \"%s\" не появилась.", msg));
        }
        catch (DialogErrorException e)
        {
            String assertMsg = String.format(
                    "Некорректное сообщение об ошибке при нажатии на кнопку Сохранить. %n [%s] не содержится в [%s]",
                    msg, e.getMessage());
            Assert.assertTrue(assertMsg, e.getMessage().contains(msg));
            GUIError.ignoreError();
        }
    }

    /**
     * Нажать кнопку сохранить на форме и ожидать ошибку и дополнительный информационный диалог
     * @param message текст ошибки
     * @param args символы форматирования для message
     */
    public static void applyFormAssertErrorAndDialog(String message, Object... args)
    {
        String msg = String.format(message, args);
        try
        {
            clickApply();
            GUIError.waitError(WaitTool.WAIT_TIME);
            Assert.fail(String.format("Ошибка \"%s\"не появилась.", msg));
        }
        catch (DialogErrorException e)
        {
            String assertMsg = String.format(
                    "Некорректное сообщение об ошибке при нажатии на кнопку Сохранить. %n [%s] не содержится в [%s]",
                    msg, e.getMessage());
            Assert.assertTrue(assertMsg, e.getMessage().contains(msg));
            // Закрываем дополнительный диалог
            tester.clickWithoutErrorCheck(GUIXpath.Div.OK);
            GUIError.ignoreError();
        }
    }

    /**
     * Нажать кнопку сохранить, проверить, что появилось сообщение
     * @param message текст сообщение, может иметь символы форматирования
     * @param args символы форматирования для message
     */
    public static void applyFormAssertMessage(String message, Object... args)
    {
        clickApply();
        assertFormAppear(GUIXpath.Div.DIALOG_WIDGET_HEAD);
        assertMessage(message, args);
    }

    /**
     * Нажать кнопку сохранить, проверить, что появилось сообщение об ошибке
     * @param attr проверяемый атрибут
     * @param message ожидаемый текст ошибки
     */
    public static void applyFormAssertValidation(Attribute attr, String message)
    {
        clickApply();
        GUIValidation.assertValidation(attr.getCode(), message);
    }

    /**
     * Нажать кнопку сохранить, проверить, что появилось сообщение "Поле должно быть заполнено",
     * форма не закрылась
     * @param xpath формы
     */
    public static void applyFormAssertValidation(String xpath)
    {
        applyFormAssertValidation(xpath, ConfirmMessages.VALIDATION_REQUIRED_FIELD);
    }

    /**
     * Нажать кнопку сохранить, проверить, что появилось сообщение с заданным текстом, форма не закрылась
     * @param xpath формы
     * @param message сообщение окна
     */
    public static void applyFormAssertValidation(String xpath, String message)
    {
        clickApply();
        assertValidationPresent(message);
        assertFormAppear(xpath);
    }

    /**
     * Нажать кнопку сохранить на форме и проверить, что форма не закрылась
     */
    public static void applyFormWithValidationErrors()
    {
        clickApply();
        assertDialogAppear("Форма закрылась");
    }

    /**
     * Нажать кнопку "OK" на информационном диалоге и проверить что он закрылся
     */
    public static void applyInfoDialog()
    {
        tester.click(GUIXpath.Div.INFO_DIALOG + GUIXpath.Div.OK);
        assertInfoDialogDisappear("Форма с подтверждением не закрылась");
    }

    /**
     * Нажать кнопку на иформационном диалоге
     * @param buttonXpath xpath кнопки
     */
    public static void applyInfoDialog(String buttonXpath)
    {
        tester.click(GUIXpath.Div.INFO_DIALOG + buttonXpath);
        assertInfoDialogDisappear("Форма с подтверждением не закрылась");
    }

    /**
     * Нажимает на кнопку "Сохранить" в последнем открытом диалоговом окне и проверяет, что оно закрылось
     * (ИСПОЛЬЗОВАТЬ ТОЛЬКО ТОГДА, КОГДА ОТКРЫТО НЕСКОЛЬКО МОДАЛЬНЫХ ФОРМ)
     */
    public static void applyLastModalForm()
    {
        String formPath = Div.PROPERTY_DIALOG_BOX + "[" + tester.getElements(Div.PROPERTY_DIALOG_BOX).size() + "]";
        tester.click(formPath + GUIXpath.Any.APPLY_BUTTON);
        GUITester.assertAbsent(formPath, "Форма не закрылась.");
    }

    /**
     * Нажать сохранить на модальной форме и проверить, что она закрылась
     */
    public static void applyModalForm()
    {
        tester.click(Div.PROPERTY_DIALOG_BOX + Any.APPLY_BUTTON);
        assertDialogDisappear("Модальная форма не закрылась");
    }

    /**
     * Нажать кнопку сохранить на модальной форме и подтвердить действие, если возникает такая необходимость.
     * Затем проверить, что форма закрылась
     */
    public static void applyModalFormWithConfirmIfNeed()
    {
        tester.click(Div.PROPERTY_DIALOG_BOX + Any.APPLY_BUTTON);
        if (tester.waitAppear(1, GUIXpath.Complex.QUESTION_DIALOG_BTN_YES))
        {
            clickYes();
        }
        assertDialogDisappear("Модальная форма не закрылась");
    }

    /**
     * Нажать кнопку сохранить на модальной форме и проверить, что она закрылась.
     *
     * @param timeout - время, в течение которого форма должна закрыться
     */
    public static void applyModalForm(long timeout)
    {
        tester.click(Div.PROPERTY_DIALOG_BOX + Any.APPLY_BUTTON);
        Assert.assertTrue(tester.waitDisappear(timeout, Div.PROPERTY_DIALOG_BOX));
    }

    /**
     * Нажать кнопку "OK" на форме с подтверждением/предупреждением и проверить что диалоговое окно закрылось
     */
    public static void applyQuestion()
    {
        tester.click(GUIXpath.Div.OK);
        assertQuestionDisappear("Форма с подтверждением/предупреждением не закрылась");
    }

    /**
     * Проверить отсутствие полей на форме
     *
     * @param absenceFields перечень полей, отсутсвие которых необходимо проверить
     */
    public static void assertAbsenceFields(String... absenceFields)
    {
        for (String field : absenceFields)
        {
            Assert.assertTrue(String.format("На форме присутствует поле с кодом: '%s'", field),
                    tester.waitDisappear(ANY_FIELD, field));
        }
    }

    /**
     * Проверить что кнопка "Сохранить" активна
     */
    public static void assertApplyBtnEnabled(boolean enabled)
    {
        assertBtnEnabled(enabled, "Сохранить", GUIXpath.Any.APPLY_BUTTON);
    }

    /**
     * Проверка сообщения в появившемся предупреждении
     *
     * @param expectedMessage ожидаемое сообщение, может иметь символы форматирования
     * @param args            параметры для форматированного expectedMessage, могут отсутствовать
     */
    public static void assertAttention(String expectedMessage, Object... args)
    {
        String actualMessage = tester.findDisplayed(GUIXpath.Any.ATTENTION).getText();
        Assert.assertEquals("Полученное значение предупреждения не совпало с ожидаемым.",
                String.format(expectedMessage, args), actualMessage);
    }

    /**
     * Проверить отсутствие предупреждения на странице
     */
    public static void assertAttentionAbsence()
    {
        Assert.assertTrue("На странице присутствует предупреждение.", tester.waitDisappear(GUIXpath.Any.ATTENTION));
    }

    /**
     * Проверить отсутствие кнопки "Сбросить" в предупреждении на странице
     */
    public static void assertAttentionButtonAbsence()
    {
        Assert.assertTrue("На странице присутствует кнопка Сбросить в предупреждении.",
                tester.waitDisappear(GUIXpath.Complex.ATTENTION_BUTTON));
    }

    /**
     * Проверка вхождения сообщения в появившемся предупреждении
     *
     * @param expectedMessage ожидаемое сообщение, может иметь символы форматирования
     * @param args            параметры для форматированного expectedMessage, могут отсутствовать
     */
    public static void assertAttentionContains(String expectedMessage, Object... args)
    {
        String actualMessage = tester.findDisplayed(GUIXpath.Any.ATTENTION).getText();
        Assert.assertTrue("Полученное значение предупреждения не содержит ожидаемое.",
                actualMessage.contains(String.format(expectedMessage, args)));
    }

    /**
     * Проверяет отсутствие указанных атрибутов на форме редактирования/добавления
     *
     * @param attributes перечень проверяемых атрибутов
     */
    public static void assertAttrAbsence(Attribute... attributes)
    {
        String msg = "Атрибут '%s' присутствует на форме редактирования/добавления.";
        for (Attribute attr : attributes)
        {
            Assert.assertTrue(String.format(msg, attr.getTitle()),
                    tester.waitDisappear(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, attr.getCode()));
        }
    }

    /**
     * Проверить доступен ли атрибут для редактирования на форме
     *
     * @param enabled true - проверить на доступность, false - на недоступность
     * @param attr    модель проверяемого атрибута
     */
    public static void assertAttrEnabled(boolean enabled, Attribute attr)
    {
        String disabledAttr = tester.find(ANY_FIELD, attr.getCode()).getAttribute("disabled");
        if (enabled)
        {
            Assert.assertNull("Атрибут '" + attr.getTitle() + "' не доступен для редактирования.", disabledAttr);
        }
        else
        {
            Assert.assertEquals("Атрибут '" + attr.getTitle() + "' доступен для редактирования.",
                    Boolean.TRUE.toString(), disabledAttr);
        }
    }

    /**
     * Проверить строковый атрибут на форме редактирования/добавления
     *
     * @param attribute модель атрибута
     * @param expected  ожидаемое значение атрибута
     */
    public static void assertAttribute(Attribute attribute, String expected)
    {
        GUITester.assertValue(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, expected, attribute.getCode());
    }

    /**
     * На форме проверить название атрибута
     *
     * @param attrs набор моделей атрибутов
     */
    public static void assertAttributeCaption(Attribute... attrs)
    {
        for (Attribute attr : attrs)
        {
            String msg = "На форме отсутствует атрибут с названием: " + attr.getTitle();
            GUITester.assertTextContainsWithMsg(GUIXpath.Any.ANY_CAPTION, attr.getTitle(), msg, attr.getCode());
        }
    }

    /**
     * Проверяет навзвание атрибута на форме
     */
    public static void assertAttributeCaption(String attributeCode, String attributeCaption)
    {
        String msg = "На форме отсутствует атрибут с названием: " + attributeCaption;
        GUITester.assertTextContainsWithMsg(GUIXpath.Any.ANY_CAPTION, attributeCaption, msg, attributeCode);
    }

    /**
     * На форме проверить описания атрибутов
     *
     * @param attrs набор моделей атрибутов
     */
    public static void assertAttributeDescription(Attribute... attrs)
    {
        for (Attribute attr : attrs)
        {
            String msg = String.format("На форме отсутствует атрибут '%s' с описанием '%s'", attr.getTitle(),
                    attr.getDescription());
            GUITester.assertTextContainsWithMsg(GUIContent.X_ATTR_DESCRIPTION_PATTERN, attr.getDescription(), msg,
                    attr.getCode());
        }
    }

    /**
     * Проверить строковый атрибут на форме редактирования/добавления
     *
     * @param attribute модель атрибута
     * @param expected  ожидаемое значение атрибута
     */
    public static void assertAttributeValue(Attribute attribute, String expected)
    {
        GUITester.assertTextPresent(GUIXpath.Any.ANY_VALUE, expected, attribute.getCode());
    }

    /**
     * Проверить наличие иконки в атрибуте на форме
     * @param attr модель атрибута
     * @param iconCode код иконки
     */
    public static void assertAttributeIconPresent(Attribute attr, String iconCode)
    {
        String msg = "Иконка '%s' отсутствует в атрибуте '%s'";
        Assert.assertTrue(String.format(msg, iconCode, attr.getTitle()),
                tester.waitAppear(GUIXpath.Any.ANY_VALUE + Span.ANY_ICON, attr.getCode(), iconCode));
    }

    /**
     * Проверить отсутствие иконки в атрибуте на форме
     * @param attr модель атрибута
     * @param iconCode код иконки
     */
    public static void assertAttributeIconAbsent(Attribute attr, String iconCode)
    {
        String msg = "Иконка '%s' присутствует в атрибуте '%s'";
        Assert.assertTrue(String.format(msg, iconCode, attr.getTitle()),
                tester.waitDisappear(GUIXpath.Any.ANY_VALUE + Span.ANY_ICON, attr.getCode(), iconCode));
    }

    /**
     * Проверить значение атрибута "ссылка на БО" на форме редактирования/добавления
     *
     * @param attribute модель атрибута
     * @param expected  ожидаемое значение атрибута
     */
    public static void assertObjectLinkAttributeValue(Attribute attribute, String expected)
    {
        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, attribute.getCode()),
                null == expected ? "[не указано]" : expected);
    }

    /**
     * Проверить значение атрибута "ссылка на БО" на форме редактирования/добавления
     * @param attribute модель атрибута
     * @param expected ожидаемое значение атрибута
     */
    public static void assertObjectLinkAttributeValue(Attribute attribute, Bo expected)
    {
        assertObjectLinkAttributeValue(attribute, expected.getTitle());
    }

    /**
     * Проверить порядок размещения атрибутов на форме
     *
     * @param attributes - модели атрибутов в том порядке в котором они должны быть размещены
     */
    public static void assertAttrOrder(Attribute... attributes)
    {
        //@formatter:off
        List<String> expected = Arrays.stream(attributes)
                .map(Attribute::getCode)
                .map(code -> String.format("gwt-debug-%s-", code))
                .collect(toList());
        //@formatter:on

        //@formatter:off
        List<String> actual = tester.findDisplayedElements(GUIXpath.Div.FORM_CONTAINS + "//tr/td//*[contains(@id, 'value')]")
                .stream().map(e -> e.getAttribute("id"))
                .filter(id -> id != null && id.endsWith("value"))
                .collect(toList());
        //@formatter:on

        Assert.assertEquals("Действительное количество атрибутов отличается от ожидаемого", expected.size(),
                actual.size());

        for (int i = 0; i < expected.size(); ++i)
        {
            Assert.assertTrue("Порядок атрибутов отличается от ожидаемого", actual.get(i).startsWith(expected.get(i)));
        }
    }

    /**
     * Проверяет присутствие указанных атрибутов на форме редактирования/добавления
     * @param attributes перечень проверяемых атрибутов
     */
    public static void assertAttrPresent(Attribute... attributes)
    {
        String msg = "Атрибут '%s' отсутствует на форме редактирования/добавления.";
        for (Attribute attr : attributes)
        {
            if (BooleanType.CODE.equals(attr.getType()))
            {
                Assert.assertTrue(String.format(msg, attr.getTitle()),
                        tester.waitAppear(GUIXpath.Any.ANY_VALUE_CONTAINS, attr.getCode()));
            }
            else
            {
                Assert.assertTrue(String.format(msg, attr.getTitle()),
                        tester.waitAppear(GUIXpath.Any.ANY_VALUE, attr.getCode()));
            }

        }
    }

    /**
     * На форме проверить название логического атрибута
     * @param attr набор моделей атрибутов
     */
    public static void assertBooleanAttributeCaption(Attribute attr)
    {
        String msg = "На форме отсутствует логический атрибут с названием: " + attr.getTitle();
        GUITester.assertTextContainsWithMsg(GUIXpath.Any.ANY_VALUE_LABEL_CONTAINS, attr.getTitle(), msg,
                attr.getCode());
    }

    /**
     * Проверить, что кнопка активна/неактивна
     * @param enabled true - проверить что кнопка активна, false - неактивна
     * @param buttonName название кнопки
     * @param xpath xpath кнопки
     * @param args аргументы для формирования xpath
     */
    public static void assertBtnEnabled(boolean enabled, String buttonName, String xpath, Object... args)
    {
        String disabledAttr = tester.find(xpath, args).getAttribute("disabled");
        if (enabled)
        {
            Assert.assertNull("Кнопка '" + buttonName + "' не активна.", disabledAttr);
        }
        else
        {
            Assert.assertEquals("Кнопка '" + buttonName + "' активна.",
                    Boolean.TRUE.toString(), disabledAttr);
        }
    }

    /**
     * Проверить доступность/недоступность кнопкок сохранения и отмены на форме
     */
    public static void assertButtonsEnabled(boolean enabled)
    {
        String disabledClass = "buttonDisabled";
        String formBlockerId = "form_blocker";
        if (enabled)
        {
            Assert.assertTrue("Кнопки недоступны!",
                    !tester.isPresence(Div.PROPERTY_DIALOG_BOX + Div.BUTTONS + Div.ANY_CLASS_CONTAINS,
                            disabledClass) && !tester.isPresence(Div.ANY, formBlockerId));
        }
        else
        {
            Assert.assertTrue("Кнопки доступны!",
                    tester.isPresence(Div.PROPERTY_DIALOG_BOX + Div.BUTTONS + Div.ANY_CLASS_CONTAINS,
                            disabledClass) || tester.isPresence(Div.ANY, formBlockerId));
        }
    }

    /**
     *  Проверяет, что кнопка «Сохранить» остается некликабельной.
     */
    public static void assertApplyButtonStaysNonClickable()
    {
        try
        {
            Assert.assertNull("Кнопка стала доступна для нажатия.",
                    WaitTool.waitToBeClickable(tester.getWebDriver(), By.xpath(GUIXpath.Any.APPLY_BUTTON)));
        }
        catch (TimeoutException e) //NOPMD
        {
            // Кнопка осталась заблокированной либо была отпилена полностью.
        }
    }

    /**
     * Проверить что кнопка "Отмена" активна
     */
    public static void assertCancelBtnEnabled(boolean enabled)
    {
        assertBtnEnabled(enabled, "Отмена", GUIXpath.Div.CANCEL);
    }

    /**
     * Проверить активность указанных кнопок на форме
     *
     * @param xpath xPath для поиска кнопки
     * @param codes коды кнопок
     */
    public static void assertButtonsEnabled(String xpath, String... codes)
    {
        for (String code : codes)
        {
            String disabledAttr = tester.find(xpath, code).getAttribute("disabled");
            Assert.assertNull("Кнопка '" + code + "' не активна.", disabledAttr);
        }
    }

    /**
     * Проверить отсутствие указанных кнопок на форме
     *
     * @param xpath xPath для поиска кнопки
     * @param codes коды кнопок
     */
    public static void assertButtonsAbsence(String xpath, String... codes)
    {
        for (String code : codes)
        {
            Assert.assertFalse("Кнопка '" + code + "' присутствует.", tester.isPresence(xpath, code));
        }
    }

    /**
     * Проверка расположения по центру модального окна
     */
    public static void assertCenteredDialogBox()
    {
        int halfWidthOuter = tester.findDisplayed(GUIXpath.Other.MAIN_WIN).getSize().getWidth() / 2;
        WebElement dialog = tester.findDisplayed(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        int actualLeft = halfWidthOuter - dialog.getSize().getWidth() / 2;
        int expectedLeft = Integer.parseInt(dialog.getCssValue("left").replace("px", ""));
        Assert.assertEquals("Модальное окно расположено не по центру", actualLeft, expectedLeft);
    }

    /**
     * Проверить значение чекбокса для атрибута на модальной форме
     * @param boolAttribute атрибут
     * @param checked ожидаемой значение чекбокса
     */
    public static void assertCheckBoxAttributeOnModalForm(Attribute boolAttribute, boolean checked)
    {
        GUITester.assertCheckboxState(
                String.format(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + Any.ANY_VALUE_INPUT_CONTAINS,
                        boolAttribute.getCode()), checked);
    }

    /**
     * Проверить значение чекбокса для атрибута на модальной форме
     * @param code - код атрибута
     * @param checked - ожидаемой значение чекбокса
     */
    public static void assertCheckBoxAttributeOnModalForm(String code, boolean checked)
    {
        GUITester.assertCheckboxState(
                String.format(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + Any.ANY_VALUE_INPUT_CONTAINS, code), checked);
    }

    /**
     * Проверяет, что появилось диалоговое окно с указанным сообщением
     * @param message сообщение в диалоговом окне
     */
    public static void assertDialog(String message)
    {
        GUITester.assertTextPresentWithMsg(GUIXpath.Any.DIALOG_WIDGET_DESCRIPTION_ELEMENT, message,
                "Полученное сообщение не совпало с ожидаемым.");
    }

    /**
     * Проверяем, что диалоговое окно отображается
     * @param message сообщение которое необходимо передать при ошибке появления диалогового окна
     */
    public static void assertDialogAppear(String message)
    {
        Assert.assertTrue(message, tester.waitAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION));
    }

    /**
     * Проверить, что диалоговое окно с указанным id появилось
     * @param debugId id диалогового окна
     */
    public static void assertDialogAppearById(String debugId, String title)
    {
        GUITester.assertTextPresent(Any.ANY_CAPTION, title, debugId);
    }

    /**
     * Проверить заголовок диалогового окна
     * @param expected ожидаемый заголовок
     */
    public static void assertDialogCaption(String expected)
    {
        GUITester.assertTextPresentWithMsg(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION, expected,
                "Полученный заголовок диалогового окна не совпал с ожидаемым.");
    }

    /**
     * Проверить наличие текста в диалоговом окне
     * @param expected ожидаемый текст
     */
    public static void assertDialogContainsText(String expected)
    {
        GUITester.assertTextContains(GUIXpath.Div.DIALOG_WIDGET_DESCRIPTION_ELEMENT, expected);
    }

    /**
     * Проверяем, что диалоговое окно исчезло
     * @param message сообщение которое необходимо передать при ошибке исчезновения диалогового окна
     */
    public static void assertDialogDisappear(String message)
    {
        Assert.assertTrue(message, tester.waitDisappear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION));
    }

    /**
     * Проверить количество модельных окон, открытых на странице
     *
     * @param expected - ожидаемое количество
     */
    public static void assertDialogsCount(int expected)
    {
        int count = tester.findDisplayedElements(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION).size();
        Assert.assertEquals("Количество открытых модальных форм не совпало с ожидаемым", expected, count);

    }

    /**
     * Проверяет, что текст бейджа в названии диалогового окна соответствует 'Версия'
     */
    public static void assertDialogVersionBadge()
    {
        GUITester.assertPresent(Div.PROPERTY_DIALOG_BOX_CAPTION_BADGE,
                "Фактический бейдж не совпал с ожидаемым: '" + VERSION_BADGE + "'", VERSION_BADGE);
    }

    /**
     * Проверить появилось ли одно из сообщений об ошибке
     * @param expectedMessages ожидаемые сообщения об ошибке
     */
    public static void assertErrorAnyMessageOnForm(String... expectedMessages)
    {
        try
        {
            GUIError.waitError(WaitTool.WAIT_TIME);
            Assert.fail("Ошибка не появилась.");
        }
        catch (DialogErrorException e)
        {
            boolean assertAnyMessage = false;
            for (String message : expectedMessages)
            {
                if (e.getMessage().contains(message))
                {
                    assertAnyMessage = true;
                    break;
                }
            }

            String expectedVairants = StringUtils.join(Lists.newArrayList(expectedMessages), "\n");
            Assert.assertTrue("Полученное сообщение об ошибке не совпало ни с 1 из ожидаемых:\n" + expectedVairants,
                    assertAnyMessage);
            GUIError.ignoreError();
        }
    }

    /**
     * Проверить появилось сообщение об ошибке
     */
    public static void assertErrorMessageOnForm()
    {
        try
        {
            GUIError.waitError(WaitTool.WAIT_TIME);
            Assert.fail("Ошибка не появилась.");
        }
        catch (DialogErrorException e)
        {
            GUIError.ignoreError();
        }
    }

    /**
     * Проверить появилось ли сообщение об ошибке
     * @param expectedMessage ожидаемое сообщение об ошибке
     */
    public static void assertErrorMessageOnForm(String expectedMessage)
    {
        try
        {
            GUIError.waitError(WaitTool.WAIT_TIME);
            Assert.fail("Ошибка не появилась.");
        }
        catch (DialogErrorException e)
        {
            Assert.assertEquals("Полученное сообщение об ошибке не совпало с ожидаемым.", expectedMessage,
                    e.getMessage());
            GUIError.ignoreError();
        }
    }

    /**
     * Проверяет ошибки вида "Поле должно быть заполнено" на форме
     * @param errosCount количество ошибок
     */
    public static void assertErrorsOfEmptyFieldsOnForm(int errosCount)
    {
        List<WebElement> validationElements = tester.findElements(GUIXpath.Div.VALIDATION_TEXT_CONTAINS);
        int size = 0;
        for (WebElement el : validationElements)
        {
            if (!el.getText().isEmpty())
            {
                Assert.assertEquals(ConfirmMessages.VALIDATION_REQUIRED_FIELD, el.getText());
                size++;
            }
        }
        Assert.assertEquals(errosCount, size);
    }

    /**
     * Проверка цвета шрифта
     * @param expectedColor ожидаемое значение цвета шрифта
     * @param xpathElements пути к элементам для проверки
     */
    public static void assertFontColor(String expectedColor, String... xpathElements)
    {
        for (String element : xpathElements)
        {
            String actualColor = GUITester.waitCssValue(element, "color", expectedColor);
            Assert.assertEquals("Неправильный цвет шрифта", expectedColor, actualColor);
        }
    }

    /**
     * Проверяем, что форма появилась
     */
    public static void assertFormAppear()
    {
        assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
    }

    /**
     * Проверяем, что форма появилась
     */
    public static void assertFormAppear(String xpath)
    {
        Assert.assertTrue("Отсутствует форма.", tester.waitAppear(xpath));
    }

    /**
     * Проверить отсутствие блокирующего экрана на форме
     */
    public static void assertFormBlockerAbsence()
    {
        assertFormBlockerExists(false, 2000);
    }

    /**
     * Проверить присутствие блокирующего экрана на форме
     *
     * @param exists присутствует/отсутствует (true/false)
     * @param wait заданное количество миллисекунд для ожидания
     */
    public static void assertFormBlockerExists(boolean exists, long wait)
    {
        WaitTool.waitMills(wait);
        Assert.assertEquals(!exists, tester.getElements(X_FORM_BLOCKER).isEmpty());
    }

    /**
     * Проверяем, что форма исчезла
     */
    public static void assertFormDisappear(String xpath)
    {
        Assert.assertTrue("Форма не исчезла.", tester.waitDisappear(xpath));
    }

    /**
     * Проверить название формы
     * @param expected ожидаемое название
     */
    public static void assertFormTitle(String expected)
    {
        GUIBo.assertBoTitle(expected);
    }

    /**
     * Проверить горизонтальный размер формы, ожидает в течение времени ожидания ({@link WaitTool#WAIT_TIME})
     * @param expected ожидаемый размер
     */
    public static void assertFormWidth(final int expected)
    {
        Boolean sameWidth = WaitTool.waitSomething(tester, input ->
        {
            WebElement form = input.findDisplayed(GUIXpath.Div.PROPERTY_DIALOG_BOX);
            int actualWidth = form.getSize().getWidth();
            return actualWidth == expected;
        });
        String msg = "Горизонтальный размер формы не соответствует ожидаемому";
        Assert.assertTrue(msg, sameWidth);
    }

    /**
     * Проверить вертикальный размер тела диалогового окна.
     * Ожидает в течение времени ожидания ({@link WaitTool#WAIT_TIME}).
     *
     * @param expectedHeight ожидаемая высота тела диалогового окна
     */
    public static void asserDialogBodyHeight(final int expectedHeight)
    {
        Boolean sameHeight = WaitTool.waitSomething(tester, input ->
        {
            WebElement form = input.findDisplayed(GUIXpath.Any.DIALOG_WIDGET_BODY);
            int actualHeight = form.getSize().getHeight();
            return actualHeight == expectedHeight;
        });
        Assert.assertEquals("Вертикальный размер тела диалогового окна не соответствует ожидаемому",
                Boolean.TRUE, sameHeight);
    }

    /**
     * Проверить, что активно "стекло", закрывающее основную часть приложения при открытом модальном окне
     */
    public static void assertGlassEnabled()
    {
        Assert.assertTrue(tester.waitForDisplayed("//div[contains(@class, 'b-lightbox-form__darkening')]", false));
    }

    /**
     * Проверяем, что появилось информационное диалоговое окно с указанным сообщением
     * @param message сообщение на диалоговом окне
     */
    public static void assertInfoDialog(String message)
    {
        assertInfoDialog(message, WaitTool.WAIT_TIME);
    }

    /**
     * Проверяем, что появилось информационное диалоговое окно с указанным сообщением
     * @param message сообщение на диалоговом окне
     * @param timeout таймаут ожидания в секундах
     */
    public static void assertInfoDialog(String message, long timeout)
    {
        assertInfoDialogAppear("Информационное диалоговое окно не появилось", timeout);
        GUITester.assertTextPresentWithMsg(GUIXpath.Any.DIALOG_WIDGET_DESCRIPTION_ELEMENT, message,
                "Полученное сообщение не совпало с ожидаемым.");
    }

    /**
     * Проверяем, что появилось информационное диалоговое окно
     * @param expectedPattern ожидаемый текст сообщения(может иметь символы форматирования)
     * @param args параметры форматирования
     */
    public static void assertInfoDialog(String expectedPattern, Object... args)
    {
        String expected = String.format(expectedPattern, args);
        Assert.assertEquals(WRONG_FORM_MESSAGE_ERROR, expected,
                tester.getText(Div.INFO_DIALOG + GUIXpath.Any.DIALOG_WIDGET_BODY));
    }

    /**
     * Проверяем, что информационное диалоговое окно появилось
     * @param message сообщение которое необходимо передать при ошибке появления диалогового окна
     */
    public static void assertInfoDialogAppear(String message)
    {
        assertInfoDialogAppear(message, WaitTool.WAIT_TIME);
    }

    /**
     * Проверяем, что информационное диалоговое окно появилось
     * @param message сообщение, которое необходимо передать при ошибке появления диалогового окна
     * @param timeout таймаут ожидания в секундах
     */
    public static void assertInfoDialogAppear(String message, long timeout)
    {
        Assert.assertTrue(message, tester.waitAppear(timeout, GUIXpath.Div.INFO_DIALOG));
    }

    /**
     * Проверяем, что диалоговое окно исчезло
     * @param message сообщение которое необходимо передать при ошибке исчезновения диалогового окна
     */
    public static void assertInfoDialogDisappear(String message)
    {
        Assert.assertTrue(message, tester.waitDisappear(GUIXpath.Div.INFO_DIALOG));
    }

    /**
     * Проверить название формы с информационным сообщением
     * @param expected ожидаемое название
     */
    public static void assertInfoDialogTitle(String expected)
    {
        Assert.assertTrue("Отсутствует форма с заголовком: " + expected,
                tester.waitAppear(GUIXpath.Complex.INFO_DIALOG_TITLE, expected));
    }

    /**
     * Проверяет присутствие указанных атрибутов на форме массового редактирования
     * @param blockFqn FQN класса/типа для блока формы массового редактирования
     * @param attributes перечень проверяемых атрибутов
     */
    public static void assertMassAttrPresent(String blockFqn, Attribute... attributes)
    {
        String msg = "Атрибут '%s' отсутствует на форме массового редактирования.";
        for (Attribute attr : attributes)
        {
            String attrCode = blockFqn + '@' + attr.getCode();
            if (BooleanType.CODE.equals(attr.getType()))
            {
                Assert.assertTrue(String.format(msg, attr.getTitle()),
                        tester.waitAppear(GUIXpath.Any.ANY_VALUE_CONTAINS, attrCode));
            }
            else
            {
                Assert.assertTrue(String.format(msg, attr.getTitle()),
                        tester.waitAppear(GUIXpath.Any.ANY_VALUE, attrCode));
            }
        }
    }

    /**
     * Проверка сообщения в появившемся предупреждении
     * @param expectedMessage ожидаемое сообщение, может иметь символы форматирования
     * @param args параметры для форматированного expectedMessage, могут отсутствовать
     */
    public static void assertMessage(String expectedMessage, Object... args)
    {
        String actualMessage = tester.findDisplayed(GUIXpath.Any.DIALOG_WIDGET_BODY).getText();
        Assert.assertEquals("Полученное значение предупреждения не совпало с ожидаемым.",
                String.format(expectedMessage, args), actualMessage);
        applyInfoDialog();
    }

    /**
     * Проверяет, что модальное окно свёрнуто
     */
    public static void assertModalFormMinimized()
    {
        Assert.assertTrue(tester.waitForDisplayed(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION, false));
        Assert.assertTrue(tester.waitForNotDisplayed(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT, false));
    }

    /**
     * Проверяет, что модальное окно нормально развёрнуто
     */
    public static void assertModalFormNormal()
    {
        Assert.assertTrue(tester.waitForDisplayed(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION, false));
        Assert.assertTrue(tester.waitForDisplayed(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT, false));
    }

    /**
     * Проверить название модальной формы
     * @param expected ожидаемое название
     */
    public static void assertModalFormTitle(String expected)
    {
        assertTopmostModalFormTitle(expected, false);
    }

    /**
     * Проверяем, что окно подтверждением действия появилось
     * @param expectedPattern ожидаемый текст подтверждения(может иметь символы форматирования)
     * @param args параметры форматирования
     */
    public static void assertQuestion(String expectedPattern, Object... args)
    {
        String expected = String.format(expectedPattern, args);
        Assert.assertEquals(WRONG_FORM_MESSAGE_ERROR, expected,
                tester.getText(GUIXpath.Any.QUESTION_DIALOG + GUIXpath.Any.DIALOG_WIDGET_BODY));
    }

    /**
     * Проверяем, что окно с подтверждением действия отсутствует
     */
    public static void assertQuestionAbsence()
    {
        String message = "Присутствует форма с подтверждением действия";
        Assert.assertTrue(message, tester.waitDisappear(GUIXpath.Any.QUESTION_DIALOG));
    }

    /**
     * Проверяем, что окно подтверждением действия появилось и соглашаемся
     * @param message текст в окне подтверждения
     * @param params - параметры для форматирования сообщения
     */
    public static void assertQuestionAndApply(String message, String... params)
    {
        GUIForm.assertQuestion(message, params);
        confirmByYes();
    }

    /**
     * Проверяем, что окно вопроса появилось
     * @param message сообщение которое необходимо передать при ошибке появления формы вопроса
     */
    public static void assertQuestionAppear(String message)
    {
        Assert.assertTrue(message, tester.waitAppear(GUIXpath.Any.QUESTION_DIALOG));
    }

    /**
     * Проверить название формы с проверочным сообщением
     * @param expected ожидаемое название
     */
    public static void assertQuestionDialogTitle(String expected)
    {
        Assert.assertTrue("Отсутствует форма с заголовком: " + expected,
                tester.waitAppear(GUIXpath.Complex.QUESTION_DIALOG_TITLE, expected));
    }

    /**
     * Проверяем, что форма вопроса исчезла
     * @param message сообщение которое необходимо передать при ошибке исчезновения формы вопроса
     */
    public static void assertQuestionDisappear(String message)
    {
        Assert.assertTrue(message, tester.waitDisappear(GUIXpath.Any.QUESTION_DIALOG));
    }

    /**
     * Проверить наличие кнопки "Очистить значение во всех выбранных объектах" в атрибуте
     * @param attr модель атрибута
     * @param exists true - кнопка присутствует, false - иначе
     */
    public static void assertQuickActionClearExists(Attribute attr, boolean exists)
    {
        tester.moveTo(GUIXpath.Any.ANY_VALUE, attr.getCode());
        GUITester.assertExists(String.format(GUIForm.QUICK_ACTIONS_PANEL + GUIXpath.Span.CLEAR_ICON, attr.getCode()),
                exists);
    }

    /**
     * Проверить наличие кнопки "Отменить редактирование атрибута на форме" в атрибуте
     * @param attr модель атрибута
     * @param exists true - кнопка присутствует, false - иначе
     */
    public static void assertQuickActionRefreshExists(Attribute attr, boolean exists)
    {
        tester.moveTo(GUIXpath.Any.ANY_VALUE, attr.getCode());
        GUITester.assertExists(String.format(GUIForm.QUICK_ACTIONS_PANEL + GUIXpath.Span.REFRESH_ICON, attr.getCode()),
                exists);
    }

    /**
     * Навести курсор на атрибут и проверить присутствие кнопки быстрого добавления
     * @param attribute атрибут для наведения
     * */
    public static void assertQuickAddButtonPresent(Attribute attribute)
    {
        tester.moveMouse(GUIXpath.InputComplex.ANY_VALUE, 5, -5, attribute.getCode());
        String msg = "Для атрибута '%s' отсутствует кнопка быстрого добавления.";
        Assert.assertTrue(String.format(msg, attribute.getTitle()), tester.waitAppear(GUIForm.QUICK_ACTION_ADD,
                attribute.getCode()));
    }

    /**
     * Проверить отсутствие кнопки "Восстановить" на форме
     */
    public static void assertRestoreValuesAbsence()
    {
        Assert.assertTrue("На странице присутствует кнопка Восстановить.",
                tester.waitDisappear(GUIXpath.SpecificComplex.RESTORE_VALUES_BUTTON));
    }

    /**
     * Проверить присутствие кнопки "Восстановить" на форме
     */
    public static void assertRestoreValuesPresence()
    {
        Assert.assertTrue("На странице отсутствует кнопка Восстановить.",
                tester.waitAppear(GUIXpath.SpecificComplex.RESTORE_VALUES_BUTTON));
    }

    /**
     * Проверить появилось ли сообщение об ошибке, в котором могут присутствовать подстроки в
     * случайном порядке через разделитель
     * @param expectedMessage ожидаемое сообщение об ошибке
     * @param sep разделитель
     * @param args список параметров, порядок которых не известен, присутствующих в проверяемой строке через разделитель
     */
    public static void assertShuffleErrorMessageOnForm(String expectedMessage, String sep, Object... args)
    {
        assertShuffleErrorMessageWithOrWithoutNumbers(expectedMessage, sep, false, args);
    }

    /**
     * Проверить появилось ли сообщение об ошибке, в котором могут присутствовать подстроки в
     * случайном порядке с нумерацией через разделитель
     * @param expectedMessage ожидаемое сообщение об ошибке
     * @param sep разделитель
     * @param args список параметров, порядок которых не известен, присутствующих в проверяемой строке через разделитель
     */
    public static void assertShuffleErrorMessageWithNumbers(String expectedMessage, String sep, Object... args)
    {
        assertShuffleErrorMessageWithOrWithoutNumbers(expectedMessage, sep, true, args);
    }

    /**
     * Проверить появилось ли окно с подтверждением действия, в котором могут присутствовать подстроки в
     * случайном порядке через разделитель
     * @param expectedMessage ожидаемый текст подтверждения
     * @param sep разделитель
     * @param args список параметров, порядок которых не известен, присутствующих в проверяемой строке через разделитель
     */
    public static void assertShuffleQuestion(String expectedMessage, String sep, Object... args)
    {
        String actual = tester.getText(GUIXpath.Any.QUESTION_DIALOG + GUIXpath.Any.DIALOG_WIDGET_BODY);
        Assert.assertTrue(String
                        .format(WRONG_FORM_MESSAGE_ERROR + " Получено:%n%s", actual),
                StringUtils.checkShuffleStringPattern(actual, expectedMessage, sep, args));
    }

    /**
     * Проверяет, что в последнем открытом диалоговом окне отсутствуют сообщения-предупреждения.
     */
    public static void assertTopmostDialogAttentionAbsence()
    {
        String xpath = String.format(GUIXpath.SpecificComplex.LAST_ELEMENT_PATTERN, GUIXpath.Any.ATTENTION);
        Assert.assertTrue("Нет сообщения-предупреждения на форме с указанным текстом",
                tester.waitDisappear(xpath));
    }

    /**
     * Проверяет наличие в последнем открытом диалоговом окне сообщения-предупреждения с указанным текстом
     * (частичное совпадение)
     * @param expectedMessage ожидаемое сообщение
     * @param args список параметров
     */
    public static void assertTopmostDialogAttentionContains(String expectedMessage, Object... args)
    {
        String xpath = String.format(GUIXpath.SpecificComplex.LAST_ELEMENT_PATTERN, GUIXpath.Any.ATTENTION);
        Assert.assertTrue("Нет сообщения-предупреждения на форме с указанным текстом",
                tester.waitTextContains(xpath, String.format(expectedMessage, args)));
    }

    /**
     * Проверяет, что в последнем открытом диалоговом окне отсутствуют информационные сообщения.
     */
    public static void assertTopmostDialogInfoMessageAbsence()
    {
        String xpath = String.format(GUIXpath.SpecificComplex.LAST_ELEMENT_PATTERN, GUIXpath.Any.INFO_MESSAGE);
        Assert.assertTrue("Нет сообщения-предупреждения на форме с указанным текстом",
                tester.waitDisappear(xpath));
    }

    /**
     * Проверяет наличие в последнем открытом диалоговом окне информационного сообщения с указанным текстом
     * (частичное совпадение)
     * @param expectedMessage ожидаемое сообщение
     * @param args список параметров
     */
    public static void assertTopmostDialogInfoMessageContains(String expectedMessage, Object... args)
    {
        String xpath = String.format(GUIXpath.SpecificComplex.LAST_ELEMENT_PATTERN, GUIXpath.Any.INFO_MESSAGE);
        Assert.assertTrue("Нет сообщения-предупреждения на форме с указанным текстом",
                tester.waitTextContains(xpath, String.format(expectedMessage, args)));
    }

    /**
     * Проверить название последней открытой модальной формы
     * @param expected ожидаемое название
     */
    public static void assertTopmostModalFormTitle(String expected)
    {
        assertTopmostModalFormTitle(expected, true);
    }

    /**
     * Проверяет отсутствие на форме сообщения валидации
     */
    public static void assertValidationAbsent(String message)
    {
        GUITester.assertAbsentElements(String.format(GUIXpath.Div.VALIDATION_TEXT_AND_TEXT_CONTAINS, message));
    }

    /**
     * Проверить, что появилось сообщение с заданным текстом при наведении курсора на атрибут
     * @param attr модель атрибута
     * @param message текст сообщения
     */
    public static void assertValidationAttribute(Attribute attr, String message)
    {
        tester.moveTo(GUIXpath.Any.ANY_VALUE, attr.getCode());
        assertValidationPresent(message);
    }

    /**
     * Нажать кнопку сохранить на форме и проверить, что появились сообщения о незаполненности обязательных полей
     * @param expected список ожидаемых сообщений на форме
     */
    public static void assertValidationErrorsMessages(List<String> expected)
    {
        applyFormWithValidationErrors();

        List<WebElement> validationElements = tester.findElements(GUIXpath.Div.VALIDATION_TEXT_CONTAINS);
        int i = 0;
        for (WebElement el : validationElements)
        {
            if (!el.getText().isEmpty())
            {
                Assert.assertTrue(expected.get(i).contains(el.getText()));
                i++;
            }
        }
        Assert.assertEquals("Количество всплывающих сообщений не совпало с ожидаемым.", expected.size(), i);
    }

    /**
     * Проверить, что появилось всплывающее сообщение "Поле должно быть заполнено"
     */
    public static void assertValidationPresent()
    {
        assertValidationPresent(ConfirmMessages.VALIDATION_REQUIRED_FIELD);
    }

    /**
     * Проверить, что появилось сообщение с заданным текстом
     * @param message текст сообщения
     */
    public static void assertValidationPresent(String message)
    {
        String errorMessage = "Всплывающее сообщение с текстом '" + message + "' не появилось";

        String text = tester.getText(GUIXpath.Div.VALIDATION_TEXT_AND_TEXT_CONTAINS, message);
        Assert.assertTrue(errorMessage, message.contains(text));
    }

    /**
     * Проверить, что сообщение валидации не появилось
     */
    public static void assertValidationTextAbsence()
    {
        String message = "Появилось сообщение валидации";
        Boolean res = false;
        for (WebElement element : tester.findElements(GUIXpath.Div.VALIDATION_TEXT_CONTAINS))
        {
            WebTester.scrollIntoView(tester.getWebDriver(), element);
            res = res || !element.getText().isEmpty();
        }
        Assert.assertFalse(message, res);
    }

    /**
     * Нажать кнопку отмена на диалоговом окне и проверить, что оно закрылось
     */
    public static void cancelDialog()
    {
        clickCancel();
        assertDialogDisappear("Форма не закрылась");
    }

    /**
     * Нажать кнопку отмены на форме и проверить, что форма закрылась
     */
    public static void cancelForm()
    {
        clickCancel();
        assertFormDisappear(GUIXpath.Div.FORM_CONTAINS);
    }

    /**
     * Убрать фокус с поля ввода на форме добавления/редактирования/диалоговом окне/карточке БО
     */
    public static void cleanFocus()
    {
        String focusElementXpath = tester.getFocusElementXpath();

        if (tester.isPresence(GUIAdvListFastFilterForm.FAST_FILTER_FORM))
        {
            tester.clickTopLeftCorner(GUIAdvListFastFilterForm.FAST_FILTER_FORM);
        }
        else if (tester.isPresence(GUIXpath.Div.PROPERTY_DIALOG_BOX))
        {
            tester.clickTopLeftCorner(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        }
        else if (tester.isPresence(GUIXpath.Div.HEADER_TITLE))
        {
            tester.clickTopLeftCorner(GUIXpath.Div.HEADER_TITLE);
        }
        else if (tester.isPresence(FORM_XPATH))
        {
            tester.click(FORM_XPATH);
        }

        if (focusElementXpath.equalsIgnoreCase(tester.getFocusElementXpath()))
        {
            tester.actives().clearFocus();
        }
    }

    /**
     * Кликнуть по выпадающему списку и выбрать значение по тексту
     * @param xpath путь к выпадающему списку
     * @param text текст элемента из выпадающего списка
     */
    public static void clickAndSelect(String xpath, String text)
    {
        tester.click(xpath);
        tester.click(POPUP + GUIXpath.Span.SPAN_TEXT_PATTERN, text);
    }

    /**
     * Нажать "Сохранить" на форме (В МЕТОДЕ НЕТ ПРОВЕРОК, ИСПОЛЬЗОВАНИЕ НЕ ЖЕЛАТЕЛЬНО)
     * для сохранения модальной формы использовать {@link #applyModalForm()}
     * если модальных форм несколько использовать {@link #applyLastModalForm()}
     * для проверок после нажатия на "Сохранить" есть applyFormAssert...
     */
    public static void clickApply()
    {
        tester.click(GUIXpath.Any.APPLY_BUTTON);
    }

    /**
     * Нажать кнопку "Сохранить" на форме, проверить что появилось диалоговое окно с ошибкой.
     * @param expectedError ожидаемое сообщение об ошибке
     */
    public static void clickApplyWithAssertError(String expectedError)
    {
        GUIForm.clickApply();
        GUIForm.assertErrorMessageOnForm(expectedError);
    }

    /**
     * Нажать кнопку "Сохранить" на форме, проверить что появилось диалоговое окно с ошибкой,
     * затем нажать кнопку "Отмена" на форме.
     * @param expectedError ожидаемое сообщение об ошибке
     */
    public static void clickApplyWithAssertErrorAndCancelForm(String expectedError)
    {
        clickApplyWithAssertError(expectedError);
        GUIForm.cancelForm();
    }

    /**
     * Нажать на кнопку «Сохранить» без ожидания.
     * Нажимает сразу же, как только кнопка станет кликабельной.
     */
    public static void clickApplyWithoutAsyncCallWaiting()
    {
        tester.clickWithoutAsyncCallWaiting(GUIXpath.Any.APPLY_BUTTON);
    }

    /**
     * Нажать "Сохранить" в диалоговом окне с заданным заголовком.
     * Метод необходимо вызывать при наличии нескольких модальных окон на экране.
     * @param dialogTitle заголовок окна
     */
    public static void clickApplyTitledDialog(String dialogTitle)
    {
        tester.click(
                GUIXpath.Any.TEXT_PATTERN + "/parent::*/parent::*/parent::*/parent::*/parent::*"
                + GUIXpath.Any.APPLY_BUTTON,
                dialogTitle);
    }

    /**
     * Нажать "Сохранить" на последнем открытом диалоговом окне (НЕ СОДЕРЖИТ ПРОВЕРОК)
     * лучше использовать {@link #applyLastModalForm()}
     */
    public static void clickApplyTopmostDialog()
    {
        tester.click(GUIXpath.SpecificComplex.LAST_ELEMENT_PATTERN, GUIXpath.Any.APPLY_BUTTON);
    }

    /**
     * Нажать кнопку "Сбросить" в предупреждении на форме
     */
    public static void clickAttentionButton()
    {
        tester.click(GUIXpath.Complex.ATTENTION_BUTTON);
    }

    /**
     * Нажимает на кнопку "Отмена".
     */
    public static void clickCancel()
    {
        tester.click(GUIXpath.Div.CANCEL);
    }

    /**
     * Нажать "Отмена" в диалоговом окне с заданным заголовком.
     * Метод необходимо вызывать при наличии нескольких модальных окон на экране.
     * @param dialogTitle заголовок окна
     */
    public static void clickCancelTitledDialog(String dialogTitle)
    {
        tester.click(
                GUIXpath.Any.TEXT_PATTERN + "/parent::*/parent::*/parent::*/parent::*/parent::*" + GUIXpath.Div.CANCEL,
                dialogTitle);
    }

    /**
     * Нажимает на кнопку "Отмена" в последнем открытом диалоговом окне.
     */
    public static void clickCancelTopmostDialog()
    {
        tester.click(GUIXpath.SpecificComplex.LAST_ELEMENT_PATTERN, GUIXpath.Div.CANCEL);
    }

    /**
     * Закрыть форму нажатием на крестик
     */
    public static void clickCloseOnTheCross()
    {
        tester.click(Div.PROPERTY_DIALOG_BOX_CAPTION + "//span[@_code='close2']");
        assertDialogDisappear("Диалоговое окно не закрылось");
    }

    /**
     * Нажать кнопку "Подтвердить"
     */
    public static void clickConfirmation()
    {
        tester.click(Div.CONFIRMATION);
    }

    /**
     * Нажать "Продолжить без сохранения" на форме диалога
     */
    public static void clickContinueWithoutSaving()
    {
        tester.click(GUIXpath.Complex.QUESTION_DIALOG_CONTINUE_WITHOUT_SAVING);
    }

    /**
     * Нажать "Нет" на форме диалога
     */
    public static void clickNo()
    {
        tester.click(GUIXpath.SpecificComplex.QUESTION_DIALOG_BTN_NO);
    }

    /**
     * Нажать кнопку "Очистить значение во всех выбранных объектах" в атрибуте
     * @param attr код атрибута
     */
    public static void clickQuickActionClear(Attribute attr)
    {
        tester.moveTo(GUIXpath.Any.ANY_VALUE, attr.getCode());
        tester.click(String.format(GUIForm.QUICK_ACTIONS_PANEL + GUIXpath.Span.CLEAR_ICON, attr.getCode()));
    }

    /**
     * Открывает форму быстрого добавления для атрибута.
     * @param attribute модель атрибута
     */
    public static void clickQuickAddForm(Attribute attribute)
    {
        tester.moveMouse(GUIXpath.InputComplex.ANY_VALUE, 5, -5, attribute.getCode());
        tester.click(QUICK_ACTION_ADD, attribute.getCode());
        assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
    }

    /**
     * Открывает форму быстрого добавления для параметра ДПС.
     * @param parameter модель атрибута
     */
    public static void clickQuickAddFormOnParameterForm(FormParameter parameter)
    {
        tester.moveMouse(GUIXpath.InputComplex.ANY_VALUE, 5, -5, parameter.getCode());
        tester.click(QUICK_ACTION_ADD, parameter.getCode());
    }

    /**
     * Открывает форму быстрого редактирования для атрибута.
     * @param attribute модель атрибута
     */
    public static void clickQuickEditForm(Attribute attribute)
    {
        tester.moveMouse(GUIXpath.InputComplex.ANY_VALUE, 5, -5, attribute.getCode());
        tester.click(QUICK_ACTION_EDIT, attribute.getCode());
    }

    /**
     * Открывает форму быстрого редактирования для одного из выбранных объектов.
     * @param attribute модель атрибута
     * @param objectId идентификатор редактируемого объекта
     */
    public static void clickQuickEditForm(Attribute attribute, String objectId)
    {
        String xpath = String.format(GUIXpath.Any.ANY_VALUE + GUIXpath.Complex.FORMTAGS_CARD + GUIXpath.Span.EDIT_ICON,
                attribute.getCode(), objectId);
        tester.click(xpath);
    }

    /**
     * Нажать кнопку "Восстановить" в предупреждении на форме
     */
    public static void clickRestoreValuesButton()
    {
        tester.click(GUIXpath.SpecificComplex.RESTORE_VALUES_BUTTON);
    }

    /**
     * Нажать кнопку "Сохранить" на форме диалога
     */
    public static void clickSave()
    {
        tester.click(GUIXpath.Complex.QUESTION_DIALOG_BTN_SAVE);
    }

    /**
     * Кликнуть на кнопку "Да"
     */
    public static void clickYes()
    {
        tester.click(GUIXpath.Complex.QUESTION_DIALOG_BTN_YES);
    }

    /**
     * Нажать кнопку "Да" на форме, проверить что появилось диалоговое окно с ошибкой.
     * @param expectedError ожидаемое сообщение об ошибке
     */
    public static void clickYesWithAssertError(String expectedError)
    {
        GUIForm.clickYes();
        GUIForm.assertErrorMessageOnForm(expectedError);
    }

    /**
     * Нажать кнопку отмен на форме вопроса и проверить, что форма закрылась
     */
    public static void closeDialog()
    {
        tester.click(GUITab.X_CLOSE_BTN);
        assertDialogDisappear("Форма не закрылась");
    }

    /**
     * Подтвердить действие кнопкой "Подтвердить", проверить, что форма исчезла
     */
    public static void confirm()
    {
        tester.click(GUIXpath.Div.CONFIRM);
        assertQuestionDisappear("Форма подтверждения не исчезла");
    }

    /**
     * Подтвердить архивирование, проверяем, что форма закрылась
     */
    public static void confirmArchive()
    {
        assertFormAppear(GUIXpath.Any.QUESTION_DIALOG);
        Assert.assertEquals("Данная форма не является формой архивирования.", "Подтверждение архивирования",
                tester.getText(GUIXpath.Complex.QUESTION_DIALOG_TITLE));
        confirmByYes();
    }

    /**
     * Подтвердить действие кнопкой "Да", проверить, что форма исчезла
     */
    public static void confirmByYes()
    {
        clickYes();
        assertQuestionDisappear("Форма подтверждения не исчезла");
    }

    /**
     * Подтвердить удаление, проверяем, что форма закрылась
     */
    public static void confirmDelete()
    {
        assertFormAppear(GUIXpath.Any.QUESTION_DIALOG);
        Assert.assertEquals("Данная форма не является формой удаления.", "Подтверждение удаления",
                tester.getText(GUIXpath.Complex.QUESTION_DIALOG_TITLE));
        confirmByYes();
    }

    /**
     * Нажать кнопку ОК на диалоге и проверить, что форма закрылась
     */
    public static void confirmErrorDialog()
    {
        tester.clickWithoutErrorCheck(Div.ERROR_DIALOG + GUIXpath.Div.OK);
        assertDialogDisappear("Диалог не закрылся");
    }

    /**
     * Нажать кнопку "Продолжить" и проверить, что диалоговое окно закрылось
     */
    public static void continueDialog()
    {
        tester.click(Any.CONTINUE_BUTTON);
        assertDialogDisappear("Диалог не закрылся");
    }

    /**
     * Заполнить строковый атрибут на форме редактирования/добавления
     * @param attribute модель атрибута
     */
    public static void fillAttribute(Attribute attribute)
    {
        fillAttribute(attribute, attribute.getValue());
    }

    /**
     * Заполнить строковый атрибут на форме редактирования/добавления
     * @param attribute модель атрибута
     * @param value значение атрибута
     */
    public static void fillAttribute(Attribute attribute, String value)
    {
        //Не должно быть null, использовать пустую строку
        Preconditions.checkNotNull(value);
        assertAttrPresent(attribute);
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, value,
                attribute.getCode());
    }

    /**
     * Заполнить строковый атрибут на форме редактирования/добавления
     * @param code параметр для форматированного xpath
     * @param value значение атрибута
     */
    public static void fillAttribute(String code, String value)
    {
        String msg = "Атрибут с кодом '%s' отсутствует на форме редактирования/добавления.";
        Assert.assertTrue(String.format(msg, code),
                tester.waitAppear(GUIXpath.Any.ANY_VALUE, code));
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, value, code);
    }

    /**
     * Вставить в поле указанный текст, очищая старое значение.<br>
     * Метод сделан с целью возможности вставить непечатаемый символ на Chrome
     * @param newTitle новое наименование
     */
    public static void fillAttributeByUseClipboard(String xPath, String newTitle)
    {
        tester.click(xPath);
        ActionToActiveElement.copyToClipboard(newTitle);
        tester.actives().clear();
        tester.actives().pressingPasteKey();
    }

    /**
     * Заполнить скрытое input-поле строкового атрибута на форме редактирования/добавления
     * @param code параметр для форматированного xpath
     * @param value значение атрибута
     */
    public static void fillAttributeInput(String code, String value)
    {
        tester.sendKeys(GUIXpath.InputComplex.ANY_VALUE, value, code);
    }

    /**
     * Вставить в поле с маской ввода указанное значение
     * @param attribute модель атрибута
     * @param value значение атрибута
     */
    public static void fillAttributeWithMask(Attribute attribute, String value)
    {
        //Не должно быть null, использовать пустую строку
        Preconditions.checkNotNull(value);
        assertAttrPresent(attribute);
        ActionToActiveElement.copyToClipboard(value);
        tester.click(GUIXpath.Div.FORM_CONTAINS + GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, attribute.getCode());
        tester.actives().clear();
        tester.actives().pressingPasteKey();
    }

    /**
     * Заполнить на форме атрибут Checkbox
     *
     * @param attr - заполняемый атрибут
     */
    public static void fillCheckbox(Attribute attr)
    {
        tester.setCheckbox(GUIXpath.Any.ANY_VALUE_CONTAINS.concat(X_VALUE_INPUT), Boolean.parseBoolean(attr.getValue()),
                attr.getCode());
    }

    /**
     * Заполнить на открытой форме атрибут через ввод даты или даты и времени
     * (для атрибута без разделения на дату и время)
     *
     * @param attr - заполняемый атрибут
     */
    public static void fillDateAttr(Attribute attr)
    {
        tester.sendKeys(GUIXpath.Any.ANY_VALUE + GUIXpath.Any.ANY_CONTAINS,
                attr.getValue(), attr.getCode(), CVConsts.ATTR_DATE_BOX_INPUT);
    }

    /**
     * Заполнить на форме атрибут в виде гиперссылки
     * @param attr - заполняемый атрибут
     * @param title - наименование гиперссылки
     * @param url - адрес гиперссылки
     */
    public static void fillHyperLinkAttr(Attribute attr, String title, String url)
    {
        tester.sendKeys(String.format(GUIXpath.Any.ANY_VALUE, attr.getCode()) + GUIXpath.Input.HYPERLINK_TEXT, title);
        tester.sendKeys(String.format(GUIXpath.Any.ANY_VALUE, attr.getCode()) + GUIXpath.Input.HYPERLINK_URL, url);
    }

    /**
     * Заполнить строковый атрибут на форме массового редактирования
     * @param attribute модель атрибута
     * @param value значение атрибута
     */
    public static void fillMassEditFormAttribute(Attribute attribute, String value)
    {
        //Не должно быть null, использовать пустую строку
        Preconditions.checkNotNull(value);
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, value,
                attribute.getFqn());
    }

    /**
     * Заполнить скрытое input-поле строкового атрибута на форме массового редактирования
     * @param attribute модель атрибута
     * @param value значение атрибута
     */
    public static void fillMassEditFormAttributeInput(Attribute attribute, String value)
    {
        //Не должно быть null, использовать пустую строку
        Preconditions.checkNotNull(value);
        tester.sendKeys(GUIXpath.InputComplex.ANY_VALUE, value, attribute.getParentFqn() + "@" + attribute.getCode());
    }

    /**
     * Заполнить значение атрибута, представленного на форме в виде Выпадающего списка.
     * Если значение атрибута null, будет выбрано значение "Не указано"
     *
     * @param attr - атрибут для заполнения
     */
    public static void fillSelectAttributeValueForListByTitle(Attribute attr)
    {
        if (attr.getValue() != null)
        {
            GUISelect.selectByTitle(GUIXpath.InputComplex.ANY_VALUE, attr.getValue(), attr.getCode());
        }
        else
        {
            GUISelect.selectEmpty(GUIXpath.InputComplex.ANY_VALUE, attr.getCode());
        }
    }

    /**
     * Заполнить значение атрибута, представленного на форме в виде Дерева.
     *
     * @param attr - атрибут для заполнения
     */
    public static void fillSelectAttributeValueForTreeByTitle(Attribute attr)
    {
        String selectInputXpath = String.format(GUISelect.SELECT_INPUT, attr.getCode());
        GUISelect.search(selectInputXpath, attr.getValue());
        String spanXpath = POPUP + Span.SPAN_TEXT_PATTERN;
        GUISelect.selectByXpathWithoutHide(selectInputXpath, String.format(spanXpath, attr.getValue()));
        GUISelect.hideSelect(selectInputXpath);
    }

    /**
     * Заполнить на форме строковое поле
     * @param title значение поля
     * @param parameterCode код заполняемого поля
     */
    public static void fillStringOnParametersForm(String title, String parameterCode)
    {
        String parameterInputXpath = String.format(GUIXpath.Any.ANY_VALUE, parameterCode);
        tester.sendKeys(parameterInputXpath, title);
    }

    /**
     * Заполнить на открытой форме (любой) атрибут через поле ввода значения
     * @param attr - заполняемый атрибут
     * @param value - значение атрибута
     */
    public static void fillTextareaAttr(Attribute attr, String value)
    {
        tester.sendKeys(GUIXpath.Other.TEXTAREA_ANY_VALUE, value, attr.getCode());
    }

    /**
     * Заполнить на открытой форме поле "Название"
     * @param title - название
     */
    public static void fillTitle(String title)
    {
        tester.sendKeys(GUIXpath.Any.TITLE_VALUE, title);
    }

    /**
     * Заполнить на открытой форме быстрого добавления\редактирования объекта поле "Название"
     * @param title название
     * */
    public static void fillTitleOnFastAddEditForm(String title)
    {
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, title, SystemAttrEnum.TITLE.getCode());
    }

    /**
     * Заполнить на открытой форме поле "Название"
     * @param title - название
     */
    public static void fillTitleOnLastModalForm(String title)
    {
        String xPath = String.format(GUIXpath.SpecificComplex.LAST_ELEMENT_PATTERN, GUIXpath.Any.TITLE_VALUE);
        tester.sendKeys(xPath, title);
    }

    /**
     * Сворачивает модальное окно и проверяет, что оно свернулось
     */
    public static void minimizeModalForm()
    {
        tester.click(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION + "/div/span[contains(@class, 'formMinimize')]");
        assertModalFormMinimized();
    }

    /**
     * Открыть форму удаления, проверить, что форма открылась.
     */
    public static void openDeleteForm()
    {
        tester.click(GUIXpath.Div.DEL);
        Assert.assertEquals("Данная форма не является формой удаления.",
                tester.getText(GUIXpath.Complex.QUESTION_DIALOG_TITLE), "Подтверждение удаления");
    }

    /**
     * Нажать кнопку "Редактировать"(id='gwt-debug-edit'), проверить, что форма открылась.
     */
    public static void openEditForm()
    {
        tester.click(GUIXpath.Div.EDIT);
        assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
    }

    /**
     * Нажать кнопку "Обновить" на форме и проверить, что форма закрылась
     */
    public static void refreshDialog()
    {
        tester.click(GUITab.X_REFRESH_BTN);
        assertDialogDisappear("Форма не закрылась");
    }

    /**
     * Разворачивает модальное окно до обычного размера и проверяет, что оно развернулось
     */
    public static void restoreModalForm()
    {
        tester.click(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION + "/div/span[contains(@class, 'formRestore')]");
        assertModalFormNormal();
    }

    /**
     * Выбрать тип объекта
     */
    public static void selectCase(MetaClass metaClass)
    {
        selectCase(metaClass.getFqn());
    }

    /**
     * Выбрать тип объекта
     */
    public static void selectCase(String fqn)
    {
        GUISelect.selectByXpath(GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, fqn));
    }

    /**
     * Отправить последовательность символов в активное поле на форме
     * @param keys последовательность символов
     */
    public static void sendKeys(CharSequence keys)
    {
        tester.actives().sendKeys(keys);
    }

    /**
     * Установить чекбокс атрибута на модальной форме
     * @param boolAttribute атрибут
     * @param value устанавливаемое значение чекбокса
     */
    public static void setCheckBoxAttributeOnModalForm(Attribute boolAttribute, boolean value)
    {
        tester.setCheckbox(String.format(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + Any.ANY_VALUE_INPUT_CONTAINS,
                boolAttribute.getCode()), value);
    }

    /**
     * Установить/снять чекбокс "Включен" на форме добавления/редактирования
     * @param value true/false (установить/снять)
     */
    public static void setEnabledCheckBox(boolean value)
    {
        tester.setCheckbox(GUIXpath.Input.ENABLED_VALUE_INPUT, value);
    }

    /**
     * Возвращает uuid формы, необходимо находиться на форме добавления/редактирования
     * @return uuid формы
     */
    public static String getFormCode()
    {
        String id = tester.find(Div.FORM_CONTAINS).getAttribute("id");
        return id.substring(id.indexOf('.') + 1);
    }

    /**
     * Получить название формы
     */
    public static String getFormTitle()
    {
        return tester.getText(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
    }

    /**
     * Выбрать значение атрибута Родитель
     * @param parents набор моделей БО - родителей, не включая Компанию
     */
    public static void setParent(Bo... parents)
    {
        BoTree tree = new BoTree(String.format(GUIXpath.Any.ANY_VALUE, PARENT_FIELD), true);
        tree.setElementInSelectTree(parents);
    }

    /**
     * Проскролить к атрибуту
     * @param attribute атрибут
     */
    public static void scrollToAttribute(Attribute attribute)
    {
        String attributeXPath = BooleanType.CODE.equals(attribute.getType())
                ? GUIXpath.Any.ANY_VALUE_CONTAINS + X_VALUE_LABEL
                : GUIXpath.Any.ANY_VALUE;
        String xPath = String.format(LAST_ELEMENT_PATTERN, attributeXPath);
        tester.scrollIntoView(tester.find(xPath, attribute.getCode()), ScrollAlignment.CENTER, ScrollAlignment.NEAREST);
    }

    /**
     * Проверить появилось ли сообщение об ошибке, в котором могут присутствовать подстроки в
     * случайном порядке с нумерацией или без через разделитель
     * @param expectedMessage ожидаемое сообщение об ошибке
     * @param sep разделитель
     * @param withNumbers с нумерацией или без
     * @param args список параметров, порядок которых не известен, присутствующих в проверяемой строке через разделитель
     */
    private static void assertShuffleErrorMessageWithOrWithoutNumbers(String expectedMessage, String sep,
            boolean withNumbers, Object... args)
    {
        try
        {
            GUIError.waitError(WaitTool.WAIT_TIME);
            Assert.fail("Ошибка не появилась.");
        }
        catch (DialogErrorException e)
        {
            String actual = e.getMessage();
            StringBuilder params = new StringBuilder();
            for (Object arg : args)
            {
                params.append((String)arg).append(' ');
            }

            //@formatter:off
            boolean checkResult = withNumbers ?
                    StringUtils.checkShuffleStringPatternWithNumbers(actual, expectedMessage, sep, args) :
                    StringUtils.checkShuffleStringPattern(actual, expectedMessage, sep, args);
            //@formatter:on

            Assert.assertTrue(String.format(
                    "Полученное сообщение об ошибке не совпало с ожидаемым. Получено:%n%s  Ожидалось сообщение:%n%s С"
                    + " параметрами, порядок которых не известен:%n%s",
                    actual, expectedMessage, params.toString()), checkResult);
            GUIError.ignoreError();
            GUIError.expectError();
        }
    }

    private static void assertTopmostModalFormTitle(String expected, boolean onTopmost)
    {
        String formXPath = GUIXpath.Div.PROPERTY_DIALOG_BOX + FORM_CAPTION;
        String xPath = onTopmost ? String.format(LAST_ELEMENT_PATTERN, formXPath) : formXPath;

        Assert.assertTrue("Отсутствует форма с заголовком: " + expected, tester.waitTextPresent(xPath, expected));
    }
}