package ru.naumen.selenium.casesutil.interfaceelement;

import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;

/**
 * Общие методы для работы с выпадающими списками типа дерево, в качестве элементов в котором используются статусы
 * <AUTHOR>
 * @since 27.03.2013
 */
public class StateTree extends CoreTree
{
    /**
     * Конструктор
     * @param treeId id элемента, который является деревом, либо его xpath(может иметь символы форматирования)
     */
    public StateTree(String treeId, Object... args)
    {
        super(treeId, args);
    }

    /**
     * Проверить название статуса в дереве
     * @param status модель проверяемого статуса
     * @param nodes набор id родителей до нужного узла, включая сам узел
     */
    public void assertElementTitle(BoStatus status, MetaClass... nodes)
    {
        assertElementTitle(status.getTitle(), toStringId(status, nodes));
    }

    /**
     * Проверить можно ли выбрать элемент в дереве
     * @param expected true - выбрать можно, false - выбрать нельзя
     * @param status модель статуса, который проверяем
     * @param nodes набор моделей метаклассов - родителей до нужного узла, включая сам узел
     */
    public void assertEnableElement(boolean expected, BoStatus status, MetaClass... nodes)
    {
        assertEnableElement(expected, toStringId(status, nodes));
    }

    /**
     * Проверить, что элемент не выбран в дереве множественного выбора
     * @param status модель статуса, который проверяем
     * @param nodes набор моделей метаклассов - родителей до нужного узла, включая сам узел
     */
    public void assertNotSelected(BoStatus status, MetaClass... nodes)
    {
        assertNotSelectedMultiSelectTree(toStringId(status, nodes));
    }

    /**
     * Проверить, что элемент выбран в дереве множественного выбора
     * @param status модель статуса, который проверяем
     * @param nodes набор моделей метаклассов - родителей до нужного узла, включая сам узел
     */
    public void assertSelected(BoStatus status, MetaClass... nodes)
    {
        assertSelectedMultiSelectTree(toStringId(status, nodes));
    }

    @Override
    public String getXNode(String... nodes)
    {
        StringBuilder buffer = new StringBuilder(X_VALUE_TREE);
        int level = 1;
        for (String node : nodes)
        {
            buffer.append(String.format(X_TREE_ITEM, node, level++));
        }
        return buffer.toString();
    }

    /**
     * Выделить узел в дереве
     * @param status модель статуса, который выделяем
     * @param nodes набор моделей метаклассов - родителей до нужного узла, включая сам узел
     */
    public void setElementInMultiSelectTree(BoStatus status, MetaClass... nodes)
    {
        //Периодически не удаётся установить элемент в stateTree,
        int current = 0;
        while (!getValue().contains(status.getTitle()) || current < 5)
        {
            setElementInMultiSelectTree(toStringId(status, nodes));
            current++;
        }
    }

    /**
     * Снять выделение узла в дереве
     * @param status модель статуса, в который снимаем выделение
     * @param nodes набор моделей метаклассов - родителей до нужного узла, включая сам узел
     */
    public void unsetElementInMultiSelectTree(BoStatus status, MetaClass... nodes)
    {
        unsetElementInMultiSelectTree(toStringId(status, nodes));
    }

    /**
     * Получить из набора моделей метаклассов набор идентификаторов метаклассов + последний элемент:
     * иденификатор метакласса в котором находится статус + ":" + код статуса
     * @param status модель статуса
     * @param nodes набор моделей метаклассов
     * @return набор идентификаторов метаклассов + последний элемент:
     * иденификатор метакласса в котором находится статус + ":" + код статуса
     */
    private String[] toStringId(BoStatus status, MetaClass... nodes)
    {
        String[] fqns = new String[nodes.length + 1];
        for (int i = 0; i < nodes.length; i++)
        {
            fqns[i] = nodes[i].getFqn();
        }
        fqns[nodes.length] = fqns[nodes.length - 1] + ":" + status.getCode();
        return fqns;
    }
}
