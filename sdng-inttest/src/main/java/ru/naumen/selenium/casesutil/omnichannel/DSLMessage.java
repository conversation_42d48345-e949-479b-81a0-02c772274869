package ru.naumen.selenium.casesutil.omnichannel;

import java.util.List;
import java.util.Map;

import org.junit.Assert;

import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.OmnichannelConstants.MessageStates;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.omnichannel.DAOMessage;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.Json;

/**
 * Утилитарные методы работы с сообщением омниканальности
 * <AUTHOR>
 * @since 11.10.2021
 */
public class DSLMessage
{
    /**
     * Таймаут ожидания прихода сообщения из NCC в SMP в секундах
     */
    private static final long WAITING_MESSAGE_TIMEOUT_IN_SECONDS = 60L;

    /**
     * Проверяет, что сообщения в списке идут в порядке даты создания
     * @param messages список сообщений
     */
    public static void assertOrderMessagesByDate(List<Bo> messages)
    {
        List<Long> creationDates = messages.stream()
                .map(message -> Long.valueOf((String)Json.GSON.fromJson(message.getUserAttributes(), Map.class)
                        .get(SystemAttrEnum.CREATION_DATE.getCode())))
                .toList();
        Assert.assertEquals("Сообщения в списке идут не в порядке создания", creationDates,
                creationDates.stream().sorted().toList());
    }

    /**
     * Проверяет входящее сообщение
     * @param message сообщение
     * @param chatUuid ожидаемый идентификатор чата
     * @param sessionUuid ожидаемый идентификатор сессии
     * @param text ожидаемый текст
     */
    public static void assertInComMessage(Bo message, String chatUuid, String sessionUuid, @Nullable String text)
    {
        assertInComMessage(message, chatUuid, sessionUuid, text, SystemAttrEnum.SERIES.getCode(),
                SystemAttrEnum.EXTERNAL_ID.getCode());
    }

    /**
     * Проверяет входящее сообщение
     * @param message сообщение
     * @param chatUuid ожидаемый идентификатор чата
     * @param sessionUuid ожидаемый идентификатор сессии
     * @param text ожидаемый текст
     * @param presentAttrs список атрибутов для которых необходимо проверить наличие значения
     */
    public static void assertInComMessage(Bo message, String chatUuid, String sessionUuid, @Nullable String text,
            String... presentAttrs)
    {
        assertOutComMessage(message, chatUuid, sessionUuid, text, MessageStates.RECEIVED);
        DSLBo.assertStringAttrsPresent(message, presentAttrs);
    }

    /**
     * Проверяет исходящее сообщение
     * @param message сообщение
     * @param chatUuid ожидаемый идентификатор чата
     * @param sessionUuid ожидаемый идентификатор сессии
     * @param text ожидаемый текст
     * @param state ожидаемый статус (атрибут "Статус" сообщения)
     */
    public static void assertOutComMessage(Bo message, String chatUuid, String sessionUuid, @Nullable String text,
            String state)
    {
        DSLBo.assertObjectAttr(message, DAOAttribute.createPseudo(SystemAttrEnum.CHAT.getTitle(),
                SystemAttrEnum.CHAT.getCode(), chatUuid));
        DSLBo.assertObjectAttr(message, DAOAttribute.createPseudo(SystemAttrEnum.DIALOG_SESSION.getTitle(),
                SystemAttrEnum.DIALOG_SESSION.getCode(), sessionUuid));
        DSLBo.assertStringAttr(message, SystemAttrEnum.TEXT.getCode(), text);
        DSLBo.assertStringAttr(message, SystemAttrEnum.MESSAGE_STATE.getCode(), state);
    }

    /**
     * Проверяет исходящее сообщение
     * @param message сообщение
     * @param chatUuid ожидаемый идентификатор чата
     * @param sessionUuid ожидаемый идентификатор сессии
     * @param text ожидаемый текст
     * @param state ожидаемый статус
     * @param keyboard ожидаемая клавиатура
     */
    public static void assertOutComMessage(Bo message, String chatUuid, String sessionUuid, String text,
            String state, String keyboard)
    {
        assertOutComMessage(message, chatUuid, sessionUuid, text, state);
        DSLBo.assertStringAttr(message, SystemAttrEnum.KEYBOARD.getCode(), keyboard);
    }

    /**
     * Проверяет исходящее сообщение
     * @param message сообщение
     * @param chatUuid ожидаемый идентификатор чата
     * @param sessionUuid ожидаемый идентификатор сессии
     * @param text ожидаемый текст
     * @param state ожидаемый статус
     * @param keyboard ожидаемый клавиатура
     * @param sentDate ожидаемая дата отправки в мс
     */
    public static void assertOutComMessage(Bo message, String chatUuid, String sessionUuid, String text,
            String state, @Nullable String keyboard, @Nullable String sentDate)
    {
        assertOutComMessage(message, chatUuid, sessionUuid, text, state, keyboard);
        DSLBo.assertStringAttr(message, SystemAttrEnum.SENT_DATE.getCode(), sentDate);
    }

    /**
     * Отправить сообщение в чат выполнив метод "api.omnichannel.sendMessage" (используется для сообщений в статусе
     * "Ошибка")
     * @param message сообщение
     */
    public static void sendMessage(Bo message)
    {
        new ScriptRunner(String.format("api.omnichannel.sendMessage('%s')", message.getUuid())).runScript();
    }

    /**
     * Ожидает, пока сообщение с указанным именем внешнего файла создастся в SMP. Ожидание в течение таймаута
     * {@link DSLMessage#WAITING_MESSAGE_TIMEOUT_IN_SECONDS}.
     * @param fileName имя файла
     * @throws AssertionError сообщение отсутствует в SMP
     */
    public static Bo waitMessageByFileName(String fileName)
    {
        String errorMessage = "Файл с именем '%s' из чата не доставлен в SMP";
        String result = WaitTool.waitSomething(fileName, WAITING_MESSAGE_TIMEOUT_IN_SECONDS, 500L, input ->
        {
            Map<String, String> exFileModel = ScriptModules.getModuleExternalFile().getByFileName(fileName);
            return exFileModel == null ? null : exFileModel.get("messageUuid");
        });

        Assert.assertNotNull(String.format(errorMessage, fileName), result);

        return createMessageFromModelMap(SdDataUtils.getObjectByUUID(result, SystemClass.SYS_MESSAGE.getCode()));
    }

    /**
     * Получить модель письма по его uuid
     * @param uuid uuid письма
     * @return модель письма
     */
    public static ModelMap getRecordModel(String uuid)
    {
        return SdDataUtils.getObjectByUUID(uuid, SystemClass.MAIL.getCode());
    }

    /**
     * Ожидает, пока сообщение с указанным текстом создастся в SMP и будет в указанном кол-ве. Ожидание в течение
     * таймаута {@link DSLMessage#WAITING_MESSAGE_TIMEOUT_IN_SECONDS}.
     * @param text текст сообщения
     * @param serialNumber ожидаемое кол-во сообщение с таким тестом
     * @throws AssertionError сообщение отсутствует в SMP
     */
    public static Bo waitMessageByText(String text, int serialNumber)
    {
        String errorMessage = "Сообщение с текстом '%s' из чата не доставлено в SMP";
        List<ModelMap> result = WaitTool.waitSomething(text, WAITING_MESSAGE_TIMEOUT_IN_SECONDS, 500L, input ->
        {
            List<ModelMap> objects = SdDataUtils.findObjects(SystemClass.SYS_MESSAGE.getCode(),
                    ModelMap.newMap(SystemAttrEnum.TEXT.getCode(), input));
            return objects.size() == serialNumber ? objects : null;
        });

        Assert.assertNotNull(String.format(errorMessage, text), result);

        return createMessageFromModelMap(result.get(serialNumber - 1));
    }

    /**
     * Ожидает, пока сообщение с указанным текстом создастся в SMP. Ожидание в течение таймаута
     * {@link DSLMessage#WAITING_MESSAGE_TIMEOUT_IN_SECONDS}.
     * @param text текст сообщения
     * @throws AssertionError сообщение отсутствует в SMP
     */
    public static Bo waitMessageByText(String text)
    {
        return waitMessageByText(text, 1);
    }

    /**
     * Ожидает пока сообщение примет указанный статус. Ожидание в течение
     * {@link DSLMessage#WAITING_MESSAGE_TIMEOUT_IN_SECONDS}.
     * @param message сообщение
     * @param expectedState ожидаемый статус
     */
    public static void waitForState(Bo message, String expectedState)
    {
        waitForState(message, expectedState, WAITING_MESSAGE_TIMEOUT_IN_SECONDS);
    }

    /**
     * Ожидает пока сообщение примет указанный статус. Ожидание в течение указанного времени.
     * @param message сообщение
     * @param expectedState ожидаемый статус
     * @param timeout таймаут ожидания в секундах
     */
    public static void waitForState(Bo message, String expectedState, long timeout)
    {
        Boolean result = WaitTool.waitSomething(message, timeout, 1000L, input ->
                expectedState.equals(SdDataUtils.getStringValue(input, SystemAttrEnum.MESSAGE_STATE.getCode())));
        Assert.assertTrue(String.format("Сообщение '%s' не приняло ожидаемый статус", message.getUuid()),
                result != null && result);
    }

    /**
     * Возвращает сообщение по его идентификатору
     * @param messageUuid идентификатор сообщения
     * @return сообщение
     */
    public static Bo getMessageByUuid(String messageUuid)
    {
        return createMessageFromModelMap(SdDataUtils.getObjectByUUID(messageUuid, SystemClass.SYS_MESSAGE.getCode()));
    }

    private static Bo createMessageFromModelMap(ModelMap modelMap)
    {
        Bo message = DAOMessage.messageBuilder().
                setChatUuid(SdDataUtils.getMapValue(modelMap, SystemAttrEnum.CHAT.getCode())
                        .get(SystemAttrEnum.UUID.getCode()))
                .setSessionUuid(SdDataUtils.getMapValue(modelMap, SystemAttrEnum.DIALOG_SESSION.getCode())
                        .get(SystemAttrEnum.UUID.getCode()))
                .setTitle(modelMap.get(SystemAttrEnum.TITLE.getCode()))
                .setText(modelMap.get(SystemAttrEnum.TEXT.getCode()))
                .setSeries(modelMap.get(SystemAttrEnum.SERIES.getCode()))
                .setExternalId(modelMap.get(SystemAttrEnum.EXTERNAL_ID.getCode()))
                .setMessageState(modelMap.get(SystemAttrEnum.MESSAGE_STATE.getCode()))
                .setCreationDate(modelMap.get(SystemAttrEnum.CREATION_DATE.getCode()))
                .build();
        message.setUuid(modelMap.get(SystemAttrEnum.UUID.getCode()));
        message.setExists(true);
        return message;
    }
}
