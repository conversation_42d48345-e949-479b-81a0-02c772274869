package ru.naumen.selenium.casesutil;

import org.junit.Assert;
import org.openqa.selenium.By;
import org.openqa.selenium.StaleElementReferenceException;
import org.openqa.selenium.WebElement;

import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListFastFilterForm;
import ru.naumen.selenium.core.WebTester;
import ru.naumen.selenium.core.WebTester.ScrollAlignment;
import ru.naumen.selenium.core.exception.DialogErrorException;

/**
 * Методы для работы с предупреждениями в интерфейсе
 * <AUTHOR>
 *
 */
public class GUIAttention extends CoreTester
{
    public static final String ATTENTION = GUIXpath.divGwtDebugId("attention");
    public static final String ATTENTION_TEXT = ATTENTION + "//span[2]";

    /**
     * Проверить отсутствие предупреждения на странице
     */
    public static void assertAttentionAbsence()
    {
        Assert.assertFalse("На странице присутствует предупреждение!", tester.getElements(ATTENTION).stream()
                .anyMatch(element -> element.isDisplayed()));
    }

    /**
     * Проверить соответствие текста предупреждения на карточке мобильного контента ожидаемому
     */
    public static void assertAttentionMessage(String expectedMessage)
    {
        String text = tester.findDisplayed(ATTENTION_TEXT, null).getText();
        Assert.assertEquals("Текст предупреждения отличается от ожидаемого", expectedMessage, text);
    }

    /**
     * Проверить соответствие текста предупреждения на форме быстрой фильтрации
     */
    public static void assertFastFilterAttentionMessage(String expectedMessage)
    {
        String text = tester.findDisplayed(GUIAdvListFastFilterForm.ATTENTION_PANEL, null).getText();
        Assert.assertEquals("Текст предупреждения отличается от ожидаемого", expectedMessage, text);
    }

    /**
     * Проверить соответствие текста предупреждения в списке мобильных контентов ожидаемому
     */
    public static void assertAttentionMessageInList(String expectedMessage)
    {
        if (tester.isExist(ATTENTION_TEXT))
        {
            for (WebElement attention : tester.getWebDriver().findElements(By.xpath(ATTENTION_TEXT)))
            {
                WebTester.scrollIntoView(tester.getWebDriver(), attention, ScrollAlignment.END,
                        ScrollAlignment.NEAREST);
                try
                {
                    Boolean attentionDisplayed = attention.isDisplayed();
                    if (attentionDisplayed)
                    {
                        String text = attention.getText();
                        Assert.assertEquals("Текст предупреждения отличается от ожидаемого", expectedMessage, text);

                    }
                }
                catch (StaleElementReferenceException e)
                {
                    return;
                }
                catch (DialogErrorException e)
                {
                    throw e;
                }
            }
        }
    }

    /**
     * Проверить присутствие предупреждения на странице
     */
    public static void assertAttentionPresence()
    {
        boolean attention = tester.waitAppear(ATTENTION);
        Assert.assertTrue("На странице отсутствует сообщение об ошибке.", attention);
    }

    /**
     * Проверить присутствие предупреждения на странице
     * @param message текст предупреждения
     */
    public static void assertAttentionPresence(String message)
    {
        GUITester.assertPresent(ATTENTION + GUIXpath.Span.SPAN_TEXT_PATTERN,
                "На странице отсутствует необходимое предупреждение.", message);
    }

    /**
     *
     * @param message
     */
    public static void show(String message)
    {
        GUITester.assertPresent(ATTENTION + GUIXpath.Span.SPAN_TEXT_PATTERN,
                "На странице отсутствует необходимое предупреждение.", message);
    }

}
