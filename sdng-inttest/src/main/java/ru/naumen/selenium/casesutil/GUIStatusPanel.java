package ru.naumen.selenium.casesutil;

import org.junit.Assert;

/**
 * Методы для работы с панелью состояния.
 * <AUTHOR>
 * @since May 06, 2022
 */
public class GUIStatusPanel extends CoreTester
{
    public static final String X_STATUS_MESSAGE = "//div[@id='gwt-debug-currentMessage']";
    public static final String X_STATUS_MESSAGE_TEXT = X_STATUS_MESSAGE + "//div[@id='gwt-debug-messageText']";
    public static final String X_ACTION_TOOL = X_STATUS_MESSAGE + "//div[@id='gwt-debug-actionTool']";
    public static final String X_EXPAND_TOOL = X_STATUS_MESSAGE + "//div[@id='gwt-debug-expandButton']";
    public static final String X_LOG_AREA = "//div[@id='gwt-debug-logArea']";
    public static final String X_LOG_MESSAGE = X_LOG_AREA + "/div[%s]";
    public static final String X_LOG_MESSAGE_TEXT = X_LOG_MESSAGE + "//div[@id='gwt-debug-messageText']";
    public static final String X_CLOSE_PANEL = X_STATUS_MESSAGE + "//div[@_code='close2']";

    /**
     * Проверяет наличие сообщения с указанным текстом в логе панели состояния.
     * @param item номер элемента лога
     * @param expected ожидаемое сообщение
     */
    public static void assertLogMessage(int item, String expected)
    {
        GUITester.assertTextPresentWithMsg(X_LOG_MESSAGE_TEXT, expected,
                "Сообщение в логе панели состояния не совпало с ожидаемым.", item);
    }

    /**
     * Проверяет наличие сообщения с указанным текстом на панели состояния.
     * @param expected ожидаемое сообщение
     */
    public static void assertStatusMessage(String expected)
    {
        GUITester.assertTextPresentWithMsg(X_STATUS_MESSAGE_TEXT, expected,
                "Сообщение на панели состояния не совпало с ожидаемым.");
    }

    /**
     * Проверяет наличие панели состояния.
     */
    public static void assertStatusPanelAppear()
    {
        Assert.assertTrue("Панель состояния не появилась.", tester.waitAppear(X_STATUS_MESSAGE));
    }

    /**
     * Проверяет отсутствие панели состояния.
     */
    public static void assertStatusPanelDisappear()
    {
        Assert.assertTrue("Панель состояния не исчезла.", tester.waitDisappear(X_STATUS_MESSAGE));
    }

    /**
     * Выполняет нажатие на кнопку разворачивания в сообщении на панели состояния.
     */
    public static void clickExpandMessage()
    {
        tester.click(X_EXPAND_TOOL);
    }

    /**
     * Выполняет нажатие на кнопку действия в сообщении на панели состояния.
     */
    public static void clickMessageAction()
    {
        tester.click(X_ACTION_TOOL);
    }

    /**
     * Выполняет нажатие на иконку закрытия панели состояния.
     */
    public static void clickClosePanel()
    {
        tester.click(X_CLOSE_PANEL);
    }
}
