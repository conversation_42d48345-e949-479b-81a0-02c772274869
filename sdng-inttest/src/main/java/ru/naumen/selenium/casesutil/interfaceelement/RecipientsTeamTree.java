package ru.naumen.selenium.casesutil.interfaceelement;

import ru.naumen.selenium.casesutil.GUIXpath;

/**
 * Общие методы для работы с выпадающими списками типа дерево в поле "Сотрудники" (получатели оповещения) на
 * форме добавления/редактирования действия по событию типа "Оповещение", поддерево "Команды"
 * <AUTHOR>
 * @since 18.06.2014
 */
public class RecipientsTeamTree extends BoTree
{
    public RecipientsTeamTree()
    {
        super(GUIXpath.Id.RECIPIENTS_VALUE, false);
    }

    @Override
    public String getXNode(String... nodes)
    {
        StringBuilder buffer = new StringBuilder(X_VALUE_TREE);
        int level = 1;
        buffer.append(String.format(X_TREE_ITEM, RecipientsTree.TEAMS_NODE, level++));
        for (String node : nodes)
        {
            buffer.append(String.format(X_TREE_ITEM, node, level++));
        }
        return buffer.toString();
    }
}