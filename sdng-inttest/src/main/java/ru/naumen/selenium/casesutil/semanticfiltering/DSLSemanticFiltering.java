package ru.naumen.selenium.casesutil.semanticfiltering;

import ru.naumen.selenium.casesutil.model.RemoveOperation;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.Cleaner;

/**
 * Методы для работы с фильтрацией с учетом морфологии
 *
 * <AUTHOR>
 * @since 07 окт. 2019 г.
 */
public class DSLSemanticFiltering
{
    /**
     * Включает фильтрацию с учетом морфологии,
     * по окончанию тестов фильтрация выключается
     */
    public static void setEnabled()
    {
        ScriptElement element = SESemanticFiltering.setEnabled();
        new ScriptRunner(true, element).runScript();
        setTestDictionaryConfigurationName();
        Cleaner.push(new RemoveOperation(SESemanticFiltering.setDisabled()));
    }

    /**
     * Устанавливает конфигурацию словарей морфологической фильтрации - russian,
     * которая входит в поставку postgresql. Необходимо для успешного прохождения тестов
     * без дополнительной настройки postgresql.
     */
    private static void setTestDictionaryConfigurationName()
    {
        ScriptElement element = SESemanticFiltering.setTestDictionaryConfigurationName();
        new ScriptRunner(true, element).runScript();
    }
}
