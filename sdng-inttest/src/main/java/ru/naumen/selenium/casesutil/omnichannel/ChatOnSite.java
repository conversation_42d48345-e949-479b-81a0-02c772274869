package ru.naumen.selenium.casesutil.omnichannel;

/**
 * Содержит параметры доступных для подключения чатов на сайте
 * <AUTHOR>
 * @since 30.01.2024
 */
public enum ChatOnSite
{
    MESSAGE_BOX("https://localhost/chatOnSite"),
    OMNI_GATE("https://localhost/omnigate/chatOnSite");

    private final String address;

    ChatOnSite(String address)
    {
        this.address = address;
    }

    public String getAddress()
    {
        return address;
    }
}
