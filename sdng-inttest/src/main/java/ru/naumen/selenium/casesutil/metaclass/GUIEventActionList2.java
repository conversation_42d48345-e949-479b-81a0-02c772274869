package ru.naumen.selenium.casesutil.metaclass;

import static ru.naumen.selenium.casesutil.model.attr.DAOAttribute.createPseudo;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath.Constant;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.model.attr.Attribute;

/**
 * Утилитарные методы для работы со списком действий по событию
 * <AUTHOR>
 * @since 31.03.2015
 */
public class GUIEventActionList2 extends GUIAdvListUtil
{
    private static final String ADVLIST = "gwt-debug-eventActions" + Constant.SINGLE_CONTENT_ON_TAB_ID_POSTFIX;

    public static final Attribute TITLE_ATTR = createPseudo("Название", "title", null);
    public static final Attribute ACTION_ATTR = createPseudo("Действие", "action", null);
    public static final Attribute EVENT_ATTR = createPseudo("Событие", "event", null);
    public static final Attribute ENABLE_ATTR = createPseudo("Включено", "on", null);
    public static final Attribute OBJECTS_ATTR = createPseudo("Объекты", "linkedClasses", null);
    public static final Attribute DESCRIPTION_ATTR = createPseudo("Описание", "description", null);
    public static final Attribute JMSQUEUE_TITLE_ATTR = createPseudo("Очередь обработки действия", "jmsQueue", null);

    /**Пиктограммы*/
    public static final String PICT_EDIT = "editEventAction";
    public static final String PICT_DELETE = "deleteEventAction";
    public static final String PICT_ENABLE = "toggleProvider";

    private static volatile GUIEventActionList2 advlist;

    public static GUIEventActionList2 advlist()
    {
        if (advlist == null)
        {
            advlist = new GUIEventActionList2();
        }
        return advlist;
    }

    /**
     * Проверить, что находишься в разделе 'Действия по событиям'
     */
    public static void assertThatList()
    {
        String message = "Находимся не в разделе 'Действия по событиям'";
        GUITester.assertTextPresentWithMsg(Div.HEADER_TITLE, "Действия по событиям", message);
    }

    private GUIEventActionList2()
    {
        super(ADVLIST);
    }
}
