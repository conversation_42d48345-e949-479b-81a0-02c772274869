package ru.naumen.selenium.layout.models;

import ru.naumen.selenium.casesutil.model.ModelFactory;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOBranch;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.layout.models.LayoutModels.Agreement;
import ru.naumen.selenium.layout.models.LayoutModels.Branch;
import ru.naumen.selenium.layout.models.LayoutModels.Employee;
import ru.naumen.selenium.layout.models.LayoutModels.Ou;
import ru.naumen.selenium.layout.models.LayoutModels.Sc;
import ru.naumen.selenium.layout.models.LayoutModels.Team;
import ru.naumen.selenium.layout.models.LayoutModels.UserBo;

/**
 * Модели бизнес-объектов в системе
 *
 * <AUTHOR>
 * @since 15 мар. 2019 г.
 */
public class LayoutBo
{
    /** team$4401: Команда*/
    public static final Bo TEAM_1 = createTeamBo(LayoutMetaClass.TEAM_CASE1, Team.TEAM1);
    /** ou$4201: Отдел */
    public static final Bo OU1_0 = createOu(LayoutMetaClass.OU_CASE1, Ou.OU1_0);
    /** ou$6201: Тестирование отображения цепочки объектов с вложенностью 8 уровней, истории и избранного
     * в левом навигационном меню */
    public static final Bo OU1_1 = createOu(LayoutMetaClass.OU_CASE1, Ou.OU1_1);
    /** ou$6202: Вложенный отдел второго уровня */
    public static final Bo OU1_2 = createOu(LayoutMetaClass.OU_CASE1, Ou.OU1_2);
    /** ou$6203: Вложенный отдел третьего уровня */
    public static final Bo OU1_3 = createOu(LayoutMetaClass.OU_CASE1, Ou.OU1_3);
    /** ou$6204: Вложенный отдел четвертого уровня */
    public static final Bo OU1_4 = createOu(LayoutMetaClass.OU_CASE1, Ou.OU1_4);
    /** ou$6205: 5 */
    public static final Bo OU1_5 = createOu(LayoutMetaClass.OU_CASE1, Ou.OU1_5);
    /** ou$6206: Вложенный отдел шестого уровня */
    public static final Bo OU1_6 = createOu(LayoutMetaClass.OU_CASE1, Ou.OU1_6);
    /** ou$6207: Вложенный отдел седьмого уровня */
    public static final Bo OU1_7 = createOu(LayoutMetaClass.OU_CASE1, Ou.OU1_7);
    /** ou$6208: Восьмой уровень конец лесенки */
    public static final Bo OU1_8 = createOu(LayoutMetaClass.OU_CASE1, Ou.OU1_8);
    /** ou$78401: Отдел для левого меню */
    public static final Bo OU1_9 = createOu(LayoutMetaClass.OU_CASE1, Ou.OU1_9);
    /** ou$56012: Отдел вложенный в отдел для сложных форм */
    public static final Bo OU2_1 = createOu(LayoutMetaClass.OU_CASE2, Ou.OU2_1);
    /** ou$56002: Отдел вложенный в отдел для сложных форм 1 */
    public static final Bo OU2_2 = createOu(LayoutMetaClass.OU_CASE2, Ou.OU2_2);

    /** employee$4501: Оченьдлиннофамильевская Апполинария Константиновна */
    public static final Bo EMPL1_1 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL1);
    /** employee$7201: Пользователь Меняющий Тип */
    public static final Bo EMPL1_2 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL2);
    /** employee$42201: Компактный Иван Иванович */
    public static final Bo EMPL1_3 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL3);
    /** employee$27501: Неполноправный Василий Петрович */
    public static final Bo EMPL1_4 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL4);
    /** employee$42901: Неполноправно-компактный Михайло Потапович */
    public static final Bo EMPL1_5 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL5);
    /** employee$56212: Сложных Василий Формович */
    public static final Bo EMPL1_6 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL6);
    /** employee$58401: Уменьшенный Ильдар Рафаилович */
    public static final Bo EMPL1_7 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL7);
    /** employee$59701: Компактноуменьшенный Варфаламей Иосифович */
    public static final Bo EMPL1_8 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL8);
    /** employee$76401: Менюшкин Лаврентий Петрович */
    public static final Bo EMPL1_9 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_9, Employee.EMPL9);
    /** employee$76401: Менюшкин Скролл Денисович */
    public static final Bo EMPL1_10 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_9, Employee.EMPL10);
    /** employee$74401: Уведомленный Иван Давыдович */
    public static final Bo EMPL1_11 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL11);
    /** employee$74402: Уведомленный Иннокентий Борисович */
    public static final Bo EMPL1_12 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL12);
    /** employee$85801: Избранный Гарри Джеймсович */
    public static final Bo EMPL1_13 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL13);
    /** employee$89701: Темный Аркадий Савельевич */
    public static final Bo EMPL1_14 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_1, Employee.EMPL14);
    /** employee$101101: Ответственный Захар Илларионович */
    public static final Bo EMPL1_15 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_1, Employee.EMPL15);
    /** employee$105801: Бронзовый Алексей Тематический*/
    public static final Bo EMPL1_16 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_0, Employee.EMPL16);
    /** employee$93301: Векторный Василий Петрович*/
    public static final Bo EMPL1_17 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_1, Employee.EMPL17);
    /** employee$109701: Английский Джон Сноу*/
    public static final Bo EMPL1_18 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_1, Employee.EMPL18);
    /** employee$118301: Темноуведомленный Андрей Робертович*/
    public static final Bo EMPL1_19 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_1, Employee.EMPL19);
    /** employee$119501: Уведомленный Иван Леонидович*/
    public static final Bo EMPL1_20 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_1, Employee.EMPL20);
    /** employee$119502: Уведомленный Илья Константинович*/
    public static final Bo EMPL1_21 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_1, Employee.EMPL21);
    /** employee$121601: Вебсокетный Василий Витальевич*/
    public static final Bo EMPL1_22 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_1, Employee.EMPL22);
    /** employee$130801: Домашний Чел*/
    public static final Bo EMPL1_23 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_1, Employee.EMPL23);
    /** employee$142501: Смпишный Вова Темович*/
    public static final Bo EMPL1_24 = createEmployee(LayoutMetaClass.EMPL_CASE1, OU1_1, Employee.EMPL24);

    /** mainTestClass$5201: Тестирование отображения цепочки хлебных крошек и названия объекта в шапке карточки
     * объекта: цепочка переносятся на вторую строку и не наезжает на иконки добавления в избранное и перехода на
     * домашнюю страницу */
    public static final Bo USERBO1_1_1 = createUserBo(LayoutMetaClass.USER_CASE1_1, null, UserBo.BO16);
    /** mainTestClass$127401: Тестирование отсутствия пустого пространства в шапке карточки объекта при выборе
     * варианта заголовка объекта [Без заголовка]*/
    public static final Bo USERBO1_1_2 = createUserBo(LayoutMetaClass.USER_CASE1_1_2, null, UserBo.BO105);
    /** mainTestClass$5601: Тестирование отображения кнопок разного вида с названиями разной длины на панели действий
     *  в шапке карточки объекта */
    public static final Bo USERBO1_2_1 = createUserBo(LayoutMetaClass.USER_CASE1_2, null, UserBo.BO17);
    /** mainTestClass$26006: Тестирование отображения кнопок на панели действий в сложном списке */
    public static final Bo USERBO1_2_2 = createUserBo(LayoutMetaClass.USER_CASE1_2, null, UserBo.BO30);
    /** mainTestClass$26007: Вложенный объект */
    public static final Bo USERBO1_2_3 = createUserBo(LayoutMetaClass.USER_CASE1_2, null, UserBo.BO31);
    /** mainTestClass$6001: Тестирование сворачивания вкладок в шапке карточки объекта в меню «Еще» */
    public static final Bo USERBO1_3_1 = createUserBo(LayoutMetaClass.USER_CASE1_3, null, UserBo.BO18);
    /** mainTestClass$6901: Тестирование поведения выпадающего списка с длинными названиями типов на модальной форме
     * изменения типа */
    public static final Bo USERBO1_4_1 = createUserBo(LayoutMetaClass.USER_CASE1_4, null, UserBo.BO1);
    /** mainTestClass$13301: Группа: Тест-кейсы на проверку верстки выпадающих списков в интерфейсе оператора */
    public static final Bo USERBO1_6_1 = createUserBo(LayoutMetaClass.USER_CASE1_6, null, UserBo.BO3);
    /** mainTestClass$9601: Тестирование поведения выпадающего списка на модальной форме
     редактирования объекта с большим уровнем вложенности */
    public static final Bo USERBO1_4_2 = createUserBo(LayoutMetaClass.USER_CASE1_4, USERBO1_6_1, UserBo.BO2);
    /** mainTestClass$10001: Первый уровень */
    public static final Bo USERBO1_4_3 = createUserBo(LayoutMetaClass.USER_CASE1_4, USERBO1_4_2, UserBo.BO4);
    /** mainTestClass$10002: Второй уровень */
    public static final Bo USERBO1_4_4 = createUserBo(LayoutMetaClass.USER_CASE1_4, USERBO1_4_3, UserBo.BO5);
    /** mainTestClass$10003: Третий уровень */
    public static final Bo USERBO1_4_5 = createUserBo(LayoutMetaClass.USER_CASE1_4, USERBO1_4_4, UserBo.BO6);
    /** mainTestClass$10004: Четвертый уровень */
    public static final Bo USERBO1_4_6 = createUserBo(LayoutMetaClass.USER_CASE1_4, USERBO1_4_5, UserBo.BO7);
    /** mainTestClass$10005: Пятый уровень */
    public static final Bo USERBO1_4_7 = createUserBo(LayoutMetaClass.USER_CASE1_4, USERBO1_4_6, UserBo.BO8);
    /** mainTestClass$10005: Шестой уровень */
    public static final Bo USERBO1_4_8 = createUserBo(LayoutMetaClass.USER_CASE1_4, USERBO1_4_7, UserBo.BO9);
    /** mainTestClass$10007: Седьмой уровень и здесь очень длинное название, переносящееся на следующую строку в
     * данной формочке */
    public static final Bo USERBO1_4_9 = createUserBo(LayoutMetaClass.USER_CASE1_4, USERBO1_4_8, UserBo.BO10);
    /** mainTestClass$10008: Восьмой уровень */
    public static final Bo USERBO1_4_10 = createUserBo(LayoutMetaClass.USER_CASE1_4, USERBO1_4_9, UserBo.BO11);
    /** mainTestClass$10009: Тестирование поведения выпадающего списка с фильтрацией значений по вхождению */
    public static final Bo USERBO1_4_11 = createUserBo(LayoutMetaClass.USER_CASE1_4, null, UserBo.BO13);
    /** mainTestClass$12501: Тестирование поведения выпадающего списка, расположенного внизу на модальной
     форме, при наличии скролла внутри списка и наличии/отсутствии на модальной форме / при проскролливании формы */
    public static final Bo USERBO1_4_12 = createUserBo(LayoutMetaClass.USER_CASE1_4, null, UserBo.BO14);
    /** mainTestClass$13803: Тестирование поведения выпадающего списка видов списка адвлиста с длинными названиями */
    public static final Bo USERBO1_4_13 = createUserBo(LayoutMetaClass.USER_CASE1_4, null, UserBo.BO15);
    /** mainTestClass$16301: Группа: Тест-кейсы на тестирование верстки адвлиста */
    public static final Bo USERBO1_6_2 = createUserBo(LayoutMetaClass.USER_CASE1_6, null, UserBo.BO22);
    /** mainTestClass$16302: Тестирования расположения списка объектов, связанных и вложенных объектов слева, справа
     * и на всю ширину карточки объекта. */
    public static final Bo USERBO1_7_2 = createUserBo(LayoutMetaClass.USER_CASE1_7, null, UserBo.BO19);
    /** mainTestClass$16303: Вложенный объект для тестирования расположения списка объектов, связанных и
     вложенных объектов слева, справа и на всю ширину карточки объекта */
    public static final Bo USERBO1_7_3 = createUserBo(LayoutMetaClass.USER_CASE1_7, USERBO1_7_2, UserBo.BO20);
    /** mainTestClass$27301: Тестирование отображения списка отчетов и формы сохранения вида */
    public static final Bo USERBO1_7_4 = createUserBo(LayoutMetaClass.USER_CASE1_7, null, UserBo.BO32);
    /** mainTestClass$29002: Тестирование отображения сложного списка, лежащего на панели вкладок, списка со скрытым
     * названием и панелью действий */
    public static final Bo USERBO1_7_5 = createUserBo(LayoutMetaClass.USER_CASE1_7, null, UserBo.BO33);
    /** mainTestClass$29407: Тестирование отображения сложного списка, содержащего ссылки на архивные
     * объекты, при выключенной настройке «Очищать тексты в полях типа \"Текст в формате RTF\" от стилей» */
    public static final Bo USERBO1_7_6 = createUserBo(LayoutMetaClass.USER_CASE1_7, null, UserBo.BO34);
    /** mainTestClass$40903: Тестирование отображения сложных списков при отключенном системном
     скроллинге элементов страницы */
    public static final Bo USERBO1_7_7 = createUserBo(LayoutMetaClass.USER_CASE1_7, null, UserBo.BO35);
    /** mainTestClass$101401: Тестирование отображения сообщения об ошибке в сложном списке */
    public static final Bo USERBO1_7_8 = createUserBo(LayoutMetaClass.USER_CASE1_7, null, UserBo.BO92);
    /** mainTestClass$18001: Тестирование отображения блока сложной фильтрации в сложном списке */
    public static final Bo USERBO1_7_1_1 = createUserBo(LayoutMetaClass.USER_CASE1_7_1, USERBO1_6_2, UserBo.BO21);
    /** mainTestClass$19503: Короткий объект */
    public static final Bo USERBO1_7_1_2 = createUserBo(LayoutMetaClass.USER_CASE1_7_1, USERBO1_7_1_1, UserBo.BO23);
    /** mainTestClass$19501: Объектик */
    public static final Bo USERBO1_7_1_3 = createUserBo(LayoutMetaClass.USER_CASE1_7_1, USERBO1_7_1_1, UserBo.BO24);
    /** mainTestClass$19502: Объект с длинным названием для проверки ограничения ширины плашки */
    public static final Bo USERBO1_7_1_4 = createUserBo(LayoutMetaClass.USER_CASE1_7_1, USERBO1_7_1_1, UserBo.BO25);
    /** mainTestClass$21201: Тестирование отображения сложного списка кейсы 6-11 */
    public static final Bo USERBO1_7_1_5 = createUserBo(LayoutMetaClass.USER_CASE1_7_1, null, UserBo.BO26);
    /** mainTestClass$23101: Тестирование отображения панели постраничной навигации в сложном списке с большим числом
     *  объектов */
    public static final Bo USERBO1_7_2_1 = createUserBo(LayoutMetaClass.USER_CASE1_7_2, null, UserBo.BO27);
    /** mainTestClass$26001: Тестирование отображения форм инлайн-редактирования атрибутов в сложных списках */
    public static final Bo USERBO1_7_3_1 = createUserBo(LayoutMetaClass.USER_CASE1_7_3, null, UserBo.BO28);
    /** mainTestClass$26005: Объектик */
    public static final Bo USERBO1_7_3_2 = createUserBo(LayoutMetaClass.USER_CASE1_7_3, null, UserBo.BO29);
    /** mainTestClass$94501: Тестирование отображения формы инлайн-редактирования: критичные замечания по верстке */
    public static final Bo USERBO1_7_3_3 = createUserBo(LayoutMetaClass.USER_CASE1_7_3, null, UserBo.BO85);
    /** mainTestClass$94502: Объект для списка */
    public static final Bo USERBO1_7_3_4 = createUserBo(LayoutMetaClass.USER_CASE1_7_3, null, UserBo.BO86);
    /** mainTestClass$27901: Тестирование отображения контента «Параметры объекта» */
    public static final Bo USERBO1_8_1_1 = createUserBo(LayoutMetaClass.USER_CASE1_8_1, null, UserBo.BO36);
    /** mainTestClass$150001: Длинные названия кнопок с разделителем ИО*/
    public static final Bo USERBO1_8_1_1_1 = createUserBo(LayoutMetaClass.USER_CASE1_8_1_1, null, UserBo.BO111);
    /** mainTestClass$39401: Тестирование отображения значений атрибутов в разных представлениях на контенте
     * «Параметры объекта» */
    public static final Bo USERBO1_8_1_2 = createUserBo(LayoutMetaClass.USER_CASE1_8_1, null, UserBo.BO37);
    /** mainTestClass$43401: Тестирование отображения описаний атрибутов и пользовательских контролов к атрибутам на
     * контенте типа «Параметры объекта» */
    public static final Bo USERBO1_8_1_3 = createUserBo(LayoutMetaClass.USER_CASE1_8_1, null, UserBo.BO38);
    /** mainTestClass$29001: Тестирование отображения контентов типа «Параметры связанного объекта» */
    public static final Bo USERBO1_8_1_4 = createUserBo(LayoutMetaClass.USER_CASE1_8_1, null, UserBo.BO39);
    /** mainTestClass$114201: естирование отображения панелей контролов у атрибута и кнопок типа иконка в них на
     * контентах "Параметры объекта" и "Параметры связанного объекта"*/
    public static final Bo USERBO1_8_1_5 = createUserBo(LayoutMetaClass.USER_CASE1_8_1, null, UserBo.BO102);
    /** mainTestClass$29401: Тестирование отображения списка в представлении «Простой список» в положении слева,
     * справа и на всю ширину */
    public static final Bo USERBO1_8_2_1 = createUserBo(LayoutMetaClass.USER_CASE1_8_2, null, UserBo.BO40);
    /** mainTestClass$29404: Тестирование отображения содержимого простого списка */
    public static final Bo USERBO1_8_2_2 = createUserBo(LayoutMetaClass.USER_CASE1_8_2, null, UserBo.BO41);
    /** mainTestClass$29405: Тестирование отображения содержимого простого списка, лежащего на панели вкладок */
    public static final Bo USERBO1_8_2_3 = createUserBo(LayoutMetaClass.USER_CASE1_8_2, null, UserBo.BO42);
    /** mainTestClass$29408: Тестирование отображения простого списка, содержащего ссылку на архивный объект, при
     * отключенной настройке «Очищать тексты в полях типа "Текст в формате RTF" от стилей» */
    public static final Bo USERBO1_8_2_4 = createUserBo(LayoutMetaClass.USER_CASE1_8_2, null, UserBo.BO43);
    /** mainTestClass$40905: Тестирование отображения простых списков при отключенном системном скроллинге элементов
     * страницы */
    public static final Bo USERBO1_8_2_5 = createUserBo(LayoutMetaClass.USER_CASE1_8_2, null, UserBo.BO44);
    /** mainTestClass$40906: Вложенный объект для простого списка без скролла */
    public static final Bo USERBO1_8_2_6 = createUserBo(LayoutMetaClass.USER_CASE1_8_2, null, UserBo.BO57);
    /** mainTestClass$44101: Тестирование отображения контентов «Панель вкладок», многократно вложенных друг в друга */
    public static final Bo USERBO1_8_3_1 = createUserBo(LayoutMetaClass.USER_CASE1_8_3, null, UserBo.BO45);
    /** mainTestClass$44701: Тестирование отображения контентов «Список файлов» и «Комментарии» */
    public static final Bo USERBO1_8_4_1 = createUserBo(LayoutMetaClass.USER_CASE1_8_4, null, UserBo.BO46);
    /** mainTestClass$46201: Тестирование отображения контента «Комментарии» с постраничной навигацией "
     + "и отсутствием прав на просмотр автора комментария */
    public static final Bo USERBO1_8_4_2 = createUserBo(LayoutMetaClass.USER_CASE1_8_4, null, UserBo.BO47);
    /** mainTestClass$46202: Тестирование отображения окна предпросмотра файла формата .docx */
    public static final Bo USERBO1_8_4_3 = createUserBo(LayoutMetaClass.USER_CASE1_8_4, null, UserBo.BO48);
    /** mainTestClass$46601: Тестирование отображения контентов «История изменений объекта» и «История изменений
     * ответственного и статуса» */
    public static final Bo USERBO1_8_4_4 = createUserBo(LayoutMetaClass.USER_CASE1_8_5, null, UserBo.BO49);
    /** mainTestClass$47201: Тестирование отображения контентов «Диаграмма Ганта», «Встроенное приложение», «Схема
     * связей» и «Диаграмма жизненного цикла» */
    public static final Bo USERBO1_8_4_5 = createUserBo(LayoutMetaClass.USER_CASE1_8_5, null, UserBo.BO50);
    /** mainTestClass$79901: Тестирование отображения контентов «Список отчетов, печатных форм» и «Отчет, печатная
     * форма» */
    public static final Bo USERBO1_8_5_1 = createUserBo(LayoutMetaClass.USER_CASE1_8_5, null, UserBo.BO82);
    /** mainTestClass$59101: Тестирование верстки контентов при выборе уменьшенного шрифта в персональных настройках
     * пользователя */
    public static final Bo USERBO1_8_6_1 = createUserBo(LayoutMetaClass.USER_CASE1_8_6, null, UserBo.BO58);
    /** mainTestClass$60201: Тестирование верстки карточки архивного объекта */
    public static final Bo USERBO1_8_7_1 = createUserBo(LayoutMetaClass.USER_CASE1_8_7, null, UserBo.BO60);
    /** mainTestClass$88901: Тестирование отображения встроенного приложения Геосервисы */
    public static final Bo USERBO1_8_8_1 = createUserBo(LayoutMetaClass.USER_CASE1_8_8, null, UserBo.BO83);
    /** mainTestClass$53502: Тестирование верстки полей ввода для атрибутов разных типов на полноэкранной форме
     * редактирования объекта */
    public static final Bo USERBO1_9_1 = createUserBo(LayoutMetaClass.USER_CASE1_9, null, UserBo.BO51);
    /** mainTestClass$55002: Тестирование верстки полей ввода для атрибутов разных типов на модальной форме */
    public static final Bo USERBO1_9_1_1 = createUserBo(LayoutMetaClass.USER_CASE1_9_1, null, UserBo.BO54);
    /** mainTestClass$95406: Тестирование верстки формы экспорта списка */
    public static final Bo USERBO1_9_2_1 = createUserBo(LayoutMetaClass.USER_CASE1_9_2, null, UserBo.BO84);
    /** mainTestClass$95401: Тестирование отображения сообщений об ошибках и сообщений-предупреждений в интерфейсе
     * оператора */
    public static final Bo USERBO1_9_2_2 = createUserBo(LayoutMetaClass.USER_CASE1_9_2, null, UserBo.BO93);
    /** mainTestClass$95402: Сообщения "Объект для выбора на форме с сообщеньками"*/
    public static final Bo USERBO1_9_2_3 = createUserBo(LayoutMetaClass.USER_CASE1_9_2, null, UserBo.BO100);
    /** mainTestClass$55009: Тестирование верстки страницы при свернутой модальной форме */
    public static final Bo USERBO1_9_2 = createUserBo(LayoutMetaClass.USER_CASE1_9, null, UserBo.BO55);
    /** mainTestClass$56301: Тестирование верстки сложных модальных форм */
    public static final Bo USERBO1_9_3 = createUserBo(LayoutMetaClass.USER_CASE1_9, null, UserBo.BO56);
    /** mainTestClass$68701: Тестирование отображения атрибутов со скрытыми названиями*/
    public static final Bo USERBO1_9_4 = createUserBo(LayoutMetaClass.USER_CASE1_11, null, UserBo.BO67);
    /** mainTestClass$104204: Контенты на формах добавления и редактирования "Тестирования отображения контентов на
     * формах добавления и редактирования пиупиу"*/
    public static final Bo USERBO1_9_3_3 = createUserBo(LayoutMetaClass.USER_CASE1_9, null, UserBo.BO95);
    /** mainTestClass$110801: Объект для тестирования контентов на форме редактирования / Форма редактирования*/
    public static final Bo USERBO1_9_3_4 = createUserBo(LayoutMetaClass.USER_CASE1_9_3, null, UserBo.BO96);
    /** mainTestClass$110201: Тестирование текста РТФ */
    public static final Bo USERBO1_9_4_1 = createUserBo(LayoutMetaClass.USER_CASE1_9_4, null, UserBo.BO101);
    /** mainTestClass$128901: Тестирование отображения выпадающих меню в редакторе Froala на формах */
    public static final Bo USERBO1_9_5 = createUserBo(LayoutMetaClass.USER_CASE1_9_5, null, UserBo.BO104);
    /** mainTestClass$59111: Туманность Голова обезьяны */
    public static final Bo USERBO1_10_1 = createUserBo(LayoutMetaClass.USER_CASE1_10, null, UserBo.BO59);
    /** mainTestClass$59103: Туманность Кошачий глаз */
    public static final Bo USERBO1_10_2 = createUserBo(LayoutMetaClass.USER_CASE1_10, null, UserBo.BO61);
    /** mainTestClass$71202: Вложенный первого уровня 2 */
    public static final Bo USERBO1_11_3 = createUserBo(LayoutMetaClass.USER_CASE1_12, null, UserBo.BO68);
    /** mainTestClass$64301: Тестирование раскрытия контентов на всю страницу */
    public static final Bo USERBO1_13_2_1 = createUserBo(LayoutMetaClass.USER_CASE1_13_2, null, UserBo.BO80);
    /** mainTestClass$65401: Вложенный объект для маленького списка 1 */
    public static final Bo USERBO1_13_2_2 = createUserBo(LayoutMetaClass.USER_CASE1_13_2, null, UserBo.BO81);
    /** mainTestClass$71801: Тестирование отображения контента Иерархическое дерево */
    public static final Bo USERBO1_13_5_1 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, null, UserBo.BO69);
    /** mainTestClass$73901: Корневой объект */
    public static final Bo USERBO1_13_5_2 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, null, UserBo.BO70);
    /** mainTestClass$74922: Объект того же класса, что и корневой, вложенный в него */
    public static final Bo USERBO1_13_5_2_1 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, USERBO1_13_5_2,
            UserBo.BO71);
    /** mainTestClass$92609: Вложенный объект */
    public static final Bo USERBO1_13_5_2_2 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, null, UserBo.BO89);
    /** mainTestClass$92610: Вложенный во вложенный */
    public static final Bo USERBO1_13_5_2_2_1 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, null, UserBo.BO90);
    /** mainTestClass$74925: Постраничная навигация для разворачивания */
    public static final Bo USERBO1_13_5_3 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, null, UserBo.BO73);
    /** mainTestClass$83004: Поиск верхнеуровневый 1 */
    public static final Bo USERBO1_13_5_4 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, null, UserBo.BO74);
    /** mainTestClass$83007: Поиск второй уровень основной класс 1 */
    public static final Bo USERBO1_13_5_5 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, null, UserBo.BO75);
    /** mainTestClass$84201: Для выбора на СФДС */
    public static final Bo USERBO1_13_5_6 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, null, UserBo.BO76);
    /** mainTestClass$92607: Тестирование отображения иерархии объектов в контенте Иерархическое дерево со скрытием
     * заголовков */
    public static final Bo USERBO1_13_5_7 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, null, UserBo.BO88);
    /** mainTestClass$92608: Корневой объект */
    public static final Bo USERBO1_13_5_8 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, null, UserBo.BO91);
    /** mainTestClass$83004: Поиск верхнеуровневый 1 */
    public static final Bo USERBO1_13_5_9 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, null, UserBo.BO97);
    /** mainTestClass$84703: Вложенный в СФДС */
    public static final Bo USERBO1_13_5_6_1 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, USERBO1_13_5_6,
            UserBo.BO77);
    /** mainTestClass$84704: Третий уровень СФДС */
    public static final Bo USERBO1_13_5_6_1_1 = createUserBo(LayoutMetaClass.USER_CASE1_13_5, USERBO1_13_5_6_1,
            UserBo.BO78);
    /** mainTestClass$91501: Тестирование отображения интерфейса оператора в теме интерфейса с пользовательскими
     * параметрами */
    public static final Bo USERBO1_14_5_1 = createUserBo(LayoutMetaClass.USER_CLASS1, null, UserBo.BO87);

    /** mainTestClass$121402: 1. webSocket уведомления "Тестирование отображения webSocket уведомлений"*/
    public static final Bo USERBO1_14_1 = createUserBo(LayoutMetaClass.USER_CASE1_14_1, null, UserBo.BO103);
    /** mainTestClass$128201: 3. Встроенные приложения на модальной форме "Тестирование верстки при расположении
     * встроенного приложения на модальной форме"*/
    public static final Bo USERBO1_14_3 = createUserBo(LayoutMetaClass.USER_CASE1_14_3, null, UserBo.BO106);
    /** mainTestClass$136401: Замена иконок на векторные "Тестирование отображения атрибутов типа Набор элемента
     * справочника и Элемент справочника, настроенных на системный справочник Иконки для элементов управления, в
     * Простых и Сложных списках, Параметрах объекта и Иерархических деревьях"*/
    public static final Bo USERBO1_15_2_1 = createUserBo(LayoutMetaClass.USER_CASE1_15_2, null, UserBo.BO108);
    /** nestTestClass$10201: Вложенный объект вложенного класса с длинным названием для проверки отображения в
     * выпадающем списке */
    public static final Bo USERBO2_1_1 = createUserBo(LayoutMetaClass.USER_CASE2_1, USERBO1_4_10, UserBo.BO12);
    /** nestTestClass$75522: Объект вложенного класса, вложенный во вложенный объект */
    public static final Bo USERBO2_2_1 = createUserBo(LayoutMetaClass.USER_CASE2_1, USERBO1_13_5_2_1,
            UserBo.BO72);
    /** leftMenuTest$59801: Туманность Песочные часы - планетарная туманность взрывообразной формы, расстояние до
     * которой около 8000 световых лет*/
    public static final Bo USERBO3_1 = createUserBo(LayoutMetaClass.USER_CASE1_10, null, UserBo.BO62);
    /** maxClass$59901: Туманность Киля - эмиссионная туманность (область ионизированного водорода) в созвездии Киль*/
    public static final Bo USERBO4_1 = createUserBo(LayoutMetaClass.USER_CASE1_10, null, UserBo.BO64);
    /** maxClass$59902: Туманность Лагуна - гигантское межзвёздное облако и область H II в созвездии Стрельца*/
    public static final Bo USERBO4_2 = createUserBo(LayoutMetaClass.USER_CASE1_10, null, UserBo.BO65);
    /** versClass$78803: Объект, имеющий копию в ветке 1*/
    public static final Bo USERBO5_1_1 = createUserBo(LayoutMetaClass.USER_CASE5_1, null, UserBo.BO79);
    /** mainTestClass$112701: Тестирование доработки изменения ширины колонок иерархического дерева*/
    public static final Bo USERBO1_10_5_1 = createUserBo(LayoutMetaClass.USER_CASE5_1, null, UserBo.BO94);
    /** versClass$117007: Объект типа бейджи в режиме планирования (пончик)*/
    public static final Bo USERBO5_1_2 = createUserBo(LayoutMetaClass.USER_CASE5_1, null, UserBo.BO98);
    /** versClass$117004: 1. Бейджи "Архивный"*/
    public static final Bo USERBO5_1_3 = createUserBo(LayoutMetaClass.USER_CASE5_1, null, UserBo.BO99);
    /** versClass$116801: 1. Бейджи "Объект типа бейджи в режиме планирования (пончик)"*/
    public static final Bo USERBO5_1_4 = createUserBo(LayoutMetaClass.USER_CASE5_1, null, UserBo.BO107);
    /** mainTestClass$147302: 2. webSocket автообновление "Тестирование отображения webSocket уведомлений об
     * автообновлении и подсветки изменений"*/
    public static final Bo USERBO1_16_1_2 = createUserBo(LayoutMetaClass.USER_CASE1_16_2, null, UserBo.BO109);
    /** mainTestClass$144201: 1. Файлы в комментариях "Тестирование отображения прикрепленных к комментарию файлов в виде набора бейджей"*/
    public static final Bo USERBO1_16_1_3 = createUserBo(LayoutMetaClass.USER_CASE1_16_3, null, UserBo.BO110);

    private static final CatalogItem FAKE_CATALOG_ITEM = new CatalogItem();
    /** agreement$20301: Соглашение */
    public static final Bo AGREEMENT1 = createAgreement(LayoutMetaClass.AGREEMENT_CASE1, Agreement.AGREEMENT1);
    private static final Bo FAKE_AGREEMENT = AGREEMENT1;
    /** serviceCall$22202: 3 — Однострочный */
    public static final Bo SC1_1 = createSc(LayoutMetaClass.SC_CASE1, Sc.SC1);
    /** serviceCall$22205: 6 - Запрос у которого нет файла-ссылки */
    public static final Bo SC1_2 = createSc(LayoutMetaClass.SC_CASE1, Sc.SC2);
    /** serviceCall$22203: 4 - Запрос, у которого картинка в svg и форматирование в RTF */
    public static final Bo SC1_3 = createSc(LayoutMetaClass.SC_CASE1, Sc.SC3);
    /** serviceCall$47401: 263 - Запрос для ДГ с очень длинным названием, которое должно обрезаться с многоточием */
    public static final Bo SC1_4 = createSc(LayoutMetaClass.SC_CASE1, Sc.SC4);
    /** serviceCall$56801: 269 - Запрос для работы с массовыми запросами */
    public static final Bo SC1_5 = createSc(LayoutMetaClass.SC_CASE1, Sc.SC5);
    /** serviceCall$57307: 276 - Запрос для работы с массовыми запросами */
    public static final Bo SC1_6 = createSc(LayoutMetaClass.SC_CASE1, Sc.SC6);
    /** serviceCall$57306: 275 - Запрос для работы с массовыми запросами */
    public static final Bo SC1_7 = createSc(LayoutMetaClass.SC_CASE1, Sc.SC7);
    /** serviceCall$108601: 317 - Запрос для тестирования форм */
    public static final Bo SC1_8 = createSc(LayoutMetaClass.SC_CASE1, Sc.SC8);

    /** sys_branch$63601: Ветка 1 */
    public static final Bo BRANCH1_1 = createBranch(LayoutMetaClass.BRANCH_CASE1, Branch.BRANCH1_1);

    /**
     * Создать модель соглашения
     * @param parent родительский класс/тип
     * @param agreement параметры соглашения
     * @return модель
     */
    private static Bo createAgreement(MetaClass parent, Agreement agreement)
    {
        Bo bo = DAOAgreement.create(parent, FAKE_CATALOG_ITEM, FAKE_CATALOG_ITEM);
        bo.setTitle(agreement.getTitle());
        bo.setUuid(agreement.getUuid());
        return bo;
    }

    /**
     * Создать модель сотрудника
     * @param parent родительский класс/тип
     * @param ou отдел
     * @param empl параметры сотрудника
     * @return модель
     */
    private static Bo createEmployee(MetaClass parent, Bo ou, Employee empl)
    {
        // не используем базовые методы для создания сотрудника, чтобы избежать создание shared объектов
        Bo bo = ModelFactory.create(Bo.class);
        bo.setMetaclassTitle(parent.getTitle());
        bo.setMetaclassCode(parent.getCode());
        bo.setMetaclassFqn(parent.getFqn());
        bo.setParentUuid(ou.getUuid());
        bo.setUuid(empl.getUuid());
        bo.setTitle(empl.getTitle());
        bo.setLogin(empl.getLogin());
        bo.setPassword(empl.getPassword());
        return bo;
    }

    /**
     * Создать модель ветки
     * @param parent родительский класс/тип
     * @param branch параметры ветки
     * @return модель
     */
    private static Bo createBranch(MetaClass parent, Branch branch)
    {
        Bo bo = DAOBranch.create(parent);
        bo.setTitle(branch.getTitle());
        bo.setUuid(branch.getUuid());
        return bo;
    }

    /**
     * Создать модель отдела
     * @param parent родительский класс/тип
     * @param ou параметры отдела
     * @return модель
     */
    private static Bo createOu(MetaClass parent, Ou ou)
    {
        Bo bo = DAOOu.create(parent);
        bo.setTitle(ou.getTitle());
        bo.setUuid(ou.getUuid());
        return bo;
    }

    /**
     * Создать модель запроса
     * @param parent родительский класс/тип
     * @param sc параметры запроса
     * @return модель
     */
    private static Bo createSc(MetaClass parent, Sc sc)
    {
        Bo bo = DAOSc.create(parent, null, FAKE_AGREEMENT, FAKE_CATALOG_ITEM);
        bo.setTitle(sc.getTitle());
        bo.setUuid(sc.getUuid());
        return bo;
    }

    /**
     * Создать модель команды БО
     * @param parent родительский класс/тип
     * @param team параметры БО
     * @return модель
     */
    private static Bo createTeamBo(MetaClass parent, Team team)
    {
        Bo bo = DAOTeam.create(parent);
        bo.setTitle(team.getTitle());
        bo.setUuid(team.getUuid());
        return bo;
    }

    /**
     * Создать модель пользовательского БО
     * @param parent родительский класс/тип
     * @param parentBo родительский БО
     * @param userBo параметры БО
     * @return модель
     */
    private static Bo createUserBo(MetaClass parent, Bo parentBo, UserBo userBo)
    {
        Bo bo = DAOUserBo.create(parent);
        bo.setTitle(userBo.getTitle());
        bo.setUuid(userBo.getUuid());
        bo.setParentUuid(parentBo != null ? parentBo.getUuid() : null);
        return bo;
    }
}
